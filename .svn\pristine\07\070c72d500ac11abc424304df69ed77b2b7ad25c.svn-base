<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssc00104.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssc00104.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css">

<SCRIPT type="text/javascript">

</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssc00104.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssc00104.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssc00104.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssc00104.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
	styleClass="commandExButton" id="returnDisp" action="#{pc_Ssc00104.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
	<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="800" border="0" cellpadding="3" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblKgyCd" value="#{pc_Ssc00104.propKgyCd.labelName}"
							style="#{pc_Ssc00104.propKgyCd.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlKgyCd"
							value="#{pc_Ssc00104.propKgyCd.stringValue}"
							style="#{pc_Ssc00104.propKgyCd.style}"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="17%"><h:outputText styleClass="outputText"
							id="lblKgyName" value="#{pc_Ssc00104.propKgyName.labelName}"
							style="#{pc_Ssc00104.propKgyName.labelStyle}"></h:outputText></TH>
						<TD width="33%"><h:outputText styleClass="outputText"
							id="htmlKgyName" value="#{pc_Ssc00104.propKgyName.displayValue}"
							style="#{pc_Ssc00104.propKgyName.style}"
							title="#{pc_Ssc00104.propKgyName.value}"></h:outputText></TD>
						<TH class="v_c" width="17%"><h:outputText styleClass="outputText"
							id="lblKgyNameKana"
							value="#{pc_Ssc00104.propKgyNameKana.labelName}"
							style="#{pc_Ssc00104.propKgyNameKana.labelStyle}"></h:outputText></TH>
						<TD width="33%"><h:outputText styleClass="outputText"
							id="htmlKgyNameKana"
							value="#{pc_Ssc00104.propKgyNameKana.displayValue}"
							style="#{pc_Ssc00104.propKgyNameKana.style}"
							title="#{pc_Ssc00104.propKgyNameKana.value}"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblKgyNameRyak"
							value="#{pc_Ssc00104.propKgyNameRyak.labelName}"
							style="#{pc_Ssc00104.propKgyNameRyak.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlKgyNameRyak"
							value="#{pc_Ssc00104.propKgyNameRyak.displayValue}"
							style="#{pc_Ssc00104.propKgyNameRyak.style}"
							title="#{pc_Ssc00104.propKgyNameRyak.value}"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_e"><h:outputText styleClass="outputText"
							id="lblGyosyuName"
							value="#{pc_Ssc00104.propGyosyuName.labelName}"
							style="#{pc_Ssc00104.propGyosyuName.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlGyosyuName"
							value="#{pc_Ssc00104.propGyosyuName.displayValue}"
							style="#{pc_Ssc00104.propGyosyuName.style}"
							title="#{pc_Ssc00104.propGyosyuName.value}"></h:outputText></TD>
						<TH class="v_f"><h:outputText styleClass="outputText"
							id="lblChikiNameRyak"
							value="#{pc_Ssc00104.propChikiName.labelName}"
							style="#{pc_Ssc00104.propChikiName.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlChikiName"
							value="#{pc_Ssc00104.propChikiName.displayValue}"
							style="#{pc_Ssc00104.propChikiName.style}"
							title="#{pc_Ssc00104.propChikiName.value}"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE style="margin-top:15px" width="800" border="0" cellpadding="3" cellspacing="0" class="table">
				<TBODY>

						<TR>
							<TH class="v_a" width="160"><h:outputText
								styleClass="outputText" id="lblSetuSotuNendo"
								value="#{pc_Ssc00104.propSetuSotuNendo.labelName}"
								style="#{pc_Ssc00104.propSetuSotuNendo.labelStyle}"></h:outputText></TH>
							<TD width="242"><h:inputText styleClass="inputText"
								id="htmlSetuSotuNendo"
								disabled="#{pc_Ssc00104.propSetuSotuNendo.disabled}"
								readonly="#{pc_Ssc00104.propSetuSotuNendo.readonly}"
								style="#{pc_Ssc00104.propSetuSotuNendo.style}" size="4"
								value="#{pc_Ssc00104.propSetuSotuNendo.dateValue}">
								<hx:inputHelperAssist imeMode="inactive"
									errorClass="inputText_Error" promptCharacter="_" />
								<f:convertDateTime pattern="yyyy" />
								</h:inputText><h:outputText styleClass="outputText"
								id="lblNendo" value="年度"></h:outputText></TD>
							<TH class="v_b" width="170"><h:outputText
								styleClass="outputText" id="lblSetuDate"
								style="#{pc_Ssc00104.propSetuDate.labelStyle}"
								value="#{pc_Ssc00104.propSetuDate.labelName}"></h:outputText></TH>
							<TD width="228"><h:inputText styleClass="inputText"
								id="htmlSetuDate"
								disabled="#{pc_Ssc00104.propSetuDate.disabled}"
								style="#{pc_Ssc00104.propSetuDate.style}"
								value="#{pc_Ssc00104.propSetuDate.dateValue}" size="10"
								readonly="#{pc_Ssc00104.propSetuDate.readonly}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
									imeMode="inactive" promptCharacter="_" />
								</h:inputText></TD>
						</TR>
						
						<TR>
							<TH class="v_f" width="160"><h:outputText
								styleClass="outputText" id="lblEdaBan"
								style="#{pc_Ssc00104.propEdaBan.labelStyle}"
								value="#{pc_Ssc00104.propEdaBan.labelName}"></h:outputText></TH>
							<TD width="242"><h:inputText styleClass="inputText" id="htmlEdaBan"
								readonly="#{pc_Ssc00104.propEdaBan.readonly}" size="3"
								style="#{pc_Ssc00104.propEdaBan.style}"
								value="#{pc_Ssc00104.propEdaBan.integerValue}"
								disabled="#{pc_Ssc00104.propEdaBan.disabled}"
								maxlength="#{pc_Ssc00104.propEdaBan.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertNumber pattern="###" />
							</h:inputText></TD>
						</TR>
						
						<TR>
							<TH width="160" class="v_c"><h:outputText
								styleClass="outputText" id="lblSetuStart"
								style="#{pc_Ssc00104.propSetuStart.labelStyle}"
								value="#{pc_Ssc00104.propSetuStart.labelName}"></h:outputText></TH>
							<TD width="242"><h:inputText styleClass="inputText"
								id="htmlSetuStart"
								disabled="#{pc_Ssc00104.propSetuStart.disabled}"
								readonly="#{pc_Ssc00104.propSetuStart.readonly}" size="5"
								style="#{pc_Ssc00104.propSetuStart.style}"
								value="#{pc_Ssc00104.propSetuStart.dateValue}">
								<hx:inputHelperAssist imeMode="inactive"
									errorClass="inputText_Error" promptCharacter="_" />
								<f:convertDateTime type="time" timeStyle="short" />
								<hx:inputHelperDatePicker />
							</h:inputText></TD>
							<TH class="v_d" width="170"><h:outputText
								styleClass="outputText" id="lblZuijiFlg"
								style="#{pc_Ssc00104.propZuijiFlg.labelStyle}"
								value="#{pc_Ssc00104.propZuijiFlg.labelName}"></h:outputText></TH>
							<TD width="228"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlZuijiFlg"
								value="#{pc_Ssc00104.propZuijiFlg.checked}"
								disabled="#{pc_Ssc00104.propZuijiFlg.disabled}"
								readonly="#{pc_Ssc00104.propZuijiFlg.readonly}"
								style="#{pc_Ssc00104.propZuijiFlg.style}"></h:selectBooleanCheckbox></TD>
						</TR>
						<TR>
							<TH class="v_e" width="160"><h:outputText
								styleClass="outputText" id="lblSetuSaisyuDate"
								style="#{pc_Ssc00104.propSetuSaisyuDate.labelStyle}"
								value="#{pc_Ssc00104.propSetuSaisyuDate.labelName}"></h:outputText></TH>
							<TD width="242"><h:inputText styleClass="inputText"
								id="htmlSetuSaisyuDate"
								disabled="#{pc_Ssc00104.propSetuSaisyuDate.disabled}"
								readonly="#{pc_Ssc00104.propSetuSaisyuDate.readonly}"
								size="10" style="#{pc_Ssc00104.propSetuSaisyuDate.style}"
								value="#{pc_Ssc00104.propSetuSaisyuDate.dateValue}"
								required="false">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" imeMode="inactive" />
							</h:inputText></TD>
							<TH class="v_f" width="170"><h:outputText
								styleClass="outputText" id="lblSetuPlaceRyk"
								style="#{pc_Ssc00104.propSetuPlaceRyk.labelStyle}"
								value="#{pc_Ssc00104.propSetuPlaceRyk.labelName}"></h:outputText></TH>
							<TD width="228"><h:inputText styleClass="inputText"
								id="htmlSetuPlaceRyk"
								disabled="#{pc_Ssc00104.propSetuPlaceRyk.disabled}"
								maxlength="#{pc_Ssc00104.propSetuPlaceRyk.maxLength}"
								readonly="#{pc_Ssc00104.propSetuPlaceRyk.readonly}"
								style="#{pc_Ssc00104.propSetuPlaceRyk.style}"
								value="#{pc_Ssc00104.propSetuPlaceRyk.stringValue}" size="20">
							</h:inputText></TD>
						</TR>
						<TR>
							<TH class="v_g" width="160"><h:outputText
								styleClass="outputText" id="lblSetuPlace"
								style="#{pc_Ssc00104.propSetuPlace.labelStyle}"
								value="#{pc_Ssc00104.propSetuPlace.labelName}"></h:outputText></TH>
							<TD width="640" colspan="3"><h:inputTextarea styleClass="inputTextarea"
								id="htmlSetuPlace" cols="75" rows="2"
								disabled="#{pc_Ssc00104.propSetuPlace.disabled}"
								readonly="#{pc_Ssc00104.propSetuPlace.readonly}"
								style="#{pc_Ssc00104.propSetuPlace.style}"
								value="#{pc_Ssc00104.propSetuPlace.stringValue}">
							</h:inputTextarea></TD>
						</TR>
						<TR>
							<TH class="v_a" width="160"><h:outputText
								styleClass="outputText" id="lblSetuMemo"
								style="#{pc_Ssc00104.propSetuMemo.labelStyle}"
								value="#{pc_Ssc00104.propSetuMemo.labelName}"></h:outputText></TH>
							<TD width="640" colspan="3"><h:inputTextarea styleClass="inputTextarea"
								id="htmlSetuMemo" cols="75" rows="2"
								disabled="#{pc_Ssc00104.propSetuMemo.disabled}"
								readonly="#{pc_Ssc00104.propSetuMemo.readonly}"
								style="#{pc_Ssc00104.propSetuMemo.style}"
								value="#{pc_Ssc00104.propSetuMemo.stringValue}">
							</h:inputTextarea></TD>
						</TR>
						<TR>
							<TH class="v_b" width="160"><h:outputText
								styleClass="outputText" id="lblSetuTorokuDate"
								style="#{pc_Ssc00104.propSetuTorokuDate.labelStyle}"
								value="#{pc_Ssc00104.propSetuTorokuDate.labelName}"></h:outputText></TH>
							<TD width="242"><h:inputText styleClass="inputText"
								id="htmlSetuTorokuDate"
								disabled="#{pc_Ssc00104.propSetuTorokuDate.disabled}"
								readonly="#{pc_Ssc00104.propSetuTorokuDate.readonly}"
								size="10" style="#{pc_Ssc00104.propSetuTorokuDate.style}"
								value="#{pc_Ssc00104.propSetuTorokuDate.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" imeMode="inactive" />
							</h:inputText></TD>
							<TH class="v_c" width="170"><h:outputText
								styleClass="outputText" id="lblSetuKoshinDate"
								style="#{pc_Ssc00104.propSetuKoshinDate.labelStyle}"
								value="#{pc_Ssc00104.propSetuKoshinDate.labelName}"></h:outputText></TH>
							<TD width="228"><h:inputText styleClass="inputText"
								id="htmlSetuKoshinDate"
								disabled="#{pc_Ssc00104.propSetuKoshinDate.disabled}"
								style="#{pc_Ssc00104.propSetuKoshinDate.style}"
								readonly="#{pc_Ssc00104.propSetuKoshinDate.readonly}"
								size="10" value="#{pc_Ssc00104.propSetuKoshinDate.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" imeMode="inactive" />
							</h:inputText></TD>
						</TR>

				</TBODY>
			</TABLE>
			<TABLE style="margin-top:15px" width="800" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="700">
							<TABLE  width="700" border="0" cellpadding="3" cellspacing="0" class="table">
								<TR>
									<TH class="v_d" width="153"><h:outputText
										styleClass="outputText" id="lblKjnNendo"
										value="#{pc_Ssc00104.propKjnNendo.labelName}"
										style="#{pc_Ssc00104.propKjnNendo.labelStyle}"></h:outputText></TH>
									<TD width="207"><h:inputText styleClass="inputText"
										id="htmlKjnNendo"
										disabled="#{pc_Ssc00104.propKjnNendo.disabled}" size="4"
										readonly="#{pc_Ssc00104.propKjnNendo.readonly}"
										style="#{pc_Ssc00104.propKjnNendo.style}"
										value="#{pc_Ssc00104.propKjnNendo.dateValue}">

										<hx:inputHelperAssist imeMode="inactive"
											errorClass="inputText_Error" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText><h:outputText styleClass="outputText"
										id="lblNendo2" value="年度"></h:outputText></TD>
									<TH width="110" class="v_e"><h:outputText
										styleClass="outputText" id="lblGakkoCd"
										value="#{pc_Ssc00104.propGakkoCd.labelName}"
										style="#{pc_Ssc00104.propGakkoCd.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakkoCd"
										disabled="#{pc_Ssc00104.propGakkoCd.disabled}"
										readonly="#{pc_Ssc00104.propGakkoCd.readonly}"
										style="#{pc_Ssc00104.propGakkoCd.style};width:200px;"
										value="#{pc_Ssc00104.propGakkoCd.value}">
										<f:selectItems value="#{pc_Ssc00104.propGakkoCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TABLE>
						</TD>
						<TD align="center" width="100"><hx:commandExButton
							type="submit" value="求人検索" styleClass="commandExButton_dat"
							id="search" action="#{pc_Ssc00104.doSearchAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE cellpadding="0" cellspacing="0" style="margin-top:15px">
				<TBODY>
					<TR>
						<TD valign="bottom" align="right" height="0"><h:outputText
							styleClass="outputText" id="htmlKjnListCnt"
							value="#{pc_Ssc00104.propKjnList.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="lblKensuu" value="件"></h:outputText></TD>
					</TR>
					<TR>
						<TD style="text-align:center;"><DIV id="listScroll" class="listScroll" 
							onscroll="setScrollPosition('scroll',this);" style="height:107px;"><h:dataTable id="htmlKjnList"
							value="#{pc_Ssc00104.propKjnList.list}" var="varlist"
							styleClass="meisai_scroll" headerClass="headerClass"
							footerClass="footerClass" width="780"
							rowClasses="#{pc_Ssc00104.propKjnList.rowClasses}"
							columnClasses=",columnCenter,columnCenter,columnRight,columnRight,,"
							border="0" cellpadding="3" cellspacing="0" 
							rows="#{pc_Ssc00104.propKjnList.rows}"
							first="#{pc_Ssc00104.propKjnList.first}"
							rendered="#{pc_Ssc00104.propKjnList.rendered}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="求人対象校"
										id="lblListGakkoName"></h:outputText>
								</f:facet>
								<h:outputText id="htmlListGakkoName"
									value="#{varlist.gakkoName.displayValue}"
									title="#{varlist.gakkoName.stringValue}"
									styleClass="outputText">
								</h:outputText>
								<f:attribute value="210" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="求人票受付番号"
										id="lblListKjnhNo"></h:outputText>
								</f:facet>
								<h:outputText id="htmlListKjnhNo" styleClass="outputText"
									value="#{varlist.kjnhNo}" rendered="#{varlist.rendered}">
								</h:outputText>
								<f:attribute value="120" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="求人登録日"
										id="lblListKjnTrkDate"></h:outputText>
								</f:facet>
								<f:attribute value="120" name="width" />
								<h:outputText styleClass="outputText" id="htmlListKjnTrkDate"
									value="#{varlist.kjnTrkDate}" rendered="#{varlist.rendered}">
									<f:convertDateTime />
								</h:outputText>
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="求人数（全体）"
										id="lblKjnSu"></h:outputText>
								</f:facet>
								<f:attribute value="100" name="width" />

								<h:outputText styleClass="outputText" id="htmlListKjnSu"
									value="#{varlist.kjnSu}" rendered="#{varlist.rendered}"></h:outputText>
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="推薦枠（全体）"
										id="lblKjnSuSsen"></h:outputText>
								</f:facet>
								<f:attribute value="100" name="width" />
								<h:outputText styleClass="outputText" id="htmlListKjnSuSsen"
									value="#{varlist.kjnSuSsen}" rendered="#{varlist.rendered}"></h:outputText>
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学部指定求人"
										id="lblGakubuSitei"></h:outputText>
								</f:facet>
								<f:attribute value="text-align:center" name="style" />
								<f:attribute value="100" name="width" />
								<h:outputText styleClass="outputText" id="htmlGakubuSitei"
									value="#{varlist.gakubuSitei}"></h:outputText>
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="30" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="selectList"
									rendered="#{varlist.rendered}"
									action="#{pc_Ssc00104.doSelectListAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></DIV></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="803">
				<TBODY>
					<TR>
						<TD align="left" width="327"><TABLE class="table" border="0" cellpadding="3"
							cellspacing="0" width="320">
							<TBODY>
								<TR>
									<TH width="155" class="v_f"><h:outputText
										styleClass="outputText" id="lblKjnhNo"
										style="#{pc_Ssc00104.propKjnhNo.labelStyle}"
										value="#{pc_Ssc00104.propKjnhNo.labelName}"></h:outputText></TH>
									<TD width="155" align="left" valign="middle"><h:outputText
										styleClass="outputText" id="htmlKjnhNo"
										style="#{pc_Ssc00104.propKjnhNo.style}"
										value="#{pc_Ssc00104.propKjnhNo.longValue}">
										<f:convertNumber pattern="##########" />
									</h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD align="left" width="474" height="32"><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton"
							id="cancelKjnhNo" action="#{pc_Ssc00104.doCancelKjnhNoAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE class="button_bar">
				<TBODY>
					<TR>
						<TD height="26" width="806"><hx:commandExButton type="submit"
							value="設定" styleClass="commandExButton_dat" id="register" action="#{pc_Ssc00104.doRegisterAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Ssc00104.doDeleteAction}"
							rendered="#{pc_Ssc00104.propDelete.rendered}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
			<h:inputHidden value="#{pc_Ssc00104.propKjnList.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Ssc00104.propHdnSetuNendo.integerValue}"
				id="htmlHdnNendo">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Ssc00104.propHdnSetuDate.dateValue}"
				id="htmlHdnSetuDate">
				<f:convertDateTime />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
	<jsp:include page="../inc/footer.jsp" />
	</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="javascript">
window.attachEvent('onload', endload);
function endload(){
changeScrollPosition('scroll','listscroll');
}
</SCRIPT>
</HTML>

