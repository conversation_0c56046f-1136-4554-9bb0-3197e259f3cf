<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cog80201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cog80501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<style type="text/css">
<!--
 .setWidth TD {width: 110px; white-space: nowrap;}
-->
</style>

<%@page import="com.jast.gakuen.framework.batch.BatchConst"%>

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cog80501.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cog80501.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Cog80501.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Cog80501.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトのため全角１文字 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="800">
							<TBODY>
								<TR>
									<TH class="v_a" colspan="2" width="150"><h:outputText
										styleClass="outputText" id="lblRegNendo"
										value="#{pc_Cog80501.propRegNendo.labelName}"
										style="#{pc_Cog80501.propRegNendo.labelStyle}"></h:outputText></TH>
									<TD class="" width="650"><h:inputText styleClass="inputText"
										id="htmlRegNendo" size="5" tabindex="1"
										style="#{pc_Cog80501.propRegNendo.style}"
										value="#{pc_Cog80501.propRegNendo.dateValue}"
										disabled="#{pc_Cog80501.propRegNendo.disabled}"
										readonly="#{pc_Cog80501.propRegNendo.readonly}">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" colspan="2">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFile"
													value="#{pc_Cog80501.propInputFile.name}"
													style="#{pc_Cog80501.propInputFile.labelStyle}"></h:outputText></TH>
											</TR>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFilePre"
													value="#{pc_Cog80501.propInputFilePre.name}"
													style="#{pc_Cog80501.propInputFilePre.labelStyle}"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									<TD class="">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border"><hx:fileupload
													styleClass="fileupload" id="htmlInputFile" size="50"
													value="#{pc_Cog80501.propInputFile.value}" tabindex="2"
													style="width:640px">
													<hx:fileProp name="fileName"
														value="#{pc_Cog80501.propInputFile.fileName}" />
													<hx:fileProp name="contentType"
														value="#{pc_Cog80501.propInputFile.contentType}" />
												</hx:fileupload></TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:outputText
													styleClass="outputText" id="htmlInputFilePre"
													value="#{pc_Cog80501.propInputFilePre.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblRegKbn"
										value="#{pc_Cog80501.propRegKbn.name}"
										style="#{pc_Cog80501.propRegKbn.labelStyle}"></h:outputText></TH>
									<TD class=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRegKbn"
										layout="pageDirection" tabindex="3"
										value="#{pc_Cog80501.propRegKbn.stringValue}"
										readonly="#{pc_Cog80501.propRegKbn.readonly}"
										disabled="#{pc_Cog80501.propRegKbn.disabled}">
										<f:selectItem itemValue="#{pc_Cog80501.propRegKbnInsertOnly.stringValue}"
											itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
										<f:selectItem itemValue="#{pc_Cog80501.propRegKbnUpdate.stringValue}"
											itemLabel="データを登録または更新します。同一データが存在すれば上書き更新します。" />
										<f:selectItem itemValue="#{pc_Cog80501.propRegKbnDeleteInsert.stringValue}"
											itemLabel="登録会計年度のデータを一旦削除し、新たにデータを登録します。" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblSyoriKubunL"
										value="#{pc_Cog80501.propSyoriKbn.name}"
										style="#{pc_Cog80501.propSyoriKbn.labelStyle}"></h:outputText></TH>
									<TD class="v_e">
									<TABLE border="0" class="">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlSyoriKubun"
													tabindex="4" value="#{pc_Cog80501.propSyoriKbn.checked}"
													disabled="#{pc_Cog80501.propSyoriKbn.disabled}"
													readonly="#{pc_Cog80501.propSyoriKbn.readonly}"></h:selectBooleanCheckbox><h:outputText
													styleClass="outputText" id="lblSyoriKubun"
													value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblCheckList"
										value="#{pc_Cog80501.propChkListNormal.name}"
										style="#{pc_Cog80501.propChkListNormal.labelStyle}"></h:outputText></TH>
									<TD >
									<TABLE border="0" class="">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlChkListNormal"
													value="#{pc_Cog80501.propChkListNormal.checked}"
													tabindex="5"
													disabled="#{pc_Cog80501.propChkListNormal.disabled}"
													readonly="#{pc_Cog80501.propChkListNormal.readonly}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListNormal" 
													value="正常データ"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlChkListError"
													tabindex="6"
													value="#{pc_Cog80501.propChkListError.checked}"
													readonly="#{pc_Cog80501.propChkListError.readonly}"
													disabled="#{pc_Cog80501.propChkListError.disabled}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListError"
													value="エラーデータ"></h:outputText></TD>
											</TR>
											<TR>
												<TD class="clear_border">
													<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlChkListWarning" tabindex="7"
													value="#{pc_Cog80501.propChkListWarning.checked}"
													disabled="#{pc_Cog80501.propChkListWarning.disabled}"
													readonly="#{pc_Cog80501.propChkListWarning.readonly}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListWarning"
													value="ワーニングデータ"></h:outputText>
                                                </TD>
											</TR>
										</TBODY>
									</TABLE>
                                  </TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="800" class="button_bar" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="入力項目指定"
										styleClass="commandExButton_etc"
										id="setinput" tabindex="8"
										action="#{pc_Cog80501.doSetinputAction}"
										disabled="#{pc_Cog80501.propSetinput.disabled}"
										rendered="#{pc_Cog80501.propSetinput.rendered}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_dat"
										id="exec" tabindex="9"
										action="#{pc_Cog80501.doExecAction}"
										disabled="#{pc_Cog80501.propExec.disabled}"
										rendered="#{pc_Cog80501.propExec.rendered}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

