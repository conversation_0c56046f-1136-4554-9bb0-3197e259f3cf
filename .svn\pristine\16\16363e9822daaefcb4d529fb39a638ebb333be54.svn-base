<%--
 * 卒業生成績検索								
 * Kmg02201.jsp
 * liuJB
 * 作成日: 2005/11/22
 * version 1.0
 --%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmg02201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmg02201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
	function confirmOk() {	
		document.getElementById('form1:clearCount').value = "1";
		indirectClick('search');
	}
	function confirmCancel() {
		// alert('実行を中断しました。');
	}
	function openSubWindow5(thisObj, thisEvent) {
		// 卒業生検索画面
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0201.jsp?retFieldName=form1:htmlBangou&retFieldName2=form1:htmlNendo&retFieldName3=form1:htmlGakki";
		openModalWindow(url, "pCob0201", "<%=com.jast.gakuen.rev.co.PCob0201.getWindowOpenOption() %>");
		return true;
	}

	function doGakuseiAjax(thisObj, thisEvent) {
		// 卒業生名称を取得する                                                                                                                                                                                           
		var nendo = document.getElementById("form1:htmlNendo").value;
		var gakki = document.getElementById("form1:htmlGakki").value;
		var gaksekicd = document.getElementById("form1:htmlBangou").value;
		var target = "form1:htmlGakuseName";
		
		if((gaksekicd != null && gaksekicd != "")
			&& (nendo != null && nendo != "")
			&& (gakki != null && gakki != "")){
	
			var servlet = "rev/co/CobSotAJAX";
			var args = new Array();
			args['code1'] = gaksekicd;
			args['code2'] = nendo;
			args['code3'] = gakki;
			
			var ajaxUtil = new AjaxUtil();
			ajaxUtil.getCodeName(servlet, target, args);
		
		}else{
			document.getElementById(target).innerHTML = "";
		}
	}
	
	function loadAction(event){
		changeScrollPosition('scroll', 'listScroll');
		doGakuseiAjax(document.getElementById('form1:htmlBangou'), event);
	}

function func_1(thisObj, thisEvent) {
// 評価リスト一括チェック
check("htmlKomokuList","htmlSelectItem");
}
function func_2(thisObj, thisEvent) {
// 時間割リスト一括チェック解除
uncheck("htmlKomokuList","htmlSelectItem");
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmg02201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmg02201.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmg02201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmg02201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<!-- ↓ここに戻る／閉じるボタンを配置 --> 
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->


			<TABLE width="870" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="600">
						<TABLE width="100%" class="table">
							<TBODY>
								<TR>
									<TH nowrap width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Kmg02201.propNendo.labelName}"
										style="#{pc_Kmg02201.propNendo.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlNendo" value="#{pc_Kmg02201.propNendo.dateValue}"
										disabled="#{pc_Kmg02201.propNendo.disabled}"
										onblur="return doGakuseiAjax(this, event);"
										style="#{pc_Kmg02201.propNendo.style}" size="10">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" /></h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblGakki"
										value="#{pc_Kmg02201.propGakki.labelName}"
										style="#{pc_Kmg02201.propGakki.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlGakki" value="#{pc_Kmg02201.propGakki.integerValue}"
										disabled="#{pc_Kmg02201.propGakki.disabled}"
										maxlength="#{pc_Kmg02201.propGakki.maxLength}"
										onblur="return doGakuseiAjax(this, event);"
										style="#{pc_Kmg02201.propGakki.style}" size="10">
										<f:convertNumber type="number" pattern="#0"/>
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c"><h:outputText
										styleClass="outputText" id="lblBangou"
										value="#{pc_Kmg02201.propBangou.labelName}"
										style="#{pc_Kmg02201.propBangou.labelStyle}"></h:outputText></TH>
									<TD width="450"><h:inputText styleClass="inputText"
											id="htmlBangou" value="#{pc_Kmg02201.propBangou.stringValue}"
											disabled="#{pc_Kmg02201.propBangou.disabled}"
											maxlength="#{pc_Kmg02201.propBangou.maxLength}"
											onblur="return doGakuseiAjax(this, event);"
											style="#{pc_Kmg02201.propBangou.style}" size="10"></h:inputText>
										<hx:commandExButton	type="button" value="検" styleClass="commandExButton_search"
											id="query" style="styleClass"
											onclick="return openSubWindow5(this, event);"
											disabled="#{pc_Kmg02201.propQuery.disabled}"></hx:commandExButton>
										<h:outputText styleClass="outputText" id="htmlGakuseName"
											value="#{pc_Kmg02201.propGakuseiName.stringValue}"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_d"><h:outputText
										styleClass="outputText" id="lblOrder"
										value="#{pc_Kmg02201.propOrder.labelName}"
										style="#{pc_Kmg02201.propOrder.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlOrder"
										value="#{pc_Kmg02201.propOrder.value}"
										style="#{pc_Kmg02201.propOrder.style}"
										disabled="#{pc_Kmg02201.propOrder.disabled}">
										<f:selectItem itemValue="0" itemLabel="科目分類" />
										<f:selectItem itemValue="1" itemLabel="修得年度学期" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD align="center" valign="middle"><hx:commandExButton
							type="submit" value="選択" styleClass="commandExButton" id="search"
							disabled="#{pc_Kmg02201.propSearch.disabled}"
							action="#{pc_Kmg02201.doSearchAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="解除" styleClass="commandExButton" id="unlock"
							disabled="#{pc_Kmg02201.propUnlock.disabled}"
							action="#{pc_Kmg02201.doUnlockAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE width="870" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="600">
							<TABLE width="100%">
								<TBODY>
									<TR>
										<TD width="200"></TD>
										<TD width="400"></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
						<TD align="center" valign="middle">
							<hx:commandExButton type="submit"
							value="新規登録" styleClass="commandExButton" id="addNew"
							disabled="#{pc_Kmg02201.propAddNew.disabled}"
							action="#{pc_Kmg02201.doAddNewAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<BR>
			<TABLE width="870" cellpadding="0" border="0">
				<tr>
					<td align="right"><h:outputFormat styleClass="outputFormat"
						id="htmlKensu" value="{0}件">
						<f:param name="listCount"
							value="#{pc_Kmg02201.propListCount.value==null?\"0\":pc_Kmg02201.propListCount.value}"></f:param>
					</h:outputFormat></td>
				</tr>
			</TABLE>

			<TABLE width="870" cellpadding="0" border="0">
				<tr>
					<td align="center">
					<DIV onscroll="setScrollPosition('scroll', this)"
						style="height:300px" id="listScroll"
						class="listScroll"><h:dataTable border="0" cellpadding="2"
						cellspacing="0" columnClasses="columnClass1"
						headerClass="headerClass" footerClass="footerClass"
						rowClasses="#{pc_Kmg02201.propHykList.rowClasses}" styleClass="meisai_scroll"
						id="htmlKomokuList" value="#{pc_Kmg02201.propHykList.list}"
						var="varlist" width="855">
						<h:column id="column1">
							<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
								id="htmlSelectItem" value="#{varlist.selected}"></h:selectBooleanCheckbox>
							<f:facet name="header">
							</f:facet>
							<f:attribute value="25" name="width" />
							<f:attribute value="text-align:center" name="style" />
						</h:column>
						<h:column id="column7">
							<h:outputText styleClass="outputText" id="syutokNendo"
								value='#{varlist.syutokNendo== 0? "": varlist.syutokNendo}'></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="修得年度" id="text6"></h:outputText>
							</f:facet>
							<f:attribute value="70" name="width" />
						</h:column>
						<h:column id="column8">
							<h:outputText styleClass="outputText" id="syuutokuGakuki"
								value="#{varlist.syutokGakkiName}" 
								style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis;width:65px;display:block;" 
								title="#{varlist.syutokGakkiName}"></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="修得学期" id="text7"></h:outputText>
							</f:facet>
							<f:attribute value="70" name="width" />
						</h:column>
						<h:column id="column2">
							<h:outputText styleClass="outputText" id="funruyiName"
								value="#{varlist.kamokBunruiCd}"></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="科目分類コード" id="text1"></h:outputText>
							</f:facet>
							<f:attribute value="90" name="width" />
						</h:column>
						<h:column id="column3">
							<h:outputText styleClass="outputText" id="kamokuCd"
								value="#{varlist.kamokBunruiNameRyakInputText.displayValue}"
								style="#{varlist.kamokBunruiNameRyakInputText.style}"
								title="#{varlist.kamokBunruiNameRyakInputText.stringValue}"></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="科目分類名称" id="text2"></h:outputText>
							</f:facet>
							<f:attribute value="90" name="width" />
						</h:column>
						<h:column id="lbl">
							<h:outputText styleClass="outputText" id="kamokuName"
								value="#{varlist.kamokCd}"
								style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis;width:80px;display:block;"
								title="#{varlist.kamokCd}"></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="科目コード" id="text3"></h:outputText>
							</f:facet>
							<f:attribute value="87" name="width" />
						</h:column>
						<h:column id="column5">
							<h:outputText styleClass="outputText" id="kynName"
								value="#{varlist.kamokNameInputText.displayValue}"
								title="#{varlist.kamokNameInputText.stringValue}"
								style="#{varlist.kamokNameInputText.style}"></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="科目名称" id="text4"></h:outputText>
							</f:facet>
							<f:attribute value="195" name="width" />
						</h:column>
						<h:column id="column6">
							<h:outputText styleClass="outputText" id="tani"
								value='#{varlist.taniSu == null ? "" : varlist.taniSu}'></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="単位" id="text5"></h:outputText>
							</f:facet>
							<f:attribute value="40" name="width" />
							<f:attribute value="text-align:right" name="style" />
						</h:column>
						<h:column id="column9">
							<h:outputText styleClass="outputText" id="hyoka"
								value="#{varlist.hyokaNameRyak}"></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="評価" id="text8"></h:outputText>
							</f:facet>
							<f:attribute value="40" name="width" />
							<f:attribute value="text-align:center" name="style" />
						</h:column>
						<h:column id="column10">
							<h:outputText styleClass="outputText" id="risyuHoho"
								value='#{varlist.risyuHohoDispValue == null? "" : varlist.risyuHohoDispValue}'></h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="履修方法" id="text9"></h:outputText>
							</f:facet>
							<f:attribute value="80" name="width" />
							<f:attribute value="text-align:center" name="style" />
						</h:column>
						<h:column id="column11">
							<hx:commandExButton type="submit" value="編集"
								styleClass="commandExButton" id="edit"
								action="#{pc_Kmg02201.doEditAction}"></hx:commandExButton>
							<f:facet name="header">
							</f:facet>
							<f:attribute value="35" name="width" />
						</h:column>
					</h:dataTable></DIV>
					</td>
				</tr>
				<TR>
					<TD align="left">
						<TABLE class="table_footer" width="100%">
							<TBODY>
								<TR>
									<TD>
									<hx:commandExButton 
										type="button" value="一括チェック" styleClass="check" id="checkAll"
										disabled="#{pc_Kmg02201.propCheckAll.disabled}"
										onclick="return func_1(this, event);">
									</hx:commandExButton> 
									<hx:commandExButton
										type="button" value="一括解除" styleClass="uncheck" id="uncheckAll"
										disabled="#{pc_Kmg02201.propUncheckAll.disabled}"
										onclick="return func_2(this, event);">
									</hx:commandExButton>
									<hx:commandExButton type="submit"
										value="削除" styleClass="commandExButton" id="delete"
										disabled="#{pc_Kmg02201.propDelete.disabled}"
										action="#{pc_Kmg02201.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}">
									</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kmg02201.propClearButton.integerValue}"
				id="clearCount">
				<f:convertNumber type="number" />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kmg02201.propHykList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

