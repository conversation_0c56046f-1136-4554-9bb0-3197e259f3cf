<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmi00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmi00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
//チェックボックス一括チェック
function doAllSelect(thisObj, thisEvent) {
	//substitute();
	check('table1','rowSelect1');
}

// チェックボックス一括解除
function doAllUnSelect(thisObj, thisEvent) {
	//substitute();
	uncheck('table1','rowSelect1');
}
function func_1(thisObj, thisEvent) {
setTarget('Kmi00802'); return true;
}
function func_2(thisObj, thisEvent) {
//use 'thisObj' to refer directly to this component instead of keyword 'this'
//use 'thisEvent' to refer to the event generated instead of keyword 'event'
var servlet = "rev/km/GakseiAJAX";

var target = "form1:htmlstuName";

var code = thisObj.value;

getCodeName(servlet, target, code );
return true;
}
function openSubWindow5(thisObj, thisEvent) {

	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlStuNo";
	openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
//	setTarget("pCob0101");
	return true;
}
window.attachEvent('onload', endload);
function endload(){
  changeScrollPosition('scroll', 'listScroll');
}
function doGakuseiAjax(thisObj, thisEvent) {
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;
	args['code2'] = "";
	args['code3'] = "";
	var target = "form1:htmlstuName";
	var ajaxUtil = new AjaxUtil();
//	ajaxUtil.getCodeName(servlet, target, args);
	ajaxUtil.getPluralValueSetMethod(servlet, null, args,'callBackMethodKd');
}

function callBackMethodKd(value){
    document.getElementById('form1:htmlstuName').innerText = value['name'];
    document.getElementById('form1:htmlstuName').title = value['name'];
}

function loadAction(event) {
doGakuseiAjax(document.getElementById("form1:htmlStuNo"), event);
}</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmi00801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmi00801.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmi00801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmi00801.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<DIV align="center">
			<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
				height="515">
				<TBODY>
					<TR>
						<TD align="left"><odc:tabbedPanel binding="#{pc_Kmi00801.htmlTabbedPanel}" slantActiveRight="0"
							styleClass="tabbedPanel" width="800" slantInactiveRight="0"
							height="515" variableTabLength="false" showBackNextButton="false"
							showTabs="true" id="tabbedPanel1">
							<odc:bfPanel id="bfpanel1" name="一括指定"
								showFinishCancelButton="false">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
									height="100%">
									<TBODY>
										<TR>
											<TD align="center" valign="top" height="440">
											<TABLE width="760" border="0" cellpadding="0" cellspacing="0">
												<TBODY>
													<TR>
														<TD valign="bottom" align="right" height="27"><h:outputText
															styleClass="outputText" id="text34"
															value="#{pc_Kmi00801.propListNo.stringValue}"></h:outputText><h:outputText
															styleClass="outputText" id="text17" value="件"></h:outputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<TABLE border="0" cellpadding="0" cellspacing="0" width="760">
												<TBODY>
													<TR>
														<TD valign="top">
														<div class="listScroll" style="height:200px;width: 760px;"
															id="listScroll"
															onscroll="setScrollPosition('scroll',this);"><h:dataTable
															border="0" cellpadding="2" cellspacing="0"
															headerClass="headerClass" footerClass="footerClass"
															rowClasses="#{pc_Kmi00801.propKmiKsdKmk.rowClasses}"
															styleClass="meisai_scroll" id="htmlKmiKsdKmk"
															value="#{pc_Kmi00801.propKmiKsdKmk.list}" var="varlist">
															<h:column id="column4">
																<f:facet name="header">
																	<hx:jspPanel id="jspPanel3">
																	<table border="0" cellpadding="0" cellspacing="0" width="520" align="center">
																		<TR>
																			<TD colspan="2" style="border-top-style:none; border-left-style: none;border-right-style: none;text-align:center"><h:outputText styleClass="outputText" id="text301"
																			value="所属学科組織"></h:outputText></TD>
																		</TR>
																		<TR>
																			<TD style="border-top-style:none; border-bottom-style: none; border-left-style: none;text-align:center;width:120px"><h:outputText styleClass="outputText" id="text302"
																			value="コード"></h:outputText></TD>
																			<TD style="border-top-style:none; border-bottom-style: none; border-left-style: none;border-right-style: none;text-align:center;width:400px"><h:outputText styleClass="outputText" id="text303"
																			value="名称"></h:outputText></TD>
																		</TR>
																	</table>
																</hx:jspPanel>
																</f:facet>
																<hx:jspPanel id="jspPanel1">
																	<table border="0" cellpadding="0" cellspacing="0" width="520">
																		<TR>
																			<TD style="border-top-style:none; border-bottom-style: none; border-right-style: none; border-left-style: none;text-align:left;width:125px"><h:outputText styleClass="outputText" id="text29"
																			value="#{varlist.szkGakkaCd}"></h:outputText></TD>
																			<TD style="border-top-style:none; border-bottom-style: none; border-right-style: none;text-align:left;width:395px"><h:outputText styleClass="outputText" id="text30"
																	value="#{varlist.szkGakkaCdString.displayValue}"
																	title="#{varlist.szkGakkaCdString.value}"></h:outputText></TD>
																		</TR>
																	</table>
																	</hx:jspPanel>
																<f:attribute value="520" name="width" />
																<f:attribute value="text-align: center" name="style" />
															</h:column>
															<h:column id="column6">
																<h:outputText styleClass="outputText" id="text31"
																	value="#{varlist.gakuen}"></h:outputText>
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="学年"
																		id="text23"></h:outputText>
																</f:facet>
																<f:attribute value="80" name="width" />
																<f:attribute value="text-align: right" name="style" />
															</h:column>
															<h:column id="column7">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="ｾﾒｽﾀ"
																		id="text24"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text32"
																	value="#{varlist.semester}"></h:outputText>
																<f:attribute value="80" name="width" />
																<f:attribute value="text-align: right" name="style" />
															</h:column>
															<h:column id="column8">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="性別"
																		id="text25"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text33"
																	value="#{varlist.seibetu}"></h:outputText>
																<f:attribute value="80" name="width" />
																<f:attribute value="text-align: left" name="style" />
															</h:column>
															<h:column id="column9">
																<f:facet name="header">
																</f:facet>
																<hx:commandExButton type="submit" value="選択"
																	styleClass="commandExButton" id="select"
																	action="#{pc_Kmi00801.doSelectAction}"></hx:commandExButton>
																<f:attribute value="30" name="width" />
																<f:attribute value="text-align: center" name="style" />
															</h:column>
														</h:dataTable></div>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE width="760" border="0" cellpadding="0" cellspacing="0"
												class="table">
												<TBODY>
													<TR>
														<TH class="v_a"><h:outputText
															styleClass="outputText" id="text2"
															value="#{pc_Kmi00801.propSszGakka.labelName}"
															style="#{pc_Kmi00801.propSszGakka.labelStyle}"></h:outputText></TH>
														<TD colspan="5"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlSszGakka"
															value="#{pc_Kmi00801.propSszGakka.value}"
															style="width:440px">
															<f:selectItems value="#{pc_Kmi00801.propSszGakka.list}" />
														</h:selectOneMenu></TD>
													</TR>
													<TR>
														<TH class="v_b"><h:outputText
															styleClass="outputText" id="text3"
															style="#{pc_Kmi00801.propGakuen.labelStyle}"
															value="#{pc_Kmi00801.propGakuen.labelName}"></h:outputText></TH>
														<TD colspan="2"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlGakuen"
															value="#{pc_Kmi00801.propGakuen.value}"
															style="width:140px">
															<f:selectItems value="#{pc_Kmi00801.propGakuen.list}" />
														</h:selectOneMenu></TD>
														<TH colspan="1" class="v_c"><h:outputText
															styleClass="outputText" id="text9"
															value="#{pc_Kmi00801.propSemesuta.labelName}"
															style="#{pc_Kmi00801.propSemesuta.labelStyle}"></h:outputText></TH>
														<TD colspan="2"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlSemesuta"
															value="#{pc_Kmi00801.propSemesuta.value}"
															style="width:140px">
															<f:selectItems value="#{pc_Kmi00801.propSemesuta.list}" />
														</h:selectOneMenu></TD>
													</TR>
													<TR>
														<TH class="v_d"><h:outputText
															styleClass="outputText" id="text4"
															value="#{pc_Kmi00801.propSeibetu.labelName}"
															style="#{pc_Kmi00801.propSeibetu.labelStyle}"></h:outputText></TH>
														<TD colspan="5"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlSeibetu"
															value="#{pc_Kmi00801.propSeibetu.value}"
															style="width:140px">
															<f:selectItems value="#{pc_Kmi00801.propSeibetu.list}" />
														</h:selectOneMenu></TD>
													</TR>
													<TR>
														<TH class="v_e" width="133"><h:outputText
															styleClass="outputText" id="text5"
															value="#{pc_Kmi00801.propTallFlg.labelName}"
															style="#{pc_Kmi00801.propTallFlg.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlTallFlg"
															value="#{pc_Kmi00801.propTallFlg.checked}"
															style="#{pc_Kmi00801.propTallFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_f" width="133"><h:outputText styleClass="outputText"
															id="text10"
															value="#{pc_Kmi00801.propWeightFlg.labelName}"
															style="#{pc_Kmi00801.propWeightFlg.style}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlWeightFlg"
															value="#{pc_Kmi00801.propWeightFlg.checked}"
															style="#{pc_Kmi00801.propWeightFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_g" width="133"><h:outputText
															styleClass="outputText" id="text18"
															value="腹囲受診"
															style="#{pc_Kmi00801.propFukuiFlg.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlFukuiFlg"
															value="#{pc_Kmi00801.propFukuiFlg.checked}"
															style="#{pc_Kmi00801.propFukuiFlg.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_g" width="133"><h:outputText
															styleClass="outputText" id="text19"
															value="体脂肪率受診"
															style="#{pc_Kmi00801.propTaishibouFlg.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlTaishibouFlg"
															value="#{pc_Kmi00801.propTaishibouFlg.checked}"
															style="#{pc_Kmi00801.propTaishibouFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_g" width="133"><h:outputText
															styleClass="outputText" id="text14"
															value="#{pc_Kmi00801.propSiryokFlg.labelName}"
															style="#{pc_Kmi00801.propSiryokFlg.labelName}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlSiryokFlg"
															value="#{pc_Kmi00801.propSiryokFlg.checked}"
															style="#{pc_Kmi00801.propSiryokFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_a"><h:outputText
															styleClass="outputText" id="text6"
															value="#{pc_Kmi00801.propChoryokFlg.labelName}"
															style="#{pc_Kmi00801.propChoryokFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlChoryokFlg"
															value="#{pc_Kmi00801.propChoryokFlg.checked}"
															style="#{pc_Kmi00801.propChoryokFlg.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_f"><h:outputText styleClass="outputText"
															id="text11" value="#{pc_Kmi00801.propOgioFlg.labelName}"
															style="#{pc_Kmi00801.propOgioFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlOgioFlg"
															value="#{pc_Kmi00801.propOgioFlg.checked}"
															style="#{pc_Kmi00801.propOgioFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_e"><h:outputText
															styleClass="outputText" id="text15"
															value="#{pc_Kmi00801.propKetuatuFlg.labelName}"
															style="#{pc_Kmi00801.propKetuatuFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlKetuatuFlg"
															value="#{pc_Kmi00801.propKetuatuFlg.checked}"
															style="#{pc_Kmi00801.propKetuatuFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_b"><h:outputText
															styleClass="outputText" id="text7"
															value="#{pc_Kmi00801.propNyoFlg.labelName}"
															style="#{pc_Kmi00801.propNyoFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlNyoFlg"
															value="#{pc_Kmi00801.propNyoFlg.checked}"
															style="#{pc_Kmi00801.propNyoFlg.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_a"><h:outputText styleClass="outputText"
															id="text12" value="#{pc_Kmi00801.propBloodFlg.labelName}"
															style="#{pc_Kmi00801.propBloodFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlBloodFlg"
															value="#{pc_Kmi00801.propBloodFlg.checked}"
															style="#{pc_Kmi00801.propBloodFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_f"><h:outputText
															styleClass="outputText" id="text16"
															value="#{pc_Kmi00801.propXsenFlg.labelName}"
															style="#{pc_Kmi00801.propXsenFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlXsenFlg"
															value="#{pc_Kmi00801.propXsenFlg.checked}"
															style="#{pc_Kmi00801.propXsenFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_c"><h:outputText
															styleClass="outputText" id="text8"
															value="#{pc_Kmi00801.propSinDenZfFlg.labelName}"
															style="#{pc_Kmi00801.propSinDenZfFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlSinDenZfFlg"
															value="#{pc_Kmi00801.propSinDenZfFlg.checked}"
															style="#{pc_Kmi00801.propSinDenZfFlg.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_b"><h:outputText styleClass="outputText"
															id="text13" value="#{pc_Kmi00801.propNaikaFlg.labelName}"
															style="#{pc_Kmi00801.propNaikaFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlNaikaFlg"
															value="#{pc_Kmi00801.propNaikaFlg.checked}"
															style="#{pc_Kmi00801.propNaikaFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TD class="" colspan="4" style=""></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD align="center">
											<TABLE width="100%" border="0" cellpadding="0"
												cellspacing="0" class="button_bar">
												<TBODY>
													<TR>
														<TD><hx:commandExButton type="submit" value="確定"
															styleClass="commandExButton_dat" id="register"
															action="#{pc_Kmi00801.doRegisterAction}"
															confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
															<hx:commandExButton
															type="submit" value="削除" styleClass="commandExButton_dat"
															id="delete" action="#{pc_Kmi00801.doDeleteAction}"
															confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton>
															<hx:commandExButton
															type="submit" value="クリア"
															styleClass="commandExButton_dat" id="clear"
															action="#{pc_Kmi00801.doClearAction}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</odc:bfPanel>
							<odc:bfPanel id="bfpanel2" name="学生別指定"
								showFinishCancelButton="false">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
									height="100%">
									<TBODY>
										<TR>
											<TD align="center" valign="top" height="440">
											<BR>
											<TABLE width="760" border="0" cellpadding="0" cellspacing="0"
												class="table">
												<TBODY>
													<TR>
														<TH class="v_a" width="133"><h:outputText
															styleClass="outputText" id="text36" value="ファイル指定"
															style="#{pc_Kmi00801.propFileUpLoad.labelStyle}"></h:outputText><BR>
														<h:outputText styleClass="outputText" id="text37"
															value="(前回ファイル)"
															style="#{pc_Kmi00801.propOldPath.labelStyle}"></h:outputText></TH>
														<TD colspan="3" width="626"><hx:fileupload styleClass="fileupload"
															id="htmlfileupload"
															value="#{pc_Kmi00801.propFileUpLoad.value}"
															style="#{pc_Kmi00801.propFileUpLoad.style};width:580px">
															<hx:fileProp name="fileName"
																value="#{pc_Kmi00801.propFileUpLoad.fileName}" />
															<hx:fileProp name="contentType" />
														</hx:fileupload><hx:commandExButton type="submit"
															value="取込" styleClass="commandExButton" id="read"
															action="#{pc_Kmi00801.doReadAction}"></hx:commandExButton><BR>
															<h:outputText styleClass="outputText"
															id="htmlOldPath"
															value="#{pc_Kmi00801.propOldPath.stringValue}"
															style="#{pc_Kmi00801.propOldPath.style}"></h:outputText><BR></TD>
													</TR>
													<TR>
														<TH class="v_c"><h:outputText
															styleClass="outputText" id="text38"
															value="#{pc_Kmi00801.propStuNo.labelName}"
															style="#{pc_Kmi00801.propStuNo.labelStyle}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlStuNo"
															value="#{pc_Kmi00801.propStuNo.stringValue}"
															maxlength="#{pc_Kmi00801.propStuNo.maxLength}"
															onblur="return doGakuseiAjax(this, event);"
															style="#{pc_Kmi00801.propStuNo.style}"></h:inputText><hx:commandExButton
															type="button" styleClass="commandExButton_search"
															id="search" 
															onclick="return openSubWindow5(this, event);"></hx:commandExButton>
														<hx:commandExButton type="submit" value="追加"
															styleClass="commandExButton" id="add"
															action="#{pc_Kmi00801.doAddAction}"></hx:commandExButton><h:outputText
															styleClass="likeOutput" id="htmlstuName"
															></h:outputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<TABLE width="760" border="0" cellpadding="0" cellspacing="0">
												<TBODY>
													<TR>
														<TD valign="top" height="222">
														<div class="listScroll" id="listScroll2" style="height: 200px" onscroll="setScrollPosition('htmlHidScroll', this);">
														<h:dataTable border="1"
															cellpadding="2" cellspacing="0" columnClasses="columnClass1" headerClass="headerClass"
															footerClass="footerClass"
															rowClasses="#{pc_Kmi00801.propKmiKsdGak.rowClasses}"
															styleClass="meisai_scroll" id="table1" 
															value="#{pc_Kmi00801.propKmiKsdGak.list}" var="varlist"
															>
															<h:column id="column19">
																<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
																	id="rowSelect1" value="#{varlist.selected}"></h:selectBooleanCheckbox>
																<f:facet name="header"></f:facet>
																<f:attribute value="20" name="width" />
																<f:attribute value="text-align: center" name="style" />
															</h:column>
															<h:column id="column10">
																<f:facet name="header">
																	<h:outputText id="text42" styleClass="outputText"
																		value="設定"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text50"
																	value="#{varlist.seted}"></h:outputText>
																<f:attribute value="40" name="width" />
																<f:attribute value="center" name="align" />
																<f:attribute value="text-align: center" name="style" />
															</h:column>
															<h:column id="column11">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="学籍番号"
																		id="text43"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text51"
																	value="#{varlist.stuNo}"></h:outputText>
																<f:attribute value="100" name="width" />
															</h:column>
															<h:column id="column12">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="学生氏名"
																		id="text44"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text52"
																	value="#{varlist.name.displayValue}"
																	title="#{varlist.name.value}"></h:outputText>
																<f:attribute value="582" name="width" />
															</h:column>
															<h:column id="column13">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="学年"
																		id="text45"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text53"
																	value="#{varlist.gakuen}"></h:outputText>
																<f:attribute value="55" name="width" />
																<f:attribute value="text-align: right" name="style" />
															</h:column>
															<h:column id="column14">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="セメスタ"
																		id="text46"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text54"
																	value="#{varlist.semester}"></h:outputText>
																<f:attribute value="65" name="width" />
																<f:attribute value="text-align: right" name="style" />
															</h:column>
															<h:column id="column17">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="性別"
																		id="text49"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText" id="text57"
																	value="#{varlist.seibetu}"></h:outputText>
																<f:attribute value="40" name="width" />
																<f:attribute value="text-align: left" name="style" />
															</h:column>
														</h:dataTable></div>
														<TABLE width="760">
														<TR>
														<TD align="left"><hx:commandExButton type="submit"
														value="一括チェック" styleClass="check" id="allcheck"
														onclick="return doAllSelect(this, event);">
														</hx:commandExButton> <hx:commandExButton type="submit"
														value="一括解除" styleClass="uncheck" id="alluncheck"
														onclick="return doAllUnSelect(this, event);">
														</hx:commandExButton></TD>
														<TD align="right">
															<h:outputText
															styleClass="outputText" id="output"
															value="#{pc_Kmi00801.propOutMessenege.stringValue}"
															style="text-align: right; vertical-align: bottom"></h:outputText>
														</TD>
														</TR>
														</TABLE>
														</TD>
													</TR>
												</TBODY>
												</TABLE><BR>
											<TABLE width="760" border="0" cellpadding="0" cellspacing="0"
												class="table" valign="top">
												<TBODY>
													<TR>
														<TH class="v_f" width="133"><h:outputText
															styleClass="outputText" id="text151"
															value="#{pc_Kmi00801.propTallFlgK.labelName}"
															style="#{pc_Kmi00801.propTallFlg.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlTallFlgK"
															value="#{pc_Kmi00801.propTallFlgK.checked}"
															style="#{pc_Kmi00801.propTallFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_e" width="133"><h:outputText styleClass="outputText"
															id="text110"
															value="#{pc_Kmi00801.propWeightFlgK.labelName}"
															style="#{pc_Kmi00801.propWeightFlg.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlWeightFlgK"
															value="#{pc_Kmi00801.propWeightFlgK.checked}"
															style="#{pc_Kmi00801.propWeightFlg.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_g" width="133"><h:outputText
															styleClass="outputText" id="text119"
															value="腹囲受診"
															style="#{pc_Kmi00801.propFukuiFlgK.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlFukuiFlgK"
															value="#{pc_Kmi00801.propFukuiFlgK.checked}"
															style="#{pc_Kmi00801.propFukuiFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_g" width="133"><h:outputText
															styleClass="outputText" id="text120"
															value="体脂肪率受診"
															style="#{pc_Kmi00801.propTaishibouFlgK.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlTaishibouFlgK"
															value="#{pc_Kmi00801.propTaishibouFlgK.checked}"
															style="#{pc_Kmi00801.propTaishibouFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_d" width="133"><h:outputText
															styleClass="outputText" id="text114"
															value="#{pc_Kmi00801.propSiryokFlgK.labelName}"
															style="#{pc_Kmi00801.propSiryokFlgK.labelStyle}"></h:outputText></TH>
														<TD width="120"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlSiryokFlgK"
															value="#{pc_Kmi00801.propSiryokFlgK.checked}"
															style="#{pc_Kmi00801.propSiryokFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_a" width="133"><h:outputText
															styleClass="outputText" id="text106"
															value="#{pc_Kmi00801.propChoryokFlgK.labelName}"
															style="#{pc_Kmi00801.propChoryokFlg.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlChoryokFlgK"
															value="#{pc_Kmi00801.propChoryokFlgK.checked}"
															style="#{pc_Kmi00801.propChoryokFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_f"><h:outputText styleClass="outputText"
															id="text111"
															value="#{pc_Kmi00801.propOgioFlgK.labelName}"
															style="#{pc_Kmi00801.propOgioFlgK.labelStyle}"></h:outputText></TH>
														<TD width="125"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlOgioFlgK"
															value="#{pc_Kmi00801.propOgioFlgK.checked}"
															style="#{pc_Kmi00801.propOgioFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_e" width="124"><h:outputText
															styleClass="outputText" id="text115"
															value="#{pc_Kmi00801.propKetuatuFlgK.labelName}"
															style="#{pc_Kmi00801.propKetuatuFlgK.labelStyle}"></h:outputText></TH>
														<TD width="139"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlKetuatuFlgK"
															value="#{pc_Kmi00801.propKetuatuFlgK.checked}"
															style="#{pc_Kmi00801.propKetuatuFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_b" width="133"><h:outputText
															styleClass="outputText" id="text117"
															value="#{pc_Kmi00801.propNyoFlgK.labelName}"
															style="#{pc_Kmi00801.propNyoFlgK.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlNyoFlgK"
															value="#{pc_Kmi00801.propNyoFlgK.checked}"
															style="#{pc_Kmi00801.propNyoFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_a"><h:outputText styleClass="outputText"
															id="text112"
															value="#{pc_Kmi00801.propBloodFlgK.labelName}"
															style="#{pc_Kmi00801.propBloodFlgK.labelStyle}"></h:outputText></TH>
														<TD width="125"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlBloodFlgK"
															value="#{pc_Kmi00801.propBloodFlgK.checked}"
															style="#{pc_Kmi00801.propBloodFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_f" width="124"><h:outputText
															styleClass="outputText" id="text116"
															value="#{pc_Kmi00801.propXsenFlgK.labelName}"
															style="#{pc_Kmi00801.propXsenFlgK.labelStyle}"></h:outputText></TH>
														<TD width="139"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlXsenFlgK"
															value="#{pc_Kmi00801.propXsenFlgK.checked}"
															style="#{pc_Kmi00801.propBloodFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TH class="v_c" width="133"><h:outputText
															styleClass="outputText" id="text118"
															value="#{pc_Kmi00801.propSinDenZfFlgK.labelName}"
															style="#{pc_Kmi00801.propSinDenZfFlgK.labelStyle}"></h:outputText></TH>
														<TD><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlSinDenZfFlgK"
															value="#{pc_Kmi00801.propSinDenZfFlgK.checked}"
															style="#{pc_Kmi00801.propSinDenZfFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
													</TR>
													<TR>
														<TH class="v_b"><h:outputText styleClass="outputText"
															id="text113"
															value="#{pc_Kmi00801.propNaikaFlgK.labelName}"
															style="#{pc_Kmi00801.propNaikaFlgK.labelStyle}"></h:outputText></TH>
														<TD width="125"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlNaikaFlgK"
															value="#{pc_Kmi00801.propNaikaFlgK.checked}"
															style="#{pc_Kmi00801.propNaikaFlgK.style}"></h:selectBooleanCheckbox>受診</TD>
														<TD class="v_a" width="124" colspan="4"></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD align="center">
											<TABLE width="100%" border="0" cellpadding="0"
												cellspacing="0" class="button_bar">
												<TBODY>
													<TR>
														<TD><hx:commandExButton type="submit" value="確定"
															styleClass="commandExButton_dat" id="registerK"
															action="#{pc_Kmi00801.doRegisterKAction}"
															confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
															<hx:commandExButton
															type="submit" value="削除" styleClass="commandExButton_dat"
															id="deleteK" action="#{pc_Kmi00801.doDeleteKAction}"
															confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton>
															<hx:commandExButton
															type="submit" value="クリア"
															styleClass="commandExButton_dat" id="clearK"
															action="#{pc_Kmi00801.doClearKAction}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</odc:bfPanel>
							<f:facet name="back">
								<hx:commandExButton type="submit" value="&lt; Back"
									id="tabbedPanel1_back" style="display:none"></hx:commandExButton>
							</f:facet>
							<f:facet name="next">
								<hx:commandExButton type="submit" value="Next &gt;"
									id="tabbedPanel1_next" style="display:none"></hx:commandExButton>
							</f:facet>
							<f:facet name="finish">
								<hx:commandExButton type="submit" value="Finish"
									id="tabbedPanel1_finish" style="display:none"></hx:commandExButton>
							</f:facet>
							<f:facet name="cancel">
								<hx:commandExButton type="submit" value="Cancel"
									id="tabbedPanel1_cancel" style="display:none"></hx:commandExButton>
							</f:facet>
						</odc:tabbedPanel></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kmi00801.propKmiKsdKmk.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>

	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>
