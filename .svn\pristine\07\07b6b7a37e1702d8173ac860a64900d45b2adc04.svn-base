<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaa00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>注文書出力</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function alChk(thisObj, thisEvent) {
	check('htmlChotList','htmlCheck');
}
function alUnchk(thisObj, thisEvent) {
	uncheck('htmlChotList','htmlCheck');
}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function submitMethod() {
	// 発行者名称のdisabledを一旦解除
	var hakkoShaName = document.getElementById("form1:htmlHakkoShaName");
	hakkoShaName.disabled = false;
	return true;
}

function func_TypeSelect(thisObj, thisEvent) {
	// 発行者ラジオボタン判定
	// 「その他」を選択した場合は発行者名称を入力可能にする
    var obj = document.getElementsByName('form1:htmlHakkoSha');
	var hakkoSha = "";
	for (i = 0; obj.length > i; i++) {
			 if (obj[i].checked) {
			 		 hakkoSha = obj[i].value;
			 }
	}    	
	if (hakkoSha == "9") {
		setHakkoShaNameDisabled(false);
	} else {
		setHakkoShaNameDisabled(true);
	}
}

function setHakkoShaNameDisabled(flg) {
	// 発行者名称の入力可・不可設定

	var hakkoShaName = document.getElementById("form1:htmlHakkoShaName");
	var srcProp;
	if (flg) {
		srcProp = document.getElementById("form1:htmlDisabledSmp");
	} else {
		srcProp = document.getElementById("form1:htmlNormalSmp");
		
	}
	hakkoShaName.style.cssText = srcProp.style.cssText;
	hakkoShaName.disabled = flg;
	
}

function endload() {
	// ページ遷移時のイベントを設定	
	func_TypeSelect(document.getElementById('form1:htmlHakkoSha'), '');
　　changeScrollPosition('scroll', 'listScroll');
}

window.attachEvent("onload", endload);

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kaa00501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kaa00501.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kaa00501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kaa00501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> 
			　<!-- レイアウト対応 全角スペース -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="100%" class="" style="margin-top:0px">
				<TBODY>
					<TR>
						<TD width="3%"></TD>
						<TD width="880">
						<DIV>
			<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="880">
				<TBODY>
					<TR>
						<TH class="v_a" width="140"><h:outputText
							styleClass="outputText" id="lblChotNendo"
							style="#{pc_Kaa00501.propChotNendo.labelStyle}"
							value="#{pc_Kaa00501.propChotNendo.labelName}">
						</h:outputText></TH>
						<TD width="220">
							<h:inputText id="htmlChotNendo" styleClass="inputText"
								style="#{pc_Kaa00501.propChotNendo.style}"
								value="#{pc_Kaa00501.propChotNendo.dateValue}"
								tabindex="1" size="4">
							<f:convertDateTime type="date" pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>
						</TD>
							<TH class="v_b" width="140"><h:outputText
								styleClass="outputText" id="lblChotNo"
								style="#{pc_Kaa00501.propChotNo.labelStyle}"
								value="#{pc_Kaa00501.propChotNo.labelName}">
							</h:outputText></TH>							
						<TD width="300">
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD colspan="2">
										<h:inputText id="htmlChotNo" styleClass="inputText"
												style="#{pc_Kaa00501.propChotNo.style}"
												value="#{pc_Kaa00501.propChotNo.stringValue}"
												maxlength="#{pc_Kaa00501.propChotNo.maxLength}" size="20"
												tabindex="2">
											</h:inputText>
										<h:outputText
											styleClass="outputText" 
											value="（前方一致）"
											id="lblKanriBumonCdFindType">
										</h:outputText>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblSinseiDate"
							value="#{pc_Kaa00501.propSinseiDateFrom.labelName}"
							style="#{pc_Kaa00501.propSinseiDateFrom.labelStyle}">
						</h:outputText></TH>
						<TD nowrap>
							<h:inputText id="htmlSinseiDateFrom" styleClass="inputText"
								style="#{pc_Kaa00501.propSinseiDateFrom.style}"
								value="#{pc_Kaa00501.propSinseiDateFrom.dateValue}" size="10"
								tabindex="3">
								<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
							</h:inputText>
							<h:outputText 
								styleClass="outputText" 
								value="～"
								id="lblKara1">
							</h:outputText>
							<h:inputText id="htmlSinseiDateTo" styleClass="inputText"
								style="#{pc_Kaa00501.propSinseiDateTo.style}"
								value="#{pc_Kaa00501.propSinseiDateTo.dateValue}" size="10"
								tabindex="4">
								<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
							<TH class="v_d"><h:outputText
								styleClass="outputText" id="lblRingiNo"
								style="#{pc_Kaa00501.propRingiNo.labelStyle}"
								value="#{pc_Kaa00501.propRingiNo.labelName}">
							</h:outputText></TH>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
							<TR>
								<TD width="100">
									<h:inputText id="htmlRingiNo" styleClass="inputText"
										style="#{pc_Kaa00501.propRingiNo.style}"
										value="#{pc_Kaa00501.propRingiNo.stringValue}"
										maxlength="#{pc_Kaa00501.propRingiNo.maxLength}" size="30"
										tabindex="5">
									</h:inputText>
								</TD>
								<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRingiNoFindType"
										value="#{pc_Kaa00501.propRingiNoFindType.stringValue}"
										tabindex="6">
										<f:selectItems
											value="#{pc_Kaa00501.propRingiNoFindType.list}" />
									</h:selectOneRadio>
								</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>			
					<TR>
						<TH class="v_e"><h:outputText styleClass="outputText"
							id="lblNonyuDate"
							value="#{pc_Kaa00501.propNonyuDateFrom.name}"
							style="#{pc_Kaa00501.propNonyuDateFrom.labelStyle}">
						</h:outputText></TH>
						<TD nowrap>
							<h:inputText id="htmlNonyuDateFrom" styleClass="inputText"
								style="#{pc_Kaa00501.propNonyuDateFrom.style}"
								value="#{pc_Kaa00501.propNonyuDateFrom.dateValue}" size="10"
								tabindex="7">
								<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
							</h:inputText>
							<h:outputText 
								styleClass="outputText" 
								value="～"
								id="lblKara2">
							</h:outputText>
							<h:inputText id="htmlNonyuDateTo" styleClass="inputText"
								style="#{pc_Kaa00501.propNonyuDateTo.style}"
								value="#{pc_Kaa00501.propNonyuDateTo.dateValue}" size="10"
								tabindex="8">
								<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
						<TH class="v_f"><h:outputText
								styleClass="outputText" id="lblChotName"
								style="#{pc_Kaa00501.propChotName.labelStyle}"
								value="#{pc_Kaa00501.propChotName.labelName}">
							</h:outputText></TH>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
							<TR>
								<TD width="100">
									<h:inputText id="htmlChotName" styleClass="inputText"
											style="#{pc_Kaa00501.propChotName.style}"
											value="#{pc_Kaa00501.propChotName.stringValue}"
											maxlength="#{pc_Kaa00501.propChotName.maxLength}" size="30"
											tabindex="9">
										</h:inputText>
								</TD>
								<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlChotNameFindType"
											value="#{pc_Kaa00501.propChotNameFindType.stringValue}"
											tabindex="10">
											<f:selectItems
												value="#{pc_Kaa00501.propChotNameFindType.list}" />
										</h:selectOneRadio>
								</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>			
					<TR>
						<TH class="v_g"><h:outputText styleClass="outputText"
							id="lblShutokusakiCd"
							style="#{pc_Kaa00501.propShutokusakiCd.labelStyle}"
							value="#{pc_Kaa00501.propShutokusakiCd.labelName}">
						</h:outputText></TH>
						<TD nowrap>
							<h:inputText id="htmlShutokusakiCd" styleClass="inputText"
									style="#{pc_Kaa00501.propShutokusakiCd.style}"
									value="#{pc_Kaa00501.propShutokusakiCd.stringValue}"
									maxlength="#{pc_Kaa00501.propShutokusakiCd.maxLength}" 
									size="20"
									tabindex="11">
							</h:inputText>
							<hx:commandExButton type="submit"
									styleClass="commandExButton_search" id="searchShutokusakiCd"
									action="#{pc_Kaa00501.doSearchShutokusakiCdAction}" tabindex="12">
							</hx:commandExButton>
						</TD>
							<TH class="v_a"><h:outputText
									styleClass="outputText" id="lblShutokusakiName"
									style="#{pc_Kaa00501.propShutokusakiName.labelStyle}"
									value="#{pc_Kaa00501.propShutokusakiName.labelName}">
								</h:outputText></TH>
							<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="100">
										<h:inputText id="htmlShutokusakiName" styleClass="inputText"
												style="#{pc_Kaa00501.propShutokusakiName.style}"
												value="#{pc_Kaa00501.propShutokusakiName.stringValue}"
												maxlength="#{pc_Kaa00501.propShutokusakiName.maxLength}" size="30"
												tabindex="13">
											</h:inputText>
									</TD>
									<TD>
										<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlShutokusakiNameFindType"
												value="#{pc_Kaa00501.propShutokusakiNameFindType.stringValue}"
												tabindex="14">
												<f:selectItems
													value="#{pc_Kaa00501.propShutokusakiNameFindType.list}" />
											</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>			
					<TR>
						<TH class="v_b"><h:outputText styleClass="outputText"
							id="lblTyumonOutKbn" value="#{pc_Kaa00501.propTyumonOutKbn.name}"
							style="#{pc_Kaa00501.propTyumonOutKbn.labelStyle}">
						</h:outputText></TH>
						<TD colspan="3">
							<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlTyumonOutKbn"
									value="#{pc_Kaa00501.propTyumonOutKbn.stringValue}"
									tabindex="15">
									<f:selectItems
										value="#{pc_Kaa00501.propTyumonOutKbn.list}" />
								</h:selectOneRadio>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
							</DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD style="margin-top:10px;">
							<hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search"
							action="#{pc_Kaa00501.doSearchAction}" tabindex="16"></hx:commandExButton>
							<hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clearSearchItem"
							action="#{pc_Kaa00501.doClearSearchItemAction}" tabindex="17"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%" class="" style="margin-top:0px">
				<TBODY>
					<TR>
						<TD width="3%"></TD>
						<TD width="880" align="right"><h:outputText
							styleClass="outputText" id="htmlListCount"
							value="#{pc_Kaa00501.propChotList.listCount}"
							style="font-size: 8pt"></h:outputText><SPAN
							style="font-size: 8pt">件</SPAN></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE  border="0" cellpadding="0" cellspacing="0" width="880">
				<TBODY>
					<TR>
						<TD>
							<h:dataTable border="0" cellpadding="2" cellspacing="0"
								columnClasses="columnClass1" headerClass="headerClass"
								footerClass="footerClass"
								styleClass="meisai_scroll" id="htmlChotListHd"
								width="880">
								<h:column id="column1Hd">
									<f:facet name="header">
									</f:facet>
									<f:attribute value="30" name="width" />
									<f:attribute value="text-align: center; text-valign: middle"
										name="style" />
								</h:column>
								<h:column id="column2Hd">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="納期"
											id="lblListNonyuDate">
										</h:outputText>
									</f:facet>
									<f:attribute value="75" name="width" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
								<h:column id="column3Hd">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="申請日付"
											id="lblListSinseiDate">
										</h:outputText>
									</f:facet>
									<f:attribute value="75" name="width" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
								<h:column id="column4Hd">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="調達番号"
											id="lblListChotNo">
										</h:outputText>
									</f:facet>
									<f:attribute value="160" name="width" />
								</h:column>
								<h:column id="column5Hd">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="件名"
											id="lblListChotName">
										</h:outputText>
									</f:facet>
									<f:attribute value="260" name="width" />
									<f:attribute value="text-align: left" name="style" />
									<f:attribute value="true" name="nowrap" />
								</h:column>
								<h:column id="column6Hd">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="稟議番号"
											id="lblListRingiNo">
										</h:outputText>
									</f:facet>
									<f:attribute value="200" name="width" />
									<f:attribute value="text-align: left" name="style" />
									<f:attribute value="true" name="nowrap" />
								</h:column>
								<h:column id="column7Hd">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="出力回数"
											id="lblListTyumonOutCnt">
										</h:outputText>
									</f:facet>
									<f:attribute value="80" name="width" />
									<f:attribute value="padding-right: 3px; text-align: right"
										name="style" />								
								</h:column>
							</h:dataTable>
						</TD>
					</TR>
					<TR>
						<TD>
							<DIV style="height: 135px; OVERFLOW:scroll;overflow-x: hidden;" 
								 id="listScroll" 
								 onscroll="setScrollPosition('scroll',this);" 
								 class="listScroll">
								<h:dataTable border="0" cellpadding="2" cellspacing="0"
									columnClasses="columnClass1" headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Kaa00501.propChotList.rowClasses}"
									styleClass="meisai_scroll" id="htmlChotList"
									value="#{pc_Kaa00501.propChotList.list}" var="varlist"
									width="860">
									<h:column id="column1">
										<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
											id="htmlCheck" value="#{varlist.outputCheck}" tabindex="18">
										</h:selectBooleanCheckbox>
										<f:attribute value="30" name="width" />
										<f:attribute value="text-align: center; text-valign: middle"
											name="style" />
									</h:column>
									<h:column id="column2">
										<f:attribute value="75" name="width" />
										<h:outputText styleClass="outputText" id="htmlListNonyuDate"
											value="#{varlist.nonyuDate}">
											<f:convertDateTime pattern="yyyy/MM/dd" />
										</h:outputText>
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									<h:column id="column3">
										<f:attribute value="75" name="width" />
										<h:outputText styleClass="outputText" id="htmlListSinseiDate"
											value="#{varlist.sinseiDate}">
											<f:convertDateTime pattern="yyyy/MM/dd" />
										</h:outputText>
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									<h:column id="column4">
										<f:attribute value="160" name="width" />
										<h:outputText styleClass="outputText" id="htmlListChotNo"
											value="#{varlist.chotNo}" 
											title="#{varlist.chotNo}"></h:outputText>
									</h:column>
									<h:column id="column5">
										<hx:jspPanel id="jspPanelL1">
											<DIV
												style="width:260px;white-space:nowrap;overflow:hidden;display:block;">
											<h:outputText id="htmlListChotName"
												value="#{varlist.chotName.stringValue}"
												styleClass="outputText"
												title="#{varlist.chotName.stringValue}">
											</h:outputText></DIV>
										</hx:jspPanel>
										<f:attribute value="260" name="width" />
										<f:attribute value="text-align: left" name="style" />
										<f:attribute value="true" name="nowrap" />
									</h:column>
									<h:column id="column6">
										<hx:jspPanel id="jspPanelL2">
											<DIV
												style="width:200px;white-space:nowrap;overflow:hidden;display:block;">
											<h:outputText id="htmlListRingiNo"
												value="#{varlist.ringiNo.stringValue}"
												styleClass="outputText"
												title="#{varlist.ringiNo.stringValue}">
											</h:outputText></DIV>
										</hx:jspPanel>
										<f:attribute value="200" name="width" />
										<f:attribute value="text-align: left" name="style" />
										<f:attribute value="true" name="nowrap" />
									</h:column>
									<h:column id="column7">
										<f:attribute value="56" name="width" />
										<h:outputText styleClass="outputText" id="htmlListTyumonOutCnt"
											value="#{varlist.tyumonOutCnt}" style="text-align: right">
											<f:convertNumber pattern="##0;##0" />
										</h:outputText>
										<f:attribute value="padding-right: 3px; text-align: right"
											name="style" />								
									</h:column>
								</h:dataTable>
							</DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
				<TD width="3%"></TD>
				<TD width="880" style="text-align: left">
					<hx:panelBox styleClass="panelBox" id="box1">
						<hx:jspPanel id="jspPanel1">
							<hx:commandExButton type="button" 
												value="on" 
												styleClass="check" 
												id="check" 
												onclick="return alChk(this, event);" tabindex="19">
							</hx:commandExButton>
							<hx:commandExButton type="button" 
												value="off" 
												styleClass="uncheck" 
												id="uncheck" 
												onclick="return alUnchk(this, event);" tabindex="20">
							</hx:commandExButton>							
						</hx:jspPanel>
					</hx:panelBox>
				</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%" class="" style="margin-top:0px">
				<TBODY>
					<TR>
						<TD width="3%"></TD>
						<TD width="880">
						<DIV>
			<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="880">
				<TBODY>
					<TR>
						<TH class="v_c" width="167"><h:outputText styleClass="outputText"
							id="lblHakkoDate"
							value="#{pc_Kaa00501.propHakkoDate.name}"
							style="#{pc_Kaa00501.propHakkoDate.labelStyle}">
						</h:outputText></TH>
						<TD  nowrap width="711">
							<h:inputText id="htmlHakkoDate" styleClass="inputText"
										style="#{pc_Kaa00501.propHakkoDate.style}"
										value="#{pc_Kaa00501.propHakkoDate.dateValue}" size="12"
										tabindex="21">
										<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText>							
						</TD>
					</TR>		
					<TR>
						<TH class="v_c" width="167">
						<h:outputText styleClass="outputText" id="lblJuryouDate"
										value="#{pc_Kaa00501.propJuryouDate.name}"
										style="#{pc_Kaa00501.propJuryouDate.labelStyle}"></h:outputText></TH>
						<TD  nowrap width="711">						
						<h:inputText styleClass="inputText" id="htmlJuryouDate"
										style="#{pc_Kaa00501.propJuryouDate.style}"
										value="#{pc_Kaa00501.propJuryouDate.dateValue}" size="12" tabindex="22">
										<f:convertDateTime pattern="yyyy/MM/dd" />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
					</TR>		
					<TR>
						<TH class="v_d" width="167"><h:outputText styleClass="outputText"
							id="lblHojinName" value="#{pc_Kaa00501.propHojinName.labelName}"
							style="#{pc_Kaa00501.propHojinName.labelStyle}">
						</h:outputText></TH>
						<TD width="711">
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD>
										<h:inputText id="htmlHojinName" styleClass="inputText"
												style="#{pc_Kaa00501.propHojinName.style}"
												value="#{pc_Kaa00501.propHojinName.stringValue}"
												maxlength="#{pc_Kaa00501.propHojinName.maxLength}" size="80"
												tabindex="23">
											</h:inputText>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_e" width="167"><h:outputText styleClass="outputText"
							id="lblHakkoSha"
							style="#{pc_Kaa00501.propHakkoSha.labelStyle}"
							value="#{pc_Kaa00501.propHakkoSha.name}">
						</h:outputText></TH>
						<TD width="711">
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
							<TR>
								<TD width="285">
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlHakkoSha"
										value="#{pc_Kaa00501.propHakkoSha.stringValue}"
										tabindex="24"
										onclick="return func_TypeSelect(this, event);">
										<f:selectItems
											value="#{pc_Kaa00501.propHakkoSha.list}" />
									</h:selectOneRadio>
								</TD>
								<TD>
									<h:inputText id="htmlHakkoShaName" styleClass="inputText"
										style="#{pc_Kaa00501.propHakkoShaName.style}"
										disabled="#{pc_Kaa00501.propHakkoShaName.disabled}"
										value="#{pc_Kaa00501.propHakkoShaName.stringValue}"
												maxlength="#{pc_Kaa00501.propHakkoShaName.maxLength}"  size="60"
										tabindex="25">
									</h:inputText>
								</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="167"><h:outputText styleClass="outputText"
							id="lblJuryouShoOutFlg"
							value="#{pc_Kaa00501.propJuryouShoOutFlg.name}"
							style="#{pc_Kaa00501.propJuryouShoOutFlg.labelStyle}">
						</h:outputText></TH>
						<TD width="711">
							<h:selectManyCheckbox styleClass="selectManyCheckbox setWidth"
								id="htmlJuryouShoOutFlg"
								value="#{pc_Kaa00501.propJuryouShoOutFlg.value}"
								disabledClass="selectManyCheckbox_Disabled" tabindex="26"
								readonly="#{pc_Kaa00501.propJuryouShoOutFlg.readonly}"
								disabled="#{pc_Kaa00501.propJuryouShoOutFlg.disabled}">
								<f:selectItems
									value="#{pc_Kaa00501.propJuryouShoOutFlg.list}" />
							</h:selectManyCheckbox>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="167"><h:outputText styleClass="outputText"
										id="lblChumonChohyoTitle"
										style="#{pc_Kaa00501.propChumonChohyoTitle.labelStyle}"
										value="#{pc_Kaa00501.propChumonChohyoTitle.labelName}"></h:outputText></TH>
						<TD width="711">
						<h:inputText styleClass="inputText" id="htmlChumonChohyoTitle"
										maxlength="#{pc_Kaa00501.propChumonChohyoTitle.maxLength}"
										size="100" style="#{pc_Kaa00501.propChumonChohyoTitle.style}"
										value="#{pc_Kaa00501.propChumonChohyoTitle.stringValue}"
										readonly="#{pc_Kaa00501.propChumonChohyoTitle.readonly}"
										rendered="#{pc_Kaa00501.propChumonChohyoTitle.rendered}"
										disabled="#{pc_Kaa00501.propChumonChohyoTitle.disabled}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_f" width="167"><h:outputText styleClass="outputText"
										id="lblJuryouChohyoTitle"
										style="#{pc_Kaa00501.propJuryouChohyoTitle.labelStyle}"
										value="#{pc_Kaa00501.propJuryouChohyoTitle.labelName}"></h:outputText></TH>
						<TD width="711">
						<h:inputText styleClass="inputText" id="htmlJuryouChohyoTitle"
										maxlength="#{pc_Kaa00501.propJuryouChohyoTitle.maxLength}"
										size="100" style="#{pc_Kaa00501.propJuryouChohyoTitle.style}"
										value="#{pc_Kaa00501.propJuryouChohyoTitle.stringValue}"
										readonly="#{pc_Kaa00501.propJuryouChohyoTitle.readonly}"
										rendered="#{pc_Kaa00501.propJuryouChohyoTitle.rendered}"
										disabled="#{pc_Kaa00501.propJuryouChohyoTitle.disabled}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_f" width="167"><h:outputText styleClass="outputText"
										id="lblOuinran" value="#{pc_Kaa00501.propOuinran1.labelName}"
										style="#{pc_Kaa00501.propOuinran1.labelStyle}"></h:outputText></TH>
						<TD colspan="" width="711">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
							<TR>
							<TD width="20%" class="clear_border">
							<h:inputText styleClass="inputText" id="htmlOuinran1"
													value="#{pc_Kaa00501.propOuinran1.stringValue}"
													style="#{pc_Kaa00501.propOuinran1.style}"
													maxlength="#{pc_Kaa00501.propOuinran1.maxLength}"></h:inputText>
							</TD>
							<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlOuinran2"
													value="#{pc_Kaa00501.propOuinran2.stringValue}"
													style="#{pc_Kaa00501.propOuinran2.style}"
													maxlength="#{pc_Kaa00501.propOuinran2.maxLength}"></h:inputText>
							</TD>
							<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlOuinran3"
													style="#{pc_Kaa00501.propOuinran3.style}"
													value="#{pc_Kaa00501.propOuinran3.stringValue}"
													maxlength="#{pc_Kaa00501.propOuinran3.maxLength}"></h:inputText>
							</TD>
							<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlOuinran4"
													style="#{pc_Kaa00501.propOuinran4.style}"
													value="#{pc_Kaa00501.propOuinran4.stringValue}"
													maxlength="#{pc_Kaa00501.propOuinran4.maxLength}"></h:inputText>
							</TD>
							</TR>
							</TBODY>
						</TABLE>
						</TD>
						</TR>
										<TR>
						<TH class="v_f" width="167"><h:outputText styleClass="outputText"
										id="lblJuryousyoOuinran" value="#{pc_Kaa00501.propJuryousyoOuinran1.labelName}"
										style="#{pc_Kaa00501.propJuryousyoOuinran1.labelStyle}"></h:outputText></TH>
						<TD colspan="" width="711">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
							<TR>
							<TD width="20%" class="clear_border">
							<h:inputText styleClass="inputText" id="htmlJuryousyoOuinran1"
													value="#{pc_Kaa00501.propJuryousyoOuinran1.stringValue}"
													style="#{pc_Kaa00501.propJuryousyoOuinran1.style}"
													maxlength="#{pc_Kaa00501.propJuryousyoOuinran1.maxLength}"></h:inputText>
							</TD>
							<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlJuryousyoOuinran2"
													value="#{pc_Kaa00501.propJuryousyoOuinran2.stringValue}"
													style="#{pc_Kaa00501.propJuryousyoOuinran2.style}"
													maxlength="#{pc_Kaa00501.propJuryousyoOuinran2.maxLength}"></h:inputText>
							</TD>
							<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlJuryousyoOuinran3"
													style="#{pc_Kaa00501.propJuryousyoOuinran3.style}"
													value="#{pc_Kaa00501.propJuryousyoOuinran3.stringValue}"
													maxlength="#{pc_Kaa00501.propJuryousyoOuinran3.maxLength}"></h:inputText>
							</TD>
							<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlJuryousyoOuinran4"
													style="#{pc_Kaa00501.propJuryousyoOuinran4.style}"
													value="#{pc_Kaa00501.propJuryousyoOuinran4.stringValue}"
													maxlength="#{pc_Kaa00501.propJuryousyoOuinran4.maxLength}"></h:inputText>
							</TD>
							</TR>
							</TBODY>
						</TABLE>
						</TD>
						</TR>
					<TR>
						<TH class="v_g" width="167"><h:outputText styleClass="outputText"
							id="lblShiharaiJouken"
							value="#{pc_Kaa00501.propShiharaiJouken.labelName}"
							style="#{pc_Kaa00501.propShiharaiJouken.labelStyle}">
						</h:outputText></TH>
						<TD width="711">
							<h:inputTextarea styleClass="inputTextarea"
								id="htmlShiharaiJouken" cols="70" rows="2"
								value="#{pc_Kaa00501.propShiharaiJouken.stringValue}"
								style="#{pc_Kaa00501.propShiharaiJouken.style}" tabindex="27">
							</h:inputTextarea>
						</TD>
					</TR>
					<TR>
						<TH class="v_g" width="167">
						<h:outputText styleClass="outputText" id="lblTeishutsusakiInji"
							value="#{pc_Kaa00501.propTeishutsusakiInji.labelName}"
							style="#{pc_Kaa00501.propTeishutsusakiInji.labelStyle}"></h:outputText></TH>
						<TD width="711"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlTeishutsusakiInji"
										disabled="#{pc_Kaa00501.propTeishutsusakiInji.disabled}"
										readonly="#{pc_Kaa00501.propTeishutsusakiInji.readonly}"
										rendered="#{pc_Kaa00501.propTeishutsusakiInji.rendered}"
										style="#{pc_Kaa00501.propTeishutsusakiInji.style}"
										value="#{pc_Kaa00501.propTeishutsusakiInji.stringValue}">
										<f:selectItems
											value="#{pc_Kaa00501.propTeishutsusakiInji.list}" />
									</h:selectManyCheckbox></TD>
					</TR>
				</TBODY>
			</TABLE>
							</DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>		
			<BR>
			<TABLE width="880" border="0" style="" class="button_bar">
				<TBODY>
				<TR>
					<TD>
						<hx:commandExButton
							type="submit"
							value="クリア" tabindex="28"
							styleClass="commandExButton_etc"
							id="clear"
							action="#{pc_Kaa00501.doClearAction}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="PDF作成" tabindex="29"
							styleClass="commandExButton_out"
							id="pdfout"
							action="#{pc_Kaa00501.doPdfoutAction}"
							confirm="#{msg.SY_MSG_0019W}">
						</hx:commandExButton>
						<hx:commandExButton 
							type="submit" 
							value="EXCEL作成" tabindex="29"
							styleClass="commandExButton_out" 
							id="excelout"
							action="#{pc_Kaa00501.doExcelOutAction}"  
							confirm="#{msg.SY_MSG_0027W}" disabled="#{pc_Kaa00501.propExcelout.disabled}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="CSV作成" tabindex="30"
							styleClass="commandExButton_out"
							id="csvout"
							action="#{pc_Kaa00501.doCsvoutAction}"
							confirm="#{msg.SY_MSG_0020W}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="出力項目指定" tabindex="31"
							styleClass="commandExButton_out"
							id="setoutput"
							action="#{pc_Kaa00501.doSetoutputAction}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="優先順位指定" tabindex="32"
							styleClass="commandExButton_out"
							id="setorder"
							action="#{pc_Kaa00501.doSetorderAction}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="印刷" tabindex="33"
							styleClass="commandExButton_out"
							id="print"
							action="#{pc_Kaa00501.doPrintAction}"
							confirm="#{msg.SY_MSG_0022W}">
						</hx:commandExButton>
					</TD>
				</TR>
					
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kaa00501.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kaa00501.propChotList.scrollPosition}"
				id="scroll"></h:inputHidden>
			<div style="display:none;">
			<h:inputText styleClass="inputText"
				id="htmlNormalSmp" size="40"
				disabled="#{pc_Kaa00501.propNormalSmp.disabled}"
				readonly="#{pc_Kaa00501.propNormalSmp.readonly}"
				style="#{pc_Kaa00501.propNormalSmp.style}"
				value="#{pc_Kaa00501.propNormalSmp.stringValue}">
			</h:inputText>
			<h:inputText styleClass="inputText"
				id="htmlDisabledSmp" size="40"
				disabled="#{pc_Kaa00501.propDisabledSmp.disabled}"
				readonly="#{pc_Kaa00501.propDisabledSmp.readonly}"
				style="#{pc_Kaa00501.propDisabledSmp.style}"
				value="#{pc_Kaa00501.propDisabledSmp.stringValue}">
			</h:inputText>
			</div>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

