<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmc01902T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmc01902T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >   

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
    return true;
}
function func_2(thisObj, thisEvent) {
    var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlJinjiCd";
    openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
    return true;

}
function doKyoinAjax(thisObj, thisEvent) {
    // 教員名称を取得する


    var servlet = "rev/co/CobJinjAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    var target = "form1:htmlName";
    
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, target, args);
}

function delConfirm(message) {
    var param = new Array("リスト情報");
    return confirm(messageCreate(message,param));
}

function loadAction(event){
 doKyoinAjax(document.getElementById('form1:htmlJinjiCd'), event);
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmc01902T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />        

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
    styleClass="commandExButton" id="closeDisp"
    action="#{pc_Kmc01902T02.doCloseDispAction}"
    ></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kmc01902T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName"
                value="#{pc_Kmc01902T02.screenName}"></h:outputText>
</div>          
            
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
    styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
            
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
                styleClass="commandExButton" id="returnDisp" action="#{pc_Kmc01902T02.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">          
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
            <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
                <TBODY>
                    <TR>
                        <TD align="center">
                        <TABLE border="0" cellspacing="0" class="table" cellpadding="0"
                            width="600">
                            <TBODY>
                                <TR>
                                    <TH bgcolor="" width="150" class="v_a"><h:outputText
                                        styleClass="outputText" id="lblNendo"
                                        value="#{pc_Kmc01902T01.kmc01902.propNendo.labelName}"
                                        style="#{pc_Kmc01902T01.kmc01902.propNendo.labelStyle}"></h:outputText></TH>
                                    <TD width="150"><h:outputText styleClass="outputText"
                                        id="htmlNendo"
                                        value="#{pc_Kmc01902T02.kmc01902.propNendo.integerValue}"
                                        style="#{pc_Kmc01902T02.kmc01902.propNendo.style}"></h:outputText></TD>
                                    <TH width="150" class="v_b"><h:outputText
                                        styleClass="outputText" id="lblGakkiNo"
                                        value="#{pc_Kmc01902T01.kmc01902.propGakkiNo.labelName}"
                                        style="#{pc_Kmc01902T01.kmc01902.propGakkiNo.labelStyle}"></h:outputText></TH>
                                    <TD width="150"><h:outputText styleClass="outputText"
                                        id="htmlGakkiNo"
                                        value="#{pc_Kmc01902T02.kmc01902.propGakkiNo.stringValue}"
                                        style="#{pc_Kmc01902T02.kmc01902.propGakkiNo.style}"></h:outputText></TD>
                                </TR>
                                <TR>
                                    <TH bgcolor="" width="150" class="v_c"><h:outputText
                                        styleClass="outputText" id="lblPdfTitle"
                                        value="#{pc_Kmc01902T01.kmc01902.propPdfTitle.labelName}"
                                        style="#{pc_Kmc01902T01.kmc01902.propPdfTitle.labelStyle}"></h:outputText></TH>
                                    <TD width="450" colspan="3"><h:inputText styleClass="inputText"
                                        id="htmlPdfTitle"
                                        value="#{pc_Kmc01902T02.kmc01902.propPdfTitle.stringValue}"
                                        style="#{pc_Kmc01902T02.kmc01902.propPdfTitle.style}"
                                        size="60"
                                        maxlength="#{pc_Kmc01902T02.kmc01902.propPdfTitle.maxLength}"></h:inputText></TD>
                                </TR>
                            </TBODY>
                        </TABLE></TD>
                    </TR>
                    <TR>
                        <TD align="center"><BR>
                        </TD>
                    </TR>
                    <TR>
                        <TD align="center">
                        <TABLE border="0" cellpadding="0" cellspacing="0">
                            <TBODY>
                                <TR>
                                    <TD align="left"><TABLE border="0" cellpadding="0" cellspacing="0">
                                        <TBODY>
                                            <TR>
                                                <TD><hx:commandExButton type="submit" value="一括指定"
                                                    styleClass="tab_head_off" id="ikkatsuSitei"
                                                    action="#{pc_Kmc01902T02.doIkkatsuSiteiAction}"></hx:commandExButton></TD>
                                                <TD><hx:commandExButton type="submit" value="教員指定"
                                                    styleClass="tab_head_on" id="kyoinSitei" action="#{pc_Kmc01902T02.doKyoinSiteiAction}"></hx:commandExButton></TD>
                                            </TR>
                                        </TBODY>
                                    </TABLE>
                                    </TD>
                                </TR>
                                <TR>
                                    <TD>
                                    <TABLE border="0" cellpadding="0" cellspacing="0" width="600" class="tab_body">
                                        <TBODY>
                                            <TR>
                                                <TD><TABLE border="0" cellpadding="0" cellspacing="0">
                                                    <TBODY>
                                                        <TR>
                                                            <TD align="left"><h:outputText styleClass="outputText"
                                                                id="lblGakusei" value="教員コード"></h:outputText></TD>
                                                        </TR>
                                                        <TR>
                                                            <TD>
                                                            <TABLE border="0" cellpadding="0" cellspacing="0"
                                                                width="600" class="table">
                                                                <TBODY>
                                                                    <TR>
                                                                        <TH bgcolor="" width="150" class="v_a"><h:outputText styleClass="outputText" id="lblInputFile" value="#{pc_Kmc01902T02.propInputFile.labelName}"></h:outputText><BR>
                                                                        <h:outputText styleClass="outputText" id="lblInputFileOld" value="#{pc_Kmc01902T02.propInputFileOld.labelName}" style="#{pc_Kmc01902T02.propInputFileOld.labelStyle}"></h:outputText>
                                                                        </TH>
                                                                        <TD colspan="2" width="450"><hx:fileupload
                                                                            styleClass="fileupload" id="htmlInputFile"
                                                                            value="#{pc_Kmc01902T02.propInputFile.value}"
                                                                            style="#{pc_Kmc01902T02.propInputFile.style};width:405px"
                                                                            size="50">
                                                                            <hx:fileProp name="fileName"
                                                                                value="#{pc_Kmc01902T02.propInputFile.fileName}" />
                                                                            <hx:fileProp name="contentType" />
                                                                        </hx:fileupload><hx:commandExButton type="submit"
                                                                            value="取込" styleClass="commandExButton"
                                                                            id="inputFile"
                                                                            action="#{pc_Kmc01902T02.doInputFileAction}"></hx:commandExButton><BR>
                                                                    <h:outputText styleClass="outputText" id="htmlInputFileOld" value="#{pc_Kmc01902T02.propInputFileOld.stringValue}"
                                                                            style="#{pc_Kmc01902T02.propInputFileOld.style}"></h:outputText><BR></TD>
                                                                    </TR>
                                                                    <TR>
                                                                        <TH bgcolor="" width="150" class="v_b"><h:outputText
                                                                            styleClass="outputText" id="lblJinjiCd"
                                                                            value="#{pc_Kmc01902T02.propJinjiCd.labelName}"
                                                                            style="#{pc_Kmc01902T02.propJinjiCd.labelStyle}"></h:outputText></TH>
                                                                        <TD width="250"><h:inputText styleClass="inputText"
                                                                            id="htmlJinjiCd"
                                                                            value="#{pc_Kmc01902T02.propJinjiCd.stringValue}"
                                                                            style="#{pc_Kmc01902T02.propJinjiCd.style}" size="20"
                                                                            onblur="return doKyoinAjax(this, event);"
                                                                            maxlength="#{pc_Kmc01902T02.propJinjiCd.maxLength}"></h:inputText><hx:commandExButton
                                                                            type="button" styleClass="commandExButton_search" id="search"
                                                                            action="#{pc_Kmc01902T02.doSearchAction}" onclick="return func_2(this, event);"></hx:commandExButton><hx:commandExButton
                                                                            type="submit" value="追加" styleClass="commandExButton"
                                                                            id="add" action="#{pc_Kmc01902T02.doAddAction}"></hx:commandExButton></TD>
                                                                        <TD width="200"><h:outputText
                                                                            styleClass="outputText" id="htmlName"
                                                                            value="#{pc_Kmc01902T02.propName.stringValue}"
                                                                            style="#{pc_Kmc01902T02.propName.style}"></h:outputText>&nbsp;</TD>
                                                                    </TR>
                                                                </TBODY>
                                                            </TABLE>
                                                            </TD>
                                                        </TR>
                                                    </TBODY>
                                                </TABLE>
                                                <BR>
                                                <HR noshade class="hr">
                                                <TABLE border="0" width="500" align="center">
                                                    <TBODY>
                                                        <TR>
                                                            <TD width="410" align="left"><h:outputText
                                                                styleClass="outputText" id="lblTargetKyoin"
                                                                value="#{pc_Kmc01902T02.propTargetKyoin.labelName}"
                                                                style="#{pc_Kmc01902T02.propTargetKyoin.labelStyle}"></h:outputText></TD>
                                                            <TD width="90" valign="top" align="center"></TD>
                                                        </TR>
                                                        <TR>
                                                            <TD><h:selectManyListbox styleClass="selectManyListbox"
                                                                id="htmlTargetKyoin" size="16" style="width: 100%"
                                                                value="#{pc_Kmc01902T02.propTargetKyoin.integerValue}">
                                                                <f:selectItems
                                                                    value="#{pc_Kmc01902T02.propTargetKyoin.list}" />
                                                            </h:selectManyListbox></TD>
                                                            <TD valign="top" align="center"><hx:commandExButton
                                                                type="submit" value="除外" styleClass="commandExButton"
                                                                id="remove" action="#{pc_Kmc01902T02.doRemoveAction}" style="width:60px"></hx:commandExButton><BR>
                                                            <h:outputText styleClass="outputText" id="lblManySelect"
                                                                value="（複数選択可）"></h:outputText>
                                                            <BR>
                                                            <hx:commandExButton type="submit" value="全て除外" onclick="return delConfirm('#{msg.SY_MSG_0006W}');"
                                                                styleClass="commandExButton" id="allRemove" action="#{pc_Kmc01902T02.doAllRemoveAction}" style="width:60px"></hx:commandExButton></TD>
                                                        </TR>
                                                        <TR>
                                                            <TD align="right"><h:outputFormat styleClass="outputFormat"
                                                                id="format3" value="合計件数：　{0}件">
                                                                <f:param name="normalCount"
                                                                    value="#{pc_Kmc01902T02.propTargetKyoin.listCount}"></f:param>
                                                            </h:outputFormat><h:outputFormat styleClass="outputFormat"
                                                                id="format2" value="正常件数：　{0}件">
                                                                <f:param name="normalCount"
                                                                    value="#{pc_Kmc01902T02.propTargetKyoin.listCount - pc_Kmc01902T02.propErrorCount.integerValue}"></f:param>
                                                            </h:outputFormat><h:outputFormat styleClass="outputFormat"
                                                                id="htmlErrorCount" value="エラー件数：　{0}件">
                                                                <f:param name="errorCount"
                                                                    value="#{pc_Kmc01902T02.propErrorCount.integerValue}"></f:param>
                                                            </h:outputFormat></TD>
                                                            <TD valign="top" align="center"></TD>
                                                        </TR>
                                                    </TBODY>
                                                </TABLE>
                                                <BR>
                                                <TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
                                                    class="button_bar">
                                                    <TBODY>
                                                        <TR id="">
                                                            <TD width="100%"><hx:commandExButton type="submit"
                                                                value="PDF作成" styleClass="commandExButton_out" id="pdfout"
                                                                action="#{pc_Kmc01902T02.doPdfoutAction}" confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
                                                            <hx:commandExButton type="submit" value="EXCEL作成"
                                                                styleClass="commandExButton_out" id="excelout"
                                                                action="#{pc_Kmc01902T02.doExceloutAction}" confirm="#{msg.SY_MSG_0027W}"></hx:commandExButton>
                                                            <hx:commandExButton type="submit" value="CSV作成"
                                                                styleClass="commandExButton_out" id="csvout"
                                                                action="#{pc_Kmc01902T02.doCsvoutAction}" confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
                                                            <hx:commandExButton type="submit" value="出力項目指定"
                                                                styleClass="commandExButton_out" id="setoutput"
                                                                action="#{pc_Kmc01902T02.doSetoutputAction}" onclick="return func_1(this, event);"></hx:commandExButton></TD>
                                                        </TR>
                                                    </TBODY>
                                                </TABLE>
                                                </TD>
                                            </TR>
                                        </TBODY>
                                    </TABLE>
                                    </TD>
                                </TR>
                            </TBODY>
                        </TABLE>
                        </TD>
                    </TR>
                </TBODY>
            </TABLE><!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--> 
</h:form>
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />

</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>
