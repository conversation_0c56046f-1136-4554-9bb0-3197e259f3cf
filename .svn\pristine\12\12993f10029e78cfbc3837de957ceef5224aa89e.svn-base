<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc00301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc00301.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc00301.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトの問題の為に、全角スペースを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style=""
							class="table" width="80%">
							<TBODY>
								<TR>
									<TH class="v_a" colspan="" width="25%">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFile"
													value="#{pc_Nsc00301.propInputFile.labelName}"
													style="#{pc_Nsc00301.propInputFile.labelStyle}"></h:outputText></TH>
											</TR>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFilePre"
													value="(前回ファイル)"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									</TH>
									<TD class="" width="75%">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border"><hx:fileupload
													styleClass="fileupload" id="htmlInputFile" size="50" value="#{pc_Nsc00301.propInputFile.value}" style="width:500px" tabindex="1">
													<hx:fileProp name="fileName" value="#{pc_Nsc00301.propInputFile.fileName}"/>
													<hx:fileProp name="contentType" value="#{pc_Nsc00301.propInputFile.contentType}"/>
												</hx:fileupload></TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:outputText
													styleClass="outputText" id="htmlInputFilePre" value="#{pc_Nsc00301.propInputFilePre.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblDataKubun"
										value="#{pc_Nsc00301.propDataKubun.labelName}"></h:outputText></TH>
									<TD class=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlDataKubun"
										layout="pageDirection" value="#{pc_Nsc00301.propDataKubun.value}" tabindex="2">
										<f:selectItem itemValue="1"
											itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
										<f:selectItem itemValue="2"
											itemLabel="データを登録または更新します。同一データが存在すれば上書き更新します。" />
										<f:selectItem itemValue="4" itemLabel="入力ファイルに指定されたデータを削除します。" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH colspan="" class="v_c"><h:outputText
										styleClass="outputText" id="lblSyoriKubunL"
										value="#{pc_Nsc00301.propSyoriKubun.labelName}"></h:outputText></TH>
									<TD class="v_e"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSyoriKubun" value="#{pc_Nsc00301.propSyoriKubun.checked}" tabindex="3"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblSyoriKubun"
										value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblCheckList" value="チェックリスト出力指定"></h:outputText></TH>
									<TD class="v_e"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlCheckList"
										layout="pageDirection" value="#{pc_Nsc00301.propCheckList.value}" tabindex="4">
										<f:selectItem itemValue="0" itemLabel="正常データ" />
										<f:selectItem itemValue="1" itemLabel="エラーデータ" />
										<f:selectItem itemValue="2" itemLabel="ワーニングデータ" />
									</h:selectManyCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar" cellpadding=""
							cellspacing="">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="入力項目指定"
										styleClass="commandExButton_etc" id="setinput"
										action="#{pc_Nsc00301.doSetinputAction}" tabindex="5">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="実行" styleClass="commandExButton_dat" id="exec"
										action="#{pc_Nsc00301.doExecAction}" tabindex="6"
										confirm="#{msg.SY_MSG_0001W}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

