#Tue Jun 20 11:35:35 JST 2023
override=true
USER_MANUAL_PREFERENCE=enabledManualValidatorListorg.eclipse.jst.j2ee.internal.classpathdep.ClasspathDependencyValidator;org.eclipse.wst.wsi.ui.internal.WSIMessageValidator;com.ibm.jee.sdo.jdbc.ui.validators.JDBCMediatorConnectionFileValidator;com.ibm.etools.j2ee.migration.classpath.J2EEClasspathValidator;com.ibm.ast.ws.ext.validator.WsExtValidator;com.ibm.etools.validation.ejb.ext.EJBValidatorExt;
vals/org.eclipse.jst.jsp.core.JSPBatchValidator/global=FF01
suspend=false
vals/org.eclipse.jst.jsp.core.JSPContentValidator/global=FF01
eclipse.preferences.version=1
vf.version=3
DELEGATES_PREFERENCE=delegateValidatorList
USER_PREFERENCE=overrideGlobalPreferencestruedisableAllValidationfalseversion1.2.251.v201208301900
USER_BUILD_PREFERENCE=enabledBuildValidatorListorg.eclipse.jst.j2ee.internal.classpathdep.ClasspathDependencyValidator;org.eclipse.wst.wsi.ui.internal.WSIMessageValidator;com.ibm.jee.sdo.jdbc.ui.validators.JDBCMediatorConnectionFileValidator;com.ibm.etools.j2ee.migration.classpath.J2EEClasspathValidator;com.ibm.ast.ws.ext.validator.WsExtValidator;com.ibm.etools.validation.ejb.ext.EJBValidatorExt;
