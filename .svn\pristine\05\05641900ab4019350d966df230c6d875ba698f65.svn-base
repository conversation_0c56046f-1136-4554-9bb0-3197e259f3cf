<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab01602.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>備品分割一覧</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_2(thisObj, thisEvent) {
	// 備品照会画面を表示する。　　途中・・・

}
function confirmOk() {
	var status = document.getElementById('form1:htmlExecutableBunkatsu').value;
	if (status == "1") {
		document.getElementById('form1:htmlExecutableBunkatsu').value = "2";
		indirectClick('bunkatsu');	
	}
	
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableBunkatsu').value = "0";
}

function func_detail(thisObj, thisEvent) {
	setPage('htmlPage', 'htmlBihnBunKatuList');
}

function func_bunkatsu(thisObj, thisEvent) {
	setPage('htmlPage', 'htmlBihnBunKatuList');
}

function onLoadFunc() {
	attachFormatNumber();
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="onLoadFunc();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kab01602.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<DIV style="display:none;">
	<hx:commandExButton
		type="submit"
		value="閉じる"
		styleClass="commandExButton"
		id="closeDisp"
		action="#{pc_Kab01602.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText
		styleClass="outputText" id="htmlFuncId" value="#{pc_Kab01602.funcId}">
	</h:outputText>
	<h:outputText
		styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}">
	</h:outputText>
	<h:outputText
		styleClass="outputText" id="htmlScrnName" value="#{pc_Kab01602.screenName}">
	</h:outputText>
</DIV>
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err">
	<LEGEND>エラーメッセージ</LEGEND>
	<h:outputText
		id="message"
		value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText"
		escape="false">
	</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<!-- 戻るボタンを配置 -->
	<hx:commandExButton
		type="submit"
		value="戻る"
		styleClass="commandExButton"
		id="returnDisp"
		action="#{pc_Kab01602.doReturnDispAction}"
		disabled="#{pc_Kab01602.propReturnDisp.disabled}"
		rendered="#{pc_Kab01602.propReturnDisp.rendered}"
		style="#{pc_Kab01602.propReturnDisp.style}">
	</hx:commandExButton>
<!-- 戻るボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" width="900"
		style="margin-top:10px">
		<TBODY>
			<TR>
				<TD align="right">
					<h:outputText styleClass="outputText"
						id="lblCodeListCnt"
						value="#{pc_Kab01602.propBihnBunkatuList.listCount}">
					</h:outputText> <h:outputText styleClass="outputText" id="lblKen"
						value="件">
					</h:outputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" height="440px">
		<TBODY>
			<TR>
				<TD valign="top">
				<h:dataTable columnClasses="columnClass1" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Kab01602.propBihnBunkatuList.rowClasses}"
							styleClass="meisai_scroll" id="htmlBihnBunKatuList"
							rows="#{pc_Kab01602.propBihnBunkatuList.rows}"
							value="#{pc_Kab01602.propBihnBunkatuList.list}" var="varlist"
							first="#{pc_Kab01602.propBihnBunkatuList.first}" width="900"
							border="0" cellpadding="2" cellspacing="0">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="備品番号"
										id="lblColumn1"></h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn1" styleClass="outputText"
									value="#{varlist.bhnNo}" style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="142" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="枝番"
										id="lblColumn2"></h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn2" styleClass="outputText"
									value="#{varlist.edaNo}" style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align:center" name="style" />
								<f:attribute value="100" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="資産名称"
										id="lblColumn3"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel1">
									<DIV style="width:155px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlColumn3" styleClass="outputText"
										value="#{varlist.ssnRyak.stringValue}"
										style="white-space:nowrap;"
										title="#{varlist.ssnRyak.stringValue}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="155" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="資産分類名称"
										id="lblColumn4"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel2">
									<DIV style="width:155px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlColumn4" styleClass="outputText"
										value="#{varlist.ssnBnrRyak.stringValue}"
										style="white-space:nowrap;"
										title="#{varlist.ssnBnrRyak.stringValue}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="155" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="設置場所名称"
										id="lblColumn5"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel3">
									<DIV style="width:155px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText styleClass="outputText" id="htmlColumn5"
										value="#{varlist.setchiBashoRyak.stringValue}"
										style="white-space:nowrap;"
										title="#{varlist.setchiBashoRyak.stringValue}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="155" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<f:facet name="footer">
							</f:facet>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="管理部門名称"
										id="lblColumn6"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel4">
									<DIV style="width:155px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText styleClass="outputText" id="htmlColumn6"
										value="#{varlist.kanriBmnRyak.stringValue}"
										style="white-space:nowrap;"
										title="#{varlist.kanriBmnRyak.stringValue}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="155" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Kab01602.doSelectAction}" style="width:38px"
									disabled="#{pc_Kab01602.propSelect.disabled}"
									rendered="#{pc_Kab01602.propSelect.rendered}">
								</hx:commandExButton>
								<f:attribute value="38px" name="width" />
							</h:column>
							<f:facet name="footer">
								<hx:panelBox styleClass="panelBox" id="box1">
									<hx:pagerDeluxe styleClass="pagerDeluxe" id="deluxe1" />
									<hx:pagerGoto styleClass="pagerGoto" id="goto1" />
								</hx:panelBox>
							</f:facet>
						</h:dataTable>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" cellpadding="0" cellspacing="0" style="margin-top: 10px;" class="table" width="900">
		<TBODY>
			<TR>
				<TH style="" class="v_a" width="150">
					<h:outputText
						styleClass="outputText" id="lblBhnNo"
						value="#{pc_Kab01602.propBhnNo.labelName}"
						style="#{pc_Kab01602.propBhnNo.labelStyle}">
					</h:outputText>
				</TH>
				<TD width="150">
					<h:outputText styleClass="outputText"
						id="htmlBhnNo" value="#{pc_Kab01602.propBhnNo.stringValue}">
					</h:outputText>
				</TD>
				<TH width="150" class="v_b">
					<h:outputText styleClass="outputText"
						id="lblEdaNo" value="#{pc_Kab01602.propEdaNo.labelName}"
						style="#{pc_Kab01602.propEdaNo.labelStyle}">
					</h:outputText>
				</TH>
				<TD width="200">
					<h:outputText styleClass="outputText"
						id="htmlEdaNo" value="#{pc_Kab01602.propEdaNo.stringValue}">
					</h:outputText>
				</TD>
				<TH width="110" class="v_c">
					<h:outputText styleClass="outputText"
						id="lblBunkatsuSu"
						value="#{pc_Kab01602.propBunkatsuSu.labelName}"
						style="#{pc_Kab01602.propBunkatsuSu.labelStyle}">
					</h:outputText>
				</TH>
				<TD width="140">
					<h:inputText rendered="#{pc_Kab01602.propBunkatsuSu.rendered}"
							id="htmlBunkatsuSu" styleClass="inputText"
							readonly="#{pc_Kab01602.propBunkatsuSu.readonly}"
							style="padding-right: 3px; text-align: right;#{pc_Kab01602.propBunkatsuSu.style}"
							size="2" value="#{pc_Kab01602.propBunkatsuSu.stringValue}"
							maxlength="#{pc_Kab01602.propBunkatsuSu.maxLength}"
							disabled="#{pc_Kab01602.propBunkatsuSu.disabled}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_d">
					<h:outputText
						styleClass="outputText" id="lblEdanoFlg"
						value="#{pc_Kab01602.propEdanoFlg.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:selectOneRadio
						rendered="#{pc_Kab01602.propEdanoFlg.rendered}"
						id="htmlEdanoFlg"
						readonly="#{pc_Kab01602.propEdanoFlg.readonly}"
						styleClass="selectOneRadio" style="#{pc_Kab01602.propEdanoFlg.style}"
						disabledClass="selectOneRadio_Disabled"
						value="#{pc_Kab01602.propEdanoFlg.value}"
						disabled="#{pc_Kab01602.propEdanoFlg.disabled}">
							<f:selectItems value="#{pc_Kab01602.propEdanoFlg.list}" />
						</h:selectOneRadio>
				</TD>
				<TH class="v_e">
					<h:outputText styleClass="outputText"
						id="lblJyoiBhnNo" value="#{pc_Kab01602.propJyoiBhnNo.labelName}"
						style="#{pc_Kab01602.propJyoiBhnNo.labelStyle}">
					</h:outputText>
				</TH>
				<TD>
					<h:inputText
						rendered="#{pc_Kab01602.propJyoiBhnNo.rendered}"
						id="htmlJyoiBhnNo" styleClass="inputText"
						readonly="#{pc_Kab01602.propJyoiBhnNo.readonly}"
						style="#{pc_Kab01602.propJyoiBhnNo.style}"
						size="20"
						value="#{pc_Kab01602.propJyoiBhnNo.stringValue}"
						maxlength="#{pc_Kab01602.propJyoiBhnNo.maxLength}"
						disabled="#{pc_Kab01602.propJyoiBhnNo.disabled}">
						<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
					</h:inputText>
					<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="searchJyoiBhnNo"
							disabled="#{pc_Kab01602.propSearchJyoiBhnNo.disabled}"
							rendered="#{pc_Kab01602.propSearchJyoiBhnNo.rendered}"
							action="#{pc_Kab01602.doSearchJoyiBihinNoAction}">
						</hx:commandExButton>
				</TD>
				<TH>
					<h:outputText styleClass="outputText" id="lblKintouBunkatsu"
							value="#{pc_Kab01602.propKintouBunkatsu.labelName}"
							style="#{pc_Kab01602.propKintouBunkatsu.labelStyle}">
						</h:outputText>
				</TH>
				<TD>
					<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlKintouBunkatsu"
							value="#{pc_Kab01602.propKintouBunkatsu.stringValue}"
							readonly="#{pc_Kab01602.propKintouBunkatsu.readonly}"
							disabled="#{pc_Kab01602.propKintouBunkatsu.disabled}">
							<f:selectItems value="#{pc_Kab01602.propKintouBunkatsu.list}" />
						</h:selectOneRadio>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
		<TABLE border="0" cellpadding="0" cellspacing="0" width="750"
		style="" class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton
						type="submit" value="分割"
						styleClass="commandExButton_etc" id="bunkatsu"
						action="#{pc_Kab01602.doBunkatsuAction}"
						disabled="#{pc_Kab01602.propBunkatsu.disabled}"
						rendered="#{pc_Kab01602.propBunkatsu.rendered}"
						style="#{pc_Kab01602.propBunkatsu.style}"
						onclick="return func_bunkatsu(this, event);">
					</hx:commandExButton>
					<hx:commandExButton
						type="submit" value="クリア"
						styleClass="commandExButton_etc" id="clear"
						action="#{pc_Kab01602.doClearAction}"
						disabled="#{pc_Kab01602.propClear.disabled}"
						rendered="#{pc_Kab01602.propClear.rendered}"
						style="#{pc_Kab01602.propClear.style}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/footer.jsp" />
	<h:inputHidden
		id="htmlExecutableBunkatsu" 
		value="#{pc_Kab01602.propExecutableBunkatsu.integerValue}">
		<f:convertNumber />
	</h:inputHidden>
	<h:inputHidden
		value="#{pc_Kab01602.propBihnBunkatuList.currentPage}"
		id="htmlPage">
	</h:inputHidden>
	<h:inputHidden value="htmlBunkatsuSu=#0;#0"
		id="htmlFormatNumberOption">
	</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
