<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/PCog1201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.system.co.util.UtilCogFormatObject" %>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pCog1201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function func_Select_01(thisObj, thisEvent) {
	try {
		var SEP = "<%= UtilCogFormatObject.JOIN_STR %>";
		var noSelectMsg = document.getElementById("form1:htmlNoSelectMsgHidden").value;
		var chushutsuKbnId = document.getElementById("form1:prmChushutsuKbnId").value;
		var chushutsuNameId = document.getElementById("form1:prmChushutsuNameId").value;
		var focusId = document.getElementById("form1:prmFocusId").value;
		var buttonId = document.getElementById("form1:prmButtonId").value;
		var selTypeFlg = document.getElementById("form1:prmMultiple").value;
		var listBox = (selTypeFlg == 0)?
						document.getElementById("form1:htmlChushutsuOneList"):
							(selTypeFlg == 1)?
								document.getElementById("form1:htmlChushutsuManyList"):
									null;
		if (window.opener && listBox != null) {
			var select = 0;
			var targetCd = getElementByIdEx(window.opener.document, chushutsuKbnId);
			var targetNm = getElementByIdEx(window.opener.document, chushutsuNameId);
			var focusEl = getElementByIdEx(window.opener.document, focusId);
			var button = getElementByIdEx(window.opener.document, buttonId);
			
			if (listBox.selectedIndex > 0) {
				flapWindow(window);
				if (selTypeFlg == 0) {
					// 単一
					select = setOneValue(targetCd, targetNm, 0, 1, listBox, SEP);
					// イベント起動
					if (select > 0) {
						riseEvent(targetCd);
					}
				} else {
					// 複数
					select = setManyValue(targetCd, 0, 1, listBox, SEP);
				}
			}

			if (select > 0) {
				// 指定ボタンクリック
				riseEvent(button);

				var targets = new Array();
				targets.push(focusEl);
				targets.push(targetCd);
				targets.push(targetNm);

				windowClose(targets);
			} else {
				reflapWindow(window);
				setErrMsg(noSelectMsg); 
			}
		} else {
			throw "";
		}
	} catch(e) {
		window.moveTo(0, 0);
		setErrMsg("呼び出し元画面に値を返せません。");
	}
	return false;
}
function func_Cancel_01(thisObj, thisEvent) {
	var chushutsuKbnId = document.getElementById("form1:prmChushutsuKbnId").value;
	var chushutsuNameId = document.getElementById("form1:prmChushutsuNameId").value;
	var focusId = document.getElementById("form1:prmFocusId").value;

	var targetCd = getElementByIdEx(window.opener.document, chushutsuKbnId);
	var targetNm = getElementByIdEx(window.opener.document, chushutsuNameId);
	var focusEl = getElementByIdEx(window.opener.document, focusId);

	var targets = new Array();
	targets.push(focusEl);
	targets.push(targetCd);
	targets.push(targetNm);
	
	windowClose(targets);
	return false;

}
//----------↓経理検索部品共通js↓----------
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}
//----------↑経理検索部品共通js↑----------
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PCog1201.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_PCog1201.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PCog1201.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PCog1201.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600">
							<TBODY>
								<TR>
									<TD>
									<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="table">
										<TBODY>
											<TR>
												<TH width="145" class="v_a"><h:outputText
													styleClass="outputText" id="lblKaikeiNendo"
													value="#{pc_PCog1201.propKaikeiNendo.name}"
													style="#{pc_PCog1201.propKaikeiNendo.labelStyle}"></h:outputText></TH>
												<TD><h:outputText styleClass="outputText" id="htmlKaikeiNendo" 
													value="#{pc_PCog1201.propKaikeiNendo.integerValue}" 
													style="#{pc_PCog1201.propKaikeiNendo.style}">
													<f:convertNumber pattern="####" />
												</h:outputText></TD></TR>
											<TR>
												<TH width="145" class="v_b"><h:outputText
													styleClass="outputText" id="lblChushutsuKbn"
													value="#{pc_PCog1201.propChushutsuKbn.labelName}"
													style="#{pc_PCog1201.propChushutsuKbn.labelStyle}"></h:outputText></TH>
												<TD>
												<TABLE width="450" border="0" cellpadding="0" cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="100"><h:inputText styleClass="inputText"
																id="htmlChushutsuKbn" size="6"
																value="#{pc_PCog1201.propChushutsuKbn.stringValue}"
																disabled="#{pc_PCog1201.propChushutsuKbn.disabled}"
																readonly="#{pc_PCog1201.propChushutsuKbn.readonly}"
																style="#{pc_PCog1201.propChushutsuKbn.style}"
																maxlength="#{pc_PCog1201.propChushutsuKbn.maxLength}" tabindex="1"></h:inputText></TD>
															<TD><h:outputText styleClass="outputText"
																id="lblChushutsuKbnFindType" value="（前方一致）"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_c"><h:outputText styleClass="outputText"
													id="lblChushutsuName"
													value="#{pc_PCog1201.propChushutsuName.labelName}"
													style="#{pc_PCog1201.propChushutsuName.labelStyle}"></h:outputText></TH>
												<TD>
												<TABLE width="450" border="0" cellpadding="0" cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="200"><h:inputText styleClass="inputText"
																id="htmlChushutsuName" size="28"
																disabled="#{pc_PCog1201.propChushutsuName.disabled}"
																readonly="#{pc_PCog1201.propChushutsuName.readonly}"
																style="#{pc_PCog1201.propChushutsuName.style}"
																value="#{pc_PCog1201.propChushutsuName.stringValue}"
																maxlength="#{pc_PCog1201.propChushutsuName.maxLength}" tabindex="2"></h:inputText></TD>
															<TD width="250"><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlChushutsuNameFindType"
																value="#{pc_PCog1201.propChushutsuNameFindType.stringValue}"
																disabled="#{pc_PCog1201.propChushutsuNameFindType.disabled}"
																readonly="#{pc_PCog1201.propChushutsuNameFindType.readonly}" tabindex="3">
																<f:selectItems
																	value="#{pc_PCog1201.propChushutsuNameFindType.list}" />
															</h:selectOneRadio></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD></TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
						</TBODY>
					</TABLE>
					</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600"
							class="button_bar">
							<TBODY>
								<TR>
									<TD>
									<hx:commandExButton type="submit" value="検索"
										styleClass="commandExButton_dat" id="search"
										action="#{pc_PCog1201.doSearchAction}" tabindex="4">
									</hx:commandExButton><hx:commandExButton type="submit"
										value="クリア" styleClass="commandExButton_etc" 
										id="clear" action="#{pc_PCog1201.doClearAction}" tabindex="5">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<HR noshade>
						</TD>
					</TR>
					<TR>
						<TD>
							<hx:jspPanel id="jspPanel1">
								<TABLE border="0" cellpadding="0" cellspacing="0">
									<TBODY>
										<TR>
											<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD width="500" style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none"></TD>
													<TD style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none">
														<h:outputText styleClass="outputText" 
														id="htmlListCount" value="#{pc_PCog1201.propListCount.integerValue}" 
														style="#{pc_PCog1201.propListCount.style}">
														<f:convertNumber />
													</h:outputText><h:outputText styleClass="outputText" id="lblKensu" 
														value="件" style="text-align: right"></h:outputText></TD>
												</TR>
												<TR>
													<TH width="600" colspan="2">
													<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
														<TBODY>
															<TR>
																<TD style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify" 
																	width="60"><h:outputText styleClass="outputText"
																	id="lblListChushutsuKbn" value="抽出区分"></h:outputText></TD>
																<TD style="border-bottom-style:none;border-left-style:none;border-top-style:none;border-right-style:none;text-align:justify">
																	<h:outputText styleClass="outputText" id="lblListChushutsuName" value="名称"></h:outputText></TD>
															</TR>
														</TBODY>
													</TABLE>
													</TH>
												</TR>
												<TR>
													<TD width="600" colspan="2">
													<h:selectOneListbox styleClass="selectOneListbox"
														id="htmlChushutsuOneList" size="18"
														style='font-family: "ＭＳ ゴシック";width:100%'
														disabled="#{pc_PCog1201.propChushutsuOneList.disabled}"
														readonly="#{pc_PCog1201.propChushutsuOneList.readonly}"
														value="#{pc_PCog1201.propChushutsuOneList.value}"
														rendered="#{pc_PCog1201.propChushutsuOneList.rendered}" tabindex="6"
														ondblclick="return func_Select_01(this, event);">
														<f:selectItems
															value="#{pc_PCog1201.propChushutsuOneList.list}" />
													</h:selectOneListbox>
													<h:selectManyListbox styleClass="selectManyListbox"
														id="htmlChushutsuManyList" size="18"
														style='font-family: "ＭＳ ゴシック";width:100%'
														disabled="#{pc_PCog1201.propChushutsuManyList.disabled}"
														readonly="#{pc_PCog1201.propChushutsuManyList.readonly}"
														value="#{pc_PCog1201.propChushutsuManyList.value}"
														rendered="#{pc_PCog1201.propChushutsuManyList.rendered}">
														<f:selectItems
															value="#{pc_PCog1201.propChushutsuManyList.list}" />
													</h:selectManyListbox>
													</TD></TR>
											</TBODY>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="600" class="button_bar">
											<TBODY>
												<TR>
													<TD><hx:commandExButton type="submit"
														styleClass="commandExButton_etc" id="select" value="選択"
														onclick="return func_Select_01(this, event);" tabindex="7">
													</hx:commandExButton><hx:commandExButton type="submit"
														value="キャンセル" styleClass="commandExButton_etc" id="cancel"
														onclick="return func_Cancel_01(this, event);" tabindex="8">
													</hx:commandExButton></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</hx:jspPanel></TD>
					</TR>
					<TR>
						<TD>
							<h:inputHidden id="htmlNoSelectMsgHidden" value="#{pc_PCog1201.propNoSelectMsgHidden.stringValue}"></h:inputHidden>
							<h:inputHidden id="htmlExecutableSearchHidden" value="#{pc_PCog1201.propExecutableSearchHidden.integerValue}"><f:convertNumber /></h:inputHidden>
							<input id="form1:prmChushutsuKbnId" type="hidden" name="prmChushutsuKbnId" value="<c:out value="${pc_PCog1201.prmChushutsuKbnId}"/>">
							<input id="form1:prmChushutsuNameId" type="hidden" name="prmChushutsuNameId" value="<c:out value="${pc_PCog1201.prmChushutsuNameId}"/>">
							<input id="form1:prmMultiple" type="hidden" name="prmMultiple" value="<c:out value="${pc_PCog1201.prmMultiple ? 1 : 0}"/>">
							<input id="form1:prmButtonId" type="hidden" name="prmButtonId" value="<c:out value="${pc_PCog1201.prmButtonId}"/>">
							<input id="form1:prmFocusId" type="hidden" name="prmFocusId" value="<c:out value="${pc_PCog1201.prmFocusId}"/>">
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/childFooter.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

