<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00901T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01001T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">	
function fncInit() {
    	//ＰＤＦ作成ボタンの制御
	if(document.getElementById('form1:htmlHidErrorKbn').value == '1'){
		document.getElementById('form1:PdfOut').disabled = true;
		document.getElementById('form1:htmlAllSitei').disabled = true;
		document.getElementById('form1:link1').disabled = true;
	}
}

function confirmOk() {	
	document.getElementById('form1:clearCount').value = "1";
	indirectClick('search');
}
function confirmCancel() {
		// alert('実行を中断しました。');	
}

// 確認ダイアログで「ＯＫ」の場合
function confirmOk() {
	addListWarnOK("htmlExecutableBtnAdd", "btnAdd1");
}
// 確認ダイアログで「キャンセル」の場合
function confirmCancel() {	
	addListWarnCancel("htmlExecutableBtnAdd");
}

</SCRIPT></HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="fncInit();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrm00901T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrm00901T01.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrm00901T01.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrm00901T01.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

<TABLE border="0" cellpadding="3" cellspacing="0" width="100%" align="center">
	<TBODY>
		<TR>
			<TD>

			<TABLE border="0" cellpadding="0" cellspacing="0" align="center">
				<TBODY>
					<TR>
						<TD width="637">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="637">
								<TBODY>
									<TR height="50">
										<TD valign="top"  align="left">
										<br>
										<TABLE class="table" width="600">
											<TBODY>
												<TR>
													<TH nowrap class="v_a" width="146"><h:outputText styleClass="outputText"
													 id="lblNendo"
													 value="#{pc_Xrm00901T01.propNendo.labelName}"
													 style="#{pc_Xrm00901T01.propNendo.labelStyle}"></h:outputText></TH>
													<TD width="454"><h:inputText styleClass="inputText"
													 id="htmlNendo" style="#{pc_Xrm00901T01.propNendo.style}"
													 disabled="#{pc_Xrm00901T01.propNendo.disabled}"
													 value="#{pc_Xrm00901T01.propNendo.dateValue}"
													 size="5">
													<hx:inputHelperAssist errorClass="inputText_Error"
							    					imeMode="inactive" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
													</h:inputText></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR><TD><BR></TD></TR>
					<TR>
						<TD width="637" align="left">
						<hx:commandExButton type="submit" value="#{pc_Xrm00901T01.proplblAllSitei.name}" styleClass="tab_head_on" id="htmlAllSitei"></hx:commandExButton><hx:commandExButton
						 type="submit" value="#{pc_Xrm00901T01.proplblSelectSitei.name}" styleClass="tab_head_off" id="link1" action="#{pc_Xrm00901T01.doLink1Action}"></hx:commandExButton></TD>
					</TR>
					<TR>
						<TD width="637">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="637"
							class="tab_body">
								<TBODY>
									<TR height="300">
										<TD valign="top"  align="center">
										<br>
										<TABLE class="table" width="600">
											<TBODY>
												<TR>
													<TH nowrap class="v_a" width="146"><h:outputText styleClass="outputText"
													 id="lblGakuhiNendo"
													 value="#{pc_Xrm00901T01.propNyugakNendo.labelName}"
													 style="#{pc_Xrm00901T01.propNyugakNendo.labelStyle}"></h:outputText></TH>
													<TD width="454"><h:inputText styleClass="inputText"
													 id="htmlGakuhiNendo" style="#{pc_Xrm00901T01.propNyugakNendo.style}"
													 disabled="#{pc_Xrm00901T01.propNyugakNendo.disabled}"
													 value="#{pc_Xrm00901T01.propNyugakNendo.dateValue}"
													 size="5">
													<hx:inputHelperAssist errorClass="inputText_Error"
							    					imeMode="inactive" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
													</h:inputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_b" width="146"><h:outputText styleClass="outputText"
													 id="lblGakkiNo" value="#{pc_Xrm00901T01.propGakkiNo.labelName}"
													 style="#{pc_Xrm00901T01.propGakkiNo.labelStyle}"></h:outputText></TH>
													<TD width="454"><h:inputText styleClass="inputText"
													 id="htmlGakkiNo" style="#{pc_Xrm00901T01.propGakkiNo.style}"
													 disabled="#{pc_Xrm00901T01.propGakkiNo.disabled}"
													 value="#{pc_Xrm00901T01.propGakkiNo.integerValue}"
													 size="5">
													<f:convertNumber type="number" pattern="#0"/>
													<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
													</h:inputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_d" width="146"><h:outputText styleClass="outputText"
													 id="lblSgks" value="#{pc_Xrm00901T01.propSgks.labelName}"></h:outputText></TH>
													<TD width="454"><h:selectOneMenu styleClass="selectOneMenu"
																id="htmlSgks"
																disabled="#{pc_Xrm00901T01.propSgks.disabled}"
																style="width:445px"
																value="#{pc_Xrm00901T01.propSgks.value}"
																style="width:435px;">
																<f:selectItems value="#{pc_Xrm00901T01.propSgks.list}" />
															</h:selectOneMenu></TD>
												</TR>
												<TR>
													<TH nowrap class="v_e" width="146"><h:outputText styleClass="outputText"
													 id="lblCurGakka" value="#{pc_Xrm00901T01.propCurGakka.labelName}"></h:outputText></TH>
													<TD width="454"><h:selectOneMenu styleClass="selectOneMenu"
																id="htmlCurGakka"
																disabled="#{pc_Xrm00901T01.propCurGakka.disabled}"
																style="width:445px"
																value="#{pc_Xrm00901T01.propCurGakka.value}"
																style="width:435px;">
																<f:selectItems
																	value="#{pc_Xrm00901T01.propCurGakka.list}" />
															</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD></TR>
								   <TR height="50">
								 		<TD  valign="top">
											<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="600">
												<TBODY>
													<TR align="right">
														<TD align="center">
															<hx:commandExButton type="submit" value="PDF作成"
															styleClass="commandExButton_out" id="PdfOut" action="#{pc_Xrm00901T01.doBtnPdfOutAction}"
															confirm="#{msg.SY_MSG_0019W}">
															</hx:commandExButton>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
								     </TD>
								   </TR>
								</TBODY>
							</TABLE>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrm00901T01.propClearButton.integerValue}"
				id="clearCount">
				<f:convertNumber type="number" />
			</h:inputHidden>

			<h:inputHidden value="#{pc_Xrm00901T01.propErrorKbn.stringValue}"
				id="htmlHidErrorKbn"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
