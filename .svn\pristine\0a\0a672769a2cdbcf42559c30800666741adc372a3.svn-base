<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/kk/Kka00201T03.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kka00201T03.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT type="text/javascript">

// 画面ロード時の名称再取得
function loadAction(event){
  doKaiinAjax(document.getElementById('form1:htmlKaiinNo'), event, 'form1:htmlKaiinNm');
  doJinjiAJAX(document.getElementById('form1:htmlSemiKyoinCd'), event, 'form1:htmlAjaxSemiKyoinName');
  doJinjiAJAX(document.getElementById('form1:htmlSotKyoinCd'), event, 'form1:htmlAjaxSotKyoinName');
}

// 会員氏名を取得する
function doKaiinAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/kk/VKkaKaiDaiAJAX";
  var args = new Array();
  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

function doJinjiAJAX(thisObj, thisEvent, thisTarget) {
  // 人事氏名を取得する
  var servlet = "rev/co/CobJinjAJAX";
  var code = new Array();
  code['code'] = thisObj.value;
  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, thisTarget, code);
}

function openSubWindow(field) {
  var kensakutaishoKbn = "";
  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=" + field + "&kensakutaishoKbn="+kensakutaishoKbn;
  openModalWindow(url, "", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
  setTarget("");
  return false;
}

function doJinjiPluralAjax1(thisObj, thisEvent) {

  // ゼミ担当教員取得用
  // 氏名、氏名_カナ、氏名_英語、氏名_WEBを取得する
  var servlet = "rev/co/CoiJinjPluralAJAX";
  var args = new Array();
  args['code'] = document.getElementById('form1:htmlSemiKyoinCd').value;
  args['flg'] = "1";
  args['rtnField'] = "1";
  var target = "";

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getPluralValue(servlet, target, args);
}

function doJinjiPluralAjax2(thisObj, thisEvent) {

  // 卒論担当教員取得用
  // 氏名、氏名_カナ、氏名_英語、氏名_WEBを取得する
  var servlet = "rev/co/CoiJinjPluralAJAX";
  var args = new Array();
  args['code'] = document.getElementById('form1:htmlSotKyoinCd').value;
  args['flg'] = "1";
  args['rtnField'] = "2";
  var target = "";

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getPluralValue(servlet, target, args);
}

function setAjaxKbn(kbn) {
	document.getElementById('form1:htmlHidAjaxKbn').value = kbn;
}

function callBackMethod(value){

  if(value['rtnField'] == '1'){
    document.getElementById('form1:htmlSemiKyoinName').value = value['key2'];
    document.getElementById('form1:htmlSemiKyoinNameKana').value = value['key3'];
    document.getElementById('form1:htmlSemiKyoinNameEng').value = value['key4'];
    document.getElementById('form1:htmlSemiKyoinNameWeb').value = value['key5'];
  }
  if(value['rtnField'] == '2'){
    document.getElementById('form1:htmlSotKyoinName').value = value['key2'];
    document.getElementById('form1:htmlSotKyoinNameKana').value = value['key3'];
    document.getElementById('form1:htmlSotKyoinNameEng').value = value['key4'];
    document.getElementById('form1:htmlSotKyoinNameWeb').value = value['key5'];
  }
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}
// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById("form1:htmlHidButtonKbn").value = "0";
}

</SCRIPT></HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kka00201T03.onPageLoadBegin}">
<!-- <gakuen:itemStateCtrl managedbean="pc_Kka00201T03"> -->
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Kka00201T03.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kka00201T03.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kka00201T03.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="自由設定"
  styleClass="commandExButton" id="freeEdit" tabindex="1"
  disabled="#{pc_Kka00201T01.kka00201.propFreeBtn.disabled}"
  action="#{pc_Kka00201T03.doFreeAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

  <TABLE border="0" cellpadding="5">
    <TBODY>
      <TR>
        <TD width="870">
        <TABLE class="table" width="100%">
          <TBODY>
            <TR align="center" valign="middle">
        	 <TH class="v_a" nowrap  width="150">
             <!-- 登録対象 -->
               <h:outputText styleClass="outputText" id="lblTorokuTaisyo"
               value="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.labelName}"
               style="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.labelStyle}"></h:outputText></TH>
             <TD width="540"><h:selectOneRadio
               id="htmlTorokuTaisyo" disabledClass="selectOneRadio_Disabled"
               styleClass="selectOneRadio" tabindex="2"
               value="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.stringValue}"
               disabled="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.disabled}">
               <f:selectItems value="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.list}" /></h:selectOneRadio>
             </TD>

              <TD style="background-color: transparent; text-align: right"
                class="clear_border"><hx:commandExButton type="submit" tabindex="5"
                value="選　択" styleClass="commandExButton" id="select"
                disabled="#{pc_Kka00201T01.kka00201.propSelect.disabled}"
                action="#{pc_Kka00201T01.doSelectAction}"></hx:commandExButton>
              <hx:commandExButton type="submit" value="解　除" tabindex="6"
                styleClass="commandExButton" id="unselect"
                disabled="#{pc_Kka00201T01.kka00201.propUnSelect.disabled}"
                action="#{pc_Kka00201T01.doUnselectAction}"></hx:commandExButton>
              </TD>
            </TR>
            <TH nowrap class="v_a">
            <!--会員番号 -->
              <h:outputText styleClass="outputText" id="lblKaiinNo"
              value="#{pc_Kka00201T01.kka00201.propKaiinNo.labelName}"
              style="#{pc_Kka00201T01.kka00201.propKaiinNo.labelStyle}"></h:outputText></TH>
            <TD><h:inputText styleClass="inputText"
              id="htmlKaiinNo" size="18" tabindex="3"
              maxlength="#{pc_Kka00201T01.kka00201.propKaiinNo.maxLength}"
              disabled="#{pc_Kka00201T01.kka00201.propKaiinNo.disabled}"
              value="#{pc_Kka00201T01.kka00201.propKaiinNo.stringValue}"
              readonly="#{pc_Kka00201T01.kka00201.propKaiinNo.readonly}"
              style="#{pc_Kka00201T01.kka00201.propKaiinNo.style}"
              onblur="return doKaiinAjax(this, event, 'form1:htmlKaiinNm');"></h:inputText>
              <hx:commandExButton type="submit" value="検" tabindex="4"
              styleClass="commandExButton_search" id="btnKaiin"
              disabled="#{pc_Kka00201T01.kka00201.propKaiinNo.disabled}"
              action="#{pc_Kka00201T01.doKaiinSubAction}">
              </hx:commandExButton>
              <h:inputText styleClass="likeOutput"
                id="htmlKaiinNm" size="55"
                readonly="true" tabindex="-1"
                value="#{pc_Kka00201T01.kka00201.propName.stringValue}"></h:inputText>
            </TD>
            <TR>
              <TH nowrap class="v_b">
              <!-- 卒業校 -->
                <h:outputText styleClass="outputText" id="lblGakJokyo"
                value="#{pc_Kka00201T01.kka00201.propGakko.labelName}"
                style="#{pc_Kka00201T01.kka00201.propGakko.labelStyle}"></h:outputText></TH>
              <TD><h:outputText styleClass="outputText" id="htmlGakoOut"
                value="#{pc_Kka00201T01.kka00201.propGakko.stringValue}"></h:outputText></TD>
            </TR>
          </TBODY>
        </TABLE>
        <BR>
        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TBODY>
            <TR>
              <TD>
              <TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
                style="border-bottom-style: none; ">
                <TBODY>
                  <TR>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="27"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T01" style="width: 100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKihon.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T01Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="28"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T02" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameZaigakuji.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T02Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_on" width="8%"><hx:commandExButton tabindex="7"
                      type="button" styleClass="tab_head_on" id="tabKka00201T03" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameZemi.stringValue}"
                      ></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="29"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T04" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameSyussin.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T04Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="30"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T05" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameAddr.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T05Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="31"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T06" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKiseisaki.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T06Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="32"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T07" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameSinzoku.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T07Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="33"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T08" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKeireki.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T08Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="34"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T09" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameGroup.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T09Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="35"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T10" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKizou.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T10Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="36"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T11" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameSonota.stringValue}"
                      action="#{pc_Kka00201T03.doTabKka00201T11Action}"></hx:commandExButton></TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
            <TR>
              <TD>
              <TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0"
                width="100%" style="border-top-style: none; ">
                <TBODY>
                  <TR>
                    <TD width="100%">
                    <div style="height: 395px">
						<TABLE class="table" style="margin-top:15px" width="820">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="185"><h:outputText
										styleClass="outputText" id="lblSemiKyoinCd"
										value="#{pc_Kka00201T03.propSemiKyoinCd.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSemiKyoinCd" size="24" tabindex="8"
										value="#{pc_Kka00201T03.propSemiKyoinCd.stringValue}"
										style="#{pc_Kka00201T03.propSemiKyoinCd.style}"
										disabled="#{pc_Kka00201T03.propSemiKyoinCd.disabled}"
										maxlength="#{pc_Kka00201T03.propSemiKyoinCd.maxLength}"
										onblur="return doJinjiAJAX(this, event, 'form1:htmlAjaxSemiKyoinName');"></h:inputText>
									<hx:commandExButton type="button" value="検" tabindex="9"
										disabled="#{pc_Kka00201T03.propZemiSelectBtn.disabled}"
										styleClass="commandExButton_search" id="btnSearch1"
										onclick="return openSubWindow('form1:htmlSemiKyoinCd');"></hx:commandExButton>
									<hx:commandExButton type="submit" value="選択" tabindex="10"
										disabled="#{pc_Kka00201T03.propZemiSelectBtn.disabled}"
										styleClass="commandExButton" id="btnSelect1"
										onclick="return setAjaxKbn('1');"
										action="#{pc_Kka00201T03.doSelectKyoinAction}"></hx:commandExButton>
									<h:outputText styleClass="outputText" id="htmlAjaxSemiKyoinName"></h:outputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b" width="185"><h:outputText
										styleClass="outputText" id="lblSemiKyoinName"
										value="#{pc_Kka00201T03.propSemiKyoinName.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSemiKyoinName" size="98" tabindex="11"
										value="#{pc_Kka00201T03.propSemiKyoinName.stringValue}"
										style="#{pc_Kka00201T03.propSemiKyoinName.style}"
										disabled="#{pc_Kka00201T03.propSemiKyoinName.disabled}"
										maxlength="#{pc_Kka00201T03.propSemiKyoinName.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c" width="185"><h:outputText
										styleClass="outputText" id="lblSemiKyoinNameKana"
										value="#{pc_Kka00201T03.propSemiKyoinNameKana.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSemiKyoinNameKana" size="98" tabindex="12"
										value="#{pc_Kka00201T03.propSemiKyoinNameKana.stringValue}"
										style="#{pc_Kka00201T03.propSemiKyoinNameKana.style}"
										disabled="#{pc_Kka00201T03.propSemiKyoinNameKana.disabled}"
										maxlength="#{pc_Kka00201T03.propSemiKyoinNameKana.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_d" width="185"><h:outputText
										styleClass="outputText" id="lblSemiKyoinNameEng"
										value="#{pc_Kka00201T03.propSemiKyoinNameEng.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSemiKyoinNameEng" size="98" tabindex="13"
										value="#{pc_Kka00201T03.propSemiKyoinNameEng.stringValue}"
										style="#{pc_Kka00201T03.propSemiKyoinNameEng.style}"
										disabled="#{pc_Kka00201T03.propSemiKyoinNameEng.disabled}"
										maxlength="#{pc_Kka00201T03.propSemiKyoinNameEng.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_e" width="185"><h:outputText
										styleClass="outputText" id="lblSemiKyoinNameWeb"
										value="#{pc_Kka00201T03.propSemiKyoinNameWeb.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSemiKyoinNameWeb" size="98" tabindex="14"
										value="#{pc_Kka00201T03.propSemiKyoinNameWeb.stringValue}"
										style="#{pc_Kka00201T03.propSemiKyoinNameWeb.style}"
										disabled="#{pc_Kka00201T03.propSemiKyoinNameWeb.disabled}"
										maxlength="#{pc_Kka00201T03.propSemiKyoinNameWeb.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_f" width="185"><h:outputText
										styleClass="outputText" id="lblSemiTxtArea"
										value="#{pc_Kka00201T03.propSemiTxtArea.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputTextarea
										styleClass="inputTextarea" id="htmlSemiTxtArea" cols="75"
										rows="3" tabindex="15"
										value="#{pc_Kka00201T03.propSemiTxtArea.stringValue}"
										disabled="#{pc_Kka00201T03.propSemiTxtArea.disabled}"
										style="#{pc_Kka00201T03.propSemiTxtArea.style}"></h:inputTextarea></TD>
								</TR>
								<TR>
									<TH nowrap class="v_g" width="185"><h:outputText
										styleClass="outputText" id="lblSotKyoinCd"
										value="#{pc_Kka00201T03.propSotKyoinCd.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSotKyoinCd" size="24" tabindex="16"
										value="#{pc_Kka00201T03.propSotKyoinCd.stringValue}"
										disabled="#{pc_Kka00201T03.propSotKyoinCd.disabled}"
										style="#{pc_Kka00201T03.propSotKyoinCd.style}"
										maxlength="#{pc_Kka00201T03.propSotKyoinCd.maxLength}"
										onblur="return doJinjiAJAX(this, event, 'form1:htmlAjaxSotKyoinName');"></h:inputText>
									<hx:commandExButton type="button" value="検" tabindex="17"
										disabled="#{pc_Kka00201T03.propSotuSelectBtn.disabled}"
										styleClass="commandExButton_search" id="btnSearch2"
										onclick="return openSubWindow('form1:htmlSotKyoinCd');"></hx:commandExButton>
									<hx:commandExButton type="submit" value="選択" tabindex="18"
										disabled="#{pc_Kka00201T03.propSotuSelectBtn.disabled}"
										styleClass="commandExButton" id="btnSelect2"
										onclick="return setAjaxKbn('2');"
										action="#{pc_Kka00201T03.doSelectKyoinAction}"></hx:commandExButton>
									<h:outputText styleClass="outputText" id="htmlAjaxSotKyoinName"></h:outputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_a" width="185"><h:outputText
										styleClass="outputText" id="lblSotKyoinName"
										value="#{pc_Kka00201T03.propSotKyoinName.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSotKyoinName" size="98" tabindex="19"
										value="#{pc_Kka00201T03.propSotKyoinName.stringValue}"
										disabled="#{pc_Kka00201T03.propSotKyoinName.disabled}"
										style="#{pc_Kka00201T03.propSotKyoinName.style}"
										maxlength="#{pc_Kka00201T03.propSotKyoinName.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b" width="185"><h:outputText
										styleClass="outputText" id="lblSotKyoinNameKana"
										value="#{pc_Kka00201T03.propSotKyoinNameKana.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSotKyoinNameKana" size="98" tabindex="20"
										value="#{pc_Kka00201T03.propSotKyoinNameKana.stringValue}"
										disabled="#{pc_Kka00201T03.propSotKyoinNameKana.disabled}"
										style="#{pc_Kka00201T03.propSotKyoinNameKana.style}"
										maxlength="#{pc_Kka00201T03.propSotKyoinNameKana.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c" width="185"><h:outputText
										styleClass="outputText" id="lblSotKyoinNameEng"
										value="#{pc_Kka00201T03.propSotKyoinNameEng.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSotKyoinNameEng" size="98" tabindex="21"
										value="#{pc_Kka00201T03.propSotKyoinNameEng.stringValue}"
										style="#{pc_Kka00201T03.propSotKyoinNameEng.style}"
										disabled="#{pc_Kka00201T03.propSotKyoinNameEng.disabled}"
										maxlength="#{pc_Kka00201T03.propSotKyoinNameEng.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_d" width="185"><h:outputText
										styleClass="outputText" id="lblSotKyoinNameWeb"
										value="#{pc_Kka00201T03.propSotKyoinNameWeb.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputText styleClass="inputText"
										id="htmlSotKyoinNameWeb" size="98" tabindex="22"
										value="#{pc_Kka00201T03.propSotKyoinNameWeb.stringValue}"
										style="#{pc_Kka00201T03.propSotKyoinNameWeb.style}"
										disabled="#{pc_Kka00201T03.propSotKyoinNameWeb.disabled}"
										maxlength="#{pc_Kka00201T03.propSotKyoinNameWeb.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_e" width="185"><h:outputText
										styleClass="outputText" id="lblSotThemaTxtArea"
										value="#{pc_Kka00201T03.propSotThemaTxtArea.labelName}"></h:outputText></TH>
									<TD width="637"><h:inputTextarea
										styleClass="inputTextarea" id="htmlSotThemaTxtArea"
										cols="75" rows="3" tabindex="23"
										value="#{pc_Kka00201T03.propSotThemaTxtArea.stringValue}"
										disabled="#{pc_Kka00201T03.propSotThemaTxtArea.disabled}"
										style="#{pc_Kka00201T03.propSotThemaTxtArea.style}"></h:inputTextarea></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="820">
	                      <TBODY>
	                        <TR align="right">
	                          <TD align="center"><hx:commandExButton tabindex="24"
	                            type="submit" value="クリア" styleClass="commandExButton_etc"
	                            id="clear" disabled="#{pc_Kka00201T03.propClearBtn.disabled}"
	                            onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
	                            action="#{pc_Kka00201T03.doClearAction}"></hx:commandExButton></TD>
	                        </TR>
	                      </TBODY>
	                    </TABLE>
                    </div>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
          <TBODY>
            <TR align="right">
              <TD align="center"><hx:commandExButton tabindex="25"
                type="submit" value="確定" styleClass="commandExButton_dat"
                id="kakutei" disabled="#{pc_Kka00201T01.kka00201.propKakuteiBtn.disabled}"
                confirm="#{msg.SY_MSG_0001W}"
                action="#{pc_Kka00201T03.doKakuteiAction}"></hx:commandExButton>
                <hx:commandExButton type="submit" value="削除" styleClass="commandExButton_dat"
                id="delete" disabled="#{pc_Kka00201T01.kka00201.propDeleteBtn.disabled}"
                confirm="#{msg.SY_MSG_0004W}" tabindex="26"
                action="#{pc_Kka00201T03.doDeleteAction}"></hx:commandExButton></TD>
            </TR>
          </TBODY>
        </TABLE>
        </TD>
      </TR>
    </TBODY>
  </TABLE>
  <h:inputHidden id="htmlHidButtonKbn" value="#{pc_Kka00201T01.kka00201.propHidButtonKbn.integerValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidAction" value="#{pc_Kka00201T01.kka00201.propHidAction.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidErrMessage" value="#{pc_Kka00201T01.kka00201.propHidErrMessage.value}"></h:inputHidden>
  <h:inputHidden id="htmlHidAjaxKbn" value="#{pc_Kka00201T03.propHidAjaxKbn.value}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

</h:form>
<!-- </gakuen:itemStateCtrl> -->
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
