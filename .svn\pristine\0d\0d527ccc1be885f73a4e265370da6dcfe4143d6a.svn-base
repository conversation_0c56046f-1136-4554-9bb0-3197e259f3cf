<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coi00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>従事職種設定検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Coi00301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Coi00301.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Coi00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Coi00301.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" 
					value="新規登録" 
					styleClass="commandExButton" 
					id="register" 
					action="#{pc_Coi00301.doRegisterAction}" 
					disabled="#{pc_Coi00301.propRegister.disabled}" 
					rendered="#{pc_Coi00301.propRegister.rendered}" 
					style="#{pc_Coi00301.propRegister.style}">
</hx:commandExButton>
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style=""
		   class="table" 
		   width="750">
		<TBODY>
			<TR>
				<TH style="" class="v_a" width="150">
					<h:outputText styleClass="outputText" 
								  id="lblTaishoDate" 
								  style="#{pc_Coi00301.propTaishoDate.style}" 
								  value="#{pc_Coi00301.propTaishoDate.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText styleClass="inputText" 
								 id="htmlTaishoDate" 
								 size="10" 
								 value="#{pc_Coi00301.propTaishoDate.dateValue}" 
								 style="#{pc_Coi00301.propTaishoDate.style}" 
								 readonly="#{pc_Coi00301.propTaishoDate.readonly}" 
								 disabled="#{pc_Coi00301.propTaishoDate.disabled}" 
								 rendered="#{pc_Coi00301.propTaishoDate.rendered}">
						<f:convertDateTime />
						<hx:inputHelperAssist errorClass="inputText_Error" 
											  promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_b" width="">
					<h:outputText styleClass="outputText" 
								  id="lblShokushuCd" 
								  style="#{pc_Coi00301.propShokushuCd.style}" 
								  value="#{pc_Coi00301.propShokushuCd.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlShokushuCd" 
								 styleClass="inputText" 
								 size="5" 
								 maxlength="#{pc_Coi00301.propShokushuCd.maxLength}" 
								 value="#{pc_Coi00301.propShokushuCd.stringValue}" 
								 style="#{pc_Coi00301.propShokushuCd.style}" 
								 readonly="#{pc_Coi00301.propShokushuCd.readonly}" 
								 disabled="#{pc_Coi00301.propShokushuCd.disabled}" 
								 rendered="#{pc_Coi00301.propShokushuCd.rendered}">
					</h:inputText>　（前方一致）
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_c" width="">
					<h:outputText styleClass="outputText" 
								  id="lblShokushuName" 
								  style="#{pc_Coi00301.propShokushuName.style}" 
								  value="#{pc_Coi00301.propShokushuName.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlShokushuName" 
								 styleClass="inputText" 
								 size="45" 
								 maxlength="#{pc_Coi00301.propShokushuName.maxLength}" 
								 value="#{pc_Coi00301.propShokushuName.stringValue}" 
								 style="#{pc_Coi00301.propShokushuName.style}" 
								 readonly="#{pc_Coi00301.propShokushuName.readonly}" 
								 disabled="#{pc_Coi00301.propShokushuName.disabled}" 
								 rendered="#{pc_Coi00301.propShokushuName.rendered}">
					</h:inputText>　（部分一致）
				</TD>
			</TR>
		</TBOD>
	</TABLE><BR>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="750"
		   class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton type="submit" 
										value="検索" 
										styleClass="commandExButton_dat" 
										id="search" 
										action="#{pc_Coi00301.doSearchAction}" 
										disabled="#{pc_Coi00301.propSearch.disabled}" 
										rendered="#{pc_Coi00301.propSearch.rendered}" 
										style="#{pc_Coi00301.propSearch.style}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="クリア" 
										styleClass="commandExButton_etc" 
										id="clear" 
										action="#{pc_Coi00301.doClearAction}" 
										style="#{pc_Coi00301.propClear.style}" 
										disabled="#{pc_Coi00301.propClear.disabled}" 
										rendered="#{pc_Coi00301.propClear.rendered}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="750" 
		   style="margin-top:10px;" 
		   height="205">
		<TBODY>
			<TR>
				<TD align="right">
					<h:outputText styleClass="outputText"
								  id="htmlListCount"
								  value="#{pc_Coi00301.propShokushuList.listCount}"
								  style="font-size: 8pt">
					</h:outputText>
					<h:outputText styleClass="outputText" 
								  id="text1" 
								  value="件">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TD valign="top" height="208">
					<DIV style="height: 333px;overflow-y:scroll" 
						 id="listScroll" 
						 onscroll="setScrollPosition('scroll',this);" 
						 class="listScroll">
						<h:dataTable headerClass="headerClass" 
									 footerClass="footerClass" 
									 rowClasses="#{pc_Coi00301.propShokushuList.rowClasses}" 
									 styleClass="meisai_scroll" 
									 style="#{pc_Coi00301.propShokushuList.style}; table-layout: fixed;"
									 id="htmlCodeList" 
									 value="#{pc_Coi00301.propShokushuList.list}" 
									 var="varlist" 
									 border="0" 
									 cellpadding="2" 
									 cellspacing="0" 
									 width="730">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="有効開始日" 
												  id="lblColumn1">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn1" 
											  value="#{varlist.startDate}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="85" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="有効終了日" 
												  id="lblColumn2">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn2" 
											  value="#{varlist.endDate}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="85" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="従事職種コード" 
												  id="lblColumn3">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn3" 
											  value="#{varlist.shokushuCd}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="100" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="従事職種名称" 
												  id="lblColumn4">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn4" 
											  value="#{varlist.shokushuName}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="336" name="width" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" 
													value="詳細" 
													styleClass="commandExButton" 
													id="detail" 
													action="#{pc_Coi00301.doDetailAction}" 
													disabled="#{pc_Coi00301.propDetail.disabled}" 
													rendered="#{pc_Coi00301.propDetail.rendered}">
								</hx:commandExButton>
								<hx:commandExButton type="submit" 
													value="編集" 
													styleClass="commandExButton" 
													id="edit" 
													action="#{pc_Coi00301.doEditAction}" 
													disabled="#{pc_Coi00301.propEdit.disabled}" 
													rendered="#{pc_Coi00301.propEdit.rendered}">
								</hx:commandExButton>
								<hx:commandExButton type="submit" 
													value="ｺﾋﾟｰ" 
													styleClass="commandExButton" 
													id="copy" 
													action="#{pc_Coi00301.doCopyAction}" 
													disabled="#{pc_Coi00301.propCopy.disabled}" 
													rendered="#{pc_Coi00301.propCopy.rendered}">
								</hx:commandExButton>
								<f:attribute value="" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
						</h:dataTable>
					</DIV>
				</TD>
			</TR>
		</TBODY>
	</TABLE><BR>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   class="button_bar" 
		   width="750">
		<TBODY>
			<TR>
				<TD align="center" style="margin-top:10px;">
					<hx:commandExButton type="submit" 
										styleClass="commandExButton_out" 
										id="csvout" 
										value="CSV作成" 
										action="#{pc_Coi00301.doCsvoutAction}" 
										disabled="#{pc_Coi00301.propCsvout.disabled}" 
										rendered="#{pc_Coi00301.propCsvout.rendered}" 
										style="#{pc_Coi00301.propCsvout.style}" 
										confirm="#{msg.SY_MSG_0020W}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="出力項目指定" 
										styleClass="commandExButton_out" 
										id="setoutput" 
										action="#{pc_Coi00301.doSetoutputAction}" 
										disabled="#{pc_Coi00301.propSetoutput.disabled}" 
										rendered="#{pc_Coi00301.propSetoutput.rendered}" 
										style="#{pc_Coi00301.propSetoutput.style}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden value="#{pc_Coi00301.propExecutableSearch.integerValue}"
			   id="htmlExecutableSearch">
</h:inputHidden>
<h:inputHidden value="#{pc_Coi00301.propShokushuList.scrollPosition}"
			   id="scroll">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>
