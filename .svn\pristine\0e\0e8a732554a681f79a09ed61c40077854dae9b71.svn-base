<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kae00104T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>建設仮勘定詳細</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kae00104T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kae00104T02.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kae00104T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kae00104T02.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp" action="#{pc_Kae00104T02.doReturnDispAction}">
			</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" width="900" class="table">
		<TBODY>
			<TR>
				<TH width="175" class="v_a"><h:outputText id="lblKenkariNo"
							styleClass="outputText"
							value="#{pc_Kae00104T01.kae00104.propKenkariNo.labelName}">
						</h:outputText></TH>
				<TD><h:outputText id="htmlKenkariNo"
							styleClass="outputText"
							value="#{pc_Kae00104T01.kae00104.propKenkariNo.stringValue}"
							title="#{pc_Kae00104T01.kae00104.propKenkariNo.stringValue}">
						</h:outputText></TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" cellpadding="0" cellspacing="0" width="900" height="400" style="margin-top: 10px;">
		<TBODY>
			<TR>
				<TD height="27" align="left">
					<hx:commandExButton type="submit" style="width: 164px"
							value="建設仮勘定情報" id="tabKenkari" styleClass="tab_head_off"
							action="#{pc_Kae00104T02.doTabKenkariAction}" tabindex="2"></hx:commandExButton><hx:commandExButton
							type="submit" style="width: 164px" value="管理情報" id="tabKanri"
							styleClass="tab_head_on" tabindex="3">
						</hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD height="100%" align="left" valign="top">
					<TABLE border="1" cellpadding="20" cellspacing="0" height="100%" width="100%" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center" valign="top">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="880" class="table" style="margin-top: 10px;">
								<TBODY>
									<TR>
										<TH class="v_b" width="175"><h:outputText id="lblSetchiBasho"
													styleClass="outputText"
													value="#{pc_Kae00104T02.propSetchiBasho.name}"></h:outputText></TH>
										<TD colspan="3" nowrap >
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText id="htmlSetchiBasho" styleClass="outputText"
													value="#{pc_Kae00104T02.propSetchiBasho.stringValue}"
													title="#{pc_Kae00104T02.propSetchiBasho.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="175"><h:outputText id="lblShozaichi"
											value="#{pc_Kae00104T02.propShozaichi.labelName}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3" nowrap >
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
											<h:outputText id="htmlShozaichi" styleClass="outputText"
													value="#{pc_Kae00104T02.propShozaichi.stringValue}"
													title="#{pc_Kae00104T02.propShozaichi.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_d" width="175"><h:outputText id="lblKensyuDate"
													value="#{pc_Kae00104T02.propKensyuDate.labelName}"
													styleClass="outputText">
												</h:outputText></TH>
										<TD colspan="3" nowrap ><h:outputText id="htmlKensyuDate"
													styleClass="outputText" 
													value="#{pc_Kae00104T02.propKensyuDate.dateValue}">
													<f:convertDateTime pattern="yyyy/MM/dd" />
												</h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_e" width="175"><h:outputText
											id="lblYsnTCd"
											value="#{pc_Kae00104T02.propYsnTani.name}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3" nowrap >
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText id="htmlYsnTani"
													value="#{pc_Kae00104T02.propYsnTani.stringValue}"
													styleClass="outputText"
													title="#{pc_Kae00104T02.propYsnTani.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_f" width="175"><h:outputText
											id="lblMoku"
											value="#{pc_Kae00104T02.propMoku.name}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3" nowrap >
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText id="htmlMoku"
													value="#{pc_Kae00104T02.propMoku.stringValue}"
													styleClass="outputText"
													title="#{pc_Kae00104T02.propMoku.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_g" width="175"><h:outputText
											id="lblKmk"
											value="#{pc_Kae00104T02.propKmk.name}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3" nowrap >
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText id="htmlKmk"
													value="#{pc_Kae00104T02.propKmk.stringValue}"
													styleClass="outputText"
													title="#{pc_Kae00104T02.propKmk.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_a" width="175"><h:outputText id="lblSinseiDate"
													value="#{pc_Kae00104T02.propSinseiDate.labelName}"
													styleClass="outputText">
												</h:outputText></TH>
										<TD colspan="3" nowrap ><h:outputText id="htmlSinseiDate"
													styleClass="outputText" 
													value="#{pc_Kae00104T02.propSinseiDate.dateValue}">
													<f:convertDateTime pattern="yyyy/MM/dd" />
												</h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_b" width="175"><h:outputText
											id="lblSinseiBumon"
											value="#{pc_Kae00104T02.propSinseiBumon.name}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3" nowrap >
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText id="htmlSinseiBumon"
													value="#{pc_Kae00104T02.propSinseiBumon.stringValue}"
													styleClass="outputText"
													title="#{pc_Kae00104T02.propSinseiBumon.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="175"><h:outputText styleClass="outputText"
											id="lblSinseiId" value="#{pc_Kae00104T02.propSinsei.name}"
											style="#{pc_Kae00104T02.propSinsei.labelStyle}"></h:outputText></TH>
										<TD colspan="3" nowrap >
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText"
													value="#{pc_Kae00104T02.propSinsei.stringValue}"
													id="htmlSinsei"
													title="#{pc_Kae00104T02.propSinsei.stringValue}"></h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_d" width="175"><h:outputText id="lblSinseiRiyu"
											value="#{pc_Kae00104T02.propSinseiRiyu.labelName}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3">
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText id="htmlSinseiRiyu" styleClass="outputText"
													value="#{pc_Kae00104T02.propSinseiRiyu.stringValue}"
													title="#{pc_Kae00104T02.propSinseiRiyu.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_e" width="175"><h:outputText
											id="lblShutokuRingiNo"
											value="#{pc_Kae00104T02.propShutokuRingiNo.labelName}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3"><h:outputText id="htmlShutokuRingiNo"
													value="#{pc_Kae00104T02.propShutokuRingiNo.stringValue}"
													styleClass="outputText"
													title="#{pc_Kae00104T02.propShutokuRingiNo.stringValue}">
												</h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_f" width="175"><h:outputText
											id="lblShutokuDenpyoKanriNo"
											value="#{pc_Kae00104T02.propShutokuDenpyoKanriNo.labelName}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3"><h:outputText id="htmlShutokuDenpyoKanriNo"
													value="#{pc_Kae00104T02.propShutokuDenpyoKanriNo.stringValue}"
													styleClass="outputText"
													title="#{pc_Kae00104T02.propShutokuDenpyoKanriNo.stringValue}">
												</h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_g" width="175"><h:outputText id="lblBiko"
													value="#{pc_Kae00104T02.propBiko.name}"
													styleClass="outputText">
												</h:outputText></TH>
										<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
													id="htmlBiko" cols="70" rows="2"
													readonly="#{pc_Kae00104T02.propBiko.readonly}"
													disabled="true"
													value="#{pc_Kae00104T02.propBiko.stringValue}"
													style="#{pc_Kae00104T02.propBiko.style}" tabindex="22"
													title="#{pc_Kae00104T02.propBiko.stringValue}">
												</h:inputTextarea></TD>
									</TR>
								</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE class="button_bar" width="820" style="margin-top: 10px;">
		<TBODY>
			<TR>
				<TD>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

