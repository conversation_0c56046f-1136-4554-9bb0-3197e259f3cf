<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KMB_SMJK_GPA" name="卒業見込条件要素＿ＧＰＡ" prod_id="KM" description="卒業見込条件要素を構成するＧＰＡです。
卒業見込条件要素の判定方法２が「３」（ＧＰＡ指定）の時、そのＧＰＡ値を保持します。
「卒業見込条件要素」と「卒業見込条件要素＿ＧＰＡ」は１：１で対応します。
『卒業見込条件設定』にて作成されます。">
<STATMENT><![CDATA[
KMB_SMJK_GPA
]]></STATMENT>
<COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="卒業見込判定で判定対象となる学生のみなし入学年度が設定されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="卒業見込判定で判定対象となる学生の入学学期が設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="卒業見込判定で判定対象となる学生のカリキュラム学科組織コードが設定されます。"/><COLUMN id="SOTGYO_MKM_JOKEN_CD" name="卒業見込条件コード" type="string" length="5" lengthDP="0" byteLength="5" description="卒業見込条件を識別する任意のコードが設定されます。"/><COLUMN id="YOSO_NO" name="要素ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="１つの卒業見込条件を構成する複数の卒業見込条件要素を識別する番号です。"/><COLUMN id="GPA" name="ＧＰＡ値" type="number" length="8" lengthDP="5" byteLength="0" description="ＧＰＡの値が設定されます。判定時に学生の通算ＧＰＡと比較されます。"/>
</TABLE>
