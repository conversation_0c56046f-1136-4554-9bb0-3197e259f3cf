<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsd01801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsd01801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function openPCos0401() {
 setTarget("xxxx");
 openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
 return true;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsd01801.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsd01801.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsd01801.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsd01801.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="80%">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblNyushiNendo"
										value="#{pc_Nsd01801.propNyushiNendo.labelName}"
										style="#{pc_Nsd01801.propNyushiNendo.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:inputText styleClass="inputText"
										id="htmlNyushiNendo" size="4"
										value="#{pc_Nsd01801.propNyushiNendo.dateValue}"
										required="false" tabindex="1"
										disabled="#{pc_Nsd01801.propNyushiNendo.disabled}"
										maxlength="#{pc_Nsd01801.propNyushiNendo.maxLength}"
										readonly="#{pc_Nsd01801.propNyushiNendo.readonly}"
										style="#{pc_Nsd01801.propNyushiNendo.style}">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblNyushiGakkiNo"
										value="#{pc_Nsd01801.propNyushiGakkiNo.labelName}"
										style="#{pc_Nsd01801.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlNyushiGakkiNo" size="2"
										value="#{pc_Nsd01801.propNyushiGakkiNo.integerValue}"
										tabindex="2"
										disabled="#{pc_Nsd01801.propNyushiGakkiNo.disabled}"
										maxlength="#{pc_Nsd01801.propNyushiGakkiNo.maxLength}"
										readonly="#{pc_Nsd01801.propNyushiGakkiNo.readonly}"
										style="#{pc_Nsd0001.propNyushiGakkiNo.style}">
										<f:convertNumber pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="80%" style="margin-top:15px">
							<TBODY>
								<TR>
									<TH class="v_a" colspan="" width="150">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFile"
													value="#{pc_Nsd01801.propInputFile.labelName}"
													style="#{pc_Nsd01801.propInputFile.labelStyle}"></h:outputText></TH>
											</TR>
											<TR>
												<TH class="clear_border"><h:outputText styleClass="outputText" id="lblInputFilePreL"
													value="(前回ファイル)"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									</TH>
									<TD class="">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border"><hx:fileupload styleClass="fileupload"
													id="htmlInputFile" size="50"
													value="#{pc_Nsd01801.propInputFile.value}" style="width:550px"
													tabindex="3">
													<hx:fileProp name="fileName"
														value="#{pc_Nsd01801.propInputFile.fileName}" />
													<hx:fileProp name="contentType"
														value="#{pc_Nsd01801.propInputFile.contentType}" />
												</hx:fileupload></TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:outputText styleClass="outputText" id="lblInputFileOld"
													value="#{pc_Nsd01801.propInputFileOld.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH nowrap width="150" class="v_e"><h:outputText
										styleClass="outputText" id="lblSyoryKbnL"
										value="#{pc_Nsd01801.propSyoriKbn.labelName}"
										style="#{pc_Nsd01801.propSyoriKbn.labelStyle}"></h:outputText></TH>
									<TD char="0"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
										value="#{pc_Nsd01801.propSyoriKbn.checked}" tabindex="4"></h:selectBooleanCheckbox>
									チェックのみ（データの登録/更新は行いません）</TD>
								</TR>
								<TR>
									<TH class="v_f" nowrap><h:outputText
										styleClass="outputText" id="lblCheckList"
										value="#{pc_Nsd01801.propCheckList.labelName}"
										style="#{pc_Nsd01801.propCheckList.labelStyle}"></h:outputText></TH>
									<TD height="300"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlCheckList"
										layout="pageDirection"
										value="#{pc_Nsd01801.propCheckList.value}" tabindex="5">
										<f:selectItem itemValue="0" itemLabel="正常データ" />
										<f:selectItem itemValue="1" itemLabel="エラーデータ" />
										<f:selectItem itemValue="2" itemLabel="ワーニングデータ" />
									</h:selectManyCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="入力項目指定"
										styleClass="commandExButton_etc" id="setinput" onclick="return openPCos0401();"
										action="#{pc_Nsd01801.doSetinputAction}" tabindex="6"></hx:commandExButton><hx:commandExButton
										type="submit" value="実行" styleClass="commandExButton_dat"
										id="exec" action="#{pc_Nsd01801.doExecAction}"
										confirm="#{msg.SY_MSG_0001W}" tabindex="7"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					<TR>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

