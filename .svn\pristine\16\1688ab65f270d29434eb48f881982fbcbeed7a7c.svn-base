﻿<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsa00502T06.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsa00502T06.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Nsa00502T06.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Nsa00502T06.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Nsa00502T06.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Nsa00502T06.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>

						<TABLE border="0" cellpadding="0" cellspacing="0" width="90%">
							<TBODY>
								<TR>
									<TD style="" width="550">
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%">
										<TBODY>
											<TR>
												<TH style="" class="v_a" width="150"><h:outputText
													styleClass="outputText" id="lblToiCd"
													value="#{pc_Nsa00502T06.nsa00502.propToiCd.labelName}"></h:outputText></TH>
												<TD width="200"><h:outputText styleClass="outputText"
													id="htmlToiCd"
													value="#{pc_Nsa00502T06.nsa00502.propToiCd.stringValue}"></h:outputText></TD>
												<TH style="" class="v_b" width="150"><h:outputText
													styleClass="outputText" id="lblToiName"
													value="#{pc_Nsa00502T06.nsa00502.propToiName.labelName}"></h:outputText></TH>
												<TD width="400"><h:outputText styleClass="outputText"
													id="htmlToiName"
													value="#{pc_Nsa00502T06.nsa00502.propToiName.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TH></TH>
					</TR>
					<TR>
						<TH>

						<TABLE border="0" cellpadding="0" cellspacing="0" width="90%">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="tab_head_off" width="16%"><hx:commandExButton
													type="submit" value="問合せ者情報①" styleClass="tab_head_off"
													id="selectTab1" style="width: 100%"
													disabled="#{pc_Nsa00502T06.propSelectTab1.disabled}"
													action="#{pc_Nsa00502T06.doSelectTab1Action}" tabindex="11"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="16%"><hx:commandExButton
													type="submit" value="問合せ者情報②" styleClass="tab_head_off"
													id="selectTab2" style="width: 100%"
													disabled="#{pc_Nsa00502T06.propSelectTab2.disabled}"
													action="#{pc_Nsa00502T06.doSelectTab2Action}" tabindex="12"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="17%"><hx:commandExButton
													type="submit" value="出身校等情報" styleClass="tab_head_off"
													id="selectTab3" style="width: 100%"
													disabled="#{pc_Nsa00502T06.propSelectTab3.disabled}"
													action="#{pc_Nsa00502T06.doSelectTab3Action}" tabindex="13"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="17%"><hx:commandExButton
													type="submit" value="保証人情報" styleClass="tab_head_off"
													id="selectTab4" style="width: 100%"
													disabled="#{pc_Nsa00502T06.propSelectTab4.disabled}"
													action="#{pc_Nsa00502T06.doSelectTab4Action}" tabindex="14"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="17%"><hx:commandExButton
													type="submit" value="募集情報" styleClass="tab_head_off"
													id="selectTab5" style="width: 100%"
													disabled="#{pc_Nsa00502T06.propSelectTab5.disabled}"
													action="#{pc_Nsa00502T06.doSelectTab5Action}" tabindex="15"></hx:commandExButton></TD>
												<TD class="tab_head_on" width="17%"><hx:commandExButton
													type="submit" value="自由設定" styleClass="tab_head_on"
													id="selectTab6" style="width: 100%"
													disabled="#{pc_Nsa00502T06.propSelectTab6.disabled}" tabindex="1"
													></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="100%" height="380">
										<TBODY>
											<TR>
												<TD height="40" valign="bottom">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													width="90%">
													<TBODY>

														<TR valign="middle">
															<TD align="left">
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="table">
																<TBODY>
																	<TR>
																		<TH class="v_c" width="150"><h:outputText
																			styleClass="outputText" id="lblFreTsyCd"
																			value="#{pc_Nsa00502T06.propFreTsyCd.labelName}"></h:outputText></TH>
																		<TD width="360"><h:selectOneMenu
																			styleClass="selectOneMenu" id="htmlFreTsyCd"
																			style="width:350px;"
																			value="#{pc_Nsa00502T06.propFreTsyCd.value}"
																			disabled="#{pc_Nsa00502T06.propFreTsyCd.disabled}"
																			readonly="#{pc_Nsa00502T06.propFreTsyCd.readonly}" tabindex="2">
																			<f:selectItems
																				value="#{pc_Nsa00502T06.propFreTsyCd.list}" />
																		</h:selectOneMenu></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															<TD></TD>
															<TD width="60"><hx:commandExButton type="submit"
																value="選択" styleClass="commandExButton" id="select"
																disabled="#{pc_Nsa00502T06.propSelect.disabled}" 
																action="#{pc_Nsa00502T06.doSelectAction}" tabindex="3"></hx:commandExButton></TD>
															<TD width="60"><hx:commandExButton type="submit"
																value="解除" styleClass="commandExButton" id="reset"
																disabled="#{pc_Nsa00502T06.propReset.disabled}" 
																action="#{pc_Nsa00502T06.doResetAction}" tabindex="4"></hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TD height="40" valign="middle">

												<TABLE border="0" cellpadding="0" cellspacing="0"
													width="90%">
													<TBODY>
														<TR>
															<TD width="350" align="left">
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="table">
																<TBODY>
																	<TR>
																		<TH class="v_d" width="150"><h:outputText
																			styleClass="outputText" id="lblFreDataNoName"
																			value="#{pc_Nsa00502T06.propFreDataNo.labelName}"></h:outputText></TH>
																		<TD class="v_d" width="25"><hx:commandExButton
																			type="submit" value="|＜" styleClass="commandExButton"
																			id="first"
																			disabled="#{pc_Nsa00502T06.propFirst.disabled}" 
																			action="#{pc_Nsa00502T06.doFirstAction}" tabindex="5"></hx:commandExButton></TD>
																		<TD width="25"><hx:commandExButton type="submit"
																			value="＜" styleClass="commandExButton" id="prev"
																			disabled="#{pc_Nsa00502T06.propPrev.disabled}" 
																			action="#{pc_Nsa00502T06.doPrevAction}" tabindex="6"></hx:commandExButton></TD>
																		<TD width="25" >
																			<h:outputText styleClass="outputText"
																				id="lblFreDataNo" value="#{pc_Nsa00502T06.propLblDataNo.integerValue}">
																			<f:convertNumber pattern="##0" />
																		</h:outputText>
																		</TD>
																		<TD width="25"><hx:commandExButton type="submit"
																			value="＞" styleClass="commandExButton" id="next"
																			disabled="#{pc_Nsa00502T06.propNext.disabled}" 
																			action="#{pc_Nsa00502T06.doNextAction}" tabindex="7"></hx:commandExButton></TD>
																		<TD width="25"><hx:commandExButton type="submit"
																			value="＞|" styleClass="commandExButton" id="last"
																			disabled="#{pc_Nsa00502T06.propLast.disabled}" 
																			action="#{pc_Nsa00502T06.doLastAction}" tabindex="8"></hx:commandExButton></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															<TD></TD>
															<TD width="200"><h:inputText styleClass="inputText"
																id="htmlFreDataNo" size="3"
																value="#{pc_Nsa00502T06.propFreDataNo.integerValue}"
																disabled="#{pc_Nsa00502T06.propFreDataNo.disabled}"
																maxlength="#{pc_Nsa00502T06.propFreDataNo.maxLength}"
																readonly="#{pc_Nsa00502T06.propFreDataNo.readonly}"
																style="#{pc_Nsa00502T06.propFreDataNo.style}"
																tabindex="9">
																<f:convertNumber pattern="##0" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
															</h:inputText>
															<hx:commandExButton type="submit" value="ジャンプ"
																styleClass="commandExButton" id="jump"
																disabled="#{pc_Nsa00502T06.propJump.disabled}"
																action="#{pc_Nsa00502T06.doJumpAction}" tabindex="10"></hx:commandExButton><TD width="60">
															</TD>
															<TD align="right" width="200"><h:outputText
																styleClass="outputText" id="lblCount1" value="データNo登録件数">
															</h:outputText>&nbsp;<h:outputText
																styleClass="outputText" id="htmlDataNoCount"
																value="#{pc_Nsa00502T06.propDataNoCount.stringValue}">
															</h:outputText><h:outputText styleClass="outputText"
																id="lblCount2" value="件"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TD height="15">
												</TD>
											</TR>
											<TR>
												<TD>
												<div class="listScroll" style="height:215px;width:90%"
													id="listscroll"
													onscroll="setScrollPosition('scroll',this);"><h:dataTable
													border="0" cellpadding="2" cellspacing="0"
													columnClasses="columnClass1" headerClass="headerClass"
													footerClass="footerClass" styleClass="meisai_scroll"
													id="htmlFreList" width="" var="varlist"
													value="#{pc_Nsa00502T06.propFreList.list}"
													rowClasses="#{pc_Nsa00502T06.propFreList.rowClasses}">
													<h:column id="column1">
														<f:facet name="header">
															<h:outputText styleClass="outputText" value="項目NO"
																id="lbl1"></h:outputText>
														</f:facet>
														<h:outputText styleClass="outputText" id="htmlFreKomokNo"
															value="#{varlist.freKomokNo}" ></h:outputText>
														<f:attribute value="50" name="width" />
														<f:attribute value="text-align: center" name="style" />
													</h:column>
													<h:column id="column2">
														<f:facet name="header">
															<h:outputText styleClass="outputText" id="lbl2"
																value="自由設定項目名称"></h:outputText>
														</f:facet>
														<h:outputText styleClass="outputText"
															id="htmlFreKomokName" value="#{varlist.freKomokName}"></h:outputText>
														<f:attribute value="200" name="width" />
													</h:column>
													<h:column id="columnToi3">
														<f:facet name="header">
															<h:outputText styleClass="outputText" value="自由設定内容"
																id="lbl"></h:outputText>
														</f:facet>
														<h:inputTextarea styleClass="inputTextarea"
															id="htmlFreValue" style="width:100%"
															value="#{varlist.freValue}" readonly="true"></h:inputTextarea>
														<f:attribute value="500" name="width" />
													</h:column></h:dataTable></div>
												</TD>
											</TR>


										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TH>
					</TR>

				</TBODY>
			</TABLE>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Nsa00502T06.propFreList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="javascript">
window.attachEvent('onload', endload);
function endload(){
	changeScrollPosition('scroll', 'listScroll');
}
</SCRIPT>
</HTML>

