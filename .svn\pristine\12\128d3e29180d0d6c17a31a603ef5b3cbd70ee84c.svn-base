<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmc00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById("form1:htmlExecButton").value = "1";
	indirectClick("exec");
}

function confirmCancel(){
	alert('中断しました');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmc00101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kmc00101.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kmc00101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kmc00101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 --><TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center"><TABLE width="600" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="200"><h:outputText
										styleClass="outputText" id="lblKanriBsyoCd"
										value="#{pc_Kmc00101.propKanriBsyoCd.labelName}"
										style="#{pc_Kmc00101.propKanriBsyoCd.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlKanriBsyoCd"
										value="#{pc_Kmc00101.propKanriBsyoCd.stringValue}"
										style="width:390px">
										<f:selectItems value="#{pc_Kmc00101.propKanriBsyoCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="200" class="v_b"><h:outputText
										styleClass="outputText" id="lblCurGakkaCd"
										value="#{pc_Kmc00101.propCurGakkaCd.labelName}"
										style="#{pc_Kmc00101.propCurGakkaCd.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlCurGakkaCd"
										value="#{pc_Kmc00101.propCurGakkaCd.stringValue}"
										style="width:390px">
										<f:selectItems value="#{pc_Kmc00101.propCurGakkaCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_c" width="200"><h:outputText
										styleClass="outputText" id="lblGakunen"
										value="#{pc_Kmc00101.propGakunen.labelName}" style="#{pc_Kmc00101.propGakunen.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakunen"
										value="#{pc_Kmc00101.propGakunen.stringValue}"
										style="width:140px">
										<f:selectItems value="#{pc_Kmc00101.propGakunen.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="200" class="v_d"><h:outputText
										styleClass="outputText" id="lblSemester"
										value="#{pc_Kmc00101.propSemester.labelName}" style="#{pc_Kmc00101.propSemester.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSemester"
										value="#{pc_Kmc00101.propSemester.stringValue}"
										style="width:140px">
										<f:selectItems value="#{pc_Kmc00101.propSemester.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center" height="40">
						<HR noshade class="hr"/>
						</TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE width="600" border="0" cellpadding="0" cellspacing="0"
							class="button_bar">
							<TBODY>
								<TR charoff="0">
									<TD><hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_dat" id="exec" action="#{pc_Kmc00101.doExecAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kmc00101.propExecutableExec.integerValue}"
				id="htmlExecButton">
				<f:convertNumber type="number" />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

