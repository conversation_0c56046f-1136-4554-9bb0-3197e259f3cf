<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="NSC_SGN_CORE" name="志願者個人情報" prod_id="NS" description="志願者の個人情報です。">
<STATMENT><![CDATA[
(SELECT SGN_CORE.SGN_CD, SUBSTR(SGN_CORE.SGN_ADDR_NO,1,3) SGN_ADDR_NO_3KETA, SUBSTR(SGN_CORE.SGN_ADDR_NO,4) SGN_ADDR_NO_4KETA, SGN_CORE.SGN_ADDR1, SGN_CORE.SGN_ADDR2, SGN_CORE.SGN_ADDR3, SGN_CORE.SGN_ADDR_KANA1, SGN_CORE.SGN_ADDR_KANA2, SGN_CORE.SGN_ADDR_KANA3, SGN_CORE.SGN_TEL1, SGN_CORE.SGN_TEL2, SGN_CORE.SGN_KEITAI_TEL, SGN_CORE.SGN_FAX, SGN_CORE.SGN_E_MAIL, SGN_CORE.RENRAKU, SGN_CORE.RENRAKU_TEL, SGN_CORE.BIRTH, SGN_CORE.SSN_CD, SGN_CORE.SSN_KTI_CD, SGN_CORE.CENTER_SYUSINKO_KA_CD, SGN_CORE.SSN_GKK_CD, SGN_CORE.SSN_NGAK_NEN, SGN_CORE.RONIN_NENSU, SGN_CORE.SNT_SSN_CD, SGN_CORE.COUNTRY_CD, SGN_CORE.HONSEKI_CD, SGN_CORE.SYUSSIN_CD, SGN_CORE.CHIKU_CD FROM NSC_SGN SGN_CORE)
]]></STATMENT>
<COLUMN id="SGN_CD" name="志願者番号" type="string" length="10" lengthDP="0" byteLength="10" description="志願者を一意に識別するための番号です。"/><COLUMN id="SGN_ADDR_NO_3KETA" name="志願者郵便番号（上３桁）" type="string" length="3" lengthDP="0" byteLength="3" description="志願者の郵便番号の上３桁が設定されます。"/><COLUMN id="SGN_ADDR_NO_4KETA" name="志願者郵便番号（下４桁）" type="string" length="4" lengthDP="0" byteLength="4" description="志願者の郵便番号の下４桁が設定されます。"/><COLUMN id="SGN_ADDR1" name="志願者住所１" type="string" length="50" lengthDP="0" byteLength="150" description="志願者の住所１です。"/><COLUMN id="SGN_ADDR2" name="志願者住所２" type="string" length="50" lengthDP="0" byteLength="150" description="志願者の住所２です。"/><COLUMN id="SGN_ADDR3" name="志願者住所３" type="string" length="50" lengthDP="0" byteLength="150" description="志願者の住所３です。"/><COLUMN id="SGN_ADDR_KANA1" name="志願者住所＿カナ１" type="string" length="100" lengthDP="0" byteLength="300" description="志願者のカナ住所１を表します。"/><COLUMN id="SGN_ADDR_KANA2" name="志願者住所＿カナ２" type="string" length="100" lengthDP="0" byteLength="300" description="志願者のカナ住所２を表します。"/><COLUMN id="SGN_ADDR_KANA3" name="志願者住所＿カナ３" type="string" length="100" lengthDP="0" byteLength="300" description="志願者のカナ住所３を表します。"/><COLUMN id="SGN_TEL1" name="志願者電話番号１" type="string" length="25" lengthDP="0" byteLength="25" description="志願者の電話番号です。"/><COLUMN id="SGN_TEL2" name="志願者電話番号２" type="string" length="25" lengthDP="0" byteLength="25" description="志願者の電話番号です。"/><COLUMN id="SGN_KEITAI_TEL" name="志願者携帯電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="志願者の携帯電話の電話番号です。"/><COLUMN id="SGN_FAX" name="志願者ＦＡＸ番号" type="string" length="25" lengthDP="0" byteLength="25" description="志願者のＦＡＸ番号です。"/><COLUMN id="SGN_E_MAIL" name="志願者E_MAILアドレス" type="string" length="60" lengthDP="0" byteLength="60" description="志願者のE-Mailアドレスです。"/><COLUMN id="RENRAKU" name="連絡先" type="string" length="100" lengthDP="0" byteLength="300" description="本人以外の連絡先住所です。"/><COLUMN id="RENRAKU_TEL" name="連絡先電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="本人以外の連絡先電話番号です。"/><COLUMN id="BIRTH" name="生年月日" type="date" length="0" lengthDP="0" byteLength="0" description="学生の生年月日が設定されます。"/><COLUMN id="SSN_CD" name="出身校コード" type="string" length="6" lengthDP="0" byteLength="6" description="出身校のコードです。"/><COLUMN id="SSN_KTI_CD" name="出身校課程コード" type="string" length="2" lengthDP="0" byteLength="2" description="出身校の課程コードです。"/><COLUMN id="CENTER_SYUSINKO_KA_CD" name="センター試験出身校学科コード" type="string" length="1" lengthDP="0" byteLength="1" description="センター試験用の出身校科コードです。"/><COLUMN id="SSN_GKK_CD" name="出身校学科コード" type="string" length="5" lengthDP="0" byteLength="5" description="出身校の学科コードです。"/><COLUMN id="RONIN_NENSU" name="浪人年数" type="number" length="2" lengthDP="0" byteLength="0" description="学生の浪人年数が設定されます。"/><COLUMN id="SNT_SSN_CD" name="その他出身校等コード" type="string" length="6" lengthDP="0" byteLength="6" description="その他出身校等を識別する為の任意のコードです。"/><COLUMN id="COUNTRY_CD" name="国籍コード" type="string" length="5" lengthDP="0" byteLength="5" description="国籍のコードです。"/><COLUMN id="HONSEKI_CD" name="本籍地コード" type="string" length="2" lengthDP="0" byteLength="2" description="本籍地のコードです。"/><COLUMN id="SYUSSIN_CD" name="出身地コード" type="string" length="2" lengthDP="0" byteLength="2" description="出身地のコードです。"/><COLUMN id="CHIKU_CD" name="地区コード" type="string" length="2" lengthDP="0" byteLength="2" description="地区のコードです。"/>
</TABLE>
