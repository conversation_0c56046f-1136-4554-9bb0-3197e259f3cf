<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KMD_JGKM_KYN" name="授業コマ＿教員" prod_id="KM" description="授業のコマ毎の担当教員情報を持ちます。
『授業登録』、『授業一括登録』にて作成されます。">
<STATMENT><![CDATA[
KMD_JGKM_KYN
]]></STATMENT>
<COLUMN id="NENDO" name="年度" type="number" length="4" lengthDP="0" byteLength="0" description="現在年度が設定されます。"/><COLUMN id="JUGYO_CD" name="授業コード" type="string" length="10" lengthDP="0" byteLength="10" description="授業を一意に識別するコードが設定されます。
『授業登録』、『授業一括登録』にて作成されます。"/><COLUMN id="KAIKO_NENDO" name="開講年度" type="number" length="4" lengthDP="0" byteLength="0" description="授業が開講される年度が設定されます。
『授業登録』、『授業一括登録』にて作成されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="授業が開講される学期ＮＯが設定されます。"/><COLUMN id="JUGYO_KBN" name="授業区分" type="string" length="1" lengthDP="0" byteLength="1" description="授業の区分が設定されます。"><CODE><CASE value="1" display="週間授業"/><CASE value="2" display="集中講義"/><CASE value="3" display="実習"/><CASE value="4" display="隔週偶数"/><CASE value="5" display="隔週奇数"/></CODE></COLUMN><COLUMN id="KAIKO_YOBI" name="開講曜日" type="number" length="1" lengthDP="0" byteLength="0" description="授業が開講される曜日が設定されます。
週間授業、隔週授業以外の場合は０です。
"><CODE><CASE value="1" display="月曜"/><CASE value="2" display="火曜"/><CASE value="3" display="水曜"/><CASE value="4" display="木曜"/><CASE value="5" display="金曜"/><CASE value="6" display="土曜"/><CASE value="7" display="日曜"/></CODE></COLUMN><COLUMN id="JIGEN_NO" name="時限ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="授業が開講される時限ＮＯが設定されます。週間授業、隔週授業以外の場合は０です。"/><COLUMN id="TANTO_KYOIN_CD" name="担当教員コード" type="string" length="20" lengthDP="0" byteLength="20" description="担当教員のコードが設定されます。"/><COLUMN id="JYUGYO_DAIHYO_KYOIN_FLG" name="授業代表教員フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="授業の代表教員かを判断するためのフラグが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="KOMA_DAIHYO_KYOIN_FLG" name="コマ代表教員フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="授業コマの代表教員かを判断するためのフラグが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="KOMA_SU" name="コマ数" type="number" length="5" lengthDP="2" byteLength="0" description="教員の担当コマ数が設定されます。"/>
</TABLE>
