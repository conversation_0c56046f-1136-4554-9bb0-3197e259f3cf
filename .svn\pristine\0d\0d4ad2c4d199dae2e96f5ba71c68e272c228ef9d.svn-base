<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/PKab0301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>設置場所検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function getYsnTNameRyak(thisObj, thisEvent) {
	// 予算単位名称を取得する																				
	var servlet = "rev/ka/KaCogYosanTaniAJAX";
	var args = new Array();
	args['code1'] = '0';
	args['code2'] = '1';
	args['code3'] = '';
	args['code4'] = thisObj.value;
	var target = "form1:htmlYsnTNameRyak";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}



function func_1(thisObj, thisEvent) {
//選択時
	var retId = document.getElementById("form1:htmlHiddenSetchiBashoCdId").value;
	var listBox = document.getElementById("form1:htmlSetchiBashoList");
	var noSelectMsg = document.getElementById("form1:htmlNoSelectMsgHidden").value;
	
	if (window.opener) {
		if (listBox.selectedIndex != -1) {
			var sel = listBox.options[listBox.selectedIndex].value;
			var retObj = window.opener.document.getElementById(retId);
			if (sel != "|no select|") {
				// 親ウィンドウのInputTextにコードをセット
				retObj.value = sel;
				// 親ウィンドウのAjaxを実行して名称をセット
				retObj.onblur();
				// 親ウィンドウのInputTextにフォーカスセット
				if (retObj.style.disabled != true && retObj.readOnly != true) {
					retObj.focus();
				}
				indirectClick('closeDisp');
				return true;
			} else {
				setErrMsg(noSelectMsg);
			}
		} else {
				setErrMsg(noSelectMsg);
		}
	}
	return true;
}

function loadFunc() {
	getYsnTNameRyak(document.getElementById('form1:htmlGenkaKeijoYsnTCd'), '');
}

window.attachEvent("onload", attachFormatNumber);
window.attachEvent("onload", loadFunc);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PKab0301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/childHeader.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
	<hx:commandExButton
		 type="submit"
		value="閉じる"
		styleClass="commandExButton"
		id="closeDisp"
		action="#{pc_PKab0301.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PKab0301.funcId}"></h:outputText>
	<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
	<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PKab0301.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err">
	<LEGEND>エラーメッセージ</LEGEND>
	<h:outputText
		id="message"
		value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText"
		escape="false">
	</h:outputText>
</FIELDSET>
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウト崩れ防止の為、全角スペースを配置-->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-top:10px">
	<TR>
		<TD>
			<TABLE border="0" class="table" width="650" cellpadding="0" cellspacing="0">
				<TR>
					<TH class="v_a" align="left" width="170">
						<h:outputText
							styleClass="outputText"
							id="lblSetchiBashoCd"
							value="#{pc_PKab0301.propSetchiBashoCd.labelName}"
							style="#{pc_PKab0301.propSetchiBashoCd.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="480">
						<h:inputText 
							rendered="#{pc_PKab0301.propSetchiBashoCd.rendered}" 
							id="htmlSetchiBashoCd" 
							styleClass="inputText" 
							readonly="#{pc_PKab0301.propSetchiBashoCd.readonly}" 
							style="#{pc_PKab0301.propSetchiBashoCd.style}" 
							size="10" 
							value="#{pc_PKab0301.propSetchiBashoCd.stringValue}" 
							maxlength="#{pc_PKab0301.propSetchiBashoCd.maxLength}" 
							disabled="#{pc_PKab0301.propSetchiBashoCd.disabled}">
						</h:inputText>
						(前方一致)
					</TD>
				</TR>
				<TR>
					<TH class="v_b" align="left">
						<h:outputText
							styleClass="outputText"
							id="lblSetchiBashoName"
							value="#{pc_PKab0301.propSetchiBashoName.labelName}"
							style="#{pc_PKab0301.propSetchiBashoName.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<TABLE border="0" class="clear_border">
							<TR>
								<TD>
									<h:inputText
										rendered="#{pc_PKab0301.propSetchiBashoName.rendered}"
										id="htmlSetchiBashoName" styleClass="inputText"
										readonly="#{pc_PKab0301.propSetchiBashoName.readonly}"
										style="#{pc_PKab0301.propSetchiBashoName.style}" size="40"
										value="#{pc_PKab0301.propSetchiBashoName.stringValue}"
										maxlength="#{pc_PKab0301.propSetchiBashoName.maxLength}"
										disabled="#{pc_PKab0301.propSetchiBashoName.disabled}">
									</h:inputText>
								</TD>
								<TD>
									<h:selectOneRadio
										rendered="#{pc_PKab0301.propSetchiBashoNameFindType.rendered}"
										id="htmlSetchiBashoNameFindType"
										readonly="#{pc_PKab0301.propSetchiBashoNameFindType.readonly}"
										styleClass="selectOneRadio"
										style="#{pc_PKab0301.propSetchiBashoNameFindType.style}"
										disabledClass="selectOneRadio_Disabled"
										value="#{pc_PKab0301.propSetchiBashoNameFindType.stringValue}"
										disabled="#{pc_PKab0301.propSetchiBashoNameFindType.disabled}">
										<f:selectItem itemValue="0" itemLabel="前方一致" />
										<f:selectItem itemValue="1" itemLabel="部分一致" />
									</h:selectOneRadio>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TH class="v_c" align="left">
						<h:outputText
							styleClass="outputText"
							id="lblSetchiBashoNameRyak"
							style="#{pc_PKab0301.propSetchiBashoNameRyak.labelStyle}"
							value="#{pc_PKab0301.propSetchiBashoNameRyak.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<TABLE border="0" class="clear_border">
							<TR>
								<TD>
									<h:inputText
										rendered="#{pc_PKab0301.propSetchiBashoNameRyak.rendered}"
										id="htmlSetchiBashoNameRyak" styleClass="inputText"
										readonly="#{pc_PKab0301.propSetchiBashoNameRyak.readonly}"
										style="#{pc_PKab0301.propSetchiBashoNameRyak.style}" size="20"
										value="#{pc_PKab0301.propSetchiBashoNameRyak.stringValue}"
										maxlength="#{pc_PKab0301.propSetchiBashoNameRyak.maxLength}"
										disabled="#{pc_PKab0301.propSetchiBashoNameRyak.disabled}">
									</h:inputText>
								</TD>
								<TD>
									<h:selectOneRadio
										rendered="#{pc_PKab0301.propSetchiBashoNameRyakFindType.rendered}"
										id="htmlSetchiBashoNameRyakFindType"
										readonly="#{pc_PKab0301.propSetchiBashoNameRyakFindType.readonly}"
										styleClass="selectOneRadio"
										style="#{pc_PKab0301.propSetchiBashoNameRyakFindType.style}"
										disabledClass="selectOneRadio_Disabled"
										value="#{pc_PKab0301.propSetchiBashoNameRyakFindType.stringValue}"
										disabled="#{pc_PKab0301.propSetchiBashoNameRyakFindType.disabled}">
										<f:selectItem itemValue="0" itemLabel="前方一致 " />
										<f:selectItem itemValue="1" itemLabel="部分一致" />
									</h:selectOneRadio>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TH class="v_d" align="left">
						<h:outputText
							styleClass="outputText" id="lblGenkaKeijoYsnTCd"
							style="#{pc_PKab0301.propGenkaKeijoYsnTCd.labelStyle}"
							value="#{pc_PKab0301.propGenkaKeijoYsnTCd.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText
							rendered="#{pc_PKab0301.propGenkaKeijoYsnTCd.rendered}"
							id="htmlGenkaKeijoYsnTCd"
							styleClass="inputText"
							readonly="#{pc_PKab0301.propGenkaKeijoYsnTCd.readonly}"
							style="#{pc_PKab0301.propGenkaKeijoYsnTCd.style}"
							size="10"
							value="#{pc_PKab0301.propGenkaKeijoYsnTCd.stringValue}"
							maxlength="#{pc_PKab0301.propGenkaKeijoYsnTCd.maxLength}"
							disabled="#{pc_PKab0301.propGenkaKeijoYsnTCd.disabled}"
							onblur="return getYsnTNameRyak(this, event);">
						</h:inputText>
						<hx:commandExButton type="submit"
								styleClass="commandExButton_search"
								id="searchShokyakuYosanTaniCd" action="#{pc_PKab0301.doSearchShokyakuYosanTaniCdAction}">
							</hx:commandExButton>
						<h:outputText
							styleClass="outputText"
							id="htmlYsnTNameRyak"
							value="#{pc_PKab0301.propYsnTNameRyak.stringValue}">
						</h:outputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_e" align="left" width="170">
						<h:outputText
							styleClass="outputText"
							id="lblLvl"
							style="#{pc_PKab0301.propLvl.labelStyle}"
							value="#{pc_PKab0301.propLvl.labelName}">
						</h:outputText>
					</TH>
					<TD width="480">
						<h:inputText
							rendered="#{pc_PKab0301.propLvl.rendered}"
							id="htmlLvl"
							styleClass="inputText"
							readonly="#{pc_PKab0301.propLvl.readonly}"
							style="padding-right: 3px; text-align: right;#{pc_PKab0301.propLvl.style}"
							size="1"
							value="#{pc_PKab0301.propLvl.stringValue}"
							maxlength="#{pc_PKab0301.propLvl.maxLength}"
							disabled="#{pc_PKab0301.propLvl.disabled}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_e" align="left" width="170">
						<h:outputText 
							styleClass="outputText" 
							id="lblYukoMuko"
							value="#{pc_PKab0301.propYukoMuko.name}">
						</h:outputText>
					</TH>
					<TD width="480">
						<h:selectBooleanCheckbox 
							styleClass="selectBooleanCheckbox"
							id="htmlYukoFlg" 
							disabled="#{pc_PKab0301.propYukoFlg.disabled}"
							readonly="#{pc_PKab0301.propYukoFlg.readonly}"
							value="#{pc_PKab0301.propYukoFlg.checked}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblYuko"
							value="#{pc_PKab0301.propYuko.name}">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlMukoFlg"
							disabled="#{pc_PKab0301.propMukoFlg.disabled}"
							readonly="#{pc_PKab0301.propMukoFlg.readonly}"
							value="#{pc_PKab0301.propMukoFlg.checked}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" 
							id="lblMuko"
							value="#{pc_PKab0301.propMuko.name}">
						</h:outputText>
					</TD>
				</TR>
			</TABLE>
			<TABLE width="650" class="button_bar">
				<TR>
					<TD>
						<hx:commandExButton
							type="submit"
							value="検索"
							styleClass="commandExButton_dat"
							id="search"
							action="#{pc_PKab0301.doSearchAction}"
							disabled="#{pc_PKab0301.propSearch.disabled}"
							rendered="#{pc_PKab0301.propSearch.rendered}"
							style="#{pc_PKab0301.propSearch.style}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="クリア"
							styleClass="commandExButton_etc"
							id="clear"
							action="#{pc_PKab0301.doClearAction}"
							disabled="#{pc_PKab0301.propClear.disabled}"
							rendered="#{pc_PKab0301.propClear.rendered}"
							style="#{pc_PKab0301.propClear.style}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
		</TD>
	</TR>
	<TR>
		<TD>
		<HR noshade>
		</TD>
	</TR>
	<TR>
		<TD>
			<TABLE border="0" width="650">
				<TR>
					<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TR>
								<TD style="text-align:right">
									<h:outputText
										styleClass="outputText"
										id="htmlListCnt"
										value="#{pc_PKab0301.propListCount.stringValue}">
									</h:outputText>
									<h:outputText
										styleClass="outputText"
										id="lblKen"
										value="件">
									</h:outputText>
								</TD>
							</TR>
							<TR>
								<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TR>
											<TD style="text-align:left; width: 125px">
												<h:outputText
													styleClass="outputText"
													id="lblListSetchiBashoCd"
													value="設置場所コード">
												</h:outputText>
											</TD>
											<TD style="text-align:left; width: 275px">
												<h:outputText
													styleClass="outputText"
													id="lblListSetchiBashoName"
													value="設置場所名称">
												</h:outputText>
											</TD>
											<TD style="text-align:left; width: 145px">
												<h:outputText
													styleClass="outputText"
													id="lblListSetchiBashoNameRyak"
													value="設置場所略称">
												</h:outputText>
											</TD>											
											<TD style="text-align:left; width: 105px">
												<h:outputText
													styleClass="outputText"
													id="lblListLvl"
													value="レベル">
												</h:outputText>
											</TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
							<TR>
								<TD>
									<h:selectOneListbox
										styleClass="selectOneListbox"
										id="htmlSetchiBashoList"
										style="width: 650px; font-size: 9pt;"
										size="13"
										value="#{pc_PKab0301.propKazSetiList.value}"
										disabled="#{pc_PKab0301.propKazSetiList.disabled}"
										rendered="#{pc_PKab0301.propKazSetiList.rendered}"
										readonly="#{pc_PKab0301.propKazSetiList.readonly}"
										ondblclick="return func_1(this, event);">
										<f:selectItems value="#{pc_PKab0301.propKazSetiList.list}" />
									</h:selectOneListbox>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE width="100%" class="button_bar" style="margin-top: 10px">
							<TR>
								<TD>
									<hx:commandExButton
										type="button"
										value="選択"
										styleClass="commandExButton_etc"
										id="select"
										onclick="return func_1(this, event);"
										disabled="#{pc_PKab0301.propSelect.disabled}"
										rendered="#{pc_PKab0301.propSelect.rendered}"
										style="#{pc_PKab0301.propSelect.style}">
									</hx:commandExButton>
									<hx:commandExButton
										type="submit"
										value="キャンセル"
										styleClass="commandExButton_etc"
										id="cancel"
										disabled="#{pc_PKab0301.propCancel.disabled}"
										rendered="#{pc_PKab0301.propCancel.rendered}"
										style="#{pc_PKab0301.propCancel.style}"
										action="#{pc_PKab0301.doCancelAction}">
									</hx:commandExButton>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
		</TD>
	</TR>
</TABLE>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/childFooter.jsp" />
	<h:inputHidden
		value="#{pc_PKab0301.propExecutableSearch.integerValue}"
		id="htmlExecutableSearch">
	</h:inputHidden>
			<h:inputHidden id="htmlHiddenSetchiBashoCdId"
				value="#{pc_PKab0301.propHiddenSetchiBashoCdId.stringValue}">
			</h:inputHidden>
			<h:inputHidden
		value="htmlLvl=0;0"
		id="htmlFormatNumberOption">
	</h:inputHidden>
	<h:inputHidden value="#{pc_PKab0301.propNoSelectMsgHidden.stringValue}"
		id="htmlNoSelectMsgHidden">
	</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

