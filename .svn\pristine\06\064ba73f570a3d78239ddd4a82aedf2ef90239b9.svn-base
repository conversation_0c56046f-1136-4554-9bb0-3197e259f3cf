<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="COB_SOT_CGKS" name="卒業生カリキュラム学科組織" prod_id="CO" description="卒業生カリキュラム学科組織の情報です。">
<STATMENT><![CDATA[
(SELECT SOT_CGKS.NYUGAK_NENDO, SOT_CGKS.GAKKI_NO, SOT_CGKS.CUR_GAKKA_CD, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 1 THEN SOT_CGKS.CUR_GAKKA_CD WHEN 2 THEN SOT_CGKS2.CUR_GAKKA_CD WHEN 3 THEN SOT_CGKS3.CUR_GAKKA_CD WHEN 4 THEN SOT_CGKS4.CUR_GAKKA_CD WHEN 5 THEN SOT_CGKS5.CUR_GAKKA_CD WHEN 6 THEN SOT_CGKS6.CUR_GAKKA_CD END CUR_GAKKA_CD1, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 2 THEN SOT_CGKS.CUR_GAKKA_CD WHEN 3 THEN SOT_CGKS2.CUR_GAKKA_CD WHEN 4 THEN SOT_CGKS3.CUR_GAKKA_CD WHEN 5 THEN SOT_CGKS4.CUR_GAKKA_CD WHEN 6 THEN SOT_CGKS5.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD2, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 3 THEN SOT_CGKS.CUR_GAKKA_CD WHEN 4 THEN SOT_CGKS2.CUR_GAKKA_CD WHEN 5 THEN SOT_CGKS3.CUR_GAKKA_CD WHEN 6 THEN SOT_CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD3, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 4 THEN SOT_CGKS2.CUR_GAKKA_CD WHEN 5 THEN SOT_CGKS3.CUR_GAKKA_CD WHEN 6 THEN SOT_CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD4, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 5 THEN SOT_CGKS3.CUR_GAKKA_CD WHEN 6 THEN SOT_CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD5, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 6 THEN SOT_CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD6, LTRIM(COALESCE(SOT_CGKS6.CUR_GAKKA_NAME,'') || ' ' || COALESCE(SOT_CGKS5.CUR_GAKKA_NAME,'') || ' ' || COALESCE(SOT_CGKS4.CUR_GAKKA_NAME,'') || ' ' || COALESCE(SOT_CGKS3.CUR_GAKKA_NAME,'') || ' ' || COALESCE(SOT_CGKS2.CUR_GAKKA_NAME,'') || ' ' || COALESCE(SOT_CGKS.CUR_GAKKA_NAME,'')) FULL_NAME, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 1 THEN SOT_CGKS.CUR_GAKKA_NAME WHEN 2 THEN SOT_CGKS2.CUR_GAKKA_NAME WHEN 3 THEN SOT_CGKS3.CUR_GAKKA_NAME WHEN 4 THEN SOT_CGKS4.CUR_GAKKA_NAME WHEN 5 THEN SOT_CGKS5.CUR_GAKKA_NAME WHEN 6 THEN SOT_CGKS6.CUR_GAKKA_NAME END CUR_GAKKA_NAME1, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 2 THEN SOT_CGKS.CUR_GAKKA_NAME WHEN 3 THEN SOT_CGKS2.CUR_GAKKA_NAME WHEN 4 THEN SOT_CGKS3.CUR_GAKKA_NAME WHEN 5 THEN SOT_CGKS4.CUR_GAKKA_NAME WHEN 6 THEN SOT_CGKS5.CUR_GAKKA_NAME ELSE '' END CUR_GAKKA_NAME2, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 3 THEN SOT_CGKS.CUR_GAKKA_NAME WHEN 4 THEN SOT_CGKS2.CUR_GAKKA_NAME WHEN 5 THEN SOT_CGKS3.CUR_GAKKA_NAME WHEN 6 THEN SOT_CGKS4.CUR_GAKKA_NAME ELSE '' END CUR_GAKKA_NAME3, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 4 THEN SOT_CGKS.CUR_GAKKA_NAME WHEN 5 THEN SOT_CGKS2.CUR_GAKKA_NAME WHEN 6 THEN SOT_CGKS3.CUR_GAKKA_NAME ELSE '' END CUR_GAKKA_NAME4, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 5 THEN SOT_CGKS.CUR_GAKKA_NAME WHEN 6 THEN SOT_CGKS2.CUR_GAKKA_NAME ELSE '' END CUR_GAKKA_NAME5, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 6 THEN SOT_CGKS.CUR_GAKKA_NAME ELSE '' END CUR_GAKKA_NAME6, LTRIM(COALESCE(SOT_CGKS6.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(SOT_CGKS5.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(SOT_CGKS4.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(SOT_CGKS3.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(SOT_CGKS2.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(SOT_CGKS.CUR_GAKKA_NAME_RYAK,'')) FULL_NAME_RYAK, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 1 THEN SOT_CGKS.CUR_GAKKA_NAME_RYAK WHEN 2 THEN SOT_CGKS2.CUR_GAKKA_NAME_RYAK WHEN 3 THEN SOT_CGKS3.CUR_GAKKA_NAME_RYAK WHEN 4 THEN SOT_CGKS4.CUR_GAKKA_NAME_RYAK WHEN 5 THEN SOT_CGKS5.CUR_GAKKA_NAME_RYAK WHEN 6 THEN SOT_CGKS6.CUR_GAKKA_NAME_RYAK END CUR_GAKKA_NAME_RYAK1, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 2 THEN SOT_CGKS.CUR_GAKKA_NAME_RYAK WHEN 3 THEN SOT_CGKS2.CUR_GAKKA_NAME_RYAK WHEN 4 THEN SOT_CGKS3.CUR_GAKKA_NAME_RYAK WHEN 5 THEN SOT_CGKS4.CUR_GAKKA_NAME_RYAK WHEN 6 THEN SOT_CGKS5.CUR_GAKKA_NAME_RYAK ELSE '' END CUR_GAKKA_NAME_RYAK2, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 3 THEN SOT_CGKS.CUR_GAKKA_NAME_RYAK WHEN 4 THEN SOT_CGKS2.CUR_GAKKA_NAME_RYAK WHEN 5 THEN SOT_CGKS3.CUR_GAKKA_NAME_RYAK WHEN 6 THEN SOT_CGKS4.CUR_GAKKA_NAME_RYAK ELSE '' END CUR_GAKKA_NAME_RYAK3, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 4 THEN SOT_CGKS.CUR_GAKKA_NAME_RYAK WHEN 5 THEN SOT_CGKS2.CUR_GAKKA_NAME_RYAK WHEN 6 THEN SOT_CGKS3.CUR_GAKKA_NAME_RYAK ELSE '' END CUR_GAKKA_NAME_RYAK4, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 5 THEN SOT_CGKS.CUR_GAKKA_NAME_RYAK WHEN 6 THEN SOT_CGKS2.CUR_GAKKA_NAME_RYAK ELSE '' END CUR_GAKKA_NAME_RYAK5, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 6 THEN SOT_CGKS.CUR_GAKKA_NAME_RYAK ELSE '' END CUR_GAKKA_NAME_RYAK6, LTRIM(COALESCE(SOT_CGKS6.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(SOT_CGKS5.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(SOT_CGKS4.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(SOT_CGKS3.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(SOT_CGKS2.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(SOT_CGKS.CUR_GAKKA_NAME_ENG,'')) FULL_NAME_ENG, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 1 THEN SOT_CGKS.CUR_GAKKA_NAME_ENG WHEN 2 THEN SOT_CGKS2.CUR_GAKKA_NAME_ENG WHEN 3 THEN SOT_CGKS3.CUR_GAKKA_NAME_ENG WHEN 4 THEN SOT_CGKS4.CUR_GAKKA_NAME_ENG WHEN 5 THEN SOT_CGKS5.CUR_GAKKA_NAME_ENG WHEN 6 THEN SOT_CGKS6.CUR_GAKKA_NAME_ENG END CUR_GAKKA_NAME_ENG1, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 2 THEN SOT_CGKS.CUR_GAKKA_NAME_ENG WHEN 3 THEN SOT_CGKS2.CUR_GAKKA_NAME_ENG WHEN 4 THEN SOT_CGKS3.CUR_GAKKA_NAME_ENG WHEN 5 THEN SOT_CGKS4.CUR_GAKKA_NAME_ENG WHEN 6 THEN SOT_CGKS5.CUR_GAKKA_NAME_ENG ELSE '' END CUR_GAKKA_NAME_ENG2, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 3 THEN SOT_CGKS.CUR_GAKKA_NAME_ENG WHEN 4 THEN SOT_CGKS2.CUR_GAKKA_NAME_ENG WHEN 5 THEN SOT_CGKS3.CUR_GAKKA_NAME_ENG WHEN 6 THEN SOT_CGKS4.CUR_GAKKA_NAME_ENG ELSE '' END CUR_GAKKA_NAME_ENG3, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 4 THEN SOT_CGKS.CUR_GAKKA_NAME_ENG WHEN 5 THEN SOT_CGKS2.CUR_GAKKA_NAME_ENG WHEN 6 THEN SOT_CGKS3.CUR_GAKKA_NAME_ENG ELSE '' END CUR_GAKKA_NAME_ENG4, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 5 THEN SOT_CGKS.CUR_GAKKA_NAME_ENG WHEN 6 THEN SOT_CGKS2.CUR_GAKKA_NAME_ENG ELSE '' END CUR_GAKKA_NAME_ENG5, CASE SOT_CGKS.CUR_GAKKA_LVL WHEN 6 THEN SOT_CGKS.CUR_GAKKA_NAME_ENG ELSE '' END CUR_GAKKA_NAME_ENG6, SOT_CGKS.CUR_GAKKA_LVL, SOT_CGKS.HIGH_CUR_GAKKA_CD, SOT_CGKS.ROW_NO FROM COB_SOT_CGKS SOT_CGKS LEFT OUTER JOIN COB_SOT_CGKS SOT_CGKS2 ON SOT_CGKS.NYUGAK_NENDO = SOT_CGKS2.NYUGAK_NENDO AND SOT_CGKS.GAKKI_NO = SOT_CGKS2.GAKKI_NO AND SOT_CGKS.HIGH_CUR_GAKKA_CD = SOT_CGKS2.CUR_GAKKA_CD LEFT OUTER JOIN COB_SOT_CGKS SOT_CGKS3 ON SOT_CGKS2.NYUGAK_NENDO = SOT_CGKS3.NYUGAK_NENDO AND SOT_CGKS2.GAKKI_NO = SOT_CGKS3.GAKKI_NO AND SOT_CGKS2.HIGH_CUR_GAKKA_CD = SOT_CGKS3.CUR_GAKKA_CD LEFT OUTER JOIN COB_SOT_CGKS SOT_CGKS4 ON SOT_CGKS3.NYUGAK_NENDO = SOT_CGKS4.NYUGAK_NENDO AND SOT_CGKS3.GAKKI_NO = SOT_CGKS4.GAKKI_NO AND SOT_CGKS3.HIGH_CUR_GAKKA_CD = SOT_CGKS4.CUR_GAKKA_CD LEFT OUTER JOIN COB_SOT_CGKS SOT_CGKS5 ON SOT_CGKS4.NYUGAK_NENDO = SOT_CGKS5.NYUGAK_NENDO AND SOT_CGKS4.GAKKI_NO = SOT_CGKS5.GAKKI_NO AND SOT_CGKS4.HIGH_CUR_GAKKA_CD = SOT_CGKS5.CUR_GAKKA_CD LEFT OUTER JOIN COB_SOT_CGKS SOT_CGKS6 ON SOT_CGKS5.NYUGAK_NENDO = SOT_CGKS6.NYUGAK_NENDO AND SOT_CGKS5.GAKKI_NO = SOT_CGKS6.GAKKI_NO AND SOT_CGKS5.HIGH_CUR_GAKKA_CD = SOT_CGKS6.CUR_GAKKA_CD)
]]></STATMENT>
<COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="カリキュラム学科組織が配当されている入学年度が設定されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="カリキュラム学科組織が配当されている入学学期が設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="配当対象となるカリキュラム学科組織コードが設定されます。"/><COLUMN id="CUR_GAKKA_CD1" name="第１レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="最上位の学科組織コード"/><COLUMN id="CUR_GAKKA_CD2" name="第２レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第２レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD3" name="第３レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第３レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD4" name="第４レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第４レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD5" name="第５レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第５レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD6" name="第６レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第６レベルの学科組織コード"/><COLUMN id="FULL_NAME" name="カリキュラム学科組織名称" type="string" length="310" lengthDP="0" byteLength="930" description="カリキュラム学科組織の上位連結名称です。"/><COLUMN id="CUR_GAKKA_NAME1" name="第１レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル１の名称です。"/><COLUMN id="CUR_GAKKA_NAME2" name="第２レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル２の名称です。"/><COLUMN id="CUR_GAKKA_NAME3" name="第３レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル３の名称です。"/><COLUMN id="CUR_GAKKA_NAME4" name="第４レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル４の名称です。"/><COLUMN id="CUR_GAKKA_NAME5" name="第５レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル５の名称です。"/><COLUMN id="CUR_GAKKA_NAME6" name="第６レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="50" description="カリキュラム学科組織レベル６の名称です。"/><COLUMN id="FULL_NAME_RYAK" name="カリキュラム学科組織略称" type="string" length="110" lengthDP="0" byteLength="330" description="カリキュラム学科組織の上位連結略称名称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK1" name="第１レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル１の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK2" name="第２レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル２の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK3" name="第３レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル３の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK4" name="第４レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル４の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK5" name="第５レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル５の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK6" name="第６レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル６の略称です。"/><COLUMN id="FULL_NAME_ENG" name="カリキュラム学科組織英語名称" type="string" length="430" lengthDP="0" byteLength="430" description="カリキュラム学科組織の上位連結名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG1" name="第１レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル１の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG2" name="第２レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル２の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG3" name="第３レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル３の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG4" name="第４レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル４の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG5" name="第５レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル５の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG6" name="第６レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル６の名称＿英語です。"/><COLUMN id="CUR_GAKKA_LVL" name="カリキュラム学科組織レベル" type="number" length="1" lengthDP="0" byteLength="0" description="カリキュラム学科組織のレベルが設定されます。数字が小さい程、上位学科組織となります。"/><COLUMN id="HIGH_CUR_GAKKA_CD" name="上位カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="上位のカリキュラム学科組織コードが設定されます。"/><COLUMN id="ROW_NO" name="並び順ＮＯ" type="number" length="3" lengthDP="0" byteLength="0" description="カリキュラム学科組織の並び順が設定されます。"/>
</TABLE>
