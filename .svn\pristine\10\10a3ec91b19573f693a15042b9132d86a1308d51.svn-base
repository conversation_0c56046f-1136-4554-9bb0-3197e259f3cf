<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cog90001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cog90001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<style type="text/css">
<!--
 .setWidth TD {width: 110px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript">
function ysnAlChk(thisObj, thisEvent) {
	switchCheckbox('htmlCheckList11', true);
	switchCheckbox('htmlCheckList12', true);
}
function ysnAlUnchk(thisObj, thisEvent) {
	switchCheckbox('htmlCheckList11', false);
	switchCheckbox('htmlCheckList12', false);
}
function ksnAlChk(thisObj, thisEvent) {
	switchCheckbox('htmlCheckList21', true);
	switchCheckbox('htmlCheckList22', true);
}
function ksnAlUnchk(thisObj, thisEvent) {
	switchCheckbox('htmlCheckList21', false);
	switchCheckbox('htmlCheckList22', false);
}
function switchCheckbox(checkboxname, checkFlg)
{
	try{
		elms = document.getElementById('form1').elements;
		for (var i = 0; i < elms.length; i++) {
			var elm = elms[i];
	        if (elm.name == 'form1:' + checkboxname){
        		if (elm.disabled == false){
	        		elm.checked = checkFlg;
	        	}
	        }
		}
	} catch(e) {
	}
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cog90001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cog90001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Cog90001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Cog90001.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトのため全角１文字 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="800">
							<TBODY>
								<TR>
									<TH class="v_a" colspan="" width="150"><h:outputText
										styleClass="outputText" id="lblNendoMoto"
										value="#{pc_Cog90001.propNendoMoto.labelName}"
										style="#{pc_Cog90001.propNendoMoto.labelStyle}"></h:outputText></TH>
									<TD class="" width="650" colspan=""><h:inputText
										styleClass="inputText" id="htmlNendoMoto" size="5"
										tabindex="1" style="#{pc_Cog90001.propNendoMoto.style}"
										value="#{pc_Cog90001.propNendoMoto.dateValue}"
										disabled="#{pc_Cog90001.propNendoMoto.disabled}"
										readonly="#{pc_Cog90001.propNendoMoto.readonly}">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" colspan="" width="150"><h:outputText
										styleClass="outputText" id="lblNendoSaki"
										value="#{pc_Cog90001.propNendoSaki.labelName}"
										style="#{pc_Cog90001.propNendoSaki.labelStyle}"></h:outputText></TH>
									<TD class="" width="650" colspan=""><h:inputText
										styleClass="inputText" id="htmlNendoSaki" size="5"
										tabindex="2" style="#{pc_Cog90001.propNendoSaki.style}"
										value="#{pc_Cog90001.propNendoSaki.dateValue}"
										disabled="#{pc_Cog90001.propNendoSaki.disabled}"
										readonly="#{pc_Cog90001.propNendoSaki.readonly}">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_c" colspan=""><h:outputText
										styleClass="outputText" id="lblRegKbn"
										value="#{pc_Cog90001.propRegKbn.name}"
										style="#{pc_Cog90001.propRegKbn.labelStyle}"></h:outputText></TH>
									<TD class="" colspan=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRegKbn"
										layout="pageDirection" tabindex="3"
										value="#{pc_Cog90001.propRegKbn.stringValue}"
										readonly="#{pc_Cog90001.propRegKbn.readonly}"
										disabled="#{pc_Cog90001.propRegKbn.disabled}" border="0">
										<f:selectItem itemValue="1"
											itemLabel="データをコピーします。データが存在すれば更新しません。" />
										<f:selectItem itemValue="2"
											itemLabel="データをコピーします。データが存在すれば上書き更新します。" />
										<f:selectItem itemValue="3"
											itemLabel="コピー先年度のデータを削除し、データをコピーします。" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_d" colspan=""><h:outputText
										styleClass="outputText" id="lblCheckList"
										value="#{pc_Cog90001.propCheckList11.name}"
										style="#{pc_Cog90001.propCheckList11.labelStyle}"></h:outputText></TH>
									<TD class="v_e" colspan=""><TABLE border="0" class="clear_border" width="100%">
									<TBODY>
											<TR>
												<TD colspan="2">
													<h:outputText styleClass="outputText"
														id="lblComment1" value="予算確定までコピー可能"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TD width="40%">
												<DIV valign="top"><h:selectManyCheckbox
													disabledClass="selectManyCheckbox_Disabled"
													styleClass="selectManyCheckbox" id="htmlCheckList11"
													layout="pageDirection"
													value="#{pc_Cog90001.propCheckList11.value}" tabindex="4">
													<f:selectItems value="#{pc_Cog90001.propCheckList11.list}"/>
												</h:selectManyCheckbox></DIV>
												<h:outputText styleClass="outputText" id="txtDummy1" value=" 　"></h:outputText></TD>
												<TD width="40%" valign="top"><h:selectManyCheckbox
													disabledClass="selectManyCheckbox_Disabled"
													styleClass="selectManyCheckbox" id="htmlCheckList12"
													layout="pageDirection"
													value="#{pc_Cog90001.propCheckList12.value}" tabindex="5">
													<f:selectItems value="#{pc_Cog90001.propCheckList12.list}"/>
												</h:selectManyCheckbox>
												<h:outputText styleClass="outputText" id="txtDummy2" value=" 　"></h:outputText><BR>
												<h:outputText styleClass="outputText" id="txtDummy3" value=" 　"></h:outputText></TD>
											</TR>
											<TR>
												<TD align="left">
													<hx:panelBox styleClass="panelBox" id="box1">
														<hx:jspPanel id="jspPanel1">
															<hx:commandExButton type="button" 
																				value="on" 
																				styleClass="check" 
																				id="ysnCheck" 
																				onclick="return ysnAlChk(this, event);">
															</hx:commandExButton>
															<hx:commandExButton type="button" 
																				value="off" 
																				styleClass="uncheck" 
																				id="ysnUncheck" 
																				onclick="return ysnAlUnchk(this, event);">
															</hx:commandExButton>
															(全てチェックする／外す)
														</hx:jspPanel>
													</hx:panelBox>
												</TD>
											</TR>
	 										<TR>
												<TD></TD>
											</TR>
											<TR>
												<TD colspan="2">
													<h:outputText styleClass="outputText"
														id="lblComment2" value="決算確定までコピー可能"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TD width="40%">
												<DIV valign="top"><h:selectManyCheckbox
													disabledClass="selectManyCheckbox_Disabled"
													styleClass="selectManyCheckbox" id="htmlCheckList21"
													layout="pageDirection"
													value="#{pc_Cog90001.propCheckList21.value}" tabindex="5">
												<f:selectItems value="#{pc_Cog90001.propCheckList21.list}"/>
												</h:selectManyCheckbox></DIV>
												<h:outputText styleClass="outputText" id="txtDummy4" value=" 　"></h:outputText><BR>
												<h:outputText styleClass="outputText" id="txtDummy5" value=" 　"></h:outputText><BR>
												<h:outputText styleClass="outputText" id="txtDummy6" value=" 　"></h:outputText></TD>
												<TD width="40%" valign="top"><h:selectManyCheckbox
													disabledClass="selectManyCheckbox_Disabled"
													styleClass="selectManyCheckbox" id="htmlCheckList22"
													layout="pageDirection"
													value="#{pc_Cog90001.propCheckList22.value}" tabindex="7">
													<f:selectItems value="#{pc_Cog90001.propCheckList22.list}"/>
												</h:selectManyCheckbox></TD>
											</TR>
											<TR>
												<TD align="left">
													<hx:panelBox styleClass="panelBox" id="box2">
														<hx:jspPanel id="jspPanel2">
															<hx:commandExButton type="button" 
																				value="on" 
																				styleClass="check" 
																				id="ksnCheck" 
																				onclick="return ksnAlChk(this, event);">
															</hx:commandExButton>
															<hx:commandExButton type="button" 
																				value="off" 
																				styleClass="uncheck" 
																				id="ksnUncheck" 
																				onclick="return ksnAlUnchk(this, event);">
															</hx:commandExButton>
															(全てチェックする／外す)
														</hx:jspPanel>
													</hx:panelBox>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="800" class="button_bar" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton
										type="submit" value="実行" styleClass="commandExButton_dat"
										id="exec" action="#{pc_Cog90001.doExecAction}" tabindex="8"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

