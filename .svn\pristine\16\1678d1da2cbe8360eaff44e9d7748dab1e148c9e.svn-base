<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb50101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>伝票検索条件指定</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">

function onClickGetButtonId(thisObj, thisEvent) {
	document.getElementById('form1:htmlLastClickButtonId').value = thisObj.id;
}
function confirmOk() {
	var status;
	status = document.getElementById('form1:htmlExecutableStatus').value;
	if (status == "0") {
		document.getElementById('form1:htmlExecutableStatus').value = "1";
		var targetId = document.getElementById('form1:htmlLastClickButtonId').value;
		document.getElementById(targetId).click();
	}
	if (status == "0") {
		document.getElementById('form1:htmlExecutableStatus').value = "1";
		var targetId = document.getElementById('form1:htmlLastClickButtonId').value;
		document.getElementById(targetId).click();
	}
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableStatus').value = "0";
}

function func_toriSakiCdAJAX(thisObj, thisEvent) {
	// 取引先名称を取得する
	var gyosha = false;
	for (i=0; i<document.getElementsByName('form1:htmlToriSakiKbn').length; i++) {
		if (document.getElementsByName('form1:htmlToriSakiKbn')[i].value == '0') {
			gyosha = document.getElementsByName('form1:htmlToriSakiKbn')[i].checked;
		}
	}

	if (gyosha) {
	  // 業者の場合
		var servlet = "rev/co/CogToriKihonAJAX";
		var target = "form1:htmlToriSakiName";
		var code = new Array();
		code['code1'] = thisObj.value;
		code['code2'] = '0';

		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, code);
	} else {
	  // 教職員の場合
		var servlet = "rev/co/CoiJinjAJAX";
		var target = "form1:htmlToriSakiName";
		var code = new Array();
		code['code'] = thisObj.value;

		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, code);
	}
}
function copy_mokutekiTo(thisObj, thisEvent) {
	// 目的コード(from)を目的コード(to)にコピーする
	if(document.getElementById("form1:htmlShikkoMokutekiCdTo").value == ""){
			document.getElementById("form1:htmlShikkoMokutekiCdTo").value = document.getElementById("form1:htmlShikkoMokutekiCdFrom").value;
	}
}
function copy_kamokuTo(thisObj, thisEvent) {
	// 科目コード(from)を科目コード(to)にコピーする
	if(document.getElementById("form1:htmlShikkoKmkCdTo").value == ""){
			document.getElementById("form1:htmlShikkoKmkCdTo").value = document.getElementById("form1:htmlShikkoKmkCdFrom").value;
	}
}
function loadFunc() {
	func_toriSakiCdAJAX(document.getElementById('form1:htmlToriSakiCd'), '');
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="return loadFunc();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb50101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Keb50101.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb50101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb50101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err">
<LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<hx:commandExButton
		type="submit"
		value="新規登録"
		styleClass="commandExButton"
		id="register"
		action="#{pc_Keb50101.doRegisterAction}">
	</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style=""
		   class="table" 
		   width="900">
		<TBODY>
			<TR>
				<!-- 会計年度 -->
				<TH class="v_a" width="153">
					<h:outputText
						styleClass="outputText" 
						id="lblKaikeiNendo" 
						style="#{pc_Keb50101.propKaikeiNendo.labelStyle}" 
						value="#{pc_Keb50101.propKaikeiNendo.labelName}" 
						rendered="#{pc_Keb50101.propKaikeiNendo.rendered}">
					</h:outputText>
				</TH>
				<TD width="230">
					<h:inputText
						styleClass="inputText"
						id="htmlKaikeiNendo"
						size="4"
						style="#{pc_Keb50101.propKaikeiNendo.style}"
						readonly="#{pc_Keb50101.propKaikeiNendo.readonly}"
						disabled="#{pc_Keb50101.propKaikeiNendo.disabled}"
						value="#{pc_Keb50101.propKaikeiNendo.dateValue}"
						rendered="#{pc_Keb50101.propKaikeiNendo.rendered}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
					</h:inputText>
				</TD>
				<!-- 起票者ID -->
				<TH class="v_b" width="153">
					<h:outputText
						styleClass="outputText"
						id="lblKihyoshaId"
						value="#{pc_Keb50101.propKihyoshaId.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:inputText
						styleClass="inputText"
						id="htmlKihyoshaId"
						size="30"
						disabled="#{pc_Keb50101.propKihyoshaId.disabled}"
						value="#{pc_Keb50101.propKihyoshaId.stringValue}"
						style="#{pc_Keb50101.propKihyoshaId.style}"
						readonly="#{pc_Keb50101.propKihyoshaId.readonly}"
						rendered="#{pc_Keb50101.propKihyoshaId.rendered}"
						maxlength="#{pc_Keb50101.propKihyoshaId.maxLength}">
					</h:inputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style=""
		   class="table" 
		   width="900">
		<TBODY>
			<!-- 伝票番号 -->
			<TR>
				<TH class="v_b" width="153" style="border-top-style:none;">
					<h:outputText
						styleClass="outputText"
						id="lblDenpyoNo"
						value="#{pc_Keb50101.propDenpyoNo.labelName}"
						style="#{pc_Keb50101.propDenpyoNo.style}">
					</h:outputText>
				</TH>
				<TD colspan="2" style="border-top-style:none;">
					<h:inputText
						styleClass="inputText"
						id="htmlDenpyoNoFrom" size="30"
						disabled="#{pc_Keb50101.propDenpyoNoFrom.disabled}"
						maxlength="#{pc_Keb50101.propDenpyoNoFrom.maxLength}"
						readonly="#{pc_Keb50101.propDenpyoNoFrom.readonly}"
						rendered="#{pc_Keb50101.propDenpyoNoFrom.rendered}"
						style="#{pc_Keb50101.propDenpyoNoFrom.style}"
						value="#{pc_Keb50101.propDenpyoNoFrom.stringValue}">
					</h:inputText>　～　<h:inputText
						styleClass="inputText"
						id="htmlDenpyoNoTo" size="30"
						disabled="#{pc_Keb50101.propDenpyoNoTo.disabled}"
						maxlength="#{pc_Keb50101.propDenpyoNoTo.maxLength}"
						readonly="#{pc_Keb50101.propDenpyoNoTo.readonly}"
						rendered="#{pc_Keb50101.propDenpyoNoTo.rendered}"
						style="#{pc_Keb50101.propDenpyoNoTo.style}"
						value="#{pc_Keb50101.propDenpyoNoTo.stringValue}">
					</h:inputText>
				</TD>
			</TR>
			<!-- 基準日付 -->
			<TR>
				<TH class="v_e">
					<h:outputText
						styleClass="outputText"
						id="lblKijunDate"
						value="基準日付">
					</h:outputText>
				</TH>
				<TD colspan="2">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
						<TR>
							<TD width="150">
								<h:selectOneRadio
									id="htmlHizukeKbn"
									styleClass="selectOneRadio"
									rendered="#{pc_Keb50101.propHizukeKbn.rendered}"
									readonly="#{pc_Keb50101.propHizukeKbn.readonly}"
									style="#{pc_Keb50101.propHizukeKbn.style}"
									disabledClass="selectOneRadio_Disabled"
									disabled="#{pc_Keb50101.propHizukeKbn.disabled}"
									value="#{pc_Keb50101.propHizukeKbn.value}">
									<f:selectItem itemValue="0" itemLabel="出納日付" />
									<f:selectItem itemValue="1" itemLabel="起票日付" />
								</h:selectOneRadio>
							</TD>
							<TD>
								<h:inputText
									styleClass="inputText"
									id="htmlKijunDateFrom"
									size="12"
									disabled="#{pc_Keb50101.propKijunDateFrom.disabled}"
									readonly="#{pc_Keb50101.propKijunDateFrom.readonly}"
									rendered="#{pc_Keb50101.propKijunDateFrom.rendered}"
									style="#{pc_Keb50101.propKijunDateFrom.style}"
									value="#{pc_Keb50101.propKijunDateFrom.dateValue}">
									<f:convertDateTime />
									<hx:inputHelperDatePicker />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
								</h:inputText>　～　<h:inputText
									styleClass="inputText"
									id="htmlKijunDateTo"
									size="12"
									disabled="#{pc_Keb50101.propKijunDateTo.disabled}"
									readonly="#{pc_Keb50101.propKijunDateTo.readonly}"
									rendered="#{pc_Keb50101.propKijunDateTo.rendered}"
									style="#{pc_Keb50101.propKijunDateTo.style}"
									value="#{pc_Keb50101.propKijunDateTo.dateValue}">
									<f:convertDateTime />
									<hx:inputHelperDatePicker />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
								</h:inputText>
							</TD>
						</TR>
					</TABLE>
				</TD>
			</TR>
			<!-- 起票予算単位 -->
			<TR>
				<TH class="v_e">
					<h:outputText styleClass="outputText" id="lblKihyoYosanTaniCd"
							style="#{pc_Keb50101.propKihyoYosanTaniCd.labelStyle}"
							value="#{pc_Keb50101.propKihyoYosanTaniCd.labelName}">
						</h:outputText>
				</TH>
				<TD colspan="2">
					<h:inputText styleClass="inputText" id="htmlKihyoYosanTaniCd"
							size="12"
							disabled="#{pc_Keb50101.propKihyoYosanTaniCd.disabled}"
							maxlength="#{pc_Keb50101.propKihyoYosanTaniCd.maxLength}"
							readonly="#{pc_Keb50101.propKihyoYosanTaniCd.readonly}"
							rendered="#{pc_Keb50101.propKihyoYosanTaniCd.rendered}"
							style="#{pc_Keb50101.propKihyoYosanTaniCd.style}"
							value="#{pc_Keb50101.propKihyoYosanTaniCd.stringValue}">
					</h:inputText>
					<hx:commandExButton
						type="submit"
						styleClass="commandExButton_search"
						id="kihyoYosanTaniSearch"
						action="#{pc_Keb50101.doKihyoYosanTaniSearchAction}">
					</hx:commandExButton>
				</TD>
			</TR>

			<!-- 執行予算単位 -->
			<TR>
				<TH class="v_e">
					<h:outputText styleClass="outputText" id="lblShikkoYosanTaniCd"
							style="#{pc_Keb50101.propShikkoYosanTaniCd.labelStyle}"
							value="#{pc_Keb50101.propShikkoYosanTaniCd.labelName}">
						</h:outputText>
				</TH>
				<TD colspan="2">
					<h:inputText styleClass="inputText" id="htmlShikkoYosanTaniCd"
							size="12"
							disabled="#{pc_Keb50101.propShikkoYosanTaniCd.disabled}"
							maxlength="#{pc_Keb50101.propShikkoYosanTaniCd.maxLength}"
							readonly="#{pc_Keb50101.propShikkoYosanTaniCd.readonly}"
							rendered="#{pc_Keb50101.propShikkoYosanTaniCd.rendered}"
							style="#{pc_Keb50101.propShikkoYosanTaniCd.style}"
							value="#{pc_Keb50101.propShikkoYosanTaniCd.stringValue}">
					</h:inputText>
					<hx:commandExButton
						type="submit"
						styleClass="commandExButton_search"
						id="shikoYosanTaniSearch"
						action="#{pc_Keb50101.doShikoYosanTaniSearchAction}">
					</hx:commandExButton>
				</TD>
			</TR>
			<!-- 執行目的 -->
			<TR style="${pc_Keb50101.trMokutekiStyle};">
				<TH class="v_e">
					<h:outputText styleClass="outputText" id="lblShikkoMokutekiCd"
							style="#{pc_Keb50101.propShikkoMokutekiCd.labelStyle}"
							value="#{pc_Keb50101.propShikkoMokutekiCd.labelName}">
						</h:outputText>
				</TH>
				<TD colspan="2">
					<h:inputText styleClass="inputText" id="htmlShikkoMokutekiCdFrom"
							size="12"
							maxlength="#{pc_Keb50101.propShikkoMokutekiCdFrom.maxLength}"
							disabled="#{pc_Keb50101.propShikkoMokutekiCdFrom.disabled}"
							readonly="#{pc_Keb50101.propShikkoMokutekiCdFrom.readonly}"
							rendered="#{pc_Keb50101.propShikkoMokutekiCdFrom.rendered}"
							style="#{pc_Keb50101.propShikkoMokutekiCdFrom.style}"
							value="#{pc_Keb50101.propShikkoMokutekiCdFrom.stringValue}"
							onblur="copy_mokutekiTo(this, event);">
						</h:inputText>
					<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="mokuSearchFrom"
							action="#{pc_Keb50101.doMokuSearchFromAction}">
						</hx:commandExButton>　～　<h:inputText styleClass="inputText"
							id="htmlShikkoMokutekiCdTo" size="12"
							maxlength="#{pc_Keb50101.propShikkoMokutekiCdTo.maxLength}"
							disabled="#{pc_Keb50101.propShikkoMokutekiCdTo.disabled}"
							readonly="#{pc_Keb50101.propShikkoMokutekiCdTo.readonly}"
							rendered="#{pc_Keb50101.propShikkoMokutekiCdTo.rendered}"
							style="#{pc_Keb50101.propShikkoMokutekiCdTo.style}"
							value="#{pc_Keb50101.propShikkoMokutekiCdTo.stringValue}">
						</h:inputText>
					<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="mokuSearchTo"
							action="#{pc_Keb50101.doMokuSearchToAction}">
						</hx:commandExButton>
				</TD>
			</TR>
			<!-- 執行科目 -->
			<TR>
				<TH class="v_e">
					<h:outputText
						styleClass="outputText"
						id="lblShikkoKmkCd"
						style="#{pc_Keb50101.propShikkoKmkCd.labelStyle}"
						value="#{pc_Keb50101.propShikkoKmkCd.labelName}">
					</h:outputText>
				</TH>
				<TD colspan="2">
					<h:inputText
						styleClass="inputText"
						id="htmlShikkoKmkCdFrom"
						size="12"
						maxlength="#{pc_Keb50101.propShikkoKmkCdFrom.maxLength}"
						disabled="#{pc_Keb50101.propShikkoKmkCdFrom.disabled}"
						readonly="#{pc_Keb50101.propShikkoKmkCdFrom.readonly}"
						rendered="#{pc_Keb50101.propShikkoKmkCdFrom.rendered}"
						style="#{pc_Keb50101.propShikkoKmkCdFrom.style}"
						value="#{pc_Keb50101.propShikkoKmkCdFrom.stringValue}"
						onblur="copy_kamokuTo(this, event);">
					</h:inputText>
					<hx:commandExButton
						type="submit"
						styleClass="commandExButton_search"
						id="kmkSearchFrom"
						action="#{pc_Keb50101.doKmkSearchFromAction}">
					</hx:commandExButton>　～　<h:inputText
						styleClass="inputText"
						id="htmlShikkoKmkCdTo"
						size="12"
						maxlength="#{pc_Keb50101.propShikkoKmkCdTo.maxLength}"
						disabled="#{pc_Keb50101.propShikkoKmkCdTo.disabled}"
						readonly="#{pc_Keb50101.propShikkoKmkCdTo.readonly}"
						rendered="#{pc_Keb50101.propShikkoKmkCdTo.rendered}"
						style="#{pc_Keb50101.propShikkoKmkCdTo.style}"
						value="#{pc_Keb50101.propShikkoKmkCdTo.stringValue}">
					</h:inputText>
					<hx:commandExButton
						type="submit"
						styleClass="commandExButton_search"
						id="kmkSearchTo"
						action="#{pc_Keb50101.doKmkSearchToAction}">
					</hx:commandExButton>
				</TD>
			</TR>
			<!-- 執行額 -->
			<TR>
				<TH class="v_e">
					<h:outputText
						styleClass="outputText"
						id="lblShikkoGaku"
						style="#{pc_Keb50101.propShikkoGaku.labelStyle}"
						value="#{pc_Keb50101.propShikkoGaku.labelName}">
					</h:outputText>
				</TH>
				<TD colspan="2">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
						<TR>
							<TD width="150">
								<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlShikkoGakuTani"
									disabled="#{pc_Keb50101.propShikkoGakuTani.disabled}"
									readonly="#{pc_Keb50101.propShikkoGakuTani.readonly}"
									rendered="#{pc_Keb50101.propShikkoGakuTani.rendered}"
									style="#{pc_Keb50101.propShikkoGakuTani.style}"
									value="#{pc_Keb50101.propShikkoGakuTani.stringValue}">
									<f:selectItem itemValue="0" itemLabel="明細金額" />
									<f:selectItem itemValue="1" itemLabel="伝票金額" />
								</h:selectOneRadio>							</TD>
							<TD>
								<h:inputText
									id="htmlShikkoGakuFrom"
									styleClass="inputText"
									size="15"
									disabled="#{pc_Keb50101.propShikkoGakuFrom.disabled}"
									readonly="#{pc_Keb50101.propShikkoGakuFrom.readonly}"
									rendered="#{pc_Keb50101.propShikkoGakuFrom.rendered}"
									style="padding-right: 3px; text-align: right; #{pc_Keb50101.propShikkoGakuFrom.style}"
									value="#{pc_Keb50101.propShikkoGakuFrom.stringValue}">
									<hx:inputHelperAssist errorClass="inputText_Error" />
								</h:inputText>　～　<h:inputText
									id="htmlShikkoGakuTo"
									styleClass="inputText"
									size="15"
									disabled="#{pc_Keb50101.propShikkoGakuTo.disabled}"
									readonly="#{pc_Keb50101.propShikkoGakuTo.readonly}"
									rendered="#{pc_Keb50101.propShikkoGakuTo.rendered}"
									style="padding-right: 3px; text-align: right; #{pc_Keb50101.propShikkoGakuTo.style}"
									value="#{pc_Keb50101.propShikkoGakuTo.stringValue}">
									<hx:inputHelperAssist errorClass="inputText_Error" />
								</h:inputText>
							</TD>
						</TR>
					</TABLE>
				</TD>
			</TR>
			<!-- 取引先 -->
			<TR>
				<TH class="v_e">
					<h:outputText
						styleClass="outputText"
						id="lblToriSaki"
						value="#{pc_Keb50101.propToriSakiCd.labelName}">
					</h:outputText>
				</TH>
				<TD style="border-right-style:none;" width="650px" nowrap>
					<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
						<TR>
							<TD width="150">
								<h:selectOneRadio
									id="htmlToriSakiKbn"
									styleClass="selectOneRadio"
									rendered="#{pc_Keb50101.propToriSakiKbn.rendered}"
									readonly="#{pc_Keb50101.propToriSakiKbn.readonly}"
									style="#{pc_Keb50101.propToriSakiKbn.style}"
									disabledClass="selectOneRadio_Disabled"
									disabled="#{pc_Keb50101.propToriSakiKbn.disabled}"
									value="#{pc_Keb50101.propToriSakiKbn.value}">
									<f:selectItem itemValue="0" itemLabel="業者" />
									<f:selectItem itemValue="1" itemLabel="教職員" />
								</h:selectOneRadio>
							</TD>
							<TD width="500">
							<DIV style="width:500px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText
									styleClass="inputText"
									id="htmlToriSakiCd"
									size="20"
									disabled="#{pc_Keb50101.propToriSakiCd.disabled}"
									maxlength="#{pc_Keb50101.propToriSakiCd.maxLength}"
									readonly="#{pc_Keb50101.propToriSakiCd.readonly}"
									rendered="#{pc_Keb50101.propToriSakiCd.rendered}"
									style="#{pc_Keb50101.propToriSakiCd.style}"
									value="#{pc_Keb50101.propToriSakiCd.stringValue}"
									onblur="return func_toriSakiCdAJAX(this, event);">
								</h:inputText><hx:commandExButton
									type="submit"
									styleClass="commandExButton_search"
									id="toriSakiSearch"
									action="#{pc_Keb50101.doToriSakiSearchAction}">
								</hx:commandExButton>
								<h:outputText
									styleClass="outputText"
									id="htmlToriSakiName">
								</h:outputText>
							</DIV>
							</TD>
						</TR>
					</TABLE>
				</TD>
				<TD style="border-left-style:none;">
					<h:outputText
						id="text3"
						styleClass="outputText"
						value="（完全一致）">
					</h:outputText>
				</TD>
			</TR>
			<!-- 摘要 -->
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText"
						id="lblTekiyoName"
						value="#{pc_Keb50101.propTekiyoName.labelName}">
					</h:outputText>
				</TH>
				<TD style="border-right-style:none;" width="650">
					<h:inputText
						styleClass="inputText"
						id="htmlTekiyoName"
						size="60"
						disabled="#{pc_Keb50101.propTekiyoName.disabled}"
						maxlength="#{pc_Keb50101.propTekiyoName.maxLength}"
						readonly="#{pc_Keb50101.propTekiyoName.readonly}"
						rendered="#{pc_Keb50101.propTekiyoName.rendered}"
						style="#{pc_Keb50101.propTekiyoName.style}"
						value="#{pc_Keb50101.propTekiyoName.stringValue}">
					</h:inputText>
				</TD>
				<TD style="border-left-style:none;" width="88">
					<h:outputText
						id="text2"
						styleClass="outputText"
						value="（部分一致）">
					</h:outputText>
				</TD>
			</TR>
			<!-- 研究課題 -->
			<TR style="${pc_Keb50101.trKnkyuKadaiStyle};">
				<TH class="v_a">
					<h:outputText
						styleClass="outputText"
						id="lblKenkyukadai"
						value="研究課題">
					</h:outputText>
				</TH>
				<TD colspan="2" width="738px">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
						<TBODY>
						<TR>
						<TD width="190">
						<h:inputHidden
							id="htmlKenkyuKadaiKbnHidden"
							value="#{pc_Keb50101.propHiddenKenkyuKadaiKbn.stringValue}">
						</h:inputHidden>
						<h:inputText styleClass="inputText" id="htmlKenkyuKadaiHakkoNendo"
							size="4"
							value="#{pc_Keb50101.propKenkyuKadaiHakkoNendo.dateValue}"
							style="#{pc_Keb50101.propKenkyuKadaiHakkoNendo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText>
						<h:inputText styleClass="inputText" id="htmlKenkyuKadaiNo"
							size="#{pc_Keb50101.propKenkyuKadaiNo.maxLength}"
							maxlength="#{pc_Keb50101.propKenkyuKadaiNo.maxLength}"
							style="#{pc_Keb50101.propKenkyuKadaiNo.style}"
							value="#{pc_Keb50101.propKenkyuKadaiNo.stringValue}">
						</h:inputText>
						<hx:commandExButton
							type="submit"
							styleClass="commandExButton_search"
							id="kenkyuKadaiSearch"
							action="#{pc_Keb50101.doKenkyuKadaiSearchAction}">
						</hx:commandExButton>
						</TD>
						<TD width="548">
						<h:selectManyCheckbox
							disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlKenkyuKadaiKbn"
							value="#{pc_Keb50101.propKenkyuKadaiKbn.stringValue}"
							style="#{pc_Keb50101.propKenkyuKadaiKbn.style}">
							<f:selectItems
								value="#{pc_Keb50101.propKenkyuKadaiKbn.list}" />
						</h:selectManyCheckbox>
						<!-- 画面項目自動表示処理用隠しボタン -->
						<DIV style="display:none;">
							<hx:commandExButton
								type="submit"
								styleClass="commandExButton"
								id="autoDisp"
								value="自動表示"
								action="#{pc_Keb50101.doAutoDispAction}">
							</hx:commandExButton>
						</DIV>
						</TD>
						</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
			<!-- 伝票状態 -->
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText"
						id="lblDenpyoJotai"
						style="#{pc_Keb50101.propDenpyoJotai.labelStyle}"
						value="#{pc_Keb50101.propDenpyoJotai.labelName}">
					</h:outputText>
				</TH>
				<TD colspan="2">
					<h:selectManyCheckbox
						disabledClass="selectManyCheckbox_Disabled"
						styleClass="selectManyCheckbox" id="htmlDenpyoJotai"
						value="#{pc_Keb50101.propDenpyoJotai.stringValue}"
						style="#{pc_Keb50101.propDenpyoJotai.style}">
						<f:selectItems
							value="#{pc_Keb50101.propDenpyoJotai.list}" />
					</h:selectManyCheckbox>
				</TD>
			</TR>
			<!-- 伝票種類 -->
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText"
						id="lblDenpyoShurui"
						style="#{pc_Keb50101.propDenpyoShurui.labelStyle}"
						value="伝票種類">
					</h:outputText>
				</TH>
				<TD colspan="2">
					<h:selectManyCheckbox
						disabledClass="selectManyCheckbox_Disabled"
						styleClass="selectManyCheckbox" id="htmlDenpyoShurui"
						value="#{pc_Keb50101.propDenpyoShurui.stringValue}"
						style="#{pc_Keb50101.propDenpyoShurui.style}">
						<f:selectItems
							value="#{pc_Keb50101.propDenpyoShurui.list}" />
					</h:selectManyCheckbox>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="850"
		   style="margin-top:10px;" 
		   class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton
						type="submit" 
						value="検索" 
						styleClass="commandExButton_dat" 
						id="search" 
						action="#{pc_Keb50101.doSearchAction}" 
						disabled="#{pc_Keb50101.propSearch.disabled}" 
						rendered="#{pc_Keb50101.propSearch.rendered}" 
						style="#{pc_Keb50101.propSearch.style}"
						onclick="return onClickGetButtonId(this, event);">
					</hx:commandExButton>
					<hx:commandExButton
						type="submit" 
						value="クリア" 
						styleClass="commandExButton_etc" 
						id="clear" 
						action="#{pc_Keb50101.doClearAction}" 
						style="#{pc_Keb50101.propClear.style}" 
						disabled="#{pc_Keb50101.propClear.disabled}" 
						rendered="#{pc_Keb50101.propClear.rendered}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="900">
		<TBODY>
			<TR>
				<TD>
					<TABLE width="100%">
						<TR>
							<TD align="left">
								<h:outputText
									styleClass="outputText"
									id="htmlNendoStr"
									style="font-size: 8pt"
									value="#{pc_Keb50101.propNendoStr.stringValue}">
								</h:outputText>
							</TD>
							<TD align="right">
								<h:outputText
									styleClass="outputText"
									id="htmlListCount"
									style="font-size: 8pt"
									value="#{pc_Keb50101.propDenpyoList.listCount}">
									<f:convertNumber pattern="#,##0" />
								</h:outputText>
								<h:outputText
									styleClass="outputText" 
									id="text1" 
									value="件">
								</h:outputText>
							</TD>
						</TR>
					</TABLE>
				</TD>
			</TR>
			<TR>
				<TD valign="top" height="260">
					<DIV style="height: 260px;overflow-y:scroll" 
						 id="listScroll" 
						 onscroll="setScrollPosition('scroll',this);" 
						 class="listScroll">
						<h:dataTable
							columnClasses="columnClass1"
							headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Keb50101.propDenpyoList.rowClasses}" 
							styleClass="meisai_scroll"
							id="htmlDenpyoList"
							var="varlist"
							border="0"
							cellpadding="2"
							cellspacing="0"
							width="880"
							value="#{pc_Keb50101.propDenpyoList.list}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="伝票番号"
										id="lblColumn1">
									</h:outputText>
								</f:facet>
								<h:outputText
									id="htmlColumn1"
									styleClass="outputText"
									value="#{varlist.denpyoNo}">
								</h:outputText>
								<f:attribute value="150px" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="#{pc_Keb50101.denJtiLbl}"
										id="lblColumn2">
									</h:outputText>
								</f:facet>
								<h:outputText
									id="htmlColumn2"
									styleClass="outputText"
									value="#{varlist.denpyoJotai}">
								</h:outputText>
								<f:attribute value="#{pc_Keb50101.denJtiWidth}" name="width" />
							</h:column>
							<h:column id="column9"  rendered="#{pc_Keb50101.revban}">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="調"
										id="lblColumn9">
									</h:outputText>
								</f:facet>
								<h:outputText
									id="htmlColumn9"
									styleClass="outputText"
									value="#{varlist.chotat}"
									title="#{varlist.chotNo}">
								</h:outputText>
								<f:attribute value="20px" name="width" />
							</h:column>
							<h:column id="column10">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="伝票種類"
										id="lblColumn10">
									</h:outputText>
								</f:facet>
								<h:outputText
									id="htmlColumn10"
									styleClass="outputText"
									value="#{varlist.denpyoShurui}">
								</h:outputText>
								<f:attribute value="70px" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="起票日付"
										id="lblColumn3">
									</h:outputText>
								</f:facet>
								<h:outputText
									id="htmlColumn3"
									styleClass="outputText"
									value="#{varlist.kihyoDate}">
								</h:outputText>
								<f:attribute value="70px" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="出納日付"
										id="lblColumn4">
									</h:outputText>
								</f:facet>
								<h:outputText
									id="htmlColumn4"
									styleClass="outputText"
									value="#{varlist.suitoDate}">
								</h:outputText>
								<f:attribute value="70px" name="width" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="摘要"
										id="lblColumn5">
									</h:outputText>
								</f:facet>
							<hx:jspPanel id="jspPanel1">
								<DIV style="width:265px;white-space:nowrap;overflow:hidden;display:block;">
								<h:outputText
									id="htmlColumn5"
									value="#{varlist.tekiyoName}"
									title="#{varlist.tekiyoName}">
								</h:outputText>
								</DIV>
							</hx:jspPanel>
							<f:attribute value="265px" name="width" />
							<f:attribute value="true" name="nowrap" /></h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText
										styleClass="outputText"
										value="伝票金額"
										id="lblColumn6">
									</h:outputText>
								</f:facet>
								<h:outputText
									id="htmlColumn6"
									styleClass="outputText"
									style="white-space:nowrap;"
									value="#{varlist.denpyoKingaku}">
									<f:convertNumber pattern="###,###,###,##0" />
								</h:outputText>
								<f:attribute value="112px" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton
									id="edit"
									type="submit"
									value="編集"
									styleClass="commandExButton"
									disabled="#{varlist.colEditButton.disabled}"
									action="#{pc_Keb50101.doEditAction}"
									onclick="return onClickGetButtonId(this, event);">
								</hx:commandExButton>
								<f:attribute value="38" name="width" />
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton
									id="copy"
									type="submit"
									value="ｺﾋﾟｰ"
									styleClass="commandExButton"
									disabled="#{varlist.colCopyButton.disabled}"
									action="#{pc_Keb50101.doCopyAction}">
								</hx:commandExButton>
								<f:attribute value="36" name="width" />
							</h:column>
						</h:dataTable>
					</DIV>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden
	id="htmlExecutableStatus"
	value="#{pc_Keb50101.propExecutableStatus.integerValue}">
</h:inputHidden>
<h:inputHidden
	id="htmlLastClickButtonId"
	value="#{pc_Keb50101.propLastClickButtonId.stringValue}">
</h:inputHidden>
<h:inputHidden
	id="htmlFormatNumberOption"
	value="htmlShikkoGakuFrom=###,###,###,##0;###,###,###,##0 | htmlShikkoGakuTo=###,###,###,##0;###,###,###,##0">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

