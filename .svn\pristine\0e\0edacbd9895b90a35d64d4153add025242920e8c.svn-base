<%--
  卒業生学科組織設定（基本情報）

  <AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coe00201T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Coe00201T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT type="text/javascript">

// 所属学科組織コード変更時の処理
function onChangeSzkGakkaCd(id) {
	indirectClick(id);
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// 更新時のクリック処理
function onClickUpdate(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// 削除時のクリック処理
function onClickDelete(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}
// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {

}

window.attachEvent('onload', endload);
  function endload() {
  changeScrollPosition('scroll','listScroll');
}
</SCRIPT></HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Coe00201T01.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Coe00201T01">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Coe00201T01.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Coe00201T01.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Coe00201T01.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、全角スペースを配置-->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
  <TABLE border="0" cellpadding="5">
    <TBODY>
      <TR>
        <TD width="870">
        <TABLE class="table" width="100%">
          <TBODY>
            <TR align="center" valign="middle">
              <!-- 入学年度 -->
              <TH nowrap class="v_a" width="150">
                <h:outputText
                  id="lblNyugakNendo"
                  styleClass="outputText"
                  value="#{pc_Coe00201T01.coe00201.propNyugakNendo.labelName}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakNendo.labelStyle}">
                </h:outputText>
              </TH>
              <TD width="200">
                <h:inputText
                  id="htmlNyugakNendo"
                  styleClass="inputText"
                  size="4"
                  disabled="#{pc_Coe00201T01.coe00201.propNyugakNendo.disabled}"
                  value="#{pc_Coe00201T01.coe00201.propNyugakNendo.dateValue}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakNendo.style}">
            	  <hx:inputHelperAssist errorClass="inputText_Error"
					imeMode="inactive" promptCharacter="_" />
				  <f:convertDateTime pattern="yyyy" />
                </h:inputText>
              </TD>
              <!-- 入学学期ＮＯ -->
              <TH nowrap class="v_b" width="150">
                <h:outputText
                  id="lblNyugakGakkiNo"
                  styleClass="outputText"
                  value="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.labelName}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.labelStyle}">
                </h:outputText>
              </TH>
              <TD width="200">
                <h:inputText
                  id="htmlNyugakGakkiNo"
                  styleClass="inputText"
                  size="2"
                  maxlength="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.maxLength}"
                  disabled="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.disabled}"
                  value="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.integerValue}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.style}">
				  <hx:inputHelperAssist errorClass="inputText_Error"
					imeMode="inactive" promptCharacter="_" />
				  <f:convertNumber type="number" pattern="#0"/>
                </h:inputText>
              </TD>
              <TD style="background-color: transparent; text-align: right" class="clear_border">
                <!-- 選択ボタン -->
                <hx:commandExButton
                  type="submit"
                  id="select"
                  styleClass="commandExButton"
                  value="選　択"
                  disabled="#{pc_Coe00201T01.coe00201.propSelect.disabled}"
                  action="#{pc_Coe00201T01.doSelectAction}">
                </hx:commandExButton>
                <!-- 解除ボタン -->
                <hx:commandExButton
                  type="submit"
                  id="unselect"
                  styleClass="commandExButton"
                  value="解　除"
                  disabled="#{pc_Coe00201T01.coe00201.propUnSelect.disabled}"
                  action="#{pc_Coe00201T01.doUnselectAction}">
                </hx:commandExButton>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
		<!-- ↓レコードカウントのテーブル -->
		<TABLE border="0" align="center" width="870">
			<TBODY>
				<TR>
					<TD align="right" nowrap class="outputText" width="100%">
						<h:outputText
							styleClass="outputText"
							id="lblSotGakkaSskListCnt" 
							value="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.listCount}">
						</h:outputText>件
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		<!-- ↓データテーブルのテーブル -->
		<TABLE border="0" width="870">
		  <TBODY>
			<TR>
			  <TD>
				<DIV class="listScroll" id="listScroll" style="height:130px;" onscroll="setScrollPosition('scroll',this);">
				  <h:dataTable
					border="0" cellpadding="0" cellspacing="0"
					columnClasses="columnClass"
					headerClass="headerClass"
					footerClass="footerClass" 
					rowClasses="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.rowClasses}"
					styleClass="meisai_scroll" id="htmlSotGakkaSskList"
					value="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.list}" var="varlist">
					<h:column id="column1">
						<f:facet name="header">
							<h:outputText styleClass="outputText" id="lblszkGakkaCd_head"
								value="コード"></h:outputText>
						</f:facet>
						<h:outputText styleClass="outputText" id="lblszkGakkaCd_list"
							value="#{varlist.szkGakkaCd}"></h:outputText>
						<f:attribute value="100" name="width" />
					</h:column>
					<h:column id="column2">
						<f:facet name="header">
							<h:outputText styleClass="outputText" id="lblszkGakkaName_head"
								value="所属学科組織名称"></h:outputText>
						</f:facet>
						<h:outputText styleClass="outputText" id="lblszkGakkaName_list"
							value="#{varlist.szkGakkaName}"></h:outputText>
						<f:attribute value="710" name="width" />
					</h:column>
					<h:column id="column3">
						<f:facet name="header">
						</f:facet>
						<hx:commandExButton
							type="submit"value="選択"
							styleClass="commandExButton"
							id="button_select"
							action="#{pc_Coe00201T01.doSelectList}">
						</hx:commandExButton>
						<f:attribute value="40" name="width" />
					</h:column>
				  </h:dataTable>
				</DIV>
			  </TD>
			</TR>
		  </TBODY>
		</TABLE>
		<!-- データテーブルと入力フォームの間の余白 -->
		<DIV class="linespace" id="linespace" style="height:10px;"></DIV>
        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TBODY>
            <TR align="left">
              <TD>
              <TABLE border="0" cellpadding="0" cellspacing="0"
                style="border-bottom-style: none; ">
                <TBODY>
                  <TR>
                    <TD class="tab_head_on" width="100">
                      <hx:commandExButton
                        type="button"
                        id="tabCoe00201T01"
                        styleClass="tab_head_on"
                        value="#{pc_Coe00201T01.coe00201.propTabNameKihon.stringValue}"
                        style="width: 100%">
                      </hx:commandExButton>
                    </TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="submit"
                        id="tabCoe00201T02"
                        styleClass="tab_head_off"
                        value="#{pc_Coe00201T01.coe00201.propTabNameAddr.stringValue}"
                        action="#{pc_Coe00201T01.doTabCoe00201T02Action}"
                        style="width:100%">
                      </hx:commandExButton></TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="submit"
                        id="tabCoe00201T03"
                        styleClass="tab_head_off"
                        value="#{pc_Coe00201T01.coe00201.propTabNameDaihyo.stringValue}"
                        action="#{pc_Coe00201T01.doTabCoe00201T03Action}"
                        style="width:100%">
                      </hx:commandExButton>
                    </TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="submit"
                        id="tabCoe00201T04"
                        styleClass="tab_head_off"
                        value="#{pc_Coe00201T01.coe00201.propTabNameMeisai.stringValue}"
                        action="#{pc_Coe00201T01.doTabCoe00201T04Action}"
                        style="width:100%">
                      </hx:commandExButton>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
            <TR>
              <TD>
              <TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" width="100%">
                <TBODY>
                  <TR>
                    <TD width="100%">
                    <div style="height: 318px">
                    <TABLE class="table" width="853">
                      <TBODY>
                        <TR style="height:10px;">
                        </TR>
                        <TR>
                          <!-- 所属学科組織コード -->
                          <TH nowrap class="v_a" width="200">
                            <h:outputText
                              id="lblSzkGakkaCd"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaCd.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaCd.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD width="94">
                            <h:inputText
                              id="htmlSzkGakkaCd"
                              styleClass="inputText"
                              size="8"
                              onchange="return onChangeSzkGakkaCd('linkSzkGakkaCd');"
                              maxlength="#{pc_Coe00201T01.propSzkGakkaCd.maxLength}"
                              disabled="#{pc_Coe00201T01.propSzkGakkaCd.disabled}"
                              value="#{pc_Coe00201T01.propSzkGakkaCd.stringValue}"
                              style="#{pc_Coe00201T01.propSzkGakkaCd.style}">
                            </h:inputText>
                            <h:commandLink
                              styleClass="commandLink"
                              id="linkSzkGakkaCd"
                              tabindex="-1"
                              action="#{pc_Coe00201T01.doLinkSzkGakkaCdAction}">
                            </h:commandLink>
                          </TD>
                          <!-- 所属学科組織レベル -->
                          <TH nowrap class="v_b" width="194" colspan="2">
                            <h:outputText
                              id="lblSzkGakkaLvl"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaLvl.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaLvl.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD width="60">
			                <h:inputText
			                  id="htmlSzkGakkaLvl"
							  tabindex="-1"
							  styleClass="likeOutput"
							  size="2"
			                  disabled="true"
			                  value="#{pc_Coe00201T01.propSzkGakkaLvl.stringValue}">
			                </h:inputText>
                          </TD>
                          <!-- 上位所属学科組織コード -->
                          <TH nowrap class="v_c" width="215">
                            <h:outputText
                              id="lblHighSzkGakkaCd"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propHighSzkGakkaCd.labelName}"
                              style="#{pc_Coe00201T01.propHighSzkGakkaCd.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD width="90">
                            <h:inputText
                              id="htmlHighSzkGakkaCd"
                              tabindex="-1"
                              styleClass="likeOutput"
                              size="6"
			                  disabled="true"
                              value="#{pc_Coe00201T01.propHighSzkGakkaCd.stringValue}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 所属学科組織名称 -->
                          <TH nowrap class="v_d">
                            <h:outputText
                              id="lblSgksMeiName"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSgksMeiName.labelName}"
                              style="#{pc_Coe00201T01.propSgksMeiName.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:inputText
                              id="htmlSgksMeiName"
                              styleClass="inputText"
                              size="60"
                              maxlength="#{pc_Coe00201T01.propSgksMeiName.maxLength}"
                              disabled="#{pc_Coe00201T01.propSgksMeiName.disabled}"
                              value="#{pc_Coe00201T01.propSgksMeiName.stringValue}"
                              style="#{pc_Coe00201T01.propSgksMeiName.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 所属学科組織略称 -->
                          <TH nowrap class="v_e">
                            <h:outputText
                              id="lblSgksMeiNameRyak"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSgksMeiNameRyak.labelName}"
                              style="#{pc_Coe00201T01.propSgksMeiNameRyak.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:inputText
                              id="htmlSgksMeiNameRyak"
                              styleClass="inputText"
                              size="20"
                              maxlength="#{pc_Coe00201T01.propSgksMeiNameRyak.maxLength}"
                              disabled="#{pc_Coe00201T01.propSgksMeiNameRyak.disabled}"
                              value="#{pc_Coe00201T01.propSgksMeiNameRyak.stringValue}"
                              style="#{pc_Coe00201T01.propSgksMeiNameRyak.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 所属学科組織英語 -->
                          <TH nowrap class="v_f">
                            <h:outputText
                              id="lblSgksMeiNameEng"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSgksMeiNameEng.labelName}"
                              style="#{pc_Coe00201T01.propSgksMeiNameEng.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:inputText
                              id="htmlSgksMeiNameEng"
                              styleClass="inputText"
                              size="90"
                              maxlength="#{pc_Coe00201T01.propSgksMeiNameEng.maxLength}"
                              disabled="#{pc_Coe00201T01.propSgksMeiNameEng.disabled}"
                              value="#{pc_Coe00201T01.propSgksMeiNameEng.stringValue}"
                              style="#{pc_Coe00201T01.propSgksMeiNameEng.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 電話番号１ -->
                          <TH nowrap class="v_g">
                            <h:outputText
                              id="lblTel1"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propTel1.labelName}"
                              style="#{pc_Coe00201T01.propTel1.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="2">
                            <h:inputText
                              id="htmlTel1"
                              styleClass="inputText"
                              size="30"
                              maxlength="#{pc_Coe00201T01.propTel1.maxLength}"
                              disabled="#{pc_Coe00201T01.propTel1.disabled}"
                              value="#{pc_Coe00201T01.propTel1.stringValue}"
                              style="#{pc_Coe00201T01.propTel1.style}">
                            </h:inputText>
                          </TD>
                          <!-- 電話番号２ -->
                          <TH nowrap class="v_h" colspan="2">
                            <h:outputText
                              id="lblTel2"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propTel2.labelName}"
                              style="#{pc_Coe00201T01.propTel2.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="2">
                            <h:inputText
                              id="htmlTel2"
                              styleClass="inputText"
                              size="30"
                              maxlength="#{pc_Coe00201T01.propTel2.maxLength}"
                              disabled="#{pc_Coe00201T01.propTel2.disabled}"
                              value="#{pc_Coe00201T01.propTel2.stringValue}"
                              style="#{pc_Coe00201T01.propTel2.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- FAX番号 -->
                          <TH nowrap class="v_i">
                            <h:outputText
                              id="lblFax"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propFax.labelName}"
                              style="#{pc_Coe00201T01.propFax.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:inputText
                              id="htmlFax"
                              styleClass="inputText"
                              size="30"
                              maxlength="#{pc_Coe00201T01.propFax.maxLength}"
                              disabled="#{pc_Coe00201T01.propFax.disabled}"
                              value="#{pc_Coe00201T01.propFax.stringValue}"
                              style="#{pc_Coe00201T01.propFax.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- E_MAIL -->
                          <TH nowrap class="v_j">
                            <h:outputText
                              id="lblEMail"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propEMail.labelName}"
                              style="#{pc_Coe00201T01.propEMail.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:inputText
                              id="htmlEMail"
                              styleClass="inputText"
                              size="70"
                              maxlength="#{pc_Coe00201T01.propEMail.maxLength}"
                              disabled="#{pc_Coe00201T01.propEMail.disabled}"
                              value="#{pc_Coe00201T01.propEMail.stringValue}"
                              style="#{pc_Coe00201T01.propEMail.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 大学院課程区分 -->
                          <TH nowrap class="v_l">
	                       <h:outputText
	                          id="lblDaigakuinKateiKbn"
	                          styleClass="outputText"
	                          value="#{pc_Coe00201T01.propDaigakuinKateiKbn.labelName}"
	                          style="#{pc_Coe00201T01.propDaigakuinKateiKbn.labelStyle}">
	                       </h:outputText>
	                      </TH>
	                      <TD colspan="2">
	                        <h:selectOneMenu
	                          id="htmlDaigakuinKateiKbn"
	                          styleClass="selectOneMenu"
	                          value="#{pc_Coe00201T01.propDaigakuinKateiKbn.value}"
	                          disabled="#{pc_Coe00201T01.propDaigakuinKateiKbn.disabled}">
	                          <f:selectItems value="#{pc_Coe00201T01.propDaigakuinKateiKbn.list}" />
	                        </h:selectOneMenu>
	                       </TD>
	                       <!-- 昼夜間区分 -->
                          <TH nowrap class="v_m" colspan="2">
	                       <h:outputText
	                          id="lblChuyakanKbn"
	                          styleClass="outputText"
	                          value="#{pc_Coe00201T01.propChuyakanKbn.labelName}"
	                          style="#{pc_Coe00201T01.propChuyakanKbn.labelStyle}">
	                       </h:outputText>
	                      </TH>
	                      <TD colspan="2">
	                        <h:selectOneMenu
	                          id="htmlChuyakanKbn"
	                          styleClass="selectOneMenu"
	                          value="#{pc_Coe00201T01.propChuyakanKbn.value}"
	                          disabled="#{pc_Coe00201T01.propChuyakanKbn.disabled}">
	                          <f:selectItems value="#{pc_Coe00201T01.propChuyakanKbn.list}" />
	                        </h:selectOneMenu>
	                       </TD>
                        </TR>
                        <TR>
                          <!-- 学校 -->
                          <TH nowrap class="v_k">
                            <h:outputText
                              id="lblGakkoCombo"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propGakkoCombo.labelName}"
                              style="#{pc_Coe00201T01.propGakkoCombo.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:selectOneMenu
                              id="htmlGakkoCombo"
                              styleClass="selectOneMenu"
                              value="#{pc_Coe00201T01.propGakkoCombo.value}"
                              disabled="#{pc_Coe00201T01.propGakkoCombo.disabled}"
                              style="width:455px;">
                              <f:selectItems value="#{pc_Coe00201T01.propGakkoCombo.list}" />
                            </h:selectOneMenu>
                          </TD>
                        </TR>
                       <TR>
                          <!-- 所属学科組織分類マスタ -->
                          <TH nowrap class="v_n">
                            <h:outputText
                              id="lblSzkGakkaBunruiMaster"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaBunruiMaster.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaBunruiMaster.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:selectOneMenu
                              id="htmlSzkGakkaBunruiMaster"
                              styleClass="selectOneMenu"
                              value="#{pc_Coe00201T01.propSzkGakkaBunruiMaster.value}"
                              disabled="#{pc_Coe00201T01.propSzkGakkaBunruiMaster.disabled}"
                              style="width:455px;">
                              <f:selectItems value="#{pc_Coe00201T01.propSzkGakkaBunruiMaster.list}" />
                            </h:selectOneMenu>
					    	<hx:commandExButton
			    				type="submit"value="選択"
			    				styleClass="commandExButton"
			    				id="sgks_bunrui_select"
		    					action="#{pc_Coe00201T01.doSelectSzkGakkaBunrui}">
		    				</hx:commandExButton>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 所属学科組織分類コード -->
                          <TH nowrap class="v_n">
                            <h:outputText
                              id="lblSzkGakkaBunruiCd"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaBunruiCd.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaBunruiCd.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD>
                            <h:inputText
                              id="htmlSzkGakkaBunruiCd"
                              styleClass="inputText"
                              size="8"
                              maxlength="#{pc_Coe00201T01.propSzkGakkaBunruiCd.maxLength}"
                              disabled="#{pc_Coe00201T01.propSzkGakkaBunruiCd.disabled}"
                              value="#{pc_Coe00201T01.propSzkGakkaBunruiCd.stringValue}"
                              style="#{pc_Coe00201T01.propSzkGakkaBunruiCd.style}">
                            </h:inputText>
                          </TD>
                          <!-- 所属学科組織分類名称 -->
                          <TH nowrap class="v_o" colspan="2">
                            <h:outputText
                              id="lblSzkGakkaBunruiName"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaBunruiName.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaBunruiName.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlSzkGakkaBunruiName"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T01.propSzkGakkaBunruiName.maxLength}"
                              disabled="#{pc_Coe00201T01.propSzkGakkaBunruiName.disabled}"
                              value="#{pc_Coe00201T01.propSzkGakkaBunruiName.stringValue}"
                              style="#{pc_Coe00201T01.propSzkGakkaBunruiName.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 所属学科組織系統分類マスタ -->
                          <TH nowrap class="v_p">
                            <h:outputText
                              id="lblSzkGakkaKeitoBunruiMaster"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiMaster.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiMaster.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:selectOneMenu
                              id="htmlSzkGakkaKeitoBunruiMaster"
                              styleClass="selectOneMenu"
                              value="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiMaster.value}"
                              disabled="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiMaster.disabled}"
                              style="width:455px;">
                              <f:selectItems value="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiMaster.list}" />
                            </h:selectOneMenu>
					    	<hx:commandExButton
			    				type="submit" value="選択"
			    				styleClass="commandExButton"
			    				id="sgks_keito_bunrui_select"
		    					action="#{pc_Coe00201T01.doSelectSzkGakkaKeitoBunrui}">
		    				</hx:commandExButton>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 所属学科組織系統分類コード -->
                          <TH nowrap class="v_q">
                            <h:outputText
                              id="lblSzkGakkaKeitoBunruiCd"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiCd.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiCd.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD>
                            <h:inputText
                              id="htmlSzkGakkaKeitoBunruiCd"
                              styleClass="inputText"
                              size="8"
                              maxlength="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiCd.maxLength}"
                              disabled="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiCd.disabled}"
                              value="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiCd.stringValue}"
                              style="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiCd.style}">
                            </h:inputText>
                          </TD>
                          <!-- 所属学科組織系統分類名称 -->
                          <TH nowrap class="v_r" colspan="2">
                            <h:outputText
                              id="lblSzkGakkaKeitoBunruiName"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiName.labelName}"
                              style="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiName.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlSzkGakkaKeitoBunruiName"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiName.maxLength}"
                              disabled="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiName.disabled}"
                              value="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiName.stringValue}"
                              style="#{pc_Coe00201T01.propSzkGakkaKeitoBunruiName.style}">
                            </h:inputText>
                          </TD>
                         </TR>
                         <TR>
                          <!-- 学校課程種別区分 -->
                          <TH nowrap class="v_s">
                            <h:outputText
                              id="lblGakkoKateiSbtKbn"
                              styleClass="outputText"
                              value="#{pc_Coe00201T01.propGakkoKateiSbtKbn.labelName}"
                              style="#{pc_Coe00201T01.propGakkoKateiSbtKbn.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="6">
                            <h:selectOneMenu
                              id="htmlGakkoKateiSbtKbn"
                              styleClass="selectOneMenu"
                              value="#{pc_Coe00201T01.propGakkoKateiSbtKbn.value}"
                              disabled="#{pc_Coe00201T01.propGakkoKateiSbtKbn.disabled}">
                              <f:selectItems value="#{pc_Coe00201T01.propGakkoKateiSbtKbn.list}" />
                            </h:selectOneMenu>
                          </TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    </div>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
          <TBODY>
            <TR align="right">
              <TD align="center">
                <hx:commandExButton
                  type="submit"
                  id="update"
                  styleClass="commandExButton_dat"
                  value="確定"
                  disabled="#{pc_Coe00201T01.coe00201.propUpdate.disabled}"
                  onclick="return onClickUpdate('#{msg.SY_MSG_0001W}');"
                  action="#{pc_Coe00201T01.doUpdateAction}">
                </hx:commandExButton>
                <hx:commandExButton
                  type="submit"
                  id="delete"
                  styleClass="commandExButton_dat"
                  value="削除"
                  disabled="#{pc_Coe00201T01.coe00201.propDelete.disabled}"
                  onclick="return onClickDelete('#{msg.SY_MSG_0004W}');"
                  action="#{pc_Coe00201T01.doDeleteAction}">
                </hx:commandExButton>
                <hx:commandExButton
                  type="submit"
                  id="clear" styleClass="commandExButton_etc"
                  value="クリア"
                  disabled="#{pc_Coe00201T01.coe00201.propClear.disabled}"
                  action="#{pc_Coe00201T01.doClearAction}">
                </hx:commandExButton>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        </TD>
      </TR>
    </TBODY>
  </TABLE>
  <h:inputHidden id="htmlHidButtonKbn" value="#{pc_Coe00201T01.coe00201.propHidButtonKbn.integerValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidAction" value="#{pc_Coe00201T01.coe00201.propHidAction.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidInsUpdKbn" value="#{pc_Coe00201T01.coe00201.propHidInsUpdKbn.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidErrMessage" value="#{pc_Coe00201T01.coe00201.propHidErrMessage.value}"></h:inputHidden>
  <h:inputHidden id="scroll" value="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.scrollPosition}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
