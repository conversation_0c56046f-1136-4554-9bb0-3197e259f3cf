<%--
  発行機代理発行予約状況詳細画面

  <AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/hk/PHkd0401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE></TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_PHkd0401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_PHkd0401.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_PHkd0401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_PHkd0401.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<!--↓content↓-->
			<DIV class="head_button_area">　<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" width="650">
				<TBODY>
					<TR>
						<TD>
							<TABLE border="0" width="100%" class="table">
							<TBODY>
								<TR>
									<!-- 予約番号 -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblYoyakuNo"
										value="#{pc_PHkd0401.propYoyakuNo.labelName}"
										style="#{pc_PHkd0401.propYoyakuNo.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:outputText styleClass="outputText"
										id="htmlYoyakuNo"
										value="#{pc_PHkd0401.propYoyakuNo.value}"
										style="#{pc_PHkd0401.propYoyakuNo.style}"></h:outputText></TD>
								</TR>
								<TR>
									<!-- 予約日時 -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblYoyakuDate"
										value="#{pc_PHkd0401.propYoyakuDate.labelName}"
										style="#{pc_PHkd0401.propYoyakuDate.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText styleClass="outputText"
										id="htmlYoyakuDate"
										value="#{pc_PHkd0401.propYoyakuDate.value}"
										style="#{pc_PHkd0401.propYoyakuDate.style}"></h:outputText></TD>
									<!-- 発行日時 -->
									<TH class="v_a" width="120"><h:outputText
										styleClass="outputText" id="lblHakoDate"
										value="#{pc_PHkd0401.propHakoDate.labelName}"
										style="#{pc_PHkd0401.propHakoDate.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:outputText styleClass="outputText"
										id="htmlHakoDate"
										value="#{pc_PHkd0401.propHakoDate.value}"
										style="#{pc_PHkd0401.propHakoDate.style}"></h:outputText></TD>
								</TR>
								<TR>
									<!-- ユーザＩＤ -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblUserId"
										value="#{pc_PHkd0401.propUserId.labelName}"
										style="#{pc_PHkd0401.propUserId.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText styleClass="outputText"
										id="htmlUserId"
										value="#{pc_PHkd0401.propUserId.value}"
										style="#{pc_PHkd0401.propUserId.style}"></h:outputText></TD>
									<!-- ユーザ名称 -->
									<TH class="v_a" width="120"><h:outputText
										styleClass="outputText" id="lblUserName"
										value="#{pc_PHkd0401.propUserName.labelName}"
										style="#{pc_PHkd0401.propUserName.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:outputText styleClass="outputText"
										id="htmlUserName"
										value="#{pc_PHkd0401.propUserName.value}"
										style="#{pc_PHkd0401.propUserName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<!-- 人事番号 -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblJinjiCd"
										value="#{pc_PHkd0401.propJinjiCd.labelName}"
										style="#{pc_PHkd0401.propJinjiCd.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText styleClass="outputText"
										id="htmlJinjiCd"
										value="#{pc_PHkd0401.propJinjiCd.value}"
										style="#{pc_PHkd0401.propJinjiCd.style}"></h:outputText></TD>
									<!-- 人事名称 -->
									<TH class="v_a" width="120"><h:outputText
										styleClass="outputText" id="lblJinjiName"
										value="#{pc_PHkd0401.propJinjiName.labelName}"
										style="#{pc_PHkd0401.propJinjiName.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:outputText styleClass="outputText"
										id="htmlJinjiName"
										value="#{pc_PHkd0401.propJinjiName.value}"
										style="#{pc_PHkd0401.propJinjiName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<!-- 発行機ＩＤ -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblHakokiId"
										value="#{pc_PHkd0401.propHakokiId.labelName}"
										style="#{pc_PHkd0401.propHakokiId.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText styleClass="outputText"
										id="htmlHakokiId"
										value="#{pc_PHkd0401.propHakokiId.value}"
										style="#{pc_PHkd0401.propHakokiId.style}"></h:outputText></TD>
									<!-- 発行機名称 -->
									<TH class="v_a" width="120"><h:outputText
										styleClass="outputText" id="lblHakokiNm"
										value="#{pc_PHkd0401.propHakokiNm.labelName}"
										style="#{pc_PHkd0401.propHakokiNm.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:outputText styleClass="outputText"
										id="htmlHakokiNm"
										value="#{pc_PHkd0401.propHakokiNm.value}"
										style="#{pc_PHkd0401.propHakokiNm.style}"></h:outputText></TD>
								</TR>
								<TR>
									<!-- 卒業年度 -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblSotugyoNendo"
										value="#{pc_PHkd0401.propSotugyoNendo.labelName}"
										style="#{pc_PHkd0401.propSotugyoNendo.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText styleClass="outputText"
										id="htmlSotugyoNendo"
										value="#{pc_PHkd0401.propSotugyoNendo.value}"
										style="#{pc_PHkd0401.propSotugyoNendo.style}"></h:outputText></TD>
									<!-- 卒業学期名称 -->
									<TH class="v_a" width="120"><h:outputText
										styleClass="outputText" id="lblSotugyoGakkiName"
										value="#{pc_PHkd0401.propSotugyoGakkiName.labelName}"
										style="#{pc_PHkd0401.propSotugyoGakkiName.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:outputText styleClass="outputText"
										id="htmlSotugyoGakkiName"
										value="#{pc_PHkd0401.propSotugyoGakkiName.value}"
										style="#{pc_PHkd0401.propSotugyoGakkiName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<!-- 学籍番号 -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblGakusekiCd"
										value="#{pc_PHkd0401.propGakusekiCd.labelName}"
										style="#{pc_PHkd0401.propGakusekiCd.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText styleClass="outputText"
										id="htmlGakusekiCd"
										value="#{pc_PHkd0401.propGakusekiCd.value}"
										style="#{pc_PHkd0401.propGakusekiCd.style}"></h:outputText></TD>
									<!-- 学生氏名 -->
									<TH class="v_a" width="120"><h:outputText
										styleClass="outputText" id="lblGakusekiName"
										value="#{pc_PHkd0401.propGakusekiName.labelName}"
										style="#{pc_PHkd0401.propGakusekiName.labelStyle}"></h:outputText></TH>
									<TD width="230"><h:outputText styleClass="outputText"
										id="htmlGakusekiName"
										value="#{pc_PHkd0401.propGakusekiName.value}"
										style="#{pc_PHkd0401.propGakusekiName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<!-- 確認番号 -->
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblConfNo"
										value="#{pc_PHkd0401.propConfNo.labelName}"
										style="#{pc_PHkd0401.propConfNo.labelStyle}"
										rendered="#{pc_PHkd0401.propConfNo.rendered}"></h:outputText></TH>
									<TD colspan="3"><h:outputText styleClass="outputText"
										id="htmlConfNo" style="#{pc_PHkd0401.propConfNo.style}"
										value="#{pc_PHkd0401.propConfNo.value}"
										rendered="#{pc_PHkd0401.propConfNo.rendered}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<br>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="800">
					<%-- 件数 --%>
					<TR>
						<TD align="right" valign="bottom" nowrap class="outputText">
							<h:outputText styleClass="outputText" id="lblSearchListCnt" value="#{pc_PHkd0401.propOutSyomList.listCount}">
							</h:outputText>件
						</TD>
					</TR>
			</TABLE>
			<%-- 出力証明書一覧のテーブル --%>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="800" style="margin-top:-1px;">
					<TR>
						<TD style="border-top-style:none;border-left-style:none;border-bottom-style:none">
							<div class="listScroll" id="listScroll" style="height: 245px"
								onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
								border="1" cellpadding="2" cellspacing="0"
								columnClasses="columnClass1" headerClass="headerClass"
								footerClass="footerClass"
								rowClasses="#{pc_PHkd0401.propOutSyomList.rowClasses}"
								styleClass="meisai_scroll" id="htmlOutSyomList"
								value="#{pc_PHkd0401.propOutSyomList.list}" var="varlist">
									<%-- 証明書 --%>
									<h:column id="column1">
										<f:facet name="header">
											<h:outputText id="lblSyom_head" styleClass="outputText" value="#{pc_PHkd0401.propSyom.labelName}"
												style="#{pc_PHkd0401.propSyom.labelStyle}"></h:outputText>
										</f:facet>
										<f:attribute value="160" name="width" />
										<h:outputText styleClass="outputText" id="lblSyom_list"
											value="#{varlist.syom}"></h:outputText>
									</h:column>
									<%-- 文面 --%>
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText id="lblBunmen_head" styleClass="outputText" value="#{pc_PHkd0401.propBunmen.labelName}"
												style="#{pc_PHkd0401.propBunmen.labelStyle}"></h:outputText>
										</f:facet>
										<f:attribute value="320" name="width" />
										<h:outputText styleClass="outputText" id="lblBunmenKbnName_list"
											value="#{varlist.bunmenKbnName}"></h:outputText>
										<h:outputText styleClass="outputText" id="lblBunmenEda_list"
											value="#{varlist.bunmenEda}"></h:outputText>
										<h:outputText value="：" rendered="#{varlist.bunmenEda != null}" ></h:outputText>
										<h:outputText styleClass="outputText" id="lblTitle_list"
											value="#{varlist.title}"></h:outputText>
									</h:column>
									<%-- 使用目的 --%>
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText id="lblSyomMokuteki_head" styleClass="outputText" value="#{pc_PHkd0401.propSyomMokuteki.labelName}"
												style="#{pc_PHkd0401.propSyomMokuteki.labelStyle}"></h:outputText>
										</f:facet>
										<f:attribute value="240" name="width" />
										<h:outputText styleClass="outputText" id="lblCode_list"
											value="#{varlist.code}"></h:outputText>
										<h:outputText value="：" rendered="#{varlist.code != null}" ></h:outputText>
										<h:outputText styleClass="outputText" id="lblSyomMokuteki_list"
											value="#{varlist.syomMokuteki}"></h:outputText>
									</h:column>
									<%-- 部数 --%>
									<h:column id="column4">
										<f:facet name="header">
											<h:outputText id="lblBusu_head" styleClass="outputText" value="#{pc_PHkd0401.propBusu.labelName}"
												 style="#{pc_PHkd0401.propBusu.labelStyle}"></h:outputText>
										</f:facet>
										<f:attribute value="80" name="width" />
										<h:outputText styleClass="outputText" id="lblBusu_list"
											value="#{varlist.busu}"></h:outputText>
										<f:attribute value="text-align: right; vertical-align: middle" name="style" />
									</h:column>
								</h:dataTable>
							</div>
						</TD>
					</TR>
			</TABLE>
			<TABLE border="0" width="800">
					<TR>
						<TD>
							<TABLE border="0" class="button_bar" width="800">
								<TBODY>
									<TR>
										<!-- ＯＫボタン -->
										<TD align="center" width="800"><hx:commandExButton type="submit" value="ＯＫ"
											styleClass="commandExButton_dat" id="ok"
											action="#{pc_PHkd0401.doCloseDispAction}"></hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/childFooter.jsp" />
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

