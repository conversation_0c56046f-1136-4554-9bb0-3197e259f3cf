<%-- 
	出身地設定
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coz00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Coz00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

	function confirmOk() {
			document.getElementById('form1:propExecutableDecision').value = 1;
			indirectClick('decision');
	}
	
	function confirmCancel() {

		return;
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Coz00301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる" styleClass="commandExButton" id="closeDisp"
	action="#{pc_Coz00301.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Coz00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Coz00301.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false"></h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
　
</DIV>
<DIV id="content">			
<DIV class="column" align="center">		
			
<!-- ↓ここにコンポーネントを配置 -->

<TABLE border="0" cellpadding="5">
	<TBODY>
		<TR>
			<TD>
				<DIV>
				<TABLE border="0" cellpadding="0" width="680">
					<TBODY>
						<TR>
							<TD>
							<DIV align="right">
								<h:outputText styleClass="outputText" id="sum" value="#{pc_Coz00301.propSstList.listCount}"></h:outputText>
								<h:outputText styleClass="outputText" id="lblKen" value="件"></h:outputText>
							</DIV>
							</TD>
						</TR>
						<TR>
							<TD>
									<DIV class="listScroll" id="listScroll" style="height: 403px"
										onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
										border="0" cellpadding="2" cellspacing="0"
										headerClass="headerClass" footerClass="footerClass"
										rowClasses="#{pc_Coz00301.propSstList.rowClasses}"
										styleClass="meisai_scroll" id="htmlSstList"
										value="#{pc_Coz00301.propSstList.list}" var="varlist"
										width="680" columnClasses="columnClass1">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="textCode" styleClass="outputText"
													value="出身地コード"></h:outputText>
											</f:facet>
											<f:attribute value="80" name="width" />
											<h:outputText id="lblSstListCd" value="#{varlist.sstCd}"
												style="#{pc_Coz00301.propSstList.style}"
												styleClass="outputText"></h:outputText>
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="出身地名称"
													id="textName"></h:outputText>
											</f:facet>
											<f:attribute value="150" name="width" />
											<h:outputText styleClass="outputText" id="lblSstListNm"
												value="#{varlist.sstNm}"
												style="#{pc_Coz00301.propSstList.style}"></h:outputText>
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="出身地名称英語"
													id="textNameEn"></h:outputText>
											</f:facet>
											<f:attribute value="420" name="width" />
											<h:outputText styleClass="outputText" id="lblSstListNmEn"
												value="#{varlist.sstNmEn}"
												style="#{pc_Coz00301.propSstList.style}"></h:outputText>
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="30" name="width" />
											<hx:commandExButton type="submit" value="選択"
												styleClass="commandExButton" id="selectButton"
												action="#{pc_Coz00301.doSelectAction}"></hx:commandExButton>
										</h:column>
									</h:dataTable></DIV>
									</TD>
						</TR>
					</TBODY>
				</TABLE>
				</DIV>
			</TD>
		</TR>
		<TR>
			<TD height="20"></TD>
		</TR>
		<TR>
			<TD>
				<DIV align="left">
				<TABLE width="680" cellspacing="0" cellpadding="0" border="0" class="table">
					<TR>
						<TH nowrap width="180" class="v_a">
							<h:outputText styleClass="outputText" id="lblSstCd" style="#{pc_Coz00301.propSstCd.labelStyle}" 
								value="#{pc_Coz00301.propSstCd.labelName}"></h:outputText>
						</TH>
						<TD colspan="4" nowrap width="500">
							<h:inputText styleClass="inputText" style="#{pc_Coz00301.propSstCd.style}" 
								id="htmlSstCd" value="#{pc_Coz00301.propSstCd.stringValue}" size="6" 
								maxlength="#{pc_Coz00301.propSstCd.maxLength}"></h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_b">
							<h:outputText styleClass="outputText" id="lblSstNm" style="#{pc_Coz00301.propSstNm.labelStyle}" 
								value="#{pc_Coz00301.propSstNm.labelName}"></h:outputText>
						</TH>
						<TD colspan="4" nowrap >
							<h:inputText styleClass="inputText" style="#{pc_Coz00301.propSstNm.style}" id="htmlSstNm" 
								value="#{pc_Coz00301.propSstNm.stringValue}" size="20"
								maxlength="#{pc_Coz00301.propSstNm.maxLength}"></h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_c">
							<h:outputText styleClass="outputText" id="lblSstNmEn" style="#{pc_Coz00301.propSstNmEn.labelStyle}" 
							value="#{pc_Coz00301.propSstNmEn.labelName}"></h:outputText>
						</TH>
						<TD colspan="4" nowrap>
							<h:inputText styleClass="inputText" id="htmlSstNmEn" value="#{pc_Coz00301.propSstNmEn.stringValue}" 
							size="61" style='#{pc_Coz00301.propSstNmEn.style}' maxlength="#{pc_Coz00301.propSstNmEn.maxLength}" ></h:inputText>
						</TD>
					</TR>
				</TABLE>
				</DIV>
			</TD>
		</TR>
		<TR>
			<TD>
				<DIV align="left">
				<TABLE class="button_bar" width="680" cellspacing="1" cellpadding="1">
					<TBODY>
						<TR>
							<TD align="center"><hx:commandExButton type="submit" value="確定" styleClass="commandExButton_dat" 
								id="decision" action="#{pc_Coz00301.doDecisionAction}"></hx:commandExButton>
								<hx:commandExButton type="submit" 
								value="削除" styleClass="commandExButton_dat" id="delete" action="#{pc_Coz00301.doDeleteAction}" 
								confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton>
								<hx:commandExButton type="submit" value="クリア" 
								styleClass="commandExButton_etc" id="clear" action="#{pc_Coz00301.doClearAction}"></hx:commandExButton>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</DIV>
			</TD>
		</TR>
	</TBODY>
</TABLE>
<h:inputHidden id="htmlHidScroll" value="#{pc_Coz00301.propSstList	.scrollPosition}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden value="#{pc_Coz00301.propExecutableDecision.integerValue}" id="propExecutableDecision"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>

</HTML>