<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssd01701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssd01701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssd01701.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssd01701.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssd01701.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssd01701.screenName}"></h:outputText>
</div>			

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton	type="submit" value="新規登録" 
	styleClass="commandExButton" id="register" 
	action="#{pc_Ssd01701.doRegisterAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE width="100%" border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD>
				<TABLE class="table" width="540">
					<TBODY>
						<TR>
							<TH nowrap class="v_a" width="120">
							<!-- 文面区分 -->
								<h:outputText styleClass="outputText" id="lblBunmenKbnOut"
								value="#{pc_Ssd01701.propBunmenKbnName.labelName}"></h:outputText></TH>
							<TD><h:outputText styleClass="outputText" id="lblBunmenKbn" 
								value="#{pc_Ssd01701.propBunmenKbnName.stringValue}"></h:outputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<BR>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="540">
					<TBODY>
						<TR>
							<TD align="right" nowrap class="outputText" width="100%"><h:outputText
								styleClass="outputText" id="lblBunmenEdabanCnt" value="#{pc_Ssd01701.propBunmenEdabanlist.listCount}"></h:outputText>件</TD>
						</TR>
						<TR>
							<TD>
							<div class="listScroll" id="listScroll" style="height: 338px" onscroll="setScrollPosition('htmlHidScroll', this);">
								<h:dataTable border="1" cellpadding="2" cellspacing="0"
									headerClass="headerClass" footerClass="footerClass"
									columnClasses="columnClass1"
									rowClasses="#{pc_Ssd01701.propBunmenEdabanlist.rowClasses}"
									styleClass="meisai_scroll" id="htmlBunmenEdabanList" var="varlist"
									value="#{pc_Ssd01701.propBunmenEdabanlist.list}">
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText id="lblEdaban_head" styleClass="outputText"
											value="枝番"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText"
										id="lblEdaban_list"
										value="#{varlist.bunmenEdaban}"></h:outputText>
									<f:attribute value="40" name="width" />
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="text-align: right" name="style" />
								</h:column>
								<h:column id="column2">
									<f:facet name="header">
										<h:outputText styleClass="outputText" id="lblTitle_head"
											value="タイトル"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="lblTitle_list"
										value="#{varlist.title}"></h:outputText>
									<f:attribute value="400" name="width" />
								</h:column>
								<h:column id="column6">
									<f:facet name="header">
									</f:facet>
									<hx:commandExButton type="submit" value="編集"
										styleClass="commandExButton" id="edit"
										style="width: 45px;"
										disabled="#{varlist.editButton.disabled}"
										action="#{pc_Ssd01701.doEditAction}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="コピー"
										styleClass="commandExButton" id="button_copy"
										style="width: 45px;"
										action="#{pc_Ssd01701.doCopyAction}"></hx:commandExButton>
									<f:attribute value="90" name="width" />
								</h:column>
							</h:dataTable><BR>
							</div>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<h:inputHidden id="htmlHidScroll" value="#{pc_Ssd01701.propBunmenEdabanlist.scrollPosition}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>

</HTML>
