<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00109.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<f:subview id="Xrx00109">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrx00109.onPageLoadBegin}">
		<%-- ↓ コンテンツ部 ↓ --%>
		<hx:jspPanel>
			<DIV class="column" align="center" style="width:870px">
			<TABLE class="table" border="0" cellpadding="5" width="870">
				<TBODY>
					<!-- 学生氏名カナ -->
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblShimeiNameKana"
							value="#{pc_Xrx00109.propShimeiNameKana.labelName}"
							style="#{pc_Xrx00109.propShimeiNameKana.labelStyle}"></h:outputText></TH>
						<TD width="280"><h:outputText styleClass="outputText"
							id="htmlShimeiNameKana"
							value="#{pc_Xrx00109.propShimeiNameKana.stringValue}"
							style="#{pc_Xrx00109.propShimeiNameKana.style}"></h:outputText></TD>
						<!-- 性別 -->
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblSeibetu" value="#{pc_Xrx00109.propSeibetu.labelName}"
							style="#{pc_Xrx00109.propSeibetu.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlSeibetu"
							value="#{pc_Xrx00109.propSeibetu.stringValue}"
							style="#{pc_Xrx00109.propSeibetu.style}"></h:outputText></TD>
						<%-- 学年 --%>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblGakunen" value="#{pc_Xrx00109.propGakunen.labelName}"
							style="#{pc_Xrx00109.propGakunen.labelStyle}"></h:outputText></TH>
						<TD width="280"><h:outputText styleClass="outputText"
							id="htmlGakunen" value="#{pc_Xrx00109.propGakunen.stringValue}"
							style="#{pc_Xrx00109.propGakunen.style}"></h:outputText></TD>
						<!-- 在籍期限 -->
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblLimitzaiseki"
							value="#{pc_Xrx00109.propLimitzaiseki.labelName}"
							style="#{pc_Xrx00109.propLimitzaiseki.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlLimitzaiseki"
							value="#{pc_Xrx00109.propLimitzaiseki.stringValue}"
							style="#{pc_Xrx00109.propLimitzaiseki.style}"></h:outputText></TD>
						<%-- 学費期限 --%>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblLimitkigen"
							value="#{pc_Xrx00109.propLimitkigen.labelName}"
							style="#{pc_Xrx00109.propLimitkigen.labelStyle}"></h:outputText></TH>
						<TD width="280"><h:outputText styleClass="outputText"
							id="htmlLimitkigen"
							value="#{pc_Xrx00109.propLimitkigen.stringValue}"
							style="#{pc_Xrx00109.propLimitkigen.style}"></h:outputText></TD>

						<!-- 異動出学状態 -->
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblIdo" value="#{pc_Xrx00109.propIdo.labelName}"
							style="#{pc_Xrx00109.propIdo.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlIdo"
							value="#{pc_Xrx00109.propIdo.stringValue}"
							style="#{pc_Xrx00109.propIdo.style}"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<DIV class="column" align="center" style="width:870px">
			<TABLE class="table" border="0" cellpadding="5" width="870">
				<TBODY>
					<!-- 年度 -->
					<TR>
						<TH nowrap class="v_a" width="150px"><h:outputText
							styleClass="outputText" id="lblGhYear"
							value="#{pc_Xrx00109.propGhYear.labelName}"
							style="#{pc_Xrx00109.propGhYear.labelStyle}">
						</h:outputText></TH>
						<TD width="*" colspan=2><h:inputText styleClass="inputText"
							id="htmlGhYear" size="4" value="#{pc_Xrx00109.propGhYear.value}"
							style="#{pc_Xrx00109.propGhYear.style}"
							disabled="#{pc_Xrx00109.propGhYear.disabled}" tabindex="1">
							<hx:inputHelperAssist imeMode="inactive"
								errorClass="inputText_Error" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
						<TD rowspan="2" width="300px" valign="middle">
						<hx:commandExButton
							type="submit" value="選択" 
							styleClass="cmdBtn_dat_s" 
							id="select"
							disabled="#{pc_Xrx00109.propSelect.disabled}"
							action="#{pc_Xrx00109.doSelectAction}">
						</hx:commandExButton>
						<hx:commandExButton 
							type="submit" value="解除"
							styleClass="cmdBtn_etc_s" 
							id="unselect"
							action="#{pc_Xrx00109.doUnselectAction}"
							disabled="#{pc_Xrx00109.propUnselect.disabled}">
						</hx:commandExButton>
						</TD>
						<%-- 業務コード --%>
					<TR>
						<TH width="150px" nowrap class="v_b"><h:outputText
							styleClass="outputText" id="propgyoumCD"
							value="#{pc_Xrx00109.propgyoumCD.name}"
							style="#{pc_Xrx00109.propgyoumCD.labelStyle}">
						</h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="propgyoumList"
							style="width:300px;" value="#{pc_Xrx00109.propgyoumList.value}"
							disabled="#{pc_Xrx00109.propgyoumList.disabled}"
							tabindex="7">
							<f:selectItems value="#{pc_Xrx00109.propgyoumList.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			<CENTER class="column" style="width:870px"><h:dataTable
				rowClasses="#{pc_Xrx00109.propPayList.rowClasses}" cellpadding="0"
				cellspacing="0" headerClass="headerClass" footerClass="footerClass"
				styleClass="meisai_scroll" id="htmlPayList"
				value="#{pc_Xrx00109.propPayList.list}" var="varlist" width="870">
				<h:column id="column1">
					<!-- 納付金パターン名称 -->
					<f:facet name="header">
						<hx:jspPanel>
							<CENTER><h:outputText styleClass="outputText"
								id="lblListNofukinName" value="納付金パターン名称">
							</h:outputText></CENTER>
						</hx:jspPanel>
					</f:facet>
					<h:outputText styleClass="outputText" id="htmlListNofukinName"
						value="#{varlist.nofukinNameOut.displayValue}"
						title="#{varlist.nofukinName}">
					</h:outputText>
					<f:attribute value="150" name="width" />
					<f:attribute value="text-align: left" name="style" />
				</h:column>
				<!-- 内訳科目 -->
				<h:column id="column2">
					<f:facet name="header">
						<hx:jspPanel>
							<CENTER><h:outputText styleClass="outputText"
								id="lblListUtiwakekamoku" value="内訳科目">
							</h:outputText></CENTER>
						</hx:jspPanel>
					</f:facet>
					<h:outputText styleClass="outputText" id="htmlListUtiwakekamoku"
						value="#{varlist.utiwakekamokuOut.displayValue}"
						title="#{varlist.utiwakekamoku}">
					</h:outputText>
					<f:attribute value="120" name="width" />
					<f:attribute value="text-align: left" name="style" />
				</h:column>
				<!-- 当該(入金(金額、日付)・返金(金額、日付)) -->
				<!-- 前受(入金(金額、日付)・返金(金額、日付)) -->
				<h:column id="column3">
					<f:facet name="header">
						<hx:jspPanel id="jspPanel1">
							<TABLE cellpadding="0" cellspacing="0" height="100%">
								<TBODY>
									<TR>
										<TH width="300" colspan="4"><h:outputText
											styleClass="outputText" id="lblListTogai" value="当該"></h:outputText></TH>
										<TH width="300" colspan="4"><h:outputText
											styleClass="outputText" id="lblListMeuke" value="前受"></h:outputText></TH>
									</TR>
									<TR>
										<TH width="150" colspan="2"><h:outputText
											styleClass="outputText" id="lblTogainyukin" value="入金"></h:outputText></TH>
										<TH width="150" colspan="2"><h:outputText
											styleClass="outputText" id="lblTogaihenkin" value="返金"></h:outputText></TH>
										<TH width="150" colspan="2"><h:outputText
											styleClass="outputText" id="lblMeukenyukin" value="入金"></h:outputText></TH>
										<TH width="150" colspan="2"><h:outputText
											styleClass="outputText" id="lblMeukehenkin" value="返金"></h:outputText></TH>
									</TR>
									<TR>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblTogaiNyukinMoney" value="金額"></h:outputText></TH>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblTogaiNyukinDate" value="日付"></h:outputText></TH>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblTogaiHenkinMoney" value="金額"></h:outputText></TH>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblTogaiHenkinDate" value="日付"></h:outputText></TH>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblMeukeNyukinMoney" value="金額"></h:outputText></TH>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblMeukeNyukinDate" value="日付"></h:outputText></TH>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblMeukeHenkinMoney" value="金額"></h:outputText></TH>
										<TH width="75"><h:outputText styleClass="outputText"
											id="lblMeukeHenkinDate" value="日付"></h:outputText></TH>
									</TR>
								</TBODY>
							</TABLE>
						</hx:jspPanel>
					</f:facet>
					<hx:jspPanel id="jspPanel2">
						<TABLE style="table-layout: fixed;" cellpadding="0" cellspacing="0"  height="100%"
							style="border-top-style:none; border-bottom-style:none; ">
							<TBODY>
								<TR>
								
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70"  ><h:outputText
										styleClass="outputText" id="lblTogaiNyukinMoney_list"
										value="#{varlist.togaiNyukinMoney}" ></h:outputText></TD>
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70" ><h:outputText
										styleClass="outputText" id="lblTogaiNyukinDate_list"
										value="#{varlist.togaiNyukinDate}"></h:outputText></TD>
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70" ><h:outputText
										styleClass="outputText" id="lblTogaiHenkinMoney_list"
										value="#{varlist.togaiHenkinMoney}"></h:outputText></TD>
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70" ><h:outputText styleClass="outputText"
										id="lblTogaiHenkinDate_list"
										value="#{varlist.togaiHenkinDate}"></h:outputText></TD>
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70" ><h:outputText styleClass="outputText"
										id="lblMeukeNyukinMoney_list"
										value="#{varlist.meukeNyukinMoney}"></h:outputText></TD>
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70" ><h:outputText styleClass="outputText"
										id="lblMeukeNyukinDate_list"
										value="#{varlist.meukeNyukinDate}"></h:outputText></TD>
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70" ><h:outputText styleClass="outputText"
										id="lblMeukeHenkinMoney_list"
										value="#{varlist.meukeHenkinMoney}"></h:outputText></TD>
									<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
									 	text-align: right" width="70" ><h:outputText styleClass="outputText"
										id="lblMeukeHenkinDate_list"
										value="#{varlist.meukeHenkinDate}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
					</hx:jspPanel>
				</h:column>
			</h:dataTable></CENTER>
			</DIV>
			</DIV>
			<h:inputHidden value="#{pc_Xrx00109.propExecutableSearch.integerValue}" id="htmlExecutableSearch"></h:inputHidden>
			
			
		</hx:jspPanel>
		<BR>
		<%-- ↑ コンテンツ部 ↑ --%>
	</hx:scriptCollector>
</f:subview>

