<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Col00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Col00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
//onload
function onloadProc(event) {
	getGakuseiName(document.getElementById('form1:htmlGaksekiCd')    , event, 'form1:htmlGakseiName');
	getGakuseiName(document.getElementById('form1:htmlGaksekiCdEdit'), event, 'form1:htmlGakseiNameEdit');
	getJinjiName(document.getElementById('form1:htmlJinjiCd')    , event, 'form1:htmlJinjiName');
	getJinjiName(document.getElementById('form1:htmlJinjiCdEdit'), event, 'form1:htmlJinjiNameEdit');
}

function confirmOk() {
	var btnAction = document.getElementById('form1:htmlBtnAction').value;
	document.getElementById('form1:htmlExecConfirm').value = "1";
	indirectClick(btnAction);
}
function confirmCancel() {
	document.getElementById('form1:htmlExecConfirm').value = "0";
}

//学生名称を取得する
function getGakuseiName(thisObj, thisEvent, target) {
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;
	args['code2'] = "";
	args['code3'] = "";
	//var target = "form1:htmlGakseiName";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//学生検索画面
function openGakusei(field1) {
	var url = "${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
			+ "?retFieldName=" + field1;
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return false;
}

//人事名称を取得する
function getJinjiName(thisObj, thisEvent, target) {
	var servlet = "rev/co/CobJinjAJAX";
	//var target = "form1:lblJinjName";
	getCodeName(servlet, target, thisObj.value);
	return false;
}

//教員検索画面
function openJinji(field1) {
	var url = "${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp"
			+ "?kyoShokuin=3&retFieldName=" + field1;
	openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return false;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY onload="onloadProc(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Col00801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Col00801.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Col00801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Col00801.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			
			<%-- 検索条件エリア --%>
			<TABLE border="0" width="100%" style="margin-top:10px;">
				<TBODY>
					<TR>
						<TD width="100%">
							<TABLE border="0" class="table" width="100%">
								<TBODY>
									<%-- ユーザＩＤ --%>
									<TR>
										<TH width="13%" class="v_e">
											<h:outputText styleClass="outputText" id="lblUserId"
												value="#{pc_Col00801.propUserId.labelName}"
												style="#{pc_Col00801.propUserId.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="27%" style="border-right-style:none;">
											<h:inputText styleClass="inputText" id="htmlUserId"
												maxlength="#{pc_Col00801.propUserId.maxLength}" size="32"
												value="#{pc_Col00801.propUserId.stringValue}"
												style="#{pc_Col00801.propUserId.style}">
											</h:inputText>
										</TD>
										<TD style="border-left-style:none;">
											<h:selectOneRadio styleClass="selectOneRadio" id="htmlUserIdFindType"
												disabledClass="selectOneRadio_Disabled"
												value="#{pc_Col00801.propUserIdFindType.value}">
												<f:selectItem itemValue="0" itemLabel="部分一致" />
												<f:selectItem itemValue="1" itemLabel="前方一致" />
											</h:selectOneRadio>
										</TD>
									</TR>
									<%-- 学籍番号 --%>
									<TR>
										<TH class="v_e">
											<h:outputText styleClass="outputText" id="lblGaksekiCd"
												value="#{pc_Col00801.propGaksekiCd.labelName}"
												style="#{pc_Col00801.propGaksekiCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="2">
											<h:inputText styleClass="inputText" id="htmlGaksekiCd"
												maxlength="#{pc_Col00801.propGaksekiCd.maxLength}" size="32"
												onblur="return getGakuseiName(this, event, 'form1:htmlGakseiName');"
												value="#{pc_Col00801.propGaksekiCd.stringValue}"
												style="#{pc_Col00801.propGaksekiCd.style}">
											</h:inputText>
											<hx:commandExButton type="button" value="検"
												styleClass="commandExButton_search" id="searchGaksei"
												onclick="openGakusei('form1:htmlGaksekiCd');">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlGakseiName"></h:outputText>
										</TD>
									</TR>
									<%-- 人事番号 --%>
									<TR>
										<TH class="v_e">
											<h:outputText styleClass="outputText" id="lblJinjiCd"
												value="#{pc_Col00801.propJinjiCd.labelName}"
												style="#{pc_Col00801.propJinjiCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="2">
											<h:inputText styleClass="inputText" id="htmlJinjiCd"
												maxlength="#{pc_Col00801.propJinjiCd.maxLength}" size="32"
												onblur="return getJinjiName(this, event, 'form1:htmlJinjiName');"
												value="#{pc_Col00801.propJinjiCd.stringValue}"
												style="#{pc_Col00801.propJinjiCd.style}">
											</h:inputText>
											<hx:commandExButton type="button" value="検"
												styleClass="commandExButton_search" id="searchJinji"
												onclick="openJinji('form1:htmlJinjiCd');">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlJinjiName"></h:outputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center" width="100%">
							<hx:commandExButton type="submit" value="検索"
								styleClass="commandExButton_dat" id="search"
								action="#{pc_Col00801.doSearchAction}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="クリア"
								styleClass="commandExButton_etc" id="clear"
								action="#{pc_Col00801.doClearAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<%-- 検索結果エリア --%>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD align="right">
							<h:outputText styleClass="outputText" id="count"
								value="#{pc_Col00801.propLdapUserList.listCount}">
							</h:outputText>
							<h:outputText styleClass="outputText" id="countText" value="件"></h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD>
							<DIV class="listScroll" id="listScroll"
								style="height:233px; width:100%" onscroll="setScrollPosition('scroll',this);">
								<h:dataTable styleClass="meisai_scroll" id="table1"
									border="0" width="98%" cellpadding="2" cellspacing="0"
									headerClass="headerClass" footerClass="footerClass"
									rowClasses="#{pc_Col00801.propLdapUserList.rowClasses}"
									value="#{pc_Col00801.propLdapUserList.list}" var="varlist">
									<h:column id="column1">
										<f:facet name="header">
											<h:outputText styleClass="outputText" id="text1" value="ユーザＩＤ"></h:outputText>
										</f:facet>
										<hx:jspPanel>
											<div style="text-overflow:ellipsis; white-space:nowrap; overflow:hidden; width:150px;">
												<h:outputText styleClass="outputText" id="text11"
													value="#{varlist.userId}" title="#{varlist.userId}"></h:outputText>
											</div>
										</hx:jspPanel>
										<f:attribute value="160" name="width" />
									</h:column>
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText styleClass="outputText" id="text2" value="利用者識別区分"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="text12"
											value="#{varlist.shikibetsuKbn}"></h:outputText>
										<f:attribute value="100" name="width" />
									</h:column>
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText styleClass="outputText" id="text3" value="学籍番号／人事番号"></h:outputText>
										</f:facet>
										<hx:jspPanel>
											<div style="text-overflow:ellipsis; white-space:nowrap; overflow:hidden; width:140px;">
												<h:outputText styleClass="outputText" id="text13"
													value="#{varlist.dispCd}" title="#{varlist.dispCd}"></h:outputText>
											</div>
										</hx:jspPanel>
										<f:attribute value="150" name="width" />
									</h:column>
									<h:column id="column4">
										<f:facet name="header">
											<h:outputText styleClass="outputText" id="text4" value="氏名"></h:outputText>
										</f:facet>
										<hx:jspPanel>
											<div style="text-overflow:ellipsis; white-space:nowrap; overflow:hidden; width:450px;">
												<h:outputText styleClass="outputText" id="text14"
													value="#{varlist.name}" title="#{varlist.name}"></h:outputText>
											</div>
										</hx:jspPanel>
									</h:column>
									<h:column id="column5">
										<f:facet name="header"></f:facet>
										<hx:commandExButton type="submit" value="選択" id="select"
											styleClass="commandExButton"
											action="#{pc_Col00801.doSelectAction}">
										</hx:commandExButton>
										<f:attribute value="40" name="width" />
									</h:column>
								</h:dataTable>
							</DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<%-- 編集エリア --%>
			<TABLE border="0" width="100%" style="margin-top:10px;">
				<TBODY>
					<TR>
						<TD width="100%">
							<TABLE border="0" class="table" width="100%">
								<TBODY>
									<%-- ユーザＩＤ --%>
									<TR>
										<TH width="13%" class="v_e">
											<h:outputText styleClass="outputText" id="lblUserIdEdit"
												value="#{pc_Col00801.propUserIdEdit.labelName}"
												style="#{pc_Col00801.propUserIdEdit.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputText styleClass="inputText" id="htmlUserIdEdit"
												maxlength="#{pc_Col00801.propUserIdEdit.maxLength}" size="32"
												value="#{pc_Col00801.propUserIdEdit.stringValue}"
												style="#{pc_Col00801.propUserIdEdit.style}">
											</h:inputText>
										</TD>
									</TR>
									<%-- 学籍番号 --%>
									<TR>
										<TH class="v_e">
											<h:outputText styleClass="outputText" id="lblGaksekiCdEdit"
												value="#{pc_Col00801.propGaksekiCdEdit.labelName}"
												style="#{pc_Col00801.propGaksekiCdEdit.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputText styleClass="inputText" id="htmlGaksekiCdEdit"
												maxlength="#{pc_Col00801.propGaksekiCdEdit.maxLength}" size="32"
												onblur="return getGakuseiName(this, event, 'form1:htmlGakseiNameEdit');"
												value="#{pc_Col00801.propGaksekiCdEdit.stringValue}"
												style="#{pc_Col00801.propGaksekiCdEdit.style}">
											</h:inputText>
											<hx:commandExButton type="button" value="検"
												styleClass="commandExButton_search" id="searchGakseiEdit"
												onclick="openGakusei('form1:htmlGaksekiCdEdit');">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlGakseiNameEdit"></h:outputText>
										</TD>
									</TR>
									<%-- 人事番号 --%>
									<TR>
										<TH class="v_e">
											<h:outputText styleClass="outputText" id="lblJinjiCdEdit"
												value="#{pc_Col00801.propJinjiCdEdit.labelName}"
												style="#{pc_Col00801.propJinjiCdEdit.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputText styleClass="inputText" id="htmlJinjiCdEdit"
												maxlength="#{pc_Col00801.propJinjiCdEdit.maxLength}" size="32"
												onblur="return getJinjiName(this, event, 'form1:htmlJinjiNameEdit');"
												value="#{pc_Col00801.propJinjiCdEdit.stringValue}"
												style="#{pc_Col00801.propJinjiCdEdit.style}">
											</h:inputText>
											<hx:commandExButton type="button" value="検"
												styleClass="commandExButton_search" id="searchJinjiEdit"
												onclick="openJinji('form1:htmlJinjiCdEdit');">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlJinjiNameEdit"></h:outputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" class="button_bar" align="right" width="100%">
				<TBODY>
					<TR>
						<TD align="center" width="100%">
							<hx:commandExButton type="submit" value="確定"
								styleClass="commandExButton_dat" id="register"
								action="#{pc_Col00801.doRegisterAction}"
								confirm="#{msg.SY_MSG_0001W}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="削除"
								styleClass="commandExButton_dat" id="delete"
								action="#{pc_Col00801.doDeleteAction}"
								confirm="#{msg.SY_MSG_0004W}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="クリア"
								styleClass="commandExButton_etc" id="clearEdit"
								action="#{pc_Col00801.doClearEditAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden id="scroll" value="#{pc_Col00801.propLdapUserList.scrollPosition}"></h:inputHidden>
			<h:inputHidden id="htmlExecConfirm" value="#{pc_Col00801.propExecConfirm.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlBtnAction" value="#{pc_Col00801.propBtnAction.stringValue}"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

