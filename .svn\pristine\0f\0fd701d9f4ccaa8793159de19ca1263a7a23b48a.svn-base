<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsd00502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsd00502.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

window.onload = init;
function init(){
	// iframeを生成する
	createHeadShim("form1:htmlSeisekiList");
	// スクロールを戻す処理を実装
	changeScrollPosition('scroll','listScroll');
}
function createHeadShim(tableId){
	children = document.getElementById(tableId).getElementsByTagName("thead");
	var headerObject;
	var top;
	var left;
	for (i = 0;i < children.length;i++) {
		headerObject = children[i];
		thObject = headerObject.getElementsByTagName("th");
		for (j = 0;j < thObject.length;j++) {
			thObject[j].style.zIndex = 2;
			if (j == 0){	
				top = thObject[j].offsetTop;
				left = thObject[j].offsetLeft;
			}
		}
	} 
	var shim = document.createElement("<iframe scrolling='no' frameborder='0'"+
	"style='position:absolute; top:0px;"+
	"left:0px; display:none' id='headerObject'></iframe>");
	window.document.body.appendChild(shim);
	shim.style.width = headerObject.offsetWidth;
	shim.style.height = headerObject.offsetHeight;
	shim.style.top = top;
	shim.style.left = left;
	shim.style.zIndex = 1;
	shim.style.position = "absolute";
	shim.style.display = "block";
}

function confirmOk() {
	var buttonID = document.getElementById('form1:htmlPushButtonID').value;
	if (buttonID == 'search') {
		document.getElementById('form1:htmlExecutableSearch').value = "1";	
		indirectClick('search');
	} else if (buttonID == 'fix') {
		document.getElementById('form1:htmlExecutableFix').value = "1";	
		indirectClick('fix');
	}
}
function confirmCancel() {
	var buttonID = document.getElementById('form1:htmlPushButtonID').value;
	if (buttonID == 'search') {
		document.getElementById('form1:htmlExecutableSearch').value = "0";
	} else if (buttonID == 'fix') {
		document.getElementById('form1:htmlExecutableFix').value = "0";
	}
}

function func_1(thisObj, thisEvent) {
	openModalWindow("", "PNsc0101", "<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
	setTarget("PNsc0101");
	return true;

}
function func_2(thisObj, thisEvent) {
	openModalWindow("", "PNsc0101", "<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
	setTarget("PNsc0101");
	return true;

}
function func_3(thisObj, thisEvent) {
}
function func_4(thisObj, thisEvent) {
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsd00502.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsd00502.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsd00502.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsd00502.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp" action="#{pc_Nsd00502.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="4" width="100%">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="80%" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH width="145" class="v_a"><h:outputText
										styleClass="outputText" id="lblNyushiNendoHeader" value="#{pc_Nsd00502.propNyushiNendo.labelName}"></h:outputText></TH>
									<TD width="120"><h:outputText styleClass="outputText"
										id="lblNyushiNendo" value="#{pc_Nsd00502.propNyushiNendo.stringValue}">
									</h:outputText></TD>
									<TH width="145" class="v_b"><h:outputText
										styleClass="outputText" id="lblNyushiGakkiHeader" value="#{pc_Nsd00502.propNyushiGakki.labelName}"></h:outputText></TH>
									<TD colspan="3" width="390"><h:outputText
										styleClass="outputText" id="lblNyushiGakki" value="#{pc_Nsd00502.propNyushiGakki.stringValue}"></h:outputText><h:inputText
										styleClass="inputText" id="htmlJukenCdHidden" size="10"
										rendered="false"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="145" class="v_c"><h:outputText
										styleClass="outputText" id="lblNysSbtCdHeader" value="#{pc_Nsd00502.propNysSbtCd.labelName}"></h:outputText></TH>
									<TD width="120"><h:outputText styleClass="outputText"
										id="lblNysSbtCd" value="#{pc_Nsd00502.propNysSbtCd.stringValue}"></h:outputText></TD>
									<TH width="145" class="v_d"><h:outputText
										styleClass="outputText" id="lblNysSbtNameHeader" value="#{pc_Nsd00502.propNysSbtName.labelName}"></h:outputText></TH>
									<TD colspan="3" width="390"><h:outputText
										styleClass="outputText" id="lblNysSbtName" value="#{pc_Nsd00502.propNysSbtName.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="145" class="v_e"><h:outputText
										styleClass="outputText" id="lblGakkaCdHeader" value="#{pc_Nsd00502.propGakkaCd.labelName}"></h:outputText></TH>
									<TD width="120"><h:outputText styleClass="outputText"
										id="lblGakkaCd" value="#{pc_Nsd00502.propGakkaCd.stringValue}"></h:outputText></TD>
									<TH width="145" class="v_f"><h:outputText
										styleClass="outputText" id="lblGakkaNameHeader" value="#{pc_Nsd00502.propGakkaName.labelName}"></h:outputText></TH>
									<TD colspan="3" width="390"><h:outputText
										styleClass="outputText" id="lblGakkaName" value="#{pc_Nsd00502.propGakkaName.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="145" class="v_g"><h:outputText
										styleClass="outputText" id="lblKyokaNoHeader" value="#{pc_Nsd00502.propKyokaNo.labelName}"></h:outputText></TH>
									<TD width="120"><h:outputText styleClass="outputText"
										id="lblKyokaNo" value="#{pc_Nsd00502.propKyokaNo.stringValue}">
									</h:outputText></TD>
									<TH width="145" class="v_a"><h:outputText
										styleClass="outputText" id="lblKyokaHeader" value="#{pc_Nsd00502.propKyoka.labelName}"></h:outputText></TH>
									<TD width="235"><h:outputText styleClass="outputText"
										id="lblKyoka" value="#{pc_Nsd00502.propKyoka.stringValue}"></h:outputText></TD>
									<TH width="55" class="v_b"><h:outputText
										styleClass="outputText" id="lblDankaiHeader" value="#{pc_Nsd00502.propDankai.labelName}"></h:outputText></TH>
									<TD width="100"><h:outputText styleClass="outputText"
										id="lblDankai" value="#{pc_Nsd00502.propDankai.stringValue}">
									</h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="80%" border="0" cellpadding="0" cellspacing="0" style="margin-top:10px">
							<TBODY>
								<TR>
									<TD width="595">
									<TABLE width="595" border="0" cellpadding="0" cellspacing="0"
										class="table">
										<TBODY>
											<TR>
												<TH width="130" class="v_c"><h:outputText
													styleClass="outputText" id="lblJukenCd"
													value="#{pc_Nsd00502.propJukenCdHidden.labelName}"></h:outputText></TH>
												<TD width="455"><h:inputText styleClass="inputText"
													id="htmlJukenCdFrom" size="18"
													value="#{pc_Nsd00502.propJukenCdFrom.stringValue}"
													maxlength="#{pc_Nsd00502.propJukenCdFrom.maxLength}"
													style="#{pc_Nsd00502.propJukenCdFrom.style}"
													onblur="return func_3(this, event);" tabindex="1"></h:inputText><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchJknFrom"
													action="#{pc_Nsd00502.doSearchJknFromAction}"
													onclick="return func_1(this, event);" tabindex="2"></hx:commandExButton><h:outputText
													styleClass="outputText" id="lblKara" value="～"></h:outputText>
												<h:inputText styleClass="inputText" id="htmlJukenCdTo"
													size="18" value="#{pc_Nsd00502.propJukenCdTo.stringValue}"
													maxlength="#{pc_Nsd00502.propJukenCdTo.maxLength}"
													style="#{pc_Nsd00502.propJukenCdTo.style}"
													onblur="return func_4(this, event);" tabindex="3"></h:inputText><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchJknTo"
													action="#{pc_Nsd00502.doSearchJknToAction}"
													onclick="return func_2(this, event);" tabindex="4"></hx:commandExButton>
												</TD>

											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="30"></TD>
									<TD align="left" width="185"><hx:commandExButton type="submit" value="検索"
										styleClass="commandExButton" id="search"
										action="#{pc_Nsd00502.doSearchAction1}" tabindex="5"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" style="margin-top:10px">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText styleClass="outputText"
										id="lblListCount" style="style:outputText" value="#{pc_Nsd00502.propSeisekiList.listCount}">
									</h:outputText> <h:outputText styleClass="outputText"
										id="lblKen" value="件" style="style:outputText"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TD align="center"> 
										<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
											<TBODY>	

												<TR>
													<TD align="center">
 													<DIV class="listScroll" style="height:207px;width=0%" id="listScroll" onscroll="setScrollPosition('scroll',this);"> 
													<h:dataTable border="0" cellpadding="0" cellspacing="0"
													headerClass="headerClass" footerClass="footerClass"
													rowClasses="#{pc_Nsd00502.propSeisekiList.rowClasses}"
													styleClass="meisai_scroll" id="htmlSeisekiList" width="727"
													value="#{pc_Nsd00502.propSeisekiList.list}" var="varlist">
													<h:column id="column1">
														<f:facet name="header">
																<h:outputText id="lblHeaderJukenCd" styleClass="outputText"
																	value="受験番号"
																	style="text-align: center; "></h:outputText>
														</f:facet>
														<f:attribute value="120" name="width" />
														<h:outputText styleClass="outputText" id="lblDataJukenCd"
															value="#{varlist.jukenCd}"></h:outputText>
													</h:column>
													<h:column id="column2">
														<f:facet name="header">
																<h:outputText styleClass="outputText" value="氏名"
																	id="lblHeaderName"
																	style="text-align: center; vertical-align: super"></h:outputText>
														</f:facet>
														<h:outputText styleClass="outputText" id="lblDataName"
															value="#{varlist.name.displayValue}"
															title="#{varlist.name.value}"></h:outputText>
														<f:attribute value="175" name="width" />
													</h:column>

													<h:column id="column3">
														<f:facet name="header">
																<h:outputText styleClass="outputText" value="科目"
																	id="lblHeaderKamok"
																	style="text-align: center; vertical-align: super"></h:outputText>
														</f:facet>
														<f:attribute value="text-align: center" name="style" />
														<f:attribute value="177" name="width" />
														<h:selectOneMenu styleClass="selectOneListbox"
															id="htmlDataKamok" style="width:167px;"
															value="#{varlist.propKamok.value}"
															disabled="#{varlist.propKamok.disabled}"
															readonly="#{varlist.propKamok.readonly}" tabindex="6">
															<f:selectItems value="#{varlist.propKamok.list}" />
														</h:selectOneMenu>
													</h:column>

													<h:column id="column4">
														<f:facet name="header">
																<hx:jspPanel id="jspPanel1">
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		width="100%" height="100%"
																		style="border-bottom-style:none;border-top-style:none;">
																		<TBODY>
																			<TR>
																				<TH style="border-top-style: none;border-left-style: none;"><h:outputText
																					styleClass="outputText" value="得点"
																					id="lblHeaderTokten1" style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																			<TR>
																				<TH style="border-top-style: none;border-left-style: none;border-bottom-style: none;"><h:outputText
																					styleClass="outputText" value="(半4[3.1])"
																					id="lblHeaderTokten2" style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																		</TBODY>
																	</TABLE>
																</hx:jspPanel>
														</f:facet>
														<f:attribute value="65" name="width" />
														<h:inputText styleClass="inputText" id="htmlDataTokten"
															size="5" value="#{varlist.propTokten.stringValue}"
															disabled="#{varlist.propTokten.disabled}"
															maxlength="#{varlist.propTokten.maxLength}"
															readonly="#{varlist.propTokten.readonly}"
															style="#{varlist.propTokten.style}; padding-right:3px; text-align:right" tabindex="7">

															<hx:inputHelperAssist
																errorClass="inputText_Error"
																promptCharacter="_"
																imeMode="#{varlist.propTokten.imeMode}" />

														</h:inputText>
														<f:attribute
															value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
															name="style" />
													</h:column>
													<h:column id="column5">
														<f:facet name="header">
															<hx:jspPanel id="jspPanel2">
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	width="100%" height="100%"
																	style="border-bottom-style:none;border-top-style:none;">
																	<TBODY>
																		<TR>
																			<TH style="border-top-style: none;border-left-style: none;"><h:outputText
																				styleClass="outputText" value="ﾘｽﾆﾝｸﾞ"
																				id="lblHeaderLsnKbn1" style="text-align: center">
																			</h:outputText></TH>
																		</TR>
																		<TR>
																			<TH style="border-top-style: none;border-left-style: none;border-bottom-style: none;"><h:outputText
																				styleClass="outputText" value="区分"
																				id="lblHeaderLsnKbn2" style="text-align: center">
																			</h:outputText></TH>
																		</TR>
																	</TBODY>
																</TABLE>
															</hx:jspPanel>
														</f:facet>
														<f:attribute value="85" name="width" />
														<h:selectOneMenu styleClass="selectOneListbox"
															id="htmlDataLsnKbn" style="width:80px;"
															value="#{varlist.propLsnKbn.value}"
															readonly="#{varlist.propLsnKbn.readonly}"
															disabled="#{varlist.propLsnKbn.disabled}" tabindex="8">
															<f:selectItems value="#{varlist.propLsnKbn.list}" />
														</h:selectOneMenu>
														<f:attribute
															value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
															name="style" />
													</h:column>
													<h:column id="column6">
														<f:facet name="header">
																<hx:jspPanel id="jspPanel3">
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		width="100%" height="100%"
																		style="border-bottom-style:none;border-top-style:none;">
																		<TBODY>
																			<TR>
																				<TH style="border-top-style: none;border-left-style: none;"><h:outputText
																					styleClass="outputText" value="換算点"
																					id="lblHeaderKansanten1" style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																			<TR>
																				<TH style="border-top-style: none;border-left-style: none;border-bottom-style: none;"><h:outputText
																					styleClass="outputText" value="(半4[3.1])"
																					id="lblHeaderKansanten2" style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																		</TBODY>
																	</TABLE>
																</hx:jspPanel>
														</f:facet>
														<f:attribute value="65" name="width" />
														<h:inputText styleClass="inputText" id="htmlDataKansanten"
															size="5" value="#{varlist.propKansanTen.stringValue}"
															disabled="#{varlist.propKansanTen.disabled}"
															maxlength="#{varlist.propKansanTen.maxLength}"
															readonly="#{varlist.propTokten.readonly}"
															style="#{varlist.propKansanTen.style}; padding-right:3px; text-align:right" tabindex="9">
														</h:inputText>
														<f:attribute
															value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
															name="style" />
													</h:column>
													<h:column id="column7">
														<f:facet name="header">
																<h:outputText styleClass="outputText" value="欠席"
																	id="lblHeaderKeseki"
																	style="text-align: center;"></h:outputText>
														</f:facet>
														<f:attribute value="text-align:center" name="style" />
														<f:attribute value="40" name="width" />
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlDataKeseki"
															value="#{varlist.keseki}" tabindex="10"></h:selectBooleanCheckbox>
													</h:column>
												</h:dataTable>
													</DIV>
													</TD>

												</TR>
											</TBODY>
										</TABLE>
									</TD>

								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar">
							<TBODY>
								<TR>
									<TD>
										<hx:commandExButton
										type="submit" value="確定" styleClass="commandExButton_dat"
										id="fix" disabled="#{pc_Nsd00502.propFix.disabled}" action="#{pc_Nsd00502.doFixAction}" confirm="#{msg.SY_MSG_0003W}" tabindex="11"></hx:commandExButton>
										<hx:commandExButton type="submit" value="クリア"
										styleClass="commandExButton_etc" id="clear"
										disabled="#{pc_Nsd00502.propClear.disabled}" action="#{pc_Nsd00502.doClearAction}" tabindex="12"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<h:inputHidden
				value="#{pc_Nsd00502.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsd00502.propExecutableFix.integerValue}"
				id="htmlExecutableFix">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsd00502.propPushButtonID.stringValue}"
				id="htmlPushButtonID">
			</h:inputHidden>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Nsd00502.propSeisekiList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

