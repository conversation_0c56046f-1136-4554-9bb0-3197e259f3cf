<%-- 
	教室設定
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmz01102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmz01102.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1";
	indirectClick('register');
	return true;	
}
function confirmCancel() {
	return false;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmz01102.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kmz01102.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kmz01102.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kmz01102.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton
	type="submit" value="戻る" styleClass="commandExButton"
	id="returnDisp" action="#{pc_Kmz01102.doReturnDispAction}">
</hx:commandExButton>

<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" width="100%">
		<TBODY>
		<TR>
			<TD>
				<TABLE border="0" align="center" class="table" width="600">
				<TBODY>
					<TR>
						<TH align="left" valign="middle" class="v_a" nowrap width="150">
							<h:outputText styleClass="outputText" id="lblShisetsuNo"
								style="#{pc_Kmz01102.propShisetsuNo.labelStyle}"
								value="#{pc_Kmz01102.propShisetsuNo.labelName}">
							</h:outputText>
						</TH>
						<TD align="left" valign="middle" class="" width="550">
							<h:outputText styleClass="outputText" id="lblShisetsuNo1"
								style="#{pc_Kmz01102.propShisetsuNo.labelStyle}"
								value="#{pc_Kmz01102.propShisetsuNo.stringValue}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH align="left" valign="middle" class="v_b" nowrap width="150">
							<h:outputText styleClass="outputText" id="lblShisetsuName"
								style="#{pc_Kmz01102.propShisetsuName.labelStyle}"
								value="#{pc_Kmz01102.propShisetsuName.labelName}">
							</h:outputText>
						</TH>
						<TD align="left" valign="middle" width="550">
							<h:outputText styleClass="outputText" id="lblShisetsuName1"
								style="#{pc_Kmz01102.propShisetsuName.labelStyle}"
								value="#{pc_Kmz01102.propShisetsuName.stringValue}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
				</TABLE>
			</TD>
		</TR>
		</TBODY>
	</TABLE>
	<BR>
	<HR class="hr">
	<BR>
	<TABLE border="0" width="100%" align="center">
		<TBODY>
		<TR>
			<TD>
				<TABLE border="0" align="center" class="table" width="600">
					<TBODY>
						<TR>
							<TH align="left" valign="middle" class="v_a" nowrap width="150">
								<h:outputText
									styleClass="outputText" id="lblInputFile"
									style="#{pc_Kmz01102.propInputFile.labelStyle}"
									value="#{pc_Kmz01102.propInputFile.labelName}">
								</h:outputText>
							</TH>
							<TD align="left" valign="middle" class="" width="500">
								<hx:fileupload styleClass="fileupload" id="htmlInputFile"
									value="#{pc_Kmz01102.propInputFile.value}"
									disabled=""
									readonly=""
									style="#{pc_Kmz01102.propInputFile.style};width:500px">
								<hx:fileProp name="fileName"
									value="#{pc_Kmz01102.propInputFile.fileName}" />
								<hx:fileProp name="contentType"
									value="#{pc_Kmz01102.propInputFile.contentType}" />
								</hx:fileupload>
								<br>
								<h:outputText styleClass="outputText" id="htmlPreInputFile" 
									value="#{pc_Kmz01102.propPreInputFile.value}" 
									style="#{pc_Kmz01102.propPreInputFile.style}">
								</h:outputText>
							</TD>
							<TD align="left" valign="middle" class="" width="50">
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="register"
									action="#{pc_Kmz01102.doRegisterAction}" value="追加">
								</hx:commandExButton>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</TD>
		</TR>
		</TBODY>
	</TABLE>
	<BR>
	<TABLE border="0" align="center">
		<TBODY>
			<TR>
				<TD align="right">
					<h:outputText styleClass="outputText"
						id="lblConditionListCount" style="font-size: 8pt"
						value="#{pc_Kmz01102.propTempFiles.listCount == null ? 0 : pc_Kmz01102.propTempFiles.listCount}">
					</h:outputText>
					<h:outputText styleClass="outputText"
						id="text3" value="件" style="font-size: 8pt">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TD>
					<div  class="listScroll" style="height:229px;" id="listScroll" onscroll="setScrollPosition('scroll',this);">
						<h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Kmz01102.propTempFiles.rowClasses}" id="tempFiles"
							style="listScroll" styleClass="meisai_scroll"
							value="#{pc_Kmz01102.propTempFiles.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="text1" value="添付ファイル"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblFileName_list"
									value="#{varlist.fileName}"></h:outputText>
								<f:attribute value="400" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="text2" value="サイズ"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblFileSize_list"
									value="#{varlist.fileSize}"></h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="select" value="表示"
									action="#{pc_Kmz01102.doSelectAction}"
									onclick="cancelSubmitCtrl();">
								</hx:commandExButton>
								<f:attribute value="30" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="delete" value="削除"
									action="#{pc_Kmz01102.doDeleteAction}"></hx:commandExButton>
								<f:attribute value="30" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable>
					</div>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<h:inputHidden
		value="#{pc_Kmz01102.propExecutable.integerValue}"
		id="htmlExecutable">
		<f:convertNumber />
	</h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden value="#{pc_Kmz01102.propTempFiles.scrollPosition}"
				id="scroll"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
