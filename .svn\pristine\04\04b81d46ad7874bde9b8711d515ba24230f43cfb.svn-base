<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssh00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssh00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssh00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssh00201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssh00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssh00201.screenName}"></h:outputText>
				<h:inputHidden id="htmlKmImportFlg" value="#{pc_Ssh00201.propKmImprotFlg.stringValue}"></h:inputHidden>
				</div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" cellspacing="0" cellpadding="0" width="800">
				<TBODY>
					<TR>
						<TD width="10%"></TD>
						<TD width="80%">
						<TABLE border="0" class="table" width="100%">
							<TBODY>
								<TR>
									<TH class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="text1"
										value="#{pc_Ssh00201.propGakko.labelName}"
										style="#{pc_Ssh00201.propGakko.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakko" value="#{pc_Ssh00201.propGakko.value}"
										style="#{pc_Ssh00201.propGakko.style}"
										disabled="#{pc_Ssh00201.propGakko.disabled}"
										readonly="#{pc_Ssh00201.propGakko.readonly}">
										<f:selectItems value="#{pc_Ssh00201.propGakko.list}" />
									</h:selectOneMenu></TD>
								</TR>

								<TR>
									<TH class="v_b" colspan="2" valign="top" rowspan="2" width="20%"><h:outputText
										styleClass="outputText" id="text2" value="年度指定"></h:outputText></TH>
									<TD rowspan="2" style="border-right:none" width="20%"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlNendo"
										layout="pageDirection"
										value="#{pc_Ssh00201.propNendo.stringValue}"
										disabled="#{pc_Ssh00201.propNendo.disabled}"
										readonly="#{pc_Ssh00201.propNendo.readonly}"
										style="#{pc_Ssh00201.propNendo.style}">
										<f:selectItem itemValue="0" itemLabel="大学卒業時" />
										<f:selectItem itemValue="1" itemLabel="高校卒業時" />
									</h:selectOneRadio></TD>									
									<TD width="20%"
										style="border-left:none;border-right:none;border-bottom:none"><h:outputText
										styleClass="outputText" id="text3"
										value="#{pc_Ssh00201.propNendoDai.labelName}"
										style="#{pc_Ssh00201.propNendoDai.labelStyle}"></h:outputText>
										<h:inputText id="htmlNendoDai" size="4" styleClass="inputText"
											value="#{pc_Ssh00201.propNendoDai.dateValue}"
											style="#{pc_Ssh00201.propNendoDai.style}"
											maxlength="#{pc_Ssh00201.propNendoDai.maxLength}"
											disabled="#{pc_Ssh00201.propNendoDai.disabled}"
											readonly="#{pc_Ssh00201.propNendoDai.readonly}">
											<hx:inputHelperAssist errorClass="inputText_Error"
												imeMode="inactive" promptCharacter="_" />
											<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>
									<TD width="40%" style="border-left:none;border-bottom:none"><h:outputText
										styleClass="outputText" id="text7"
										value="#{pc_Ssh00201.propGakkiNo.labelName}"
										style="#{pc_Ssh00201.propGakkiNo.labelStyle}"></h:outputText><h:inputText
										styleClass="inputText" id="htmlGakkiNo" size="2"
										value="#{pc_Ssh00201.propGakkiNo.stringValue}"
										maxlength="#{pc_Ssh00201.propGakkiNo.maxLength}"
										style="#{pc_Ssh00201.propGakkiNo.style}"
										disabled="#{pc_Ssh00201.propGakkiNo.disabled}"
										readonly="#{pc_Ssh00201.propGakkiNo.readonly}">
										</h:inputText></TD>
								</TR>

								<TR>									
									<TD style="border-left:none;border-top:none" colspan="2"><h:outputText
										styleClass="outputText" id="text4"
										value="#{pc_Ssh00201.propNendoHigh.labelName}"
										style="#{pc_Ssh00201.propNendoHigh.labelStyle}"></h:outputText><h:inputText
										id="htmlNendoHigh" size="4" styleClass="inputText"
										value="#{pc_Ssh00201.propNendoHigh.dateValue}"
										maxlength="#{pc_Ssh00201.propNendoHigh.maxLength}"
										disabled="#{pc_Ssh00201.propNendoHigh.disabled}"
										readonly="#{pc_Ssh00201.propNendoHigh.readonly}"
										style="#{pc_Ssh00201.propNendoHigh.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText></TD>									
								</TR>
								<TR>
									<TH class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="text12"
										value="#{pc_Ssh00201.propBunMen.labelName}"
										style="#{pc_Ssh00201.propBunMen.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlBunMen" value="#{pc_Ssh00201.propBunMen.stringValue}"
										style="#{pc_Ssh00201.propBunMen.style}"
										disabled="#{pc_Ssh00201.propBunMen.disabled}"
										readonly="#{pc_Ssh00201.propBunMen.readonly}">
										<f:selectItems value="#{pc_Ssh00201.propBunMen.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="text6" value="発行日付"
										style="#{pc_Ssh00201.propHakkoDate.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHakkoDate" size="10"
										value="#{pc_Ssh00201.propHakkoDate.dateValue}"
										style="#{pc_Ssh00201.propHakkoDate.style}"
										maxlength="#{pc_Ssh00201.propHakkoDate.maxLength}"
										disabled="#{pc_Ssh00201.propHakkoDate.disabled}"
										readonly="#{pc_Ssh00201.propHakkoDate.readonly}">
										<f:convertDateTime dateStyle="long" type="date" />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="" />
									</h:inputText></TD>
								</TR>
								<TR id="seiseki" style="display:none">
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="text10" value="在学時成績"></h:outputText></TH>
									<TD colspan="3"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSeiseki"
										value="#{pc_Ssh00201.propSeiseki.stringValue}"
										disabled="#{pc_Ssh00201.propSeiseki.disabled}"
										readonly="#{pc_Ssh00201.propSeiseki.readonly}"
										style="#{pc_Ssh00201.propSeiseki.style}">
										<f:selectItem itemValue="0" itemLabel="出力する" />
										<f:selectItem itemValue="1" itemLabel="出力しない" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2"><h:outputText
										styleClass="outputText" id="text14" value="宛名ラベル"></h:outputText></TH>
									<TD colspan="3"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlAtenaLabel"
										value="#{pc_Ssh00201.propAtenaLabel.stringValue}"
										disabled="#{pc_Ssh00201.propAtenaLabel.disabled}"
										readonly="#{pc_Ssh00201.propAtenaLabel.readonly}"
										style="#{pc_Ssh00201.propAtenaLabel.style}">
										<f:selectItem itemValue="0" itemLabel="連動出力する" />
										<f:selectItem itemValue="1" itemLabel="連動出力しない" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label">&nbsp;&nbsp;</TH>
									<TH class="v_a"><h:outputText styleClass="outputText"
										id="text16" value="〒表示"></h:outputText></TH>
									<TD colspan="3"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlDisplay"
										value="#{pc_Ssh00201.propDisplay.stringValue}"
										disabled="#{pc_Ssh00201.propDisplay.disabled}"
										readonly="#{pc_Ssh00201.propDisplay.readonly}"
										style="#{pc_Ssh00201.propAtenaLabel.style}">
										<f:selectItem itemValue="0" itemLabel="あり" />
										<f:selectItem itemValue="1" itemLabel="なし" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label">&nbsp;&nbsp;</TH>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="text17" value="レイアウト"></h:outputText></TH>
									<TD colspan="3"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlLayout"
										value="#{pc_Ssh00201.propLayout.stringValue}"
										style="#{pc_Ssh00201.propLayout.style}"
										readonly="#{pc_Ssh00201.propLayout.readonly}"
										disabled="#{pc_Ssh00201.propLayout.disabled}">
										<f:selectItem itemValue="0" itemLabel="Ａ４縦" />
										<f:selectItem itemValue="1" itemLabel="Ａ３横" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label">&nbsp;&nbsp;</TH>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="text20" value="#{pc_Ssh00201.propKaishiIchi.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText" id="htmlKaishiIchi"
										value="#{pc_Ssh00201.propKaishiIchi.integerValue}"
										style="#{pc_Ssh00201.propKaishiIchi.style}" size="2">
										<f:convertNumber type="number" pattern="##;##" />
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="group_label">&nbsp;&nbsp;</TH>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="text18" value="#{pc_Ssh00201.propPrinter.labelName}"
										style="#{pc_Ssh00201.propPrinter.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlPrinter"
										value="#{pc_Ssh00201.propPrinter.stringValue}"
										style="#{pc_Ssh00201.propPrinter.style}"
										disabled="#{pc_Ssh00201.propPrinter.disabled}"
										readonly="#{pc_Ssh00201.propPrinter.readonly}">
										<f:selectItems value="#{pc_Ssh00201.propPrinter.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="group_label_bottom">&nbsp;&nbsp;</TH>
									<TH class="v_d" valign="top"><h:outputText styleClass="outputText"
										id="text19" value="#{pc_Ssh00201.propRemark.labelName}"
										style="#{pc_Ssh00201.propRemark.labelStyle}"></h:outputText><hx:graphicImageEx
										styleClass="graphicImageEx" id="imageEx1" url="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlRemark" value="#{pc_Ssh00201.propRemark.stringValue}"
										style="#{pc_Ssh00201.propRemark.style}"
										maxlength="#{pc_Ssh00201.propRemark.maxLength}" size="40"
										disabled="#{pc_Ssh00201.propRemark.disabled}"
										readonly="#{pc_Ssh00201.propRemark.readonly}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="10%"></TD>
					</TR>
				</TBODY>
			</TABLE>			
			<BR>
			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"	
							action="#{pc_Ssh00201.doRegisterAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<SCRIPT type="text/javascript">				
				var improt=document.getElementById('form1:htmlKmImportFlg').value;				
				if(improt=="true")
				{	document.all("seiseki").style.display="";
				}				
			</SCRIPT>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

