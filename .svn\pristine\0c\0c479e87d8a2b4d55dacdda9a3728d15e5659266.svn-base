<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssf00401T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssf00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css" title="Style">

<SCRIPT type="text/javascript">
function openSearchSeminerKyoinWindow() {
// 教員検索画面
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlSeminerKyoinCd";
//	openModalWindow(url, "pCob0301", "status=yes,toolbar=no,menubar=no,location=no");
	openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
}

function func_3(thisObj, thisEvent) {

	if (thisObj != undefined) {
		// ゼミ担当教員名称を取得する

		var servlet = "rev/co/CoiJinjAJAX";
		var target = "form1:htmlSeminerKyoinName";
		var code = thisObj.value;
		getCodeName(servlet, target, code );
	}
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="func_3(document.getElementById('form1:htmlSeminerKyoinCd'), event);">
<!--
<BODY onLoad="resizeTo('1024', '768');">
-->
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssf00401T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<DIV style="display:none;">
	<hx:commandExButton
		type="submit"
		value="閉じる"
		styleClass="commandExButton" id="closeDisp"
		action="#{pc_Ssf00401T02.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText
		styleClass="outputText"
		id="htmlFuncId"
		value="#{pc_Ssf00401T02.funcId}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlScrnName"
		value="#{pc_Ssf00401T02.screenName}">
	</h:outputText>
</DIV>

<!--↓outer↓-->
<DIV class="outer">
	<FIELDSET class="fieldset_err">
		<LEGEND>エラーメッセージ</LEGEND>
		<h:outputText
			id="message"
			value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			styleClass="outputText"
			escape="false">
		</h:outputText>
	</FIELDSET>
	
	<DIV class="head_button_area" >
	<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトの問題の為に、全角スペースを配置 -->
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	<!--↓content↓-->
	<DIV id="content">
		<!--↓column↓-->
		<DIV class="column">
		<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" class="table" width="730" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="150">
							<h:outputText
								styleClass="outputText"
								id="lblKjnTsyNendo"
								value="#{pc_Ssf00401T01.ssf00401.propKjnTsyNendo.labelName}"
								style="#{pc_Ssf00401T01.ssf00401.propKjnTsyNendo.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText
								styleClass="inputText"
								id="htmlKjnTsyNendo"
								size="5"
								disabled="#{pc_Ssf00401T01.ssf00401.propKjnTsyNendo.disabled}"
								readonly="#{pc_Ssf00401T01.ssf00401.propKjnTsyNendo.readonly}"
								style="#{pc_Ssf00401T01.ssf00401.propKjnTsyNendo.style}"
								value="#{pc_Ssf00401T01.ssf00401.propKjnTsyNendo.dateValue}">
								<hx:inputHelperAssist
									imeMode="inactive"
									errorClass="inputText_Error"
									promptCharacter="_" />
								<f:convertDateTime
									pattern="yyyy" />
							</h:inputText>
							<h:outputText
								styleClass="outputText"
								value="年度">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_b" width="150">
							<h:outputText
								styleClass="outputText"
								id="lblSotYoteiYM"
								value="#{pc_Ssf00401T01.ssf00401.propSotYoteiYM.labelName}"
								style="#{pc_Ssf00401T01.ssf00401.propSotYoteiYM.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText
								styleClass="inputText"
								id="htmlSotYoteiYM"
								size="5"
								disabled="#{pc_Ssf00401T01.ssf00401.propSotYoteiYM.disabled}"
								readonly="#{pc_Ssf00401T01.ssf00401.propSotYoteiYM.readonly}"
								style="#{pc_Ssf00401T01.ssf00401.propSotYoteiYM.style}"
								value="#{pc_Ssf00401T01.ssf00401.propSotYoteiYM.dateValue}">
								<hx:inputHelperAssist
									imeMode="inactive"
									errorClass="inputText_Error"
									promptCharacter="_" />
								<f:convertDateTime
									pattern="yyyy/MM"
									type="date" />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_c" width="150">
							<h:outputText
								styleClass="outputText"
								id="lblTaisyoJoken"
								value="#{pc_Ssf00401T01.ssf00401.propTaisyoJoken.labelName}"
								style="#{pc_Ssf00401T01.ssf00401.propTaisyoJoken.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:selectOneRadio
								styleClass="selectOneRadio"
								id="htmlTaisyoJoken"
								layout="lineDirection"
								style="#{pc_Ssf00401T01.ssf00401.propTaisyoJoken.style}"
								value="#{pc_Ssf00401T01.ssf00401.propTaisyoJoken.value}"
								disabled="#{pc_Ssf00401T01.ssf00401.propTaisyoJoken.disabled}"
								readonly="#{pc_Ssf00401T01.ssf00401.propTaisyoJoken.readonly}">
								<f:selectItem itemValue="1" itemLabel="全員" />
								<f:selectItem itemValue="2" itemLabel="内定者" />
								<f:selectItem itemValue="3" itemLabel="就職先決定者" />
								<f:selectItem itemValue="4" itemLabel="未内定者" />
								<f:selectItem itemValue="5" itemLabel="非就職者" />
							</h:selectOneRadio>
						</TD>
					</TR>
					<TR>
						<TH class="v_g" width="150"><h:outputText
							styleClass="outputText" id="lblKanriTsy"
							value="#{pc_Ssf00401T01.ssf00401.propKanriTsy.labelName}"
							style="#{pc_Ssf00401T01.ssf00401.propKanriTsy.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneRadio
							styleClass="selectOneRadio" id="htmlKanriTsy"
							value="#{pc_Ssf00401T01.ssf00401.propKanriTsy.stringValue}"
							readonly="#{pc_Ssf00401T01.ssf00401.propKanriTsy.readonly}"
							style="#{pc_Ssf00401T01.ssf00401.propKanriTsy.style}"
							disabled="#{pc_Ssf00401T01.ssf00401.propKanriTsy.disabled}">
							<f:selectItem itemValue="2" itemLabel="含まない" />
							<f:selectItem itemValue="1" itemLabel="含む" />
							<f:selectItem itemValue="3" itemLabel="のみ" />
						</h:selectOneRadio></TD>
					</TR>
					
<hx:jspPanel rendered="#{pc_Ssf00401T01.ssf00401.propHiSeikiPanelDisp.rendered == true}">
					<TR>
						<TH class="v_g" width="150"><h:outputText
							styleClass="outputText" id="lblHiseikiKoyo"
							value="#{pc_Ssf00401T01.ssf00401.propHiseikiKoyo.labelName}"
							style="#{pc_Ssf00401T01.ssf00401.propHiseikiKoyo.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneRadio
							styleClass="selectOneRadio" id="htmlHiseikiKoyo"
							value="#{pc_Ssf00401T01.ssf00401.propHiseikiKoyo.stringValue}"
							readonly="#{pc_Ssf00401T01.ssf00401.propHiseikiKoyo.readonly}"
							style="#{pc_Ssf00401T01.ssf00401.propHiseikiKoyo.style}"
							disabled="#{pc_Ssf00401T01.ssf00401.propHiseikiKoyo.disabled}">
							<f:selectItem itemValue="2" itemLabel="出力しない" />
							<f:selectItem itemValue="1" itemLabel="出力する" />
						</h:selectOneRadio></TD>
					</TR>
</hx:jspPanel>
					
					<TR>
						<TH class="v_d" width="150">
							<h:outputText
								styleClass="outputText"
								id="lblChohyoTitle"
								value="#{pc_Ssf00401T01.ssf00401.propChohyoTitle.labelName}"
								style="#{pc_Ssf00401T01.ssf00401.propChohyoTitle.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText styleClass="inputText" id="htmlChohyoTitle" size="80"
								style="#{pc_Ssf00401T01.ssf00401.propChohyoTitle.style}"
								value="#{pc_Ssf00401T01.ssf00401.propChohyoTitle.stringValue}"
								disabled="#{pc_Ssf00401T01.ssf00401.propChohyoTitle.disabled}"
								readonly="#{pc_Ssf00401T01.ssf00401.propChohyoTitle.readonly}"
								maxlength="#{pc_Ssf00401T01.ssf00401.propChohyoTitle.maxLength}">
							</h:inputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellspacing="0" cellpadding="0" width="730" style="margin-top:10px;">
				<TBODY>
					<TR>
						<TD class="" align="left"><hx:commandExButton type="submit"
								value="学年・学部指定" styleClass="tab_head_off" id="btnSsf00401T01" style="width:14%"
								action="#{pc_Ssf00401T02.doBtnSsf00401T01Action}"></hx:commandExButton><hx:commandExButton type="submit"
								value="ゼミ教員指定" styleClass="tab_head_on" id="btnSsf00401T02" style="width:14%"
								readonly="true"></hx:commandExButton><hx:commandExButton type="submit"
								value="学生指定" styleClass="tab_head_off" id="btnSsf00401T03" style="width:14%"
								action="#{pc_Ssf00401T02.doBtnSsf00401T03Action}"></hx:commandExButton></TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="tab_body">
								<TBODY>
									<TR>
										<TD>
											<DIV style="height:300px" valign="top">
												<TABLE width="668" border="0" cellpadding="0" cellspacing="0" style="margin-top:20px;">
													<TBODY>
														<TR>
															<TD align="left">
																<TABLE width="569" border="0" cellspacing="0" cellpadding="3" class="table">
																	<TBODY>
																		<TR>
																			<TH class="v_e" width="150">
																				<h:outputText
																					styleClass="outputText" id="lblSeminarKyoinCd"
																					value="#{pc_Ssf00401T02.propSeminerKyoinCd.labelName}"
																					style="#{pc_Ssf00401T02.propSeminerKyoinCd.labelStyle}">
																				</h:outputText></TH>
																			<TD>
																				<h:inputText styleClass="inputText"
																					id="htmlSeminerKyoinCd"
																					style="#{pc_Ssf00401T02.propSeminerKyoinCd.style}"
																					value="#{pc_Ssf00401T02.propSeminerKyoinCd.stringValue}"
																					disabled="#{pc_Ssf00401T02.propSeminerKyoinCd.disabled}"
																					readonly="#{pc_Ssf00401T02.propSeminerKyoinCd.readonly}"
																					maxlength="#{pc_Ssf00401T02.propSeminerKyoinCd.maxLength}"
																					onblur="return func_3(this, event);"
																					size="24">
																				</h:inputText>
																				<hx:commandExButton
																					type="button"
																					value="検索"
																					styleClass="commandExButton_search"
																					id="search"
																					onclick="openSearchSeminerKyoinWindow();">
																				</hx:commandExButton>
																				<hx:commandExButton
																					type="submit"
																					value="選択"
																					styleClass="commandExButton" id="select"
																					action="#{pc_Ssf00401T02.doSelectAction}">
																				</hx:commandExButton>　
																				<h:outputText styleClass="outputText"
																					id="htmlSeminerKyoinName"
																					style="#{pc_Ssf00401T02.propSeminerKyoinName.style}"
																					value="#{pc_Ssf00401T02.propSeminerKyoinName.stringValue}">
																				</h:outputText>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
												<BR>
												<TABLE  width="668" border="0" cellpadding="0" cellspacing="0">
													<TBODY>
														<TR>
															<TD align="left">
																<TABLE width="569" border="0" cellpadding="0" cellspacing="0" class="">
																	<TBODY>
																		<TR>
																			<TH align="left" style="text-align:left">
																				<h:outputText
																					styleClass="outputText"
																					id="lblListSeminerKyoinCmt1"
																					value="教員一覧">
																				</h:outputText> 
																			</TH>
																			<TD style="text-align:right" align="right" width="20%">
																				<h:outputText
																					styleClass="outputText"
																					id="htmlCountListSeminerKyoin"
																					value="#{pc_Ssf00401T02.propListSeminerKyoin.listCount}">
																				</h:outputText>
																				<h:outputText
																					styleClass="outputText"
																					id="lblCountCmt"
																					value="件">
																				</h:outputText>
																			</TR>
																			<TR>
																				<TD colspan="3" nowrap>
																				<TABLE width="569" border="0" cellpadding="0" cellspacing="0" class="list_table">
																					<TBODY>
																						<TR>
																						<TD>
																						<h:selectManyListbox
																							styleClass="selectManyListbox"
																							id="htmlListSeminerKyoin"
																							size="11"
																							value="#{pc_Ssf00401T02.propListSeminerKyoin.value}"
																							disabled="#{pc_Ssf00401T02.propListSeminerKyoin.disabled}"
																							readonly="#{pc_Ssf00401T02.propListSeminerKyoin.readonly}"
																							style="width:100%">
																							<f:selectItems
																								value="#{pc_Ssf00401T02.propListSeminerKyoin.list}" />
																						</h:selectManyListbox>
																					</TD>
																						</TR>
																				</TBODY>
																				</TABLE>
																					
																				</TD>
																			</TR>
																	</TBODY>
																</TABLE>
															</TD>
															<TD align="center" style="padding: 0 5px;">
																<hx:commandExButton type="submit" value="除外" id="exclusion"
																	styleClass="commandExButton"
																	action="#{pc_Ssf00401T02.doExclusionAction}" style="width:65px"></hx:commandExButton><BR>
																<h:outputText styleClass="note" id="lblExclusionCmt" value="（複数選択可）"></h:outputText><BR><BR>
																<hx:commandExButton type="submit" value="全て除外"
																	id="allExclusion" styleClass="commandExButton"
																	action="#{pc_Ssf00401T02.doAllExclusionAction}" style="width:65px"></hx:commandExButton>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
											</DIV>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="100%" class="button_bar">
								<TBODY>
									<TR>
										<TD>
											<hx:commandExButton 
												type="submit" value="PDF作成"
												styleClass="commandExButton_out" id="pdfout"
												action="#{pc_Ssf00401T02.doPdfOutAction}"
												confirm="#{msg.SY_MSG_0019W}">
											</hx:commandExButton>
											<hx:commandExButton 
												type="submit" value="EXCEL作成"
												styleClass="commandExButton_out" id="excelout"
												action="#{pc_Ssf00401T02.doExcelOutAction}"
												confirm="#{msg.SY_MSG_0027W}">
											</hx:commandExButton>
											<hx:commandExButton 
												type="submit" value="CSV作成"
												styleClass="commandExButton_out" id="csvout"
												action="#{pc_Ssf00401T02.doCsvOutAction}"
												confirm="#{msg.SY_MSG_0020W}">
											</hx:commandExButton>
											<hx:commandExButton
												type="submit" value="出力項目指定"
												styleClass="commandExButton_out"id="setoutput"
												action="#{pc_Ssf00401T02.doSetoutputAction}">
											</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
		<!-- ↑ここにコンポーネントを配置 -->
		</DIV>
		<!--↑column↑--> 
	</DIV>
	<!--↑content↑--> 
	<!-- フッダーインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
</DIV>
<!--↑outer↑-->
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
