<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmi01402.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">


<SCRIPT type="text/javascript">


function func_1(thisObj, thisEvent) {
//use 'thisObj' to refer directly to this component instead of keyword 'this'
//use 'thisEvent' to refer to the event generated instead of keyword 'event'
indirectClick('button1');
}
function func_2(thisObj, thisEvent) {
//use 'thisObj' to refer directly to this component instead of keyword 'this'
//use 'thisEvent' to refer to the event generated instead of keyword 'event'
var changeFlg = document.getElementById('form1:changeFlg');
changeFlg.value = 'true';
}



function func_3(thisObj, thisEvent) {
//use 'thisObj' to refer directly to this component instead of keyword 'this'
//use 'thisEvent' to refer to the event generated instead of keyword 'event'


var changeFlg = document.getElementById('form1:changeFlg');
var changed = changeFlg.value;
if (changed == 'true') {

if ( confirm("編集中のデータが確定されていません。編集中のデータを無効にしてもよろしいですか？")) {
changeFlg.value ='false';
indirectClick('button2');

} 

} else {

indirectClick('button2');
}
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmi01402.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmi01402.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmi01402.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmi01402.screenName}"></h:outputText> <hx:commandExButton
				type="submit" value="Submit" styleClass="commandExButton"
				id="button1" action="#{pc_Kmi01402.doButton1Action}"></hx:commandExButton><hx:commandExButton
				type="submit" value="Submit" styleClass="commandExButton"
				id="button2" action="#{pc_Kmi01402.doButton2Action}"></hx:commandExButton><h:inputHidden
				id="changeFlg" value="#{pc_Kmi01402.propChangeFlg.stringValue}"></h:inputHidden></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<TABLE border="0" cellpadding="0" cellspacing="0" width="70" class="">
				<TBODY>
					<TR>
						<TD align="center" valign="middle" height="11"><hx:commandExButton
							type="submit" value="戻る" styleClass="commandExButton"
							id="returnDisp" action="#{pc_Kmi01402.doReturnDispAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↓ここに戻る／閉じるボタンを配置 --> <!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->


			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="80%">
				<TBODY>
					<TR>
						<TH class="v_a" width="148"><h:outputText styleClass="outputText"
							id="text1" value="#{pc_Kmi01402.propNendo.name}"
							style="#{pc_Kmi01402.propNendo.labelStyle}"></h:outputText></TH>
						<TD width="602"><h:outputText styleClass="outputText" id="text2"
							value="#{pc_Kmi01402.propNendo.stringValue}"
							style="#{pc_Kmi01402.propNendo.style}"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="148"><h:outputText styleClass="outputText"
							id="text3" value="#{pc_Kmi01402.propStuNo.labelName}"
							style="#{pc_Kmi01402.propStuNo.labelStyle}"></h:outputText></TH>
						<TD width="602"><h:outputText styleClass="outputText" id="text4"
							value="#{pc_Kmi01402.propStuNo.stringValue}"
							style="#{pc_Kmi01402.propStuNo.style}"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="148"><h:outputText styleClass="outputText"
							id="text5" value="#{pc_Kmi01402.propName.labelName}"
							style="#{pc_Kmi01402.propName.labelStyle}"></h:outputText></TH>
						<TD width="602"><h:outputText styleClass="outputText" id="text6"
							value="#{pc_Kmi01402.propName.stringValue}"
							style="#{pc_Kmi01402.propName.style}"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<HR noshade width="100%" class="hr">
			<TABLE width="80%" border="0" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
						<TH class="v_d" width="148"><h:outputText styleClass="outputText"
							id="text7" value="#{pc_Kmi01402.propFreTsy.labelName}"
							style="#{pc_Kmi01402.propFreTsy.labelStyle}"></h:outputText></TH>
						<TD colspan="2" width="390"><h:selectOneMenu
							styleClass="selectOneMenu" id="menu1"
							value="#{pc_Kmi01402.propFreTsy.value}"
							disabled="#{pc_Kmi01402.propFreTsy.disabled}" style="width:300px">
							<f:selectItems value="#{pc_Kmi01402.propFreTsy.list}" />
						</h:selectOneMenu><hx:commandExButton type="submit"
							styleClass="commandExButton" id="select"
							disabled="#{pc_Kmi01402.propSelect.disabled}" value="選択"
							action="#{pc_Kmi01402.doSelectAction}"></hx:commandExButton> <hx:commandExButton
							type="submit" value="解除" styleClass="commandExButton" id="unlock"
							disabled="#{pc_Kmi01402.propUnlock.disabled}"
							onclick="return func_3(this, event);"></hx:commandExButton></TD>
					</TR>
					<TR>
						<TH class="v_e" width="148"><h:outputText styleClass="outputText"
							id="text8" value="#{pc_Kmi01402.propListNo.labelName}"
							style="#{pc_Kmi01402.propListNo.labelStyle}"></h:outputText></TH>
						<TD width="390"><hx:commandExButton type="submit"
							value="#{pc_Kmi01402.propToHead.name}"
							styleClass="commandExButton" id="button5"
							disabled="#{pc_Kmi01402.propToHead.disabled}"
							action="#{pc_Kmi01402.doButton5Action}"
							style="height: 17px;
width: 17px;
border-left-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;

border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-repeat:no-repeat;
margin:0px 1px 0px 1px;"></hx:commandExButton><hx:commandExButton
							type="submit" value="#{pc_Kmi01402.propBack.name}"
							styleClass="commandExButton" id="button6"
							disabled="#{pc_Kmi01402.propBack.disabled}"
							action="#{pc_Kmi01402.doButton6Action}"
							style="height: 17px;
width: 17px;
border-left-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;

border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-repeat:no-repeat;
margin:0px 1px 0px 1px;"></hx:commandExButton><h:inputText
							styleClass="inputText" id="htmlInputNo" style="width: 25px"
							value="#{pc_Kmi01402.propListNo.stringValue}"
							disabled="#{pc_Kmi01402.propListNo.disabled}"
							maxlength="#{pc_Kmi01402.propListNo.maxLength}"
							onchange="return func_1(this, event);"></h:inputText><hx:commandExButton
							type="submit" value="#{pc_Kmi01402.propForword.name}"
							styleClass="commandExButton" id="button7"
							disabled="#{pc_Kmi01402.propForword.disabled}"
							action="#{pc_Kmi01402.doButton7Action}"
							style="height: 17px;
width: 17px;
border-left-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;

border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-repeat:no-repeat;
margin:0px 1px 0px 1px;"></hx:commandExButton><hx:commandExButton
							type="submit" value="#{pc_Kmi01402.propToEnd.name}"
							styleClass="commandExButton" id="button8"
							disabled="#{pc_Kmi01402.propToEnd.disabled}"
							action="#{pc_Kmi01402.doButton8Action}"
							style="height: 17px;
width: 17px;
border-left-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;

border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-repeat:no-repeat;
margin:0px 1px 0px 1px;"></hx:commandExButton>
						<hx:commandExButton type="submit" value="新規採番"
							styleClass="commandExButton" id="autosearch"
							disabled="#{pc_Kmi01402.propAutosearch.disabled}"
							action="#{pc_Kmi01402.doAutosearchAction}"></hx:commandExButton></TD>
						<TD colspan=""><h:outputText styleClass="outputText" id="text99"
							style="font-size: 8pt" value="データNo登録件数"></h:outputText><h:outputText
							styleClass="outputText" id="text999" style="font-size: 8pt"
							value="#{pc_Kmi01402.propSearchedNo.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text10" style="font-size: 8pt"
							value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR width="100%" noshade class="hr">
			<CENTER>
			<TABLE width="80%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center" width="80%">
						<DIV class="listScroll" style="height:315px;width: 99%;"
							align="left"><h:dataTable border="0" cellpadding="2"
							cellspacing="0" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Kmi01402.propKmiSnfr.rowClasses}"
							styleClass="meisai_scroll" id="table1" width="98%"
							value="#{pc_Kmi01402.propKmiSnfr.list}" var="varlist"
							first="#{pc_Kmi01402.propKmiSnfr.first}"
							rows="#{pc_Kmi01402.propKmiSnfr.rows}" style="text-align: center">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text120" styleClass="outputText" value="項目№"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text13"
									value="#{varlist.freKomokNo}"></h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="center" name="align" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: right; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText"
										value="#{pc_Kmi01402.propOutputName.name}" id="text11"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text14"
									value="#{varlist.freKomokName}"></h:outputText>
								<f:attribute value="200" name="width" />
								<f:attribute value="text-align: left; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText"
										value="#{pc_Kmi01402.propValue.labelName}" id="htmlValuetitle"
										style="#{pc_Kmi01402.propValue.labelStyle}"></h:outputText>
								</f:facet>
								<h:inputTextarea styleClass="inputTextarea" id="htmlValue"
									value="#{varlist.freValue}"
									onchange="return func_2(this, event);"
									style="#{pc_Kmi01402.propValue.style} width :100% "></h:inputTextarea>
								<f:attribute value="440" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD height="58" align="right" valign="bottom" width="100%">
						<DIV align="right">
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD align="center" valign="middle"><hx:commandExButton
										type="submit" value="確定" styleClass="commandExButton_dat"
										id="register" action="#{pc_Kmi01402.doRegisterAction}"
										disabled="#{pc_Kmi01402.propRegister.disabled}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton> <hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Kmi01402.doDeleteAction1}"
										disabled="#{pc_Kmi01402.propDelete.disabled}"
										confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</DIV>

						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

