<%-- 
	卒業生情報登録（出学情報登録）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob01512.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01512.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  if (confirm(messageCreate(id, args))) {
  	  onChangeData();
  	  return true;
  }
  return false;
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
	var kengenCheckFlg = document.getElementById('form1:htmlHidKengenCheckFlg').value;
	if (kengenCheckFlg == "1"){
		//更新後の学籍が権限なしとなるが、処理続行
		document.getElementById("form1:htmlHidButtonKbnAuth").value = "1";
	} 
	var action = document.getElementById("form1:htmlHidShoriKbn").value;
	indirectClick(action);
}
// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById("form1:htmlHidButtonKbnAuth").value = "0";
	document.getElementById("form1:htmlHidKengenCheckFlg").value = "0";
}

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
	if(changeDataFlg == "1"){
	  return confirm(id);
	}
	return true;
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob01512.onPageLoadBegin}">
<gakuen:itemStateCtrlDef managedbean="pc_Cob01502T01" property="cob01502" />
<gakuen:itemStateCtrl managedbean="pc_Cob01512">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob01512.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob01512.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob01512.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');"
				action="#{pc_Cob01512.doReturnDispAction}"></hx:commandExButton><!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">

			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE class="table" width="850">
							<TBODY>
								<TR align="center" valign="middle">
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblGaksekiCd_head"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.labelName}"
										style="#{pc_Cob01502T01.cob01502.propGaksekiCd.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:inputText styleClass="inputText"
										id="htmlGaksekiCd" size="18"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.value}"
			                            value="#{pc_Cob01502T01.cob01502.propGaksekiCd.value}"
			                            disabled="#{pc_Cob01502T01.cob01502.propGaksekiCd.disabled}"
			                            style="#{pc_Cob01502T01.cob01502.propGaksekiCd.style}"
										onchange="onChangeData();">
									</h:inputText></TD>
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblGakseiName"
										value="#{pc_Cob01502T01.cob01502.propGakseiName.name}"></h:outputText></TH>
									<TD nowrap><h:outputText styleClass="outputText"
										id="htmlName"
										value="#{pc_Cob01502T01.propName.value}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE class="table" width="850">
							<TBODY>
								<TR>
									<TH nowrap class="v_b"><!-- 異動出学種別 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakSbtCmb"
										value="#{pc_Cob01512.propIdoSyutgakSbtCmb.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakSbtCmb.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlIdoSyutgakSbtList" style="width:280px;"
										value="#{pc_Cob01512.propIdoSyutgakSbtCmb.stringValue}"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtCmb.disabled}"
										onchange="onChangeData();">
										<f:selectItems
											value="#{pc_Cob01512.propIdoSyutgakSbtCmb.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit"
										value="選択" styleClass="commandExButton" id="select2"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtCmb.disabled}"
										action="#{pc_Cob01512.doSelectIdoSyutgakSbtMstAction}"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><!-- 異動出学種別区分 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakSbtKbn"
										value="#{pc_Cob01512.propIdoSyutgakSbtKbn.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakSbtKbn.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlIdoSyutgakSbtKbn" style="width:150px;"
										value="#{pc_Cob01512.propIdoSyutgakSbtKbn.stringValue}"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtKbn.disabled}">
										<f:selectItems
											value="#{pc_Cob01512.propIdoSyutgakSbtKbn.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit"
										value="選択" styleClass="commandExButton"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtKbn.disabled}"
										action="#{pc_Cob01512.doSelectIdoSyutgakSbtAction}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="解除"
										styleClass="commandExButton"
										disabled="#{pc_Cob01512.propSbtKbnUnSelect.disabled}"
										action="#{pc_Cob01512.doUnselectIdoSyutgakSbtAction}"></hx:commandExButton>
									</TD>
								</TR>
								<TR align="center">
									<TH nowrap class="v_a"><!-- 異動出学種別コード --> <h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtCd"
										value="#{pc_Cob01512.propIdoSyutgakSbtCd.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakSbtCd.labelStyle}"></h:outputText>
									</TH>
									<TD><h:inputText styleClass="inputText" size="4"
										value="#{pc_Cob01512.propIdoSyutgakSbtCd.stringValue}"
										style="#{pc_Cob01512.propIdoSyutgakSbtCd.style}"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtCd.disabled}"
										maxlength="#{pc_Cob01512.propIdoSyutgakSbtCd.maxLength}"
										onchange="onChangeData();"></h:inputText>
									</TD>
									<TH nowrap class="v_a"><!-- 異動出学種別名称 --> <h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtName"
										value="#{pc_Cob01512.propIdoSyutgakSbtName.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakSbtName.labelStyle}"></h:outputText>
									</TH>
									<TD><h:inputText styleClass="inputText" size="29"
										value="#{pc_Cob01512.propIdoSyutgakSbtName.stringValue}"
										style="#{pc_Cob01512.propIdoSyutgakSbtName.style}"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtName.disabled}"
										maxlength="#{pc_Cob01512.propIdoSyutgakSbtName.maxLength}"
										onchange="onChangeData();"></h:inputText>
									</TD>
								</TR>
								<TR align="center">
									<TH nowrap class="v_a"><!-- 異動出学種別名称英語 --> <h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtNameEng"
										value="#{pc_Cob01512.propIdoSyutgakSbtNameEng.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakSbtNameEng.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										size="95"
										value="#{pc_Cob01512.propIdoSyutgakSbtNameEng.stringValue}"
										style="#{pc_Cob01512.propIdoSyutgakSbtNameEng.style}"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtNameEng.disabled}"
										maxlength="#{pc_Cob01512.propIdoSyutgakSbtNameEng.maxLength}"
										onchange="onChangeData();"></h:inputText>
									</TD>
								</TR>
								<TR align="center">
									<TH nowrap class="v_a"><!-- 異動出学種別対外名称 --> <h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtNameGai"
										value="#{pc_Cob01512.propIdoSyutgakSbtNameGai.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakSbtNameGai.labelStyle}"></h:outputText>
									</TH>
									<TD><h:inputText styleClass="inputText"
										size="29"
										value="#{pc_Cob01512.propIdoSyutgakSbtNameGai.stringValue}"
										style="#{pc_Cob01512.propIdoSyutgakSbtNameGai.style}"
										disabled="#{pc_Cob01512.propIdoSyutgakSbtNameGai.disabled}"
										maxlength="#{pc_Cob01512.propIdoSyutgakSbtNameGai.maxLength}"
										onchange="onChangeData();"></h:inputText>
									</TD>
									
									
									<TH nowrap class="v_a">
										<!-- 満期退学 -->
										<h:outputText styleClass="outputText" id="lblMankiTaigakFlg"
										value="#{pc_Cob01512.propMankiTaigakFlg.labelName}"
										style="#{pc_Cob01512.propMankiTaigakFlg.labelStyle}"></h:outputText>
									</TH>
									<TD>
										<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="htmlMankiTaigakFlg" 
										disabled="#{pc_Cob01512.propMankiTaigakFlg.disabled}"
										onchange="onChangeData();"
										value="#{pc_Cob01512.propMankiTaigakFlg.checked}" 
										onclick="onChangeData();">
										</h:selectBooleanCheckbox>
										<h:outputText styleClass="outputText" id="textMankiTaigakFlg" value="満期退学">
										</h:outputText>
									</TD>
								</TR>
								
								<TR>
									<TH nowrap class="v_c"><!-- 異動出学理由マスタ --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakRiyuCd"
										value="#{pc_Cob01512.propIdoSyutgakRiyuCd.labelName}"></h:outputText>
									</TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlIdoSyutgakRiyuCd" style="width:340px;"
										disabled="#{pc_Cob01512.propIdoSyutgakRiyuCd.disabled}"
										value="#{pc_Cob01512.propIdoSyutgakRiyuCd.stringValue}"
										onchange="onChangeData();">
										<f:selectItems
											value="#{pc_Cob01512.propIdoSyutgakRiyuCd.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit"
										value="選択" styleClass="commandExButton"
										disabled="#{pc_Cob01512.propIdoSyutgakRiyu.disabled}"
										action="#{pc_Cob01512.doIdoSyutgakRiyuChangeAction}"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_d"><!-- 異動出学理由 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakRiyu"
										value="#{pc_Cob01512.propIdoSyutgakRiyu.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakRiyu.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlIdoSyutgakRiyu" size="40"
										disabled="#{pc_Cob01512.propIdoSyutgakRiyu.disabled}"
										style="#{pc_Cob01512.propIdoSyutgakRiyu.style}"
										value="#{pc_Cob01512.propIdoSyutgakRiyu.stringValue}"
										maxlength="#{pc_Cob01512.propIdoSyutgakRiyu.maxLength}"
										onchange="onChangeData();"></h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_e" width="180"><!-- 異動出学年度 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakNendo"
										value="#{pc_Cob01512.propIdoSyutgakNendo.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakNendo.labelStyle}"></h:outputText>
									</TH>
									<TD width="245"><h:inputText styleClass="inputText"
										id="htmlIdoSyutgakNendo" size="4"
										value="#{pc_Cob01512.propIdoSyutgakNendo.dateValue}"
										disabled="#{pc_Cob01512.propIdoSyutgakNendo.disabled}"
										style="#{pc_Cob01512.propIdoSyutgakNendo.style}"
										maxlength="#{pc_Cob01512.propIdoSyutgakNendo.maxLength}"
										onkeydown="onChangeData();">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH nowrap class="v_e" width="180"><!-- 異動出学学期NO --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakGakki"
										value="#{pc_Cob01512.propIdoSyutgakGakki.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakGakki.labelStyle}"></h:outputText>
									</TH>
									<TD width="245"><h:inputText styleClass="inputText"
										id="htmlIdoSyutgakGakki" size="2"
										value="#{pc_Cob01512.propIdoSyutgakGakki.integerValue}"
										disabled="#{pc_Cob01512.propIdoSyutgakGakki.disabled}"
										style="#{pc_Cob01512.propIdoSyutgakGakki.style}"
										maxlength="#{pc_Cob01512.propIdoSyutgakGakki.maxLength}"
										onkeydown="onChangeData();">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_e"><!-- 異動出学学期名称 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakGakkiName"
										value="#{pc_Cob01512.propIdoSyutgakGakkiName.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakGakkiName.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlIdoSyutgakGakkiName" size="15"
										value="#{pc_Cob01512.propIdoSyutgakGakkiName.stringValue}"
										disabled="#{pc_Cob01512.propIdoSyutgakGakkiName.disabled}"
										style="#{pc_Cob01512.propIdoSyutgakGakkiName.style}"
										maxlength="#{pc_Cob01512.propIdoSyutgakGakkiName.maxLength}"
										onchange="onChangeData();">
									</h:inputText></TD>
									<TD colspan="2" class="clear_border"><BR>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_f"><!-- 異動出学願提出日 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakNegaiDate"
										value="#{pc_Cob01512.propIdoSyutgakNegaiDate.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakNegaiDate.labelStyle}"></h:outputText>
									</TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlIdoSyutgakNegaiDate" size="10"
										disabled="#{pc_Cob01512.propIdoSyutgakNegaiDate.disabled}"
										style="#{pc_Cob01512.propIdoSyutgakNegaiDate.style}"
										value="#{pc_Cob01512.propIdoSyutgakNegaiDate.dateValue}"
										onkeydown="onChangeData();">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH nowrap class="v_g"><!-- 異動出学申請結果日 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakKekkaDate"
										value="#{pc_Cob01512.propIdoSyutgakKekkaDate.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakKekkaDate.labelStyle}"></h:outputText>
									</TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlIdoSyutgakKekkaDate" size="10"
										value="#{pc_Cob01512.propIdoSyutgakKekkaDate.dateValue}"
										disabled="#{pc_Cob01512.propIdoSyutgakKekkaDate.disabled}"
										style="#{pc_Cob01512.propIdoSyutgakKekkaDate.style}"
										onkeydown="onChangeData();">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_g"><!-- 異動出学開始日 --> <h:outputText
										styleClass="outputText" id="lblIdoSyutgakStartDate"
										value="#{pc_Cob01512.propIdoSyutgakStartDate.labelName}"
										style="#{pc_Cob01512.propIdoSyutgakStartDate.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlIdoSyutgakStartDate" size="10"
										value="#{pc_Cob01512.propIdoSyutgakStartDate.dateValue}"
										disabled="#{pc_Cob01512.propIdoSyutgakStartDate.disabled}"
										style="#{pc_Cob01512.propIdoSyutgakStartDate.style}"
										onkeydown="onChangeData();">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TD colspan="2" class="clear_border"><BR>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE class="button_bar" align="right" width="850"	cellspacing="0" cellpadding="0">
							<TR>
								<TD align="center"><hx:commandExButton type="submit"
									value="登録" styleClass="commandExButton_dat" id="register"
									confirm="#{msg.SY_MSG_0002W}"
									action="#{pc_Cob01512.doRegisterAction}"></hx:commandExButton>
								<hx:commandExButton type="submit" value="クリア"
									styleClass="commandExButton_etc" id="clear"
									onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '画面項目');"
									action="#{pc_Cob01512.doClearAction}"></hx:commandExButton>
								</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Cob01512.propHidChangeDataFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidShoriKbn"  value="#{pc_Cob01512.propHidShoriKbn.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidButtonKbnAuth" value="#{pc_Cob01512.propHidButtonKbnAuth.integerValue}" ><f:convertNumber /></h:inputHidden>
			<h:inputHidden id="htmlHidKengenCheckFlg" value="#{pc_Cob01512.propHidKengenCheckFlg.stringValue}" ></h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />

		</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>
