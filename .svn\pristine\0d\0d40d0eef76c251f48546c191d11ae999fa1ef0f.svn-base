<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00101T01.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>Xrm00101T01.jsp</TITLE>
<SCRIPT type="text/javascript">

	function onClickChk(id) {
	// 201405チェックボックスをクリック時の処理追加
		indirectClick(id);
	}
	function onClickChkKigen(id) {
	// 201411納入期限チェックボックスをクリック時の処理追加
		indirectClick(id);
	}

	//納付金配当一覧　全選択
	function func_check_on(thisObj, thisEvent) {
		check('htmlPayhList','htmlPayChecked');
	}

	//納付金配当一覧　全解除
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPayhList','htmlPayChecked');
	}

	function fncButtonActive(){

		var codeRegSearch = null;

		//選択ボタン
		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch').value;
		if(codeRegSearch == 1){
			document.getElementById('form1:search').disabled = true;

		} else {
			document.getElementById('form1:unselect').disabled = true;
			document.getElementById('form1:pdfout').disabled = true;
		}


		//スクロールバーの位置を保持
		window.attachEvent('onload', endload);		

	}
	function endload() {
		//スクロールバーの位置を保持
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}

	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('search');
	}
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}

	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncButtonActive();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm00101T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm00101T01.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrm00101T01.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm00101T01.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV id="content">
			<DIV class="column" align="center">

			<TABLE width="900px">
				<TBODY>
					<TR>
						<td>
						<TABLE width="100%" border="0" class="table">
							<TBODY>

								<TR>
									<!-- 発行日付 -->
									<TH nowrap class="v_a" width="100px"><h:outputText
										styleClass="outputText" id="lblHakkouDate"
										value="#{pc_Xrm00101T01.propHakkouDate.labelName}"
										style="#{pc_Xrm00101T01.propHakkouDate.labelStyle}">
									</h:outputText></TH>
									<TD width="400px"><h:inputText styleClass="inputText"
										id="htmlHakkouDate"
										value="#{pc_Xrm00101T01.propHakkouDate.dateValue}"
										style="#{pc_Xrm00101T01.propHakkouDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<!-- 通信欄 -->
									<TD rowspan=3>
									<TABLE width="300" border="0" class="table">
										<TBODY>
											<TR>
												<TH align="center" nowrap class="v_a" width="150px"><h:outputText
													styleClass="outputText" id="lblTsushinText"
													value="#{pc_Xrm00101T01.propTsushinText.labelName}"
													style="#{pc_Xrm00101T01.propTsushinText.labelStyle}">
												</h:outputText></TH>
												<TD width="*"><h:inputTextarea styleClass="inputTextarea"
													id="htmlTsushinText" cols="50" rows="6"
													disabled="#{pc_Xrm00101T01.propTsushinText.disabled}"
													value="#{pc_Xrm00101T01.propTsushinText.stringValue}"
													readonly="#{pc_Xrm00101T01.propTsushinText.readonly}"
													style="#{pc_Xrm00101T01.propTsushinText.style}">
												</h:inputTextarea></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<!-- 通信区分 -->
									<TH nowrap class="v_a"><h:outputText styleClass="outputText"
										id="lblTushinTxt"
										value="#{pc_Xrm00101T01.propTushinKbn.labelName}"
										style="#{pc_Xrm00101T01.propTushinKbn.labelStyle}">
									</h:outputText></TH>
									<TD valign="middle" width="*"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlTusinKbn"
										onclick="return onClickChk('linkKikanUseChkBox');"
										value="#{pc_Xrm00101T01.propTushinKbn.checked}"
										disabled="#{pc_Xrm00101T01.propTushinKbn.disabled}">
									</h:selectBooleanCheckbox>定型文を利用せず通信欄を直接入力する。</TD>
									<h:commandLink
										styleClass="commandLink"
										id="linkKikanUseChkBox"
										action="#{pc_Xrm00101T01.doChkAction}">
										<h:outputText
										id="htmlLinkKikanUseChkBox"
										styleClass="outputText">
										</h:outputText>
										</h:commandLink>
									
								</TR>
								<TR>
									<!-- 出力区分 -->
									<TH class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblOutPutTxt" value="出力区分" style="">
									</h:outputText></TH>
									<TD nowrap><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="radio1"
										value="#{pc_Xrm00101T01.propOutPutKbn.value}"
										layout="pageDirection" style="}">
										<f:selectItem itemValue="1" itemLabel="請求書を発行する。" />
										<f:selectItem itemValue="2" itemLabel="未出力の請求書を発行する。" />
										<f:selectItem itemValue="3" itemLabel="請求書を再発行する。" />
									</h:selectOneRadio></TD>
								</TR>
								
								<TR>
								<!-- 期限区分 -->
								<TH class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblNonyuTxt"
										value="#{pc_Xrm00101T01.propKigenkbn.labelName}"
										style="">
								</h:outputText></TH>
									<TD valign="middle" width="*"><h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlNonyuKbn"
											onclick="return onClickChkKigen('linkKigenUseChkBox');"
											value="#{pc_Xrm00101T01.propKigenkbn.checked}"
											disabled="#{pc_Xrm00101T01.propKigenkbn.disabled}">
										</h:selectBooleanCheckbox>納入期限を直接入力する。
										<h:commandLink
										styleClass="onClickChkKigen"
										id="linkKigenUseChkBox"
										action="#{pc_Xrm00101T01.doChkKigenAction}">
										<h:outputText
										id="htmlLinkKigenUseChkBox"
										styleClass="outputText">
										</h:outputText>
										</h:commandLink>
										
									</TD>
								<!-- 納入期限 -->	
								<TD>
									<TABLE width="600"  class="table">
									<TBODY>
								<TH width="150px"><h:outputText
										styleClass="outputText" id="lblNonyukigen"
										value="#{pc_Xrm00101T01.propNonyuDate.labelName}"
										style="#{pc_Xrm00101T01.propNonyuDate.labelStyle}">
								</h:outputText></TH>
								<TD width="145px"><h:inputText styleClass="inputText"
										id="nonyuDate" disabled="#{pc_Xrm00101T01.propNonyuDate.disabled}" 
										value="#{pc_Xrm00101T01.propNonyuDate.dateValue}"
										style="#{pc_Xrm00101T01.propNonyuDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
								</h:inputText></TD>
								<!-- 有効期限-->
									
								<TH width="150px><h:outputText
										styleClass="outputText" id="lblYukokigen"
										value="#{pc_Xrm00101T01.propYukoDate.labelName}"
										style="#{pc_Xrm00101T01.propYukoDate.labelStyle}">
								</h:outputText></TH>
								<TD width="145px"><h:inputText styleClass="inputText"
										id="YukoDate"  disabled="#{pc_Xrm00101T01.propYukoDate.disabled}" 
										value="#{pc_Xrm00101T01.propYukoDate.dateValue}"
										style="#{pc_Xrm00101T01.propYukoDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
								</h:inputText></TD>								
								</TBODY>
							</TABLE>
							</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="5px"></TD>
					</TR>

					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR align="left">
									<TD><hx:commandExButton type="submit" value="納付金指定"
										styleClass="tab_head_on" id="htmlPayTab"></hx:commandExButton><hx:commandExButton
										type="submit" value="納付金・学生指定" styleClass="tab_head_off"
										id="htmlPayGakTab"
										action="#{pc_Xrm00101T01.doLinkPayGakTabAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="学生指定" styleClass="tab_head_off"
										id="htmlGakseiTab"
										action="#{pc_Xrm00101T01.doLinkGakseiTabAction}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TD>

									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										class="tab_body">
										<TBODY>
											<TR align="center">
												<TD height="20px">

												<TABLE width="850px">
													<TR>
														<TD height="5px"></TD>
													</TR>
													<TR>
														<TD>

														<TABLE width="100%" border="0" class="table">
															<TBODY>
																<TR>
																	<!-- 学費年度 -->
																	<TH nowrap class="v_a" width="150px"><h:outputText
																		styleClass="outputText" id="lblGhNendo"
																		value="#{pc_Xrm00101T01.propGhNendo.labelName}"
																		style="#{pc_Xrm00101T01.propGhNendo.labelStyle}">
																	</h:outputText></TH>
																	<TD valign="middle" width="*" colspan=2><h:inputText
																		styleClass="inputText" id="htmlGhNendo"
																		value="#{pc_Xrm00101T01.propGhNendo.dateValue}"
																		maxlength="#{pc_Xrm00101T01.propGhNendo.maxLength}"
																		style="#{pc_Xrm00101T01.propGhNendo.style}"
																		disabled="#{pc_Xrm00101T01.propGhNendo.disabled}"
																		size="4">
																		<hx:inputHelperAssist imeMode="inactive"
																			errorClass="inputText_Error" promptCharacter="_" />
																		<f:convertDateTime pattern="yyyy" />
																	</h:inputText></TD>
																	<TD rowspan=3><hx:commandExButton type="submit"
																		value="選択" styleClass="cmdBtn_dat_s" id="search"
																		action="#{pc_Xrm00101T01.doSearchAction}"
																		disabled="#{pc_Xrm00101T01.propGhNendo.disabled}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="解除" styleClass="cmdBtn_etc_s" id="unselect"
																		action="#{pc_Xrm00101T01.doUnselectAction}"
																		disabled="#{!pc_Xrm00101T01.propGhNendo.disabled}">
																	</hx:commandExButton></TD>
																</TR>
																<%--納付金種別--%>
																<TR>
																	<TH width="150px" nowrap class="v_c"><h:outputText
																		styleClass="outputText" id="lblSyubetuChkList"
																		value="#{pc_Xrm00101T01.propChkListLbl.name}"
																		style="#{pc_Xrm00101T01.propChkListLbl.labelStyle}">
																	</h:outputText></TH>

																	<TD width="550" nowrap class="clear_border"><h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox"
																		id="htmlChkListSeika"
																		value="#{pc_Xrm00101T01.propChkListSeika.checked}"
																		disabled="#{pc_Xrm00101T01.propChkListSeika.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="lblChkListSeika"
																		value="#{pc_Xrm00101T01.propChkListSeika.name}"
																		style="#{pc_Xrm00101T01.propChkListSeika.style}">
																	</h:outputText> <h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox"
																		id="htmlChkListKamokutori"
																		value="#{pc_Xrm00101T01.propChkListKamokutori.checked}"
																		disabled="#{pc_Xrm00101T01.propChkListKamokutori.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="lblChkListKamokutorir"
																		value="#{pc_Xrm00101T01.propChkListKamokutori.name}"
																		style="#{pc_Xrm00101T01.propChkListKamokutori.style}">
																	</h:outputText> <h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox"
																		id="htmlChkListToitu"
																		value="#{pc_Xrm00101T01.propChkListToitu.checked}"
																		disabled="#{pc_Xrm00101T01.propChkListToitu.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="lblChkListToitu"
																		value="#{pc_Xrm00101T01.propChkListToitu.name}"
																		style="#{pc_Xrm00101T01.propChkListToitu.style}">
																	</h:outputText></TD>
																</TR>


																<%--業務コード--%>
																<TR>
																	<TH width="150px" nowrap class="v_b"><h:outputText
																		styleClass="outputText" id="propgyoumCD"
																		value="#{pc_Xrm00101T01.propgyoumCD.name}"
																		style="#{pc_Xrm00101T01.propgyoumCD.name}">
																	</h:outputText></TH>
																	<TD><h:selectOneMenu styleClass="selectOneMenu"
																		id="propgyoumList" style="width:450px;"
																		value="#{pc_Xrm00101T01.propgyoumList.value}"
																		tabindex="7"
																		disabled="#{pc_Xrm00101T01.propgyoumList.disabled}">
																		<f:selectItems
																			value="#{pc_Xrm00101T01.propgyoumList.list}" />
																	</h:selectOneMenu></TD>
																</TR>

															</TBODY>
														</TABLE>

														<TABLE border="0" cellpadding="0" cellspacing="0"
															width="100%">
															<TBODY>
																<TR>
																	<TD align="right"><h:outputText styleClass="outputText"
																		id="htmlCount"
																		value="#{pc_Xrm00101T01.propPayhList.listCount}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblCount" value="件">
																	</h:outputText></TD>
																</TR>
																<TR>
																	<TD>
																	<DIV style="height: 168px; width=100%;" id="listScroll"
																		onscroll="setScrollPosition('htmlHidScroll',this);"
																		class="listScroll"><h:dataTable
																		footerClass="footerClass"
																		rows="#{pc_Xrm00101T01.propPayhList.rows}"
																		rowClasses="#{pc_Xrm00101T01.propPayhList.rowClasses}"
																		headerClass="headerClass" styleClass="meisai_scroll"
																		id="htmlPayhList"
																		value="#{pc_Xrm00101T01.propPayhList.list}"
																		var="varlist" width="850px">
																		<h:column id="column1">
																			<f:facet name="header">
																			</f:facet>
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox"
																				id="htmlPayChecked" value="#{varlist.payChecked}"
																				rendered="#{varlist.rendered}">
																			</h:selectBooleanCheckbox>
																			<f:attribute value="30px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column2">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T01.propPayhListPayCd.labelName}"
																					id="lblPayCd">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmpPayCd" value="#{varlist.payCd}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="95px" name="width" />
																		</h:column>
																		<h:column id="column3">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T01.propPayhListPatternCd.labelName}"
																					id="lblPatternCd">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPatternCd"
																				value="#{varlist.patternCd}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="95px" name="width" />
																		</h:column>
																		<h:column id="column4">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T01.propPayhListBunnoKbnCd.labelName}"
																					id="lblBunnoKbnCd">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlBunnoKbnCd"
																				value="#{varlist.bunnoKbnCd}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="95px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column5">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T01.propPayhListPayName.labelName}"
																					id="lblPayName">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPayName"
																				value="#{varlist.payName}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="*" name="width" />
																		</h:column>
																		<h:column id="column6">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T01.propPayhListBunkatsuNo.labelName}"
																					id="lblBunkatsuNo">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlBunkatsuNo"
																				value="#{varlist.bunkatsuNo}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="65px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column7">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T01.propPayhListPayLimit.labelName}"
																					id="lblPayLimit">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPayLimit"
																				value="#{varlist.payLimit}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="70px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																	</h:dataTable></DIV>
																	</TD>
																</TR>
																<TR>
																	<TD>
																	<TABLE border="0" cellpadding="2" cellspacing="0"
																		class="meisai_scroll" width="100%">
																		<TBODY>
																			<TR>
																				<TD class="footerClass">
																				<TABLE class="panelBox">
																					<TBODY>
																						<TR>
																							<TD><%-- 全選択・全解除 --%> <hx:jspPanel id="jspPanel1">
																								<INPUT type="button" name="check" value="on"
																									onclick="return func_check_on(this, event);"
																									class="check">
																								<INPUT type="button" name="uncheck" value="off"
																									onclick="return func_check_off(this, event);"
																									class="uncheck">
																							</hx:jspPanel></TD>
																						</TR>
																					</TBODY>
																				</TABLE>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
													<TR>
														<TD height="10px"></TD>
													</TR>
													<TR>
														<TD>

														<TABLE border="0" cellpadding="0" cellspacing="0"
															class="table" width="100%">
															<TBODY>
																<TR>
																	<!-- 就学種別 -->
																	<TH><h:outputText styleClass="outputText"
																		id="lblSyugakSbtCd"
																		value="#{pc_Xrm00101T01.propSyugakSbt.labelName}"
																		style="#{pc_Xrm00101T01.propSyugakSbt.labelStyle}">
																	</h:outputText></TH>
																	<TD width="225" colspan="3"><h:selectOneMenu
																		styleClass="selectOneMenu" id="htmlSyugakSbtCd"
																		disabled="#{pc_Xrm00101T01.propSyugakSbt.disabled}"
																		value="#{pc_Xrm00101T01.propSyugakSbt.value}"
																		style="width:210px;">
																		<f:selectItems
																			value="#{pc_Xrm00101T01.propSyugakSbt.list}" />
																	</h:selectOneMenu> <h:commandLink
																		styleClass="commandLink" id="linkSyugakSbt"
																		style="visibility:hidden"
																		action="#{pc_Xrm00101T01.doLinkSyugakSbtAction}">
																		<h:outputText id="htmlLinkSyugakSbt"
																			styleClass="outputText">
																		</h:outputText>
																	</h:commandLink></TD>
																</TR>
																<TR>
																	<TH width="150px" nowrap class="v_c" height="21"><!-- 学年 -->
																	<h:outputText styleClass="outputText" id="lblGakunen"
																		value="#{pc_Xrm00101T01.propGakunen.labelName}">
																	</h:outputText></TH>
																	<TD nowrap valign="middle" width="250px" colspan="3"><h:selectOneMenu
																		styleClass="selectOneMenu" id="htmlGakunen"
																		value="#{pc_Xrm00101T01.propGakunen.value}">
																		<f:selectItems
																			value="#{pc_Xrm00101T01.propGakunen.list}" />
																	</h:selectOneMenu></TD>

																</TR>

																<TR>
																	<TH width="150px" nowrap class="v_e" height="21"><!-- 所属学科組織 -->
																	<h:outputText styleClass="outputText" id="lblSzkGakka"
																		value="#{pc_Xrm00101T01.propSzkGakka.labelName}">
																	</h:outputText></TH>
																	<TD nowrap valign="middle" width="700px" colspan="3"><h:selectOneMenu
																		styleClass="selectOneMenu" id="htmlSzkGakka"
																		value="#{pc_Xrm00101T01.propSzkGakka.value}">
																		<f:selectItems
																			value="#{pc_Xrm00101T01.propSzkGakka.list}" />
																	</h:selectOneMenu></TD>
																</TR>




																<TR>
																	<TH width="150px" class="v_c"><!-- 異動種別 --> <h:outputText
																		styleClass="outputText" id="lblIdoInfo"
																		value="#{pc_Xrm00101T01.propIdoSbtList.labelName}"
																		style="#{pc_Xrm00101T01.propIdoSbtList.labelStyle}">
																	</h:outputText></TH>
																	<TD nowrap valign="middle" width="250"><h:selectOneMenu
																		styleClass="selectOneMenu" id="htmlIdoSbt"
																		value="#{pc_Xrm00101T01.propIdoSbtList.value}">
																		<f:selectItems
																			value="#{pc_Xrm00101T01.propIdoSbtList.list}" />
																	</h:selectOneMenu></TD>


																	<TH nowrap class="v_a" align="left" width="150px"><!-- 異動日付 -->
																	<h:outputText styleClass="outputText" id="htmlIdoDate1"
																		value="#{pc_Xrm00101T01.propIdoDate.name}"
																		style="#{pc_Xrm00101T01.propIdoDate.style}">
																	</h:outputText></TH>
																	<TD width="*">
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		width="100%">
																		<TBODY>
																			<TR>
																				<TD class="clear_border" width="115px"><h:inputText
																					styleClass="inputText" id="htmlIdoDateStart"
																					value="#{pc_Xrm00101T01.propIdoDateStart.dateValue}"
																					style="#{pc_Xrm00101T01.propIdoDateStart.style}"
																					size="11">
																					<f:convertDateTime />
																					<hx:inputHelperDatePicker />
																					<hx:inputHelperAssist errorClass="inputText_Error"
																						promptCharacter="_" />
																				</h:inputText></TD>
																				<TD class="clear_border" width="25px"><h:outputText
																					styleClass="outputText" id="textIdo" value="～">
																				</h:outputText></TD>
																				<TD class="clear_border" width="*"><h:inputText
																					styleClass="inputText" id="htmlIdoDateEnd"
																					value="#{pc_Xrm00101T01.propIdoDateEnd.dateValue}"
																					style="#{pc_Xrm00101T01.propIdoDateEnd.style}"
																					size="11">
																					<f:convertDateTime />
																					<hx:inputHelperDatePicker />
																					<hx:inputHelperAssist errorClass="inputText_Error"
																						promptCharacter="_" />
																				</h:inputText></TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																</TR>


															</TBODY>
														</TABLE>
														</TD>
													</TR>
													<TR>
														<TD height="10px"></TD>
													</TR>
													<TR>
														<TD height="10px"></TD>
													</TR>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="100%" border="0" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD width="100%"><font color="#FF0000">CSVは三菱UFJファクターに必ず送信する事。</font>
									</TD>
								</TR>
								<TR>
									<TD width="100%"><hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_out" id="pdfout"
										confirm="#{msg.SY_MSG_0001W}"
										action="#{pc_Xrm00101T01.doPdfoutAction}">
									</hx:commandExButton>&nbsp;</TD>
								</TR>
							</TBODY>
						</TABLE>

						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<h:inputHidden value="#{pc_Xrm00101T01.propPayhList.scrollPosition}"
				id="htmlHidScroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm00101T01.propActiveControlSearch.value}"
				id="htmlActiveControlSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm00101T01.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
