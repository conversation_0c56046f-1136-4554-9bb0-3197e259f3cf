<%--
 * GPA計算（一括指定）


 * Kmg02001T01.jsp
 * liuJB
 * 作成日: 2005/11/22
 * version 1.0
 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmg02001T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<HTML>
<HEAD>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmg02001T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmg02001T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmg02001T01.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmg02001T01.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmg02001T01.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				styleClass="outputText" id="htmlMessage"
				value="#{requestScope.DISPLAY_INFO.displayMessage}" escape="false"></h:outputText>
			</FIELDSET>
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　</DIV>
			<!--↓content↓-->
			<DIV id="content"><!-- ↓ここにコンポーネントを配置 -->
			<DIV class="column">
			<TABLE border="0" cellpadding="0" width="650">
				<TBODY>
					<TR>
						<TD width="650">

						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblRegistRangeNumber"
										value="#{pc_Kmg02001T01.kmg02001.propRegistRangeNumber.labelName}"
										style="#{pc_Kmg02001T01.kmg02001.propRegistRangeNumber.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:outputText styleClass="outputText"
										id="htmlRegistRangeNumberFirst" value="小数点第"></h:outputText> <h:inputText
										styleClass="inputText" id="htmlRegistRangeNumber"
										value="#{pc_Kmg02001T01.kmg02001.propRegistRangeNumber.integerValue}"
										style="#{pc_Kmg02001T01.kmg02001.propRegistRangeNumber.style}"
										size="3">
										<f:convertNumber type="number" pattern="0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />

									</h:inputText> <h:outputText styleClass="outputText"
										id="htmlRegistRangeNumberLast" value="位までを計算します"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_b" width="180"><h:outputText
										styleClass="outputText" id="lblMarumeKbn"
										value="#{pc_Kmg02001T01.kmg02001.propMarumeKbn.labelName}"
										style="#{pc_Kmg02001T01.kmg02001.propMarumeKbn.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlMarumeKbn"
										value="#{pc_Kmg02001T01.kmg02001.propMarumeKbn.stringValue}"
										style="#{pc_Kmg02001T01.kmg02001.propMarumeKbn.style}">
										<f:selectItem itemValue="0" itemLabel="四捨五入" />
										<f:selectItem itemValue="1" itemLabel="切り捨て" />
										<f:selectItem itemValue="2" itemLabel="切り上げ" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>

						<BR>
						</TD>
					</TR>
					<TR>
						<TD align="left"><br>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="140">
							<TBODY>
								<TR>
									<TD width="70" nowrap align="center" valign="middle"><hx:commandExButton
										type="button" value="一括指定" styleClass="tab_head_on"
										id="ikkatsuSitei">
									</hx:commandExButton></TD>
									<TD width="70" nowrap align="center" valign="middle"
										class="tab_head_off"><hx:commandExButton type="submit"
										value="学生指定" styleClass="tab_head_off" id="doChangeTab2Action"
										action="#{pc_Kmg02001T01.doChangeTab2Action}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="650" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="tab_body"
							width="100%" height="330px">
							<TBODY>
								<TR>
									<TD valign="top">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										class="table" style="margin-top:10px">
										<TBODY>
											<TR>
												<TH class="v_a" width="180"><h:outputText
													styleClass="outputText" id="lblKanriBusyo"
													value="#{pc_Kmg02001T01.propKanriBusyo.labelName}"
													style="#{pc_Kmg02001T01.propKanriBusyo.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneMenu
													styleClass="selectOneMenu" id="htmlKanriBusyo" 
													value="#{pc_Kmg02001T01.propKanriBusyo.stringValue}"
													style="#{pc_Kmg02001T01.propKanriBusyo.style};width:410px">
													<f:selectItems
														value="#{pc_Kmg02001T01.propKanriBusyo.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_b" width="180"><h:outputText
													styleClass="outputText" id="lblSzkGakkaCd"
													value="#{pc_Kmg02001T01.propSzkGakkaCd.labelName}"
													style="#{pc_Kmg02001T01.propSzkGakkaCd.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneMenu
													styleClass="selectOneMenu" id="htmlSzkGakkaCd"
													value="#{pc_Kmg02001T01.propSzkGakkaCd.stringValue}"
													style="#{pc_Kmg02001T01.propSzkGakkaCd.style};width:460px">
													<f:selectItems
														value="#{pc_Kmg02001T01.propSzkGakkaCd.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_c" width="180"><h:outputText
													styleClass="outputText" id="lblKarikyuramu"
													value="#{pc_Kmg02001T01.propKarikyuramu.labelName}"
													style="#{pc_Kmg02001T01.propKarikyuramu.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneListbox
													styleClass="selectOneMenu" id="htmlKarikyuramu" size="1"
													value="#{pc_Kmg02001T01.propKarikyuramu.stringValue}"
													style="#{pc_Kmg02001T01.propKarikyuramu.style};width:460px">
													<f:selectItems
														value="#{pc_Kmg02001T01.propKarikyuramu.list}" />
												</h:selectOneListbox></TD>
											</TR>
											<TR>
												<TH class="v_d" width="180"><h:outputText
													styleClass="outputText" id="lblGakunen"
													value="#{pc_Kmg02001T01.propGakunen.labelName}"
													style="#{pc_Kmg02001T01.propGakunen.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneListbox
													styleClass="selectOneMenu" id="htmlGakunen" size="1"
													value="#{pc_Kmg02001T01.propGakunen.stringValue}"
													style="#{pc_Kmg02001T01.propGakunen.style};width:140px">
													<f:selectItems value="#{pc_Kmg02001T01.propGakunen.list}" />
												</h:selectOneListbox></TD>
											</TR>
											<TR>
												<TH class="v_e" width="180"><h:outputText
													styleClass="outputText" id="lblSemesuta"
													value="#{pc_Kmg02001T01.propSemesuta.labelName}"
													style="#{pc_Kmg02001T01.propSemesuta.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneListbox
													styleClass="selectOneMenu" id="htmlSemesuta" size="1"
													value="#{pc_Kmg02001T01.propSemesuta.stringValue}"
													style="#{pc_Kmg02001T01.propSemesuta.style};width:140px">
													<f:selectItems value="#{pc_Kmg02001T01.propSemesuta.list}" />
												</h:selectOneListbox></TD>
											</TR>

										</TBODY>
									</TABLE>

									<BR>
									<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
										class="button_bar">
										<TBODY>
											<TR>
												<TD align="center" height="8"><hx:commandExButton
													type="submit" value="実行" styleClass="commandExButton_dat"
													id="ikatuJikou"
													action="#{pc_Kmg02001T01.doIkatuJikouAction}"
													confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>

						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑--></DIV>
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
