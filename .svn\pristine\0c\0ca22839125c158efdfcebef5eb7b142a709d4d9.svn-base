<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghc00901T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
	<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
	<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<META name="GENERATOR" content="IBM Software Development Platform">
	<META http-equiv="Content-Style-Type" content="text/css">
	<TITLE>Ghc00901T05.jsp</TITLE>
	<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
	<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
	<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
	
	<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
	<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
	<SCRIPT type="text/javascript">
		function doGakuseiAjax(thisObj, targetLabel1, targetLabel2){
		// 学生名称を取得する
		
		    //学生氏名取得Ajax呼び出し
		    funcAjaxGaksekiGakusekiCd(thisObj, '0', targetLabel1)
		    funcAjaxGaksekiGakusekiCd(thisObj, '0', targetLabel2)
		
		    return true;
		}
		
		function confirmOk() {
		// 「はい」が選択された場合の処理
		
		    indirectClick("linkClear");
		}
		
		function confirmCancel() {
		// 「いいえ」が選択された場合の処理
		
		}
		
		//学費学生検索画面へ遷移
		function openPGhz0301Window() {
		    openPGhz0301("<%=com.jast.gakuen.rev.gh.PGhz0301.getWindowOpenOption() %>");
		     return true;
		}
	</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghc00901T05.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">

		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />

		<!-- ヘッダーへのデータセット領域 -->
		<div style="display:none;">
			<hx:commandExButton type="submit" value="閉じる"
			    styleClass="commandExButton" id="closeDisp"
			    action="#{pc_Ghc00901T05.doCloseDispAction}">
			</hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghc00901T05.funcId}"></h:outputText>
			<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
			<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghc00901T05.screenName}"></h:outputText>
		</div>

		<!--↓outer↓-->
		<DIV class="outer">
			<FIELDSET class="fieldset_err">
				<LEGEND>エラーメッセージ</LEGEND>
				<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				    styleClass="outputText" escape="false">
				</h:outputText>
			</FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">

    				<TABLE border="0" cellpadding="5">
        				<TBODY>
            				<TR>
                				<TD width="900px">
                    				<TABLE class="table" width="100%">
                        				<TBODY>
                            				<TR align="center" valign="middle">
                                				<TH nowrap class="v_a" width="150px">
                                    				<!--学籍番号 -->
                                    				<h:outputText
                                        				styleClass="outputText"
                                        				id="lblGaksekiNo"
                                        				value="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.labelName}"
                                        				style="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.labelStyle}">
                                    				</h:outputText>
                                				</TH>
                                				<TD width="*">
                                    				<h:inputText
                                        				styleClass="inputText"
                                        				id="htmlGaksekiNo"
                                        				size="10"
                                        				maxlength="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.maxLength}"
                                        				disabled="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.disabled}"
                                        				value="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.stringValue}"
                                        				readonly="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.readonly}"
                                        				style="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.style}"
                                        				onblur="return doGakuseiAjax(this, 'form1:htmlHidName', 'form1:htmlNameHd');">
                                    				</h:inputText>
                                    				<hx:commandExButton
                                        				type="submit"
                                        				styleClass="commandExButton_search"
                                        				id="btnGakusekiF"
                                        				action="#{pc_Ghc00901T01.doPopGakSearchAction}"
                                        				onclick="return openPGhz0301Window();"
                                        				disabled="#{pc_Ghc00901T01.ghc00901.propGakusekiNo.disabled}">
                                    				</hx:commandExButton>
                                    				<h:inputText
                                        				styleClass="likeOutput"
                                        				id="htmlNameHd" size="61"
                                        				readonly="#{pc_Ghc00901T01.ghc00901.propName.readonly}"
                                        				value="#{pc_Ghc00901T01.ghc00901.propName.stringValue}"
                                        				tabindex="-1">
                                    				</h:inputText>
                                				</TD>
                            				</TR>
                        				</TBODY>
                    				</TABLE>
                    				<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
                        				<TBODY>
                            				<TR align="right">
                                				<TD align="center">
                                    				<hx:commandExButton
                                        				type="submit"
                                        				value="検　索"
                                        				styleClass="commandExButton_dat"
                                        				id="search"
                                        				action="#{pc_Ghc00901T05.doSearchAction}"
                                        				disabled="#{pc_Ghc00901T01.ghc00901.propSearch.disabled}">
                                    				</hx:commandExButton>
                                    				<hx:commandExButton
                                        				type="submit"
                                        				value="解　除"
                                        				styleClass="commandExButton_etc"
                                        				id="unselect"
                                        				action="#{pc_Ghc00901T05.doUnselectAction}"
                                        				disabled="#{pc_Ghc00901T01.ghc00901.propUnSelect.disabled}">
                                    				</hx:commandExButton>
                                				</TD>
                            				</TR>
                        				</TBODY>
                    				</TABLE>
                    				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
                        				<TBODY>
                        					<TR>
                            					<TD height="20px"></TD>
                        					</TR>
                        				</TBODY>
                    				</TABLE>
                    				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
                        				<TBODY>
                            				<TR align="left">
                                				<TD>
                                    				<hx:commandExButton
                                        				type="submit"
                                        				value="#{pc_Ghc00901T01.ghc00901.propTabNameKihon.stringValue}"
                                        				styleClass="tab_head_off"
                                        				id="link01"
                                        				action="#{pc_Ghc00901T05.doLink01Action}"></hx:commandExButton><hx:commandExButton
                                        				type="submit"
                                        				value="#{pc_Ghc00901T01.ghc00901.propTabNameShozoku.stringValue}"
                                        				styleClass="tab_head_off"
                                        				id="link02"
                                        				action="#{pc_Ghc00901T05.doLink02Action}"></hx:commandExButton><hx:commandExButton
                                        				type="submit"
                                        				value="#{pc_Ghc00901T01.ghc00901.propTabNameAddr.stringValue}"
                                        				styleClass="tab_head_off"
                                        				id="link03"
                                        				action="#{pc_Ghc00901T05.doLink03Action}"></hx:commandExButton><hx:commandExButton
                                        				type="submit"
                                        				value="#{pc_Ghc00901T01.ghc00901.propTabNameKAddr.stringValue}"
                                        				styleClass="tab_head_off"
                                        				id="link04"
                                        				action="#{pc_Ghc00901T05.doLink04Action}"></hx:commandExButton><hx:commandExButton
                                        				type="submit"
                                        				value="#{pc_Ghc00901T01.ghc00901.propTabNameHsy.stringValue}"
                                        				styleClass="tab_head_on"
                                        				id="htmlHoshoninTab">
                                    				</hx:commandExButton>
                               		 			</TD>
                            				</TR>
                            				<TR>
                                				<TD>
                                    				<TABLE class="tab_body" border="1" cellpadding="20" cellspacing="0" width="100%">
                                        				<TBODY>
                                            				<TR>
                                                				<TD width="100%">
                                                    				<div style="height: 400px">
                                                        				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
                                                            				<TR>
                                                                				<TD height="20px"></TD>
                                                            				</TR>
                                                        				</TABLE>
                                                        				<TABLE border="0" cellpadding="0" cellspacing="0" width="850px">
                                                            				<TBODY>
                                                                				<TR>
                                                                    				<TD align="right">
                                                                        				<h:outputText styleClass="outputText"
                                                                            				id="text1" value="#{pc_Ghc00901T05.propHsylist.listCount}">
                                                                        				</h:outputText>
                                                                        				<h:outputText
                                                                            				styleClass="outputText" id="text2" value="件">
                                                                        				</h:outputText>
                                                                    				</TD>
                                                                				</TR>
                                                                				<TR>
                                                                    				<TD align="left">
                                                                        				<div class="listScroll" onscroll="setScrollPosition('scrollHsy',this);" style="height: 114px">
                                                                            				<h:dataTable
																								rowClasses="#{pc_Ghc00901T05.propHsylist.rowClasses}"
																								headerClass="headerClass"
                                                                                				footerClass="footerClass"
                                                                                				styleClass="meisai_scroll"
                                                                                				rows="#{pc_Ghc00901T05.propHsylist.rows}"
                                                                                				id="htmlHsyList"
                                                                                				var="varlist"
                                                                                				value="#{pc_Ghc00901T05.propHsylist.list}"
                                                                                				width="830px">
                                                                                				<h:column id="column1">
                                                                                					<f:facet name="header">
                                                                                        				<h:outputText
                                                                                            				id="lblHsyNo_head"
                                                                                            				styleClass="outputText"
                                                                                            				value="保証人コード">
                                                                                        				</h:outputText>
                                                                                					</f:facet>
                                                                                    				<f:attribute value="150px" name="width" />
                                                                                    				<h:outputText
                                                                                        				styleClass="outputText"
                                                                                        				id="lblHsyNo_list"
                                                                                        				value="#{varlist.hsyNo}">
                                                                                    				</h:outputText>
                                                                                				</h:column>
                                                                                				<h:column id="column2">
                                                                                					<f:facet name="header">
                                                                                        				<h:outputText
                                                                                            				styleClass="outputText"
                                                                                            				id="lblHsySbtName_head"
                                                                                            				value="保証人種別">
                                                                                        				</h:outputText>
                                                                                					</f:facet>
                                                                                    				<h:outputText
                                                                                        				styleClass="outputText"
                                                                                        				id="lblHsySbtName_list"
                                                                                        				value="#{varlist.hsySbtName.displayValue}"
                                                                                        				title="#{varlist.hsySbtName.value}">
                                                                                    				</h:outputText>
                                                                                    				<f:attribute value="200px" name="width" />
                                                                                    				<f:attribute value="true" name="nowrap" />
                                                                                				</h:column>
                                                                                				<h:column id="column3">
                                                                                					<f:facet name="header">
                                                                                        				<h:outputText
                                                                                            				styleClass="outputText"
                                                                                            				id="lblHsyName_head"
                                                                                            				value="保証人氏名">
                                                                                        				</h:outputText>
                                                                                					</f:facet>
                                                                                    				<h:outputText
                                                                                        				styleClass="outputText"
                                                                                        				id="lblHsyName_list"
                                                                                        				value="#{varlist.hsyName.displayValue}"
                                                                                        				title="#{varlist.hsyName.value}">
                                                                                    				</h:outputText>
                                                                                    				<f:attribute value="*" name="width" />
                                                                                    				<f:attribute value="true" name="nowrap" />
                                                                                				</h:column>
                                                                            				</h:dataTable>
                                                                        				</div>
                                                                    				</TD>
                                                                				</TR>
                                                            				</TBODY>
                                                        				</TABLE>

				                                                        <TABLE border="0" cellpadding="0" cellspacing="0" width="850px" class="button_bar">
				                                                            <TBODY>
				                                                                <TR>
				                                                                    <TD nowrap width="100%" align="center">
				                                                                        <hx:commandExButton
				                                                                            type="submit"
				                                                                            id="edit"
				                                                                            value="編　集"
				                                                                            styleClass="commandExButton_dat"
				                                                                            action="#{pc_Ghc00901T05.doEditAction}">
				                                                                        </hx:commandExButton>
				                                                                    </TD>
				                                                                </TR>
				                                                            </TBODY>
				                                                        </TABLE>

				                                                        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				                                                            <TR>
				                                                                <TD height="20px"></TD>
				                                                            </TR>
				                                                        </TABLE>
				                                                 	</div>
                                                    			</TD> 
                                            				</TR>
                                        				</TBODY>
                                    				</TABLE>
                               	 				</TD>
                            				</TR>
                        				</TBODY>
                    				</TABLE>
				                    <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
				                        <TBODY>
				                            <TR align="right">
				                                <TD align="center">
				                                    <hx:commandExButton
				                                        type="submit"
				                                        value="更　新"
				                                        styleClass="commandExButton_dat"
				                                        id="update"
				                                        action="#{pc_Ghc00901T05.doUpdateAction}"
				                                        confirm="#{msg.SY_MSG_0001W}">
				                                    </hx:commandExButton>&nbsp;
				                                    <hx:commandExButton
				                                        type="submit"
				                                        value="削　除"
				                                        styleClass="commandExButton_dat"
				                                        id="delete"
				                                        action="#{pc_Ghc00901T05.doDeleteAction}"
				                                        confirm="#{msg.SY_MSG_0004W}"
				                                        disabled="#{pc_Ghc00901T01.ghc00901.propDelete.disabled}">
				                                    </hx:commandExButton>
				                                </TD>
				                            </TR>
				                        </TBODY>
				                    </TABLE>
                				</TD>
            				</TR>
        				</TBODY>
    				</TABLE>
    				
				<!-- ↑ここにコンポーネントを配置 -->
				</DIV>
			</DIV>
			<!--↑content↑-->
		</DIV>
		<!--↑outer↑-->

		<!-- フッダーインクルード -->
		<jsp:include page ="../inc/footer.jsp" />
		
    	<h:inputHidden
        	id="htmlHidKanriNo"
        	value="#{pc_Ghc00901T01.ghc00901.propHidKanriNo.stringValue}">
    	</h:inputHidden>
    	<h:inputHidden
        	id="htmlHidName"
        	value="#{pc_Ghc00901T01.ghc00901.propName.stringValue}">
    	</h:inputHidden>
	    <h:inputHidden
	        id="htmlHidButtonKbn"
	        value="#{pc_Ghc00901T01.ghc00901.propHidButtonKbn.integerValue}">
	    </h:inputHidden>
	    <h:inputHidden
	        id="htmlHidAction"
	        value="#{pc_Ghc00901T01.ghc00901.propHidAction.stringValue}">
	    </h:inputHidden>
	    <h:inputHidden
	        id="htmlHidGakseiUpdateDate"
	        value="#{pc_Ghc00901T01.ghc00901.propHidGakseiUpdateDate.stringValue}">
	    </h:inputHidden>
	    <h:inputHidden
	        id="htmlHidGaksekiUpdateDate"
	        value="#{pc_Ghc00901T01.ghc00901.propHidGaksekiUpdateDate.stringValue}">
	    </h:inputHidden>
	    <h:inputHidden
	        id="htmlHidGakAtskUpdateDate"
	        value="#{pc_Ghc00901T01.ghc00901.propHidGakAtskUpdateDate.stringValue}">
	    </h:inputHidden>

	</h:form>
	</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
