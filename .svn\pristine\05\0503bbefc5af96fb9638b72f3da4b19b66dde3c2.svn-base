<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea01001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea01001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<style type="text/css">
<!--
.setWidth TD {width: 115px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript">

function func_4(thisObj, thisEvent) {
	// 予算単位名称を取得する
	getYsnTName(thisObj, thisEvent);
	// 編成期間を取得する
	getHenseiKikan(thisObj, thisEvent);
}
function getYsnTName(thisObj, thisEvent) {
	// 予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoAjaxHidden").value;
	args['code2'] = document.getElementById("form1:htmlYsnTCd").value;

	var ajaxUtil = new AjaxUtil();
//	ajaxUtil.getCodeName(servlet, target, args);
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, "callBackMethod1");
}
function callBackMethod1(value) {
	//目的名称
	document.getElementById("form1:htmlYsnTName").innerHTML = value['name'];
	document.getElementById("form1:htmlYsnTName").title = value['name'];
}
function getHenseiKikan(thisObj, thisEvent) {
	// 編成期間を取得する
	var servlet = "rev/ke/YosanShinseiHenseiKikanDspAJAX";
	var target = "form1:htmlHenseiKikan";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoAjaxHidden").value;
	args['code2'] = document.getElementById("form1:htmlYsnTCd").value;
	args['code3'] = "0";

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_5(thisObj, thisEvent) {
	openModalWindow("", "PCog0101", "<%=com.jast.gakuen.rev.co.PCog0101.getWindowOpenOption()%>");
	setTarget("PCog0101");
	return true;
}
function func_6(thisObj, thisEvent) {
	openModalWindow("", "PCog0201", "<%=com.jast.gakuen.rev.co.PCog0201.getWindowOpenOption()%>");
	setTarget("PCog0201");
	return true;
}
function func_7(thisObj, thisEvent) {
	openModalWindow("", "PCog0301", "<%=com.jast.gakuen.rev.co.PCog0301.getWindowOpenOption()%>");
	setTarget("PCog0301");
	return true;
}
function func_8(thisObj, thisEvent) {
	// 目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMokuName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoAjaxHidden").value;
	args['code2'] = document.getElementById("form1:htmlMokuCd").value;

	var ajaxUtil = new AjaxUtil();
//	ajaxUtil.getCodeName(servlet, target, args);
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, "callBackMethod2");
	
}
function callBackMethod2(value) {
	//目的名称
	document.getElementById("form1:htmlMokuName").innerHTML = value['name'];
	document.getElementById("form1:htmlMokuName").title = value['name'];
}
function func_9(thisObj, thisEvent) {
	// 科目名称を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoAjaxHidden").value;
	args['code2'] = document.getElementById("form1:htmlKmkCd").value;

	var ajaxUtil = new AjaxUtil();
//	ajaxUtil.getCodeName(servlet, target, args);
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, "callBackMethod3");
}
function callBackMethod3(value) {
	//科目名称
	document.getElementById("form1:htmlKmkName").innerHTML = value['name'];
	document.getElementById("form1:htmlKmkName").title = value['name'];
}
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea01001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea01001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea01001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea01001.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<hx:commandExButton
		type="submit" value="新規登録" styleClass="commandExButton"
		id="create" action="#{pc_Kea01001.doCreateAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="145" class="v_a"><h:outputText styleClass="outputText"
										id="lblKaikeiNendo"
										value="#{pc_Kea01001.propKaikeiNendoHenseiJotai.name}"
										style="#{pc_Kea01001.propKaikeiNendoHenseiJotai.style}"></h:outputText></TH>
									<TD width="650"><TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD width="141" class="clear_border" nowrap><h:outputText styleClass="outputText"
													id="htmlKaikeiNendoHenseiJotai"
													value="#{pc_Kea01001.propKaikeiNendoHenseiJotai.stringValue}"
													style="#{pc_Kea01001.propKaikeiNendoHenseiJotai.style}"></h:outputText></TD>
												<TD class="clear_border" nowrap><h:outputText styleClass="outputText"
													id="htmlHenseiKikan"
													value="#{pc_Kea01001.propHenseiKikan.stringValue}"
													style="#{pc_Kea01001.propHenseiKikan.style}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblSyoriKbn"
										value="#{pc_Kea01001.propSyoriKbn.name}"
										style="#{pc_Kea01001.propSyoriKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled" styleClass="selectOneRadio setWidth"
										id="htmlSyoriKbn" tabindex="1"
										disabled="#{pc_Kea01001.propSyoriKbn.disabled}"
										readonly="#{pc_Kea01001.propSyoriKbn.readonly}"
										value="#{pc_Kea01001.propSyoriKbn.stringValue}" layout="lineDirection">
										<f:selectItems value="#{pc_Kea01001.propSyoriKbn.list}" /></h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblSknShohiKbn"
										value="#{pc_Kea01001.propSknShohiKbn.name}"
										style="#{pc_Kea01001.propSknShohiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled" styleClass="selectManyCheckbox setWidth"
										id="htmlSknShohiKbn" tabindex="2"
										disabled="#{pc_Kea01001.propSknShohiKbn.disabled}"
										readonly="#{pc_Kea01001.propSknShohiKbn.readonly}"
										value="#{pc_Kea01001.propSknShohiKbn.value}" layout="lineDirection">
										<f:selectItems value="#{pc_Kea01001.propSknShohiKbn.list}" /></h:selectManyCheckbox></TD>
								</TR>
								<TR>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblYsnTCd"
										value="#{pc_Kea01001.propYsnTCd.labelName}"
										style="#{pc_Kea01001.propYsnTCd.labelStyle}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlYsnTCd" size="14" tabindex="3"
												disabled="#{pc_Kea01001.propYsnTCd.disabled}"
												readonly="#{pc_Kea01001.propYsnTCd.readonly}"
												maxlength="#{pc_Kea01001.propYsnTCd.maxLength}"
												style="#{pc_Kea01001.propYsnTCd.style}"
												value="#{pc_Kea01001.propYsnTCd.stringValue}"
												onblur="return func_4(this, event);"></h:inputText><hx:commandExButton
												type="submit" styleClass="commandExButton_search"
												id="searchYsnTCd" tabindex="4"
												action="#{pc_Kea01001.doSearchYsnTCdAction}"
												onclick="return func_5(this, event);"></hx:commandExButton>
											<h:outputText styleClass="outputText"
												id="htmlYsnTName"
												value="#{pc_Kea01001.propYsnTName.stringValue}"
												style="#{pc_Kea01001.propYsnTName.style}"
												title="#{pc_Kea01001.propYsnTName.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblMokuCd"
										value="#{pc_Kea01001.propMokuCd.labelName}"
										style="#{pc_Kea01001.propMokuCd.style}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlMokuCd" size="14" tabindex="5"
												disabled="#{pc_Kea01001.propMokuCd.disabled}"
												readonly="#{pc_Kea01001.propMokuCd.readonly}"
												maxlength="#{pc_Kea01001.propMokuCd.maxLength}"
												style="#{pc_Kea01001.propMokuCd.style}"
												value="#{pc_Kea01001.propMokuCd.stringValue}"
												onblur="return func_8(this, event);"></h:inputText><hx:commandExButton
												type="submit" styleClass="commandExButton_search"
												id="searchMokuCd" tabindex="6"
												action="#{pc_Kea01001.doSearchMokuCdAction}"
												onclick="return func_6(this, event);"></hx:commandExButton>
											<h:outputText styleClass="outputText"
												id="htmlMokuName"
												value="#{pc_Kea01001.propMokuName.stringValue}"
												style="#{pc_Kea01001.propMokuName.style}"
												title="#{pc_Kea01001.propMokuName.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_f"><h:outputText styleClass="outputText"
										id="lblKmkCd"
										value="#{pc_Kea01001.propKmkCd.labelName}"
										style="#{pc_Kea01001.propKmkCd.style}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlKmkCd" size="14" tabindex="7"
												disabled="#{pc_Kea01001.propKmkCd.disabled}"
												readonly="#{pc_Kea01001.propKmkCd.readonly}"
												maxlength="#{pc_Kea01001.propKmkCd.maxLength}"
												style="#{pc_Kea01001.propKmkCd.style}"
												value="#{pc_Kea01001.propKmkCd.stringValue}"
												onblur="return func_9(this, event);"></h:inputText><hx:commandExButton
												type="submit" styleClass="commandExButton_search"
												id="searchKmkCd" tabindex="8"
												action="#{pc_Kea01001.doSearchKmkCdAction}"
												onclick="return func_7(this, event);"></hx:commandExButton>
											<h:outputText styleClass="outputText"
												id="htmlKmkName"
												value="#{pc_Kea01001.propKmkName.stringValue}"
												style="#{pc_Kea01001.propKmkName.style}"
												title="#{pc_Kea01001.propKmkName.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_g"><h:outputText styleClass="outputText"
										id="lblKomokuNaiyo"
										value="#{pc_Kea01001.propKomokuNaiyo.labelName}"
										style="#{pc_Kea01001.propKomokuNaiyo.style}"></h:outputText></TH>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border" width="300"><h:inputText styleClass="inputText"
													id="htmlKomokuNaiyo" size="80" tabindex="9"
													disabled="#{pc_Kea01001.propKomokuNaiyo.disabled}"
													maxlength="#{pc_Kea01001.propKomokuNaiyo.maxLength}"
													readonly="#{pc_Kea01001.propKomokuNaiyo.readonly}"
													style="#{pc_Kea01001.propKomokuNaiyo.style}"
													value="#{pc_Kea01001.propKomokuNaiyo.stringValue}"></h:inputText></TD>
												<TD class="clear_border"><h:outputText styleClass="outputText"
													id="lblKomokuNaiyoType"
													value="（部分一致）"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_a"><h:outputText styleClass="outputText"
										id="lblHyojiKbn"
										value="#{pc_Kea01001.propHyojiKbn.name}"
										style="#{pc_Kea01001.propHyojiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled" styleClass="selectOneRadio setWidth"
										id="htmlHyojiKbn" tabindex="10"
										value="#{pc_Kea01001.propHyojiKbn.stringValue}"
										disabled="#{pc_Kea01001.propHyojiKbn.disabled}"
										readonly="#{pc_Kea01001.propHyojiKbn.readonly}" layout="lineDirection">
										<f:selectItems value="#{pc_Kea01001.propHyojiKbn.list}" /></h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" styleClass="commandExButton_dat"
										id="search" tabindex="11" value="検索" action="#{pc_Kea01001.doSearchAction}"></hx:commandExButton><hx:commandExButton type="submit" styleClass="commandExButton_etc"
										id="cleara" tabindex="12"value="クリア" action="#{pc_Kea01001.doClearaAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 --><h:inputHidden id="htmlKaikeiNendoAjaxHidden"
				value="#{pc_Kea01001.propKaikeiNendoAjaxHidden.stringValue}">
				
			</h:inputHidden><h:inputHidden id="htmlExecutableSearch"
				value="#{pc_Kea01001.propExecutableSearch.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

