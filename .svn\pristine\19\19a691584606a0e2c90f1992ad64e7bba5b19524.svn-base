<%-- 
	カリキュラム学科組織一覧
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob04601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob04601.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob04601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob04601.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob04601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob04601.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="600">
				<TBODY>
					<TR>
						<TH width="170" class="v_a"><h:outputText
							value="#{pc_Cob04601.propTitle.labelName}" id="lblTitle"
							style="#{pc_Cob04601.propTitle.labelStyle}"
							styleClass="outputText"></h:outputText></TH>
						<TD width="430"><h:inputText styleClass="inputText" id="htmlTitle"
							size="60" value="#{pc_Cob04601.propTitle.stringValue}"
							style="#{pc_Cob04601.propTitle.style}"
							maxlength="#{pc_Cob04601.propTitle.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfout"
							confirm="#{msg.SY_MSG_0019W}"
							action="#{pc_Cob04601.doPdfoutAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="CSV作成" styleClass="commandExButton_out"
							id="csvout" confirm="#{msg.SY_MSG_0020W}"
							action="#{pc_Cob04601.doCsvoutAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="出力項目指定" styleClass="commandExButton_out"
							id="setoutput" action="#{pc_Cob04601.doSetoutputAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

