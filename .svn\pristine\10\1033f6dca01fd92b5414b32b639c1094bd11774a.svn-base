<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_NSC_SGN_SSK" name="志願者入試成績" prod_id="NS" description="志願者の教科毎の入試成績情報を持ちます。志願者登録時に志願者受験台帳及び入試選考方法台帳より作成され、|『入試採点一括登録』『入試採点登録』『センター試験成績登録』にて得点が設定されます。|入試成績台帳からは採点簿や各種成績表が出力されます。入試成績台帳は『入試期末処理』にて今年度、期の志願者の入試成績情報を過年度志願・入試成績に登録後、削除されます。">
<STATMENT><![CDATA[
NSC_SGN_SSK
]]></STATMENT>
<COLUMN id="JUKEN_CD" name="受験番号" type="string" length="10" lengthDP="0" byteLength="10" description="受験番号です。"/><COLUMN id="SENKO_HOHO_NO" name="選考方法番号" type="number" length="2" lengthDP="0" byteLength="0" description="選考方法を識別する番号が設定されます。(教科番号）"/><COLUMN id="KMK_SITEI" name="科目指定" type="number" length="2" lengthDP="0" byteLength="0" description="志願者が実際に受験した科目の科目ＮＯが設定されます。任意採点教科で選択しなかった場合はゼロが設定されます。"/><COLUMN id="TOKTEN" name="得点" type="string" length="6" lengthDP="0" byteLength="9" description="志願者の教科毎の得点（素点）です。評定採点の教科は『志願者一括／登録』にて志願者台帳の評定平均値をもとに自動的に換算し設定されます。"/><COLUMN id="KANSAN_TEN" name="換算点" type="number" length="4" lengthDP="1" byteLength="0" description="志願者の教科毎の換算点です。業務設定にて「換算点を使用する」と設定されている場合は、成績集計にて得点ではなく換算点を使用して処理が行われます。"/><COLUMN id="KESEKI_FLG" name="欠席フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="志願者の入学試験への出欠情報です。『欠席者登録』にて設定されます。||"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SYUKEI_FLG" name="集計フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="成績集計の対象となるか否かの情報を持ちます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="MIN_SCORE_FLG" name="必要最低点フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="必要最低点か否かの情報を持ちます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="HENSATI" name="偏差値" type="number" length="5" lengthDP="2" byteLength="0" description="志願者の科目毎の偏差値です。|『成績集計』で値が設定されます。入試要項で「偏差値を計算する」に設定されている教科のみ、偏差値が計算されます。偏差値は、同一入試種別、同一学科組織、同一教科、同一科目毎に計算されます。「換算点を使用する」に設定されており、かつ換算点が入試成績台帳に登録されている場合、換算点で偏差値が求められます。それ以外の場合は素点で偏差値が求められます。"/><COLUMN id="LSN_KBN" name="リスニング区分" type="string" length="1" lengthDP="0" byteLength="1" description="リスニング試験を課すセンター科目の場合、得点中にリスニング科目の得点を含んでいるか否か、またはリスニング試験免除者かを設定します。"><CODE><CASE value="1" display="リスニング含む"/><CASE value="2" display="リスニング含まない"/><CASE value="3" display="リスニング免除者"/></CODE></COLUMN><COLUMN id="SAITEN_FLG" name="採点済フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="『採点一括登録』での採点状況が設定されます。『成績未登録一覧』の対象者チェック及び『成績集計』の実行可否チェックに使用されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN>
</TABLE>
