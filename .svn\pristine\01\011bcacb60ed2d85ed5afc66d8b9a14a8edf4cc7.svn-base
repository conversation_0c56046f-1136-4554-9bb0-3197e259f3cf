<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssa00401T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Sstmp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />

<SCRIPT type="text/javascript">
function confirmOk() {
    // 確認メッセージでＯＫ押下時、しきい値フラグオンして再検索
	document.getElementById('form1:htmlExecutableSearch').value = "1";	
	indirectClick('search');	
}			
function confirmCancel() {
    // 確認メッセージでキャンセル押下時、しきい値フラグオフ


	document.getElementById('form1:htmlExecutableSearch').value = "0";	
}
function confirmCancel() {
	alert('実行を中断しました。');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssa00401T05.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<DIV style="display:none;">
	<hx:commandExButton type="submit" value="閉じる"
		styleClass="commandExButton" id="closeDisp"
		action="#{pc_Ssa00401T05.doCloseDispAction}"></hx:commandExButton>
	<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssa00401T05.funcId}"></h:outputText>
	<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
	<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssa00401T05.screenName}"></h:outputText>
</DIV>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
	<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" 
		styleClass="outputText" escape="false">
	</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　</DIV>

<DIV id="content">
<DIV class="column">
	<TABLE border="0" class="table" width="850" cellspacing="0" cellpadding="0">
		<TBODY>
				<TR>
					<TH width="150" class="v_a">
						<h:outputText
							styleClass="outputText"
							id="lblKjnTsyNendo"
							style="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.labelStyle}"
							value="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText
							styleClass="inputText"
							id="htmlKjnTsyNendo"
							size="6"
							style="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.style}"
							value="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.dateValue}"
							disabled="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.readonly}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist
								errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText>
					<h:outputText styleClass="outputText" id="lblNendo" value="年度"></h:outputText></TD>
				</TR>
				<TR>
					<TH class="v_b">
						<h:outputText
							styleClass="outputText"
							id="lblChohyoTitle"
							value="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.labelName}"
							style="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText
							styleClass="inputText"
							id="htmlChohyoTitle"
							size="70"
							value="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.stringValue}"
							style="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.readonly}"
							maxlength="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.maxLength}">
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH nowrap class="v_a"><h:outputText
						styleClass="outputText" id="lblOutput" value="出力データ"></h:outputText></TH>
					<TD>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlGaksekiInfo"
							value="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblGaksekiInfo" value="学籍情報">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlMendanRireki"
							value="#{pc_Ssa00401T01.ssa00401.propMendanRireki.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propMendanRireki.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propMendanRireki.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propMendanRireki.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblMendanRireki" value="面談履歴">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlShushokuInfo"
							value="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblShushokuInfo" value="就職活動情報">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlProfileInfo"
							value="#{pc_Ssa00401T01.ssa00401.propProfileInfo.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propProfileInfo.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propProfileInfo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propProfileInfo.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblProfileInfo" value="プロファイル情報">
						</h:outputText>
					</TD>
				</TR>
		</TBODY>
	</TABLE>
	<TABLE width="850" border="0" cellspacing="0" cellpadding="0" style="margin-top:20px;">
		<TBODY>
				<TR>
					<TD class="" align="left"><hx:commandExButton type="submit"
							value="一括指定" styleClass="tab_head_off" id="btnSsa00401T01"
							action="#{pc_Ssa00401T01.doBtnSsa00401T01Action}" 
							style="width:12%"></hx:commandExButton><hx:commandExButton type="submit"
							value="学生指定" styleClass="tab_head_off" id="btnSsa00401T03"
							action="#{pc_Ssa00401T01.doBtnSsa00401T03Action}" 
							style="width:12%"></hx:commandExButton><hx:commandExButton type="submit"
							value="ゼミ教員指定" styleClass="tab_head_off" id="btnSsa00401T04"
							action="#{pc_Ssa00401T01.doBtnSsa00401T04Action}"
							style="width:12%"></hx:commandExButton><hx:commandExButton type="submit"
							value="企業指定" styleClass="tab_head_on" id="btnSsa00401T05"
							action="#{pc_Ssa00401T01.doBtnSsa00401T05Action}"
							readonly="true" style="width:12%"></hx:commandExButton></TD>
				</TR>
			<TR>
				<TD>
					<TABLE width="850" border="0" cellspacing="0" cellpadding="0" class="tab_body">
						<TBODY>
							<TR>
										<TD align="left">
											<DIV style="height:395px">
										<TABLE width="750" border="0" cellpadding="3" cellspacing="0" class="table" style="margin-top:20px; margin-left:40px;">
											<TBODY>
												<TR>
													<TH class="v_a" width="150"><h:outputText
														styleClass="outputText" id="lblInputFile"
														value="#{pc_Ssa00401T05.propInputFile.labelName}"
														style="#{pc_Ssa00401T05.propInputFile.style}"></h:outputText></TH>
													<TD colspan="3"><hx:fileupload styleClass="fileupload"
														id="htmlInputFile" style="width:490px"
														value="#{pc_Ssa00401T05.propInputFile.value}">
														<hx:fileProp name="fileName"
															value="#{pc_Ssa00401T05.propInputFile.fileName}" />
														<hx:fileProp name="contentType"
															value="#{pc_Ssa00401T05.propInputFile.contentType}" />
														</hx:fileupload><hx:commandExButton type="submit"
													value="取込" styleClass="commandExButton" id="read"
													action="#{pc_Ssa00401T05.doReadAction}"></hx:commandExButton></TD>
												</TR>
												<TR>
													<TH class="v_b" width="150"><h:outputText
														styleClass="outputText" id="lblPreFileName"
														style="#{pc_Ssa00401T05.propPreFileName.labelStyle}"
														value="#{pc_Ssa00401T05.propPreFileName.labelName}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText"
														id="htmlPreFileName"
														value="#{pc_Ssa00401T05.propPreFileName.stringValue}"
														style="#{pc_Ssa00401T05.propPreFileName.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH class="v_c" width="150"><h:outputText
														styleClass="outputText" id="lblKgyCd" value="#{pc_Ssa00401T05.propKgyCd.labelName}" style="#{pc_Ssa00401T05.propKgyCd.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:inputText styleClass="inputText"
														id="htmlKgyCd"
														value="#{pc_Ssa00401T05.propKgyCd.stringValue}" size="18"
														style="#{pc_Ssa00401T05.propKgyCd.style}"
														readonly="#{pc_Ssa00401T05.propKgyCd.readonly}"
														disabled="#{pc_Ssa00401T05.propKgyCd.disabled}"
														maxlength="#{pc_Ssa00401T05.propKgyCd.maxLength}"></h:inputText></TD>
												</TR>
												<TR>
													<TH class="v_d" width="150"><h:outputText
														styleClass="outputText" id="lblKgyName"
														value="#{pc_Ssa00401T05.propKgyName.labelName}"
														style="#{pc_Ssa00401T05.propKgyName.labelStyle}"></h:outputText></TH>
													<TD><h:inputText styleClass="inputText" id="htmlKgyName"
														value="#{pc_Ssa00401T05.propKgyName.stringValue}" size="27"
														style="#{pc_Ssa00401T05.propKgyName.style}"
														readonly="#{pc_Ssa00401T05.propKgyName.readonly}"
														disabled="#{pc_Ssa00401T05.propKgyName.disabled}"
														maxlength="#{pc_Ssa00401T05.propKgyName.maxLength}">
														</h:inputText></TD>
													<TH class="v_e" width="150"><h:outputText
														styleClass="outputText" id="lblKgyNameKana"
														value="#{pc_Ssa00401T05.propKgyNameKana.labelName}"
														style="#{pc_Ssa00401T05.propKgyNameKana.labelStyle}"></h:outputText></TH>
													<TD><h:inputText styleClass="inputText" id="htmlKgyNameKana"
														value="#{pc_Ssa00401T05.propKgyNameKana.stringValue}"
														size="27" style="#{pc_Ssa00401T05.propKgyNameKana.style}"
														readonly="#{pc_Ssa00401T05.propKgyNameKana.readonly}"
														disabled="#{pc_Ssa00401T05.propKgyNameKana.disabled}"
														maxlength="#{pc_Ssa00401T05.propKgyNameKana.maxLength}"></h:inputText></TD>
												</TR>
												<TR>
													<TH class="v_f" width="150"><h:outputText
														styleClass="outputText" id="lblKgyNameRyak"
														value="#{pc_Ssa00401T05.propKgyNameRyak.labelName}"
														style="#{pc_Ssa00401T05.propKgyNameRyak.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:inputText styleClass="inputText"
														id="htmlKgyNameRyak"
														value="#{pc_Ssa00401T05.propKgyNameRyak.stringValue}"
														size="42" style="#{pc_Ssa00401T05.propKgyNameRyak.style}"
														readonly="#{pc_Ssa00401T05.propKgyNameRyak.readonly}"
														disabled="#{pc_Ssa00401T05.propKgyNameRyak.disabled}"
														maxlength="#{pc_Ssa00401T05.propKgyNameRyak.maxLength}"></h:inputText></TD>
												</TR>
												<TR>
													<TH class="v_g" width="150"><h:outputText
														styleClass="outputText" id="lblGyosyuCd"
														value="#{pc_Ssa00401T05.propGyosyuCd.labelName}"
														style="#{pc_Ssa00401T05.propGyosyuCd.labelStyle}"></h:outputText></TH>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlGyosyuCd"
														value="#{pc_Ssa00401T05.propGyosyuCd.value}"
														disabled="#{pc_Ssa00401T05.propGyosyuCd.disabled}"
														readonly="#{pc_Ssa00401T05.propGyosyuCd.readonly}"
														style="#{pc_Ssa00401T05.propGyosyuCd.style}">
														<f:selectItems value="#{pc_Ssa00401T05.propGyosyuCd.list}" />
													</h:selectOneMenu></TD>
													<TH class="v_a" width="150"><h:outputText
														styleClass="outputText" id="lblChikiCd"
														value="#{pc_Ssa00401T05.propChikiCd.labelName}"
														style="#{pc_Ssa00401T05.propChikiCd.labelStyle}"></h:outputText></TH>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlChikiCd" value="#{pc_Ssa00401T05.propChikiCd.value}"
														disabled="#{pc_Ssa00401T05.propChikiCd.disabled}"
														readonly="#{pc_Ssa00401T05.propChikiCd.readonly}"
														style="#{pc_Ssa00401T05.propChikiCd.style}">
														<f:selectItems value="#{pc_Ssa00401T05.propChikiCd.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										<TABLE width="750" border="0" cellpadding="3" cellspacing="0" class="button_bar" style="margin-top:8px; margin-left:40px;">
											<TBODY>
												<TR>
													<TD width="300"></TD>
													<TD><hx:commandExButton type="submit" value="検索"
														styleClass="commandExButton_dat" id="search" action="#{pc_Ssa00401T05.doSearchAction}"></hx:commandExButton></TD>
													<TD width="300" style="text-align:right"><h:outputText styleClass="note"
														id="lblComment" value="※部分一致　企業コード除く"></h:outputText></TD>
												</TR>
											</TBODY>
										</TABLE>
										<TABLE border="0" cellspacing="0" cellpadding="0"
											style="margin-top:15px;" width="750" style="margin-top:20px; margin-left:40px;">
											<TBODY>
												<TR>
													<TD style="text-align:left;">
														<TABLE width="700" border="0" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD width="580">
																		<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
																			<TBODY>
																				<TR>
																					<TH colspan="2"><h:outputText
																						styleClass="outputText" id="lblListKgy"
																						value="#{pc_Ssa00401T05.propListKgy.labelName}"
																						style="#{pc_Ssa00401T05.propListKgy.labelStyle}"></h:outputText></TH>
																				</TR>
																				<TR>
																					<TD colspan="2">
																						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="list_table">
																							<TR>
																								<TD><h:selectManyListbox
																									styleClass="selectManyListbox" id="htmlListKgy"
																									value="#{pc_Ssa00401T05.propListKgy.value}" size="9"
																									style="#{pc_Ssa00401T05.propListKgy.style}"
																									readonly="#{pc_Ssa00401T05.propListKgy.readonly}"
																									disabled="#{pc_Ssa00401T05.propListKgy.disabled}">
																									<f:selectItems value="#{pc_Ssa00401T05.propListKgy.list}" />
																									</h:selectManyListbox>
																								</TD>
																							</TR>
																						</TABLE>	
																					</TD>
																				</TR>
																				<TR>
																					<TD colspan="2" style="text-align:right;"><h:outputText
																						styleClass="outputText" id="lblTotalCnt"
																						value="#{pc_Ssa00401T05.propTotalCnt.labelName}"
																						style="#{pc_Ssa00401T05.propTotalCnt.labelStyle}"></h:outputText><h:outputText
																						styleClass="outputText" id="htmlTotalCnt"
																						value="#{pc_Ssa00401T05.propListKgy.listCount}"
																						style="#{pc_Ssa00401T05.propTotalCnt.style}">
																					</h:outputText><h:outputText styleClass="outputText"
																						id="lblTotalCntUnit" value="件" style="margin-right:10px;"></h:outputText><h:outputText styleClass="outputText"
																						id="lblNormalCnt"
																						value="#{pc_Ssa00401T05.propNormalCnt.labelName}"
																						style="#{pc_Ssa00401T05.propNormalCnt.labelStyle}"></h:outputText><h:outputText
																						styleClass="outputText" id="htmlNormalCnt"
																						value="#{pc_Ssa00401T05.propNormalCnt.value}"
																						style="#{pc_Ssa00401T05.propNormalCnt.style}"></h:outputText><h:outputText
																						styleClass="outputText" id="lblNormalCntUnit"
																						value="件" style="margin-right:10px;"></h:outputText><h:outputText
																						styleClass="outputText" id="lblErrorCnt"
																						value="#{pc_Ssa00401T05.propErrorCnt.labelName}"
																						style="#{pc_Ssa00401T05.propErrorCnt.labelStyle}"></h:outputText><h:outputText
																						styleClass="outputText" id="htmlErrorCnt"
																						value="#{pc_Ssa00401T05.propErrorCnt.value}"
																						style="#{pc_Ssa00401T05.propErrorCnt.style}"></h:outputText><h:outputText
																						styleClass="outputText" id="lblErrorCntUnit"
																						value="件"></h:outputText></TD>
																				</TR>
																			</TBODY>
																		</TABLE>
																	</TD>
															<TD valign="middle" width="120"
																style="text-align:center;"><hx:commandExButton
																type="submit" value="除外" styleClass="commandExButton"
																id="exclusion"
																action="#{pc_Ssa00401T05.doExclusionAction}" style="width:65px">
															</hx:commandExButton><BR>
															<h:outputText styleClass="note" id="lblMultiSelectable"
																value="（複数選択可）"></h:outputText><BR>
															<BR>
															<hx:commandExButton type="submit" value="全て除外"
																styleClass="commandExButton" id="allExclusion"
																action="#{pc_Ssa00401T05.doAllExclusionAction}" style="width:65px"></hx:commandExButton></TD>
														</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</DIV>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
			<TR>
				<TD>
					<TABLE width="100%" class="button_bar">
						<TBODY>
							<TR>
								<TD><hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout"
										action="#{pc_Ssa00401T05.doPdfoutAction}" confirm="#{msg.SY_MSG_0019W}"
										></hx:commandExButton><hx:commandExButton type="submit" value="EXCEL作成"
										styleClass="commandExButton_out" id="excelout"
										action="#{pc_Ssa00401T05.doExceloutAction}" confirm="#{msg.SY_MSG_0027W}"
										></hx:commandExButton><hx:commandExButton type="submit" value="CSV作成"
										styleClass="commandExButton_out" id="csvout"
										action="#{pc_Ssa00401T05.doCsvoutAction}" confirm="#{msg.SY_MSG_0020W}"
										></hx:commandExButton><hx:commandExButton type="submit" value="出力項目指定"
										styleClass="commandExButton_out" id="setoutput"
										action="#{pc_Ssa00401T05.doSetoutputAction}"
										></hx:commandExButton></TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
</DIV>
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
	
<h:inputHidden	value="#{pc_Ssa00401T05.propExecutableSearch.integerValue}" id="htmlExecutableSearch">
<f:convertNumber />
</h:inputHidden>
</DIV>		
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
