<%-- 
	卒業生情報登録（クラブサークル活動実績登録）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob01511.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01511.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

// データテーブル全選択
function listCheck(thisObj, thisEvent) {
  check('htmlClactList', 'htmlCheck');
}

// データテーブル全解除
function listUnCheck(thisObj, thisEvent) {
  uncheck('htmlClactList', 'htmlCheck');
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}


</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob01511.onPageLoadBegin}">
<gakuen:itemStateCtrlDef managedbean="pc_Cob01502T01" property="cob01502" />
<gakuen:itemStateCtrl managedbean="pc_Cob01511">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob01511.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob01511.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob01511.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Cob01511.doReturnDispAction}">
			</hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->			
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE class="table" width="800">
							<TBODY>
								<TR align="center" valign="middle">
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblGaksekiCd_head"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.name}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="htmlGaksekiCd"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.value}">
									</h:outputText></TD>
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblGakseiName"
										value="#{pc_Cob01502T01.cob01502.propGakseiName.name}"></h:outputText></TH>
									<TD nowrap width="300"><h:outputText styleClass="outputText"
										id="htmlGakseiName"
										value="#{pc_Cob01502T01.cob01502.propGakseiName.value}"></h:outputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSotNendo"
										value="#{pc_Cob01502T01.cob01502.propSotNendo.name}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="htmlSotNendo"
										value="#{pc_Cob01502T01.cob01502.propSotNendo.value}"></h:outputText></TD>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSotGakki"
										value="#{pc_Cob01502T01.cob01502.propSotGakki.name}"></h:outputText></TH>
									<TD nowrap><h:outputText styleClass="outputText"
										id="htmlSotGakki"
										value="#{pc_Cob01502T01.cob01502.propSotGakki.value}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
							<TBODY>
								<TR>
									<TD align="right" nowrap class="outputText" width="100%"><h:outputText
										styleClass="outputText" id="lblClactCount" value="#{pc_Cob01511.propClactList.listCount}件"></h:outputText></TD>
								</TR>
								<TR>
									<TD>
										<DIV class="listScroll" id="listScroll" style="height: 150px"
											onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
											border="1" cellpadding="2" cellspacing="0"
											columnClasses="columnClass1" headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Cob01511.propClactList.rowClasses}"
											styleClass="meisai_scroll" id="htmlClactList"
											value="#{pc_Cob01511.propClactList.list}" var="varlist">
											<h:column id="column1">
												<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlCheck" value="#{varlist.delFlg}"></h:selectBooleanCheckbox>
												<f:attribute
													value="text-align: center; vertical-align: middle"
													name="style" />
												<f:attribute value="30" name="width" />
											</h:column>
											<h:column id="columnC1">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Cob01502T17.propLblClubCd.name}"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText"
													value="#{varlist.clubCd}"></h:outputText>
												<f:attribute value="130" name="width" />
											</h:column>
											<h:column id="columnC2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Cob01502T17.propLblClubName.name}"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText"
													value="#{varlist.disp1511ClubName}"
													title="#{varlist.clubName}"></h:outputText>
												<f:attribute value="250" name="width" />
											</h:column>
											<h:column id="columnC3">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Cob01502T17.propLblNendo.name}"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" value="#{varlist.nendo}"></h:outputText>
												<f:attribute value="50" name="width" />
											</h:column>
											<h:column id="columnC4">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Cob01502T17.propLblEdaban.name}"></h:outputText>
												</f:facet>
												<f:attribute value="50" name="width" />
												<h:outputText styleClass="outputText"
													value="#{varlist.edaban}"></h:outputText>
											</h:column>
											<h:column id="columnC5">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Cob01502T17.propLblTitle.name}"></h:outputText>
												</f:facet>
												<f:attribute value="235" name="width" />
												<h:outputText styleClass="outputText"
													value="#{varlist.disp1511Title}"
													title="#{varlist.clubJissekiTitle}"></h:outputText>
											</h:column>
											<h:column id="column7">
												<f:attribute value="40" name="width" />
												<hx:commandExButton type="submit" value="選択"
													styleClass="commandExButton" id="select"
													action="#{pc_Cob01511.doSelectAction}"></hx:commandExButton>
											</h:column>
										</h:dataTable></DIV>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
										<TBODY>
											<TR>
												<TD class="footerClass">
												<TABLE class="panelBox">
													<TBODY>	
														<TR>
															<TD align="left"><hx:commandExButton type="button"
																styleClass="check" id="check"
																onclick="return listCheck(this, event);"
																disabled="#{pc_Cob01511.propDelete.disabled}"></hx:commandExButton>
																<hx:commandExButton	type="button" styleClass="uncheck" id="uncheck"
																onclick="return listUnCheck(this, event);"
																disabled="#{pc_Cob01511.propDelete.disabled}"></hx:commandExButton></TD>
															<TD width="5"></TD>
															<TD align="left"><hx:commandExButton type="submit" value="削除"
																id="delete" styleClass="commandExButton"
																disabled="#{pc_Cob01511.propDelete.disabled}"
																action="#{pc_Cob01511.doDeleteAction}"></hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE class="table" width="795">
							<TBODY>
								<TR>
									<TH nowrap class="v_a"><h:outputText styleClass="outputText"
										id="lblClubSelect"
										value="#{pc_Cob01511.propClubCmb.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlClubCmb" value="#{pc_Cob01511.propClubCmb.value}"
										disabled="#{pc_Cob01511.propClubCmb.disabled}"
										style="width:380px;">
										<f:selectItems value="#{pc_Cob01511.propClubCmb.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton"
										action="#{pc_Cob01511.doChangeClubAction}"
										disabled="#{pc_Cob01511.propBtnClubSelect.disabled}"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><h:outputText styleClass="outputText"
										id="lblClubCd" value="#{pc_Cob01511.propClubCd.labelName}"
										style="#{pc_Cob01511.propClubCd.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlClubCd" size="8"
										value="#{pc_Cob01511.propClubCd.stringValue}"
										disabled="#{pc_Cob01511.propClubCd.disabled}"
										style="#{pc_Cob01511.propClubCd.style}"
										maxlength="#{pc_Cob01511.propClubCd.max}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c"><h:outputText styleClass="outputText"
										id="lblClubName" value="#{pc_Cob01511.propClubName.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlClubName"
										value="#{pc_Cob01511.propClubName.stringValue}"
										style="#{pc_Cob01511.propClubName.style}"
										maxlength="#{pc_Cob01511.propClubName.max}" size="40"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="180" nowrap class="v_f"><!-- 年度 --> <h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Cob01511.propNendo.labelName}"
										style="#{pc_Cob01511.propNendo.labelStyle}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlNendo" size="4"
										disabled="#{pc_Cob01511.propNendo.disabled}"
										style="#{pc_Cob01511.propNendo.style}"
										value="#{pc_Cob01511.propNendo.dateValue}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH width="180" nowrap class="v_f"><!-- 枝番 --> <h:outputText
										styleClass="outputText" id="lblEdaban"
										value="#{pc_Cob01511.propEdaban.labelName}"
										style="#{pc_Cob01511.propEdaban.labelStyle}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlEdaban" size="4"
										disabled="#{pc_Cob01511.propEdaban.disabled}"
										style="#{pc_Cob01511.propEdaban.style}"
										value="#{pc_Cob01511.propEdaban.integerValue}">
										<f:convertNumber type="number" pattern="###0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_f"><h:outputText styleClass="outputText"
										id="lblKatudoDate"
										value="#{pc_Cob01511.propKatudoDate.labelName}"
										style="#{pc_Cob01511.propKatudoDate.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:inputText styleClass="inputText" id="htmlKatudoDate"
										size="10" disabled="#{pc_Cob01511.propKatudoDate.disabled}"
										style="#{pc_Cob01511.propKatudoDate.style}"
										value="#{pc_Cob01511.propKatudoDate.stringValue}">
									</h:inputText></TD>
									<TD colspan="2" class="clear_border"><BR>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_c"><h:outputText styleClass="outputText"
										id="lblClubJissekiTitle"
										value="#{pc_Cob01511.propClubJissekiTitle.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlClubJissekiTitle"
										value="#{pc_Cob01511.propClubJissekiTitle.stringValue}"
										style="#{pc_Cob01511.propClubJissekiTitle.style}"
										maxlength="#{pc_Cob01511.propClubJissekiTitle.max}" size="30"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c"><h:outputText styleClass="outputText"
										id="lblClubJisseki"
										value="#{pc_Cob01511.propClubJisseki.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
										id="htmlClubJisseki" rows="2" cols="70"
										value="#{pc_Cob01511.propClubJisseki.stringValue}"
										style="#{pc_Cob01511.propClubJisseki.style}"></h:inputTextarea></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
							<TBODY>
								<TR align="right">
									<TD align="center" width="100%"><hx:commandExButton
										type="submit" value="確定" styleClass="commandExButton_dat"
										id="kakutei" action="#{pc_Cob01511.doKakuteiAction}"></hx:commandExButton>
										<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Cob01511.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						<HR width="100%" class ="hr" noshade>
						<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="850">
							<TBODY>
								<TR align="right">
									<TD align="center" width="100%"><hx:commandExButton
										type="submit" value="一覧確定" styleClass="commandExButton_dat"
										id="ichirankakutei" action="#{pc_Cob01511.doIchiranKakuteiAction}"
										onclick="onChangeData();"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlHidScroll"
				value="#{pc_Cob01511.propClactList.scrollPosition}"></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Cob01502T01.cob01502.propHidChangeDataFlg.stringValue}" ></h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
  window.attachEvent('onload', endload);

  function endload() {
    changeScrollPosition('htmlHidScroll', 'listScroll');
  }
</SCRIPT>

</HTML>

