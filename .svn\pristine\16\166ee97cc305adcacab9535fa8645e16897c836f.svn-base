<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Hkb00302T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<TITLE>Hkb00302T05.jsp</TITLE>

<style type="text/css">
.meisai_scroll TH { text-align: center; }
.meisai_scroll TH .outputText { text-indent: 0px; }
</style>

<script type="text/javascript">
	//-------------------------------------------
	// 画面初期化
	//-------------------------------------------
	function initForm() {
	}

</script>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="initForm();">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Hkb00302T05.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">

		<!-- ヘッダーインクルード -->
		<jsp:include page="../../rev/inc/header.jsp" />

		<div style="display:none;">
			<hx:commandExButton type="submit" value="閉じる"
				styleClass="commandExButton" id="closeDisp"
				action="#{pc_Hkb00302T01.doCloseDispAction}">
			</hx:commandExButton>
		</div>

		<!--↓outer↓-->
		<DIV class="outer">

		<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
			<h:outputText id="message"
				value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false"
				style="color: red; font-size: 8pt; font-weight: bold">
			</h:outputText>
		</FIELDSET>

		<DIV class="head_button_area" >
			<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp"
				action="#{pc_Hkb00302T01.doReturnDispAction}">
			</hx:commandExButton>
		</DIV>

		<!--↓content↓-->
		<DIV id="content">
		<DIV class="column" align="left">
		
		<DIV id="main" style="position: absolute; top: 90px; left: 0px;width: 980px; height: 500px;">
		<!-- ヘッダ部 -->
		<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
			<TR>
				<TD align=center>
					<TABLE class="table" width="800">
						<TR>
							<!-- 枝番 -->
							<TH width="100">
								<h:outputText styleClass="outputText" value="枝番" />
							</TH>
							<TD width="150">
								<h:outputText styleClass="outputText" value="#{pc_Hkb00302T01.targetSeq}" />
							</TD>
							<!-- 発行機帳票 -->
							<TH width="100">
								<h:outputText styleClass="outputText" value="発行機帳票" />
							</TH>
							<TD width="450">
								<h:outputText styleClass="outputText" value="#{pc_Hkb00302T01.formNm}" />
							</TD>
						</TR>
						<TR>
							<!-- 認証レベル -->
							<TH>
								<h:outputText styleClass="outputText" value="認証レベル" />
							</TH>
							<TD>
								<h:outputText styleClass="outputText" value="#{pc_Hkb00302T01.authLevelName}" />
							</TD>
							<!-- プリンタ区分 -->
							<TH>
								<h:outputText styleClass="outputText" value="プリンタ区分" />
							</TH>
							<TD>
								<h:outputText styleClass="outputText" value="#{pc_Hkb00302T01.prtKbnDescribe}" />
							</TD>
						</TR>
					</TABLE>
				</TD>
			</TR>
		</TABLE>
		
		<HR width="950" align="center">
		
		<!-- タブ -->
		<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
			<TR>
				<TD align="left">
					<hx:commandExButton type="submit" value="発行機帳票"
						styleClass="tab_head_off" id="tab01" style="width: 129px"
						action="#{pc_Hkb00302T01.doSelectTab01Action}"
					></hx:commandExButton><hx:commandExButton type="submit" value="発行手数料"
						styleClass="tab_head_off" id="tab02" style="width: 129px"
						action="#{pc_Hkb00302T01.doSelectTab02Action}"
					></hx:commandExButton><hx:commandExButton type="submit" value="発行期間"
						styleClass="tab_head_off" id="tab03" style="width: 128px"
						disabled="#{pc_Hkb00302T01.propKikanFreeFlg.checked || pc_Hkb00302T01.dbKikanFreeFlg.checked}"
						action="#{pc_Hkb00302T01.doSelectTab03Action}"
					></hx:commandExButton><hx:commandExButton type="submit" value="就学種別"
						styleClass="tab_head_off" id="tab04" style="width: 128px"
						disabled="#{pc_Hkb00302T01.propSygSbtFreeFlg.checked || pc_Hkb00302T01.dbSygSbtFreeFlg.checked}"
						action="#{pc_Hkb00302T01.doSelectTab04Action}"
					></hx:commandExButton><hx:commandExButton type="submit" value="カリキュラム学科"
						styleClass="tab_head_on" id="tab05" style="width: 129px"
						onclick="return false;"
					></hx:commandExButton><hx:commandExButton type="submit" value="不可学生"
						styleClass="tab_head_off" id="tab06" style="width: 128px"
						action="#{pc_Hkb00302T01.doSelectTab06Action}"
					></hx:commandExButton><hx:commandExButton type="submit" value="申請明細"
						styleClass="tab_head_off" id="tab07" style="width: 129px"
						disabled="#{!pc_Hkb00302T01.optionOk}"
						action="#{pc_Hkb00302T01.doSelectTab07Action}"
					></hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD>
					<div class="tab_body" style="height: 420px; padding: 10px;">
						<div>
							<TABLE width="770" border="0" cellpadding="0" cellspacing="0">
								<TR>
									<TD>
										<DIV align="right"><h:outputText styleClass="outputText" value="#{pc_Hkb00302T05.propCgksDataList.listCount} 件" /></DIV>
									</TD>
								</TR>
								<TR>
									<TD>
										<DIV class="listScroll" style="height: 232px">
											<h:dataTable
												id="htmlCgksDataList"
												border="0" cellpadding="2" cellspacing="0" styleClass="meisai_scroll"
												columnClasses="columnClass1" headerClass="headerClass" footerClass="footerClass"
												rowClasses="#{pc_Hkb00302T05.propCgksDataList.rowClasses}"
												value="#{pc_Hkb00302T05.propCgksDataList.list}" var="varlist">
											
												<h:column>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="コード" />
													</f:facet>
													<f:attribute value="120" name="width" />
													<f:attribute value="text-align: left" name="style" />
													<h:outputText styleClass="outputText" value="#{varlist.curGakkaCd}" />
												</h:column>
											
												<h:column>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="カリキュラム学科組織名称" />
													</f:facet>
													<f:attribute value="400" name="width" />
													<f:attribute value="text-align: left" name="style" />
													<h:outputText styleClass="outputText" value="#{varlist.curGakkaName}" />
												</h:column>
											
												<h:column>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="入学年度" />
													</f:facet>
													<f:attribute value="100" name="width" />
													<f:attribute value="text-align: center" name="style" />
													<h:outputText styleClass="outputText" value="#{varlist.nyugakNendo == null ? '全て' : varlist.nyugakNendo}" />
												</h:column>
												
												<h:column>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="入学学期ＮＯ" />
													</f:facet>
													<f:attribute value="100" name="width" />
													<f:attribute value="text-align: center" name="style" />
													<h:outputText styleClass="outputText" value="#{varlist.gakkiNo == null ? '全て' : varlist.gakkiNo}" />
												</h:column>
												
												<h:column>
													<f:facet name="header">
													</f:facet>
													<f:attribute value="50" name="width" />
													<hx:commandExButton type="submit"
														id="selectCgksData" styleClass="commandExButton" style="width: 50px"
														value="選択"
														action="#{pc_Hkb00302T05.doSelectCgksDataAction}">
													</hx:commandExButton>
												</h:column>
											</h:dataTable>
										</DIV>
									</TD>
								</TR>
							</TABLE>
						</div>
					
						<div style="margin-top: 25px;">
							<TABLE class="table" width="770">
								<!-- カリキュラム学科組織コード -->
								<TR>
									<TH width="200">
										<h:outputText
											styleClass="outputText"
											value="#{pc_Hkb00302T05.propCurGakkaCd.labelName}"
											style="#{pc_Hkb00302T05.propCurGakkaCd.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="570">
										<h:selectOneMenu
											id="htmlCurGakkaCd" styleClass="selectOneMenu" style="width: 450px;"
											disabled="#{pc_Hkb00302T05.propCurGakkaCd.disabled}"
											value="#{pc_Hkb00302T05.propCurGakkaCd.stringValue}">
											<f:selectItems value="#{pc_Hkb00302T05.propCurGakkaCd.list}" />
										</h:selectOneMenu>
										<hx:commandExButton type="submit"
											id="selectCurGakkaCd" styleClass="commandExButton" style="width: 50px"
											value="選択"
											disabled="#{pc_Hkb00302T05.propCurGakkaCd.disabled}"
											action="#{pc_Hkb00302T05.doSelectCurGakkaCdAction}">
										</hx:commandExButton>
										<hx:commandExButton type="submit"
											id="unselectCurGakkaCd" styleClass="commandExButton" style="width: 50px"
											value="解除"
											disabled="#{!pc_Hkb00302T05.propCurGakkaCd.disabled}"
											action="#{pc_Hkb00302T05.doUnselectCurGakkaCdAction}">
										</hx:commandExButton>
									</TD>
								</TR>
								<!-- 入学年度学期ＮＯ -->
								<TR>
									<TH>
										<h:outputText
											styleClass="outputText"
											value="#{pc_Hkb00302T05.propNyugakNendoGakkiNo.labelName}"
											style="#{pc_Hkb00302T05.propNyugakNendoGakkiNo.labelStyle}">
										</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu
											id="htmlNyugakNendoGakkiNo" styleClass="selectOneMenu" style="width: 300px;"
											disabled="#{pc_Hkb00302T05.propNyugakNendoGakkiNo.disabled}"
											value="#{pc_Hkb00302T05.propNyugakNendoGakkiNo.stringValue}">
											<f:selectItems value="#{pc_Hkb00302T05.propNyugakNendoGakkiNo.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
							</TABLE>
						</div>
					
						<div style="margin-top: 10px;">
							<TABLE class="button_bar" width="770" align="center" cellspacing="1" cellpadding="1">
								<TR>
									<TD align="center">
										<hx:commandExButton type="submit"
											value="追加" styleClass="commandExButton_dat" id="register"
											action="#{pc_Hkb00302T05.doRegisterAction}"
											confirm="#{msg.SY_MSG_0002W}">
										</hx:commandExButton>
										<hx:commandExButton	type="submit"
											value="削除" styleClass="commandExButton_dat" id="delete"
											action="#{pc_Hkb00302T05.doDeleteAction}"
											confirm="#{msg.SY_MSG_0004W}">
										</hx:commandExButton>
										<hx:commandExButton	type="submit"
											value="クリア" styleClass="commandExButton_etc" id="clear"
											action="#{pc_Hkb00302T05.doClearAction}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TABLE>
						</div>
					</div>
				</TD>
			</TR>
		</TABLE>
		</DIV>
		
		</DIV>
		</DIV>
		<!--↑content↑-->
		</DIV>
		<!--↑outer↑-->
		
		<!-- フッダーインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

