<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/PCog0701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.system.co.util.UtilCogFormatObject" %>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pCog0701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

//----------↓経理検索部品共通js↓----------
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}
//----------↑経理検索部品共通js↑----------
function func_Select_01(thisObj, thisEvent) {
	try {
		var SEP = "<%= UtilCogFormatObject.JOIN_STR %>";
		var noSelectMsg = document.getElementById("form1:htmlNoSelectMsgHidden").value;
		var tekiyoCdId = document.getElementById("form1:prmTekiyoCdId").value;
		var tekiyoNameId = document.getElementById("form1:prmTekiyoNameId").value;
		var selTypeFlg = document.getElementById("form1:prmMultiple").value;
		var focusId = document.getElementById("form1:prmFocusId").value;
		var listBox = (selTypeFlg == 0)?
						document.getElementById("form1:htmlTekiyoOneList"):
							(selTypeFlg == 1)?
								document.getElementById("form1:htmlTekiyoManyList"):
									null;

		if (window.opener && listBox != null) {
			var select = 0;
			if (listBox.selectedIndex > 0) {
				flapWindow(window);
				if (selTypeFlg == 0) {
					// 単一
					var targetCd = getElementByIdEx(window.opener.document, tekiyoCdId);
					var targetNm = getElementByIdEx(window.opener.document, tekiyoNameId);
					select = setOneValue(targetCd, targetNm, 0, 1, listBox, SEP);
					// イベント起動
					if (select > 0) {
						riseEvent(targetCd);
					}
				} else {
					// 複数
					var target = getElementByIdEx(window.opener.document, tekiyoCdId);
					select = setManyValue(target, 0, 1, listBox, SEP);
				}
			}

			if (select > 0) {
				var buttonId = document.getElementById("form1:prmButtonId").value;
				if(buttonId != null && buttonId != '') {
					var button = getElementByIdEx(window.opener.document, buttonId);
					riseEvent(button);
				}
				//フォーカスを設定する項目ID
				var targets = new Array();
				targets.push(getElementByIdEx(window.opener.document, focusId));
				targets.push(getElementByIdEx(window.opener.document, tekiyoCdId));
				targets.push(getElementByIdEx(window.opener.document, tekiyoNameId));
				windowClose(targets);
			} else {
				reflapWindow(window);
				setErrMsg(noSelectMsg);
			}
		} else {
			throw "";
		}
	} catch(e) {
		window.moveTo(0, 0);
		alert("呼び出し元画面に値を返せません。");
	}
	return false;
}
function func_Cancel_01(thisObj, thisEvent) {
	var tekiyoCdId = document.getElementById("form1:prmTekiyoCdId").value;
	var tekiyoNameId = document.getElementById("form1:prmTekiyoNameId").value;
	var focusId = document.getElementById("form1:prmFocusId").value;
	//フォーカスを設定する項目ID
	var targets = new Array();
	targets.push(getElementByIdEx(window.opener.document, focusId));
	targets.push(getElementByIdEx(window.opener.document, tekiyoCdId));
	targets.push(getElementByIdEx(window.opener.document, tekiyoNameId));
	windowClose(targets);
	return false;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PCog0701.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_PCog0701.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PCog0701.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PCog0701.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600">
							<TBODY>
								<TR>
									<TD>
									<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="table">
										<TBODY>
											<TR>
												<TH width="145" class="v_a"><h:outputText
													styleClass="outputText" id="lblTekiyoKbn"
													value="#{pc_PCog0701.propTekiyoKbn.labelName}"
													style="#{pc_PCog0701.propTekiyoKbn.labelStyle}"></h:outputText></TH>
												<TD><h:outputText styleClass="outputText" id="htmlTekiyoKbn"
													value="#{pc_PCog0701.propTekiyoKbn.stringValue}"
													style="#{pc_PCog0701.propTekiyoKbn.style}">

												</h:outputText></TD></TR>
											<TR>
												<TH width="145" class="v_b"><h:outputText
													styleClass="outputText" id="lblTekiyoCd"
													value="#{pc_PCog0701.propTekiyoCd.labelName}"
													style="#{pc_PCog0701.propTekiyoCd.labelStyle}"></h:outputText></TH>
												<TD>
												<TABLE width="450" border="0" cellpadding="0" cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="200"><h:inputText styleClass="inputText"
																id="htmlTekiyoCd" size="8"
																value="#{pc_PCog0701.propTekiyoCd.stringValue}"
																disabled="#{pc_PCog0701.propTekiyoCd.disabled}"
																readonly="#{pc_PCog0701.propTekiyoCd.readonly}"
																style="#{pc_PCog0701.propTekiyoCd.style}"
																maxlength="#{pc_PCog0701.propTekiyoCd.max}"></h:inputText></TD>
															<TD width="250"><h:outputText styleClass="outputText"
																id="lblTekiyoCdFindType" value="（前方一致）"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_c"><h:outputText styleClass="outputText"
													id="lblTekiyoName"
													value="#{pc_PCog0701.propTekiyoName.labelName}"
													style="#{pc_PCog0701.propTekiyoName.labelStyle}"></h:outputText></TH>
												<TD>
												<TABLE width="450" border="0" cellpadding="0" cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="200"><h:inputText styleClass="inputText"
																id="htmlTekiyoName" size="28"
																disabled="#{pc_PCog0701.propTekiyoName.disabled}"
																readonly="#{pc_PCog0701.propTekiyoName.readonly}"
																style="#{pc_PCog0701.propTekiyoName.style}"
																value="#{pc_PCog0701.propTekiyoName.stringValue}"
																maxlength="#{pc_PCog0701.propTekiyoName.maxLength}"></h:inputText></TD>
															<TD width="250"><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlTekiyoNameFindType"
																value="#{pc_PCog0701.propTekiyoNameFindType.stringValue}"
																disabled="#{pc_PCog0701.propTekiyoNameFindType.disabled}"
																readonly="#{pc_PCog0701.propTekiyoNameFindType.readonly}">
																<f:selectItems
																	value="#{pc_PCog0701.propTekiyoNameFindType.list}" />
															</h:selectOneRadio></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD></TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
						</TBODY>
					</TABLE>
					</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600"
							class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="検索"
										styleClass="commandExButton_dat" id="search" action="#{pc_PCog0701.doSearchAction}">
									</hx:commandExButton><hx:commandExButton type="submit"
										value="クリア" styleClass="commandExButton_etc" id="clear" action="#{pc_PCog0701.doClearAction}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<HR noshade>
						</TD>
					</TR>
					<TR>
						<TD>
							<hx:jspPanel id="jspPanel1">
								<TABLE border="0" cellpadding="0" cellspacing="0">
									<TBODY>
										<TR>
											<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD width="450" style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none"></TD>
													<TD width="150" align="right"
														style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none; text-align: right">
														<h:outputText styleClass="outputText" id="htmlListCount" 
															value="#{pc_PCog0701.propListCount.integerValue}" 
															style="#{pc_PCog0701.propListCount.style}">
														<f:convertNumber />
													</h:outputText><h:outputText styleClass="outputText" id="lblKensu" value="件" style="text-align: right"></h:outputText></TD>
												</TR>
												<TR>
													<TH width="600" colspan="2">
													<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
														<TBODY>
															<TR>
																<TD style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify"
																	width="70"><h:outputText styleClass="outputText" id="lblListTekiyoCd" value="摘要コード"></h:outputText></TD>
																<TD style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify" 
																	width="443"><h:outputText styleClass="outputText" id="lblListTekiyoName" value="名称" style="text-align: left"></h:outputText></TD>
																<TD style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify" 
																	width="87" colspan=""><h:outputText styleClass="outputText" id="lblListJokyo" value="状況"></h:outputText></TD>
															</TR>
														</TBODY>
													</TABLE>
													</TH>
												</TR>
												<TR>
													<TD width="600" colspan="2">
													<h:selectOneListbox styleClass="selectOneListbox"
														id="htmlTekiyoOneList"
														style='font-family: "ＭＳ ゴシック"; font-size: 10pt; width:100%;height:230px;'
														size="20" value="#{pc_PCog0701.propTekiyoOneList.value}"
														rendered="#{pc_PCog0701.propTekiyoOneList.rendered}"
														ondblclick="return func_Select_01(this, event);">
														<f:selectItems
															value="#{pc_PCog0701.propTekiyoOneList.list}" />
													</h:selectOneListbox>
													<h:selectManyListbox styleClass="selectManyListbox"
														id="htmlTekiyoManyList"
														style='font-family: "ＭＳ ゴシック"; font-size: 10pt; width:100%;height:230px;'
														size="20" value="#{pc_PCog0701.propTekiyoManyList.value}"
														rendered="#{pc_PCog0701.propTekiyoManyList.rendered}">
														<f:selectItems
															value="#{pc_PCog0701.propTekiyoManyList.list}" />
													</h:selectManyListbox>
													</TD></TR>
											</TBODY>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="600" class="button_bar">
											<TBODY>
												<TR>
													<TD><hx:commandExButton type="submit" styleClass="commandExButton_etc" 
														id="select" value="選択" action="#{pc_PCog0701.doSelectAction}" onclick="return func_Select_01(this, event);">
													</hx:commandExButton><hx:commandExButton type="submit" 
														value="キャンセル" styleClass="commandExButton_etc" id="cancel" action="#{pc_PCog0701.doCancelAction}" onclick="return func_Cancel_01(this, event);">
													</hx:commandExButton></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</hx:jspPanel></TD>
					</TR>
					<TR>
						<TD>
							<input id="form1:prmTekiyoCdId" type="hidden" name="prmTekiyoCdId" value="<c:out value="${pc_PCog0701.prmTekiyoCdId}"/>">
							<input id="form1:prmTekiyoNameId" type="hidden" name="prmTekiyoNameId" value="<c:out value="${pc_PCog0701.prmTekiyoNameId}"/>">
							<input id="form1:prmMultiple" type="hidden" name="prmMultiple" value="<c:out value="${pc_PCog0701.prmMultiple ? 1 : 0}"/>">
							<input id="form1:prmButtonId" type="hidden" name="prmButtonId" value="<c:out value="${pc_PCog0701.prmButtonId}"/>">
							<input id="form1:prmFocusId" type="hidden" name="prmFocusId" value="<c:out value="${pc_PCog0701.prmFocusId}"/>">

							<h:inputHidden id="htmlNoSelectMsgHidden" value="#{pc_PCog0701.propNoSelectMsgHidden.stringValue}"></h:inputHidden>
							<h:inputHidden id="htmlExecutableSearchHidden" value="#{pc_PCog0701.propExecutableSearchHidden.integerValue}"><f:convertNumber /></h:inputHidden>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/childFooter.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

