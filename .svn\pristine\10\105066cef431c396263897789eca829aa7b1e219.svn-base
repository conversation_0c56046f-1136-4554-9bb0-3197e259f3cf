var startX=0;
var startY=0;

var Elm = null;
var drag_switch = 0;	//1:ドラッグ中、2:新規アイテム配置中、3:Resize中、9:複数選択中 -1:文字入力中
var bNewItem = 0;		//新規挿入アイテム（1:テキスト、2:水平線、3:画像）
var bNoWrap = true;		//nowrapか否かのフラグ

var fMode =false;

var formatElm;

var history = new Array(10);
var countHistory = -1;
var current = -1;
var fMove = false;


var LINE_MARGIN = 0.0;

var BACK_GROUND_COLOR_WRAP = "#00ff99";
var BACK_GROUND_COLOR_NOWRAP = "#87CEFA";
var BACK_GROUND_COLOR_IMAGE = "#ffd700";

var kewordsXML = null;

var currentTextRange = null;

//同一フィールドに頁予約語とその他の予約語の混在を許さないためのチェック用変数
//フィールドがアクティブになった時点の状態を保持
var currentTextRangeActHtml = "";



function Init(size,direction,keywordId,keywordSrc){

	//2009-11-09
	//text = '<XML id="' + keywordId + '" SRC="' + keywordSrc + '" />';
	//document.body.innerHTML = text + document.body.innerHTML
	kewordsXML = parent.document.getElementById(keywordId);

	document.body.oncontextmenu=Contextmenu;
	document.body.onclick=Click;
 
	document.onmousedown = Mdown;
	document.onmouseup = Mup;
	document.onmousemove = Mmove;
	document.onmouseout = Mout;
 

	document.onkeydown = Keydown;
	document.onkeyup  = Keyup;

	document.body.ondragstart = Cancel;
	document.body.ondragenter = Cancel;
	document.body.ondragend = Cancel;
	document.body.ondragover = Cancel;
	document.body.ondragleave = Cancel;
	document.body.ondrag = Cancel;
	document.body.ondrop = Cancel;
	document.body.onpaste = Cancel;
	document.body.onbeforepaste = Cancel;
	document.body.ondblclick=OnChangeMode;

	document.body.contentEditable = true;

	document.execCommand("MultipleSelection");
	document.execCommand("LiveResize");

	document.body.onmouseenter = Menter;

	var text;
	//FORMAT DIRECTION
	if(document.getElementById("FORMAT_d")){
	}else{
		text = '<XML id="FORMAT_d" SRC="FORMAT_d.XML" />';
		document.body.innerHTML = text + document.body.innerHTML
	}
	if(document.getElementById("FORMAT_g")){
	}else{
		text = '<XML id="FORMAT_g" SRC="FORMAT_g.XML" />';
		document.body.innerHTML = text + document.body.innerHTML
	}
	//add 2006/06/07
	if(document.getElementById("FORMAT_n")){
	}else{
		text = '<XML id="FORMAT_n" SRC="FORMAT_n.XML" />';
		document.body.innerHTML = text + document.body.innerHTML
	}
	if(document.getElementById("FORMAT_p")){
	}else{
		text = '<XML id="FORMAT_p" SRC="FORMAT_p.XML" />';
		document.body.innerHTML = text + document.body.innerHTML
	}
		
	//PAGE DIRECTION
	var sSize = "A4";
	var sDirection = "portrait";
	if(size > ""){
		sSize = size;
	}
	if(direction > ""){
		sDirection = direction;
	}
	
	if(document.getElementById("PAGE")){
	}else{
		text = '<P id="PAGE" class="'+sSize+sDirection+'" style="top : 0mm; left: 0mm; position : absolute;" unselectable="On" CONTENTEDITABLE="false" HIDEFOCUS="true" onfocus="Cancel();">';
		text += '<INPUT type="HIDDEN" ID="SIZE" value="'+sSize+'" />';
		text += '<INPUT type="HIDDEN" ID="DIRECTION" value="'+sDirection+'" />';
		text += '</P>';
		
		document.body.innerHTML = text + document.body.innerHTML;
	}
	
	// コンテキストメニュー
	if(document.getElementById("MENU")){
	}else{
		text = '<DIV id="MENU" class="menu" style="display: none;">';
		text += '<DIV class="menuitem" id="menuItem1" value="1">極細線</DIV>';
		text += '<DIV class="menuitem" id="menuItem2" value="2">細線</DIV>';
		text += '<DIV class="menuitem" id="menuItem3" value="3">中線</DIV>';
		text += '<DIV class="menuitem" id="menuItem4" value="4">太線</DIV>';
		text += '</DIV>';
		document.body.innerHTML = document.body.innerHTML + text;
	}
	
	initItems(keywordId);


//	alert(queryBrowserInfo().isIE);
//	alert(queryBrowserInfo().editable);


	Resume();
}


function initItems(keywordId){

	// 2009-11-09
	//var kewordsXML = document.getElementById(keywordId);
	//var XMLroot = kewordsXML.selectSingleNode("/");
	//var groupList =	XMLroot.getElementsByTagName("GROUP");

	
	//各アイテム初期化
	var elms = document.body.childNodes;
	for (var i=0 ; i <elms.length; i++){
	
		if (elms(i).className == "field"){
			elms(i).onbeforepaste=fieldBeforePaste
			elms(i).onpaste=fieldPaste;
			elms(i).onresize=FieldResize;	
			elms(i).onmove=ElmMove;
			elms(i).onblur=FieldBlur;
			elms(i).onfocus=FieldFocus;
			elms(i).onresizestart=FieldResizestart;
			elms(i).onresizeend=FieldResizeend;
			if (elms(i).style.whiteSpace == "nowrap" ){
				elms(i).style.backgroundColor = BACK_GROUND_COLOR_NOWRAP;
			}else{
				elms(i).style.backgroundColor = BACK_GROUND_COLOR_WRAP;
			}			
			
			if (elms(i).style.lineHeight == "1em"){
				elms(i).style.lineHeight = elms(i).style.fontSize;
			}
			var lineHeight = parseInt(elms(i).style.lineHeight);
			elms(i).LINECOUNT = Math.round(elms(i).clientHeight / lineHeight);
			var sBgImage = 'image/lineHeight' + elms(i).style.lineHeight + '.gif';
			elms(i).style.backgroundImage = 'url(' + sBgImage + ')';
			
			elms(i).style.borderWidth = 0;
			elms(i).style.filter = "alpha(opacity=90)";
			
			var childs = elms(i).childNodes;
			for (var j=0 ; j <childs.length; j++){
				if(childs(j).tagName == "BR"){
					childs(j).outerHTML = '<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>';
				}
				if(childs(j).tagName == "!"){
					elms(i).removeChild(childs(j));
				}
			}
		}else if (elms(i).className == "line"){
			elms(i).onresizestart=Resizestart;
			elms(i).onresize=HrResize;
			elms(i).onresizeend=Resizeend;
			elms(i).onmove=ElmMove;
			
			elms(i).style.zIndex = 6
		}else if (elms(i).className == "verticalrule"){
			elms(i).onresizestart=Resizestart;
			elms(i).onresize=VrResize;
			elms(i).onresizeend=Resizeend;
			elms(i).onmove=ElmMove;
			elms(i).contentEditable = false;
			elms(i).onbeforeeditfocus=Cancel;
			
			elms(i).style.zIndex = 6
		}else if (elms(i).className == "rectangle"){
			elms(i).onresizestart=RectResizestart;
			elms(i).onresize=RectResize;	
			elms(i).onresizeend=Resizeend;
			elms(i).onmove=ElmMove;
			elms(i).contentEditable = false;
			elms(i).onbeforeeditfocus=Cancel;

			//elms(i).style.zIndex = 6
		}else if (elms(i).className == "picturefield"){
			elms(i).onresizestart=FieldResizestart;
			elms(i).onresizeend=FieldResizeend;

			//2009-11-09
			elms(i).contentEditable = false;
			elms(i).onbeforeeditfocus=Cancel;
			
		}
	}	


	//異なる文面区分のコピー処理時、コピー先に存在しない予約語が存在する場合は、
	//その予約語の削除を行う。
	
	// 2009-11-09
	var XMLroot = kewordsXML.selectSingleNode("/");
	var groupList =	XMLroot.getElementsByTagName("GROUP");
	
	
	for (var i=0 ; i <elms.length; i++){



		if (elms(i).className == "field" || elms(i).className == "picturefield"){




			var childs = elms(i).childNodes;
			var keywordList;
			var delflg = true;
			for (var j=0 ; j <childs.length; j++){
			
				if(childs(j).tagName == "A"){
				
				
					for (var ii = 0; ii < groupList.length; ii++){
						keywordList = groupList(ii).getElementsByTagName("KEYWORD");
						for (var jj = 0; jj < keywordList.length; jj++){
							var sId = "";
							if (keywordList(jj).getAttribute("ID")){
								sId = keywordList(jj).getAttribute("ID");
							}
							
							if(childs(j).id == sId){
								delflg = false;
								
								break;
							}
						}
						if(delflg == false){
							break;
						}
					}
					if(delflg == true){
						elms(i).removeChild(childs(j));
					}
				}
			}
			//delflg = true;
			//if(delflg == true){
			//	var rng = document.body.createControlRange();
			//	rng.add(elms(i));
			//	rng.select();
			//window.document.execCommand("delete",false,null);
			//}
		}
	}




}


function Menter(){

	switch (bNewItem) {
	case 1:

		startX = event.offsetX;
		startY = event.offsetY;

		var html = '<DIV class="field"';
//		html += ' style="FONT-SIZE: 18px; FONT-FAMILY: ＭＳ ゴシック;';
//		html += ' style="FONT-SIZE: 20px; FONT-FAMILY: ＭＳ ゴシック;';
		html += ' style="FONT-SIZE: 20px; FONT-FAMILY: ＭＳ 明朝;';
		html += ' position : absolute; width: 10em; height: 1.0em;';
//		html += ' line-height: 18px;';
		html += ' line-height: 20px;';
		html += ' letter-spacing: 0px;';
		if (bNoWrap){
			html += ' white-space: nowrap;';
		}
		else{
			html += ' white-space: normal;';		
		}
		html += ' TEXT-ALIGN: left;"';

		html += ' LINECOUNT=1';
		
		html += '>';

		var elm = document.createElement(html);

		if (elm.style.whiteSpace == "nowrap" ){
			elm.style.backgroundColor = BACK_GROUND_COLOR_NOWRAP;
		}else{
			elm.style.backgroundColor = BACK_GROUND_COLOR_WRAP;
		}

		elm.onbeforepaste=fieldBeforePaste
		elm.onpaste=fieldPaste;
		elm.onresize=FieldResize;	
		elm.onmove=ElmMove;
		elm.onblur=FieldBlur;
		elm.onfocus=FieldFocus;
		elm.onresizestart=FieldResizestart;
		elm.onresizeend=FieldResizeend;
		elm.style.borderWidth = 0;
		elm.style.filter = "alpha(opacity=90)";

		document.body.insertAdjacentElement("beforeEnd",elm);

		elm.innerText = "abc";
		elm.style.pixelWidth = elm.style.pixelWidth + 1;

		elm.style.pixelLeft = startX;
		elm.style.pixelTop = startY;
		elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;

//		var sBgImage = 'image/bg18px.gif';
//		var sBgImage = 'image/lineHeight18px.gif';
		var sBgImage = 'image/lineHeight20px.gif';

		elm.style.backgroundImage = 'url(' + sBgImage + ')';
	
		Elm = elm;
		drag_switch = 2;
		break;
		
	case 2:
		startX = event.offsetX;
		startY = event.offsetY;
	
		var html = '<HR class="line" style="HEIGHT: 4px; WIDTH: 120px; position : absolute;" />';
		var elm = document.createElement(html);
		document.body.insertAdjacentElement("beforeEnd",elm);

		elm.onresizestart=Resizestart;
		elm.onresize=HrResize;
		elm.onresizeend=Resizeend;
		elm.onmove=ElmMove;

		elm.style.zIndex = 6

		elm.style.pixelLeft = startX;
		elm.style.pixelTop = startY;

		Elm = elm;
		drag_switch = 2;
		break;
		
	case 3:
		//マウスの座標を取得
		startX = event.offsetX;
		startY = event.offsetY;

		//イメージBOXの作成
		var html = '<DIV class="picturefield"';
		html += ' style="';
		html += ' position : absolute; width: 120px; height: 120px;"';
		html += '>';
		var elm = document.createElement(html);

		elm.style.backgroundColor = BACK_GROUND_COLOR_IMAGE;

		var sBgImage = 'image/bg_renga_w10.gif';
		//elm.style.backgroundImage = 'url(' + sBgImage + ')';

		//オブジェクトの指定された位置に隣接するエレメントオブジェクトを挿入
		document.body.insertAdjacentElement("beforeEnd",elm);

		//イメージBOXの表示位置設定
		elm.onresizestart=FieldResizestart;
		elm.onresizeend=FieldResizeend;
		elm.onmove=ElmMove;
		elm.innerText = "画像イメージ";
		elm.style.pixelWidth = elm.style.pixelWidth + 1;
		elm.style.pixelLeft = startX;
		elm.style.pixelTop = startY;
		elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;

		//2009-11-09
		elm.contentEditable = false;
		elm.onbeforeeditfocus=Cancel;
		
		Elm = elm;
		//1:ドラッグ中、2:新規アイテム配置中、3:Resize中、9:複数選択中 -1:文字入力中
		drag_switch = 2;
		break;

	// 垂直線
	case 4:
		//マウスの座標を取得
		startX = event.offsetX;
		startY = event.offsetY;

		//イメージBOXの作成
		var html = '<DIV class="verticalrule"';
		html += ' style="';
		html += ' position : absolute; width: 4px; height: 120px;"';
		html += '>';
		var elm = document.createElement(html);

		//オブジェクトの指定された位置に隣接するエレメントオブジェクトを挿入
		document.body.insertAdjacentElement("beforeEnd",elm);

		//イメージBOXの表示位置設定
		elm.onresizestart=Resizestart;
		elm.onresize=VrResize;
		elm.onresizeend=Resizeend;
		elm.onmove=ElmMove;
		
		elm.style.pixelLeft = startX;
		elm.style.pixelTop = startY;

		//2009-11-09
		elm.contentEditable = false;
		elm.onbeforeeditfocus=Cancel;
		
		Elm = elm;
		//1:ドラッグ中、2:新規アイテム配置中、3:Resize中、9:複数選択中 -1:文字入力中
		drag_switch = 2;
		break;

	// 矩形
	case 5:
		//マウスの座標を取得
		startX = event.offsetX;
		startY = event.offsetY;

		//イメージBOXの作成
		var html = '<DIV class="rectangle"';
		html += ' style="';
		html += ' position : absolute; width: 120px; height: 120px;"';
		html += '>';
		var elm = document.createElement(html);

		//オブジェクトの指定された位置に隣接するエレメントオブジェクトを挿入
		document.body.insertAdjacentElement("beforeEnd",elm);

		//イメージBOXの表示位置設定
		elm.onresizestart=RectResizestart;
		//elm.onresizestart=RectResize;
		elm.onresizeend=Resizeend;
		elm.onmove=ElmMove;
		elm.onresize=RectResize;	

		elm.style.pixelLeft = startX;
		elm.style.pixelTop = startY;
		//elm.style.borderWidth = 2;
		elm.style.borderTopWidth = 2;
		elm.style.borderBottomWidth = 2;
		elm.style.borderLeftWidth = 2;
		elm.style.borderRightWidth = 2;

		//2009-11-09
		elm.contentEditable = false;
		elm.onbeforeeditfocus=Cancel;
		
		Elm = elm;
		//1:ドラッグ中、2:新規アイテム配置中、3:Resize中、9:複数選択中 -1:文字入力中
		drag_switch = 2;
		break;

	}
	bNewItem = 0;

}



function fieldBeforePaste(){
	event.returnValue = false;
	return false;
}

function fieldPaste(){
	event.returnValue = false;

	var sel, rng, el, dup;

	var html = window.clipboardData.getData("Text");
	
	var re;
	re = /&/g;
	html = html.replace(re,"&amp;");
	re = /</g;
	html = html.replace(re,"&lt;");
	re = />/g;
	html = html.replace(re,"&gt;");
	re = / /g;
	html = html.replace(re,"&nbsp;");
	

	if (sel = document.selection) {

		if(sel.type.toLowerCase() == "control"){
			return false;
		}
		rng = sel.createRange();
		if (rng.parentElement()){
			el = rng.parentElement();
			if(sel.type.toLowerCase() == "none"){
				if (el.className == "field"){
					rng.pasteHTML(html);
					el.focus();
				}else if(el.tagName.toLowerCase() == "a"){
					el.insertAdjacentHTML("beforeBegin",html);
					el.parentElement.focus();
				}
			}else{
				if (rng.htmlText != rng.text){
					return false;
				}
				if (el.className == "field"){
					rng.pasteHTML(html);
					el.focus();
				}
			}
		}
	}
	
	return false;
	
}

function Resizestart(){
	drag_switch=3;
}

function Resizeend(){
	drag_switch=0;
}

function Resume(){

	if ((current > 0) && (history[current] == document.body.innerHTML)){
		return;
	}

	current++;

	if (current >= history.length){
		for(var i = 0; i < history.length - 1; i ++){
			history[i] = history[i + 1];
		}
		current = history.length - 1;
	}
	history[current] = document.body.innerHTML;
	countHistory = current;

}

function Undo(){

	//2009-11-09
	//if (current > 1){
	if (current > 0){
		current--;

		document.body.innerHTML = history[current];
		initItems();

		var elms = document.getElementsByTagName("A");
		for (var i = 0; i < elms.length; i++){
			ToggleDisp(elms(i),fMode);
		}
		
	}
}

function Redo(){

	if (current < countHistory){
		current++;

		document.body.innerHTML = history[current];
		initItems();

		var elms = document.getElementsByTagName("A");
		for (var i = 0; i < elms.length; i++){
			ToggleDisp(elms(i),fMode);
		}
	}
}


function HrResize(){

	if(window.event.srcElement.style.posHeight > 4){
		window.event.srcElement.style.height = "4px";
	}

}

function VrResize(){

	if(window.event.srcElement.style.posWidth > 4){
		window.event.srcElement.style.width = "4px";
	}

}

//var RectWidth = 0;
//var RectHeight = 0;
//var ResizeStartX = 0;
//var ResizeStartY = 0;
function RectResizestart(){
	drag_switch=3;

	var elm = window.event.srcElement;
	//RectWidth = elm.style.posWidth;
	//RectHeight = elm.style.posHeight;
	elm.RectWidth = elm.style.posWidth;
	elm.RectHeight = elm.style.posHeight;

	ResizeStartX = event.offsetX;
	ResizeStartY = event.offsetY;

	for (var srcElm = event.srcElement; srcElm.tagName.toLowerCase() != "body"; srcElm = srcElm.parentElement){
		if (srcElm.style.position == "absolute"){
		
			ResizeStartX += srcElm.style.pixelLeft;
			ResizeStartY += srcElm.style.pixelTop;
			
		}
	}
	elm.ResizeStartX = ResizeStartX;
	elm.ResizeStartY = ResizeStartY;
}	

function RectResize(){

	var elm = window.event.srcElement;
	//alert(elm.outerHTML);
	if (event.ctrlKey){

		var RectWidth = elm.RectWidth;
		var RectHeight = elm.RectHeight;
		var ResizeStartX = elm.ResizeStartX;
		var ResizeStartY = elm.ResizeStartY;
	
		var x = event.offsetX;
		var y = event.offsetY;

		for (var srcElm = event.srcElement; srcElm.tagName.toLowerCase() != "body"; srcElm = srcElm.parentElement){
			if (srcElm.style.position == "absolute"){
			
				x += srcElm.style.pixelLeft;
				y += srcElm.style.pixelTop;
				
			}
		}

	
		//if(elm.style.posWidth >= RectWidth && elm.style.posHeight >= RectHeight){
		var borderWidth = parseInt(elm.style.borderWidth);
		if(x > ResizeStartX || y > ResizeStartY){
			//alert(">="+elm.style.posWidth + ":" + RectWidth + "   " + elm.style.posHeight + ":" + RectHeight + "   " + borderWidth);
			//elm.innerText = ">="+elm.style.posWidth + ":" + RectWidth + "   " + elm.style.posHeight + ":" + RectHeight + "   " + borderWidth;
			if(borderWidth > 1){
				borderWidth--;
				//elm.style.borderWidth = borderWidth + "px";
				elm.style.borderTopWidth = borderWidth + "px";
				elm.style.borderBottomWidth = borderWidth + "px";
				elm.style.borderLeftWidth = borderWidth + "px";
				elm.style.borderRightWidth = borderWidth + "px";
				//alert(elm.style.borderWidth);
			}
			ResizeStartX = x;
			ResizeStartY = y;
		}
		//else if(elm.style.posWidth <= RectWidth && elm.style.posHeight <= RectHeight){
		else if(x < ResizeStartX || y < ResizeStartY){
			//alert("<="+elm.style.posWidth + ":" + RectWidth + "   " + elm.style.posHeight + ":" + RectHeight + "   " + borderWidth);
			//elm.innerText = "<="+elm.style.posWidth + ":" + RectWidth + "   " + elm.style.posHeight + ":" + RectHeight + "   " + borderWidth;
			if(borderWidth < 4){
				borderWidth++;
				//elm.style.borderWidth = borderWidth + "px";
				elm.style.borderTopWidth = borderWidth + "px";
				elm.style.borderBottomWidth = borderWidth + "px";
				elm.style.borderLeftWidth = borderWidth + "px";
				elm.style.borderRightWidth = borderWidth + "px";
				//alert(elm.style.borderWidth);
			}
				ResizeStartX = x;
				ResizeStartY = y;
		}
		//elm.innerText = ResizeStartX + "->" + x + "   " + ResizeStartY + "->" + y + "  " + borderWidth;

		//elm.style.posWidth = RectWidth;
		//elm.style.posHeight = RectHeight;
		window.event.srcElement.style.width = RectWidth;
		window.event.srcElement.style.height = RectHeight;

	}
	/*
	if(drag_switch == 3){
		if(elm.style.posWidth > RectWidth && elm.style.posHeight > RectHeight){
			var borderWidth = parseInt(elm.style.borderWidth);
			if(borderWidth > 1){
				borderWidth--;
				elm.style.borderWidth = borderWidth + "px";
				//alert(elm.style.borderWidth);
			}
			window.event.srcElement.style.width = RectWidth;
			window.event.srcElement.style.height = RectHeight;
		}
		else if(elm.style.posWidth < RectWidth && elm.style.posHeight < RectHeight){
			var borderWidth = parseInt(elm.style.borderWidth);
			if(borderWidth < 4){
				borderWidth++;
				elm.style.borderWidth = borderWidth + "px";
				//alert(elm.style.borderWidth);
			}
			window.event.srcElement.style.width = RectWidth;
			window.event.srcElement.style.height = RectHeight;
		}
	}
	*/
	

}	


function ContainedResize(){

	if (drag_switch != 3){
		return;
	}

	var container = event.srcElement.parentElement;
	
	event.srcElement.style.pixelLeft = 0;
	event.srcElement.style.pixelTop = 0;

	if (container.DIRECTION == "VERTICAL"){
		event.srcElement.style.pixelWidth = container.style.pixelWidth;
		if (event.srcElement.style.pixelHeight > container.style.pixelHeight){
			event.srcElement.style.pixelHeight = container.style.pixelHeight;
		}
	}else{
		if (event.srcElement.style.pixelWidth > container.style.pixelWidth){
			event.srcElement.style.pixelWidth = container.style.pixelWidth;
		}
		event.srcElement.style.pixelHeight = container.style.pixelHeight;
	}
		

}

function ContainedMove(){

	if (drag_switch == 3){
		return;
	}

	event.srcElement.style.pixelLeft = 0;
	event.srcElement.style.pixelTop = 0;

}

function FieldFocus(){

	var elm = window.event.srcElement;
	
	//フィールドにカーソルがあたった時点のHTML(フィールドに設定している予約語、固定文言)を取得
	currentTextRangeActHtml = elm.innerHTML;

	elm.style.overflowY = "visible";

	if (sel = document.selection) {
		if (sel.type.toLowerCase() == "control"){
			return true;
		}
		var rng = sel.createRange();

		//2009-11-09
		currentTextRange = rng;
	}
	
}

function FieldBlur(){

	
	var elm = window.event.srcElement;

	if (elm.style.whiteSpace == "nowrap" ){
		var lines = 0;
		for (var i = 0; i > -1; lines++){
			i = elm.innerHTML.indexOf("<BR>",i);
			if (i > -1){
				i++; 
			}
		}
		var lineHeight = parseInt(elm.style.lineHeight);

		if (elm.style.pixelHeight < lineHeight * lines){
			elm.style.pixelHeight = lineHeight * lines;
		}
		elm.LINECOUNT = Math.round(elm.clientHeight / lineHeight);
	}

	elm.style.overflowY = "hidden";
	
	Resume();
}

function FieldResizestart(){

	drag_switch=3;

	var elm = window.event.srcElement;
	elm.style.pixelHeight = elm.clientHeight;
	elm.style.overflowY = "hidden";
	
}

function FieldResizeend(){

	var elm = window.event.srcElement;

//	FieldAdjustSize(elm);

	drag_switch=0;
}

function FieldResize(){

	var elm = window.event.srcElement;
		
	if (drag_switch != 0){
		FieldAdjustSize(elm);
	}
	
	
}



function FieldAdjustSize(elm){

	if(elm.style.posWidth < 0.5){
//		elm.style.width = "0.5em";
		elm.style.posWidth = 0.5;
	}
	if(elm.style.posWidth > 128){
//		elm.style.width = "128em";
		elm.style.posWidth = 128;
	}
	
	var em = Math.round(elm.style.posWidth * 2);
	em = em / 2
	elm.style.posWidth = em;
	
	if ( elm.style.fontWeight == 'bold' ) {
		elm.style.pixelWidth = elm.style.pixelWidth + 2;
	}else{
		elm.style.pixelWidth = elm.style.pixelWidth + 1;
	}
	
	var lines = 0;

	var fontSize = parseInt(elm.style.fontSize);

	var lineHeight = parseInt(elm.style.lineHeight);

	if (elm.style.whiteSpace == "nowrap" ){
		
		if (drag_switch == 3){
			if (event.ctrlKey){
				lines = parseInt(elm.LINECOUNT);
				if (elm.clientHeight < (fontSize * lines)){
					lineHeight = fontSize;
				}else if (elm.clientHeight > fontSize * lines * 2){
					lineHeight = fontSize * 2;
				}else{
					lineHeight = Math.round(elm.clientHeight / lines);
				}
			}else{
								
				for (var i = 0; i > -1; lines++){
					i = elm.innerHTML.indexOf("<BR>",i);
					if (i > -1){
						i++; 
					}
				}
				if(elm.clientHeight > lineHeight * lines){
					lines = Math.round(elm.clientHeight / lineHeight);							
				}			
			}
		}else if (drag_switch == 0){
			lines = parseInt(elm.LINECOUNT);
			lineHeight = Math.round(elm.clientHeight / lines);
		}else{
			lines = parseInt(elm.LINECOUNT);
		}
	}else{ //ワードラップ
	
		lines = parseInt(elm.LINECOUNT);
	
		if (drag_switch == 3){
			if (event.ctrlKey){
				
				lineHeight = Math.round(elm.clientHeight / lines);

				if(lineHeight < fontSize){
					lineHeight = fontSize;
				}else if (lineHeight > fontSize * 2){
					lineHeight = fontSize * 2;
				}
			
			}else{
				if(elm.style.posHeight < 1){
					lines = 1;
					elm.style.posHeight = lines;
				}
				lines = Math.round(elm.clientHeight / lineHeight);
			}
		}else if (drag_switch == 0){
			lineHeight = Math.round(elm.clientHeight / lines);		
		}
	}
	elm.style.pixelHeight = lineHeight * lines;

	elm.style.lineHeight = lineHeight + "px";

	var sBgImage = 'image/lineHeight' + elm.style.lineHeight + '.gif';
	elm.style.backgroundImage = 'url(' + sBgImage + ')';

	elm.LINECOUNT = lines;	
	
	return true;
				
}


function ElmMove(){

	if (event.srcElement.style.pixelLeft < 0){
		event.srcElement.style.pixelLeft = 0;
	}
	if (event.srcElement.style.pixelTop < 0){
		event.srcElement.style.pixelTop = 0;
	}
}



function OnChangeMode(){

	event.cancelBubble = true;
	if (window.event.srcElement.className == "field"){
		return true;
	}
	if (window.event.srcElement.className == "line"){
		return true;
	}
	if (window.event.srcElement.className == "verticalrule"){
		return true;
	}
	if (window.event.srcElement.className == "rectangle"){
		return true;
	}
	if (window.event.srcElement.className == "picturefield"){
		return false;
	}
	changeMode();
}

function changeMode(){
	var elms = document.getElementsByTagName("A");

	if (fMode) {
		fMode = false;
	}else{
		fMode = true;
	} 
	for (var i = 0; i < elms.length; i++){
		ToggleDisp(elms(i),fMode);
	}
	return false;
	
}

function ToggleDisp(elm,fMode){

	var childs = elm.childNodes;
	var format,desc;
	var text;

	var xml;
	for (var i = 0; i < childs.length; i++){

		if (childs(i).XMLDocument){
		}else{
			text = i;
		}
	}

	format = elm.format;
	desc = elm.desc;

	var re = /'/g;
	if (fMode){
		childs(text).data = format.replace(re,"");
		elm.title = desc ;
	}else{
		childs(text).data = desc;
		elm.title = format.replace(re,"") ;
	}

}

//Format()で使用しているウィンドウポップアップオブジェクトを宣言
//(動的生成したフォーマットリストボックスから参照できるようにpublic宣言しておく)
var oPopup = window.createPopup();     

function Format(){
	var background_out = "#E4E4E4";           //マウスが外れた時の背景色
	var background_over = "#FFFFFF";          //マウスが乗った時の背景色

	var format;

	if (!fMode){
		return false;
	}
	formatElm = window.event.srcElement;
	var childs = window.event.srcElement.childNodes;
	var formats;

	//メニュー項目
	if (formatElm.type == "s"){
		return false;
	}
	if ((formatElm.type == "i") || (formatElm.type == "f")){
		var df
		//2007-03-14 -->
		if (formatElm.type == "f"){
			df = new Array(formatElm.slength + 1);
//			df = ".";
//			for (var i = 0; i < formatElm.slength; i++){
//				df = df + "0";
//			}
			df[0] = "";
			for (var i = 1; i <= formatElm.slength; i++){
				df[i] = ".";
				for (var j = 0; j < i; j++){
					df[i] = df[i] + "0";
				}
			}
		}else{
			df = new Array(1);
			df[0] = "";
		}
		//2007-03-14 <--

		if (formatElm.length > 3){
		//2007-03-14 -->
//			format = new Array(4);
			format = new Array(4 * df.length);
		//2007-03-14 <--
		}else{
		//2007-03-14 -->
//			format = new Array(2);
			format = new Array(2 * df.length);
		//2007-03-14 <--
		}
		//2007-03-14 -->
//		format[0] = '0';
//		for (var i = 1; i < formatElm.length; i++){
//			format[0] = '0' + format[0] ;
//		}
		var integralPart = '0';
		for (var i = 1; i < formatElm.length; i++){
			integralPart = '0' + integralPart;
		}		
		//2007-03-14 <--
		//2007-03-14 -->
//		if (formatElm.type == "f"){
//			format[0] = format[0] + df;
//		}
		for (var i=0; i < df.length; i++){
			format[i] = integralPart + df[i];
		}
		//2007-03-14 <--


		
		//ゼロサプレス
		//2007-03-14 -->
//		format[1]  = '0';
//		for (var i = 1; i < formatElm.length; i++){
//			format[1]  = '#' + format[1] ;
//		}
		integralPart  = '0';
		for (var i = 1; i < formatElm.length; i++){
			integralPart  = '#' + integralPart;
		}
		//2007-03-14 <--
		//2007-03-14 -->
//		if (formatElm.type == "f"){
//			format[1] = format[1] + df;
//		}
		for (var i=0; i < df.length; i++){
			var j = df.length + i;
			format[j] = integralPart + df[i];
		}
		//2007-03-14 <--
		
		if (formatElm.length > 3){
			//カンマ編集
			//2007-03-14 -->
//			format[2] = '0';
//			for (var i = 1; i < formatElm.length; i++){
//				if ((i+3)%3 == 0){
//					format[2] = ',' + format[2];
//				}
//				format[2] = '0' + format[2];
//			}
			integralPart = '0';
			for (var i = 1; i < formatElm.length; i++){
				if ((i+3)%3 == 0){
					integralPart = ',' + integralPart;
				}
				integralPart = '0' + integralPart;
			}
			//2007-03-14 <--
			//2007-03-14 -->
//			if (formatElm.type == "f"){
//				format[2] = format[2] + df;
//			}
			for (var i=0; i < df.length; i++){
				var j = df.length * 2 + i;
				format[j] = integralPart + df[i];
			}
			//2007-03-14 <--

			//カンマ編集＋ゼロサプレス
			//2007-03-14 -->
//			format[3] = '0';
//			for (var i = 1; i < formatElm.length; i++){
//				if ((i+3)%3 == 0){
//					format[3] = ',' + format[3];
//				}
//				format[3] = '#' + format[3];
//			}
			integralPart = '0';
			for (var i = 1; i < formatElm.length; i++){
				if ((i+3)%3 == 0){
					integralPart = ',' + integralPart;
				}
				integralPart = '#' + integralPart;
			}
			//2007-03-14 <--
			//2007-03-14 -->
//			if (formatElm.type == "f"){
//				format[3] = format[3] + df;
//			}
			for (var i=0; i < df.length; i++){
				var j = df.length * 3 + i;
				format[j] = integralPart + df[i];
			}
			//2007-03-14 <--
		}
	}else{
		var FormatXML = document.getElementById("FORMAT_" + formatElm.type);
		var XMLroot = FormatXML.selectSingleNode("/");
		var FormatList = XMLroot.getElementsByTagName("FORMAT");
		format = new Array(FormatList.length);
		for(var j = 0; j < FormatList.length; j++){
			format[j] = FormatList[j].text;
		}
	}
	
	var menu_status = "";                    //メニュー内容
  
		//メニュー内容作成
	var re = /'/g;

	//フォーマット設定(日付や性別等)をリストボックスで表示を行う。
	//当初はDIV属性のみでのラベル表示を行っていたが、マウスイベントでの背景色の高速切替でエラーになる場合があった(GAKEX-2627)
	menu_status += '<SELECT size="' + format.length + '" name="fList" onDblClick="parent.SetFormat(this.options[this.selectedIndex].value);parent.oPopup.hide();return false;">'

	for(count = 0; count < format.length; count++){
		menu_status += '<OPTION value="' + format[count].replace(re,"")  + '">' + format[count].replace(re,"") + '</OPTION>';
	}
	
	menu_status += '</SELECT>';


	var topper = event.clientX;              //ポップアップを表示するX座標を取得
	var lefter = event.clientY;              //ポップアップを表示するY座標を取得
	oPopup.document.body.innerHTML = menu_status; //ポップアップに表示する内容を設定
	//ポップアップを表示するメソッドをcall
	oPopup.show(topper, lefter, 180, (14 * format.length) + 10, document.body);
	event.returnValue = false;
	event.cancelBubble = true;

	//フォーマットリストボックスのオブジェクトを取得
	var elem = oPopup.document.getElementById("fList");
	//現在設定されているフォーマットをリストボックスの初期選択に
	elem.value = formatElm.format;

	return(false);
}

function SetFormat(format){

	var childs = formatElm.childNodes;
	var xml;
	var re = /'/g;

	for (var i = 0; i < childs.length; i++){

		if (childs(i).XMLDocument){
		}else{
			childs(i).data = format.replace(re,"");
		}
	}
	formatElm.format = format;
	
}

function Cancel(){

	event.returnValue = false;
	event.cancelBubble = true;
	return false;
}


var menu = null;
var lineItems = null;
function Contextmenu(){
	if(menu == null){
		menu = document.getElementById("MENU");
	}
	if(menu == null){
		return false;
	}
	lineItems = getLineItems();
	if(lineItems.length > 0){
	
	 	document.body.contentEditable = false;

		menu.style.display = "block";
		
		var x = event.offsetX;
		var y = event.offsetY;
		for (var srcElm = event.srcElement; srcElm.tagName.toLowerCase() != "body"; srcElm = srcElm.parentElement){
			if (srcElm.style.position == "absolute"){
			
				x += srcElm.style.pixelLeft;
				y += srcElm.style.pixelTop;
				
			}
		}
		menu.style.top = y;
		menu.style.left = x;
	}

	
	return false;
}

function Mdown(){

	if(event.srcElement.className != "menuitem"){
		if(menu != null){
			menu.style.display = "none";
			menu.style.top = -100;
			menu.style.left = -100;
		}
	}



 	document.body.contentEditable = true;

	
	//drag_switch  1:ドラッグ中、2:新規アイテム配置中、3:Resize中、9:複数選択中

	event.cancelBubble = true;
	fMove = false;


	if (drag_switch == 3) {
		return false;
	}

	if (drag_switch == 2) {

		//2009-11-09
		/*
		if (Elm.className == "field" || Elm.className == "picturefield"){
			Elm.innerText="";
			Elm.focus();
		}else{
			Resume();
		}
		*/
		if (Elm.className == "field"){
			Elm.innerText="";
			Elm.focus();
		}else if(Elm.className == "picturefield"){
			Elm.innerText="";
			Resume();
		}else{
			Resume();
		}
		
		
		drag_switch = 0;
		return false;
	}

	startX = event.offsetX;
	startY = event.offsetY;
	if ((window.event.srcElement.tagName.toLowerCase() == "p") || (window.event.srcElement.tagName.toLowerCase() == "body")){

		//2009-11-09
		currentTextRange = null;
		
		drag_switch = 9;
		event.returnValue = false;
		return false;

	}

	for (var srcElm = event.srcElement; srcElm.tagName.toLowerCase() != "body"; srcElm = srcElm.parentElement){
		if (srcElm.style.position == "absolute"){
			startX += srcElm.style.pixelLeft;
			startY += srcElm.style.pixelTop;
		}
	}

	if (window.event.srcElement.className != "verticalrule" && window.event.srcElement.className != "rectangle" && window.event.srcElement.className != "picturefield" && window.event.srcElement.className != "field" && window.event.srcElement.className != "line" && window.event.srcElement.tagName != "A"){
		drag_switch = 9;
		event.returnValue = false;
		return false;
	}

	drag_switch = 1;

	event.returnValue=false;
	return false;
}

function getLineItems()
{
	var sel, rng;


	var rng = document.body.createControlRange();	
	for (var i=0 ; i <rng.length; i++){
		rng.remove(i);
	}

	sel = document.selection;

	if (sel.type.toLowerCase() == "control"){
		rng = sel.createRange();
	
		for (var i=0 ; i <rng.length; i++){
			if (rng(i).className != "line" && rng(i).className != "verticalrule" && rng(i).className != "rectangle"){
				rng.remove(i);
			}
		}

	}
	return rng;
}


function Click(){
	var srcElm = window.event.srcElement;
	
	if(srcElm.className == "menuitem"){
		var width = srcElm.value;
		//alert(width);
		//var items = getLineItems();
		//alert(lineItems.length);
		for(var i=0; i<lineItems.length; i++){

			//alert(lineItems(i).outerHTML);

			if(lineItems(i).className == "line"){
				lineItems(i).style.height = width;
			}else if(lineItems(i).className == "verticalrule"){
				lineItems(i).style.width = width;
			}else if(lineItems(i).className == "rectangle"){
				//lineItems(i).style.borderWidth = width;
				lineItems(i).style.borderTopWidth = width;
				lineItems(i).style.borderBottomWidth = width;
				lineItems(i).style.borderLeftWidth = width;
				lineItems(i).style.borderRightWidth = width;
			}
		}
	}
	
	if(menu != null){
		menu.style.display = "none";
		menu.style.top = -100;
		menu.style.left = -100;
	}
}


function Mout(){
	var srcElm = window.event.srcElement;
	
	if(srcElm.className == "menuitem"){
		//alert(srcElm.id);
		srcElm.style.backgroundColor = "transparent";
		srcElm.style.color = "#000000";
	}
}

function Mmove(){	


	//drag_switch  1:ドラッグ中、2:新規アイテム配置中、3:Resize中、9:複数選択中

	event.cancelBubble = true;

	var srcElm = window.event.srcElement;
	
	if(srcElm.className == "menuitem"){
		//alert(srcElm.id);
		srcElm.style.backgroundColor = "#808080";
		srcElm.style.color = "#ffffff";
	}
	
	if (window.event.srcElement.className == "field"){
		var str;
		var fontSize = parseInt(window.event.srcElement.style.fontSize);
		fontSize = Math.round(fontSize / (100/72) * 10) / 10;
		str = '';
		str += 'フォント(' + window.event.srcElement.style.fontFamily;
		str += ' ' + fontSize + 'pt)';
		str += '\n';
		str += '位置(横:' + (window.event.srcElement.style.pixelLeft / 100) + 'in';
		str += ' 縦:' + (window.event.srcElement.style.pixelTop / 100) + 'in)';
		
		str += '\n';
		str += 'サイズ(横:半角' + Math.round(window.event.srcElement.style.posWidth * 2) + "桁";
		str += ' 縦:' + window.event.srcElement.LINECOUNT + "行)";

		str += '\n';
		str += '行高(' + Math.round(parseInt(event.srcElement.style.lineHeight) / parseInt(event.srcElement.style.fontSize) * 10) / 10 + '倍)';
		

		window.event.srcElement.title = str;
	}
	else if (window.event.srcElement.className == "picturefield"){
		var str;
		str = '';
		str += '位置(横:' + (window.event.srcElement.style.pixelLeft / 100) + 'in';
		str += ' 縦:' + (window.event.srcElement.style.pixelTop / 100) + 'in)';
		window.event.srcElement.title = str;
	}
	
	if (drag_switch == 3) {
	
		fMove = true;
		return false;
	}

	if(drag_switch == 0){
		return false;
	}

	var x = event.offsetX;
	var y = event.offsetY;


	for (var srcElm = event.srcElement; srcElm.tagName.toLowerCase() != "body"; srcElm = srcElm.parentElement){
		if (srcElm.style.position == "absolute"){
		
			x += srcElm.style.pixelLeft;
			y += srcElm.style.pixelTop;
			
		}
		
	}
	

	if(drag_switch == 1){
		ItemsMove(x - startX,y - startY);	
		startX = x;
		startY = y;
	}

	if(drag_switch == 2){

		Elm.style.pixelLeft = Elm.style.pixelLeft + (x - startX);
		Elm.style.pixelTop = Elm.style.pixelTop + (y - startY);
		Elm.style.pixelRight = Elm.style.pixelLeft + Elm.clientWidth;

		startX = x;
		startY = y;
	}
	
	fMove = true;
	return false;
}

function ItemsMove(x, y){
	var items = getItems();

	for (var i = 0; i < items.length; i++){
		items(i).style.pixelLeft = items(i).style.pixelLeft + x;
		items(i).style.pixelTop = items(i).style.pixelTop + y;
		items(i).style.pixelRight = items(i).style.pixelLeft + items(i).clientWidth;
	}
}



function Mup(){


	//drag_switch  1:ドラッグ中、2:新規アイテム配置中、3:Resize中、9:複数選択中

	event.cancelBubble = true;

	var endX = event.offsetX;
	var endY = event.offsetY;
		
	for (var srcElm = event.srcElement; srcElm.tagName.toLowerCase() != "body"; srcElm = srcElm.parentElement){
		if (srcElm.style.position == "absolute"){
			endX += srcElm.style.pixelLeft;
			endY += srcElm.style.pixelTop;
		}
	}

	if (drag_switch == 9){

		var elms = document.body.childNodes;

		var rng = document.body.createControlRange();
		
		for (var i=0 ; i <rng.length; i++){
			rng.remove(i);
		}

		if ((startX == endX) && (startY == endY)){
			if ((window.event.srcElement.className == "HEADER") ||
			    (window.event.srcElement.className == "DETAIL") || 
			    (window.event.srcElement.className == "SUM") || 
			    (window.event.srcElement.className == "TOTAL") ){
				rng.add(window.event.srcElement);
			}else{
				if (document.activeElement.tagName != "BODY"){
					document.activeElement.blur();
				}
			}

		}else{		
			for (var i=0 ; i <elms.length; i++){
		
				if ((elms(i).className == "field") || (elms(i).className == "verticalrule") || (elms(i).className == "rectangle") || (elms(i).className == "line") || (elms(i).className == "picturefield")){

					if (isFullContained(elms(i),startX,startY,endX,endY)){
	
						rng.add(elms(i));
					}
				}
			}
		}

		if (rng.length == 0){
		 	document.body.contentEditable = false;
		}
		
		rng.select();
	}

	if (drag_switch == 2){
		Elm.focus();
	}

	if (fMove){
		Resume();
	}


	drag_switch = 0;
	
	event.returnValue=false;
	return false;

}

function isFullContained(elm, startX, startY, endX, endY){

	var sX,eX,sY,eY;
	if (startX < endX){
		sX = startX;
		eX = endX;
	}else{
		sX = endX;
		eX = startX;
	}
	if (startY < endY){
		sY = startY;
		eY = endY;
	}else{
		sY = endY;
		eY = startY;
	}

	if (elm.style.pixelLeft < sX){
		return false;
	}
	if (elm.style.pixelLeft + elm.style.pixelWidth > eX){
		return false;
	}
	if (elm.style.pixelTop < sY){
		return false;
	}
	if (elm.style.pixelTop + elm.style.pixelHeight > eY){
		return false;
	}
	return true;

}

function isContained(elm, startX, startY, endX, endY){

	var sX,eX,sY,eY;
	if (startX < endX){
		sX = startX;
		eX = endX;
	}else{
		sX = endX;
		eX = startX;
	}
	if (startY < endY){
		sY = startY;
		eY = endY;
	}else{
		sY = endY;
		eY = startY;
	}

	if (elm.style.pixelLeft > eX){
		return false;
	}
	if (elm.style.pixelLeft + elm.style.pixelWidth < sX){
		return false;
	}
	if (elm.style.pixelTop > eY){
		return false;
	}
	if (elm.style.pixelTop + elm.style.pixelHeight < sY){
		return false;
	}
	return true;

}


function Keyup(){

//	alert(event.srcElement.tagName);
	//2009-11-09
	currentTextRange = null;

	code = fromCharCode(window.event.keyCode);

	if (sel = document.selection) {
	
		if (sel.type.toLowerCase() == "control"){
			return true;
		}
		var rng = sel.createRange();

		//2009-11-09
		currentTextRange = rng;
		
		var el = rng.parentElement();

		if (el.tagName.toLowerCase() == "font"){
			
			var childs = el.childNodes;
			var text = "";
			for (var j=0 ; j <childs.length; j++){
			
				if(childs(j).nodeType == 3){	//Text node
					text = text + childs(j).nodeValue;
				}
			}
			el.parentElement.removeChild(el);
			if(text > ""){
				rng.pasteHTML(text);
				event.returnValue=false;
				return false;
			}
		}
		if (el.tagName.toLowerCase() == "span"){
		
/*
		    switch(code){
			case   'left': return true;
				el.insertAdjacentHTML("afterEnd",'<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
				el.parentElement.removeChild(el);
				event.returnValue=true;
				return true;
			case   'right': return true;
				el.insertAdjacentHTML("beforeBegin",'<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
				el.parentElement.removeChild(el);
				event.returnValue=true;
				return true;
			case   'up': return true;
				el.insertAdjacentHTML("afterEnd",'<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
				el.parentElement.removeChild(el);
				event.returnValue=true;
				return true;
			case   'down': return true;
				el.insertAdjacentHTML("beforeBegin",'<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
				el.parentElement.removeChild(el);
				event.returnValue=true;
				return true;
			default:
			}
*/		
			var childs = el.childNodes;
			var beforeBR = "";
			var afterBR = "";
			var br=false;
			var hasText=false;
			var hasBefore=false;
			var hasAfter=false;
//			alert("childs.length:"+childs.length);
			for (var j=0 ; j <childs.length; j++){
			
//				alert("childs("+j+").nodeType:"+childs(j).nodeType);
				if(childs(j).nodeType == 3){	//Text node
					if (br){
						afterBR = afterBR + childs(j).nodeValue;
						hasAfter = true;
					}else{
						beforeBR = beforeBR + childs(j).nodeValue;
						hasBefore = true;
					}
					hasText = true;
				}else{
					if (childs(j).tagName == "BR"){
//						alert(childs(j).tagName);
						br = true;
					}
				}
				
			}
//			alert(el.outerHTML);
//			alert(" br:"+ br+"beforeBR:"+ beforeBR +" afterBR:"+ afterBR);
			if(br){
				if(hasText){
					if(!hasAfter){
						el.insertAdjacentHTML("afterEnd",'<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
						el.parentElement.removeChild(el);
						rng.pasteHTML(beforeBR);
					}else{
						el.parentElement.removeChild(el);
						rng.pasteHTML(beforeBR+'<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>'+afterBR);
					}
					event.returnValue=true;
					return true;
				}	
			}else{
				el.parentElement.removeChild(el);
				if(hasText){
					rng.pasteHTML(beforeBR + afterBR);
					event.returnValue=true;
					return true;
				}	
			}
		}
	}

}

function Keydown(){

	window.event.cancelBubble = true;

	var sel, rng, dup, el, code;
	
	code = fromCharCode(window.event.keyCode);



//	if (code == 'enter'){
//		event.returnValue=false;
//		return false;
//	}
		
	if (window.event.srcElement.tagName.toLowerCase() == "body"){

		if (code == 'left'){
			moveLeft();
			return false;
		}
		if (code == 'up'){
			moveUp();
			return false;
		}
		if (code == 'right'){
			moveRight();
			return false;
		}
		if (code == 'down'){
			moveDown();
			return false;
		}
		if (code == 'del'){

			rng = getItems();
			rng.select();
			window.document.execCommand("delete",false,null);
			return false;
		}
		event.returnValue=false;
		return false;
	}
	
	if (window.event.srcElement.className == "recordArea"){
		if (code == 'del'){

			rng = getItems();
			rng.select();
			window.document.execCommand("delete",false,null);
			return false;
		}
		event.returnValue=false;
		return false;
	}
	
	if (sel = document.selection) {
	
		if (sel.type.toLowerCase() == "control"){
			return true;
		}
		
		rng = sel.createRange();

		el = rng.parentElement();
		
		if (el.tagName.toLowerCase() == "a"){
		    switch(code){
			case   'left': return true;
			case   'right': return true;
			case   'up': return true;
			case   'down': return true;
			case   'del':
				el.parentElement.removeChild(el);
				event.returnValue=false;
				return false;
				
				break;
	   		case   'back':
				el.parentElement.removeChild(el);
				event.returnValue=false;
				return false;
					
				break;			
		    default:
				dup = rng.duplicate();
				dup.moveStart("character",-1);
				rng.expand("character");
				dup.expand("character");
				if (rng.text != dup.text){
					event.returnValue=false;
					return false;
				}
				return true;
			}
		}
		
		if (el.tagName.toLowerCase() == "span"){
		    switch(code){
			case   'del':
				el.parentElement.removeChild(el);
				event.returnValue=false;
				return false;
				
				break;
	   		case   'back':
				el.parentElement.removeChild(el);
				event.returnValue=false;
				return false;
					
				break;			
	   		case   'enter':
				el.parentElement.removeChild(el);
				rng.pasteHTML('<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
				rng.pasteHTML('<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
				event.returnValue=false;
				return false;
					
				break;			
		    default:
				event.returnValue=true;
				return true;
			}
		}
		
		if(el.className == "field"){
		
			if (code == 'enter'){
			
				if (el.style.whiteSpace == "nowrap" ){
//					rng.pasteHTML("<BR>");
					rng.pasteHTML('<SPAN style="LETTER-SPACING: 0px;"><BR></SPAN>');
//					rng.select();			
					event.returnValue=false;
					return false;
				}else{
					event.returnValue=false;
					return false;
				}
			}		
		
			if (code == "back"){
				var target;
				rng.move("character",-1);
				target = rng.parentElement();
				if (target.tagName.toLowerCase() == "a"){
					el.removeChild(target);
					event.returnValue=false;
					return false;
				}
			}
			if (code == "del"){
				var target;
				rng.move("character",1);
				target = rng.parentElement();
				if (target.tagName.toLowerCase() == "a"){
					el.removeChild(target);
					event.returnValue=false;
					return false;
				}
			}
			event.returnValue=true;
			return true;
		}
	}
	event.returnValue=false;
	return false;
}

function getItems()
{
	var sel, rng;


	var rng = document.body.createControlRange();	
	for (var i=0 ; i <rng.length; i++){
		rng.remove(i);
	}

	sel = document.selection;

	if (sel.type.toLowerCase() == "control"){
		rng = sel.createRange();
	
		for (var i=0 ; i <rng.length; i++){
			if (rng(i).className != "field" && rng(i).className != "line" && rng(i).className != "verticalrule" && rng(i).className != "rectangle" && rng(i).className != "picturefield"){
				rng.remove(i);
			}
		}

	}

	

	return rng;
}

function moveLeft()
{
	ItemsMove(-1,0);
}

function moveRight()
{
	ItemsMove(1,0);
}

function moveUp()
{
	ItemsMove(0,-1);
}

function moveDown()
{
	ItemsMove(0,1);
}

// キーコード文字列変換関数
function fromCharCode(c){

	//alert(c + ":" + ch);

    var ch=String.fromCharCode(c);
    // KeyCode 変換( Windows )
    switch(c){
    case   8: ch='back';  break;
    case  13: ch='enter'; break;
    case  27: ch='esc';   break;
    case  37: ch='left';  break;
    case  38: ch='up';    break;
    case  39: ch='right'; break;
    case  40: ch='down';  break;
    case  46: ch='del';   break;
    case 106: ch='*';     break;
    case 107: ch='+';     break;
    case 109: ch='-';     break;
    case 110: ch='.';     break;
    case 111: ch='/';     break;
    case 186: ch=(event.shiftKey?'*':':');  break;
    case 187: ch=(event.shiftKey?'+':';');  break;
    case 188: ch=(event.shiftKey?'<':',');  break;
    case 189: ch=(event.shiftKey?'=':'-');  break;
    case 190: ch=(event.shiftKey?'>':'.');  break;
    case 191: ch=(event.shiftKey?'?':'/');  break;
    case 192: ch=(event.shiftKey?'`':'@');  break;
    case 219: ch=(event.shiftKey?'{':'[');  break;
    case 220: ch=(event.shiftKey?'|':'\\'); break;
    case 221: ch=(event.shiftKey?'}':']');  break;
    case 222: ch=(event.shiftKey?'~':'^');  break;
    case 226: ch=(event.shiftKey?'_':'\\'); break;
    default:
      if(c>=96 && c<=105) ch='0123456789'.charAt(c-96);
      else if(event.shiftKey){
        if(ch>=0 && ch<=9) ch=' !"#$%&\'()'.charAt(ch);
        else               ch=ch.toUpperCase();
      } else ch=ch.toLowerCase();
      break;
    }
    return ch;
}


