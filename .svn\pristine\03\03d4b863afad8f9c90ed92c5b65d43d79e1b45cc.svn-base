<%--
 * 卒業見込判定結果修正
 * Kme00902.jsp
 * 作者: 王海シン
 * 作成日: 2005/11/23
 * version 1.0
 *
 * //障害対応
 * //@@@@@ (F-UT-0000-00) JAST k-hara 2006/10/4
 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kme00902.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<TITLE>Kme00902.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
check('htmlGakuseiDataTable','checkbox1');
}
function func_2(thisObj, thisEvent) {
uncheck('htmlGakuseiDataTable','checkbox1');
}</SCRIPT>
</HEAD>
<f:loadBundle basename="properties.message" var="msg" />
<f:view locale=#{SYSTEM_DATA.locale}>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kme00902.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kme00902.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kme00902.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kme00902.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				styleClass="outputText" id="htmlMessage"
				value="#{requestScope.DISPLAY_INFO.displayMessage}" escape="false"></h:outputText>
			</FIELDSET>
			<!-- @@@@@ (F-UT-0000-00) JAST k-hara 2006/10/4 START-->
			<!-- @@@@@ 確認メッセージの削除 -->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton" id="button3"
				action="#{pc_Kme00902.doModoruAction}"></hx:commandExButton></DIV>
			
			<!-- @@@@@ (F-UT-0000-00) JAST k-hara 2006/10/4 END-->
			
			<!--↓content↓-->
			<DIV id="content"><!-- ↓ここにコンポーネントを配置 -->
			<DIV class="column">
						<TABLE width="650" class="table" border="0" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TH class="v_a" width="180"><h:outputText
							styleClass="outputText" id="labelGakunen"
							value="#{pc_Kme00902.propGakunen.labelName}"
							style="#{pc_Kme00902.propGakunen.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:outputText styleClass="outputText"
							id="htmlGakunen" value="#{pc_Kme00902.propGakunen.stringValue}"
							style="#{pc_Kme00902.propGakunen.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="180"><h:outputText
							styleClass="outputText" id="lableSemesuta"
							value="#{pc_Kme00902.propSemesuta.labelName}"
							style="#{pc_Kme00902.propSemesuta.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:outputText styleClass="outputText"
							id="htmlSemesuta" value="#{pc_Kme00902.propSemesuta.stringValue}"
							style="#{pc_Kme00902.propSemesuta.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="180"><h:outputText
							styleClass="outputText" id="labelKarikyuramuName"
							value="#{pc_Kme00902.propKarikyuramuName.labelName}"
							style="#{pc_Kme00902.propKarikyuramuName.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:outputText styleClass="outputText"
							id="htmlKarikyuramuName"
							value="#{pc_Kme00902.propKarikyuramuName.stringValue}"
							style="#{pc_Kme00902.propKarikyuramuName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="180"><h:outputText
							styleClass="outputText" id="labelYoteiNengetu"
							value="#{pc_Kme00901.propYoteiNengetu.name}"
							style=""></h:outputText></TH>
									<TD width="469"><h:outputText
							styleClass="outputText" id="htmlYoteiNengetu"
							value="#{pc_Kme00901.propYoteiNengetu.dateValue}"
							style="">
							<f:convertDateTime pattern="yyyy年MM月" />
						</h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<HR style="hr" noshade class="hr">
						<TABLE width="650" border="0" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD align="right" width="650"><h:outputText
										styleClass="outputText" id="text3"
										value="#{pc_Kme00902.propGakuseiDatatable.listCount}"></h:outputText><h:outputText
										styleClass="outputText" id="text2" value="件"></h:outputText></TD>
								</TR>
								<!-- @@@@@ (F-UT-0000-00) JAST k-hara 2006/10/4 START-->
								<!-- @@@@@ 学生氏名のALT対応 -->
								<TR>
									<TD align="center" width="650">
									<DIV style="overflow:auto;height:270px" id="listScroll" class="listScroll">	
										<h:dataTable border="0" cellpadding="2" cellspacing="0"
										columnClasses="columnClass1" headerClass="headerClass"
										footerClass="footerClass" rowClasses="#{pc_Kme00902.propGakuseiDatatable.rowClasses}"
										styleClass="meisai_scroll" id="htmlGakuseiDataTable"
										value="#{pc_Kme00902.propGakuseiDatatable.list}" var="varlist">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="column0headtext" styleClass="outputText"></h:outputText>
											</f:facet>
											<f:attribute value="25" name="width" />
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
												id="checkbox1" value="#{varlist.checked}"></h:selectBooleanCheckbox>
											<f:attribute value="center" name="align" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText" id="column1headtext" value="学籍番号"></h:outputText>
											</f:facet>
											<f:attribute value="175" name="width" />
								<h:outputText styleClass="outputText" id="htmlGakuseki"
									value="#{varlist.gakusekiBango}"></h:outputText>
							</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="学生氏名"
													id="column2headtext"></h:outputText>
											</f:facet>
											<f:attribute value="300" name="width" />
								<h:outputText styleClass="outputText" id="htmlGakuseimei"
									value="#{varlist.gakuseiName.displayValue}" title="#{varlist.gakuseiName.stringValue}"></h:outputText>
							</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="卒業見込判定区分"
													id="column3headtext"></h:outputText>
											</f:facet>
											<f:attribute value="150" name="width" />
											<h:outputText styleClass="outputText" id="htmlHanteikubun"
												value="#{varlist.sotugyouFlg}"></h:outputText>
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
								<!-- @@@@@ (F-UT-0000-00) JAST k-hara 2006/10/4 END-->
								<TR>
									<TD width="650" align="left">
									<TABLE border="0" border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD><hx:commandExButton type="button"
										value="一括チェック" styleClass="check" id="button1"
										onclick="return func_1(this, event);"></hx:commandExButton></TD>
												<TD width="41"><hx:commandExButton type="button"
										value="一括解除" styleClass="uncheck" id="button2"
										onclick="return func_2(this, event);"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE width="650" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
						<TH class="v_a" width="180"><h:outputText styleClass="outputText"
							id="labelMikomiHanteiKubu"
							value="#{pc_Kme00902.propSotukouMikomiHanteiKubun.labelName}"
							style="#{pc_Kme00902.propSotukouMikomiHanteiKubun.labelStyle}"></h:outputText></TH>
						<TD width="469"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlMikomiHanteiKubu"
							value="#{pc_Kme00902.propSotukouMikomiHanteiKubun.stringValue}"
							style="#{pc_Kme00902.propSotukouMikomiHanteiKubun.style};width:155px">
							<f:selectItems
								value="#{pc_Kme00902.propSotukouMikomiHanteiKubun.list}" />
						</h:selectOneMenu></TD>
								</TR>
								<TR>
						<TH class="v_b" width="180"><h:outputText styleClass="outputText"
							id="labelMikomiHanteiHi"
							value="#{pc_Kme00902.propSotukyouMikomiHanteihi.labelName}"
							style="#{pc_Kme00902.propSotukyouMikomiHanteihi.labelStyle}"></h:outputText></TH>
						<TD width="469"><h:inputText styleClass="inputText"
							id="htmlMikomiHanteiHi"
							value="#{pc_Kme00902.propSotukyouMikomiHanteihi.dateValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
								</TR>
								<TR>
						<TH class="v_c" width="180"><h:outputText styleClass="outputText"
							id="labelMikomihi"
							value="#{pc_Kme00902.propSotukyouMikomihi.labelName}"
							style="#{pc_Kme00902.propSotukyouMikomihi.labelStyle}"></h:outputText></TH>
						<TD width="469"><h:inputText styleClass="inputText"
							id="htmlMikomihi"
							value="#{pc_Kme00902.propSotukyouMikomihi.dateValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
						</h:inputText> <h:outputText styleClass="note" id="text1"
										value="※証明書等の｢卒業見込日」として使用されます"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<HR style="hr" class="hr" noshade>
						<TABLE width="650" border="0" cellpadding="0" cellspacing="0"
							class="button_bar">
							<TBODY>
								<TR>
									<TD align="center" height="8"><hx:commandExButton type="submit"
										value="確定" styleClass="commandExButton_dat" id="doJikkou"
										action="#{pc_Kme00902.doDoJikkouAction}"
										confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
			</DIV>
			</DIV>
			</DIV>

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
