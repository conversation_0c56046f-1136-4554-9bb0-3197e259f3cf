<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc01801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsc01801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>
<style type="text/css">
<!--
.setWidth TD {width: 120px; white-space: nowrap;}
-->
</style>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc01801.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc01801.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc01801.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc01801.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>


						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" class="table" cellspacing="0"
										width="100%">
										<TBODY>
											<TR>
												<TH class="v_a" width="153"><h:outputText
													styleClass="outputText" id="lblBunmenKbn"
													value="#{pc_Nsc01801.propBunmenKbn.labelName}"
													style="#{pc_Nsc01801.propBunmenKbn.labelStyle}"></h:outputText></TH>
												<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlBunmenKbn" style="width:420px;"
													value="#{pc_Nsc01801.propBunmenKbn.value}"
													disabled="#{pc_Nsc01801.propBunmenKbn.disabled}"
													readonly="#{pc_Nsc01801.propBunmenKbn.readonly}"
													tabindex="1">
													<f:selectItems value="#{pc_Nsc01801.propBunmenKbn.list}" />
												</h:selectOneMenu></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="20"></TD>
									<TD width="60" align="left"><hx:commandExButton type="submit"
										value="選択" styleClass="commandExButton" id="selectBunmen"
										action="#{pc_Nsc01801.doSelectBunmenAction}"
										disabled="#{pc_Nsc01801.propSelectBunmen.disabled}" tabindex="2"></hx:commandExButton></TD>
									<TD width="60" align="left"><hx:commandExButton type="submit"
										value="解除" styleClass="commandExButton" id="resetBunmen"
										action="#{pc_Nsc01801.doResetBunmenAction}"
										disabled="#{pc_Nsc01801.propResetBunmen.disabled}" tabindex="3"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><BR>

						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="80%">
							<TBODY>
								<TR>
									<TH class="v_b" colspan="2" width="150"><h:outputText
										styleClass="outputText" id="lblBunmenSitei"
										value="#{pc_Nsc01801.propBunmenSitei.labelName}"
										style="#{pc_Nsc01801.propBunmenSitei.labelStyle}"></h:outputText></TH>
									<TD colspan=""><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlBunmenSitei" style="width:300px;"
										value="#{pc_Nsc01801.propBunmenSitei.value}"
										disabled="#{pc_Nsc01801.propBunmenSitei.disabled}"
										readonly="#{pc_Nsc01801.propBunmenSitei.readonly}"
										tabindex="4">
										<f:selectItems value="#{pc_Nsc01801.propBunmenSitei.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_c" width="150" colspan="2"><h:outputText
										styleClass="outputText" id="lblHakkoDate"
										value="#{pc_Nsc01801.propHakkoDate.labelName}"
										style="#{pc_Nsc01801.propHakkoDate.labelStyle}"></h:outputText></TH>
									<TD colspan=""><h:inputText styleClass="inputText"
										id="htmlHakkoDate" size="10"
										value="#{pc_Nsc01801.propHakkoDate.dateValue}"
										disabled="#{pc_Nsc01801.propHakkoDate.disabled}"
										readonly="#{pc_Nsc01801.propHakkoDate.readonly}"
										style="#{pc_Nsc01801.propHakkoDate.style}" tabindex="5">
										<f:convertDateTime pattern="yyyy/MM/dd" />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2" width="150"><h:outputText
										styleClass="outputText" id="lblOutBiko"
										value="#{pc_Nsc01801.propOutBiko.labelName}"></h:outputText></TH>
									<TD colspan=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlOutBiko"
										value="#{pc_Nsc01801.propOutBiko.value}"
										disabled="#{pc_Nsc01801.propOutBiko.disabled}"
										readonly="#{pc_Nsc01801.propOutBiko.readonly}" tabindex="6">
										<f:selectItem itemValue="0" itemLabel="出力しない" />
										<f:selectItem itemValue="1" itemLabel="男女内訳出力" />
										<f:selectItem itemValue="2" itemLabel="評定平均値出力" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2" width="150"><h:outputText
										styleClass="outputText" id="lblAtenaLabel"
										value="#{pc_Nsc01801.propAtenaLabel.labelName}"></h:outputText></TH>
									<TD colspan=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlAtenaLabel"
										value="#{pc_Nsc01801.propAtenaLabel.value}"
										disabled="#{pc_Nsc01801.propAtenaLabel.disabled}"
										readonly="#{pc_Nsc01801.propAtenaLabel.readonly}"
										tabindex="7">
										<f:selectItem itemValue="0" itemLabel="連動出力する" />
										<f:selectItem itemValue="1" itemLabel="連動出力しない" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_e" width="130"><h:outputText
										styleClass="outputText" id="lblYubinHyoji"
										value="#{pc_Nsc01801.propYubinHyoji.labelName}"></h:outputText></TH>
									<TD colspan=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlYubinHyoji"
										value="#{pc_Nsc01801.propYubinHyoji.value}"
										disabled="#{pc_Nsc01801.propYubinHyoji.disabled}"
										readonly="#{pc_Nsc01801.propYubinHyoji.readonly}"
										tabindex="8">
										<f:selectItem itemValue="1" itemLabel="あり" />
										<f:selectItem itemValue="0" itemLabel="なし" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_f" width="130"><h:outputText
										styleClass="outputText" id="lblLayout"
										value="#{pc_Nsc01801.propLayout.labelName}"></h:outputText></TH>
									<TD colspan=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlLayout"
										value="#{pc_Nsc01801.propLayout.value}"
										disabled="#{pc_Nsc01801.propLayout.disabled}"
										readonly="#{pc_Nsc01801.propLayout.readonly}" tabindex="9">
										<f:selectItem itemValue="0" itemLabel="Ａ４縦" />
										<f:selectItem itemValue="1" itemLabel="Ａ３横" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_f" width="130">
										<h:outputText styleClass="outputText"
										id="lblKaishiIchi"
										value="#{pc_Nsc01801.propKaishiIchi.labelName}">
										</h:outputText>
									</TH>
									<TD colspan="">
										<h:inputText styleClass="inputText"
										id="htmlKaishiIchi" size="2"
										value="#{pc_Nsc01801.propKaishiIchi.integerValue}"
										style="#{pc_Nsc01801.propKaishiIchi.style}" tabindex="10">
										<f:convertNumber type="number" pattern="##;##" />
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_g" width="130"><h:outputText
										styleClass="outputText" id="lblTray"
										value="#{pc_Nsc01801.propTray.labelName}"
										style="#{pc_Nsc01801.propTray.labelStyle}"></h:outputText></TH>
									<TD colspan=""><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlTray" style="width:450px;"
										value="#{pc_Nsc01801.propTray.value}"
										disabled="#{pc_Nsc01801.propTray.disabled}"
										readonly="#{pc_Nsc01801.propTray.readonly}" tabindex="11">
										<f:selectItems value="#{pc_Nsc01801.propTray.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="group_label_bottom" width="20"></TH>
									<TH class="v_a" width="130"><h:outputText
										styleClass="outputText" id="lblBikou"
										value="#{pc_Nsc01801.propBikou.labelName}"
										style="#{pc_Nsc01801.propBikou.labelStyle}"></h:outputText></TH>
									<TD colspan=""><h:inputText styleClass="inputText"
										id="htmlBikou" size="40"
										value="#{pc_Nsc01801.propBikou.stringValue}"
										disabled="#{pc_Nsc01801.propBikou.disabled}" maxlength="40"
										readonly="#{pc_Nsc01801.propBikou.readonly}" 
										style="#{pc_Nsc01801.propBikou.style}" tabindex="12">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_e" colspan="2" width="150"><h:outputText
											styleClass="outputText" id="lblTaisyoA"
											value="出力対象"></h:outputText></TH>
										<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
											id="htmlTaisyo"
											value="#{pc_Nsc01801.propTaisyo.checked}" tabindex="13">
											</h:selectBooleanCheckbox><h:outputText
											styleClass="outputText" id="lblTaisyoB" value="新規校のみ出力">
										</h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
						<h:outputText styleClass="outputText" id="lblComment" styleClass="note"
								value="※「出力対象」は「指定校決定通知書」選択時のみ有効です。"></h:outputText>
							<BR>
							<BR>
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" class="table" cellspacing="0"
										width="100%">
										<TBODY>
											<TR>
												<TH class="v_b" width="153"><h:outputText
													styleClass="outputText" id="lblGako"
													value="#{pc_Nsc01801.propGako.labelName}"
													style="#{pc_Nsc01801.propGako.labelStyle}"></h:outputText></TH>
												<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlGako" style="width:420px;"
													value="#{pc_Nsc01801.propGako.value}"
													disabled="#{pc_Nsc01801.propGako.disabled}"
													readonly="#{pc_Nsc01801.propGako.readonly}" tabindex="14">
													<f:selectItems value="#{pc_Nsc01801.propGako.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_c" width="153"><h:outputText
													styleClass="outputText" id="lblNyushiNendo"
													value="#{pc_Nsc01801.propNyushiNendo.labelName}"
													style="#{pc_Nsc01801.propNyushiNendo.labelStyle}"></h:outputText></TH>
												<TD width="120"><h:inputText styleClass="inputText"
													id="htmlNyushiNendo" size="5" 
													value="#{pc_Nsc01801.propNyushiNendo.dateValue}"
													disabled="#{pc_Nsc01801.propNyushiNendo.disabled}"
													readonly="#{pc_Nsc01801.propNyushiNendo.readonly}"
													style="#{pc_Nsc01801.propNyushiNendo.style}" tabindex="15">
													<f:convertDateTime pattern="yyyy" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TH width="150" class="v_d"><h:outputText
													styleClass="outputText" id="lblNyushiGakkiNo"
													value="#{pc_Nsc01801.propNyushiGakkiNo.labelName}"
													style="#{pc_Nsc01801.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
												<TD><h:inputText styleClass="inputText"
													id="htmlNyushiGakkiNo" size="2" 
													disabled="#{pc_Nsc01801.propNyushiGakkiNo.disabled}"
													readonly="#{pc_Nsc01801.propNyushiGakkiNo.readonly}"
													style="#{pc_Nsc01801.propNyushiGakkiNo.style}"
													value="#{pc_Nsc01801.propNyushiGakkiNo.integerValue}" tabindex="16">
													<f:convertNumber pattern="#0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="20"></TD>
									<TD width="60"></TD>
									<TD width="60"></TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%">
										<TBODY>
											<TR>
												<TH class="v_b" width="153" style="border-top:none;"><h:outputText
													styleClass="outputText" id="lblNysSbtBunrui"
													value="#{pc_Nsc01801.propNysSbtBunrui.labelName}"
													style="#{pc_Nsc01801.propNysSbtBunrui.labelStyle}"></h:outputText></TH>
												<TD style="border-top:none;"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlNysSbtBunrui"
													readonly="#{pc_Nsc01801.propNysSbtBunrui.readonly}"
													disabled="#{pc_Nsc01801.propNysSbtBunrui.disabled}"
													rendered="#{pc_Nsc01801.propNysSbtBunrui.rendered}"
													value="#{pc_Nsc01801.propNysSbtBunrui.stringValue}" tabindex="17">
													<f:selectItems value="#{pc_Nsc01801.propNysSbtBunrui.list}" />
												</h:selectOneMenu></TD>												
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="20"></TD>
									<TD width="60" align="left" valign="bottom"><hx:commandExButton type="submit"
										value="選択" styleClass="commandExButton" id="selectNendoGakki"
										 action="#{pc_Nsc01801.doSelectNendoGakkiAction}"
										disabled="#{pc_Nsc01801.propSelectNendoGakki.disabled}" tabindex="18"></hx:commandExButton></TD>
									<TD width="60" align="left" valign="bottom"><hx:commandExButton type="submit"
										value="解除" styleClass="commandExButton" id="resetNendoGakki"
										
										disabled="#{pc_Nsc01801.propResetNendoGakki.disabled}"
										action="#{pc_Nsc01801.doResetNendoGakkiAction1}" tabindex="19"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><BR><BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" style=""
							class="table" width="80%">
							<TBODY>
								<TR>
									<TH style="" class="group_label_top" width="150" colspan="2"><h:outputText
										styleClass="outputText" id="lblNysSbt"
										value="#{pc_Nsc01801.propNysSbt.labelName}"
										style="#{pc_Nsc01801.propNysSbt.labelStyle}"></h:outputText></TH>
									<TD colspan="" class=""><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlNysSbt"
										style="width:98%;" value="#{pc_Nsc01801.propNysSbt.value}"
										disabled="#{pc_Nsc01801.propNysSbt.disabled}"
										readonly="#{pc_Nsc01801.propNysSbt.readonly}" tabindex="20">
										<f:selectItems value="#{pc_Nsc01801.propNysSbt.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH style="" class="group_label_bottom" width="20"></TH>
									<TH style="" class="v_e" width="130"><h:outputText
										styleClass="outputText" id="lblNysSbtName"
										value="#{pc_Nsc01801.propNysSbtKbn.labelName}"
										style="#{pc_Nsc01801.propNysSbtKbn.labelStyle}"></h:outputText></TH>
									<TD class=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlNysSbtKbn"
										value="#{pc_Nsc01801.propNysSbtKbn.value}"
										readonly="#{pc_Nsc01801.propNysSbtKbn.readonly}" 
										disabled="#{pc_Nsc01801.propNysSbtKbn.disabled}" tabindex="21">
										<f:selectItem itemValue="0" itemLabel="学内名称" />
										<f:selectItem itemValue="1" itemLabel="対外名称" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="80%" class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="exec" 
										action="#{pc_Nsc01801.doExecAction}"
										disabled="#{pc_Nsc01801.propExec.disabled}" tabindex="22"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

