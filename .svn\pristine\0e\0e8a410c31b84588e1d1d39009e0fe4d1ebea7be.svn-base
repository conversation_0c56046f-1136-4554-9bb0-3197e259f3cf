<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00102T03.java" --%><%-- /jsf:pagecode --%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00102T03.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@page import="com.jast.gakuen.rev.ns.util.UtilNsCheck"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsc00102T03.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
function onReturnClick(id) {
// 「戻る」ボタンクリック処理

	var args = new Array();
	args[0] = "";
	return confirm(messageCreate(id, args));
}
function func_1(thisObj, thisEvent) {
changeScrollPosition('scrollJuken','listscroll');
}
function func_2(thisObj, thisEvent) {
	// チェックディジットを取得する
	var servlet = "rev/ns/CheckDigitAJAX";
	var target = "form1:htmlCheckDigit"; 
	var args = new Array();
	args['code1'] = document.getElementById("form1:inCtrPlaceCd").value;
	args['code2'] = document.getElementById("form1:inCtrJukenCd").value;
	args['code3'] = '10';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function confirmOk() {
	var exeutable = document.getElementById('form1:htmlExecutable').value
	//全タブ共通 登録ボタン押下時
	if (document.getElementById('form1:htmlExecutableFlg').value == "0") {
		if (exeutable == "0") {
			document.getElementById('form1:htmlExecutable').value = "1"
		} else {
			document.getElementById('form1:htmlExecutable').value = "2"
		}
		indirectClick('exec');
	//全タブ共通 削除ボタン押下時
	} else if(document.getElementById('form1:htmlExecutableFlg').value == "1"){
		document.getElementById('form1:htmlExecutable').value = "1"
		indirectClick('delete');
		
	//タブ内登録ボタン押下時
	} else{
		//共通受験番号の付番し直しワーニングの場合にOKボタンを
		//押下された場合は、共通受験番号付番し直しフラグを1に更新
		if(exeutable == "3"){
			document.getElementById('form1:htmlUpdateKyotuFuban').value = "1"
		}	
		indirectClick('execJkn');
	}
}
function confirmCancel() {
	//タブ内登録ボタン押下時
	if(document.getElementById('form1:htmlExecutableFlg').value == "2"){
		//共通受験番号の付番し直しワーニングの場合にキャンセルボタンを
		//押下された場合は、共通受験番号付番し直しフラグを「0」に更新し
		//再度確定ボタンを押下した状態とする。
		if(document.getElementById('form1:htmlExecutable').value == "3"){
			document.getElementById('form1:htmlUpdateKyotuFuban').value = "0"
			indirectClick('execJkn');
		}else{
			document.getElementById('form1:htmlUpdateKyotuFuban').value = "0"
			document.getElementById('form1:htmlExecutable').value = "0";
		}	
		
	//全タブ共通 登録ボタン押下時
	//全タブ共通 削除ボタン押下時		
	}else{
		document.getElementById('form1:htmlExecutable').value = "0";
	}

	return false;
}
function func_sgnSerch(thisObj, thisEvent) {
	//ローカル用
	openModalWindow("", "PNsc0101", "<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
	setTarget("PNsc0101");
	return true;
}
function func_sgnDummy(thisObj, thisEvent) {
	//志願者検索戻り用
}
window.onload = function (){
	// チェックディジットを取得する
	var servlet = "rev/ns/CheckDigitAJAX";
	var target = "form1:htmlCheckDigit"; 
	var args = new Array();
	args['code1'] = document.getElementById("form1:inCtrPlaceCd").value;
	args['code2'] = document.getElementById("form1:inCtrJukenCd").value;
	args['code3'] = '10';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
</SCRIPT>
<style type="text/css">
</style>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY onload="return func_1(this, event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Nsc00102T03.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Nsc00102T03.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Nsc00102T03.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Nsc00102T03.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton" id="button1"
				onclick="return onReturnClick('#{msgCO.CO_MSG_0013W}');"
				action="#{pc_Nsc00102T03.doButton1Action}"></hx:commandExButton> <!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<%
				boolean isKsknGhgr = false;

				if(UtilNsCheck.isKSKN_GHGR()){
				//「共通試験／合否判定グループ」機能の適用可の場合
					isKsknGhgr = true;
				}
			%>
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
								<TBODY>
									<TR>
										<TD style="" rowspan="2" align="left">
											<TABLE width="550" cellpadding="0" cellspacing="0"
												class="table">
												<TBODY>
													<TR>
														<TH style="" class="v_a" width="150"><h:outputText
															styleClass="outputText" id="lblSgnCd"
															style="#{pc_Nsc00102T03.nsc00102.propSgnCd.labelStyle}"
															value="#{pc_Nsc00102T03.nsc00102.propSgnCd.labelName}"></h:outputText></TH>
														<TD width="400"><h:inputText styleClass="inputText"
															id="htmlSgnCd" size="17"
															value="#{pc_Nsc00102T03.nsc00102.propSgnCd.stringValue}"
															disabled="#{pc_Nsc00102T03.nsc00102.propSgnCd.disabled}"
															maxlength="#{pc_Nsc00102T03.nsc00102.propSgnCd.maxLength}"
															readonly="#{pc_Nsc00102T03.nsc00102.propSgnCd.readonly}"
															style="#{pc_Nsc00102T03.nsc00102.propSgnCd.style}"
															tabindex="1" onblur="return func_sgnDummy(this, event);"></h:inputText><hx:commandExButton
															type="submit" styleClass="commandExButton_search"
															id="searchSgn" tabindex="2"
															onclick="return func_sgnSerch(this, event);"
															action="#{pc_Nsc00102T03.doSearchSgnAction}"
															disabled="#{pc_Nsc00102T03.nsc00102.propSearchSgn.disabled}"></hx:commandExButton></TD>
													</TR>
													<TR>
														<TH style="" class="v_b" width="150"><h:outputText
															styleClass="outputText" id="lblSgnName"
															style="#{pc_Nsc00102T03.nsc00102.propSgnName.labelStyle}"
															value="#{pc_Nsc00102T03.nsc00102.propSgnName.labelName}"></h:outputText></TH>
														<TD width="400"><h:inputText styleClass="inputText"
															id="htmlSgnName" size="56"
															value="#{pc_Nsc00102T03.nsc00102.propSgnName.stringValue}"
															style="#{pc_Nsc00102T03.nsc00102.propSgnName.style}"
															disabled="#{pc_Nsc00102T03.nsc00102.propSgnName.disabled}"
															maxlength="#{pc_Nsc00102T03.nsc00102.propSgnName.maxLength}"
															readonly="#{pc_Nsc00102T03.nsc00102.propSgnName.readonly}"
															tabindex="5"></h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
										<TD class="clear_border" rowspan="2" width="20"></TD>
										<TD align="right" colspan="2"><hx:commandExButton type="submit"
											value="選　択" styleClass="commandExButton" id="selectSgn"
											action="#{pc_Nsc00102T03.doSelectSgnAction}"
											disabled="#{pc_Nsc00102T03.nsc00102.propSelectSgn.disabled}"
											tabindex="3"></hx:commandExButton></TD>
										<TD align="right" colspan="2"><hx:commandExButton type="submit"
											value="解　除" styleClass="commandExButton" id="resetSgn"
											action="#{pc_Nsc00102T03.doResetSgnAction}"
											disabled="#{pc_Nsc00102T03.nsc00102.propResetSgn.disabled}"
											tabindex="4"></hx:commandExButton></TD>
									</TR>
									<TR>
										<TD align="right"><hx:commandExButton type="submit" value="|＜"
											styleClass="commandExButton" id="top"
											action="#{pc_Nsc00102T03.doTopAction}"
											disabled="#{pc_Nsc00102T03.nsc00102.propTop.disabled}"
											tabindex="6"></hx:commandExButton></TD>
										<TD align="right"><hx:commandExButton type="submit" value="＜"
											styleClass="commandExButton" id="pre"
											action="#{pc_Nsc00102T03.doPreAction}"
											disabled="#{pc_Nsc00102T03.nsc00102.propPre.disabled}"
											tabindex="7"></hx:commandExButton></TD>
										<TD align="right"><hx:commandExButton type="submit" value="＞"
											styleClass="commandExButton" id="next"
											action="#{pc_Nsc00102T03.doNextAction}"
											disabled="#{pc_Nsc00102T03.nsc00102.propNext.disabled}"
											tabindex="8"></hx:commandExButton></TD>
										<TD align="right"><hx:commandExButton type="submit" value="＞|"
											styleClass="commandExButton" id="last"
											action="#{pc_Nsc00102T03.doLastAction}"
											disabled="#{pc_Nsc00102T03.nsc00102.propLast.disabled}"
											tabindex="9"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
								<TBODY>
									<TR>
										<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
												style="border-bottom-style: none; margin-top: 10px; ">
												<TBODY>
													<TR>
														<TD class="tab_head_off" width="14%"><hx:commandExButton
															type="submit" value="志願者情報①" styleClass="tab_head_off"
															id="selectTab1" style="width: 100%"
															action="#{pc_Nsc00102T03.doSelectTab1Action}" tabindex="36"></hx:commandExButton></TD>
														<TD class="tab_head_off" width="14%"><hx:commandExButton
															type="submit" value="志願者情報②" styleClass="tab_head_off"
															id="selectTab2" style="width:100%"
															action="#{pc_Nsc00102T03.doSelectTab2Action}" tabindex="37"></hx:commandExButton></TD>
														<TD class="tab_head_on" width="14%" style=""><hx:commandExButton
															type="button" value="併願情報" styleClass="tab_head_on"
															id="selectTab3" style="width:100%" tabindex="10"></hx:commandExButton></TD>
														<TD class="tab_head_off" width="14%"><hx:commandExButton
															type="submit" value="出身校情報" styleClass="tab_head_off"
															id="selectTab4" style="width:100%"
															action="#{pc_Nsc00102T03.doSelectTab4Action}" tabindex="38"></hx:commandExButton></TD>
														<TD class="tab_head_off" width="14%"><hx:commandExButton
															type="submit" value="保証人情報" styleClass="tab_head_off"
															id="selectTab5" style="width:100%"
															action="#{pc_Nsc00102T03.doSelectTab5Action}" tabindex="39"></hx:commandExButton></TD>
														<TD class="tab_head_off" width="15%"><hx:commandExButton
															type="submit" value="問合せ者情報" styleClass="tab_head_off"
															id="selectTab6" style="width:100%"
															action="#{pc_Nsc00102T03.doSelectTab6Action}" tabindex="40"></hx:commandExButton></TD>
														<TD class="tab_head_off" width="15%"><hx:commandExButton
															type="submit" value="留学生情報" styleClass="tab_head_off"
															id="selectTab7" style="width:100%"
															action="#{pc_Nsc00102T03.doSelectTab7Action}" tabindex="41"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
<%
if(isKsknGhgr){
%>
									<TR>
										<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0"
												class="tab_body" width="100%" height="440">
												<TBODY>
													<TR>
														<TD align="right">
															<h:outputText
																styleClass="outputText" 
																id="lblDataKensu2"
																value="#{pc_Nsc00102T03.propJukenList.listCount}">
															</h:outputText>
															<h:outputText 
																styleClass="outputText"
																id="lblCount2" 
																value="件">
															</h:outputText>
														</TD>
													</TR>
													<TR>
														<TD>
															<div class="listScroll" style="height:85px;width:730px"
																id="listScroll"
																onscroll="setScrollPosition('scrollJuken',this);">
																<h:dataTable
																	border="0" cellpadding="3" cellspacing="0"
																	headerClass="headerClass" footerClass="footerClass"
																	rowClasses="#{pc_Nsc00102T03.propJukenList.rowClasses}"
																	styleClass="meisai_scroll" id="htmlTableListJuken2"
																	value="#{pc_Nsc00102T03.propJukenList.list}" var="varlist">
																	<h:column id="columnJuken6">
																		<f:facet name="header">
																			<h:outputText 
																				styleClass="outputText" 
																				id="lblColJuken2"
																				value="受験番号">
																			</h:outputText>
																		</f:facet>
																		<h:outputText 
																			styleClass="outputText" 
																			id="lblColJukenCd2"
																			value="#{varlist.jukenCd}">
																		</h:outputText>
																		<f:attribute value="160" name="width" />
																	</h:column>
																	<h:column id="columnJuken7">
																		<f:facet name="header">
																			<h:outputText 
																				styleClass="outputText" 
																				id="lblColKyotuJuken2"
																				value="共通受験番号">
																			</h:outputText>
																		</f:facet>
																		<h:outputText 
																			styleClass="outputText" 
																			id="lblColKyotuJukenCd2"
																			value="#{varlist.kyotuJukenCd}">
																		</h:outputText>
																		<f:attribute value="160" name="width" />
																	</h:column>
																	<h:column id="columnJuken8">
																		<f:facet name="header">
																			<h:outputText 
																				styleClass="outputText"
																				value="入試種別" 
																				id="lblColNysSbt2"
																				style="text-align: center">
																			</h:outputText>
																		</f:facet>
																		<h:outputText
																			styleClass="outputText"
																			id="lblColNysSbtCd2"
																			value="#{varlist.nysSbtName.displayValue}"
																			title="#{varlist.nysSbtName.stringValue}">
																		</h:outputText>
																		<f:attribute value="305" name="width" />
																	</h:column>
																	<h:column id="columnJuken9">
																		<f:facet name="header">
																			<h:outputText 
																				styleClass="outputText"
																				value="学科組織" 
																				id="lblColGakka2"
																				style="text-align: center">
																			</h:outputText>
																		</f:facet>
																		<h:outputText
																			styleClass="outputText" 
																			id="lblColGakkaCd2"
																			value="#{varlist.gakkaName.displayValue}"
																			title="#{varlist.gakkaName.stringValue}">
																		</h:outputText>
																		<f:attribute value="305" name="width" />
																	</h:column>
																	<h:column id="columnJuken10">
																		<f:facet name="header">
																		</f:facet>
																		<hx:commandExButton 
																			type="submit" 
																			value="編集"
																			styleClass="commandExButton" 
																			id="editJuken"
																			action="#{pc_Nsc00102T03.doSelectJukenAction}"
																			tabindex="11">
																		</hx:commandExButton>
																		<f:attribute value="42" name="width" />
																		<f:attribute value="text-align: center" name="style" />
																	</h:column>
																</h:dataTable>
															</div>
														</TD>
													</TR>
													<TR>
														<TD height="4"></TD>
													</TR>
													<TR>
														<TD>
															<TABLE width="730" border="0" cellpadding="0"
																cellspacing="0" style="" class="table">
																<TBODY>
																	<TR>
																		<TH style="" class="v_c" width="120">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInJukenCd"
																				value="#{pc_Nsc00102T03.propInJukenCd.labelName}">
																			</h:outputText>
																		</TH>
																		<TD width="140">
																			<h:inputText 
																				styleClass="inputText"
																				id="inJukenCd"
																				size="16"
																				value="#{pc_Nsc00102T03.propInJukenCd.stringValue}"
																				disabled="#{pc_Nsc00102T03.propInJukenCd.disabled}"
																				style="#{pc_Nsc00102T03.propInJukenCd.style}"
																				maxlength="#{pc_Nsc00102T03.propInJukenCd.maxLength}"
																				tabindex="12">
																			</h:inputText>
																		</TD>
																		<TH style="" class="v_c" width="120">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblKyotuJukenCd"
																				value="共通受験番号">
																			</h:outputText>
																		</TH>
																		<TD width="140">
																			<h:outputText 
																				styleClass="outputText"
																				id="kyotuJukenCd"
																				value="#{pc_Nsc00102T03.propKyotuJukenCd.stringValue}">
																			</h:outputText>
																		</TD>
																		<TH style="" class="v_d" width="120">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInNendoGakki"
																				value="#{pc_Nsc00102T03.propNendoGakki.labelName}">
																			</h:outputText>
																		</TH>
																		<TD width="140">
																			<h:outputText 
																				styleClass="outputText"
																				id="InNendoGakkid"
																				value="#{pc_Nsc00102T03.propNendoGakki.stringValue}">
																			</h:outputText>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</TD>	
													</TR>
													<TR>
														<TD height="5"></TD>
													</TR>
													<TR>
														<TD>
															<TABLE width="730" border="0" cellpadding="0"
																cellspacing="0" style="" class="table">
																<TBODY>
																	<TR>
																		<TH style="" class="v_g" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInNysSbt"
																				value="#{pc_Nsc00102T03.propInNysSbt.labelName}"
																				style="#{pc_Nsc00102T03.propInNysSbt.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="500" colspan="3">
																			<h:selectOneMenu 
																				styleClass="selectOneMenu"
																				id="inNysSbt" 
																				style="width: 80%;"
																				disabled="#{pc_Nsc00102T03.propInNysSbt.disabled}"
																				value="#{pc_Nsc00102T03.propInNysSbt.value}"
																				tabindex="13">
																				<f:selectItems
																					value="#{pc_Nsc00102T03.propInNysSbt.list}" />
																			</h:selectOneMenu>
																			<hx:commandExButton 
																				type="submit"
																				value="選択" 
																				styleClass="commandExButton"
																				id="selectNysSbt"
																				action="#{pc_Nsc00102T03.doSelectNysSbtAction}"
																				disabled="#{pc_Nsc00102T03.propSelectNysSbt.disabled}"
																				tabindex="14">
																			</hx:commandExButton>
																			<hx:commandExButton 
																				type="submit"
																				value="解除" 
																				styleClass="commandExButton" 
																				id="resetNysSbt"
																				disabled="#{pc_Nsc00102T03.propResetNysSbt.disabled}"
																				tabindex="15"
																				action="#{pc_Nsc00102T03.doResetNysSbtAction}">
																			</hx:commandExButton>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_a" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInGakka"
																				value="#{pc_Nsc00102T03.propInGakka.labelName}"
																				style="#{pc_Nsc00102T03.propInGakka.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:selectOneMenu
																				styleClass="selectOneMenu" 
																				id="inGakka"
																				style="width: 98%;"
																				disabled="#{pc_Nsc00102T03.propInGakka.disabled}"
																				value="#{pc_Nsc00102T03.propInGakka.value}"
																				tabindex="16">
																				<f:selectItems
																					value="#{pc_Nsc00102T03.propInGakka.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_c" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblSikenPlace"
																				value="#{pc_Nsc00102T03.propInSikenPlace.labelName}"
																				style="#{pc_Nsc00102T03.propInSikenPlace.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="210">
																			<h:selectOneMenu
																				styleClass="selectOneMenu" 
																				id="inSikenPlace"
																				style="width: 98%;"
																				disabled="#{pc_Nsc00102T03.propInSikenPlace.disabled}"
																				readonly="#{pc_Nsc00102T03.propInSikenPlace.readonly}"
																				value="#{pc_Nsc00102T03.propInSikenPlace.value}"
																				tabindex="17">
																				<f:selectItems
																					value="#{pc_Nsc00102T03.propInSikenPlace.list}" />
																			</h:selectOneMenu>
																		</TD>
																		<TH style="" class="v_c">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInSiboNo"
																				value="#{pc_Nsc00102T03.propInSiboNo.labelName}"
																				style="#{pc_Nsc00102T03.propInSiboNo.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="inSiboNo"
																				size="2"
																				disabled="#{pc_Nsc00102T03.propInSiboNo.disabled}"
																				readonly="#{pc_Nsc00102T03.propInSiboNo.readonly}"
																				style="#{pc_Nsc00102T03.propInSiboNo.style}"
																				value="#{pc_Nsc00102T03.propInSiboNo.integerValue}"
																				tabindex="18">
																				<f:convertNumber pattern="#0" />
																				<hx:inputHelperAssist errorClass="inputText_Error"
																					promptCharacter="_" />
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_d" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInCtrPlaceCd"
																				style="#{pc_Nsc00102T03.propInCtrPlaceCd.labelStyle}"
																				value="#{pc_Nsc00102T03.propInCtrPlaceCd.labelName}">
																			</h:outputText>
																		</TH>
																		<TD width="200">
																			<h:inputText 
																				styleClass="inputText"
																				id="inCtrPlaceCd" 
																				size="8"
																				disabled="#{pc_Nsc00102T03.propInCtrPlaceCd.disabled}"
																				maxlength="#{pc_Nsc00102T03.propInCtrPlaceCd.maxLength}"
																				style="#{pc_Nsc00102T03.propInCtrPlaceCd.style}"
																				value="#{pc_Nsc00102T03.propInCtrPlaceCd.stringValue}"
																				tabindex="19" 
																				onblur="return func_2(this, event);">
																			</h:inputText>
																		</TD>
																		<TH style="" class="v_e" nowrap width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInCtrJukenCd"
																				value="#{pc_Nsc00102T03.propInCtrJukenCd.labelName}"
																				style="#{pc_Nsc00102T03.propInCtrJukenCd.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText 
																				styleClass="inputText"
																				id="inCtrJukenCd" 
																				size="5"
																				disabled="#{pc_Nsc00102T03.propInCtrJukenCd.disabled}"
																				maxlength="#{pc_Nsc00102T03.propInCtrJukenCd.maxLength}"
																				style="#{pc_Nsc00102T03.propInCtrJukenCd.style}"
																				value="#{pc_Nsc00102T03.propInCtrJukenCd.stringValue}"
																				tabindex="20" onblur="return func_2(this, event);">
																			</h:inputText>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlCheckDigit"
																				style="#{pc_Nsc00102T03.propCheckDigit.style}"
																				value="#{pc_Nsc00102T03.propCheckDigit.stringValue}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_f" nowrap width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInCtrhakoSu"
																				value="#{pc_Nsc00102T03.propInCtrHakoSu.labelName}"
																				style="#{pc_Nsc00102T03.propInCtrHakoSu.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText 
																				styleClass="inputText"
																				id="inCtrHakoSu" 
																				size="2"
																				disabled="#{pc_Nsc00102T03.propInCtrHakoSu.disabled}"
																				value="#{pc_Nsc00102T03.propInCtrHakoSu.integerValue}"
																				tabindex="21"
																				style="#{pc_Nsc00102T03.propInCtrHakoSu.style}">
																				<f:convertNumber pattern="0" />
																				<hx:inputHelperAssist errorClass="inputText_Error"
																					promptCharacter="_" />
																			</h:inputText>
																		</TD>
																		<TH style="" class="v_g" nowrap width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInSeisekiKokaiFlg"
																				value="#{pc_Nsc00102T03.propInSeisekiKokaiFlg.labelName}"
																				style="#{pc_Nsc00102T03.propInSeisekiKokaiFlg.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:selectOneRadio
																				disabledClass="selectOneRadio_Disabled"
																				styleClass="selectOneRadio" 
																				id="inSeisekiKokai"
																				disabled="#{pc_Nsc00102T03.propInSeisekiKokaiFlg.disabled}"
																				readonly="#{pc_Nsc00102T03.propInSeisekiKokaiFlg.readonly}"
																				style="#{pc_Nsc00102T03.propInSeisekiKokaiFlg.style}"
																				value="#{pc_Nsc00102T03.propInSeisekiKokaiFlg.stringValue}"
																				tabindex="22">
																				<f:selectItem itemValue="1" itemLabel="する" />
																				<f:selectItem itemValue="0" itemLabel="しない" />
																			</h:selectOneRadio>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_a" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInGakunen"
																				value="#{pc_Nsc00102T03.propInGakunen.labelName}"
																				style="#{pc_Nsc00102T03.propInGakunen.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:selectOneMenu 
																				styleClass="selectOneMenu"
																				id="inGakunen"
																				disabled="#{pc_Nsc00102T03.propInGakunen.disabled}"
																				readonly="#{pc_Nsc00102T03.propInGakunen.readonly}"
																				value="#{pc_Nsc00102T03.propInGakunen.value}"
																				tabindex="23" style="width: 150px">
																				<f:selectItems
																					value="#{pc_Nsc00102T03.propInGakunen.list}" />
																			</h:selectOneMenu>
																		</TD>
																		<TH style="" class="v_b" nowrap width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInSemester"
																				value="#{pc_Nsc00102T03.propInSemester.labelName}"
																				style="#{pc_Nsc00102T03.propInSemester.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:selectOneMenu 
																				styleClass="selectOneMenu"
																				id="inSemester"
																				disabled="#{pc_Nsc00102T03.propInSemester.disabled}"
																				readonly="#{pc_Nsc00102T03.propInSemester.readonly}"
																				value="#{pc_Nsc00102T03.propInSemester.value}"
																				tabindex="24" style="width: 150px">
																				<f:selectItems
																					value="#{pc_Nsc00102T03.propInSemester.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_c" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInNyugakNendoCur"
																				style="#{pc_Nsc00102T03.propInNyugakNendoCur.labelStyle}"
																				value="#{pc_Nsc00102T03.propInNyugakNendoCur.labelName}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText 
																				styleClass="inputText"
																				id="inNyugakNendoCur" 
																				size="5"
																				disabled="#{pc_Nsc00102T03.propInNyugakNendoCur.disabled}"
																				readonly="#{pc_Nsc00102T03.propInNyugakNendoCur.readonly}"
																				style="#{pc_Nsc00102T03.propInNyugakNendoCur.style}"
																				value="#{pc_Nsc00102T03.propInNyugakNendoCur.dateValue}"
																				tabindex="25">
																				<f:convertDateTime pattern="yyyy" />
																				<hx:inputHelperAssist errorClass="inputText_Error"
																					promptCharacter="_" />
																			</h:inputText>
																		</TD>
																		<TH style="" class="v_d" nowrap width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInNyugakGakkiNoCur"
																				style="#{pc_Nsc00102T03.propInNyugakGakkiNoCur.labelStyle}"
																				value="#{pc_Nsc00102T03.propInNyugakGakkiNoCur.labelName}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText 
																				styleClass="inputText"
																				id="inNyugakGakkiNoCur" 
																				size="3"
																				disabled="#{pc_Nsc00102T03.propInNyugakGakkiNoCur.disabled}"
																				readonly="#{pc_Nsc00102T03.propInNyugakGakkiNoCur.readonly}"
																				style="#{pc_Nsc00102T03.propInNyugakGakkiNoCur.style}"
																				value="#{pc_Nsc00102T03.propInNyugakGakkiNoCur.integerValue}"
																				tabindex="26">
																				<f:convertNumber pattern="#0" />
																				<hx:inputHelperAssist errorClass="inputText_Error"
																					promptCharacter="_" />
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_e" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInNyugakSbt"
																				value="#{pc_Nsc00102T03.propInNyugakSbt.labelName}"
																				style="#{pc_Nsc00102T03.propInNyugakSbt.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:selectOneMenu 
																				styleClass="selectOneMenu"
																				id="inNyugakSbt"
																				disabled="#{pc_Nsc00102T03.propInNyugakSbt.disabled}"
																				readonly="#{pc_Nsc00102T03.propInNyugakSbt.readonly}"
																				value="#{pc_Nsc00102T03.propInNyugakSbt.value}"
																				tabindex="27" style="width: 98%">
																				<f:selectItems
																					value="#{pc_Nsc00102T03.propInNyugakSbt.list}" />
																			</h:selectOneMenu>
																		</TD>
																		<TH style="" class="v_f" nowrap width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInSyugakSbt"
																				value="#{pc_Nsc00102T03.propInSyugakSbt.labelName}"
																				style="#{pc_Nsc00102T03.propInSyugakSbt.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:selectOneMenu 
																				styleClass="selectOneMenu"
																				id="inSyugakSbt"
																				disabled="#{pc_Nsc00102T03.propInSyugakSbt.disabled}"
																				readonly="#{pc_Nsc00102T03.propInSyugakSbt.readonly}"
																				value="#{pc_Nsc00102T03.propInSyugakSbt.value}"
																				tabindex="28" style="width: 150px">
																				<f:selectItems
																					value="#{pc_Nsc00102T03.propInSyugakSbt.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_g" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInSgnDate"
																				value="#{pc_Nsc00102T03.propInSgnDate.labelName}"
																				style="#{pc_Nsc00102T03.propInSgnDate.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:inputText 
																				styleClass="inputText"
																				id="inSgnDate" size="10"
																				disabled="#{pc_Nsc00102T03.propInSgnDate.disabled}"
																				style="#{pc_Nsc00102T03.propInSgnDate.style}"
																				value="#{pc_Nsc00102T03.propInSgnDate.dateValue}"
																				tabindex="29">
																				<f:convertDateTime pattern="yyyy/MM/dd" />
																				<hx:inputHelperAssist errorClass="inputText_Error"
																					promptCharacter="_" />
																				<hx:inputHelperDatePicker />
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH style="" class="v_g" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInGohi" 
																				value="合否情報">
																			</h:outputText>
																		</TH>
																		<TD width="150">
																			<h:outputText 
																				styleClass="outputText"
																				id="inGohiSbtName"
																				value="#{pc_Nsc00102T03.propGohiSbtName.stringValue}">
																			</h:outputText>
																		</TD>
																		<TH style="" class="v_e" width="170">
																			<h:outputText
																				styleClass="outputText" 
																				id="lblInKekakRiyu" 
																				value="欠格理由">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="inKekakuRiyu"
																				value="#{pc_Nsc00102T03.propKekakuRiyu.stringValue}">
																			</h:outputText>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<TABLE width="730" border="0" cellpadding="3" cellspacing="0"
																 style="margin-top:5px;margin-bottom:5px;">
																<TBODY>
																	<TR>
																		<TD colspan="4" align="right">
																			<hx:commandExButton 
																				type="submit" value="教　　科"
																				styleClass="commandExButton" 
																				id="editKyoka"
																				disabled="#{pc_Nsc00102T03.propEditKyoka.disabled}"
																				action="#{pc_Nsc00102T03.doEditKyokaAction}"
																				tabindex="30">
																			</hx:commandExButton>
																			<hx:commandExButton
																				type="submit" 
																				value="希望学科" 
																				styleClass="commandExButton"
																				id="editGakka" 
																				disabled="#{pc_Nsc00102T03.propEditGakka.disabled}"
																				action="#{pc_Nsc00102T03.doEditGakkaAction}"
																				tabindex="31">
																			</hx:commandExButton>
																			<hx:commandExButton
																				type="submit" 
																				value="自由設定" 
																				styleClass="commandExButton"
																				id="setFree" 
																				disabled="#{pc_Nsc00102T03.propSetFree.disabled}"
																				action="#{pc_Nsc00102T03.doSetFreeAction}"
																				tabindex="32">
																			</hx:commandExButton>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>			
															<TABLE width="100%" border="0" cellpadding="3" cellspacing="0"
																	 style="margin-top:5px;margin-bottom:10px;">
																<TBODY>
																	<TR>
																		<TD>
																			<hx:commandExButton 
																				type="submit" value="確定"
																				styleClass="commandExButton_dat" 
																				id="execJkn"
																				disabled="#{pc_Nsc00102T03.propExecJkn.disabled}"
																				action="#{pc_Nsc00102T03.doExecJknAction}"
																				confirm="#{msg.SY_MSG_0003W}"
																				tabindex="33">
																			</hx:commandExButton>
																			<hx:commandExButton
																				type="submit" 
																				value="削除" 
																				styleClass="commandExButton_dat"
																				id="deleteJkn" 
																				disabled="#{pc_Nsc00102T03.propDeleteJkn.disabled}"
																				action="#{pc_Nsc00102T03.doDeleteJknAction}"
																				confirm="#{msg.SY_MSG_0004W}"
																				tabindex="34">
																			</hx:commandExButton>
																			<hx:commandExButton
																				type="submit" 
																				value="クリア" 
																				styleClass="commandExButton_dat"
																				id="clearJkn" 
																				disabled="#{pc_Nsc00102T03.propClearJkn.disabled}"
																				action="#{pc_Nsc00102T03.doClearJknAction}"
																				tabindex="35">
																			</hx:commandExButton>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
<%
}else{
%>
									<TR>
										<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0"
											class="tab_body" width="100%" height="300">
											<TBODY>
												<TR>
													<TD>
													<TABLE border="0" cellpadding="0" cellspacing="0"
														width="90%" align="center">
														<TBODY>
															<TR>
																<TD height="4"></TD>
															</TR>
															<TR>
																<TD align="right" height=""><h:outputText
																	styleClass="outputText" id="lblDataKensu"
																	value="#{pc_Nsc00102T03.propJukenList.listCount}">

																</h:outputText><h:outputText styleClass="outputText"
																	id="lblCount" value="件"></h:outputText></TD>
															</TR>
															<TR>
																<TD height="1"></TD>
															</TR>
														</TBODY>
													</TABLE>
													</TD>
												</TR>
												<TR>
													<TD>
													<div class="listScroll" style="height:99px;width:730px"
														id="listScroll"
														onscroll="setScrollPosition('scrollJuken',this);"><h:dataTable
														border="0" cellpadding="2" cellspacing="0"
														headerClass="headerClass" footerClass="footerClass"
														rowClasses="#{pc_Nsc00102T03.propJukenList.rowClasses}"
														styleClass="meisai_scroll" id="htmlTableListJuken"
														value="#{pc_Nsc00102T03.propJukenList.list}" var="varlist">
														<h:column id="columnJuken1">
															<f:facet name="header">
																<h:outputText styleClass="outputText" id="lblColJuken"
																	value="受験番号"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText" id="lblColJukenCd"
																value="#{varlist.jukenCd}"></h:outputText>
															<f:attribute value="155" name="width" />
														</h:column>
														<h:column id="columnJuken2">
															<f:facet name="header">
																<hx:jspPanel id="jspPanelNysSbtH">
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		class="" width="100%" height="100%" style="">
																		<TBODY>
																			<TR>
																				<TH colspan="2"
																					style="border-left-style: none; border-top-style: none;border-bottom-style:solid;"
																					width="260"><h:outputText styleClass="outputText"
																					value="入試種別" id="lblColNysSbt"
																					style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																			<TR>
																				<TH
																					style="border-left-style: none; border-bottom-style: none; border-top-style: none;border-right-style: none;"
																					width="85"><h:outputText styleClass="outputText"
																					value="コード" id="lblColNysSbtCdL"
																					style="text-align: center">
																				</h:outputText></TH>
																				<TH
																					style="border-left-style: solid; border-bottom-style: none; border-top-style: none;border-right-style: none;"
																					width="175"><h:outputText styleClass="outputText"
																					value="名称" id="lblColNysSbtNameL"
																					style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																		</TBODY>
																	</TABLE>
																</hx:jspPanel>
															</f:facet>
															<hx:jspPanel id="jspPanelNysSbt">
																<TABLE border="0" class="" cellpadding="0"
																	cellspacing="0" width="100%" height="100%">
																	<TBODY>
																		<TR>
																			<TD
																				style="border-left-style: none; border-top-style: none; border-bottom-style: none;"
																				width="85" height="100%"><h:outputText
																				styleClass="outputText" id="lblColNysSbtCd"
																				value="#{varlist.nysSbtCd}"></h:outputText></TD>
																			<TD
																				style="border-right-style: none; border-left-style: none; border-top-style: none; border-bottom-style: none;"
																				width="175" height="100%"><h:outputText
																				styleClass="outputText" id="lblColNysSbtName"
																				value="#{varlist.nysSbtName.displayValue}"
																				title="#{varlist.nysSbtName.value}"></h:outputText></TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</hx:jspPanel>
															<f:attribute value="305" name="width" />
														</h:column>
														<h:column id="columnJuken3">
															<f:facet name="header">
																<hx:jspPanel id="jspPanelGakkaH">
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		class="" width="100%" height="100%" style="">
																		<TBODY>
																			<TR>
																				<TH colspan="2"
																					style="border-left-style: none; border-top-style: none;border-bottom-style:solid;"
																					width="260"><h:outputText styleClass="outputText"
																					value="学科組織" id="lblColGakka"
																					style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																			<TR>
																				<TH
																					style="border-left-style: none; border-bottom-style: none; border-top-style: none;border-right-style: none;"
																					width="85"><h:outputText styleClass="outputText"
																					value="コード" id="lblColGakkaCdL"
																					style="text-align: center">
																				</h:outputText></TH>
																				<TH
																					style="border-left-style: solid; border-bottom-style: none; border-top-style: none;border-right-style: none;"
																					width="175"><h:outputText styleClass="outputText"
																					value="名称" id="lblColGakkaNameL"
																					style="text-align: center">
																				</h:outputText></TH>
																			</TR>
																		</TBODY>
																	</TABLE>
																</hx:jspPanel>
															</f:facet>
															<hx:jspPanel id="jspPanelGakka">
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	width="100%" height="100%">
																	<TBODY>
																		<TR>
																			<TD
																				style="border-left-style: none; border-top-style: none; border-bottom-style: none;"
																				width="85" height="100%"><h:outputText
																				styleClass="outputText" id="lblColGakkaCd"
																				value="#{varlist.gakkaCd}"></h:outputText></TD>
																			<TD
																				style="border-right-style: none; border-left-style: none; border-top-style: none; border-bottom-style: none;"
																				width="175" height="100%"><h:outputText
																				styleClass="outputText" id="lblColGakkaName"
																				value="#{varlist.gakkaName.displayValue}"
																				title="#{varlist.gakkaName.value}"></h:outputText></TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</hx:jspPanel>
															<f:attribute value="305" name="width" />
														</h:column>
														<h:column id="columnJuken4">
															<f:facet name="header">
															</f:facet>
															<hx:commandExButton type="submit" value="選択"
																styleClass="commandExButton" id="selectJuken"
																action="#{pc_Nsc00102T03.doSelectJukenAction}"
																tabindex="11"></hx:commandExButton>
															<f:attribute value="42" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
													</h:dataTable></div>

													</TD>
												</TR>
												<TR>
													<TD></TD>
												</TR>
												<TR>
													<TD>
													<TABLE width="730" border="0" cellpadding="0"
														cellspacing="0" style="" class="table">
														<TBODY>
															<TR>
																<TH style="" class="v_c" width="150"><h:outputText
																	styleClass="outputText" id="lblJukenCdL"
																	value="#{pc_Nsc00102T03.propJukenCd.labelName}"></h:outputText></TH>
																<TD width="200"><h:outputText styleClass="outputText"
																	id="lblJukenCdd"
																	value="#{pc_Nsc00102T03.propJukenCd.stringValue}"></h:outputText></TD>
																<TH style="" class="v_d" width="150"><h:outputText
																	styleClass="outputText" id="lblNendoGakkiL"
																	value="#{pc_Nsc00102T03.propNendoGakki.labelName}"></h:outputText></TH>
																<TD width="200"><h:outputText styleClass="outputText"
																	id="lblNendoGakkid"
																	value="#{pc_Nsc00102T03.propNendoGakki.stringValue}"></h:outputText></TD>
															</TR>
															<TR>
																<TH style="" class="v_e" width="150"><h:outputText
																	styleClass="outputText" id="lblNysSbtL" value="入試種別"></h:outputText></TH>
																<TD colspan="3"><h:outputText styleClass="outputText"
																	id="lblNysSbtName"
																	value="#{pc_Nsc00102T03.propNysSbtName.stringValue}"></h:outputText></TD>
															</TR>
															<TR>
																<TH style="" class="v_f" width="150"><h:outputText
																	styleClass="outputText" id="lblGakkaL" value="第一希望学科組織"></h:outputText></TH>
																<TD colspan="3"><h:outputText styleClass="outputText"
																	id="lblGakkaName"
																	value="#{pc_Nsc00102T03.propGakkaName.stringValue}"></h:outputText></TD>
															</TR>
															<TR>
																<TH style="" class="v_g" width="150"><h:outputText
																	styleClass="outputText" id="lblGohiL" value="合否情報"></h:outputText></TH>
																<TD width="225"><h:outputText styleClass="outputText"
																	id="lblGohiSbtName"
																	value="#{pc_Nsc00102T03.propGohiSbtName.stringValue}"></h:outputText></TD>
																<TH style="" class="v_a" width="150"><h:outputText
																	styleClass="outputText" id="lblSikenPlaceL" value="試験場"></h:outputText></TH>
																<TD width="200"><h:outputText styleClass="outputText"
																	id="lblSikenPlaceName"
																	value="#{pc_Nsc00102T03.propSikenPlaceName.stringValue}"></h:outputText></TD>
															</TR>
															<TR>
																<TH style="" class="v_b" width="150"><h:outputText
																	styleClass="outputText" id="lblCtrPlaceL"
																	value="#{pc_Nsc00102T03.propCtrPlace.labelName}"></h:outputText></TH>
																<TD><h:outputText styleClass="outputText"
																	id="lblCtrPlace"
																	value="#{pc_Nsc00102T03.propCtrPlace.stringValue}"></h:outputText></TD>
																<TH style="" class="v_c" width="150"><h:outputText
																	styleClass="outputText" id="lblCtrJukenCdL"
																	value="#{pc_Nsc00102T03.propCtrJukenCd.labelName}"></h:outputText></TH>
																<TD><h:outputText styleClass="outputText"
																	id="lblCtrJukenCd"
																	value="#{pc_Nsc00102T03.propCtrJukenCd.stringValue}"></h:outputText></TD>
															</TR>
															<TR>
																<TH style="" class="v_d" width="150"><h:outputText
																	styleClass="outputText" id="lblCtrHakoSuL"
																	value="#{pc_Nsc00102T03.propCtrHakoSu.labelName}"></h:outputText></TH>
																<TD><h:outputText styleClass="outputText"
																	id="lblCtrHakoSu"
																	value="#{pc_Nsc00102T03.propCtrHakoSu.stringValue}"></h:outputText></TD>
																<TH style="" class="v_e" width="160"><h:outputText
																	styleClass="outputText" id="lblKekakRiyuL" value="欠格理由"></h:outputText></TH>
																<TD><h:outputText styleClass="outputText"
																	id="lblKekakuRiyu"
																	value="#{pc_Nsc00102T03.propKekakuRiyu.stringValue}"></h:outputText></TD>
															</TR>
														</TBODY>
													</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
<%
}
%>
								</TBODY>
							</TABLE>
						</TD>
					</TR	
					<TR>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="80%"
								class="button_bar">
								<TBODY>
									<TR>
										<TD><hx:commandExButton type="submit" value="確定"
											styleClass="commandExButton_dat" id="exec"
											action="#{pc_Nsc00102T03.doExecAction}"
											confirm="#{msg.SY_MSG_0003W}"
											disabled="#{pc_Nsc00102T03.nsc00102.propExec.disabled}"
											tabindex="42"></hx:commandExButton><hx:commandExButton
											type="submit" value="削除" styleClass="commandExButton_dat"
											id="delete" action="#{pc_Nsc00102T03.doDeleteAction}"
											confirm="#{msg.SY_MSG_0004W}"
											disabled="#{pc_Nsc00102T03.nsc00102.propDelete.disabled}"
											tabindex="43"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Nsc00102T03.propJukenList.scrollPosition}"
				id="scrollJuken"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsc00102T03.nsc00102.propExecutable.integerValue}"
				id="htmlExecutable">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsc00102T03.propUpdateKyotuFuban.integerValue}"
				id="htmlUpdateKyotuFuban">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsc00102T03.nsc00102.propExecutableFlg.integerValue}"
				id="htmlExecutableFlg">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

