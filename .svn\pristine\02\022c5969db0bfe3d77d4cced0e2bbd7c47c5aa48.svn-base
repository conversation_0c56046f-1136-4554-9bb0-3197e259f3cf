<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssc00102T09.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@page import="com.jast.gakuen.framework.util.UtilStr"%>
<%@page import="com.jast.gakuen.framework.util.UtilSystem"%>
<%@page import="com.jast.gakuen.rev.ss.Ssc00102T01"%>
<%@page import="com.jast.gakuen.rev.ss.Ssc00102"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssc00102.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"> 
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css">

<SCRIPT type="text/javascript">

function confirmOk() {
	if (document.getElementById('form1:htmlExecutableSearch2').value == "1") {
		indirectClick('select');
	} else {
		document.getElementById('form1:htmlExecutableSearch').value = "1";
		indirectClick('registerNew');
	}
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
	document.getElementById('form1:htmlExecutableSearch2').value = "0";
}			

function func_1(thisObj, thisEvent, field, nameFlg) {
	// 企業検索画面 (htmlHonKgyCd用)
	var url="${pageContext.request.contextPath}/faces/rev/ss/pSsc0101.jsp?retFieldName=" + field + "&getNameFlg="+nameFlg;
	//openModalWindow(url, "pSsc0101", "<%=com.jast.gakuen.rev.ss.PSsc0101.getWindowOpenOption() %>");
	var pageCodeClass = "com.jast.gakuen.rev.ss.PSsc0101";
	removeSessionAndOpenWindow(pageCodeClass 
	,url, "pSsc0101", "<%=com.jast.gakuen.rev.ss.PSsc0101.getWindowOpenOption() %>","Ssc001");
	return false;
}
function func_4(thisObj, thisEvent) {
	// 企業名称を取得する
	var servlet = "rev/ss/SscKgyAJAX";
	var target = "form1:htmlKgyNameSub";
	var code = thisObj.value;
	getCodeName(servlet, target, code );
	
}
window.attachEvent('onload', onLoadKgyoName);
function onLoadKgyoName(event){
	func_4(document.getElementById('form1:htmlKgyCd'), event);
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssc00102T09.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssc00102T01.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssc00102T09.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssc00102T09.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit"
	styleClass="commandExButton" id="kyujin" 
	action="#{pc_Ssc00102T01.doGoKjnListAction}"
	rendered="#{pc_Ssc00102T01.ssc00102.propKyujin.rendered}"
	disabled="#{pc_Ssc00102T01.ssc00102.propKyujin.disabled}"
	value="#{pc_Ssc00102T01.ssc00102.propKyujin.name}">
</hx:commandExButton>
<hx:commandExButton
	type="submit" value="自由設定" styleClass="commandExButton"
	id="goKgyFre" action="#{pc_Ssc00102T01.doGoKgyFreAction}"
	rendered="#{pc_Ssc00102T01.ssc00102.propKgyFre.rendered}"
	disabled="#{pc_Ssc00102T01.ssc00102.propKgyFre.disabled}">
</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
	<!-- ↓ここにコンポーネントを配置 -->
		<TABLE width="924" border="0" cellpadding="0" cellspacing="0">
			<TR>
				<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="927">
							<TBODY>
								<TR>
									<TD style="" rowspan="2" align="left">

									<TABLE width="813" cellpadding="0" cellspacing="0"
										class="table">
										<TBODY>
											<TR>
												<TH style="" class="v_a" width="150">
													<h:outputText styleClass="outputText" id="lblKgyCd"
														value="#{pc_Ssc00102T01.ssc00102.propKgyCdSub.labelName}"
														style="#{pc_Ssc00102T01.ssc00102.propKgyCdSub.labelStyle}">
													</h:outputText>
												</TH>
												<TD colspan="3" >
										                	<h:inputText
														styleClass="inputText" id="htmlKgyCd" size="12"
														value="#{pc_Ssc00102T01.ssc00102.propKgyCdSub.stringValue}"
														style="#{pc_Ssc00102T01.ssc00102.propKgyCdSub.style}"
														disabled="#{pc_Ssc00102T01.ssc00102.propKgyCdSub.disabled}"
														readonly="#{pc_Ssc00102T01.ssc00102.propKgyCdSub.readonly}"
														maxlength="#{pc_Ssc00102T01.ssc00102.propKgyCdSub.maxLength}"
														onblur="return func_4(this, event);">
													</h:inputText>
													<hx:commandExButton type="submit"
														styleClass="commandExButton_search" id="searchKgy"
														style="margin-left:5px;"
														disabled="#{pc_Ssc00102T01.ssc00102.propSelect.disabled}"
														onclick="return func_1(this, event, 'form1:htmlKgyCd', '1');"
														onblur="return false;"
														rendered="#{pc_Ssc00102T01.ssc00102.propSearchKgy.rendered}">
													</hx:commandExButton>
													<h:outputText styleClass="outputText"
														id="htmlKgyNameSub"
														value="#{pc_Ssc00102T01.ssc00102.propKgyNameSub.displayValue}"
														style="#{pc_Ssc00102T01.ssc00102.propKgyNameSub.style}"
														title="#{pc_Ssc00102T01.ssc00102.propKgyNameSub.value}">
													</h:outputText>
												</TD>
											</TR>
											<TR>
												<TH style="" class="v_b" width="150">
													<h:outputText styleClass="outputText"
														id="lblGyosyuNameSub"
														value="#{pc_Ssc00102T01.ssc00102.propGyosyuNameSub.labelName}"
														style="#{pc_Ssc00102T01.ssc00102.propGyosyuNameSub.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="443">
													<h:outputText styleClass="outputText" id="htmlGyosyuNameSub"
														value="#{pc_Ssc00102T01.ssc00102.propGyosyuNameSub.displayValue}"
														style="#{pc_Ssc00102T01.ssc00102.propGyosyuNameSub.style}"
														title="#{pc_Ssc00102T01.ssc00102.propGyosyuNameSub.value}">
													</h:outputText>
												</TD>
										                <TH style="" class="v_b" width="150">
													<h:outputText styleClass="outputText"
														id="lblKariKigyoSub"
														value="#{pc_Ssc00102T01.ssc00102.propKariKigyoSub.labelName}"
														style="#{pc_Ssc00102T01.ssc00102.propKariKigyoSub.labelStyle}">
													</h:outputText>
										                </TH>
										                <TD  width="50">
													<h:selectBooleanCheckbox
														styleClass="selectBooleanCheckbox" id="htmlKariKigyoSub"
														onclick ="indirectClick('kariFlgChange')"
														readonly="#{pc_Ssc00102T01.ssc00102.propKariKigyoSub.readonly}"
														style="#{pc_Ssc00102T01.ssc00102.propKariKigyoSub.style}"
														value="#{pc_Ssc00102T01.ssc00102.propKariKigyoSub.checked}">
													</h:selectBooleanCheckbox>
												</TD>
											</TR>
<hx:jspPanel rendered="#{pc_Ssc00102T01.ssc00102.propWebSearchPanelDisp.rendered == true}">
											<TR>
										                <TH style="" class="v_b" width="150">
													<h:outputText styleClass="outputText"
														id="lblWebSearch"
														value="#{pc_Ssc00102T01.ssc00102.propWebSearch.labelName}"
														style="#{pc_Ssc00102T01.ssc00102.propWebSearch.labelStyle}">
													</h:outputText>
										                </TH>
										                <TD colspan=3 >
													<h:selectBooleanCheckbox
														styleClass="selectBooleanCheckbox" id="htmlWebSearch"
														readonly="#{pc_Ssc00102T01.ssc00102.propWebSearch.readonly}"
														style="#{pc_Ssc00102T01.ssc00102.propWebSearch.style}"
														value="#{pc_Ssc00102T01.ssc00102.propWebSearch.checked}">
													</h:selectBooleanCheckbox>
												</TD>
											</TR>
</hx:jspPanel>
										</TBODY>
									</TABLE>
									</TD>
									<TD class="clear_border" rowspan="2" width="20"></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										styleClass="commandExButton" id="select"
										action="#{pc_Ssc00102T01.doSelectAction}"
										rendered="#{pc_Ssc00102T01.ssc00102.propSelect.rendered}"
										disabled="#{pc_Ssc00102T01.ssc00102.propSelect.disabled}"
										value="選　択"></hx:commandExButton></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										styleClass="commandExButton" id="Unselect"
										action="#{pc_Ssc00102T01.doUnselectAction}"
										rendered="#{pc_Ssc00102T01.ssc00102.propUnselect.rendered}"
										disabled="#{pc_Ssc00102T01.ssc00102.propUnselect.disabled}"
										value="解　除"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD align="right"><hx:commandExButton type="submit"
										value="|＜"
										styleClass="commandExButton" id="selectTop"
										action="#{pc_Ssc00102T01.doSelectTopAction}"
										rendered="#{pc_Ssc00102T01.ssc00102.propToTop.rendered}"
										disabled="#{pc_Ssc00102T01.ssc00102.propToTop.disabled}"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton
										type="submit" value="＜"
										styleClass="commandExButton" id="selectAhead"
										action="#{pc_Ssc00102T01.doSelectAheadAction}"
										rendered="#{pc_Ssc00102T01.ssc00102.propToAhead.rendered}"
										disabled="#{pc_Ssc00102T01.ssc00102.propToAhead.disabled}"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton
										type="submit" value="＞"
										styleClass="commandExButton" id="selectNext"
										action="#{pc_Ssc00102T01.doSelectNextAction}"
										disabled="#{pc_Ssc00102T01.ssc00102.propToNext.disabled}"
										rendered="#{pc_Ssc00102T01.ssc00102.propToNext.rendered}"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton
										type="submit" value="＞|"
										styleClass="commandExButton" id="selectEnd"
										action="#{pc_Ssc00102T01.doSelectEndAction}"
										disabled="#{pc_Ssc00102T01.ssc00102.propToEnd.disabled}"
										rendered="#{pc_Ssc00102T01.ssc00102.propToEnd.rendered}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
				</TD>
			</TR>
		</TABLE>
			<TABLE width="924" border="0" cellpadding="0" cellspacing="0"
				style="margin-top:20px;">
				<TBODY>
					<TR>
						<TD style="text-align:left;">
						<hx:commandExButton type="submit" 
							value="基本" styleClass="tab_head_off" id="btnSsc00102T01" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T01Action}" style="width:8%"></hx:commandExButton><hx:commandExButton type="submit"
							value="所在地" styleClass="tab_head_off" id="btnSsc00102T02" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T02Action}" style="width:8%"></hx:commandExButton><hx:commandExButton type="submit"
							value="代表者" styleClass="tab_head_off" id="btnSsc00102T03" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T03Action}" style="width:8%"></hx:commandExButton><hx:commandExButton type="submit"
							value="採用担当" styleClass="tab_head_off" id="btnSsc00102T04" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T04Action}" style="width:8%"></hx:commandExButton><hx:commandExButton type="submit"
							value="説明会" styleClass="tab_head_off" id="btnSsc00102T05" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T05Action}" style="width:8%"></hx:commandExButton><hx:commandExButton type="submit"
							value="訪問記録" styleClass="tab_head_off" id="btnSsc00102T06" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T06Action}" style="width:9%"></hx:commandExButton><hx:commandExButton type="submit"
							value="売上構成" styleClass="tab_head_off" id="btnSsc00102T07" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T07Action}" style="width:9%"></hx:commandExButton><hx:commandExButton type="submit"
							value="求人票礼状" styleClass="tab_head_off" id="btnSsc00102T08" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T08Action}" style="width:9%"></hx:commandExButton><hx:commandExButton type="submit"
							value="廃業" styleClass="tab_head_on" id="btnSsc00102T09" 	
							action="#{pc_Ssc00102T09.doBtnSsc00102T09Action}" style="width:8%"></hx:commandExButton><hx:commandExButton type="submit"
							value="求人" styleClass="tab_head_off" id="btnSsc00102T10" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T10Action}" style="width:8%"></hx:commandExButton><hx:commandExButton type="submit" 
							value="ｲﾝﾀｰﾝｼｯﾌﾟ" styleClass="tab_head_off" id="btnSsc00102T12" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T12Action}" disabled="#{pc_Ssc00102T01.ssc00102.propT12Tab.disabled}" style="width:9%"></hx:commandExButton><hx:commandExButton type="submit"
							value="メモ" styleClass="tab_head_off" id="btnSsc00102T11" 
							action="#{pc_Ssc00102T09.doBtnSsc00102T11Action}" style="width:8%"></hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TD>
								<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="tab_body">
									<TBODY>
										<TR>
							<TD align="center">
								<DIV style="height:415px">
											<TABLE>
											<TR>
											<TD>
											<TABLE border="0" cellpadding="3" cellspacing="0"
												class="table" style="margin-top:20px;">
												<TBODY>
													<TR>
														<TH class="v_a" width="150"><h:outputText
															styleClass="outputText" id="lblHaigyo"
															value="#{pc_Ssc00102T09.propHaigyo.labelName}"
															style="#{pc_Ssc00102T09.propHaigyo.labelStyle}"></h:outputText></TH>
														<TD width="500"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlHaigyo"
															value="#{pc_Ssc00102T09.propHaigyo.checked}"
															style="#{pc_Ssc00102T09.propHaigyo.style}"
															readonly="#{pc_Ssc00102T09.propHaigyo.readonly}"
															disabled="#{pc_Ssc00102T09.propHaigyo.disabled}"></h:selectBooleanCheckbox></TD>
													</TR>
													<TR>
														<TH class="v_b"><h:outputText
															styleClass="outputText" id="lblHaigyoDate"
															value="#{pc_Ssc00102T09.propHaigyoDate.labelName}"
															style="#{pc_Ssc00102T09.propHaigyoDate.labelStyle}"></h:outputText></TH>
														<TD><h:inputText styleClass="inputText"
															id="htmlHaigyoDate"
															value="#{pc_Ssc00102T09.propHaigyoDate.dateValue}"
															style="#{pc_Ssc00102T09.propHaigyoDate.style}"
															readonly="#{pc_Ssc00102T09.propHaigyoDate.readonly}"
															disabled="#{pc_Ssc00102T09.propHaigyoDate.disabled}"
															size="10">
															<f:convertDateTime />
															<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
															<hx:inputHelperDatePicker />
														</h:inputText></TD>
													</TR>
													<TR>
														<TH class="v_c"><h:outputText
															styleClass="outputText" id="lblHaigyoNaiyo"
															value="#{pc_Ssc00102T09.propHaigyoNaiyo.labelName}"
															style="#{pc_Ssc00102T09.propHaigyoNaiyo.labelStyle}"></h:outputText></TH>
														<TD><h:inputTextarea styleClass="inputTextarea"
															id="htmlHaigyoNaiyo"
															value="#{pc_Ssc00102T09.propHaigyoNaiyo.stringValue}"
															cols="59" rows="4"
															style="#{pc_Ssc00102T09.propHaigyoNaiyo.style}"
															readonly="#{pc_Ssc00102T09.propHaigyoNaiyo.readonly}"
															disabled="#{pc_Ssc00102T09.propHaigyoNaiyo.disabled}">
														</h:inputTextarea></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD align="center">
											<TABLE border="0" cellpadding="3" cellspacing="0"
												class="table" style="margin:20px 0;">
												<TBODY>
													<TR>
														<TH class="v_d" width="150"><h:outputText
															styleClass="outputText" id="lblGpeiKgyCd"
															value="#{pc_Ssc00102T09.propGpeiKgyCd.labelName}"
															style="#{pc_Ssc00102T09.propGpeiKgyCd.labelStyle}"></h:outputText></TH>
														<TD width="500"><h:outputText styleClass="outputText"
															id="htmlGpeiKgyCd"
															value="#{pc_Ssc00102T09.propGpeiKgyCd.stringValue}"
															style="#{pc_Ssc00102T09.propGpeiKgyCd.style}"></h:outputText></TD>
													</TR>
													<TR>
														<TH class="v_e"><h:outputText
															styleClass="outputText" id="lblGpeiKgyName"
															value="#{pc_Ssc00102T09.propGpeiKgyName.labelName}"
															style="#{pc_Ssc00102T09.propGpeiKgyName.labelStyle}"></h:outputText></TH>
														<TD><h:outputText styleClass="outputText"
															id="htmlGpeiKgyName"
															value="#{pc_Ssc00102T09.propGpeiKgyName.displayValue}"
															style="#{pc_Ssc00102T09.propGpeiKgyName.style}"></h:outputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
											</TR>
											</TABLE>
											</DIV>
										</TD>
									</TR>
									</TBODY>
								</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="100%" border="0" cellpadding="3" cellspacing="0" class="button_bar">
								<TBODY>
									<TR>
										<TD>
										<hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="registerNew"
										action="#{pc_Ssc00102T01.doRegisterAction}"
										rendered="#{pc_Ssc00102T01.ssc00102.propRegisterNew.rendered}"
											disabled="#{pc_Ssc00102T01.ssc00102.propRegisterNew.disabled}"
										></hx:commandExButton><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="register"
										action="#{pc_Ssc00102T01.doRegisterAction}"
										confirm="#{msg.SY_MSG_0003W}"
										rendered="#{pc_Ssc00102T01.ssc00102.propRegister.rendered}"
											disabled="#{pc_Ssc00102T01.ssc00102.propRegister.disabled}"
										></hx:commandExButton><hx:commandExButton type="submit" value="削除"
										styleClass="commandExButton_dat" id="delete"
										action="#{pc_Ssc00102T01.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"
										disabled="#{pc_Ssc00102T01.ssc00102.propDelete.disabled}"
										></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
			<h:inputHidden
				value="#{pc_Ssc00102T01.ssc00102.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Ssc00102T01.ssc00102.propExecutableSearch2.integerValue}" id="htmlExecutableSearch2">
				<f:convertNumber />
			</h:inputHidden>
			<div style="display:none;">
				<hx:commandExButton type="submit" value="登録区分" id="kariFlgChange"
					styleClass="commandExButton_dat" action="#{pc_Ssc00102T01.doChangeKariFlgAction}">
				</hx:commandExButton>
			</div>
</h:form>
</hx:scriptCollector>
	<jsp:include page="../inc/footer.jsp" />
	</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

