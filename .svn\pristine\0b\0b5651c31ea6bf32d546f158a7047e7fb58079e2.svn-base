<%-- 
	卒業生情報登録（保証人登録）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob01505.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01505.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>

<SCRIPT type="text/javascript">

function openSubWindow() {
  // 住所検索子画面
  var field1 = "form1:htmlAddrCd";
  var field2 = "form1:htmlHsyAddr1";
  var field3 = "form1:htmlHsyAddrKana1";
  var No = document.getElementById("form1:htmlAddrCd").value;
  No = encodeURIComponent(No);
  var addr = document.getElementById("form1:htmlHsyAddr1").value;
  addr = encodeURIComponent(addr);
  var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp?zipNo=" + field1
                                  + "&jyusyoKanji=" + field2
                                  + "&jyusyoKana=" + field3
                                  + "&chihoCd=" + ""
                                  + "&zipNoValue=" + No
                                  + "&jyusyoValue=" + addr;
  openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");

  return false;
}

window.onload = function (){
  setZipMenu('form1:htmlAddrCd','form1:htmlHsyAddr1','form1:htmlHsyAddrKana1');
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob01505.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Cob01505">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob01505.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob01505.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob01505.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Cob01505.doReturnDispAction}">
			</hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850">
							<TBODY>
								<TR>
									<TD align="right" nowrap class="outputText" width="850"><h:outputText
										styleClass="outputText" id="lblCount"
										value="#{pc_Cob01505.propHsyList.listCount}件"></h:outputText></TD>
								</TR>
								<TR>
									<TD>
										<DIV class="listScroll" id="listScroll" style="height: 87px"
											onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
											border="1" cellpadding="2" cellspacing="0"
											columnClasses="columnClass1" headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Cob01505.propHsyList.rowClasses}"
											styleClass="meisai_scroll" id="htmlHsyList"
											value="#{pc_Cob01505.propHsyList.list}" var="varlist">
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														id="lblHsyList_HsyNo_head"
														value="#{pc_Cob01502T09.proplblHsyList_HsyNo.name}"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="lblHsyList_HsyNo"
													value="#{varlist.propHsyNo.integerValue}"></h:outputText>
												<f:attribute value="text-align: right" name="style" />
												<f:attribute value="100" name="width" />
											</h:column>
											<h:column id="column1">
												<f:facet name="header">
													<h:outputText id="lblHsyList_HsySbtMei_head"
														styleClass="outputText"
														value="#{pc_Cob01502T09.proplblHsyList_HsySbtMei.name}"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="lblHsyList_HsySbtMei"
													value="#{varlist.propHsySbtMei.stringValue}"></h:outputText>
												<f:attribute value="190" name="width" />
											</h:column>
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														id="lblHsyList_HsyName_head"
														value="#{pc_Cob01502T09.proplblHsyList_HsyName.name}"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="lblHsyList_HsyName"
													value="#{varlist.propHsyName.stringValue}"></h:outputText>
												<f:attribute value="500" name="width" />
											</h:column>
											<h:column id="column3">
												<f:facet name="header">
												</f:facet>
												<f:attribute value="41" name="width" />
												<hx:commandExButton type="submit" value="選択"
													styleClass="commandExButton"
													action="#{pc_Cob01505.doSelectAction}"></hx:commandExButton>
											</h:column>
										</h:dataTable><BR>
										</DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE class="table" width="850">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblHsySbtList"
										value="#{pc_Cob01505.propHsySbtList.labelName}"
										style="#{pc_Cob01505.propHsySbtList.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlHsySbtList"
										value="#{pc_Cob01505.propHsySbtList.value}"
										disabled="#{pc_Cob01505.propHsySbtList.disabled}"
										style="width:160px;">
										<f:selectItems value="#{pc_Cob01505.propHsySbtList.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton"
										disabled="#{pc_Cob01505.propHsySbtList.disabled}"
										action="#{pc_Cob01505.doChangeHsySbtAction}"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b" width="180"><h:outputText
										styleClass="outputText" id="lblHsyNo"
										value="#{pc_Cob01505.propHsyNo.labelName}"
										style="#{pc_Cob01505.propHsyNo.labelStyle}"></h:outputText></TH>
									<TD width="245"><h:inputText styleClass="inputText" id="htmlHsyNo"
										size="4" disabled="#{pc_Cob01505.propHsyNo.disabled}"
										value="#{pc_Cob01505.propHsyNo.integerValue}"
										maxlength="#{pc_Cob01505.propHsyNo.max}"
										style="#{pc_Cob01505.propHsyNo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<f:convertNumber pattern="#0" />
									</h:inputText></TD>
									<TH nowrap class="v_b" width="180"><h:outputText
										styleClass="outputText" id="lblHsySbtMei"
										value="#{pc_Cob01505.propHsySbtMei.labelName}"></h:outputText></TH>
									<TD width="245"><h:inputText styleClass="inputText"
										id="htmlHsySbtMei" size="15"
										disabled="#{pc_Cob01505.propHsySbtMei.disabled}"
										value="#{pc_Cob01505.propHsySbtMei.stringValue}"
										disabled="#{pc_Cob01505.propHsySbtMei.disabled}"
										style="#{pc_Cob01505.propHsySbtMei.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c"><h:outputText
										styleClass="outputText" id="lblHsyZokList"
										value="#{pc_Cob01505.propHsyZokList.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlHsyZokList"
										value="#{pc_Cob01505.propHsyZokList.value}"
										disabled="#{pc_Cob01505.propHsyZokList.disabled}"
										style="width:215px;">
										<f:selectItems value="#{pc_Cob01505.propHsyZokList.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton"
										action="#{pc_Cob01505.doChangeHsyZokAction}"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TH nowrap class="v_d"><h:outputText
										styleClass="outputText" id="lblHsyZok"
										value="#{pc_Cob01505.propHsyZok.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlHsyZok" size="4"
										disabled="#{pc_Cob01505.propHsyZok.disabled}"
										value="#{pc_Cob01505.propHsyZok.stringValue}"
										disabled="#{pc_Cob01505.propHsyZok.disabled}"
										style="#{pc_Cob01505.propHsyZok.style}"></h:inputText></TD>
									<TH nowrap class="v_d"><h:outputText
										styleClass="outputText" id="lblHsyZokMei"
										value="#{pc_Cob01505.propHsyZokMei.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlHsyZokMei" size="19"
										disabled="#{pc_Cob01505.propHsyZokMei.disabled}"
										value="#{pc_Cob01505.propHsyZokMei.stringValue}"
										style="#{pc_Cob01505.propHsyZokMei.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_e"><h:outputText
										styleClass="outputText" id="lblHsyName"
										value="#{pc_Cob01505.propHsyName.labelName}"
										style="#{pc_Cob01505.propHsyName.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyName" size="40"
										value="#{pc_Cob01505.propHsyName.stringValue}"
										style="#{pc_Cob01505.propHsyName.style}"
										disabled="#{pc_Cob01505.propHsyName.disabled}"
										maxlength="#{pc_Cob01505.propHsyName.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_f"><h:outputText
										styleClass="outputText" id="lblHsyNameKana"
										value="#{pc_Cob01505.propHsyNameKana.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyNameKana" size="80"
										value="#{pc_Cob01505.propHsyNameKana.stringValue}"
										style="#{pc_Cob01505.propHsyNameKana.style}"
										disabled="#{pc_Cob01505.propHsyNameKana.disabled}"
										maxlength="#{pc_Cob01505.propHsyNameKana.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_d" width="180">
									<!-- 郵便番号 -->
										<h:outputText styleClass="outputText" id="lblAddrCd"
										value="#{pc_Cob01505.propAddrCd.labelName}"
										style="#{pc_Cob01505.propAddrCd.labelStyle}"></h:outputText></TH>
									<TD nowrap style="border-right-style:none;"><h:inputText styleClass="inputText"
										id="htmlAddrCd" size="7"
										maxlength="#{pc_Cob01505.propAddrCd.maxLength}"
										disabled="#{pc_Cob01505.propAddrCd.disabled}"
										value="#{pc_Cob01505.propAddrCd.stringValue}"
										style="#{pc_Cob01505.propAddrCd.style}"></h:inputText>
									</TD>
									<TD colspan="2" nowrap style="border-left-style:none;">
										<h:selectOneRadio styleClass="selectOneRadio"
										id="htmlRadio"
										disabledClass="selectOneRadio_Disabled"
										value="#{pc_Cob01505.propRadio.value}">
										<f:selectItem itemValue="0" itemLabel="学生住所" />
										<f:selectItem itemValue="1" itemLabel="帰省先住所" />
										<f:selectItem itemValue="2" itemLabel="卒業時" /></h:selectOneRadio>
										<hx:commandExButton type="submit"
										id="btnCopy" value="住所コピー"
										styleClass="commandExButton"
										action="#{pc_Cob01505.doBtnCopyAction}">
										</hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_a" rowspan="3"><h:outputText
										styleClass="outputText" id="lblAddr1"
										value="#{pc_Cob01505.propHsyAddr1.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyAddr1" size="50"
										value="#{pc_Cob01505.propHsyAddr1.stringValue}"
										style="#{pc_Cob01505.propHsyAddr1.style}"
										disabled="#{pc_Cob01505.propHsyAddr1.disabled}"
										maxlength="#{pc_Cob01505.propHsyAddr1.maxLength}"></h:inputText>
									<hx:commandExButton type="button" value="〒"
										onclick="return openSubWindow();"
										styleClass="commandExButton_search" id="btnMail"></hx:commandExButton>
									<h:outputText styleClass="outputText" id="text4"
										value="（都道府県市区町村大字）"></h:outputText></TD>
								</TR>
								<TR>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyAddr2" size="50"
										value="#{pc_Cob01505.propHsyAddr2.stringValue}"
										style="#{pc_Cob01505.propHsyAddr2.style}"
										disabled="#{pc_Cob01505.propHsyAddr2.disabled}"
										maxlength="#{pc_Cob01505.propHsyAddr2.maxLength}"></h:inputText>
									<h:outputText styleClass="outputText" id="text5" value="（丁目・字以下）"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyAddr3" size="50"
										value="#{pc_Cob01505.propHsyAddr3.stringValue}"
										style="#{pc_Cob01505.propHsyAddr3.style}"
										disabled="#{pc_Cob01505.propHsyAddr3.disabled}"
										maxlength="#{pc_Cob01505.propHsyAddr3.maxLength}"></h:inputText>
									<h:outputText styleClass="outputText" id="text6"
										value="（マンション/ビル名 号室）"></h:outputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b" rowspan="3"><h:outputText
										styleClass="outputText" id="lblAddr2"
										value="#{pc_Cob01505.propHsyAddrKana1.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyAddrKana1" size="100"
										value="#{pc_Cob01505.propHsyAddrKana1.stringValue}"
										style="#{pc_Cob01505.propHsyAddrKana1.style}"
										disabled="#{pc_Cob01505.propHsyAddrKana1.disabled}"
										maxlength="#{pc_Cob01505.propHsyAddrKana1.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyAddrKana2" size="100"
										value="#{pc_Cob01505.propHsyAddrKana2.stringValue}"
										style="#{pc_Cob01505.propHsyAddrKana2.style}"
										disabled="#{pc_Cob01505.propHsyAddrKana2.disabled}"
										maxlength="#{pc_Cob01505.propHsyAddrKana2.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyAddrKana3" size="100"
										value="#{pc_Cob01505.propHsyAddrKana3.stringValue}"
										style="#{pc_Cob01505.propHsyAddrKana3.style}"
										disabled="#{pc_Cob01505.propHsyAddrKana3.disabled}"
										maxlength="#{pc_Cob01505.propHsyAddrKana3.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c"><h:outputText
										styleClass="outputText" id="lblHsyTel"
										value="#{pc_Cob01505.propHsyTel.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlHsyTel" size="30"
										value="#{pc_Cob01505.propHsyTel.stringValue}"
										style="#{pc_Cob01505.propHsyTel.style}"
										disabled="#{pc_Cob01505.propHsyTel.disabled}"
										maxlength="#{pc_Cob01505.propHsyTel.maxLength}"></h:inputText></TD>
									<TH nowrap class="v_c"><h:outputText
										styleClass="outputText" id="lblHsyFax"
										value="#{pc_Cob01505.propHsyFax.labelName}"></h:outputText></TH>
									<TD width="248"><h:inputText styleClass="inputText"
										id="htmlHsyFax" size="30"
										value="#{pc_Cob01505.propHsyFax.stringValue}"
										style="#{pc_Cob01505.propHsyFax.style}"
										disabled="#{pc_Cob01505.propHsyFax.disabled}"
										maxlength="#{pc_Cob01505.propHsyFax.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_d"><h:outputText
										styleClass="outputText" id="lblHsyKeitaiTel"
										value="#{pc_Cob01505.propHsyKeitaiTel.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlHsyKeitaiTel" size="30"
										value="#{pc_Cob01505.propHsyKeitaiTel.stringValue}"
										style="#{pc_Cob01505.propHsyKeitaiTel.style}"
										disabled="#{pc_Cob01505.propHsyKeitaiTel.disabled}"
										maxlength="#{pc_Cob01505.propHsyKeitaiTel.maxLength}"></h:inputText></TD>
									<TH nowrap class="v_d"><h:outputText
										styleClass="outputText" id="lblHsyRenrakuTel"
										value="#{pc_Cob01505.propHsyRenrakuTel.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlHsyRenrakuTel" size="30"
										value="#{pc_Cob01505.propHsyRenrakuTel.stringValue}"
										style="#{pc_Cob01505.propHsyRenrakuTel.style}"
										disabled="#{pc_Cob01505.propHsyRenrakuTel.disabled}"
										maxlength="#{pc_Cob01505.propHsyRenrakuTel.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_e"><h:outputText
										styleClass="outputText" id="lblHsyRenraku"
										value="#{pc_Cob01505.propHsyRenraku.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlHsyRenraku" size="100"
										value="#{pc_Cob01505.propHsyRenraku.stringValue}"
										style="#{pc_Cob01505.propHsyRenraku.style}"
										disabled="#{pc_Cob01505.propHsyRenraku.disabled}"
										maxlength="#{pc_Cob01505.propHsyRenraku.maxLength}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE class="button_bar" cellspacing="1" cellpadding="1">
							<TBODY>
								<TR>
									<TD align="center" width="792"><hx:commandExButton type="submit"
										value="確定" styleClass="commandExButton_dat" id="kakutei"
										action="#{pc_Cob01505.doKakuteiAction}"></hx:commandExButton>
										<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Cob01505.doDeleteAction}"
										disabled="#{pc_Cob01505.propBtnDelete.disabled}"></hx:commandExButton>
										<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Cob01505.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						<HR class="hr" noshade width="100%">
						<TABLE class="button_bar" cellspacing="1" cellpadding="1">
							<TBODY>
								<TR>
									<TD align="center" width="795"><hx:commandExButton type="submit"
										value="一覧確定" styleClass="commandExButton_dat" id="ichirankakutei"
										action="#{pc_Cob01505.doIchiranKakuteiAction}"
										onclick="onChangeData();"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>			
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Cob01502T01.cob01502.propHidChangeDataFlg.stringValue}" ></h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
