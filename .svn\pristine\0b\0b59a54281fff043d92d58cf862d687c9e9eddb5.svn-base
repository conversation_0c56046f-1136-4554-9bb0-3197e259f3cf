<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb03001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Keb03001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<style type="text/css">
<!--
 .setWidth TD {width: 90px; white-space: nowrap;}
-->
</style>
 
<SCRIPT type="text/javascript">
//スクロール位置の復元
function func_1(thisObj, thisEvent) {
	changeScrollPosition('scroll','listscroll');
	// 予算単位名称取得
	func_5(thisObj, thisEvent);
	// 目的名称取得
	func_6(thisObj, thisEvent);
	// 科目名称取得
	func_7(thisObj, thisEvent);
}
//確認メッセージでOKボタンクリック時
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}
//確認メッセージでキャンセルボタンクリック時
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}

function func_2(thisObj, thisEvent) {
	openModalWindow("", "PCog0101", "<%=com.jast.gakuen.rev.co.PCog0101.getWindowOpenOption()%>");
	setTarget("PCog0101");
	return true;

}
function func_3(thisObj, thisEvent) {
	openModalWindow("", "PCog0201", "<%=com.jast.gakuen.rev.co.PCog0201.getWindowOpenOption()%>");
	setTarget("PCog0201");
	return true;

}
function func_4(thisObj, thisEvent) {
	openModalWindow("", "PCog0301", "<%=com.jast.gakuen.rev.co.PCog0301.getWindowOpenOption()%>");
	setTarget("PCog0301");
	return true;

}
function func_5(thisObj, thisEvent) {
	// 予算単位名称取得
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlYsnTName";
	var args = new Array();
	
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById("form1:htmlYsnTCd").value;
	args['code3'] = "60";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet,target,args);
	return true;

}
function func_6(thisObj, thisEvent) {
	// 目的名称取得
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMokuName";
	var args = new Array();
	
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById("form1:htmlMokuCd").value;
	args['code3'] = "60";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet,target,args);
	return true;

}
function func_7(thisObj, thisEvent) {
	// 科目名称取得
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkName";
	var args = new Array();
	
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById("form1:htmlKmkCd").value;
	args['code3'] = "60";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet,target,args);
	return true;

}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_1(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb03001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Keb03001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb03001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb03001.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- レイアウト対応、全角スペース -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Keb03001.propKaikeiNendo.labelName}"
										style="#{pc_Keb03001.propKaikeiNendo.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlKaikeiNendo" size="5"
										disabled="#{pc_Keb03001.propKaikeiNendo.disabled}"
										readonly="#{pc_Keb03001.propKaikeiNendo.readonly}"
										style="#{pc_Keb03001.propKaikeiNendo.style}"
										value="#{pc_Keb03001.propKaikeiNendo.dateValue}" tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblYsnTCd" value="#{pc_Keb03001.propYsnTCd.labelName}"
										style="#{pc_Keb03001.propYsnTCd.labelStyle}"></h:outputText></TH>
									<TD width="650px" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												size="14" disabled="#{pc_Keb03001.propYsnTCd.disabled}"
												maxlength="#{pc_Keb03001.propYsnTCd.maxLength}"
												readonly="#{pc_Keb03001.propYsnTCd.readonly}"
												value="#{pc_Keb03001.propYsnTCd.stringValue}"
												style="#{pc_Keb03001.propYsnTCd.style}" tabindex="2"
												id="htmlYsnTCd" onblur="return func_5(this, event);"></h:inputText><hx:commandExButton
												type="submit"
												styleClass="commandExButton_search" id="searchYsnTCd"
												onclick="return func_2(this, event);"
												action="#{pc_Keb03001.doSearchYsnTCdAction}" tabindex="3"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlYsnTName"
												value="#{pc_Keb03001.propYsnTName.stringValue}"
												style="#{pc_Keb03001.propYsnTName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblMokuCd" value="#{pc_Keb03001.propMokuCd.labelName}"
										style="#{pc_Keb03001.propMokuCd.labelStyle}"></h:outputText></TH>
									<TD width="650px" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlMokuCd" size="14"
												disabled="#{pc_Keb03001.propMokuCd.disabled}"
												maxlength="#{pc_Keb03001.propMokuCd.maxLength}"
												readonly="#{pc_Keb03001.propMokuCd.readonly}"
												style="#{pc_Keb03001.propMokuCd.style}"
												value="#{pc_Keb03001.propMokuCd.stringValue}" tabindex="4"
												onblur="return func_6(this, event);"></h:inputText><hx:commandExButton
												type="submit"
												styleClass="commandExButton_search" id="searchMokuCd"
												action="#{pc_Keb03001.doSearchMokuCdAction}" tabindex="5"
												onclick="return func_3(this, event);"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMokuName"
												value="#{pc_Keb03001.propMokuName.stringValue}"
												style="#{pc_Keb03001.propMokuName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblKmkCd" value="#{pc_Keb03001.propKmkCd.labelName}"
										style="#{pc_Keb03001.propKmkCd.labelStyle}"></h:outputText></TH>
									<TD width="650px" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlKmkCd" size="14"
												disabled="#{pc_Keb03001.propKmkCd.disabled}"
												maxlength="#{pc_Keb03001.propKmkCd.maxLength}"
												readonly="#{pc_Keb03001.propKmkCd.readonly}"
												style="#{pc_Keb03001.propKmkCd.style}"
												value="#{pc_Keb03001.propKmkCd.stringValue}" tabindex="6"
												onblur="return func_7(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchKmkCd"
												action="#{pc_Keb03001.doSearchKmkCdAction}" tabindex="7"
												onclick="return func_4(this, event);"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlKmkName"
												value="#{pc_Keb03001.propKmkName.stringValue}"
												style="#{pc_Keb03001.propKmkName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblKomokuNaiyo"
										value="#{pc_Keb03001.propKomokuNaiyo.labelName}"
										style="#{pc_Keb03001.propKomokuNaiyo.labelStyle}"></h:outputText></TH>
									<TD>
									<TABLE width="500" border="0" cellpadding="0" cellspacing="0"
										class="clear_border">
										<TBODY>
											<TR>
												<TD width="350"><h:inputText styleClass="inputText"
													id="htmlKomokuNaiyo" size="56"
													disabled="#{pc_Keb03001.propKomokuNaiyo.disabled}"
													maxlength="#{pc_Keb03001.propKomokuNaiyo.maxLength}"
													readonly="#{pc_Keb03001.propKomokuNaiyo.readonly}"
													style="#{pc_Keb03001.propKomokuNaiyo.style}"
													value="#{pc_Keb03001.propKomokuNaiyo.stringValue}"
													tabindex="8"></h:inputText></TD>
												<TD><h:outputText styleClass="outputText"
													id="lblKomokuNaiyoFindType" value="（部分一致）"
													style="font-size: 9pt"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="検索"
										styleClass="commandExButton_dat" id="search"
										action="#{pc_Keb03001.doSearchAction}" tabindex="9"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Keb03001.doClearAction}" tabindex="10"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_f"><h:outputText
										styleClass="outputText" id="lblKijunDate"
										value="#{pc_Keb03001.propKijunDate.labelName}"
										style="#{pc_Keb03001.propKijunDate.labelStyle}"></h:outputText></TH>
									<TD>
									<TABLE width="450" border="0" cellpadding="0" cellspacing="0"
										class="clear_border">
										<TBODY>
											<TR>
												<TD width="460"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio setWidth" id="htmlKijunDate"
													value="#{pc_Keb03001.propKijunDate.stringValue}"
													tabindex="11"
													disabled="#{pc_Keb03001.propKijunDate.disabled}"
													readonly="#{pc_Keb03001.propKijunDate.readonly}">
													<f:selectItems value="#{pc_Keb03001.propKijunDate.list}" />
												</h:selectOneRadio></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_g"><h:outputText styleClass="outputText"
										id="lblZandakaKbn"
										value="#{pc_Keb03001.propZandakaKbn.labelName}"
										style="#{pc_Keb03001.propZandakaKbn.labelStyle}"></h:outputText></TH>
									<TD>
									<TABLE width="450" border="0" cellpadding="0" cellspacing="0"
										class="clear_border">
										<TBODY>
											<TR>
												<TD width="460"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio setWidth" id="htmlZandakaKbn"
													disabled="#{pc_Keb03001.propZandakaKbn.disabled}"
													readonly="#{pc_Keb03001.propZandakaKbn.readonly}"
													tabindex="12"
													value="#{pc_Keb03001.propZandakaKbn.stringValue}">
													<f:selectItems value="#{pc_Keb03001.propZandakaKbn.list}" />
												</h:selectOneRadio></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900">
							<TBODY>
								<TR>
									<TD width="47"></TD>
									<TD width="798" height="20" align="right"><h:outputText styleClass="outputText"
										id="htmlListCount"
										value="#{pc_Keb03001.propTableList1.listCount}">
										<f:convertNumber pattern="#,##0" />
									</h:outputText><h:outputText styleClass="outputText"
										id="lblKen"
										value="件"></h:outputText></TD>
								</TR>
								<TR>
									<TD width="47"></TD>
									<TD width="798" align="left" nowrap>
									<TABLE border="1" class="meisai_page" width="100%">
										<TR>
											<TH align="center" nowrap width="176" style="border-bottom-style: none;"><h:outputText styleClass="outputText"
												id="lblColMokuName" value="目的"></h:outputText></TH>
											<TH align="center" nowrap width="174"><h:outputText styleClass="outputText"
												id="lblColKmkName" value="科目"></h:outputText></TH>
											<TH align="center" nowrap width="174"><h:outputText styleClass="outputText"
												id="lblcolKomokuNaiyo" value="項目内容"></h:outputText></TH>
											<TH align="center" nowrap width="106"><h:outputText styleClass="outputText"
												id="lblColShikkoGendoGaku" value="執行限度額"></h:outputText></TH>
											<TH align="center" nowrap width="106"><h:outputText styleClass="outputText"
												id="lblColKihyoZandaka" value="起票残高"></h:outputText></TH>
											<TH align="center" nowrap ><h:outputText styleClass="outputText"
												id="lblColDummy"
												value="　"></h:outputText></TH>
										</TR>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD width="47"></TD>
									<TD width="798" align="left" nowrap>
									<DIV class="listScroll" style="height: 274px; width: 796px;"
										id="listScroll" 
										onscroll="setScrollPosition('scroll',this);"><h:dataTable border="0"
										cellpadding="2" cellspacing="0" headerClass="headerClass" footerClass="footerClass"
										rowClasses="#{pc_Keb03001.propTableList1.rowClasses}"
										styleClass="meisai_scroll" id="htmlTableList1" var="varlist" style="table-layout: fixed;"
										value="#{pc_Keb03001.propTableList1.list}" width="774px">
										<h:column id="column1">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColMokuNamek"
												value="#{varlist.colMokuName.displayValue}"
												title="#{varlist.colMokuName.stringValue}" 
												style="white-space:nowrap;"></h:outputText>
											<f:attribute value="174" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKmkName"
												value="#{varlist.colKmkName.displayValue}"
												title="#{varlist.colKmkName.stringValue}"
												style="white-space:nowrap;"></h:outputText>
											<f:attribute value="174" name="width" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKomokuNaiyo"
												value="#{varlist.colKomokuNaiyo.displayValue}"
												title="#{varlist.colKomokuNaiyo.stringValue}"
												style="white-space:nowrap;"></h:outputText>
											<f:attribute value="174" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText"
												id="htmlColShikkoGendoGaku"
												value="#{varlist.colShikkoGendoGaku}"></h:outputText>
											<f:attribute value="106" name="width" />
											<f:attribute value="text-align: right" name="style" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKihyoZandak"
												value="#{varlist.colKihyoZandaka}"></h:outputText>
											<f:attribute value="106" name="width" />
											<f:attribute value="true" name="nowrap" />
											<f:attribute value="text-align: right" name="style" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="true" name="nowrap" />
											<hx:commandExButton type="submit" value="詳細 "
												styleClass="commandExButton" id="detail" tabindex="13"
												style="width: 40px" action="#{pc_Keb03001.doDetailAction}"></hx:commandExButton>
											<f:attribute value="38" name="width" />
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				id="scroll"
				value="#{pc_Keb03001.propTableList1.scrollPosition}">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Keb03001.propExecutableSearchHidden.integerValue}"
				id="htmlExecutableSearchHidden">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

