<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmc01401T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kme00501T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function confirmOk() {	
	document.getElementById('form1:removeAllButtonCount').value = "1";
	indirectClick('allRemove');
}
function confirmCancel() {
	// alert('実行を中断しました。');	
}

function delConfirm(message) {
    var param = new Array("リスト情報");
    return confirm(messageCreate(message,param));
}

function openJugyoCdWindow() {
	// 授業検索画面（引数：なし）

	var nendo = document.getElementById("form1:htmlNendo").value;
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmd0101.jsp?retFieldName=form1:htmlJugyoCd&nendo="+nendo;
	openModalWindow(url, "pKmd0101", "<%=com.jast.gakuen.rev.km.PKmd0101.getWindowOpenOption() %>");
	return true;
}

function doJugyoAjax(thisObj, thisEvent) {
	 // 授業名称を取得する
	 var servlet = "rev/km/KmJugyoKamokuAJAX";
	 var args = new Array();
	 args['jigyoCd'] = document.getElementById("form1:htmlJugyoCd").value;
	 args['nendo'] = document.getElementById("form1:htmlNendo").value;

	 var target = "form1:htmlJugyoName";
	 var ajaxUtil = new AjaxUtil();
	 ajaxUtil.getPluralValueSetMethod(servlet, target, args, 'callBackMethodJk');
}

function callBackMethodJk(value){
    //　ラベル
		var show = value['kamokName'] + " " + value['jugyoName'];
   	document.getElementById('form1:htmlJugyoName').innerHTML = show;
   	document.getElementById('form1:htmlJugyoName').title = show;
}

function loadAction(event){
 doJugyoAjax(document.getElementById('form1:htmlJugyoCd'), event);

}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmc01401T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmc01401T02.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmc01401T02.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmc01401T02.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD align="center">

						<TABLE width="650" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="200"><h:outputText
										styleClass="outputText" id="lblChusenKekkaKbn"
										value="#{pc_Kmc01401T01.kmc01401.propChusenKekkaKbn.labelName}"
										style="#{pc_Kmc01401T01.kmc01401.propChusenKekkaKbn.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlChusenKekkaKbn"
										value="#{pc_Kmc01401T01.kmc01401.propChusenKekkaKbn.stringValue}"
										style="#{pc_Kmc01401T01.kmc01401.propChusenKekkaKbn.style}">
										<f:selectItems
											value="#{pc_Kmc01401T01.kmc01401.propChusenKekkaKbn.list}" />
									</h:selectManyCheckbox></TD>
								</TR>
								<TR>
									<TH class="v_b" width="200"><h:outputText
										styleClass="outputText" id="lblTeiinOver"
										value="出力条件"
										style="#{pc_Kmc01401T01.kmc01401.propTeiinOver.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlTeiinOver"
										value="#{pc_Kmc01401T01.kmc01401.propTeiinOver.checked}"
										style="#{pc_Kmc01401T01.kmc01401.propTeiinOver.style}">
									</h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblTeiinOver2"
										value="#{pc_Kmc01401T01.kmc01401.propTeiinOver.labelName}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="200"><h:outputText
										styleClass="outputText" id="lblPdfTitle"
										value="#{pc_Kmc01401T01.kmc01401.propPdfTitle.labelName}"
										style="#{pc_Kmc01401T01.kmc01401.propPdfTitle.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:inputText styleClass="inputText"
										id="htmlPdfTitle"
										value="#{pc_Kmc01401T01.kmc01401.propPdfTitle.stringValue}"
										style="#{pc_Kmc01401T01.kmc01401.propPdfTitle.style}"
										size="60"
										maxlength="#{pc_Kmc01401T01.kmc01401.propPdfTitle.maxLength}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center"><BR>
						</TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD align="left">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD><hx:commandExButton type="submit" value="一括指定"
													styleClass="tab_head_off" id="ikkatsuSitei"
													action="#{pc_Kmc01401T02.doIkkatsuSiteiAction}"></hx:commandExButton></TD>
												<TD><hx:commandExButton type="submit" value="授業指定"
													styleClass="tab_head_on" id="jugyoSitei"
													action="#{pc_Kmc01401T02.doJugyoSiteiAction}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="650"
									class="tab_body">
										<TBODY>
											<TR height="300">
												<TD valign="top"  align="center">
												<BR>
														<TABLE border="0" cellpadding="0" cellspacing="0"
																width="600" class="table">
																<TBODY>
																	<TR>
																		<TH bgcolor="" width="200" class="v_a"><h:outputText
																			styleClass="outputText" id="lblInputFile"
																			value="#{pc_Kmc01401T02.propInputFile.labelName}"></h:outputText><BR>
																		<h:outputText styleClass="outputText"
																			id="lblInputFileOld"
																			value="#{pc_Kmc01401T02.propInputFileOld.labelName}"
																			style="#{pc_Kmc01401T02.propInputFileOld.labelStyle}"></h:outputText>
																		</TH>
																		<TD colspan="2" width="400"><hx:fileupload
																			styleClass="fileupload" id="htmlInputFile"
																			value="#{pc_Kmc01401T02.propInputFile.value}"
																			style="#{pc_Kmc01401T02.propInputFile.style};width:355px"
																			size="50">
																			<hx:fileProp name="fileName"
																				value="#{pc_Kmc01401T02.propInputFile.fileName}" />
																			<hx:fileProp name="contentType" />
																		</hx:fileupload><hx:commandExButton type="submit"
																			value="取込" styleClass="commandExButton"
																			id="inputFile"
																			action="#{pc_Kmc01401T02.doInputFileAction}"></hx:commandExButton><BR>
																		<h:outputText
																			styleClass="outputText" id="htmlInputFileOld"
																			value="#{pc_Kmc01401T02.propInputFileOld.stringValue}"
																			style="#{pc_Kmc01401T02.propInputFileOld.style}"></h:outputText><BR></TD>
																	</TR>
																	<TR>
																		<TH bgcolor="" width="200" class="v_b"><h:outputText
																			styleClass="outputText" id="lblNendo"
																			value="#{pc_Kmc01401T02.propNendo.labelName}"
																			style="#{pc_Kmc01401T02.propNendo.labelStyle}"></h:outputText></TH>
																		<TD width="400" colspan="2"><h:inputText
																			styleClass="inputText" id="htmlNendo"
																			value="#{pc_Kmc01401T02.propNendo.dateValue}"
																			style="#{pc_Kmc01401T02.propNendo.style}"
																			size="10">
																			<hx:inputHelperAssist errorClass="inputText_Error"
						    												imeMode="inactive" promptCharacter="_" />
																			<f:convertDateTime pattern="yyyy" />
																		</h:inputText></TD>
																	</TR>
																	<TR>
																		<TH bgcolor="" width="200" class="v_c"><h:outputText
																			styleClass="outputText" id="lblJugyoCd"
																			value="#{pc_Kmc01401T02.propJugyoCd.labelName}"
																			style="#{pc_Kmc01401T02.propJugyoCd.labelStyle}"></h:outputText></TH>
																		<TD width="200"><h:inputText styleClass="inputText"
																			id="htmlJugyoCd"
																			onblur="return doJugyoAjax(this, event);"
																			value="#{pc_Kmc01401T02.propJugyoCd.stringValue}"
																			style="#{pc_Kmc01401T02.propJugyoCd.style}" size="10"
																			maxlength="#{pc_Kmc01401T02.propJugyoCd.maxLength}"></h:inputText><hx:commandExButton
																			type="button" styleClass="commandExButton_search"
																			id="jugyoSearch"
																			onclick="return openJugyoCdWindow(this, event);">
																		</hx:commandExButton><hx:commandExButton type="submit"
																			value="追加" styleClass="commandExButton" id="add"
																			action="#{pc_Kmc01401T02.doAddAction}"></hx:commandExButton></TD>
																		<TD width="200"><h:outputText styleClass="outputText"
																			id="htmlJugyoName"></h:outputText>&nbsp;</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<HR noshade class="hr">
												<CENTER>
												<TABLE border="0" cellspacing="0" width="600">
													<TBODY>
														<TR>
															<TD width="410" align="left">
																<h:outputText
																	styleClass="outputText" id="lblTargetJugyoList"
																	value="　開始年度　対象授業">
																</h:outputText>
															</TD>
															<TD width="90"></TD>
														</TR>
														<TR>
															<TD width="540"><h:selectManyListbox
																styleClass="selectManyListbox" id="htmlTargetJugyo"
																size="10" style="width: 100%"
																value="#{pc_Kmc01401T02.propTargetJugyo.integerValue}">
																<f:selectItems
																	value="#{pc_Kmc01401T02.propTargetJugyo.list}" />
															</h:selectManyListbox></TD>
															<TD valign="top" align="center"><hx:commandExButton
																type="submit" value="除外" styleClass="commandExButton"
																id="remove" action="#{pc_Kmc01401T02.doRemoveAction}"
																style="width:60px"></hx:commandExButton><BR>
															<h:outputText styleClass="outputText" id="lblManySelect"
																value="（複数選択可）"></h:outputText> <BR>
															<hx:commandExButton type="submit" value="全て除外"
																styleClass="commandExButton" id="allRemove"
																action="#{pc_Kmc01401T02.doAllRemoveAction}"
																onclick="return delConfirm('#{msg.SY_MSG_0006W}');" style="width:60px"></hx:commandExButton></TD>

														</TR>
														<TR>
															<TD align="right"><h:outputFormat
																styleClass="outputFormat" id="htmlAllCount"
																value="合計件数：　{0}件">
																<f:param name="normalCount"
																	value="#{pc_Kmc01401T02.propTargetJugyo.listCount}"></f:param>
															</h:outputFormat><h:outputFormat
																styleClass="outputFormat" id="htmlNomalCount"
																value="正常件数：　{0}件">
																<f:param name="normalCount"
																	value="#{pc_Kmc01401T02.propTargetJugyo.listCount - pc_Kmc01401T02.propErrorCount.integerValue}"></f:param>
															</h:outputFormat><h:outputFormat
																styleClass="outputFormat" id="htmlErrorCount"
																value="エラー件数：　{0}件">
																<f:param name="errorCount"
																	value="#{pc_Kmc01401T02.propErrorCount.integerValue}"></f:param>
															</h:outputFormat></TD>
															<TD valign="top" align="center"></TD>

														</TR>
													</TBODY>
												</TABLE>												
												</CENTER>
												</TD>
											</TR>
										   <TR height="50">
										 		<TD  valign="top">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													width="100%" class="button_bar">
													<TBODY>
														<TR id="">
															<TD width="100%"><hx:commandExButton type="submit"
																value="PDF作成" styleClass="commandExButton_out"
																id="pdfout" action="#{pc_Kmc01401T02.doPdfoutAction}" confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
															<hx:commandExButton type="submit" value="CSV作成"
																styleClass="commandExButton_out" id="csvout"
																action="#{pc_Kmc01401T02.doCsvoutAction}" confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
															<hx:commandExButton type="submit" value="出力項目指定"
																styleClass="commandExButton_out" id="setoutput"
																action="#{pc_Kmc01401T02.doSetoutputAction}"></hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>
										     </TD>
										   </TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Kmc01401T02.propRemoveAllButton.integerValue}"
				id="removeAllButtonCount"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>
