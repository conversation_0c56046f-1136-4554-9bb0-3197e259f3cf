<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab00804T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>リース資産登録(資産情報)</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function confirmOk() {
	var status;

	var execDel = document.getElementById("form1:htmlExecutableDelete");
	status = execDel.value;
	if (status == "1") {
		// 減価償却履歴確認ＯＫ
		execDel.value = "2";
		indirectClick("delete");
	} else if (status == "2") {
		// 修繕資産確認ＯＫ
		execDel.value = "3";
		indirectClick("delete");
	} else if (status == "3") {
		// リース付属品確認ＯＫ
		execDel.value = "4";
		indirectClick("delete");
	} else if (status == "4") {
		// リース財産目録確認ＯＫ
		execDel.value = "5";
		indirectClick("delete");
	} else if (status == "5") {
		// 除却対象確認ＯＫ
		execDel.value = "6";
		indirectClick("delete");
	} else if (status == "6") {
		// 廃棄確認ＯＫ
		execDel.value = "7";
		indirectClick("delete");
	}
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableDelete').value = "0";
}
//--- Ajax ---
function func_SetchiBashoAjax(thisObj, thisEvent) {
	// 設置場所名称を取得する
	var servlet = "rev/ka/KazSetiAJAX";
	var args = new Array();
	args['code'] = document.getElementById('form1:htmlSetchiBashoCd').value;
	var target = "form1:htmlSetchiBashoName";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_KanriBmnAjax(thisObj, thisEvent) {
	// 管理部門コード名称を取得する
	var servlet = "rev/ka/KaCogYosanTaniAJAX";	
	var target = "form1:htmlKanriBmnName";
	var args = new Array();
	args['code1'] = "3";
	args['code2'] = "1";
	args['code3'] = document.getElementById('form1:htmlLeaseEndDateHidden').value;
	args['code4'] = document.getElementById('form1:htmlKanriBmnCd').value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_KanrishaAjax(thisObj, thisEvent) {
	// 氏名を取得する
	var servlet = "rev/ka/KaCoiJinjAJAX";	
	var target = "form1:htmlKanrishaName";
	var args = new Array();
	args['code'] = document.getElementById('form1:htmlKanrishaId').value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_KyokanAnbunAjax(thisObj, thisEvent) {
	// 教官按分パターンを取得する
	var servlet = "rev/ka/KazKkanPatanAJAX";	
	var target = "form1:htmlKyokanAnbunPtnName";
	var code = new Array();
	code['code1'] = '3';
	code['code2'] = document.getElementById('form1:htmljokyakuDateForAjax').value;
	code['code3'] = thisObj.value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code );
}
function func_kyokanAnbunPtnSetDisabled(thisObj, thisEvent) {
	// 「教管区分≠共通」の場合、教管按分パターンをクリアしDisabledにする
	// (※資産対象外の場合は、「教管区分＝共通」でも上記処理を行う)
	var kyokanAnbunPtn = document.getElementById('form1:htmlKyokanAnbunPtn');
	var kyokanKbns = document.getElementsByName('form1:htmlKyokanKbn');
	var ssnTaishos = document.getElementsByName('form1:htmlShisanTaisho');
	for (i = 0 ; i < kyokanKbns.length ; i++) {
		if (kyokanKbns[i].checked) {
			var kyokanKbn = kyokanKbns[i].value;
		}
	}
	for (i = 0 ; i < ssnTaishos.length ; i++) {
		if (ssnTaishos[i].checked) {
			var ssnTaisho = ssnTaishos[i].value;
		}
	}
	if (ssnTaisho == 0) {
		kyokanAnbunPtn.value = "";
		elementDisabled(document.getElementById('form1:searchKyokanAnbubnPtn'), true);
		elementDisabled(document.getElementById('form1:htmlKyokanAnbunPtn'), true);
	} else if (kyokanKbn == 1) {
		kyokanAnbunPtn.value = "";
		elementDisabled(document.getElementById('form1:searchKyokanAnbubnPtn'), true);
		elementDisabled(document.getElementById('form1:htmlKyokanAnbunPtn'), true);
	} else if (kyokanKbn == 2) {
		kyokanAnbunPtn.value = "";
		elementDisabled(document.getElementById('form1:searchKyokanAnbubnPtn'), true);
		elementDisabled(document.getElementById('form1:htmlKyokanAnbunPtn'), true);
	} else if (kyokanKbn == 3) {
		elementDisabled(document.getElementById('form1:searchKyokanAnbubnPtn'), false);
		elementDisabled(document.getElementById('form1:htmlKyokanAnbunPtn'), false);
	}
	func_KyokanAnbunAjax(document.getElementById('form1:htmlKyokanAnbunPtn'), "");
}
function radioChangeSetDisabled(id) {
	document.getElementById("form1:linkDisabledFlg").rendered=true;
	indirectClick(id);
}
function loadFunc() {
	changeScrollPosition('scroll','listScroll');

	func_SetchiBashoAjax(document.getElementById('form1:htmlSetchiBashoCd'), '');
	func_KanriBmnAjax(document.getElementById('form1:htmlKanriBmnCd'), '');
	func_KanrishaAjax(document.getElementById('form1:htmlKanrishaId'), '');
	func_kyokanAnbunPtnSetDisabled(document.getElementById("form1:htmlKyokanAnbunPtn"), "");
}

window.attachEvent("onload", attachFormatNumber);
window.attachEvent("onload", loadFunc);


</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY>
<hx:scriptCollector id="scriptCollector1"
	preRender="#{pc_Kab00804T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
	value="閉じる" styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kab00804T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
	styleClass="outputText" id="htmlFuncId"
	value="#{pc_Kab00804T01.funcId}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlLoginId"
	value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlScrnName"
	value="#{pc_Kab00804T01.screenName}"></h:outputText></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
	id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText></FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
	type="submit" value="戻る" styleClass="commandExButton"
	id="returnDisp" action="#{pc_Kab00804T01.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="900" style="margin-top: 20px;">
		<TBODY>
			<TR>
				<TH class="v_a" width="140"><h:outputText styleClass="outputText"
					id="lblLeaseNo"
					value="#{pc_Kab00804T01.kab00804.propLeaseNo.labelName}"
					style="#{pc_Kab00804T01.kab00804.propLeaseNo.labelStyle}"></h:outputText></TH>
				<TD width="150"><h:outputText styleClass="outputText"
							id="htmlLeaseNo"
							value="#{pc_Kab00804T01.kab00804.propLeaseNo.stringValue}"
							title="#{pc_Kab00804T01.kab00804.propLeaseNo.stringValue}"></h:outputText></TD>
				<TH class="v_c" width="140"><h:outputText styleClass="outputText"
							id="lblKeiyakuBukkenName"
							value="#{pc_Kab00804T01.kab00804.propKeiyakuBukkenName.labelName}"
							style="#{pc_Kab00804T01.kab00804.propKeiyakuBukkenName.labelStyle}"></h:outputText></TH>
				<TD width="450">
					<DIV style="width:450px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlKeiyakuBukkenName"
							value="#{pc_Kab00804T01.kab00804.propKeiyakuBukkenName.stringValue}"
							title="#{pc_Kab00804T01.kab00804.propKeiyakuBukkenName.stringValue}"></h:outputText>
					</DIV>		
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" width="900">
		<TBODY>
			<TR>
				<TD align="right"><h:outputText
					styleClass="outputText" id="htmlListCount"
					value="#{pc_Kab00804T01.kab00804.propLeaseSisnList.listCount}件"
					style="font-size: 8pt"></h:outputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" width="900">
		<TBODY>
			<TR>
				<TD width="900">
					<DIV class="listScroll"
						style="height:86px; OVERFLOW:scroll;overflow-x: hidden;" 
						id="listScroll" onscroll="setScrollPosition('scroll', this);"><h:dataTable
						border="0" cellpadding="2" cellspacing="0"
						headerClass="headerClass" footerClass="footerClass"
						rowClasses="#{pc_Kab00804T01.kab00804.propLeaseSisnList.rowClasses}"
						styleClass="meisai_scroll" id="htmlLeaseSisnList" width="880"
						value="#{pc_Kab00804T01.kab00804.propLeaseSisnList.list}"
						var="varlist"
						first="#{pc_Kab00804T01.kab00804.propLeaseSisnList.first}"
						rows="#{pc_Kab00804T01.kab00804.propLeaseSisnList.rows}">
						<h:column id="column1">
							<f:facet name="header">
									<h:outputText id="lblListLeaseSsnNo" styleClass="outputText"
										value="リース資産番号"></h:outputText>
								</f:facet>
							<f:attribute value="90" name="width" />
								<h:outputText styleClass="outputText" id="htmlListLeaseSsnNo"
									value="#{varlist.leaseSsnNo.stringValue}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column2">
							<f:facet name="header">
									<h:outputText id="lblListSsnName" styleClass="outputText"
										value="資産名称"></h:outputText>
								</f:facet>
							<hx:jspPanel id="jspPanel1">
								<DIV style="width:220px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListSsnName"
										value="#{varlist.ssnName.stringValue}" styleClass="outputText"
										style="white-space:nowrap;"
										title="#{varlist.ssnName.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="220" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />	
						</h:column>
						<h:column id="column3">
							<f:facet name="header">
									<h:outputText id="lblListSetchiBashoName"
										styleClass="outputText" value="設置場所名称"></h:outputText>
								</f:facet>
							<hx:jspPanel id="jspPanel2">
								<DIV style="width:180px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListSetchiBashoName"
										value="#{varlist.setchiBashoName.stringValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.setchiBashoName.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="180" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />	
						</h:column>
						<h:column id="column4">
							<f:facet name="header">
									<h:outputText id="lblListKanriBmnName" styleClass="outputText"
										value="管理部門名称"></h:outputText>
								</f:facet>
							<hx:jspPanel id="jspPanel3">
								<DIV style="width:180px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListKanriBmnName"
										value="#{varlist.kanriBmnName.stringValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.kanriBmnName.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="180" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />	
						</h:column>
						<h:column id="column5">
							<f:facet name="header">
								<h:outputText id="lblListShutokuKagaku" styleClass="outputText" value="取得価額"></h:outputText>
							</f:facet>
							<hx:jspPanel id="jspPanel4">
								<DIV style="width:140px;white-space:nowrap;overflow:hidden;display:block;text-align:right;">
									<h:outputText id="htmlListShutokuKagaku"
										value="#{varlist.shutokuKagaku.stringValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.shutokuKagaku.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="140" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />
						</h:column>
						<h:column id="column6">
							<f:facet name="header">
							</f:facet>
							<hx:commandExButton type="submit" styleClass="commandExButton"
								id="select" value="選択"
								style="width:38"
								action="#{pc_Kab00804T01.doSelectAction}"></hx:commandExButton>
							<f:attribute value="38px" name="width" />
							<f:attribute value="text-align: center; vertical-align: middle"
								name="style" />
						</h:column>
					</h:dataTable>
					</DIV>
				</TD>
				
			</TR>
		</TBODY>
	</TABLE>

	<TABLE border="0" cellpadding="0" cellspacing="0" width="900" height="390" style="margin-top: 10px;">
		<TBODY>
			<TR>
				<TD align="left" height="27">
					<hx:commandExButton
						type="submit"
						style="width: 164px"
						value="資産情報１"
						styleClass="tab_head_on"
						id="ssnInfo1"></hx:commandExButton><hx:commandExButton
						type="submit"
						style="width: 164px"
						value="資産情報２"
						styleClass="tab_head_off"
						id="ssnInfo2"
						disabled="#{pc_Kab00804T01.kab00804.propSsnInfo2.disabled}"
						action="#{pc_Kab00804T01.doSsnInfo2Action}"></hx:commandExButton><hx:commandExButton
						type="submit"
						style="width: 164px"
						value="形態情報"
						styleClass="tab_head_off"
						id="keitaiInfo"
						action="#{pc_Kab00804T01.doKeitaiInfoAction}"></hx:commandExButton><hx:commandExButton
						type="submit"
						style="width: 164px"
						value="管理情報"
						styleClass="tab_head_off"
						id="kanriInfo" 
						disabled="#{pc_Kab00804T01.kab00804.propKanriInfo.disabled}"
						action="#{pc_Kab00804T01.doKanriInfoAction}">
					</hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD height="100%" align="left" valign="top">
					<TABLE border="1" cellpadding="20" cellspacing="0" height="390" width="900" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center"  valign="top">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="880" class="table" style="margin-top: 10px;">
								<TBODY>
									<TR>
										<TH class="v_a" width="160">
											<h:outputText styleClass="outputText"
												id="lblLeaseSsnNo"
												value="#{pc_Kab00804T01.propLeaseSsnNo.labelName}"
												style="#{pc_Kab00804T01.propLeaseSsnNo.labelStyle}"></h:outputText>
										</TH>
										<TD width="700" colspan="3">
											<h:outputText styleClass="outputText"
												id="htmlLeaseSsnNo"
												value="#{pc_Kab00804T01.propLeaseSsnNo.stringValue}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_a" width="160">
											<h:outputText styleClass="outputText"
												id="lblSsnBnrCd"
												value="#{pc_Kab00804T01.propSsnBnrCd.labelName}"
												style="#{pc_Kab00804T01.propSsnBnrCd.labelStyle}"></h:outputText>
										</TH>
										<TD width="700" colspan="3">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlSsnBnrCd" value="#{pc_Kab00804T01.propSsnBnrCd.value}"
												disabled="#{pc_Kab00804T01.propSsnBnrCd.disabled}">
												<f:selectItems value="#{pc_Kab00804T01.propSsnBnrCd.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
											<h:outputText styleClass="outputText"
												id="lblSsnName"
												value="#{pc_Kab00804T01.propSsnName.labelName}"
												style="#{pc_Kab00804T01.propSsnName.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText styleClass="inputText"
												id="htmlSsnName" size="100"
												value="#{pc_Kab00804T01.propSsnName.stringValue}"
												style="#{pc_Kab00804T01.propSsnName.style}"
												maxlength="#{pc_Kab00804T01.propSsnName.maxLength}"
												readonly="#{pc_Kab00804T01.propSsnName.readonly}"></h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
											<h:outputText styleClass="outputText"
												id="lblSsnNameRyak"
												value="#{pc_Kab00804T01.propSsnNameRyak.labelName}"
												style="#{pc_Kab00804T01.propSsnNameRyak.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText styleClass="inputText"
												id="htmlSsnNameRyak" size="20"
												value="#{pc_Kab00804T01.propSsnNameRyak.stringValue}"
												style="#{pc_Kab00804T01.propSsnNameRyak.style}"
												maxlength="#{pc_Kab00804T01.propSsnNameRyak.maxLength}"
												readonly="#{pc_Kab00804T01.propSsnNameRyak.readonly}"></h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
											<h:outputText styleClass="outputText"
												id="lblSetchiBashoCd"
												value="#{pc_Kab00804T01.propSetchiBashoCd.labelName}"
												style="#{pc_Kab00804T01.propSetchiBashoCd.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<DIV style="width:700px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlSetchiBashoCd" size="10"
												value="#{pc_Kab00804T01.propSetchiBashoCd.stringValue}"
												style="#{pc_Kab00804T01.propSetchiBashoCd.style}"
												maxlength="#{pc_Kab00804T01.propSetchiBashoCd.maxLength}"
												onblur="return func_SetchiBashoAjax(this, event);"
												readonly="#{pc_Kab00804T01.propSetchiBashoCd.readonly}"></h:inputText>
											<hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchSetchiBashoCd"
												rendered="#{pc_Kab00804T01.propSearchSetchiBashoName.rendered}" action="#{pc_Kab00804T01.doSearchSetchiBashoCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText"
													id="htmlSetchiBashoName"
													style="#{pc_Kab00804T01.propSetchiBashoName.style}"
													value="#{pc_Kab00804T01.propSetchiBashoName.stringValue}"
													title="#{pc_Kab00804T01.propSetchiBashoName.stringValue}"></h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
											<h:outputText styleClass="outputText"
												id="lblKanriBmnCd"
												value="#{pc_Kab00804T01.propKanriBmnCd.labelName}"
												style="#{pc_Kab00804T01.propKanriBmnCd.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<DIV style="width:700px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlKanriBmnCd" size="10"
												value="#{pc_Kab00804T01.propKanriBmnCd.stringValue}"
												style="#{pc_Kab00804T01.propKanriBmnCd.style}"
												maxlength="#{pc_Kab00804T01.propKanriBmnCd.maxLength}"
												onblur="return func_KanriBmnAjax(this, event);"
												readonly="#{pc_Kab00804T01.propKanriBmnCd.readonly}"></h:inputText>
											<hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchKanriBmnCd"
												rendered="#{pc_Kab00804T01.propSearchKanriBmnName.rendered}" action="#{pc_Kab00804T01.doSearchKanriBmnCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlKanriBmnName"
													style="#{pc_Kab00804T01.propKanriBmnName.style}"
													value="#{pc_Kab00804T01.propKanriBmnName.stringValue}"
													title="#{pc_Kab00804T01.propKanriBmnName.stringValue}"></h:outputText>
											</DIV>		
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
											<h:outputText styleClass="outputText"
												id="lblKanrishaId"
												value="#{pc_Kab00804T01.propKanrishaId.labelName}"
												style="#{pc_Kab00804T01.propKanrishaId.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<DIV style="width:700px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlKanrishaId"
													size="20"
													value="#{pc_Kab00804T01.propKanrishaId.stringValue}"
													style="#{pc_Kab00804T01.propKanrishaId.style}"
													maxlength="#{pc_Kab00804T01.propKanrishaId.maxLength}"
													onblur="return func_KanrishaAjax(this, event);"
													readonly="#{pc_Kab00804T01.propKanrishaId.readonly}"></h:inputText>
											<hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchKanrishaId"
												rendered="#{pc_Kab00804T01.propSearchKanrishaName.rendered}" action="#{pc_Kab00804T01.doSearchKanrishaIdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlKanrishaName"
													style="#{pc_Kab00804T01.propKanrishaName.style}"
													value="#{pc_Kab00804T01.propKanrishaName.stringValue}"
													title="#{pc_Kab00804T01.propKanrishaName.stringValue}"></h:outputText>
											</DIV>		
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
											<h:outputText styleClass="outputText"
												id="lblChotatsuSu" value="調達数(半5)／単位"
												style="#{pc_Kab00804T01.propChotatsuSu.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
												<TBODY>
													<TR>
														<TD>
															<h:inputText styleClass="inputText" id="htmlChotatsuSu"
																size="5"
																value="#{pc_Kab00804T01.propChotatsuSu.stringValue}"
																style="padding-right: 3px; text-align: right;#{pc_Kab00804T01.propChotatsuSu.style}"
																maxlength="#{pc_Kab00804T01.propChotatsuSu.maxLength}"
																readonly="#{pc_Kab00804T01.propChotatsuSu.readonly}">
																<hx:inputHelperAssist errorClass="inputText_Error" />
															</h:inputText>
														</TD>
														<TD>
															<h:selectOneMenu styleClass="selectOneMenu"
																id="htmlTaniCd" value="#{pc_Kab00804T01.propTaniCd.value}"
																disabled="#{pc_Kab00804T01.propTaniCd.disabled}">
																<f:selectItems value="#{pc_Kab00804T01.propTaniCd.list}" />
															</h:selectOneMenu>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblKyokanKbn"
													style="#{pc_Kab00804T01.propKyokanKbn.labelStyle}"
													value="#{pc_Kab00804T01.propKyokanKbn.labelName}"></h:outputText></TH>
										<TD style="width:250px;">
										<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlKyokanKbn"
													disabled="#{pc_Kab00804T01.propKyokanKbn.disabled}"
													readonly="#{pc_Kab00804T01.propKyokanKbn.readonly}"
													rendered="#{pc_Kab00804T01.propKyokanKbn.rendered}"
													style="#{pc_Kab00804T01.propKyokanKbn.style}"
													value="#{pc_Kab00804T01.propKyokanKbn.stringValue}"
													onclick="return func_kyokanAnbunPtnSetDisabled(this, event)">
													<f:selectItems value="#{pc_Kab00804T01.propKyokanKbn.list}" />
												</h:selectOneRadio></TD>
										<TH class="v_a" style="width:140px;white-space:nowrap;overflow:hidden;display:block;">
										<h:outputText styleClass="outputText" id="lblKyokanAnbunPtn"
													style="#{pc_Kab00804T01.propKyokanAnbunPtn.labelStyle}"
													value="#{pc_Kab00804T01.propKyokanAnbunPtn.labelName}"></h:outputText></TH>
										<TD nowrap>
											<DIV style="width:280px;white-space:nowrap;overflow:hidden;display:block;">
												<h:inputText styleClass="inputText"
														id="htmlKyokanAnbunPtn" size="5"
														onblur="return func_KyokanAnbunAjax(this, event);"
														disabled="#{pc_Kab00804T01.propKyokanAnbunPtn.disabled}"
														maxlength="#{pc_Kab00804T01.propKyokanAnbunPtn.maxLength}"
														readonly="#{pc_Kab00804T01.propKyokanAnbunPtn.readonly}"
														rendered="#{pc_Kab00804T01.propKyokanAnbunPtn.rendered}"
														style="#{pc_Kab00804T01.propKyokanAnbunPtn.style}"
														value="#{pc_Kab00804T01.propKyokanAnbunPtn.stringValue}"></h:inputText>
												<hx:commandExButton styleClass="commandExButton_search"
														id="searchKyokanAnbubnPtn" type="submit"
														action="#{pc_Kab00804T01.doSearchKyokanAnbubnPtnAction}"
														disabled="#{pc_Kab00804T01.propKyokanAnbunPtn.disabled}">
												</hx:commandExButton><h:outputText styleClass="outputText"
													id="htmlKyokanAnbunPtnName"></h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblShisanTaisho"
													style="#{pc_Kab00804T01.propShisanTaisho.labelStyle}"
													value="#{pc_Kab00804T01.propShisanTaisho.labelName}"></h:outputText>
										</TH>
										<TD colspan="3">
											<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlShisanTaisho"
												disabled="#{pc_Kab00804T01.propShisanTaisho.disabled}"
												onclick="radioChangeSetDisabled('linkDisabledFlg')"
												readonly="#{pc_Kab00804T01.propShisanTaisho.readonly}"
												rendered="#{pc_Kab00804T01.propShisanTaisho.rendered}"
												style="#{pc_Kab00804T01.propShisanTaisho.style}"
												value="#{pc_Kab00804T01.propShisanTaisho.stringValue}">
												<f:selectItems
													value="#{pc_Kab00804T01.propShisanTaisho.list}" />
											</h:selectOneRadio>
											<h:commandLink 
													styleClass="commandLink" 
													id="linkDisabledFlg" 
													style="visibility:hidden"
													action="#{pc_Kab00804T01.doTabDisabled}">
												<h:outputText id="htmlLinkDisabledFlg" styleClass="outputText">
												</h:outputText>
											</h:commandLink>
										</TD>
									</TR>
									<TR>
										<TH class="v_d">
											<h:outputText
												styleClass="outputText" id="lblShutokuDate"
												value="#{pc_Kab00804T01.propShutokuDate.labelName}"
												style="#{pc_Kab00804T01.propShutokuDate.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText styleClass="inputText" id="htmlShutokuDate"
													size="10"
													value="#{pc_Kab00804T01.propShutokuDate.dateValue}"
													style="#{pc_Kab00804T01.propShutokuDate.style}"
													readonly="#{pc_Kab00804T01.propShutokuDate.readonly}">
													<f:convertDateTime pattern="yyyy/MM/dd" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<hx:inputHelperDatePicker />
												</h:inputText>
										</TD>
									</TR>
									<TR>										
										<TH class="v_d" width="140">
											<h:outputText
												styleClass="outputText" id="lblHenkyakuDate"
												value="#{pc_Kab00804T01.propHenkyakuDate.labelName}"
												style="#{pc_Kab00804T01.propHenkyakuDate.labelStyle}"></h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText styleClass="inputText"
												id="htmlHenkyakuDate" size="10"
												value="#{pc_Kab00804T01.propHenkyakuDate.dateValue}"
												style="#{pc_Kab00804T01.propHenkyakuDate.style}"
												readonly="#{pc_Kab00804T01.propHenkyakuDate.readonly}">
													<f:convertDateTime pattern="yyyy/MM/dd" />
													<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE width="900" border="0" style="" class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton type="submit" value="登録"
						styleClass="commandExButton_dat" id="register"
						action="#{pc_Kab00804T01.doRegisterAction}"
						confirm="#{msg.SY_MSG_0002W}"
						disabled="#{pc_Kab00804T01.kab00804.propRegister.disabled}"
						rendered="#{pc_Kab00804T01.kab00804.propRegister.rendered}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" value="更新"
						styleClass="commandExButton_dat" id="update"
						confirm="#{msg.SY_MSG_0003W}"
						action="#{pc_Kab00804T01.doUpdateAction}"
						rendered="#{pc_Kab00804T01.kab00804.propUpdate.rendered}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" value="削除"
						styleClass="commandExButton_dat" id="delete"
						confirm="#{msg.SY_MSG_0004W}"
						action="#{pc_Kab00804T01.doDeleteAction}"
						rendered="#{pc_Kab00804T01.kab00804.propDelete.rendered}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" value="クリア"
						styleClass="commandExButton_etc" id="clear"
						action="#{pc_Kab00804T01.doClearAction}"
						rendered="#{pc_Kab00804T01.kab00804.propClear.rendered}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" value="関連情報"
							styleClass="commandExButton_etc" id="kanrenInfo"
							action="#{pc_Kab00804T01.doKanrenInfoAction}">
						</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>

<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--></DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../../rev/inc/footer.jsp" />
<h:inputHidden
	value="#{pc_Kab00804T01.kab00804.propExecutableDelete.stringValue}"
	id="htmlExecutableDelete">
</h:inputHidden>
<h:inputHidden value="htmlChotatsuSu=##,##0;##,##0"
	id="htmlFormatNumberOption">
</h:inputHidden>
<h:inputHidden id="htmljokyakuDateForAjax" 
	value="#{pc_Kab00804T01.kab00804.jokyakuDateForAjax}"></h:inputHidden>
<h:inputHidden
	value="#{pc_Kab00804T01.kab00804.propLeaseEndDateHidden.dateValue}"
	id="htmlLeaseEndDateHidden">
	<f:convertDateTime />
</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab00804T01.kab00804.propLeaseSisnList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

