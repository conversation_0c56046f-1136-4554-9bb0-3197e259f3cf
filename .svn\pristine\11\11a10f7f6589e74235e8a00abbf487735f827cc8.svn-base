<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KAB_SISN" name="固定資産" prod_id="KA" description="固定資産情報を保持します。">
<STATMENT><![CDATA[
KAB_SISN
]]></STATMENT>
<COLUMN id="SSN_KBN" name="資産区分" type="string" length="2" lengthDP="0" byteLength="2" description="資産区分をシステム上で識別します。"><CODE><CASE value="01" display="土地"/><CASE value="02" display="建物"/><CASE value="03" display="附帯設備"/><CASE value="04" display="構築物"/><CASE value="05" display="図書"/><CASE value="06" display="車両"/><CASE value="07" display="無形固定資産"/><CASE value="08" display="備品"/><CASE value="09" display="リース資産"/><CASE value="10" display="その他"/></CODE></COLUMN><COLUMN id="SSN_NO" name="資産番号" type="string" length="20" lengthDP="0" byteLength="20" description="固定資産番号が設定されます。"/><COLUMN id="SHUTOKU_NENDO" name="取得年度" type="number" length="4" lengthDP="0" byteLength="0" description="取得年度が設定されます。"/><COLUMN id="SHUTOKU_DATE" name="取得日付" type="date" length="0" lengthDP="0" byteLength="0" description="取得日付が設定されます。"/><COLUMN id="SSN_BNR_CD" name="資産分類コード" type="string" length="5" lengthDP="0" byteLength="5" description="資産分類のコードが設定されます。"/><COLUMN id="SSN_NAME" name="資産名称" type="string" length="100" lengthDP="0" byteLength="300" description="商品名もしくは資産名や契約物件名が設定されます。"/><COLUMN id="SSN_NAME_RYAK" name="資産名称＿略称" type="string" length="20" lengthDP="0" byteLength="60" description="商品名もしくは資産名や契約物件名の略称が設定されます。"/><COLUMN id="ZOKA_RIYU_CD" name="資産増加理由コード" type="string" length="5" lengthDP="0" byteLength="5" description="資産増加理由コードが設定されます。"/><COLUMN id="SETCHI_BASHO_CD" name="設置場所コード" type="string" length="10" lengthDP="0" byteLength="10" description="設置場所のコードが設定されます。"/><COLUMN id="KANRI_BMN_CD" name="管理部門コード" type="string" length="10" lengthDP="0" byteLength="10" description="管理部門の予算単位コードが設定されます。"/><COLUMN id="KANRISHA_ID" name="管理者ＩＤ" type="string" length="20" lengthDP="0" byteLength="20" description="資産の管理者IDが設定されます。"/><COLUMN id="CHOTATSU_SU" name="調達数" type="number" length="5" lengthDP="0" byteLength="0" description="調達数が設定されます。"/><COLUMN id="TANI_CD" name="単位コード" type="string" length="2" lengthDP="0" byteLength="2" description="単位コードが設定されます。"/><COLUMN id="SHOZAICHI" name="所在地" type="string" length="100" lengthDP="0" byteLength="300" description="住所などの所在地情報が設定されます。"/><COLUMN id="CHIBAN" name="地番" type="string" length="10" lengthDP="0" byteLength="30" description="地番が設定されます。"/><COLUMN id="CMK_CD" name="地目コード" type="string" length="2" lengthDP="0" byteLength="2" description="地目コードが設定されます。"/><COLUMN id="KAISU_CHIJO" name="階数＿地上" type="number" length="2" lengthDP="0" byteLength="0" description="階数が設定されます。"/><COLUMN id="KAISU_CHIKA" name="階数＿地下" type="number" length="2" lengthDP="0" byteLength="0" description="階数が設定されます。"/><COLUMN id="KAOKU_NO" name="家屋番号" type="string" length="20" lengthDP="0" byteLength="60" description="家屋番号が設定されます。"/><COLUMN id="KENCHIKU_KAKUNIN_NO" name="建築確認番号" type="string" length="20" lengthDP="0" byteLength="60" description="建築確認番号が設定されます。"/><COLUMN id="TOKI_DATE" name="登記日付" type="date" length="0" lengthDP="0" byteLength="0" description="登記日が設定されます。"/><COLUMN id="TOKIBO_NO" name="登記簿番号" type="string" length="20" lengthDP="0" byteLength="60" description="登記簿番号が設定されます。"/><COLUMN id="TOKI_MENSEKI" name="登記面積" type="number" length="10" lengthDP="2" byteLength="0" description="登記面積が設定されます。"/><COLUMN id="JISSOKU_MENSEKI" name="実測面積" type="number" length="10" lengthDP="2" byteLength="0" description="実測面積が設定されます。"/><COLUMN id="MAKER_NAME" name="メーカー名称" type="string" length="100" lengthDP="0" byteLength="300" description="メーカー名称が設定されます。"/><COLUMN id="SHARYO_NAME" name="車両名称" type="string" length="60" lengthDP="0" byteLength="180" description="車両名称が設定されます。"/><COLUMN id="KATA_NO" name="型番" type="string" length="50" lengthDP="0" byteLength="150" description="型番が設定されます。"/><COLUMN id="SHARYO_NO" name="車両番号標" type="string" length="40" lengthDP="0" byteLength="120" description="車両番号標が設定されます。"/><COLUMN id="SHARYO_TOROKU_DATE" name="車両登録日付" type="date" length="0" lengthDP="0" byteLength="0" description="車両登録日付が設定されます。"/><COLUMN id="HOKENSHO_NO" name="保険証番号" type="string" length="40" lengthDP="0" byteLength="120" description="保険証番号が設定されます。"/><COLUMN id="TEIIN_SU" name="定員数" type="number" length="4" lengthDP="0" byteLength="0" description="定員数が設定されます。"/><COLUMN id="JURYO" name="重量" type="number" length="8" lengthDP="0" byteLength="0" description="重量が設定されます。"/><COLUMN id="SO_HAIKI_RYO" name="総排気量" type="number" length="5" lengthDP="0" byteLength="0" description="総排気量が設定されます。"/><COLUMN id="MAX_SEKISAI_RYO" name="最大積載量" type="number" length="8" lengthDP="0" byteLength="0" description="最大積載量が設定されます。"/><COLUMN id="SHUTOKUSAKI_CD" name="取得先コード" type="string" length="20" lengthDP="0" byteLength="20" description="取引先コード（リースの場合は契約先コード）が設定されます。"/><COLUMN id="SHUTOKUSAKI_NAME" name="取得先名称" type="string" length="60" lengthDP="0" byteLength="180" description="取引先名称（リースの場合は契約先名称）が設定されます。"/><COLUMN id="CHUKAISAKI_CD" name="仲介先コード" type="string" length="20" lengthDP="0" byteLength="20" description="仲介先として業者コードが設定されます。"/><COLUMN id="CHUKAISAKI_NAME" name="仲介先名称" type="string" length="60" lengthDP="0" byteLength="180" description="仲介先の名称が設定されます。"/><COLUMN id="SEKKEISHA_CD" name="設計者コード" type="string" length="20" lengthDP="0" byteLength="20" description="設計者として業者コードが設定されます。|資産区分が６の場合は保険会社コードが設定されます。"/><COLUMN id="SEKKEISHA_NAME" name="設計者名称" type="string" length="60" lengthDP="0" byteLength="180" description="設計者の名称が設定されます。|資産区分が６の場合は保険会社名称が設定されます。"/><COLUMN id="KEIYAKU_DATE" name="契約日付" type="date" length="0" lengthDP="0" byteLength="0" description="契約日（発注日）が設定されます。"/><COLUMN id="KEIYAKU_NO" name="契約番号" type="string" length="40" lengthDP="0" byteLength="120" description="契約番号が設定されます。"/><COLUMN id="SSN_TAISHO_FLG" name="資産対象フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="資産対象とするかが設定されます。|資産対象外の場合は各種学外帳票（財産目録など）に出力されません。"><CODE><CASE value="0" display="対象外"/><CASE value="1" display="対象"/></CODE></COLUMN><COLUMN id="KYOKAN_KBN" name="教管区分" type="string" length="1" lengthDP="0" byteLength="1" description="教管区分が設定されます。|"><CODE><CASE value="1" display="教育研究"/><CASE value="2" display="管理"/><CASE value="3" display="共通"/></CODE></COLUMN><COLUMN id="KYOKAN_ANB_PTN" name="教管按分パターン" type="string" length="8" lengthDP="0" byteLength="8" description="教管按分パターンが設定されます。"/><COLUMN id="BMN_ANB_PTN_KYOKEN" name="部門按分パターン＿教研" type="string" length="8" lengthDP="0" byteLength="8" description="部門按分パターン（教育研究）が設定されます。"/><COLUMN id="BMN_ANB_PTN_KANRI" name="部門按分パターン＿管理" type="string" length="8" lengthDP="0" byteLength="8" description="部門按分パターン（管理）が設定されます。"/><COLUMN id="BMN_ANB_PTN_KYOTU" name="部門按分パターン＿共通" type="string" length="8" lengthDP="0" byteLength="8" description="部門按分パターン（共通）が設定されます。"/><COLUMN id="TEITO_FLG" name="抵当権フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="抵当状況が設定されます。"><CODE><CASE value="0" display="抵当権なし"/><CASE value="1" display="抵当権あり"/></CODE></COLUMN><COLUMN id="SHOYUKEN_KBN" name="所有権区分" type="string" length="1" lengthDP="0" byteLength="1" description="所有資産か借用資産かが設定されます。"><CODE><CASE value="1" display="所有資産"/><CASE value="2" display="借用資産"/></CODE></COLUMN><COLUMN id="SHUTOKU_KAGAKU" name="取得価額" type="number" length="13" lengthDP="0" byteLength="0" description="取得価額（リースの場合は物件価格）が設定されます。"/><COLUMN id="TYOBO_KAGAKU" name="帳簿価額" type="number" length="13" lengthDP="0" byteLength="0" description="償却後の帳簿価額が設定されます。"/><COLUMN id="SHOKYAKU_GAKU" name="償却額" type="number" length="13" lengthDP="0" byteLength="0" description="１回の償却額が設定されます。"/><COLUMN id="SHOKYAKU_RUIKEI_GAKU" name="償却累計額" type="number" length="13" lengthDP="0" byteLength="0" description="償却額の累計金額が設定されます。"/><COLUMN id="TONENDO_SHOKYAKU_GAKU" name="当年度償却額" type="number" length="13" lengthDP="0" byteLength="0" description="償却額の当年度金額が設定されます。"/><COLUMN id="SHOKYAKU_START_NENDO" name="償却開始年度" type="number" length="4" lengthDP="0" byteLength="0" description="減価償却開始年度が設定されます。ＮＵＬＬの場合、減価償却対象外となります。"/><COLUMN id="SHOKYAKU_KAISU" name="償却回数" type="number" length="3" lengthDP="0" byteLength="0" description="過去の償却回数が設定されます。"/><COLUMN id="KIHONKIN_TAISHO_FLG" name="基本金組入対象フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="基本金組入の対象かを識別する為のフラグが設定されます。"><CODE><CASE value="0" display="対象外"/><CASE value="1" display="対象"/></CODE></COLUMN><COLUMN id="KIHONKIN_KUMIIRE_GAKU" name="基本金組入額" type="number" length="13" lengthDP="0" byteLength="0" description="基本金組入額が設定されます。"/><COLUMN id="HYOKA_GAKU" name="評価額" type="number" length="13" lengthDP="0" byteLength="0" description="評価額が設定されます。"/><COLUMN id="HOTEI_TAIYO_NENSU_CD" name="法定耐用年数コード" type="string" length="5" lengthDP="0" byteLength="5" description="法定耐用年数コードが設定されます。"/><COLUMN id="TAIYO_NENSU" name="耐用年数" type="number" length="2" lengthDP="0" byteLength="0" description="耐用年数が設定されます。"/><COLUMN id="KEIJO_YSN_T_CD" name="計上予算単位コード" type="string" length="10" lengthDP="0" byteLength="10" description="計上予算単位コードが設定されます。"/><COLUMN id="HAKKO_NENDO" name="発行年度" type="number" length="4" lengthDP="0" byteLength="0" description="研究課題の発行年度が設定されます。"/><COLUMN id="KENKYU_KADAI_NO" name="研究課題番号" type="string" length="10" lengthDP="0" byteLength="10" description="研究課題の番号が設定されます。"/><COLUMN id="KENKYU_KADAI_KBN" name="研究課題区分" type="string" length="1" lengthDP="0" byteLength="1" description="研究課題の区分が設定されます。"><CODE><CASE value="1" display="研究課題"/><CASE value="2" display="間接経費"/><CASE value="3" display="オーバーヘッド"/></CODE></COLUMN><COLUMN id="KENKYUSHA_CD" name="研究者コード" type="string" length="20" lengthDP="0" byteLength="20" description="研究者の人事コードが設定されます。"/><COLUMN id="UCHI_MEISAI_CD" name="内訳明細コード" type="string" length="9" lengthDP="0" byteLength="9" description="内訳明細のコードが設定されます。"/><COLUMN id="SHUTOKU_RINGI_NO" name="取得稟議番号" type="string" length="40" lengthDP="0" byteLength="120" description="取得稟議番号が設定されます。"/><COLUMN id="SHUTOKU_DENPYO_KANRI_NO" name="取得伝票管理番号" type="string" length="25" lengthDP="0" byteLength="25" description="取得伝票の管理番号です。採番方法に従って採番されます。連番までを含みます。"/><COLUMN id="JOKYAKU_RINGI_NO" name="除却稟議番号" type="string" length="40" lengthDP="0" byteLength="120" description="除却稟議番号が設定されます。"/><COLUMN id="JOKYAKU_TAISHO_FLG" name="除却フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="除却状態が設定されます。"><CODE><CASE value="0" display="未除却"/><CASE value="1" display="除却"/></CODE></COLUMN><COLUMN id="JOKYAKU_NENDO" name="除却年度" type="number" length="4" lengthDP="0" byteLength="0" description="除却された年度が設定されます。この年度と保有年数を参照し、年次更新処理にて削除されます。"/><COLUMN id="JOKYAKU_DATE" name="除却日付" type="date" length="0" lengthDP="0" byteLength="0" description="除却された日付が設定されます。"/><COLUMN id="JOKYAKU_SIWAKE_SAKUSEI_FLG" name="除却仕訳作成フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="除却仕訳データの作成状態を保持します。"><CODE><CASE value="0" display="未作成"/><CASE value="1" display="作成済"/></CODE></COLUMN><COLUMN id="JOKYAKU_DENPYO_KANRI_NO" name="除却伝票管理番号" type="string" length="25" lengthDP="0" byteLength="25" description="除却伝票の管理番号です。採番方法に従って採番されます。連番までを含みます。"/><COLUMN id="HAIKI_FLG" name="廃棄フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="廃棄状態が設定されます。"><CODE><CASE value="0" display="未廃棄"/><CASE value="1" display="廃棄"/></CODE></COLUMN><COLUMN id="HAIKI_DATE" name="廃棄日付" type="date" length="0" lengthDP="0" byteLength="0" description="廃棄された日付が設定されます。"/><COLUMN id="BIKO_SHUTOKU" name="備考＿取得" type="string" length="200" lengthDP="0" byteLength="600" description="取得時の備考が設定されます。"/><COLUMN id="BIKO_JOKYAKU" name="備考＿除却" type="string" length="200" lengthDP="0" byteLength="600" description="除却時の備考が設定されます。"/><COLUMN id="BIKO_HAIKI" name="備考＿廃棄" type="string" length="200" lengthDP="0" byteLength="600" description="廃棄時の備考が設定されます。"/><COLUMN id="CHOT_NENDO" name="調達年度" type="number" length="4" lengthDP="0" byteLength="0" description="調達年度が設定されます。調達からの移行時にシステムで設定されます。"/><COLUMN id="CHOT_NO" name="調達番号" type="string" length="20" lengthDP="0" byteLength="20" description="調達番号が設定されます。調達からの移行時にシステムで設定されます。"/><COLUMN id="MEISAI_NO" name="調達明細番号" type="number" length="3" lengthDP="0" byteLength="0" description="調達明細番号が設定されます。調達からの移行時にシステムで設定されます。"/><COLUMN id="GAZOU_FILE" name="画像ファイル" type="string" length="50" lengthDP="0" byteLength="50" description="画像ファイルの名称が設定されます。"/>
</TABLE>
