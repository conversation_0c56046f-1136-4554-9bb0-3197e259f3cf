<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_NSH_KTOI_HSY" name="過年度問合せ者保証人" prod_id="NS" description="過去の問合せ者の保証人情報を蓄積して持ちます。過年度問合せ者・保証人は『入試期末処理』にて問合せ者・保証人から転記されます。|">
<STATMENT><![CDATA[
NSH_KTOI_HSY
]]></STATMENT>
<COLUMN id="TOI_CD" name="問合せ者番号" type="string" length="10" lengthDP="0" byteLength="10" description="問合せ者の管理番号です。"/><COLUMN id="HSY_NO" name="保証人ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="保証人種別を識別する任意の番号です。"/><COLUMN id="HSY_SBT_NAME" name="保証人種別名称" type="string" length="14" lengthDP="0" byteLength="42" description="保証人の呼称です。"/><COLUMN id="HSY_NAME_KANA" name="保証人氏名＿カナ" type="string" length="80" lengthDP="0" byteLength="240" description="保証人氏名の読み仮名です。"/><COLUMN id="HSY_NAME" name="保証人氏名" type="string" length="40" lengthDP="0" byteLength="120" description="保証人の氏名が設定されます。"/><COLUMN id="ZOKUGARA_CD" name="保証人続柄コード" type="string" length="2" lengthDP="0" byteLength="2" description="保証人続柄を識別するコードです。"/><COLUMN id="HSY_ADDR_NO" name="保証人郵便番号" type="string" length="7" lengthDP="0" byteLength="7" description="保証人の郵便番号です。"/><COLUMN id="HSY_ADDR1" name="保証人住所１" type="string" length="50" lengthDP="0" byteLength="150" description="保証人の住所１です。"/><COLUMN id="HSY_ADDR2" name="保証人住所２" type="string" length="50" lengthDP="0" byteLength="150" description="保証人の住所２です。"/><COLUMN id="HSY_ADDR3" name="保証人住所３" type="string" length="50" lengthDP="0" byteLength="150" description="保証人の住所３です。"/><COLUMN id="HSY_ADDR_KANA1" name="保証人住所＿カナ１" type="string" length="100" lengthDP="0" byteLength="300" description="保証人のカナ住所１を表します。"/><COLUMN id="HSY_ADDR_KANA2" name="保証人住所＿カナ２" type="string" length="100" lengthDP="0" byteLength="300" description="保証人のカナ住所２を表します。"/><COLUMN id="HSY_ADDR_KANA3" name="保証人住所＿カナ３" type="string" length="100" lengthDP="0" byteLength="300" description="保証人のカナ住所３を表します。"/><COLUMN id="HSY_TEL" name="保証人電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="保証人の電話番号です。"/><COLUMN id="HSY_KEITAI_TEL" name="保証人携帯電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="保証人の携帯電話番号です。"/><COLUMN id="HSY_FAX" name="保証人ＦＡＸ番号" type="string" length="25" lengthDP="0" byteLength="25" description="保証人のＦＡＸ番号です。"/><COLUMN id="HSY_E_MAIL" name="保証人E_MAILアドレス" type="string" length="60" lengthDP="0" byteLength="60" description="保証人のE-MAILアドレスが設定されます。"/><COLUMN id="RENRAKU" name="連絡先" type="string" length="100" lengthDP="0" byteLength="300" description="本人以外の連絡先住所です。"/><COLUMN id="RENRAKU_TEL" name="連絡先電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="本人以外の連絡先電話番号です。"/>
</TABLE>
