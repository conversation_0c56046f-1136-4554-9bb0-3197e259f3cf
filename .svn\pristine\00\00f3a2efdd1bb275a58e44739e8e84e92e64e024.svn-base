<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssa00401T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Sstmp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css" title="Style">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<!--
<BODY onLoad="resizeTo('1024', '768');">
-->
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssa00401T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<DIV style="display:none;">
	<hx:commandExButton
		type="submit"
		value="閉じる"
		styleClass="commandExButton"
		id="closeDisp"
		action="#{pc_Ssa00401T01.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText
		styleClass="outputText"
		id="htmlFuncId"
		value="#{pc_Ssa00401T01.funcId}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlScrnName"
		value="#{pc_Ssa00401T01.screenName}">
	</h:outputText>
</DIV>

<!--↓outer↓-->
<DIV class="outer">
	<FIELDSET class="fieldset_err">
		<LEGEND>エラーメッセージ</LEGEND>
		<h:outputText
			id="message"
			value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			styleClass="outputText"
			escape="false">
		</h:outputText>
	</FIELDSET>
	
	<DIV class="head_button_area" >　
	<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	<!--↓content↓-->
	<DIV id="content">
		<!--↓column↓-->
		<DIV class="column">
		<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="850" border="0" cellpadding="0" cellspacing="0" class="table">
			<TBODY>
				<TR>
					<TH width="150" class="v_a">
						<h:outputText
							styleClass="outputText"
							id="lblKjnTsyNendo"
							style="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.labelStyle}"
							value="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText
							styleClass="inputText"
							id="htmlKjnTsyNendo"
							size="6"
							style="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.style}"
							value="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.dateValue}"
							disabled="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propKjnTsyNendo.readonly}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist
								errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText>
					<h:outputText styleClass="outputText" id="lblNendo" value="年度"></h:outputText></TD>
				</TR>
				<TR>
					<TH class="v_b">
						<h:outputText
							styleClass="outputText"
							id="lblChohyoTitle"
							value="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.labelName}"
							style="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText
							styleClass="inputText"
							id="htmlChohyoTitle"
							size="70"
							value="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.stringValue}"
							style="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.readonly}"
							maxlength="#{pc_Ssa00401T01.ssa00401.propChohyoTitle.maxLength}">
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH nowrap class="v_a"><h:outputText
						styleClass="outputText" id="lblOutput" value="出力データ"></h:outputText></TH>
					<TD>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlGaksekiInfo"
							value="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propGaksekiInfo.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblGaksekiInfo" value="学籍情報">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlMendanRireki"
							value="#{pc_Ssa00401T01.ssa00401.propMendanRireki.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propMendanRireki.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propMendanRireki.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propMendanRireki.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblMendanRireki" value="面談履歴">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlShushokuInfo"
							value="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propShushokuInfo.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblShushokuInfo" value="就職活動情報">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlProfileInfo"
							value="#{pc_Ssa00401T01.ssa00401.propProfileInfo.checked}"
							style="#{pc_Ssa00401T01.ssa00401.propProfileInfo.style}"
							disabled="#{pc_Ssa00401T01.ssa00401.propProfileInfo.disabled}"
							readonly="#{pc_Ssa00401T01.ssa00401.propProfileInfo.readonly}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblProfileInfo" value="プロファイル情報">
						</h:outputText>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
			<TABLE width="850" border="0" style="margin-top:20px;" cellspacing="0" cellpadding="0">
			<TBODY>
				<TR>
					<TD class="" align="left"><hx:commandExButton type="submit"
							value="一括指定"
							styleClass="tab_head_on"
							id="btnSsa00401T01"
							readonly="true" style="width:12%"></hx:commandExButton><hx:commandExButton type="submit"
							value="学生指定"
							styleClass="tab_head_off"
							id="btnSsa00401T03"
							action="#{pc_Ssa00401T01.doBtnSsa00401T03Action}" 
							style="width:12%"></hx:commandExButton><hx:commandExButton type="submit"
							value="ゼミ教員指定"
							styleClass="tab_head_off"
							id="btnSsa00401T04"
							action="#{pc_Ssa00401T01.doBtnSsa00401T04Action}" 
							style="width:12%"></hx:commandExButton><hx:commandExButton type="submit"
							value="企業指定" styleClass="tab_head_off" id="btnSsa00401T05"
							action="#{pc_Ssa00401T01.doBtnSsa00401T05Action}"
							style="width:12%"></hx:commandExButton></TD>
				</TR>
				<TR>
					<TD>
						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="tab_body">
							<TBODY>
								<TR>
									<TD align="left">
										<DIV style="height:395px">
											<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="table" style="margin-top:20px; margin-left:40px;">
												<TBODY>
													<TR>
														<TH width="150" class="v_a">
															<h:outputText styleClass="outputText" id="lblGakunen"
																	style="#{pc_Ssa00401T01.propGakunen.labelStyle}"
																	value="#{pc_Ssa00401T01.propGakunen.labelName}">
																</h:outputText>
														</TH>
														<TD>
															<h:selectOneMenu styleClass="selectOneMenu" id="htmlGakunen"
																	style="#{pc_Ssa00401T01.propGakunen.style}"
																	value="#{pc_Ssa00401T01.propGakunen.stringValue}"
																	disabled="#{pc_Ssa00401T01.propGakunen.disabled}"
																	readonly="#{pc_Ssa00401T01.propGakunen.readonly}">
																	<f:selectItems value="#{pc_Ssa00401T01.propGakunen.list}" />
																</h:selectOneMenu>
														</TD>
													</TR>
													<TR>
														<TH class="v_b">
															<h:outputText
																styleClass="outputText"
																id="lblSemester"
																style="#{pc_Ssa00401T01.propSemester.labelStyle}"
																value="#{pc_Ssa00401T01.propSemester.labelName}">
															</h:outputText>
														</TH>
														<TD>
															<h:selectOneMenu
																styleClass="selectOneMenu"
																id="htmlSemester"
																style="#{pc_Ssa00401T01.propSemester.style}"
																value="#{pc_Ssa00401T01.propSemester.stringValue}"
																disabled="#{pc_Ssa00401T01.propSemester.disabled}"
																readonly="#{pc_Ssa00401T01.propSemester.readonly}">
																<f:selectItems
																	value="#{pc_Ssa00401T01.propSemester.list}" />
															</h:selectOneMenu>
														</TD>
													</TR>
													<TR>
														<TH class="v_c">
															<h:outputText
																styleClass="outputText"
																id="lblListSgks"
																style="#{pc_Ssa00401T01.propListSgks.labelStyle}"
																value="#{pc_Ssa00401T01.propListSgks.labelName}">
															</h:outputText>
														</TH>
														<TD>
															<h:selectManyListbox
																styleClass="selectManyListbox"
																id="htmlListSgks" size="18" style="width:100%;"
																value="#{pc_Ssa00401T01.propListSgks.stringValue}"
																disabled="#{pc_Ssa00401T01.propListSgks.disabled}"
																readonly="#{pc_Ssa00401T01.propListSgks.readonly}">
																<f:selectItems
																	value="#{pc_Ssa00401T01.propListSgks.list}" />
															</h:selectManyListbox>
														</TD>
													</TR>
													<TR>
														<TH class="v_a">
															<h:outputText
																styleClass="outputText"
																id="lblSort"
																style="#{pc_Ssa00401T01.propSort.labelStyle}"
																value="#{pc_Ssa00401T01.propSort.labelName}">
															</h:outputText>
														</TH>
														<TD>
															<h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio"
																id="htmlSort"
																layout="lineDirection"
																style="#{pc_Ssa00401T01.propSort.style}"
																value="#{pc_Ssa00401T01.propSort.value}">
																<f:selectItem itemValue="1" itemLabel="学科組織順" />
																<f:selectItem itemValue="2" itemLabel="ゼミ教員順" />
															</h:selectOneRadio>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE width="100%" class="button_bar">
						<TBODY>
							<TR>
								<TD>
									<hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout"
										action="#{pc_Ssa00401T01.doPdfoutAction}"
										confirm="#{msg.SY_MSG_0019W}"
										></hx:commandExButton><hx:commandExButton 
										type="submit" value="EXCEL作成"
										styleClass="commandExButton_out" id="excelout"
										action="#{pc_Ssa00401T01.doExceloutAction}"
										confirm="#{msg.SY_MSG_0027W}"
										></hx:commandExButton><hx:commandExButton 
										type="submit" value="CSV作成"
										styleClass="commandExButton_out" id="csvout"
										action="#{pc_Ssa00401T01.doCsvoutAction}"
										confirm="#{msg.SY_MSG_0020W}"
										></hx:commandExButton><hx:commandExButton
										type="submit"
										value="出力項目指定"
										styleClass="commandExButton_out"
										id="setoutput"
										action="#{pc_Ssa00401T01.doSetoutputAction}"
										></hx:commandExButton></TD>
							</TR>
						</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
		<!-- ↑ここにコンポーネントを配置 -->
		</DIV>
		<!--↑column↑--> 
	</DIV>
	<!--↑content↑--> 
	<!-- フッダーインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
</DIV>
<!--↑outer↑-->
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
