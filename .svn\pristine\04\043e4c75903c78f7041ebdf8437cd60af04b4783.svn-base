<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Sse00101T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Sse00101T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css" />

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
setPage('htmlPage', 'htmlKgyList');
}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";	
	indirectClick('search');	
}			

function confirmCancel() {
	alert('実行を中断しました。');
}			


function func_2(thisObj, thisEvent) {
setPage('htmlPage', 'htmlKgyList');
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Sse00101T01.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">

	<!-- ヘッダーインクルード -->
	<jsp:include page ="../inc/header.jsp" />

	<!-- ヘッダーへのデータセット領域 -->
	<DIV style="display:none;">
		<hx:commandExButton type="submit" value="閉じる"
			styleClass="commandExButton" id="closeDisp"
			action="#{pc_Sse00101T01.doCloseDispAction}"></hx:commandExButton>
		<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Sse00101T01.funcId}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Sse00101T01.screenName}"></h:outputText>
	</DIV>

	<!--↓outer↓-->
	<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
		<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			styleClass="outputText" escape="false">
		</h:outputText>
	</FIELDSET>
			
	<!--↓content↓-->
	<DIV class="head_button_area" >
	<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトの問題の為に、全角スペースを配置 -->
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>

	<DIV id="content">
		<DIV class="column">
			<TABLE width="800" border="0" cellspacing="0" cellpadding="0" style="margin-top:20px;">
				<TBODY>
					<TR>
						<TD style="text-align:left;"><hx:commandExButton type="submit"
							value="求人指定登録" styleClass="tab_head_on" id="btnSse00101T01"></hx:commandExButton><hx:commandExButton
							type="submit" value="求人未指定登録" styleClass="tab_head_off"
							id="btnSse00101T02" action="#{pc_Sse00101T01.doBtnSse00101T02Action}" onclick="return func_2(this, event);"></hx:commandExButton></TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="100%" border="0" cellspacing="0" cellpadding="0" class="tab_body" style="height:500px;">
						<TBODY>
						<TR>
							<TD style="text-align:center;">
								<TABLE border="0" cellspacing="0" cellpadding="0" style="margin-top:20px;">
									<TBODY>
										<TR>
											<TD style="text-align:left;">
											<TABLE width="750" border="0" cellpadding="3" class="table" cellspacing="0">
												<TBODY>
													<TR>
														<TH class="v_a" width="20%"><h:outputText
															styleClass="outputText" id="lblKgyCd"
															value="#{pc_Sse00101T01.propKgyCd.labelName}"
															style="#{pc_Sse00101T01.propKgyCd.labelStyle}"></h:outputText></TH>
														<TD width="15%"><h:inputText styleClass="inputText"
															id="htmlKgyCd"
															value="#{pc_Sse00101T01.propKgyCd.stringValue}" size="10"
															disabled="#{pc_Sse00101T01.propKgyCd.disabled}"
															maxlength="#{pc_Sse00101T01.propKgyCd.maxLength}"
															readonly="#{pc_Sse00101T01.propKgyCd.readonly}"
															style="#{pc_Sse00101T01.propKgyCd.style}">
														</h:inputText></TD>
													</TR>
													<TR>
														<TH class="v_b" width="20%"><h:outputText
															styleClass="outputText" id="lblKgyName"
															value="#{pc_Sse00101T01.propKgyName.labelName}"
															style="#{pc_Sse00101T01.propKgyName.labelStyle}"></h:outputText></TH>
														<TD width="30%"><h:inputText styleClass="inputText"
															id="htmlKgyName"
															value="#{pc_Sse00101T01.propKgyName.stringValue}"
															size="30"
															disabled="#{pc_Sse00101T01.propKgyName.disabled}"
															maxlength="#{pc_Sse00101T01.propKgyName.maxLength}"
															readonly="#{pc_Sse00101T01.propKgyName.readonly}"
															style="#{pc_Sse00101T01.propKgyName.style}">
														</h:inputText></TD>
														<TH class="v_c" width="20%"><h:outputText
															styleClass="outputText" id="lblKgyNameKana"
															value="#{pc_Sse00101T01.propKgyNameKana.labelName}"
															style="#{pc_Sse00101T01.propKgyNameKana.labelStyle}"></h:outputText></TH>
														<TD width="30%"><h:inputText styleClass="inputText"
															id="htmlKgyNameKana"
															value="#{pc_Sse00101T01.propKgyNameKana.stringValue}"
															disabled="#{pc_Sse00101T01.propKgyNameKana.disabled}"
															maxlength="#{pc_Sse00101T01.propKgyNameKana.maxLength}"
															readonly="#{pc_Sse00101T01.propKgyNameKana.readonly}"
															style="#{pc_Sse00101T01.propKgyNameKana.style}" size="30">
														</h:inputText></TD>
													</TR>
													<TR>
														<TH class="v_d" width="20%"><h:outputText
															styleClass="outputText" id="lblKgyNameRyak"
															value="#{pc_Sse00101T01.propKgyNameRyak.labelName}"
															style="#{pc_Sse00101T01.propKgyNameRyak.labelStyle}"></h:outputText></TH>
														<TD width="30%"><h:inputText styleClass="inputText"
															id="htmlKgyNameRyak"
															value="#{pc_Sse00101T01.propKgyNameRyak.stringValue}"
															size="30"
															disabled="#{pc_Sse00101T01.propKgyNameRyak.disabled}"
															maxlength="#{pc_Sse00101T01.propKgyNameRyak.maxLength}"
															readonly="#{pc_Sse00101T01.propKgyNameRyak.readonly}"
															style="#{pc_Sse00101T01.propKgyNameRyak.style}">
														</h:inputText></TD>
													</TR>
													<TR>
														<TH class="v_e" width="20%"><h:outputText
															styleClass="outputText" id="lblGyosyuCd"
															value="#{pc_Sse00101T01.propGyosyuCd.labelName}"
															style="#{pc_Sse00101T01.propGyosyuCd.labelStyle}"></h:outputText></TH>
														<TD width="30%"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlGyosyuCd"
																value="#{pc_Sse00101T01.propGyosyuCd.stringValue}"
																readonly="#{pc_Sse00101T01.propGyosyuCd.readonly}"
																style="#{pc_Sse00101T01.propGyosyuCd.style}">
																<f:selectItems value="#{pc_Sse00101T01.propGyosyuCd.list}" />
															</h:selectOneMenu></TD>
														<TH class="v_f" width="20%"><h:outputText
															styleClass="outputText" id="lblChiikiCd"
															value="#{pc_Sse00101T01.propChiikiCd.labelName}"
															style="#{pc_Sse00101T01.propChiikiCd.labelStyle}"></h:outputText></TH>
														<TD width="30%"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlChiikiCd"
																value="#{pc_Sse00101T01.propChiikiCd.stringValue}"
																disabled="#{pc_Sse00101T01.propChiikiCd.disabled}"
																readonly="#{pc_Sse00101T01.propChiikiCd.readonly}"
																style="#{pc_Sse00101T01.propChiikiCd.style}">
																<f:selectItems value="#{pc_Sse00101T01.propChiikiCd.list}" />
															</h:selectOneMenu></TD>
													</TR>
													<TR>
														<TH class="v_g" width="20%"><h:outputText
															styleClass="outputText" id="lblSsenFlg"
															value="#{pc_Sse00101T01.propSsenFlg.labelName}"
															style="#{pc_Sse00101T01.propSsenFlg.labelStyle}"></h:outputText></TH>
														<TD width="30%"><h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlSsenFlg"
																value="#{pc_Sse00101T01.propSsenFlg.checked}"
																disabled="#{pc_Sse00101T01.propSsenFlg.disabled}"
																readonly="#{pc_Sse00101T01.propSsenFlg.readonly}"
																style="#{pc_Sse00101T01.propSsenFlg.style}">
															</h:selectBooleanCheckbox><h:outputText
																styleClass="outputText" id="textSsenFlg" value="推薦者のいる企業のみ"></h:outputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD style="text-align:left;">
											<TABLE width="750" class="button_bar">
												<TBODY>
													<TR>
														<TD><hx:commandExButton type="submit" value="検索"
																id="search" styleClass="commandExButton_dat" action="#{pc_Sse00101T01.doSearchAction}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD style="text-align:right;">
												<h:outputText styleClass="note" id="text1"
													value="※部分一致　企業コードを除く"></h:outputText>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								<TABLE border="0" cellspacing="0" cellpadding="0">
									<TBODY>
										<TR>
											<TD style="text-align:left;">
											<TABLE width="750" border="0" cellspacing="3" cellpadding="0"
												class="">
												<TBODY>
													<TR>
														<TD style="text-align:right;"><h:outputText
																styleClass="outputText" id="htmlKgyListCnt"
																value="#{pc_Sse00101T01.propKgyList.listCount}">
																<f:convertNumber />
															</h:outputText>
														<h:outputText styleClass="outputText" id="lblKensuu"
															value="件"></h:outputText></TD>
													</TR>
													<TR>
														<TD>
															<h:dataTable border="0" cellpadding="3" cellspacing="0"
																columnClasses="columnCenter,,,columnCenter,,"
																headerClass="headerClass" footerClass="footerClass"
																rowClasses="#{pc_Sse00101T01.propKgyList.rowClasses}"
																styleClass="meisai_page" id="htmlKgyList" width="750"
																value="#{pc_Sse00101T01.propKgyList.list}" var="varlist"
																first="#{pc_Sse00101T01.propKgyList.first}"
																rows="#{pc_Sse00101T01.propKgyList.rows}">
																<h:column id="column1">
																	<f:facet name="header">
																		<h:outputText id="lblListKgyCd"
																			styleClass="outputText" value="企業コード"></h:outputText>
																	</f:facet>
																	<f:attribute value="100" name="width" />
																	<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																	<h:outputText styleClass="outputText"
																		id="htmlListKgyCd" value="#{varlist.kgyCd}"
																		rendered="#{varlist.rendered}"></h:outputText>
																</h:column>
																<h:column id="column2">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="企業名称"
																			id="lblListKgyName"></h:outputText>
																	</f:facet>
																	<f:attribute value="200" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlListKgyName"
																		value="#{varlist.kgyName.displayValue}"
																		rendered="#{varlist.rendered}"
																		title="#{varlist.kgyName.value}"></h:outputText>
																</h:column>
																<h:column id="column3">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="企業名称カナ"
																			id="lblListKgyNameKana"></h:outputText>
																	</f:facet>
																	<f:attribute value="230" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlListKgyNameKana"
																		value="#{varlist.kgyNameKana.displayValue}"
																		title="#{varlist.kgyNameKana.value}"
																		rendered="#{varlist.rendered}"></h:outputText>
																</h:column>
																<h:column id="column6">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="業種コード"
																			id="lblListGyosyuCd"></h:outputText>
																	</f:facet>
																	<f:attribute value="90" name="width" />
																	<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																	<h:outputText styleClass="outputText"
																		id="htmlListGyogyuCd" rendered="#{varlist.rendered}"
																		value="#{varlist.gyosyuCd}"></h:outputText>
																</h:column>
																<h:column id="column4">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="主業種"
																			id="lblListGyosyuName"></h:outputText>
																	</f:facet>
																	<f:attribute value="90" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlListGyogyuName"
																		value="#{varlist.gyosyuName.displayValue}"
																		title="#{varlist.gyosyuName.value}"></h:outputText>
																</h:column>
																<f:facet name="footer">
																	<hx:panelBox styleClass="panelBox" id="box1">
																		<hx:pagerDeluxe styleClass="pagerDeluxe" id="deluxe1" />
																		<hx:pagerGoto styleClass="pagerGoto" id="goto1" />
																	</hx:panelBox>
																</f:facet>
																<h:column id="column5">
																	<f:facet name="header">
																	</f:facet>
																	<f:attribute value="40" name="width" />
																	<hx:commandExButton type="submit" value="選択"
																		styleClass="commandExButton" id="select"
																		action="#{pc_Sse00101T01.doSelectAction}"
																		onclick="return func_1(this, event);"
																		rendered="#{varlist.rendered}"></hx:commandExButton>
																</h:column>
															</h:dataTable>

														</TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								
							</TD>
						</TR>
						</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
	</DIV>
	<!--↑outer↑-->
	<!-- フッダーインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
	</DIV>
			<h:inputHidden
				value="#{pc_Sse00101T01.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Sse00101T01.propKgyList.currentPage}"
				id="htmlPage">

			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
	</f:view>
</HTML>
