<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab01302.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>優先順位指定</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	self.close();
	return true;

}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kab01302.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kab01302.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kab01302.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kab01302.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			&#160;
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="380" style="margin-top: 20px;">
				<TBODY>
					<TR>
						<TH width="80" class="v_a">
						<CENTER>優先順位</CENTER>
						</TH>
						<TH width="200" class="v_b">
						<CENTER>項目</CENTER>
						</TH>
						<TH width="100" class="v_c">
						<CENTER>ソート順</CENTER>
						</TH>
					</TR >
					<TR>
						<TD>
						<CENTER>1</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlFirstOut"
							value="#{pc_Kab01302.propFirstOut.stringValue}"
							rendered="#{pc_Kab01302.propFirstOut.rendered}">
							<f:selectItems value="#{pc_Kab01302.propFirstOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER>
						<h:selectOneRadio 
									disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlFirstOutSort" 
									value="#{pc_Kab01302.propFirstOutSort.stringValue}" style="text-align: left"><f:selectItems 
									value="#{pc_Kab01302.propFirstOutSort.list}" />
								</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR >
						<TD>
						<CENTER>2</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlSecondOut"
							value="#{pc_Kab01302.propSecondOut.stringValue}">
							<f:selectItems value="#{pc_Kab01302.propSecondOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio 
									disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlSecondOutSort" 
									value="#{pc_Kab01302.propSecondOutSort.stringValue}" style="text-align: left"><f:selectItems 
									value="#{pc_Kab01302.propSecondOutSort.list}" />
								</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR >
						<TD>
						<CENTER>3</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlThirdOut"
							value="#{pc_Kab01302.propThirdOut.stringValue}">
							<f:selectItems value="#{pc_Kab01302.propThirdOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio 
									disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlThirdOutSort" 
									value="#{pc_Kab01302.propThirdOutSort.stringValue}" style="text-align: left"><f:selectItems 
									value="#{pc_Kab01302.propThirdOutSort.list}" />
								</h:selectOneRadio></CENTER>
						</TD>
					</TR>					
					<TR class="clear_border">
						<TD colspan="3" style="background-color:transparent;"></TD>
					</TR>
					<TR>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblKaiPageSitei" value="改頁指定"></h:outputText></TH>
						<TD colspan="2"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlKaiPageSitei"
							value="#{pc_Kab01302.propKaiPageSitei.stringValue}">
							<f:selectItems value="#{pc_Kab01302.propKaiPageSitei.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="380" class="button_bar">
				<TR>
					<TD>
						<hx:commandExButton
							type="submit"
							value="選択"
							styleClass="commandExButton_etc"
							id="select"
							action="#{pc_Kab01302.doSelectAction2}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="キャンセル"
							styleClass="commandExButton_etc"
							id="cancle"
							onclick="return func_1(this, event);">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<SCRIPT type="text/javascript">
				setMaxWidth(document.getElementById("form1:htmlFirstOut"), 300);
				setMaxWidth(document.getElementById("form1:htmlSecondOut"), 300);
				setMaxWidth(document.getElementById("form1:htmlThirdOut"), 300);
				setMaxWidth(document.getElementById("form1:htmlKaiPageSitei"), 300);
			</SCRIPT>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/childFooter.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

