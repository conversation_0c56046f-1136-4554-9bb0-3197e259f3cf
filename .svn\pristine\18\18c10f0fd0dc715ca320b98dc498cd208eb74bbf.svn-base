<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsa00101T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	var zipNo = document.getElementById("form1:htmlSgnAddrNo").value;
	zipNo = encodeURIComponent(zipNo);
	var add = document.getElementById("form1:htmlToiAddr1").value;
	add = encodeURIComponent(add);
	var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp"
		   +"?"
  		   +"zipNo=form1:htmlSgnAddrNo"
  		   +"&"
  		   +"zipNoValue="+zipNo
  		   +"&"
  		   +"jyusyoKanji=form1:htmlToiAddr1"
  		   +"&"
  		   +"jyusyoValue="+add
  		   +"&"
  		   +"jyusyoKana=form1:htmlToiAddrKana1";
	openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");
	return true;

}



function func_2(thisObj, thisEvent) {
	// Dummy ロストフォーカス処理
	// 検索画面の戻りの際、このscriptを呼び出す為
}
function func_3(thisObj, thisEvent) {
	openModalWindow("", "PNsa0101","<%=com.jast.gakuen.rev.ns.PNsa0101.getWindowOpenOption() %>");
	setTarget("PNsa0101"); 
	return true;

}

window.onload = function (){
	setZipMenu('form1:htmlSgnAddrNo','form1:htmlToiAddr1','form1:htmlToiAddrKana1');
}
function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1"
	indirectClick('exec');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutable').value = "0";
	return false;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsa00101T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsa00101T02.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsa00101T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsa00101T02.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>

						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD style="" rowspan="2" width="550">
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
										<TBODY>
											<TR>
												<TH style="" class="v_a" width="150"><h:outputText
													styleClass="outputText" id="lblToiCd"
													value="#{pc_Nsa00101T02.nsa00101.propToiCd.labelName}"
													style="#{pc_Nsa00101T02.nsa00101.propToiCd.labelStyle}"></h:outputText></TH>
												<TD width="400"><h:inputText styleClass="inputText"
													id="htmlToiCd" size="17"
													disabled="#{pc_Nsa00101T02.nsa00101.propToiCd.disabled}"
													maxlength="#{pc_Nsa00101T02.nsa00101.propToiCd.maxLength}"
													readonly="#{pc_Nsa00101T02.nsa00101.propToiCd.readonly}"
													style="#{pc_Nsa00101T02.nsa00101.propToiCd.style}"
													value="#{pc_Nsa00101T02.nsa00101.propToiCd.stringValue}" tabindex="1" onblur="return func_2(this, event);"></h:inputText><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchToi" onclick="return func_3(this, event);"
													action="#{pc_Nsa00101T02.doSearchToiAction}" tabindex="2"
													disabled="#{pc_Nsa00101T02.nsa00101.propSearchToi.disabled}"></hx:commandExButton></TD>
											</TR>
											<TR>
												<TH style="" class="v_b"><h:outputText
													styleClass="outputText" id="lblToiName"
													value="#{pc_Nsa00101T02.nsa00101.propToiName.labelName}"
													style="#{pc_Nsa00101T02.nsa00101.propToiCd.labelStyle}"></h:outputText></TH>
												<TD><h:inputText styleClass="inputText" id="htmlToiName"
													size="56"
													value="#{pc_Nsa00101T02.nsa00101.propToiName.stringValue}"
													disabled="#{pc_Nsa00101T02.nsa00101.propToiName.disabled}"
													maxlength="#{pc_Nsa00101T02.nsa00101.propToiName.maxLength}"
													readonly="#{pc_Nsa00101T02.nsa00101.propToiName.readonly}"
													style="#{pc_Nsa00101T02.nsa00101.propToiName.style}" tabindex="5"></h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD class="clear_border" rowspan="3" width="30"></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="選　択" styleClass="commandExButton" id="select"
										tabindex="3"
										disabled="#{pc_Nsa00101T02.nsa00101.propSelect.disabled}" action="#{pc_Nsa00101T02.doSelectAction}"></hx:commandExButton></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="解　除" styleClass="commandExButton" id="reset"
										disabled="#{pc_Nsa00101T02.nsa00101.propUnselect.disabled}" tabindex="4" action="#{pc_Nsa00101T02.doResetAction}"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD align="right"><hx:commandExButton type="submit"
										styleClass="commandExButton" id="first" value="|＜"
										disabled="#{pc_Nsa00101T02.nsa00101.propFirst.disabled}" tabindex="6" action="#{pc_Nsa00101T02.doFirstAction}"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton type="submit" value="＜"
										styleClass="commandExButton" id="prev" tabindex="7"
										disabled="#{pc_Nsa00101T02.nsa00101.propPrev.disabled}" action="#{pc_Nsa00101T02.doPrevAction}"></hx:commandExButton></TD>
									<TD align="left"><hx:commandExButton type="submit" value="＞"
										styleClass="commandExButton" id="next"
										disabled="#{pc_Nsa00101T02.nsa00101.propNext.disabled}" tabindex="8" action="#{pc_Nsa00101T02.doNextAction}"></hx:commandExButton></TD>
									<TD align="left"><hx:commandExButton type="submit" value="＞|"
										styleClass="commandExButton" id="last"
										disabled="#{pc_Nsa00101T02.nsa00101.propLast.disabled}"
										tabindex="9" action="#{pc_Nsa00101T02.doLastAction}"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD style="" width="600"></TD>
									<TD align="right" colspan="4"><hx:commandExButton type="submit"
										value="自由設定" styleClass="commandExButton" id="freeSetting"
										tabindex="10"
										disabled="#{pc_Nsa00101T02.nsa00101.propFreeSetting.disabled}" action="#{pc_Nsa00101T02.doFreeSettingAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>

						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" style="border-bottom-style: none;">
										<TBODY>
											<TR>
												<TD class="tab_head_off" width="149px"><hx:commandExButton
													type="submit" value="基本情報①" styleClass="tab_head_off"
													id="selectTab1" style="width: 100%"
													disabled="#{pc_Nsa00101T02.propSelectTab1.disabled}" action="#{pc_Nsa00101T02.doSelectTab1Action}" tabindex="23"></hx:commandExButton></TD>
												<TD class="tab_head_on" width="149px"><hx:commandExButton
													type="submit" value="基本情報②" styleClass="tab_head_on"
													id="selectTab2" style="width:100%"
													disabled="#{pc_Nsa00101T02.propSelectTab2.disabled}"
													tabindex="11"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="*"><hx:commandExButton
													type="submit" value="出身校等情報" styleClass="tab_head_off"
													id="selectTab3" style="width:100%"
													disabled="#{pc_Nsa00101T02.propSelectTab3.disabled}" action="#{pc_Nsa00101T02.doSelectTab3Action}" tabindex="24"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="149px"><hx:commandExButton
													type="submit" value="保証人情報" styleClass="tab_head_off"
													id="selectTab4" style="width:100%"
													disabled="#{pc_Nsa00101T02.propSelectTab4.disabled}" action="#{pc_Nsa00101T02.doSelectTab4Action}" tabindex="25"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="149px"><hx:commandExButton
													type="submit" value="募集管理" styleClass="tab_head_off"
													id="selectTab5" style="width:100%"
													disabled="#{pc_Nsa00101T02.propSelectTab5.disabled}" action="#{pc_Nsa00101T02.doSelectTab5Action}" tabindex="26"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" 
										class="tab_body" width="100%" height="300">
										<TBODY>
											<TR>
												<TD>
												<TABLE border="0" cellpadding="0" cellspacing="0" style=""
													class="table" width="98%">
													<TBODY>
														<TR>
															<TH class="v_c" width="150"><h:outputText
																styleClass="outputText" id="lblSgnAddrNo"
																value="郵便番号(半3-4)"></h:outputText></TH>
															<TD><h:inputText styleClass="inputText"
																id="htmlSgnAddrNo"
																value="#{pc_Nsa00101T02.propToiAddrCd.stringValue}"
																disabled="#{pc_Nsa00101T02.propToiAddrCd.disabled}"
																maxlength="8"
																readonly="#{pc_Nsa00101T02.propToiAddrCd.readonly}"
																style="#{pc_Nsa00101T02.propToiAddrCd.style}" size="10"
																tabindex="12"></h:inputText><hx:commandExButton
																type="button" styleClass="commandExButton_search"
																id="searchAddr" onclick="return func_1(this, event);"
																tabindex="14"></hx:commandExButton></TD>
														</TR>
														<TR>
															<TH class="v_d" width="150" rowspan="3"><h:outputText
																styleClass="outputText" id="lblSgnAddr" value="住所(全75)"></h:outputText></TH>
															<TD style="border-bottom-style:none;"><h:inputText
																styleClass="inputText" id="htmlToiAddr1" size="60"
																disabled="#{pc_Nsa00101T02.propToiAddr1.disabled}"
																maxlength="#{pc_Nsa00101T02.propToiAddr1.maxLength}"
																readonly="#{pc_Nsa00101T02.propToiAddr1.readonly}"
																style="#{pc_Nsa00101T02.propToiAddr1.style}"
																value="#{pc_Nsa00101T02.propToiAddr1.stringValue}"
																tabindex="13"></h:inputText><h:outputText
																styleClass="outputText" id="lblSgnAddr1"
																value="(都道府県市区町村大字)"></h:outputText></TD>
														</TR>
														<TR>
															<TD
																style="border-top-style:none;border-bottom-style:none;"><h:inputText
																styleClass="inputText" id="htmlToiAddr2" size="60"
																value="#{pc_Nsa00101T02.propToiAddr2.stringValue}"
																disabled="#{pc_Nsa00101T02.propToiAddr2.disabled}"
																maxlength="#{pc_Nsa00101T02.propToiAddr2.maxLength}"
																readonly="#{pc_Nsa00101T02.propToiAddr2.readonly}"
																style="#{pc_Nsa00101T02.propToiAddr2.style}" tabindex="15"></h:inputText><h:outputText
																styleClass="outputText" id="lblSgnAddr2"
																value="(丁目・字以下)"></h:outputText></TD>
														</TR>
														<TR>
															<TD style="border-top-style:none;"><h:inputText
																styleClass="inputText" id="htmlToiAddr3" size="60"
																disabled="#{pc_Nsa00101T02.propToiAddr3.disabled}"
																maxlength="#{pc_Nsa00101T02.propToiAddr3.maxLength}"
																readonly="#{pc_Nsa00101T02.propToiAddr3.readonly}"
																style="#{pc_Nsa00101T02.propToiAddr3.style}"
																value="#{pc_Nsa00101T02.propToiAddr3.stringValue}"
																tabindex="16"></h:inputText><h:outputText
																styleClass="outputText" id="lblSgnAddr3"
																value="(マンション/ビル名 号室)"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_e" width="150" rowspan="3"><h:outputText
																styleClass="outputText" id="lblSgnAddrKana" value="住所カナ(全150)"></h:outputText></TH>
															<TD style="border-bottom-style:none;"><h:inputText
																styleClass="inputText" id="htmlToiAddrKana1" size="80"
																disabled="#{pc_Nsa00101T02.propToiAddrKana1.disabled}"
																maxlength="#{pc_Nsa00101T02.propToiAddrKana1.maxLength}"
																readonly="#{pc_Nsa00101T02.propToiAddrKana1.readonly}"
																style="#{pc_Nsa00101T02.propToiAddrKana1.style}"
																value="#{pc_Nsa00101T02.propToiAddrKana1.stringValue}"
																tabindex="17"></h:inputText></TD>
														</TR>
														<TR>
															<TD
																style="border-top-style:none;border-bottom-style:none;"><h:inputText
																styleClass="inputText" id="htmlToiAddrKana2" size="80"
																disabled="#{pc_Nsa00101T02.propToiAddrKana2.disabled}"
																maxlength="#{pc_Nsa00101T02.propToiAddrKana2.maxLength}"
																readonly="#{pc_Nsa00101T02.propToiAddrKana2.readonly}"
																style="#{pc_Nsa00101T02.propToiAddrKana2.style}"
																value="#{pc_Nsa00101T02.propToiAddrKana2.stringValue}"
																tabindex="18"></h:inputText></TD>
														</TR>
														<TR>
															<TD style="border-top-style:none;"><h:inputText
																styleClass="inputText" id="htmlToiAddrKana3" size="80"
																disabled="#{pc_Nsa00101T02.propToiAddrKana3.disabled}"
																maxlength="#{pc_Nsa00101T02.propToiAddrKana3.maxLength}"
																readonly="#{pc_Nsa00101T02.propToiAddrKana3.readonly}"
																style="#{pc_Nsa00101T02.propToiAddrKana3.style}"
																value="#{pc_Nsa00101T02.propToiAddrKana3.stringValue}"
																tabindex="19"></h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_f" width="150"><h:outputText
																styleClass="outputText" id="lblSgnEMail" value="#{pc_Nsa00101T02.propToiEMail.labelName}"></h:outputText></TH>
															<TD><h:inputText styleClass="inputText"
																id="htmlSgnEMail" size="80"
																disabled="#{pc_Nsa00101T02.propToiEMail.disabled}"
																maxlength="#{pc_Nsa00101T02.propToiEMail.maxLength}"
																readonly="#{pc_Nsa00101T02.propToiEMail.readonly}"
																style="#{pc_Nsa00101T02.propToiEMail.style}"
																value="#{pc_Nsa00101T02.propToiEMail.stringValue}"
																tabindex="20"></h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_g" width="150"><h:outputText
																styleClass="outputText" id="lblRenraku" value="#{pc_Nsa00101T02.propRenraku.labelName}"></h:outputText></TH>
															<TD width="200"><h:inputText styleClass="inputText"
																id="htmlRenraku" size="80"
																disabled="#{pc_Nsa00101T02.propRenraku.disabled}"
																maxlength="#{pc_Nsa00101T02.propRenraku.maxLength}"
																readonly="#{pc_Nsa00101T02.propRenraku.readonly}"
																style="#{pc_Nsa00101T02.propRenraku.style}"
																value="#{pc_Nsa00101T02.propRenraku.stringValue}"
																tabindex="21"></h:inputText></TD>
														</TR>
														<TR>
															<TH width="150" class="v_a"><h:outputText
																styleClass="outputText" id="lblRenrakuTel" value="#{pc_Nsa00101T02.propRenrakuTel.labelName}"></h:outputText></TH>
															<TD><h:inputText styleClass="inputText"
																id="htmlRenrakuTel" size="30"
																value="#{pc_Nsa00101T02.propRenrakuTel.stringValue}"
																disabled="#{pc_Nsa00101T02.propRenrakuTel.disabled}"
																maxlength="#{pc_Nsa00101T02.propRenrakuTel.maxLength}"
																readonly="#{pc_Nsa00101T02.propRenrakuTel.readonly}"
																style="#{pc_Nsa00101T02.propRenrakuTel.style}"
																tabindex="22"></h:inputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar" cellspacing="0" cellpadding="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="exec"
										disabled="#{pc_Nsa00101T02.nsa00101.propExec.disabled}"
										tabindex="27" confirm="#{msg.SY_MSG_0003W}"
										action="#{pc_Nsa00101T02.doExecAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete"
										disabled="#{pc_Nsa00101T02.nsa00101.propDelete.disabled}"
										tabindex="28" confirm="#{msg.SY_MSG_0004W}"
										action="#{pc_Nsa00101T02.doDeleteAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

