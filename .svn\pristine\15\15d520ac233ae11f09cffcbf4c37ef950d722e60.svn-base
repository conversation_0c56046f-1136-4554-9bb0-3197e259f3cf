<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kae00103T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>建設仮勘定登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function confirmOk() {

	document.getElementById('form1:htmlConfirmFlg').value = "1";
	indirectClick(document.getElementById('form1:htmlConfirmButtonId').value);

}

function confirmCancel() {
	document.getElementById('form1:htmlConfirmFlg').value = "0";
}			

function func_KanriBmnCd(thisObj, thisEvent) {
	// 管理部門略称を取得する
	var keijoDate = document.getElementById("form1:htmlKeijoDate").value;
	var servlet = "rev/ka/KaCogYosanTaniAJAX";
	var target = "form1:htmlKanriBmnName";
	var code = new Array();
	code['code1'] = '0';
	code['code2'] = '1';
	code['code3'] = keijoDate;
	code['code4'] = thisObj.value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code );
}

function func_KanrishaId(thisObj, thisEvent) {
	// 管理者名を取得する
	var servlet = "rev/ka/KaCoiJinjAJAX";
	var target = "form1:htmlKanrishaName";
	var code = thisObj.value;
	getCodeName(servlet, target, code );
}

// 取得先コード
var _lastShutokusakiCd = "";
function func_ShutokusakiCd(thisObj, thisEvent) {
	// 取得先Ajax
	var ajaxTarget = 'htmlShutokusakiName';
	var servlet = 'rev/ka/KaCogToriKihonNameShokuchiAJAX';
	var target = '';	// ←空文字だけど必要
	var code = new Array();
	code['code'] = thisObj.value;
	var ajaxUtil = new AjaxUtil();

	ajaxUtil.getPluralValueSetMethod(servlet, target, code, "callbackShutokusakiName");
}
// callbackメソッド
function callbackShutokusakiName(value) {
	var shutokusakiCd = document.getElementById('form1:htmlShutokusakiCd').value;
	var nameObj = document.getElementById('form1:htmlShutokusakiName');

	var shutokusakiName = value['key1'];
	var shokuchiIkkenKbn = value['key2'];

	if (_lastShutokusakiCd != shutokusakiCd) {
		if (shutokusakiName == "null" || shutokusakiName == "") {
			nameObj.value = "";
		} else {
			nameObj.value = shutokusakiName;
		}
	}

	if (shutokusakiName == "null" || shutokusakiName == "") {
		// 検索結果が存在しない場合は名称をクリアし入力不可にする
		elementDisabled(nameObj, true);
	} else {
		if (shokuchiIkkenKbn == "1" || shokuchiIkkenKbn == "2") {
			// 諸口一見区分が現金、振込の場合入力可能にする
			elementDisabled(nameObj, false);
		} else if (shokuchiIkkenKbn == "null" || shokuchiIkkenKbn == "") {
			elementDisabled(nameObj, true);
		}
	}
	document.getElementById('form1:htmlLastShutokusakiCd').value = 
	document.getElementById('form1:htmlShutokusakiCd').value;
	_lastShutokusakiCd = document.getElementById('form1:htmlShutokusakiCd').value;
}



function loadFunc() {

	func_KanriBmnCd(document.getElementById('form1:htmlKanriBmnCd'), '');
	func_KanrishaId(document.getElementById('form1:htmlKanrishaId'), '');
	
	_lastShutokusakiCd = document.getElementById('form1:htmlLastShutokusakiCd').value;
	func_ShutokusakiCd(document.getElementById('form1:htmlShutokusakiCd'), '');
	
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onLoad="return loadFunc();"><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kae00103T01.onPageLoadBegin}">

<h:form styleClass="form" id="form1">

	<!-- ヘッダーインクルード -->
	<jsp:include page="../../rev/inc/header.jsp" />

	<!-- ヘッダーへのデータセット領域 -->
	<DIV style="display:none;"><hx:commandExButton type="submit"
		value="閉じる" styleClass="commandExButton" id="closeDisp"
		action="#{pc_Kae00103T01.doCloseDispAction}"
		></hx:commandExButton> <h:outputText
		styleClass="outputText" id="htmlFuncId"
		value="#{pc_Kae00103T01.funcId}"></h:outputText> <h:outputText
		styleClass="outputText" id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
		styleClass="outputText" id="htmlScrnName"
		value="#{pc_Kae00103T01.screenName}"></h:outputText></DIV>

	<!--↓outer↓-->
	<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
		id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText></FIELDSET>

	<!--↓content↓-->
	<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Kae00103T01.doReturnDispAction}"></hx:commandExButton> <!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	<DIV id="content">
	<DIV class="column" align="center">
	<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" width="900" class="table">
		<TBODY>
			<TR>
				<TH width="175" class="v_a"><h:outputText id="lblKenkariNo"
							styleClass="outputText"
							style="#{pc_Kae00103T01.kae00103.propKenkariNo.labelStyle}"
							value="#{pc_Kae00103T01.kae00103.propKenkariNo.labelName}">
						</h:outputText></TH>
				<TD><h:inputText id="htmlKenkariNo"
							styleClass="inputText" size="20"
							style="#{pc_Kae00103T01.kae00103.propKenkariNo.style}"
							readonly="#{pc_Kae00103T01.kae00103.propKenkariNo.readonly}"
							disabled="#{pc_Kae00103T01.kae00103.propKenkariNo.disabled}"
							maxlength="#{pc_Kae00103T01.kae00103.propKenkariNo.maxLength}"
							value="#{pc_Kae00103T01.kae00103.propKenkariNo.stringValue}" tabindex="1">
						</h:inputText></TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" cellpadding="0" cellspacing="0" width="900" height="400" style="margin-top: 10px;">
		<TBODY>
			<TR>
				<TD height="27" align="left">
					<hx:commandExButton type="submit" style="width: 164px"
							value="建設仮勘定情報" id="tabKenkari" styleClass="tab_head_on" tabindex="2" action="#{pc_Kae00103T01.doTabKenkariAction}"></hx:commandExButton><hx:commandExButton
							type="submit" style="width: 164px" value="管理情報" id="tabKanri"
							styleClass="tab_head_off" action="#{pc_Kae00103T01.doTabKanriAction}" tabindex="3">
						</hx:commandExButton>							
				</TD>
			</TR>			
			<TR>
				<TD height="100%" align="left" valign="top">
					<TABLE border="1" cellpadding="20" cellspacing="0" height="100%" width="100%" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center" valign="top">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="880" class="table" style="margin-top: 10px;">
								<TBODY>
									<TR>
										<TH class="v_b" width="175"><h:outputText id="lblKeijoDate"
													value="#{pc_Kae00103T01.propKeijoDate.labelName}"
													styleClass="outputText"
													style="#{pc_Kae00103T01.propKeijoDate.labelStyle}">
												</h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlKeijoDate"
													value="#{pc_Kae00103T01.propKeijoDate.dateValue}"
													readonly="#{pc_Kae00103T01.propKeijoDate.readonly}"
													disabled="#{pc_Kae00103T01.propKeijoDate.disabled}"
													style="#{pc_Kae00103T01.propKeijoDate.style}" size="12"
													tabindex="4">
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
													<hx:inputHelperDatePicker />
												</h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText id="lblAnkenName"
													styleClass="outputText"
													value="#{pc_Kae00103T01.propAnkenName.labelName}"
													style="#{pc_Kae00103T01.propAnkenName.labelStyle}">
												</h:outputText></TH>
										<TD colspan="3"><h:inputText id="htmlAnkenName"
													styleClass="inputText" size="110"
													value="#{pc_Kae00103T01.propAnkenName.stringValue}"
													maxlength="#{pc_Kae00103T01.propAnkenName.maxLength}"
													readonly="#{pc_Kae00103T01.propAnkenName.readonly}"
													disabled="#{pc_Kae00103T01.propAnkenName.disabled}"
													style="#{pc_Kae00103T01.propAnkenName.style}" tabindex="5">
												</h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText id="lblAnkenNo"
													styleClass="outputText"
													value="#{pc_Kae00103T01.propAnkenNo.labelName}"
													style="#{pc_Kae00103T01.propAnkenNo.labelStyle}">
												</h:outputText></TH>
										<TD colspan="3"><h:inputText id="htmlAnkenNo"
													styleClass="inputText" size="40"
													value="#{pc_Kae00103T01.propAnkenNo.stringValue}"
													maxlength="#{pc_Kae00103T01.propAnkenNo.maxLength}"
													readonly="#{pc_Kae00103T01.propAnkenNo.readonly}"
													disabled="#{pc_Kae00103T01.propAnkenNo.disabled}"
													style="#{pc_Kae00103T01.propAnkenNo.style}" tabindex="6">
												</h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_e"><h:outputText id="lblKanriBmnCd"
											styleClass="outputText"
											value="#{pc_Kae00103T01.propKanriBmnCd.labelName}"
											style="#{pc_Kae00103T01.propKanriBmnCd.labelStyle}">
										</h:outputText></TH>
										<TD colspan="3" nowrap>
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:inputText id="htmlKanriBmnCd"
													styleClass="inputText"
													value="#{pc_Kae00103T01.propKanriBmnCd.stringValue}"
													size="10" onblur="return func_KanriBmnCd(this, event);"
													disabled="#{pc_Kae00103T01.propKanriBmnCd.disabled}"
													maxlength="#{pc_Kae00103T01.propKanriBmnCd.maxLength}"
													readonly="#{pc_Kae00103T01.propKanriBmnCd.readonly}"
													style="#{pc_Kae00103T01.propKanriBmnCd.style}" tabindex="7">
												</h:inputText><hx:commandExButton
													id="searchKanriBmnCd" type="submit"
													styleClass="commandExButton_search"
													action="#{pc_Kae00103T01.doSearchKanriBmnCdAction}" tabindex="8">
												</hx:commandExButton><h:outputText id="htmlKanriBmnName"
													styleClass="outputText"
													value="#{pc_Kae00103T01.propKanriBmnName.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText id="lblKanrishaId"
													styleClass="outputText"
													value="#{pc_Kae00103T01.propKanrishaId.labelName}"
													style="#{pc_Kae00103T01.propKanrishaId.labelStyle}">
												</h:outputText></TH>
										<TD colspan="3" nowrap>
											<DIV style="width:705px;white-space:nowrap;overflow:hidden;display:block;">
												<h:inputText id="htmlKanrishaId"
													styleClass="inputText"
													value="#{pc_Kae00103T01.propKanrishaId.stringValue}"
													size="20" onblur="return func_KanrishaId(this, event);"
													disabled="#{pc_Kae00103T01.propKanrishaId.disabled}"
													maxlength="#{pc_Kae00103T01.propKanrishaId.maxLength}"
													readonly="#{pc_Kae00103T01.propKanrishaId.readonly}"
													style="#{pc_Kae00103T01.propKanrishaId.style}" tabindex="9">
												</h:inputText><hx:commandExButton id="searchKanrisha"
													type="submit" styleClass="commandExButton_search"
													tabindex="10"
													action="#{pc_Kae00103T01.doSearchKanrishaAction}">
												</hx:commandExButton><h:outputText
													id="htmlKanrishaName"
													styleClass="outputText"
													value="#{pc_Kae00103T01.propKanrishaName.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TH class="v_g"><h:outputText
													styleClass="outputText" id="lblKeijoGaku"
													value="#{pc_Kae00103T01.propKeijoGaku.labelName}"
													style="#{pc_Kae00103T01.propKeijoGaku.labelStyle}">
												</h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlKeijoGaku"
													value="#{pc_Kae00103T01.propKeijoGaku.stringValue}"
													readonly="#{pc_Kae00103T01.propKeijoGaku.readonly}"
													disabled="#{pc_Kae00103T01.propKeijoGaku.disabled}"
													style="padding-right: 3px; text-align: right;#{pc_Kae00103T01.propKeijoGaku.style}"
													size="17" tabindex="11">
													<hx:inputHelperAssist errorClass="inputText_Error" />
												</h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_a">
											<h:outputText styleClass="outputText" id="lblKoujiStartDate"
													value="#{pc_Kae00103T01.propKoujiStartDate.name}"
													style="#{pc_Kae00103T01.propKoujiStartDate.labelStyle}">
												</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlKoujiStartDate" styleClass="inputText"
													style="#{pc_Kae00103T01.propKoujiStartDate.style}"
													value="#{pc_Kae00103T01.propKoujiStartDate.dateValue}"
													readonly="#{pc_Kae00103T01.propKoujiStartDate.readonly}"
													disabled="#{pc_Kae00103T01.propKoujiStartDate.disabled}"
													size="12" tabindex="12">
													<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<hx:inputHelperDatePicker />
												</h:inputText>
											<h:outputText 
												styleClass="outputText" 
												value="　～"
												id="lblKara2">
											</h:outputText>
											<h:inputText id="htmlKoujiEndDate" styleClass="inputText"
													style="#{pc_Kae00103T01.propKoujiEndDate.style}"
													value="#{pc_Kae00103T01.propKoujiEndDate.dateValue}"
													readonly="#{pc_Kae00103T01.propKoujiEndDate.readonly}"
													disabled="#{pc_Kae00103T01.propKoujiEndDate.disabled}"
													size="12" tabindex="13">
													<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<hx:inputHelperDatePicker />
												</h:inputText>
										</TD>
									</TR>	
									<TR>
										<TH class="v_b"><h:outputText
													id="lblShutokusakiCd"
													value="#{pc_Kae00103T01.propShutokusakiCd.labelName}"
													styleClass="outputText"
													style="#{pc_Kae00103T01.propShutokusakiCd.labelStyle}">
												</h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlShutokusakiCd"
											value="#{pc_Kae00103T01.propShutokusakiCd.stringValue}"
											size="20"
											disabled="#{pc_Kae00103T01.propShutokusakiCd.disabled}"
											maxlength="#{pc_Kae00103T01.propShutokusakiCd.maxLength}"
											readonly="#{pc_Kae00103T01.propShutokusakiCd.readonly}"
											style="#{pc_Kae00103T01.propShutokusakiCd.style}"
											onblur="return func_ShutokusakiCd(this, event);" tabindex="14">
										</h:inputText><hx:commandExButton
											type="submit"
											styleClass="commandExButton_search"
											id="searchShutokusaki"
											action="#{pc_Kae00103T01.doSearchShutokusakiAction}" tabindex="15">
										</hx:commandExButton></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText
											id="lblShutokusakiName"
											value="#{pc_Kae00103T01.propShutokusakiName.labelName}"
											styleClass="outputText" style="#{pc_Kae00103T01.propShutokusakiName.labelStyle}">
										</h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlShutokusakiName"
											value="#{pc_Kae00103T01.propShutokusakiName.stringValue}"
											size="60"
											disabled="#{pc_Kae00103T01.propShutokusakiName.disabled}"
											maxlength="#{pc_Kae00103T01.propShutokusakiName.maxLength}"
											readonly="#{pc_Kae00103T01.propShutokusakiName.readonly}"
											style="#{pc_Kae00103T01.propShutokusakiName.style}" tabindex="16">
										</h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d">
											<h:outputText
												styleClass="outputText" 
												id="lblKeiyakuDate"
												value="#{pc_Kae00103T01.propKeiyakuDate.labelName}"
												style="#{pc_Kae00103T01.propKeiyakuDate.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlKeiyakuDate" styleClass="inputText"
													style="#{pc_Kae00103T01.propKeiyakuDate.style}"
													value="#{pc_Kae00103T01.propKeiyakuDate.dateValue}"
													readonly="#{pc_Kae00103T01.propKeiyakuDate.readonly}"
													disabled="#{pc_Kae00103T01.propKeiyakuDate.disabled}"
													size="12" tabindex="17">
													<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<hx:inputHelperDatePicker />
												</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_e">
											<h:outputText styleClass="outputText" id="lblKeiyakuNo"
												value="#{pc_Kae00103T01.propKeiyakuNo.labelName}"
												style="#{pc_Kae00103T01.propKeiyakuNo.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlKeiyakuNo" styleClass="inputText"
													style="#{pc_Kae00103T01.propKeiyakuNo.style}"
													value="#{pc_Kae00103T01.propKeiyakuNo.stringValue}"
													readonly="#{pc_Kae00103T01.propKeiyakuNo.readonly}"
													disabled="#{pc_Kae00103T01.propKeiyakuNo.disabled}"
													maxlength="#{pc_Kae00103T01.propShutokusakiName.maxLength}"
													size="40" tabindex="18">
												</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText id="lblNaiyo"
													styleClass="outputText"
													value="#{pc_Kae00103T01.propNaiyo.labelName}"
													style="#{pc_Kae00103T01.propNaiyo.labelStyle}">
												</h:outputText></TH>
										<TD colspan="3" nowrap>
										<h:inputTextarea styleClass="inputTextarea" id="htmlNaiyo"
													value="#{pc_Kae00103T01.propNaiyo.stringValue}"
													style='#{pc_Kae00103T01.propNaiyo.style}' cols="84"
													readonly="#{pc_Kae00103T01.propNaiyo.readonly}"
													disabled="#{pc_Kae00103T01.propNaiyo.disabled}" rows="7"
													tabindex="19"></h:inputTextarea>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE class="button_bar" width="820" style="margin-top:10px">
		<TBODY>
			<TR>
				<TD>
					<hx:commandExButton id="register" type="submit" value="登録"
							styleClass="commandExButton_dat"
							confirm="#{msg.SY_MSG_0002W}"
							action="#{pc_Kae00103T01.doRegisterAction}"
							disabled="#{pc_Kae00103T01.kae00103.propRegister.disabled}"
							rendered="#{pc_Kae00103T01.kae00103.propRegister.rendered}" tabindex="20">
						</hx:commandExButton>
					<hx:commandExButton id="update" type="submit" value="更新"
							styleClass="commandExButton_dat"
							confirm="#{msg.SY_MSG_0003W}"
							action="#{pc_Kae00103T01.doUpdateAction}"
							disabled="#{pc_Kae00103T01.kae00103.propUpdate.disabled}"
							rendered="#{pc_Kae00103T01.kae00103.propUpdate.rendered}" tabindex="21">
						</hx:commandExButton>
					<hx:commandExButton id="delete" type="submit" value="削除"
							styleClass="commandExButton_dat"
							confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Kae00103T01.doDeleteAction}"
							disabled="#{pc_Kae00103T01.kae00103.propDelete.disabled}"
							rendered="#{pc_Kae00103T01.kae00103.propDelete.rendered}" tabindex="22">
						</hx:commandExButton>
					<hx:commandExButton id="clear" type="submit" value="クリア"
							styleClass="commandExButton_etc"
							action="#{pc_Kae00103T01.doClearAction}"
							disabled="#{pc_Kae00103T01.kae00103.propClear.disabled}"
							rendered="#{pc_Kae00103T01.kae00103.propClear.rendered}" tabindex="23">
						</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<!-- ↑ここにコンポーネントを配置 --></DIV>
	</DIV>
	<!--↑content↑-->
	</DIV>
	<!--↑outer↑-->
	<!-- フッダーインクルード -->
	<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="htmlKeijoGaku=###,###,###,##0;###,###,###,##0"
				id="htmlFormatNumberOption">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kae00103T01.propKanzaiNendoHidden.stringValue}"
				id="htmlKanzaiNendoHidden"></h:inputHidden>
			<h:inputHidden value="#{pc_Kae00103T01.propLastShutokusakiCd.stringValue}"
			    id="htmlLastShutokusakiCd"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kae00103T01.kae00103.propConfirmFlg.integerValue}"
				id="htmlConfirmFlg">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kae00103T01.kae00103.propConfirmButtonId.stringValue}"
				id="htmlConfirmButtonId"></h:inputHidden>
		</h:form>
</hx:scriptCollector></BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

