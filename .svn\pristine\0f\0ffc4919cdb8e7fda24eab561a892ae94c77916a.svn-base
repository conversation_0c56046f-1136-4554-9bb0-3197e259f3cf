<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea01203.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@page import="com.jast.gakuen.rev.ke.action.Kea01201Act.KeiriSateiSyoriKbn"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea01203.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	var btn = document.getElementById('form1:htmlTableList1RowHidden').value;
	indirectClick(btn);
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}
function func_1(thisObj, thisEvent) {
	document.getElementById('form1:htmlTableList1RowHidden').value = thisObj.name.substr(6);
}
function func_2(thisObj, thisEvent) {
	changeScrollPosition('scroll','listscroll');
	countTotal();
}
function alChk(thisObj, thisEvent) {
	check('htmlTableList1','htmlCheck');
}
function alUnchk(thisObj, thisEvent) {
	uncheck('htmlTableList1','htmlCheck');
}
function countTotal() { 

	var syoriKbn = document.getElementById('form1:syoriKbn').value;
	var listCnt = document.getElementById('form1:htmlTableList1').rows.length;
	var sumKingaku = 0;
	var value1 = 0;
	
	for (i = 0; i < listCnt; i++){
		if(<%= KeiriSateiSyoriKbn.KEIRI_SATEI.getCode() %> == syoriKbn){
			value1 = FormatNumber.getValue(document.getElementById('form1:htmlTableList1:'+i+':htmlColKeiriSateiGaku').value);
		}else{
			value1 = FormatNumber.getValue(document.getElementById('form1:htmlTableList1:'+i+':htmlColKeiriKetteiGaku').value);
		}
		if (isNaN(value1) || value1=='') {
   			kingakuValue=0;
 		} else {
 			kingakuValue=parseInt(value1);
 		}			
   		sumKingaku = sumKingaku + kingakuValue;
  	}
  	document.getElementById('form1:htmlKingakuTotal').value = sumKingaku;
	sumKingaku = sumKingaku + '';
  	var newVar='';
  	for (var i=sumKingaku.length;i>=3;i-=3) {
  		var tmp = sumKingaku.substring(i-3,i);
  		newVar = ','+tmp+newVar;
  	}
  	if(sumKingaku.length%3 == 0) {
  		newVar = newVar.substring(1,newVar.length);
  	} else {
  		newVar = sumKingaku.substring(0,sumKingaku.length%3) + newVar;
  	}
  	document.getElementById('form1:htmlKingakuTotal').innerHTML=newVar;
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_2(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea01203.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea01203.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea01203.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea01203.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="新規登録"
	styleClass="commandExButton" id="register" action="#{pc_Kea01203.doRegisterAction}"></hx:commandExButton><hx:commandExButton type="submit" value="戻る"
	styleClass="commandExButton" id="returnDisp" action="#{pc_Kea01203.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="890">
							<TBODY>
								<TR>
									<TH width="150" class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblKaikeiNendo"
										value="#{pc_Kea01203.propKaikeiNendo.name}"
										style="#{pc_Kea01203.propKaikeiNendo.labelStyle}"></h:outputText><h:outputText styleClass="outputText"
										id="lblSyoriKbnName"
										value="#{pc_Kea01203.propSyoriKbnName.name}"
										style="#{pc_Kea01203.propSyoriKbnName.labelStyle}"></h:outputText></TH>
									<TD width="295">
										<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
											<TBODY>
												<TR>
													<TD width="55" nowrap><h:outputText styleClass="outputText"
														id="htmlKaikeiNendo"
														value="#{pc_Kea01203.propKaikeiNendo.dateValue}"
														style="#{pc_Kea01203.propKaikeiNendo.style}">
														<f:convertDateTime pattern="yyyy" /></h:outputText><h:outputText styleClass="outputText"
														id="htmlKaikeiNendoDsp"
														value="#{pc_Kea01203.propKaikeiNendoDsp.stringValue}"
														style="#{pc_Kea01203.propKaikeiNendoDsp.style}"></h:outputText></TD>
													<TD nowrap><h:outputText styleClass="outputText"
														id="htmlSyoriKbnName"
														value="#{pc_Kea01203.propSyoriKbnName.stringValue}"
														style="#{pc_Kea01203.propSyoriKbnName.style}"></h:outputText></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
									<TH width="150" class="v_b" nowrap><h:outputText styleClass="outputText"
										id="lblShikinShohiKbnName"
										value="#{pc_Kea01203.propShikinShohiKbnName.name}"
										style="#{pc_Kea01203.propShikinShohiKbnName.labelStyle}"></h:outputText></TH>
									<TD width="295"><h:outputText styleClass="outputText"
										id="htmlShikinShohiKbnName"
										value="#{pc_Kea01203.propShikinShohiKbnName.stringValue}"
										style="#{pc_Kea01203.propShikinShohiKbnName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblYsnTCd"
										value="#{pc_Kea01203.propYsnTCd.name}"
										style="#{pc_Kea01203.propYsnTCd.labelStyle}"></h:outputText></TH>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
											<TBODY>
												<TR>
													<TD width="285" nowrap><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlYsnTCd" tabindex="1"
														value="#{pc_Kea01203.propYsnTCd.stringValue}"
														style="width: 285px"
														disabled="#{pc_Kea01203.propYsnTCd.disabled}"
														readonly="#{pc_Kea01203.propYsnTCd.readonly}"
														rendered="#{pc_Kea01203.propYsnTCd.rendered}">
														<f:selectItems value="#{pc_Kea01203.propYsnTCd.list}" /></h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblMokuCd"
										value="#{pc_Kea01203.propMokuCd.name}"
										style="#{pc_Kea01203.propMokuCd.labelStyle}"></h:outputText></TH>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
											<TBODY>
												<TR>
													<TD width="70" nowrap><h:outputText styleClass="outputText" id="htmlMokuCd"
														value="#{pc_Kea01203.propMokuCd.stringValue}"
														style="#{pc_Kea01203.propMokuCd.style}"></h:outputText></TD>
													<TD nowrap><DIV style="width:221px;white-spce:nowrap;overflow:hidden;display:block;"><h:outputText
													styleClass="outputText" id="htmlMokuName"
													value="#{pc_Kea01203.propMokuName.stringValue}"
													style="#{pc_Kea01203.propMokuName.style}"
													title="#{pc_Kea01203.propMokuName.stringValue}"></h:outputText></DIV></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblKmkCd" value="#{pc_Kea01203.propKmkCd.name}"
										style="#{pc_Kea01203.propKmkCd.labelStyle}"></h:outputText></TH>
									<TD colspan="3">
									<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
										<TBODY>
											<TR>
												<TD width="95" nowrap><h:outputText styleClass="outputText"
													id="htmlKmkCd"
													value="#{pc_Kea01203.propKmkCd.stringValue}"
													style="#{pc_Kea01203.propKmkCd.style}"></h:outputText></TD>
												<TD nowrap><DIV style="width:640px;white-spce:nowrap;overflow:hidden;display:block;"><h:outputText styleClass="outputText"
													id="htmlKmkName"
													value="#{pc_Kea01203.propKmkName.stringValue}"
													title="#{pc_Kea01203.propKmkName.stringValue}"
													style="#{pc_Kea01203.propKmkName.style}"></h:outputText></DIV></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="15">　</TD>
									<TD width="890" align="right"><h:outputText styleClass="outputText"
										id="htmlListCount"
										value="#{pc_Kea01203.propTableList1.listCount}">
										<f:convertNumber pattern="##,##0" />
										</h:outputText><h:outputText styleClass="outputText"
											id="lblKen"
											value="件"></h:outputText></TD>
									<TD width="18">　</TD>
								</TR>			
								<TR>
									<TD width="18"></TD>
									<TD colspan="2" align="left" nowrap>
										<TABLE border="1" class="meisai_page" height="30">
											<TR>
												<TH align="center" nowrap width="31" height="30"><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblCheck"
													value="削">
													</h:outputText></DIV></TH>
												<TH align="center" nowrap width="31" height="30"><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblColNo"
													value="NO"></h:outputText></DIV></TH>
												<TH align="center" nowrap width="138"><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblColYsnTName"
													value="予算単位"></h:outputText></DIV></TH>
												<TH align="center" nowrap width="145"><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblColMokuName"
													value="目的"></h:outputText></DIV></TH>
												<TH align="center" nowrap width="145"><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblColKmkName"
													value="科目"></h:outputText></DIV></TH>
												<TH align="center" nowrap width="125"><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblColKomokuNaiyo"
													value="項目内容"></h:outputText></DIV></TH>
												<TH align="center" nowrap width="70" ><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblColShinseiJotai"
													value="申請状態"></h:outputText></DIV></TH>
												<TH align="center" nowrap width="134">
													<DIV style="width:80px">
														<h:outputText styleClass="outputText"
															id="lblColKeiriSateiGaku"
															value="#{pc_Kea01203.propKeiriSateiGaku.labelName}"
															style="#{pc_Kea01203.propKeiriSateiGaku.labelStyle}"
															rendered="#{pc_Kea01203.propKeiriSateiGaku.rendered}"></h:outputText><h:outputText styleClass="outputText"
															id="lblColKeiriKetteiGaku"
															value="#{pc_Kea01203.propKeiriKetteiGaku.labelName}"
															style="#{pc_Kea01203.propKeiriKetteiGaku.labelStyle}"
															rendered="#{pc_Kea01203.propKeiriKetteiGaku.rendered}"></h:outputText>
													</DIV>
												</TH>
												<TH align="center" width="80" nowrap ><DIV style="margin-bottom:10px;"><h:outputText styleClass="outputText"
													id="lblColButton"
													value="　"></h:outputText></DIV></TH>
											</TR>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD width="18"></TD>
									<TD colspan="2" align="left" nowrap>
									<DIV class="listScroll" style="height:371px; width:906px"
										onscroll="setScrollPosition('scroll', this);"
										id="listScroll" align="left"><h:dataTable border="0"
										cellpadding="2" cellspacing="0" columnClasses=",,,,"
										headerClass="headerClass" footerClass="footerClass"
										rowClasses="#{pc_Kea01203.propTableList1.rowClasses}"
										styleClass="meisai_scroll" id="htmlTableList1" var="varlist"
										width="888" value="#{pc_Kea01203.propTableList1.list}" style="table-layout: fixed;">
										<h:column id="column10">
											<f:facet name="header">
											</f:facet>
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																				  id="htmlCheck" 
																				   value="#{varlist.selected.checked}"
																				   disabled="#{varlist.selected.disabled}" >
											</h:selectBooleanCheckbox>
											<f:attribute value="30" name="width" />
										</h:column>
										<h:column id="column1">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText"
												id="htmlColNo"
												value="#{varlist.colNo}"
												style="padding-right: 3px; text-align: right"></h:outputText>
											<f:attribute value="30" name="width" />
											<f:attribute value="true" name="nowrap" />
											<f:attribute value="text-align: right" name="style" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText"
												id="htmlColYsnTName"
												value="#{varlist.colYsnTName.stringValue}"
												title="#{varlist.colYsnTName.stringValue}"
												style="font-size: 9pt;white-space:nowrap;"></h:outputText>
											<f:attribute value="138" name="width" />
											<f:attribute value="true" name="nowrap" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="145" name="width" />
											<h:outputText styleClass="outputText"
												id="htmlColMokuName"
												value="#{varlist.colMokuName.stringValue}"
												style="font-size: 9pt;white-space:nowrap;"
												title="#{varlist.colMokuName.stringValue}"></h:outputText>
											<f:attribute value="true" name="nowrap" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText"
												id="htmlColKmkName"
												value="#{varlist.colKmkName.stringValue}"
												style="font-size: 9pt;white-space:nowrap;"
												title="#{varlist.colKmkName.stringValue}"></h:outputText>
											<f:attribute value="145" name="width" />
											<f:attribute value="true" name="nowrap" />
										</h:column>
										<h:column id="column7">
											<f:facet name="header">
											</f:facet>
											<h:inputText styleClass="inputText"
												id="htmlColKomokuNaiyo" size="16" tabindex="2"
												value="#{varlist.colKomokuNaiyo.stringValue}"
												style="#{varlist.colKomokuNaiyo.style}; padding-left: 0px; padding-left: 3px; text-align: left;"
												rendered="#{varlist.colKomokuNaiyo.rendered}"
												disabled="#{varlist.colKomokuNaiyo.disabled}"
												readonly="#{varlist.colKomokuNaiyo.readonly}">
												<hx:inputHelperAssist errorClass="inputText_Error" />
												</h:inputText>
											<f:attribute value="125" name="width" />
											<f:attribute value="true" name="nowrap" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText"
												id="htmlColShinseiJotaiName"
												value="#{varlist.colShinseiJotaiName}"
												style="font-size: 9pt"></h:outputText>
											<f:attribute value="70" name="width" />
											<f:attribute value="true" name="nowrap" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
											</f:facet>
											<h:inputText styleClass="inputText"
												id="htmlColKeiriSateiGaku" size="16" tabindex="2"
												value="#{varlist.colKeiriSateiGaku.stringValue}"
												style="#{varlist.colKeiriSateiGaku.style}; padding-left: 0px; padding-right: 3px; text-align: right;"
												rendered="#{varlist.colKeiriSateiGaku.rendered}"
												disabled="#{varlist.colKeiriSateiGaku.disabled}"
												readonly="#{varlist.colKeiriSateiGaku.readonly}"
												onblur="return countTotal();">
												<hx:inputHelperAssist errorClass="inputText_Error" />
											</h:inputText>
											<h:inputText styleClass="inputText"
												id="htmlColKeiriKetteiGaku" size="16" tabindex="2"
												value="#{varlist.colKeiriKetteiGaku.stringValue}"
												style="#{varlist.colKeiriKetteiGaku.style}; padding-left: 0px; padding-right: 3px; text-align: right;"
												rendered="#{varlist.colKeiriKetteiGaku.rendered}"
												disabled="#{varlist.colKeiriKetteiGaku.disabled}"
												readonly="#{varlist.colKeiriKetteiGaku.readonly}"
												onblur="return countTotal();">
												<hx:inputHelperAssist errorClass="inputText_Error" />
											</h:inputText>
											<f:attribute value="134" name="width" />
										</h:column>
										<h:column id="column8">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" styleClass="commandExButton"
												id="detail" tabindex="2"
												value="詳細 "
												disabled="#{varlist.colDetailButton.disabled}"
												style="font-size: 9pt; width: 30px"
												action="#{pc_Kea01203.doDetailAction}"
												rendered="#{varlist.colDetailButton.rendered}"></hx:commandExButton>
											<f:attribute value="30" name="width" />
											<f:attribute value="true" name="nowrap" />
										</h:column>
										<h:column id="column9">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" styleClass="commandExButton"
												id="copy" tabindex="2"
												value="ｺﾋﾟｰ"
												disabled="#{varlist.colCopyButton.disabled}"
												style="font-size: 9pt; width: 30px"
												action="#{pc_Kea01203.doCopyAction}"
												rendered="#{varlist.colCopyButton.rendered}"></hx:commandExButton>
											<f:attribute value="30" name="width" />
											<f:attribute value="true" name="nowrap" />
										</h:column>
									</h:dataTable></DIV>
									</TD></TR>
									<TR>
										<TD width="18"></TD>
										<TD colspan="2" align="left" nowrap>
											<TABLE border="0" cellpadding="0" cellspacing="0"
													class="button_bar" width="890">
												<TBODY>
													<TR>
														<TD width="250" align="left">
															<hx:panelBox styleClass="panelBox" id="box1">
																<hx:jspPanel id="jspPanel1">
																	<hx:commandExButton type="button" 
																						value="on" 
																						styleClass="check" 
																						id="check1" 
																						onclick="return alChk(this, event);">
																	</hx:commandExButton>
																	<hx:commandExButton type="button" 
																						value="off" 
																						styleClass="uncheck" 
																						id="uncheck1" 
																						onclick="return alUnchk(this, event);">
																	</hx:commandExButton>
																	<hx:commandExButton type="submit" 
																						value="削除" 
																						styleClass="commandExButton" 
																						id="delete" 
																						action="#{pc_Kea01203.doDeleteAction}"
																						confirm="#{msg.SY_MSG_0004W}">
																	</hx:commandExButton>
																</hx:jspPanel>
															</hx:panelBox>
														</TD>
														<TD align="right" width="530" nowrap>
													 		<h:outputText
																styleClass="outputText" id="text1" value="金額計：">
															</h:outputText>
														</TD>
														<TD width="150px" align="right" style="border-bottom:1px solid black;">
															<h:outputText styleClass="outputText"
																	id="htmlKingakuTotal">
															</h:outputText>
														</TD>
														<TD align="right" width="100">
														</TD>
													</TR>
													<TR>
														<TD colspan="3" align="center" nowrap>
															<hx:commandExButton styleClass="commandExButton_dat" type="submit"
															id="exec" tabindex="6"
															value="確定"
															action="#{pc_Kea01203.doExecAction}"
															confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden id="htmlExecutableSearchHidden" value="#{pc_Kea01203.propExecutableSearchHidden.integerValue}"><f:convertNumber /></h:inputHidden>
			<h:inputHidden id="htmlTableList1RowHidden" value="#{pc_Kea01203.propTableList1RowHidden.stringValue}"></h:inputHidden>
			<h:inputHidden id="scroll" value="#{pc_Kea01203.propTableList1.scrollPosition}"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea01203.propFormatNumberOption.stringValue}"
				id="htmlFormatNumberOption"></h:inputHidden>
			<h:inputHidden id="syoriKbn" value="#{pc_Kea01203.propSyoriKbn.stringValue}"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
	document.getElementById('form1:htmlKingakuTotal').readOnly=true;
</SCRIPT>
</HTML>

