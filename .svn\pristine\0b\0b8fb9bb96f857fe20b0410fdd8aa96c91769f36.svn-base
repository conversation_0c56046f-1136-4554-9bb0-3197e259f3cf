<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KMB_SKK_KMK" name="資格用科目" prod_id="KM" description="資格用科目分類に属する科目が設定されます。
『資格用科目設定』にて作成されます。">
<STATMENT><![CDATA[
KMB_SKK_KMK
]]></STATMENT>
<COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="資格用科目を登録するみなし入学年度が設定されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="資格用科目を登録する入学学期が設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="資格用科目を登録するカリキュラム学科組織コードが設定されます。"/><COLUMN id="SIKAK_CD" name="資格コード" type="string" length="6" lengthDP="0" byteLength="6" description="資格用科目を登録する資格コードが設定されます。"/><COLUMN id="KAMOK_CD" name="科目コード" type="string" length="10" lengthDP="0" byteLength="10" description="資格を取得する為に必要な科目のコードが設定されます。"/><COLUMN id="SIKAK_KBR_CD" name="資格用科目分類コード" type="string" length="6" lengthDP="0" byteLength="6" description="資格用の科目を分類する為の資格用科目分類コードが設定されます。"/><COLUMN id="KAMOK_MEISAI_NO" name="科目明細ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="該当の入学年度、学期、カリキュラム学科組織に配当されている科目の明細ＮＯが設定されます。"/><COLUMN id="ROW_NO" name="並び順ＮＯ" type="number" length="5" lengthDP="0" byteLength="0" description="帳票、画面に表示する際の表示順が設定されます。１からの連番となります。"/><COLUMN id="TOKUTEI_KAMOK_FLG" name="特定科目フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="『特定科目受講可否判定』にて判定される科目かどうかを表します。"><CODE><CASE value="0" display="特定科目以外"/><CASE value="1" display="特定科目"/></CODE></COLUMN>
</TABLE>
