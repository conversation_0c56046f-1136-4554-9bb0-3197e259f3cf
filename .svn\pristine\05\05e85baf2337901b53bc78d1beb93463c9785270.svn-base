<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmi00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmi00601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmi00601.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmi00601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmi00601.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" width="750">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText
							styleClass="outputText" id="htmlCount"
							value="#{pc_Kmi00601.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text2" value="件"></h:outputText></TD>
					</TR>
					<TR>
						<TD align="center">
						<div class="listScroll" style="height:315px;width:751px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Kmi00601.propNaiSykenList.rowClasses}"
							styleClass="meisai_scroll" id="htmlNaiSykenList" width="734"
							value="#{pc_Kmi00601.propNaiSykenList.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text3" styleClass="outputText" value="コード"></h:outputText>
								</f:facet>
								<f:attribute value="55" name="width" />
								<h:outputText styleClass="outputText" id="text7"
									value="#{varlist.naiSyokenCd}"></h:outputText>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="内科検診所見名称"
										id="text4"></h:outputText>
								</f:facet>
								<f:attribute value="573" name="width" />
								<h:outputText styleClass="outputText" id="text8"
									value="#{varlist.naiSyokenName}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="再検査" id="text5"></h:outputText>
								</f:facet>
								<f:attribute value="65" name="width" />
								<h:outputText styleClass="outputText" id="text9"
									value="#{varlist.saikensaFlg}"></h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="40" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Kmi00601.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="750">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
							class="table">
							<TBODY>
								<TR>
									<TH width="200" class="v_a"><h:outputText
										styleClass="outputText" id="lblNaiSyokenCD"
										value="#{pc_Kmi00601.propNaiSyokenCD.labelName}"
										style="#{pc_Kmi00601.propNaiSyokenCD.labelStyle}"></h:outputText></TH>
									<TD width="550"><h:inputText styleClass="inputText"
										id="htmlNaiSyokenCD" size="2"
										value="#{pc_Kmi00601.propNaiSyokenCD.stringValue}"
										style="#{pc_Kmi00601.propNaiSyokenCD.style}"
										maxlength="#{pc_Kmi00601.propNaiSyokenCD.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText
										styleClass="outputText" id="lblNaiSyokenName"
										value="#{pc_Kmi00601.propNaiSyokenName.labelName}"
										style="#{pc_Kmi00601.propNaiSyokenName.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlNaiSyokenName" size="40"
										value="#{pc_Kmi00601.propNaiSyokenName.stringValue}"
										style="#{pc_Kmi00601.propNaiSyokenName.style}"
										maxlength="#{pc_Kmi00601.propNaiSyokenName.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_c"><h:outputText
										styleClass="outputText" id="lblSaikensaFlg"
										value="#{pc_Kmi00601.propSaikensaFlg.labelName}"
										style="#{pc_Kmi00601.propSaikensaFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSaikensaFlg"
										value="#{pc_Kmi00601.propSaikensaFlg.stringValue}"
										style="#{pc_Kmi00601.propSaikensaFlg.style}">
										<f:selectItem itemValue="1" itemLabel="再検査対象" />
										<f:selectItem itemValue="0" itemLabel="対象外" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
				width="750">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Kmi00601.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Kmi00601.doDeleteAction1}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Kmi00601.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />

			<h:inputHidden value="#{pc_Kmi00601.propNaiSykenList.scrollPosition}"
				id="scroll"></h:inputHidden>

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

