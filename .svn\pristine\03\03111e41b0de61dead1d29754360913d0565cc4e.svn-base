<%--
  事務局情報設定（管理出身地）

  <AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/kk/Kkz00901T03.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kkz00901T03.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >
<LINK rel="stylesheet" type="text/css" href="../kk/inc/gakuenKK.css"  >	

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">

// 画面ロード時の事務局名称再取得
function loadAction(event){
  doJimukyokuNameAjax(document.getElementById('form1:htmlJmkCd'), event, 'form1:ajaxJmkNm');
}

// 事務局名称を取得する
function doJimukyokuNameAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/kk/KkdJimukyokuAJAX";
  var args = new Array();
  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// データテーブル全選択
function listCheck(thisObj, thisEvent) {
  check('htmlJmkSstList', 'htmlCheck');
}

// データテーブル全解除
function listUnCheck(thisObj, thisEvent) {
  uncheck('htmlJmkSstList', 'htmlCheck');
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// 役員コピー遷移時のクリック処理
function onClickCopy(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return doPopupMsg(id, "画面に変更があった場合、更新内容は反映されませんが、");
  }
  return true;
}

// 更新時のクリック処理
function onClickUpdate(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// 削除時のクリック処理
function onClickDelete(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}
// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {

}

</SCRIPT></HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kkz00901T03.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Kkz00901T03">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Kkz00901T03.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kkz00901T03.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kkz00901T03.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="役員コピー"
  styleClass="commandExButton" id="copy"
  disabled="#{pc_Kkz00901T01.kkz00901.propCopy.disabled}"
  onclick="return onClickCopy('#{msgCO.CO_MSG_0008W}');"
  action="#{pc_Kkz00901T01.doCopyAction}" />
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

  <TABLE border="0" cellpadding="5">
    <TBODY>
      <TR>
        <TD width="870">
        <TABLE class="table" width="100%">
          <TBODY>
            <TR align="center" valign="middle">
              <TH nowrap class="v_a" width="190">
              <!--事務局コード -->
                <h:outputText id="lblJmkCd" styleClass="outputText"
                  value="#{pc_Kkz00901T01.kkz00901.propJmkCd.labelName}"
                  style="#{pc_Kkz00901T01.kkz00901.propJmkCd.labelStyle}">
                </h:outputText></TH>
              <TD>
                <h:inputText id="htmlJmkCd" styleClass="inputText" size="18"
                  maxlength="#{pc_Kkz00901T01.kkz00901.propJmkCd.maxLength}"
                  disabled="#{pc_Kkz00901T01.kkz00901.propJmkCd.disabled}"
                  value="#{pc_Kkz00901T01.kkz00901.propJmkCd.stringValue}"
                  readonly="#{pc_Kkz00901T01.kkz00901.propJmkCd.readonly}"
                  style="#{pc_Kkz00901T01.kkz00901.propJmkCd.style}"
                  onblur="return doJimukyokuNameAjax(this, event, 'form1:ajaxJmkNm');">
                </h:inputText>
                <hx:commandExButton type="submit" id="search" styleClass="commandExButton_search"
                  value="検"
                  disabled="#{pc_Kkz00901T01.kkz00901.propSearch.disabled}"
                  action="#{pc_Kkz00901T03.doSearchAction}">
                </hx:commandExButton>
                <hx:commandExButton type="submit" id="jmkSelect" styleClass="commandExButton"
                  value="選　択"
                  disabled="#{pc_Kkz00901T01.kkz00901.propJmkSelect.disabled}"
                  action="#{pc_Kkz00901T01.doJmkSelectAction}">
                </hx:commandExButton>
                <hx:commandExButton type="submit" id="jmkUnselect" styleClass="commandExButton"
                  value="解　除"
                  disabled="#{pc_Kkz00901T01.kkz00901.propJmkUnselect.disabled}"
                  action="#{pc_Kkz00901T01.doJmkUnselectAction}">
                </hx:commandExButton>
                <h:inputText id="ajaxJmkNm" styleClass="likeOutput" tabindex="-1" size="50"
                  readonly="true"
                  value="#{pc_Kkz00901T01.kkz00901.propJmkNm.stringValue}">
                </h:inputText></TD>
            </TR>
            <TR>
              <TH nowrap class="v_b">
              <!-- 校友会 -->
                <h:outputText id="lblKoyuCombo" styleClass="outputText"
                  value="#{pc_Kkz00901T01.kkz00901.propKoyuCombo.labelName}"
                  style="#{pc_Kkz00901T01.kkz00901.propKoyuCombo.labelStyle}">
                </h:outputText></TH>
              <TD>
                <h:selectOneMenu id="htmlKoyuCombo" styleClass="selectOneMenu"
                  disabled="#{pc_Kkz00901T01.kkz00901.propKoyuCombo.disabled}"
                  value="#{pc_Kkz00901T01.kkz00901.propKoyuCombo.value}"
                  style="width:350px;">
                  <f:selectItems value="#{pc_Kkz00901T01.kkz00901.propKoyuCombo.list}" />
                </h:selectOneMenu>
                <hx:commandExButton type="submit" id="koyuSelect" styleClass="commandExButton"
                  value="選　択"
                  disabled="#{pc_Kkz00901T01.kkz00901.propKoyuSelect.disabled}"
                  action="#{pc_Kkz00901T01.doKoyuSelectAction}">
                </hx:commandExButton>
                <hx:commandExButton type="submit" id="koyuUnselect" styleClass="commandExButton"
                  value="解　除"
                  disabled="#{pc_Kkz00901T01.kkz00901.propKoyuUnselect.disabled}"
                  action="#{pc_Kkz00901T01.doKoyuUnselectAction}">
                </hx:commandExButton></TD>
            </TR>
          </TBODY>
        </TABLE>
        <BR>
        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TBODY>
            <TR align="left">
              <TD>
              <TABLE border="0" cellpadding="0" cellspacing="0"
                style="border-bottom-style: none; ">
                <TBODY>
                  <TR>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton type="submit" id="tabKkz00901T01" styleClass="tab_head_off"
                        value="#{pc_Kkz00901T01.kkz00901.propTabNameKihon.stringValue}"
                        action="#{pc_Kkz00901T03.doTabKkz00901T01Action}"
                        style="width:100%">
                      </hx:commandExButton></TD>
                    <TD class="tab_head_on" width="100">
                      <hx:commandExButton type="submit" id="tabKkz00901T02" styleClass="tab_head_off"
                        value="#{pc_Kkz00901T01.kkz00901.propTabNameAddr.stringValue}"
                        action="#{pc_Kkz00901T03.doTabKkz00901T02Action}"
                        style="width: 100%">
                      </hx:commandExButton></TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton type="button" id="tabKkz00901T03" styleClass="tab_head_on"
                        value="#{pc_Kkz00901T01.kkz00901.propTabNameSst.stringValue}"
                        style="width:100%">
                      </hx:commandExButton></TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton type="submit" id="tabKkz00901T04" styleClass="tab_head_off"
                        value="#{pc_Kkz00901T01.kkz00901.propTabNameYbn.stringValue}"
                        action="#{pc_Kkz00901T03.doTabKkz00901T04Action}"
                        style="width:100%"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton type="submit" id="tabKkz00901T05" styleClass="tab_head_off"
                        value="#{pc_Kkz00901T01.kkz00901.propTabNameYakuin.stringValue}"
                        action="#{pc_Kkz00901T03.doTabKkz00901T05Action}"
                        style="width:100%"></hx:commandExButton></TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
            <TR>
              <TD>
              <TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" width="100%">
                <TBODY>
                  <TR>
                    <TD width="100%">
                    <div style="height: 400px">
                    <BR>
                    <TABLE class="table" width="438">
                      <TBODY>
                        <TR>
                          <TH nowrap class="v_a" width="120">
                          <!-- 出身地選択コンボ -->
                            <h:outputText id="lblSstCombo" styleClass="outputText"
                              value="#{pc_Kkz00901T03.propSstCombo.labelName}"
                              style="#{pc_Kkz00901T03.propSstCombo.labelStyle}">
                            </h:outputText></TH>
                          <TD>
                            <h:selectOneMenu id="htmlSstCombo" styleClass="selectOneMenu"
                              disabled="#{pc_Kkz00901T03.propSstCombo.disabled}"
                              value="#{pc_Kkz00901T03.propSstCombo.value}"
                              style="width:210px;">
                              <f:selectItems value="#{pc_Kkz00901T03.propSstCombo.list}" />
                            </h:selectOneMenu></TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    <TABLE class="button_bar" width="438">
                      <TBODY>
                        <TR>
                          <TD align="center">
                            <hx:commandExButton type="submit" id="add" styleClass="commandExButton_dat"
                              value="追加"
                              disabled="#{pc_Kkz00901T03.propAddBtn.disabled}"
                              action="#{pc_Kkz00901T03.doAddAction}">
                            </hx:commandExButton></TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    <TABLE border="0" cellpadding="0" cellspacing="0" width="440">
                      <TR>
                        <TD align="center">
                        <TABLE width="440">
                          <TR>
                            <TD align="left" width="50%"></TD>
                            <TD align="right" width="50%">
                              <h:outputText styleClass="outputText" id="lblCount"
                                value="#{pc_Kkz00901T03.propJmkSstList.listCount}件" /></TD>
                          </TR>
                        </TABLE>
                        </TD>
                      </TR>
                    </TABLE>
                    <TABLE border="0" cellpadding="0" cellspacing="0" width="440">
                      <TR>
                        <TD>
                        <div class="listScroll" id="listScroll" style="height: 234px"
                          onscroll="setScrollPosition('htmlHidScroll', this);">
                        <h:dataTable border="1" cellpadding="2" cellspacing="0"
                          columnClasses="columnClass1" headerClass="headerClass"
                          footerClass="footerClass"
                          rowClasses="#{pc_Kkz00901T03.propJmkSstList.rowClasses}"
                          styleClass="meisai_scroll" id="htmlJmkSstList"
                          value="#{pc_Kkz00901T03.propJmkSstList.list}" var="varlist">
                          <h:column id="column1">
                            <f:facet name="header" />
                            <h:selectBooleanCheckbox id="htmlCheck"
                              styleClass="selectBooleanCheckbox" value="#{varlist.delFlg}" />
                            <f:attribute value="26" name="width" />
                            <f:attribute value="text-align:center" name="style" />
                          </h:column>
                          <h:column id="column2">
                            <f:facet name="header">
                              <h:outputText id="lblSstCd_head" styleClass="outputText" value="出身地コード" />
                            </f:facet>
                            <h:outputText id="lblSstCd_list" styleClass="outputText"
                              value="#{varlist.sstCd}" />
                            <f:attribute value="120" name="width" />
                          </h:column>
                          <h:column id="column3">
                            <f:facet name="header">
                              <h:outputText id="lblSstName_head" styleClass="outputText" value="出身地名称" />
                            </f:facet>
                            <h:outputText id="lblSstNm_list" styleClass="outputText"
                              value="#{varlist.sstName}" />
                            <f:attribute value="274" name="width" />
                          </h:column>
                        </h:dataTable>
                        </div>
                        </TD>
                      </TR>
                      <TR>
                        <TD>
                          <TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
                            <TBODY>
                              <TR>
                                <TD class="footerClass">
                                <TABLE class="panelBox">
                                  <TBODY>
                                    <TR>
                                      <TD align="left">
                                        <hx:commandExButton type="button" id="check" styleClass="check"
                                          onclick="return listCheck(this, event);"
                                          disabled="#{pc_Kkz00901T03.propRemoveBtn.disabled}" />
                                        <hx:commandExButton type="button" id="uncheck" styleClass="uncheck"
                                          onclick="return listUnCheck(this, event);"
                                          disabled="#{pc_Kkz00901T03.propRemoveBtn.disabled}" /></TD>
                                      <TD width="5"></TD>
                                      <TD align="left">
                                        <hx:commandExButton type="submit" id="remove" styleClass="commandExButton"
                                          value="除外"
                                          disabled="#{pc_Kkz00901T03.propRemoveBtn.disabled}"
                                          action="#{pc_Kkz00901T03.doRemoveAction}" /></TD>
                                    </TR>
                                  </TBODY>
                                </TABLE>
                                </TD>
                              </TR>
                            </TBODY>
                          </TABLE>
                        </TD>
                      </TR>
                    </TABLE>
                    </div>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
          <TBODY>
            <TR align="right">
              <TD align="center">
                <hx:commandExButton type="submit" id="update" styleClass="commandExButton_dat"
                  value="確定"
                  disabled="#{pc_Kkz00901T01.kkz00901.propUpdate.disabled}"
                  onclick="return onClickUpdate('#{msg.SY_MSG_0001W}');"
                  action="#{pc_Kkz00901T03.doUpdateAction}">
                </hx:commandExButton>
                <hx:commandExButton type="submit" id="delete" styleClass="commandExButton_dat"
                  value="削除"
                  disabled="#{pc_Kkz00901T01.kkz00901.propDelete.disabled}"
                  onclick="return onClickDelete('#{msg.SY_MSG_0004W}');"
                  action="#{pc_Kkz00901T03.doDeleteAction}">
                </hx:commandExButton></TD>
            </TR>
          </TBODY>
        </TABLE>
        </TD>
      </TR>
    </TBODY>
  </TABLE>
  <h:inputHidden id="htmlHidButtonKbn" value="#{pc_Kkz00901T01.kkz00901.propHidButtonKbn.integerValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidAction" value="#{pc_Kkz00901T01.kkz00901.propHidAction.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidInsUpdKbn" value="#{pc_Kkz00901T01.kkz00901.propHidInsUpdKbn.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidErrMessage" value="#{pc_Kkz00901T01.kkz00901.propHidErrMessage.value}"></h:inputHidden>
  <h:inputHidden id="htmlHidScroll" value="#{pc_Kkz00901T03.propJmkSstList.scrollPosition}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>

</HTML>
