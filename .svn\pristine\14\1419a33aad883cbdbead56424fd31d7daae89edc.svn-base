<%--
 * 追試験対象者一括登録
 * Xrd00201.jsp
 * 作者:


 * 作成日: 2006/01/16
 * version 1.0
 --%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrd/Xrd00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrd00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector 
		id="scriptCollector1"
		preRender="#{pc_Xrd00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton 
					type="submit"
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp"
					action="#{pc_Xrd00201.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText
					styleClass="outputText" 
					id="htmlFuncId"
					value="#{pc_Xrd00201.funcId}">
				</h:outputText> 
				<h:outputText
					styleClass="outputText" 
					id="htmlLoginId"
					value="#{SYSTEM_DATA.loginID}">
				</h:outputText> <h:outputText
					styleClass="outputText" 
					id="htmlScrnName"
					value="#{pc_Xrd00201.screenName}">
				</h:outputText>
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<CENTER>
			<TABLE border="0" width="650" cellpadding="0" cellspacing="0" class="table">
				<TBODY>
					<TR>
						<TH nowrap class="v_a"><!-- 実習年度 -->
							<h:outputText
								styleClass="outputText"
								id="lblJissyuNendo"
								value="#{pc_Xrd00201.propJissyuNendo.labelName}"
								style="#{pc_Xrd00201.propJissyuNendo.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText
								styleClass="inputText"
								id="htmlJissyuNendo"
								size="4"
								value="#{pc_Xrd00201.propJissyuNendo.dateValue}">
									<hx:inputHelperAssist errorClass="inputText_Error" imeMode="inactive" promptCharacter="_" />
								<f:convertDateTime pattern="yyyy" />
							</h:inputText>
								
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_b"><!-- 納入期限 -->
							<h:outputText
								styleClass="outputText"
								id="lblNonyuLimit"
								value="#{pc_Xrd00201.propNonyuLimit.labelName}"
								style="#{pc_Xrd00201.propNonyuLimit.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText
								styleClass="inputText"
								id="htmlNonyuLimit"
								size="11"
								value="#{pc_Xrd00201.propNonyuLimit.dateValue}">
									<f:convertDateTime />
									<hx:inputHelperDatePicker />
									<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_c"><!-- 有効期限 -->
							<h:outputText
								styleClass="outputText"
								id="lblYukouLimit"
								value="#{pc_Xrd00201.propYukouLimit.labelName}"
								style="#{pc_Xrd00201.propYukouLimit.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText
								styleClass="inputText"
								id="htmlYukouLimit"
								size="11"
								value="#{pc_Xrd00201.propYukouLimit.dateValue}">
									<f:convertDateTime />
									<hx:inputHelperDatePicker />
									<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_d"><!-- 処理区分指定 -->
							<h:outputText 
								styleClass="outputText"
								id="lblSyoriKbn" 
								value="#{pc_Xrd00201.propSyoriKbn.labelName}"
								style="#{pc_Xrd00201.propSyoriKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD nowrap>
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" 
								id="htmlSyoriKbn"
								value="#{pc_Xrd00201.propSyoriKbn.checked}"
								style="#{pc_Xrd00201.propSyoriKbn.style}">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" 
								id="text9"
								value="チェックのみ（データの登録/更新は行いません）">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap width="30%" class="v_e"><!-- チェックリスト出力指定 -->
							<h:outputText
								styleClass="outputText" 
								id="text4" 
								value="チェックリスト出力指定">
							</h:outputText>
						</TH>
						<TD nowrap width="70%">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
								<TBODY>
									<TR>
										<TD>
											<h:selectBooleanCheckbox 
												styleClass="selectBooleanCheckbox"
												id="htmlChkListNormal"
												value="#{pc_Xrd00201.propChkListNormal.checked}"
												style="#{pc_Xrd00201.propChkListNormal.style}">
											</h:selectBooleanCheckbox>
											<h:outputText
												styleClass="outputText" 
												id="text5" 
												value="正常データ">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TD>
											<h:selectBooleanCheckbox 
												styleClass="selectBooleanCheckbox"
												id="htmlChkListError"
												value="#{pc_Xrd00201.propChkListError.checked}"
												style="#{pc_Xrd00201.propChkListError.style}">
											</h:selectBooleanCheckbox>
											<h:outputText
												styleClass="outputText" 
												id="text7" 
												value="エラーデータ">
											</h:outputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR noshade class="hr">
			<TABLE border="0" cellpadding="0" cellspacing="0" width="650" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton 
								type="submit" 
								value="実行"
								styleClass="commandExButton_dat" 
								id="exec"
								action="#{pc_Xrd00201.doExecAction}"
								confirm="#{msg.SY_MSG_0001W}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

