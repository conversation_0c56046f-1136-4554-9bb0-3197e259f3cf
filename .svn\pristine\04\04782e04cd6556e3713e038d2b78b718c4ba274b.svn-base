<%-- 
	通知書文面設定（通知書選択）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmz00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmz00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmz00801.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kmz00801.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kmz00801.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kmz00801.screenName}"></h:outputText>
</div>			

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻るボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
<!-- ↑ここに戻るボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE width="100%" border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD>
				<BR><BR><BR><BR><BR><BR><BR><BR>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="420">
					<TBODY>
						<TR>
							<TD align="right" nowrap class="outputText" width="100%"><h:outputText
								styleClass="outputText" id="lblBunmenListCnt" value="#{pc_Kmz00801.propBunmenKbnlist.listCount}"></h:outputText>件</TD>
						</TR>
						<TR>
							<TD>
							<div class="listScroll" id="listScroll" style="height: 70px">
								<h:dataTable border="1" cellpadding="2" cellspacing="0"
								headerClass="headerClass" footerClass="footerClass"
								columnClasses="columnClass1"
								rowClasses="#{pc_Kmz00801.propBunmenKbnlist.rowClasses}"
								styleClass="meisai_scroll" id="htmlBunmenList" var="varlist"
								value="#{pc_Kmz00801.propBunmenKbnlist.list}">
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText id="lblBunmenKbnName_head" styleClass="outputText"
											value="文面区分"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="lblBunmenKbnName_list"
										value="#{varlist.bunmenKbnName.displayValue}"
										title="#{varlist.bunmenKbnName.value}"></h:outputText>
									<f:attribute value="300" name="width" />
									<f:attribute value="true" name="nowrap" />
								</h:column>
								<h:column id="column2">
									<f:facet name="header">
										<h:outputText styleClass="outputText" id="lblSetteiSu_head"
											value="設定数"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="lblSetteiSu_list"
										value="#{varlist.setteiSu}"></h:outputText>
									<f:attribute value="90" name="width" />
									<f:attribute value="text-align: right" name="style" />
								</h:column>
								<h:column id="column3">
									<f:facet name="header">
									</f:facet>
									<hx:commandExButton type="submit" value="設定"
										styleClass="commandExButton" id="button_settei"
										action="#{pc_Kmz00801.doSetteiAction}"></hx:commandExButton>
									<f:attribute value="30" name="width" />
								</h:column>
							</h:dataTable>
							</div>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<h:inputHidden id="htmlHidScroll" value="#{pc_Kmz00801.propBunmenKbnlist.scrollPosition}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>

</HTML>
