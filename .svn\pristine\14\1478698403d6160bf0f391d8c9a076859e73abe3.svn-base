<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kab00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<style type="text/css">
<!--
 .setWidth TD {width: 110px; white-space: nowrap;}
 .setWidth2 TD {width: 100px; white-space: nowrap;}
 -->
</style>

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kab00201.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kab00201.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kab00201.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kab00201.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトのため全角１文字 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<!-- 入力ファイル種類 -->
								<TR>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblInputFileType"
										value="#{pc_Kab00201.propInputFileType.name}"
										style="#{pc_Kab00201.propInputFileType.labelStyle}"></h:outputText></TH>
									<TD class="">
									<TABLE>
										<TBODY>
											<TR>
												<TD class="clear_border" 
													style="text-align: right;"><h:outputText
													styleClass="outputText" id="lblInputFileTypeLbl1"
													value="#{pc_Kab00201.propInputFileTypeLbl1.name}"
													style="#{pc_Kab00201.propInputFileTypeLbl1.labelStyle}"></h:outputText>
												</TD>
												<TD class="clear_border">
													<h:selectOneMenu
														styleClass="selectOneMenu" id="htmlInputFileType1" tabindex="1"
														style="width:150px;"
														value="#{pc_Kab00201.propInputFileType1.stringValue}"
														disabled="#{pc_Kab00201.propInputFileType1.disabled}"
														readonly="#{pc_Kab00201.propInputFileType1.readonly}">
														<f:selectItems value="#{pc_Kab00201.propInputFileType1.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
											<TR>
												<TD class="clear_border" 
													style="text-align: right;"><h:outputText
													styleClass="outputText" id="lblInputFileTypeLbl2"
													value="#{pc_Kab00201.propInputFileTypeLbl2.name}"
													style="#{pc_Kab00201.propInputFileTypeLbl2.labelStyle}"></h:outputText>
												</TD>
												<TD class="clear_border">
													<h:selectOneMenu
														styleClass="selectOneMenu" id="htmlInputFileType2" tabindex="2"
														style="width:150px;"
														value="#{pc_Kab00201.propInputFileType2.stringValue}"
														disabled="#{pc_Kab00201.propInputFileType2.disabled}"
														readonly="#{pc_Kab00201.propInputFileType2.readonly}">
														<f:selectItems value="#{pc_Kab00201.propInputFileType2.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<!-- 入力ファイル -->
								<TR>
									<TH class="v_b">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFile"
													value="#{pc_Kab00201.propInputFile.name}"
													style="#{pc_Kab00201.propInputFile.labelStyle}"></h:outputText></TH>
											</TR>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFilePre"
													value="#{pc_Kab00201.propInputFilePre.name}"
													style="#{pc_Kab00201.propInputFilePre.labelStyle}"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									</TH>
									<TD class="">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border"><hx:fileupload
													styleClass="fileupload" id="htmlInputFile" size="50"
													value="#{pc_Kab00201.propInputFile.value}" tabindex="3"
													style="width:530px">
													<hx:fileProp name="fileName"
														value="#{pc_Kab00201.propInputFile.fileName}" />
													<hx:fileProp name="contentType"
														value="#{pc_Kab00201.propInputFile.contentType}" />
												</hx:fileupload></TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:outputText
													styleClass="outputText" id="htmlInputFilePre"
													value="#{pc_Kab00201.propInputFilePre.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<!-- データ登録区分指定 -->
								<TR>
									<TH class="v_c"><h:outputText
										styleClass="outputText" id="lblRegKbn"
										value="#{pc_Kab00201.propRegKbn.name}"
										style="#{pc_Kab00201.propRegKbn.labelStyle}"></h:outputText></TH>
									<TD class=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRegKbn" tabindex="4"
										layout="pageDirection"
										value="#{pc_Kab00201.propRegKbn.stringValue}"
										readonly="#{pc_Kab00201.propRegKbn.readonly}"
										disabled="#{pc_Kab00201.propRegKbn.disabled}">
										<f:selectItems value="#{pc_Kab00201.propRegKbn.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<!-- 処理区分指定 -->
								<TR>
									<TH class="v_d"><h:outputText
										styleClass="outputText" id="lblSyoriKbnL"
										value="#{pc_Kab00201.propSyoriKbn.name}"
										style="#{pc_Kab00201.propSyoriKbn.labelStyle}"></h:outputText></TH>
									<TD class="">
									<TABLE border="0" class="">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
													tabindex="5" value="#{pc_Kab00201.propSyoriKbn.checked}"
													disabled="#{pc_Kab00201.propSyoriKbn.disabled}"
													readonly="#{pc_Kab00201.propSyoriKbn.readonly}"></h:selectBooleanCheckbox>
													<h:outputText
													styleClass="outputText" id="lblSyoriKbn"
													value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<!-- チェックリスト出力指定 -->
								<TR>
									<TH class="v_e"><h:outputText
										styleClass="outputText" id="lblChkList"
										value="#{pc_Kab00201.propChkListNormal.name}"
										style="#{pc_Kab00201.propChkListNormal.labelStyle}"></h:outputText></TH>
									<TD>
									<TABLE border="0" class="">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlChkListNormal"
													tabindex="6"
													value="#{pc_Kab00201.propChkListNormal.checked}"
													disabled="#{pc_Kab00201.propChkListNormal.disabled}"
													readonly="#{pc_Kab00201.propChkListNormal.readonly}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListNormal" 
													value="正常データ"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlChkListError"
													tabindex="7"
													value="#{pc_Kab00201.propChkListError.checked}"
													readonly="#{pc_Kab00201.propChkListError.readonly}"
													disabled="#{pc_Kab00201.propChkListError.disabled}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListError"
													value="エラーデータ"></h:outputText></TD>
											</TR>
											<TR>
												<TD class="clear_border">
													<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlChkListWarning" tabindex="8"
													value="#{pc_Kab00201.propChkListWarning.checked}"
													disabled="#{pc_Kab00201.propChkListWarning.disabled}"
													readonly="#{pc_Kab00201.propChkListWarning.readonly}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListWarning"
													value="ワーニングデータ"></h:outputText>
                                                </TD>
											</TR>
										</TBODY>
									</TABLE>
                                  </TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="700" border="0" class="button_bar" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="入力項目指定"
										styleClass="commandExButton_etc" id="setinput" tabindex="9"
										action="#{pc_Kab00201.doSetinputAction}"
										disabled="#{pc_Kab00201.propSetinput.disabled}"
										rendered="#{pc_Kab00201.propSetinput.rendered}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_dat" id="exec" tabindex="10"
										action="#{pc_Kab00201.doExecAction}"
										disabled="#{pc_Kab00201.propExec.disabled}"
										rendered="#{pc_Kab00201.propExec.rendered}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

