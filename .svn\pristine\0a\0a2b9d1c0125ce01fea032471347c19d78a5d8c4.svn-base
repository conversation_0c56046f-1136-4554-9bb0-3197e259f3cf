<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob03103.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >


<SCRIPT type="text/javascript">


function func_1(thisObj, thisEvent) {
//use 'thisObj' to refer directly to this component instead of keyword 'this'
//use 'thisEvent' to refer to the event generated instead of keyword 'event'
indirectClick('button1');
}
function func_2(thisObj, thisEvent) {
//use 'thisObj' to refer directly to this component instead of keyword 'this'
//use 'thisEvent' to refer to the event generated instead of keyword 'event'
var changeFlg = document.getElementById('form1:changeFlg');
changeFlg.value = 'true';
}



function func_3(thisObj, thisEvent) {
//use 'thisObj' to refer directly to this component instead of keyword 'this'
//use 'thisEvent' to refer to the event generated instead of keyword 'event'


var changeFlg = document.getElementById('form1:changeFlg');
var changed = changeFlg.value;
if (changed == 'true') {

if ( confirm("編集中のデータが確定されていません。編集中のデータを無効にしてもよろしいですか？")) {
changeFlg.value ='false';
indirectClick('button2');

}

} else {

indirectClick('button2');
}
}

function funcJumpData(thisObj, thisEvent, id) {
  var args = new Array();
  args[0] = "";
  var changeFlg = document.getElementById('form1:changeFlg');
  var changed = changeFlg.value;
  if (changed == 'true') {
    if ( confirm(messageCreate(id, args))) {
      changeFlg.value ='false';
      indirectClick('jumpDataNo2');
    }
  } else {
    indirectClick('jumpDataNo2');
  }
}

</SCRIPT></HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cob03103.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">

<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Cob03103.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Cob03103.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Cob03103.screenName}"></h:outputText>
<hx:commandExButton type="submit" value="jumpDataNo2"
        styleClass="commandExButton" id="jumpDataNo2"
        action="#{pc_Cob03103.doJumpDataNo2Action}"></hx:commandExButton>
<hx:commandExButton type="submit" value="Submit"
        styleClass="commandExButton" id="button1" action="#{pc_Cob03103.doButton1Action}"></hx:commandExButton><hx:commandExButton
        type="submit" value="Submit" styleClass="commandExButton"
        id="button2" action="#{pc_Cob03103.doButton2Action}"></hx:commandExButton><h:inputHidden
        id="changeFlg" value="#{pc_Cob03103.propChangeFlg.stringValue}"></h:inputHidden></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
    <DIV class="head_button_area" >
    <!-- ↓ここに戻る／閉じるボタンを配置 -->
      <TABLE border="0" cellpadding="0" cellspacing="0" width="70" class="">
        <TBODY>
          <TR>
            <TD align="center" valign="middle" height="11"><hx:commandExButton
              type="submit" value="戻る" styleClass="commandExButton"
              id="returnDisp" action="#{pc_Cob03103.doReturnDispAction}"></hx:commandExButton></TD>
          </TR>
        </TBODY>
      </TABLE>
      <!-- ↑ここに戻る／閉じるボタンを配置 -->
      </DIV>
      <DIV id="content">
      <DIV class="column" align="center">
      <!-- ↓ここにコンポーネントを配置 -->
      <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
        <TBODY>
          <TR>
            <TD width="97%"><TABLE border="0" cellpadding="0"
              cellspacing="0" class="table" width="810">
              <TBODY>
                <TR>
                  <TH width="180" class="v_b"><h:outputText
                    styleClass="outputText" id="text3"
                    value="#{pc_Cob03103.propStuNo.labelName}"
                    style="#{pc_Cob03103.propStuNo.labelStyle}"></h:outputText></TH>
                  <TD width="85%"><h:outputText styleClass="outputText"
                    id="text4" value="#{pc_Cob03103.propStuNo.stringValue}"></h:outputText></TD>
                </TR>
                <TR>
                  <TH width="300"  class="v_c"><h:outputText
                    styleClass="outputText" id="text5"
                    value="#{pc_Cob03103.propName.labelName}"
                    style="#{pc_Cob03103.propName.labelStyle}"></h:outputText></TH>
                  <TD><h:outputText styleClass="outputText"
                    id="text6" value="#{pc_Cob03103.propName.stringValue}"></h:outputText></TD>
                </TR>
              </TBODY>
            </TABLE>
            </TD>
          </TR>
        </TBODY>
      </TABLE>
      <HR noshade width="100%" class="hr">
      <TABLE border="0" cellpadding="0" cellspacing="0" width="810">
        <TBODY>
          <TR>
            <TD width="97%" colspan="2">
              <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
                <TBODY>
                  <TR>
                    <TD align="left" width="60%">
                      <TABLE border="0" cellpadding="0" cellspacing="0" class="table">
                        <TBODY>
                          <TR>
                            <TH class="v_d" width="110"><h:outputText
                              styleClass="outputText" id="text7"
                              value="#{pc_Cob03103.propFreTsy.labelName}"
                              style="#{pc_Cob03103.propFreTsy.labelStyle}"></h:outputText></TH>
                            <TD>
                            <h:selectOneMenu styleClass="selectOneMenu" id="htmlFreTsy"
                              value="#{pc_Cob03103.propFreTsy.value}"
                              style="#{pc_Cob03103.propFreTsy.style}"
                              disabled="#{pc_Cob03103.propFreTsy.disabled}">
                              <f:selectItems value="#{pc_Cob03103.propFreTsy.list}" />
                            </h:selectOneMenu></TD>
                          </TR>
                        </TBODY>
                      </TABLE>
                    </TD>
                    <TD align="right" width="40%">
                      <TABLE align="right" width="100%" border="0" cellpadding="0" cellspacing="0">
                        <TBODY>
                          <TR>
                            <TD align="right" width="80%">
                              <hx:commandExButton type="submit" styleClass="commandExButton" id="select"
                              disabled="#{pc_Cob03103.propSelect.disabled}" value="選択"
                              action="#{pc_Cob03103.doSelectAction}"></hx:commandExButton>
                            </TD>
                            <TD align="right" width="20%">
                              <hx:commandExButton type="submit" value="解除" styleClass="commandExButton"
                              id="unlock" disabled="#{pc_Cob03103.propUnlock.disabled}"
                              onclick="return func_3(this, event);"></hx:commandExButton>
                            </TD>
                          </TR>
                        </TBODY>
                      </TABLE>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
            </TD>
          </TR>
          <TR>
            <TD width="97%" align="center">
            <TABLE width="810" border="0" cellpadding="0" cellspacing="0">
              <TBODY>
                <TR>
                  <TD width="30%" align="left">
                  <TABLE border="0" cellpadding="0" cellspacing="0" class="table">
                    <TBODY>
                      <TR>
                        <TH class="v_e" valign="middle" align="left" width="110"><h:outputText
                          styleClass="outputText" id="text8"
                          value="#{pc_Cob03103.propListNo.labelName}"
                          style="#{pc_Cob03103.propListNo.labelStyle}"></h:outputText></TH>
                        <TD><hx:commandExButton
                          type="submit" value="#{pc_Cob03103.propToHead.name}"
                          styleClass="commandExButton" id="button5"
                          disabled="#{pc_Cob03103.propToHead.disabled}"
                          action="#{pc_Cob03103.doButton5Action}"
                          ></hx:commandExButton><hx:commandExButton
                          type="submit" value="#{pc_Cob03103.propBack.name}"
                          styleClass="commandExButton" id="button6"
                          disabled="#{pc_Cob03103.propBack.disabled}"
                          action="#{pc_Cob03103.doButton6Action}"
                          ></hx:commandExButton></TD>
                        <TD width="28" style="padding-right: 3px;text-align:right">   
                          <h:outputText
                          styleClass="outputText" id="htmlInputNo"
                          value="#{pc_Cob03103.propListNo.stringValue}"></h:outputText></TD>
                        <TD>  
                          <hx:commandExButton
                          type="submit" value="#{pc_Cob03103.propForword.name}"
                          styleClass="commandExButton" id="button7"
                          disabled="#{pc_Cob03103.propForword.disabled}"
                          action="#{pc_Cob03103.doButton7Action}"
                          ></hx:commandExButton><hx:commandExButton
                          type="submit" value="#{pc_Cob03103.propToEnd.name}"
                          styleClass="commandExButton" id="button8"
                          disabled="#{pc_Cob03103.propToEnd.disabled}"
                          action="#{pc_Cob03103.doButton8Action}"
                          ></hx:commandExButton>
                        </TD>
                      </TR>
                    </TBODY>
                  </TABLE>
                  </TD>
                  <TD width="20%" align="left">
                  <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
                    <TBODY>
                      <TR>
                        <TD align="left"><h:inputText styleClass="inputText"
                          id="htmlFreDataNo" size="3"
                          disabled="#{pc_Cob03103.propFreDataNo.disabled}"
                          maxlength="#{pc_Cob03103.propFreDataNo.maxLength}"
                          readonly="#{pc_Cob03103.propFreDataNo.readonly}" tabindex="8"
                          value="#{pc_Cob03103.propFreDataNo.integerValue}"
                          style="#{pc_Cob03103.propFreDataNo.style}">
                          <hx:inputHelperAssist errorClass="inputText_Error"
                            promptCharacter="_" />
                          <f:convertNumber pattern="##0" />
                        </h:inputText><hx:commandExButton type="submit" value="ジャンプ"
                          styleClass="commandExButton" id="jumpDataNo" tabindex="9"
                          disabled="#{pc_Cob03103.propFreDataNo.disabled}"
                          onclick="return funcJumpData(this, event, '#{msg.SY_MSG_0014W}');"></hx:commandExButton></TD>
                      </TR>
                    </TBODY>
                  </TABLE>
                  </TD>
                  <TD width="15%" align="left">
                  <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
                    <TBODY>
                      <TR>
                        <TD align="right"  width="100">
                        <hx:commandExButton
                          type="submit" value="自動採番" styleClass="commandExButton"
                          id="autosearch"
                          disabled="#{pc_Cob03103.propAutosearch.disabled}"
                          action="#{pc_Cob03103.doAutosearchAction}"></hx:commandExButton></TD>
                      </TR>
                    </TBODY>
                  </TABLE>
                  </TD>
                  <TD width="35%" align="right" valign="bottom"><h:outputText
              styleClass="outputText" id="text99" style="font-size: 8pt"
              value="データNo登録件数"></h:outputText><h:outputText
              styleClass="outputText" id="htmlCount" style="font-size: 8pt" value="#{pc_Cob03103.propCount.stringValue}"></h:outputText><h:outputText
              styleClass="outputText" id="text10" style="font-size: 8pt"
              value="件"></h:outputText> </TD>
                </TR>
              </TBODY>
            </TABLE>
            </TD>
          </TR>
        </TBODY>
      </TABLE>
      <HR width="100%" noshade class="hr">
      <TABLE width="810" border="0" cellpadding="0" cellspacing="0">
        <TBODY>
          <TR>
            <TD align="left">
		    <div><h:dataTable
		        border="0" 
		        headerClass="headerClass" 
		        columnClasses="columnClass1"
		        styleClass="meisai_scroll" id="tableHeader1"
		        style="text-align: center;border-bottom-color:transparent; border-bottom-width:0;">
		     <h:column id="column1">
                 <f:facet name="header">
                     <h:outputText id="text120" styleClass="outputText" value="項目№"></h:outputText>
                 </f:facet>
		         <f:attribute value="109" name="width" />
		     </h:column>
		     <h:column id="column2">
                 <f:facet name="header">
                     <h:outputText styleClass="outputText"
                         value="#{pc_Cob03103.propOutputName.name}" id="text11"></h:outputText>
                 </f:facet>
				<f:attribute value="223" name="width" />
		     </h:column>
		     <h:column id="column3">
                 <f:facet name="header">
                     <h:outputText styleClass="outputText"
                         value="#{pc_Cob03103.propValue.name}"
                         id="htmlValuetitle"
                         style="#{pc_Cob03103.propValue.labelStyle}"></h:outputText>
                 </f:facet>
                <f:attribute value="464" name="width" />
		     </h:column>
             <h:column id="column4">
                 <f:attribute value="18" name="width" />
             </h:column>
		    </h:dataTable>
	     	</div>    
            <div class="listScroll" style="height:316px;"><h:dataTable
              border="0" cellpadding="2" cellspacing="0"
              headerClass="headerClass" footerClass="footerClass"
              rowClasses="#{pc_Cob03103.propKmiSnfr.rowClasses}"
              styleClass="meisai_scroll" id="table1" 
              value="#{pc_Cob03103.propKmiSnfr.list}" var="varlist"
              first="#{pc_Cob03103.propKmiSnfr.first}"
              rows="#{pc_Cob03103.propKmiSnfr.rows}" style="text-align: center">
              <h:column id="column1">
                <h:outputText styleClass="outputText" id="text13"
                  value="#{varlist.freKomokNo}"></h:outputText>
                <f:attribute value="110" name="width" />
                <f:attribute value="center" name="align" />
                <f:attribute value="middle" name="valign" />
                <f:attribute value="text-align: right; vertical-align: middle"
                  name="style" />
              </h:column>
              <h:column id="column2">
                <hx:jspPanel id="jspPanelCol21" rendered="#{varlist.inputStyleKbn == '1'}" >    									                                                                 
                    <TABLE class="table">
                        <TBODY>
                            <TR>
                                <TH nowrap class="v_c" style="border: none; background-color: transparent; margin: 0;  padding: 0; width: 220px">
                                    <h:outputText styleClass="outputText"
                                        id="lblFreKomokName1"
                                        value="#{varlist.propColInputText.labelName}"
                                        style="#{varlist.propColInputText.labelStyle}; color: black; ">
                                    </h:outputText>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE> 
                </hx:jspPanel>   
                <hx:jspPanel id="jspPanelCol22" rendered="#{varlist.inputStyleKbn == '2'}" >	                                                                 
                    <TABLE class="table">
                        <TBODY>
                            <TR>
                                <TH nowrap class="v_c" style="border: none; background-color: transparent; margin: 0;  padding: 0; width: 220px">
                                    <h:outputText styleClass="outputText"
                                        id="lblFreKomokName2"
                                        value="#{varlist.propColInputTextInt.labelName}"
                                        style="#{varlist.propColInputTextInt.labelStyle}; color: black; ">
                                    </h:outputText>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE>
                </hx:jspPanel>                                      
                <hx:jspPanel id="jspPanelCol23" rendered="#{varlist.inputStyleKbn == '3'}" >                                    
                    <TABLE class="table">
                        <TBODY>
                            <TR>
                                <TH nowrap class="v_c" style="border: none; background-color: transparent; margin: 0;  padding: 0; width: 220px">
                                    <h:outputText styleClass="outputText"
                                        id="lblFreKomokName3"
                                        value="#{varlist.propColSelectOneMenu.labelName}"
                                        style="#{varlist.propColSelectOneMenu.labelStyle}; color: black; ">
                                    </h:outputText>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE>
                </hx:jspPanel>
                <hx:jspPanel id="jspPanelCol24" rendered="#{varlist.inputStyleKbn == '4'}" >
                    <TABLE class="table" >
                        <TBODY>
                            <TR >
                                <TH nowrap class="v_c" style="border: none; background-color: transparent; margin: 0;  padding: 0; width: 220px">
                                    <h:outputText styleClass="outputText"
                                        id="lblFreKomokName4"
                                        value="#{varlist.propColInputTextarea.labelName}"
                                        style="#{varlist.propColInputTextarea.labelStyle}; color: black; ">
                                    </h:outputText>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE>
                </hx:jspPanel>                                    
                <hx:jspPanel id="jspPanelCol25" rendered="#{varlist.inputStyleKbn == '5'}" >                                                                                                         
                    <TABLE class="table">
                        <TBODY>
                            <TR>
                                <TH nowrap class="v_c" style="border: none; background-color: transparent; margin: 0;  padding: 0; width: 220px">
                                    <h:outputText styleClass="outputText"
                                        id="lblFreKomokName5"
                                        value="#{varlist.propColInputTextDate.labelName}"
                                        style="#{varlist.propColInputTextDate.labelStyle}; color: black; ">
                                    </h:outputText>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE> 
                </hx:jspPanel>   
				<f:attribute value="220" name="width" />
              </h:column>
              <h:column id="column3">
                <hx:jspPanel id="jspPanelCol31" rendered="#{varlist.inputStyleKbn == '1'}" >
                    <h:inputText styleClass="inputText"
                        id="htmlColInputText"                                               
                        value="#{varlist.propColInputText.stringValue}"
                        style="#{varlist.propColInputText.style};  width: #{varlist.inputWidth}; ime-mode: #{varlist.imeMode}"
                        maxlength="#{varlist.propColInputText.maxLength}"
                        onchange="return func_2(this, event);">
                        <hx:inputHelperAssist errorClass="inputText_Error" />
                    </h:inputText>                        
                </hx:jspPanel>
                <hx:jspPanel id="jspPanelCol32" rendered="#{varlist.inputStyleKbn == '2'}" >
                    <h:inputText styleClass="inputText"
                        id="htmlColInputTextInt"                                
                        value="#{varlist.propColInputTextInt.stringValue}"
                        style="#{varlist.propColInputTextInt.style};  width: #{varlist.inputWidth}; ime-mode: #{varlist.imeMode} "
                        maxlength="#{varlist.propColInputTextInt.maxLength}"
                        onchange="return func_2(this, event);">
                    </h:inputText>                 
                </hx:jspPanel>
                <hx:jspPanel id="jspPanelCol33" rendered="#{varlist.inputStyleKbn == '3'}" > 
                    <h:selectOneMenu styleClass="selectOneMenu"
                        id="htmlColSelectOneMenu"
                        value="#{varlist.propColSelectOneMenu.value}"
                        onchange="return func_2(this, event);">
                        <f:selectItems value="#{varlist.propColSelectOneMenu.list}" />
                    </h:selectOneMenu>               
                </hx:jspPanel>                                    
                <hx:jspPanel id="jspPanelCol34" rendered="#{varlist.inputStyleKbn == '4'}" >  
                    <h:inputTextarea styleClass="inputTextarea"
                        id="htmlColInputTextarea"
                        value="#{varlist.propColInputTextarea.stringValue}"
                        style="width: #{varlist.inputWidth}; height: #{varlist.inputHeight}; ime-mode: #{varlist.imeMode}"
                        onchange="return func_2(this, event);">
                    </h:inputTextarea>                             
                </hx:jspPanel>                             
                <hx:jspPanel id="jspPanelCol35" rendered="#{varlist.inputStyleKbn == '5'}" > 
                    <h:inputText styleClass="inputText"
                        id="htmlColInputTextDate"
                        size="10"
                        value="#{varlist.propColInputTextDate.dateValue}"
                        style="#{varlist.propColInputTextDate.style}; "
                        onblur="return func_2(this, event);"> <f:convertDateTime />
                        <hx:inputHelperAssist imeMode="inactive" errorClass="inputText_Error" promptCharacter="_" />
                    </h:inputText>                                                         
                </hx:jspPanel> 
                <f:attribute value="470" name="width" />
              </h:column>
            </h:dataTable></div>
            </TD>
          </TR>
        </TBODY>
      </TABLE>
      <BR>
      <TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
        width="100%">
        <TBODY>
          <TR>
            <TD align="center" valign="middle"><hx:commandExButton
              type="submit" value="確定" styleClass="commandExButton_dat"
              id="register" action="#{pc_Cob03103.doRegisterAction}"
              disabled="#{pc_Cob03103.propRegister.disabled}"
              confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton> <hx:commandExButton
              type="submit" value="削除" styleClass="commandExButton_dat"
              id="delete" action="#{pc_Cob03103.doDeleteAction1}"
              disabled="#{pc_Cob03103.propDelete.disabled}"
              confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton></TD>
          </TR>
        </TBODY>
      </TABLE>
      <!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
    </h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

