<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/PNsa0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pNsa0101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
try {
	var noSelectMsg = document.getElementById("form1:htmlNoSelectMsgHidden").value;
	var retFieldName = document.getElementById("form1:htmlRetFieldName").value;
	var buttonid = thisObj.id;
// 正規表現にて選択行のindex値を取得
	var point_start = buttonid.search(":[0-9]*:");
	var point_end = buttonid.search(":select");
	var index = buttonid.substring(0,point_end);
	var retValue = document.getElementById(index + ":htmlColToiCd").innerHTML;
	if (window.opener) {			
		window.opener.document.getElementById(retFieldName).value = retValue;
		window.opener.document.getElementById(retFieldName).onblur();
		self.close();
		return true;			
	} else {
		throw "";
	}
} catch (e) {
	setErrMsg("呼び出し元画面に値を返せません。");
}

}
function func_2(thisObj, thisEvent) {
	// 出身校名称を取得する
	var servlet = "rev/co/CoaSsnAJAX";
	var target = "form1:htmlSsnName";
	
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlSsnCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

}
function func_3(thisObj, thisEvent) {
	//ローカル用
	openModalWindow("", "PCoa0101", "<%=com.jast.gakuen.rev.co.PCoa0101.getWindowOpenOption()%>");
	setTarget("PCoa0101");
	return true;
	
	//server用（＜%=～%＞でエラー起こるので・・）
	//openModalWindow("", "PNsa0101", "status=yes,toolbar=no,menubar=no,location=no,height=550,width=700");

}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PNsa0101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
				styleClass="commandExButton" id="closeDisp"
				action="#{pc_PNsa0101.doCloseDispAction}" tabindex="18"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PCoa202.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PCoa202.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->　
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="600">
									<TABLE width="600" border="0" cellpadding="0" cellspacing="0"
										class="table">
										<TBODY>
											<TR>
												<TH style="" class="v_a" width="131"><h:outputText
													styleClass="outputText" id="lblToiNendo"
													value="#{pc_PNsa0101.propToiNendo.labelName}">
												</h:outputText></TH>
												<TD><h:inputText styleClass="inputText" id="htmlToiNendo"
													value="#{pc_PNsa0101.propToiNendo.dateValue}" size="4"
													tabindex="1"
													readonly="#{pc_PNsa0101.propToiNendo.readonly}"
													disabled="#{pc_PNsa0101.propToiNendo.disabled}"
													style="#{pc_PNsa0101.propToiNendo.style}">
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" imeMode="inactive" />
													<f:convertDateTime pattern="yyyy" type="date" />
												</h:inputText></TD>
												<TH class="v_b"><h:outputText styleClass="outputText"
													id="lblToiDate"
													value="#{pc_PNsa0101.propToiDate.labelName}"></h:outputText></TH>
												<TD><h:inputText styleClass="inputText"
													id="htmlToiDate"
													value="#{pc_PNsa0101.propToiDate.dateValue}"
													disabled="#{pc_PNsa0101.propToiDate.disabled}"
													readonly="#{pc_PNsa0101.propToiDate.readonly}" tabindex="2"
													style="#{pc_PNsa0101.propToiDate.style}" size="10">
													<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
											<TR>
												<TH class="v_c" width="131"><h:outputText
													styleClass="outputText" id="lblToiName" value="問合せ者氏名(全40)"></h:outputText></TH>
												<TD colspan="3" width="468">
												<TABLE width="474" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="289"><h:inputText styleClass="inputText"
																id="htmlToiName" size="45"
																disabled="#{pc_PNsa0101.propToiName.disabled}"
																maxlength="40"
																readonly="#{pc_PNsa0101.propToiName.readonly}"
																style="#{pc_PNsa0101.propToiName.style}"
																value="#{pc_PNsa0101.propToiName.stringValue}"
																tabindex="3"></h:inputText></TD>
															<TD width="185"><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlToiNameFindType"
																disabled="#{pc_PNsa0101.propToiNameFindType.disabled}"
																value="#{pc_PNsa0101.propToiNameFindType.stringValue}"
																tabindex="4" layout="lineDirection">
																<f:selectItem itemValue="1" itemLabel="前方一致" />
																<f:selectItem itemValue="2" itemLabel="部分一致" />
															</h:selectOneRadio></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_d" width="131"><h:outputText
													styleClass="outputText" id="lblToiCd"
													value="#{pc_PNsa0101.propToiCd.labelName}"></h:outputText></TH>
												<TD colspan="3" width="468">
												<TABLE width="473" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="307"><h:inputText styleClass="inputText"
																id="htmlToiCd" size="17"
																disabled="#{pc_PNsa0101.propToiCd.disabled}"
																readonly="#{pc_PNsa0101.propToiCd.readonly}"
																value="#{pc_PNsa0101.propToiCd.stringValue}"
																maxlength="#{pc_PNsa0101.propToiCd.maxLength}"
																tabindex="5" style="#{pc_PNsa0101.propToiCd.style}"></h:inputText></TD>
															<TD width="166"><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlToiCdFindType"
																disabled="#{pc_PCoa0101.propSsnCdFindType.disabled}"
																value="#{pc_PNsa0101.propToiCdFindType.stringValue}"
																tabindex="6"
																readonly="#{pc_PCoa0101.propSsnCd.readonly}"
																layout="lineDirection">
																<f:selectItem itemValue="1" itemLabel="前方一致" />
																<f:selectItem itemValue="2" itemLabel="部分一致" />
															</h:selectOneRadio></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="131"><h:outputText
													styleClass="outputText" id="lblToiKbn"
													value="#{pc_PNsa0101.propToiKbn.labelName}"></h:outputText></TH>
												<TD colspan="3" width="468">
												<TABLE width="450" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="200"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlToiKbn"
																style="width:270px;"
																value="#{pc_PNsa0101.propToiKbn.value}"
																disabled="#{pc_PNsa0101.propToiKbn.disabled}"
																readonly="#{pc_PNsa0101.propToiKbn.readonly}"
																tabindex="7">
																<f:selectItems value="#{pc_PNsa0101.propToiKbn.list}" />
															</h:selectOneMenu></TD>
															<TD width="250"></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_f" width="131"><h:outputText
													styleClass="outputText" id="lblBaitai" value="募集媒体"></h:outputText></TH>
												<TD colspan="3" width="468">
												<TABLE width="450" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="200"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlBaitai"
																style="width:400px;"
																disabled="#{pc_PNsa0101.propBaitai.disabled}"
																readonly="#{pc_PNsa0101.propBaitai.readonly}"
																tabindex="8" value="#{pc_PNsa0101.propBaitai.value}">
																<f:selectItems value="#{pc_PNsa0101.propBaitai.list}" />
															</h:selectOneMenu></TD>
															<TD width="250"></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_a" width="131"><h:outputText
													styleClass="outputText" id="lblOboSbt" value="応募種別"></h:outputText></TH>
												<TD colspan="3" width="468">
												<TABLE width="450" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="200"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlOboSbt"
																style="width:270px;"
																disabled="#{pc_PNsa0101.propOboSbt.disabled}"
																readonly="#{pc_PNsa0101.propOboSbt.readonly}"
																tabindex="9" value="#{pc_PNsa0101.propOboSbt.value}">
																<f:selectItems value="#{pc_PNsa0101.propOboSbt.list}" />
															</h:selectOneMenu></TD>
															<TD width="250"></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_b" width="131"><h:outputText
													styleClass="outputText" id="lblSsnCd" value="出身校コード(半6)"></h:outputText></TH>
												<TD colspan="3" width="468">
												<TABLE width="450" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="59"><h:inputText styleClass="inputText"
																id="htmlSsnCd"
																value="#{pc_PNsa0101.propSsnCd.stringValue}"
																disabled="#{pc_PNsa0101.propSsnCd.disabled}"
																readonly="#{pc_PNsa0101.propSsnCd.readonly}"
																tabindex="10" onblur="return func_2(this, event);"
																size="8" maxlength="#{pc_PNsa0101.propSsnCd.maxLength}"
																style="#{pc_PNsa0101.propSsnCd.style}"></h:inputText></TD>
															<TD width="391"><hx:commandExButton type="submit"
																styleClass="commandExButton_search" id="searchSsn"
																tabindex="13" onclick="return func_3(this, event);"
																action="#{pc_PNsa0101.doSearchSsnAction}"></hx:commandExButton><h:outputText
																styleClass="outputText" id="htmlSsnName"
																value="#{pc_PNsa0101.propSsnName.stringValue}"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH class="v_c" width="131"><h:outputText
													styleClass="outputText" id="lblSyussin" value="出身地"></h:outputText></TH>
												<TD colspan="3" width="468">
												<TABLE width="450" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD width="200"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlSyussin"
																style="width:200px;"
																value="#{pc_PNsa0101.propSyussin.value}" tabindex="14"
																readonly="#{pc_PNsa0101.propSyussin.readonly}"
																disabled="#{pc_PNsa0101.propSyussin.disabled}">
																<f:selectItems value="#{pc_PNsa0101.propSyussin.list}" />
															</h:selectOneMenu></TD>
															<TD width="250"></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD width="600">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="600" class="button_bar">
										<TBODY>
											<TR>
												<TD><hx:commandExButton type="submit" value="検索"
													styleClass="commandExButton_dat" id="search"
													action="#{pc_PNsa0101.doSearchAction}" tabindex="15">
												</hx:commandExButton><hx:commandExButton type="submit"
													value="クリア" styleClass="commandExButton_etc" id="clear"
													action="#{pc_PNsa0101.doClearAction}"
													onclick="return func_2(this, this)" tabindex="16">
												</hx:commandExButton></TD>
											</TR>
										</TBODY>
								</TABLE>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					</TD>
					</TR>
					<TR>
						<TD height="20">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600">
							<TBODY>
								<TR>
									<TD align="right" class="clear_border" width="100%"><h:outputText
										styleClass="outputText" id="htmlListCount"
										value="#{pc_PNsa0101.propTableList.listCount}">

									</h:outputText><h:outputText styleClass="outputText"
										id="lblCount" value="件"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="180">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="640">
							<TBODY>
								<TR>
									<TD width="40px"></TD>
									<TD >
									<DIV class="listScroll" style="height:187px;width:617px;" class="listScroll"
										align="center" ><h:dataTable border="0" cellpadding="2"
										cellspacing="0" headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_PNsa0101.propTableList.rowClasses}"
										styleClass="meisai_scroll" id="htmlTableList" width="600px"
										var="varlist" value="#{pc_PNsa0101.propTableList.list}">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="問合せ者番号"
													id="lblColToiCd"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColToiCd"
												value="#{varlist.toiCd}"></h:outputText>
											<f:attribute value="90" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="問合せ者氏名"
													id="lblColToiName"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColToiName"
												value="#{varlist.toiName.displayValue}"
												title="#{varlist.toiName.value}"></h:outputText>
											<f:attribute value="300" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="問合せ者区分内容"
													id="lblColToiKbn"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColToiKbn"
												value="#{varlist.toiKbn.displayValue}"
												title="#{varlist.toiKbn.value}"></h:outputText>
											<f:attribute value="150" name="width" />
										</h:column>

										<h:column id="column3">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="reset" value="選択"
												styleClass="commandExButton" id="select"
												onclick="return func_1(this, event);" tabindex="17"></hx:commandExButton>
											<f:attribute value="30" name="width" />
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="44"><h:inputHidden id="htmlToiNendoHidden"
							value="#{pc_PNsa0101.propToiNendoHidden.dateValue}"><f:convertDateTime /></h:inputHidden>
							<h:inputHidden
							id="htmlRetFieldName"
							value="#{pc_PNsa0101.propRetFieldName.stringValue}"></h:inputHidden>
							<h:inputHidden id="htmlNoSelectMsgHidden"
							value="#{pc_PNsa0101.propNoSelectMsgHidden.stringValue}"></h:inputHidden>
							<h:inputHidden id="htmlExecutableSearchHidden"
							value="#{pc_PNsa0101.propExecutableSearchHidden.integerValue}"><f:convertNumber /></h:inputHidden></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/childFooter.jsp" />
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

