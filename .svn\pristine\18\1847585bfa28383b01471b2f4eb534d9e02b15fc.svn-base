<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab00803T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>リース契約登録(契約情報)</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

var _lastKeiyakusakiCd;		// 前回契約先コード

function confirmOk() {
	var execKbn = document.getElementById("form1:htmlExecutableKbn").value;
	if (execKbn == "delete") {
		var status;
		var execDel = document.getElementById("form1:htmlExecutableDelete");
		status = execDel.value;
		
		if (status == "0") {
			// 関連テーブル削除OK
			execDel.value = "1";
			indirectClick("delete");
		} else if (status == "1") {
			// 仕訳作成済み削除OK
			execDel.value = "2";
			indirectClick("delete");
		}
	} else if (execKbn == "shiharaiInfo") {
		document.getElementById('form1:htmlExecutableShiharaiInfo').value = "1";
		indirectClick("shiharaiInfo");	
	}	
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableDelete').value = "0";
	document.getElementById('form1:htmlExecutableShiharaiInfo').value = "0";
	document.getElementById('form1:htmlExecutableKbn').value = "";
}

function func_1(thisObj, thisEvent) {

	var keiyakusakiCd = document.getElementById('form1:htmlKeiyakusakiCd').value;
	
	// 契約先コードを変更した場合、口座情報を初期化する
	if (_lastKeiyakusakiCd != keiyakusakiCd) {
		clearVal();
	}

	var servlet = 'rev/ka/KaCogToriKihonNameKozaAJAX';
	var target = '';
	var args = new Array();
	args['code1'] = thisObj.value;
	args['code2'] = document.getElementById('form1:htmlKaikeiNendHidden').value;
	args['code3'] = document.getElementById("form1:htmlKozaIdNoHidden").value;
	var methodName = 'callBackMethod';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, methodName);	
} 

function callBackMethod(value) {
	var keiyakusakiCd = document.getElementById('form1:htmlKeiyakusakiCd').value;
	var nameObj = document.getElementById('form1:searchKoza');
		
	var keiyakusakiName	= value['key1'];
	var shokuchiIkkenKbn = value['key2'];

	document.getElementById("form1:htmlKeiyakusakiName").innerHTML = keiyakusakiName;
	document.getElementById("form1:htmlKeiyakusakiName").title = keiyakusakiName;
	
	// 検索結果が存在しない場合
	if (keiyakusakiName == "null" || keiyakusakiName == "") {
		elementDisabled(nameObj, true);		// 口座検索ボタンを無効にする
		clearVal();							// 口座情報をクリアする
		
	// 検索結果が存在する、かつ諸口一見区分が設定されている場合
	} else if (shokuchiIkkenKbn != "null" && shokuchiIkkenKbn != "") {
		elementDisabled(nameObj, true);		// 口座検索ボタンを無効にする
		clearVal();							// 口座情報をクリアする
	
	// 検索結果が存在する、かつ諸口一見区分が未設定の場合		
	} else {
		elementDisabled(nameObj, false);	// 口座検索ボタンを有効にする
		setVal(value);						// 口座情報をセットする
	}
	
	_lastKeiyakusakiCd = keiyakusakiCd;
}

// 口座情報をクリアします
function clearVal() {
	document.getElementById("form1:htmlKozaIdNoHidden").value 		= "";
	document.getElementById("form1:htmlBnkCd").innerHTML 			= "";
	document.getElementById("form1:htmlBnkName").innerHTML 			= "";
	document.getElementById("form1:htmlStnCd").innerHTML 			= "";
	document.getElementById("form1:htmlStnName").innerHTML 			= "";
	document.getElementById("form1:htmlYokinSmkName").innerHTML 	= "";
	document.getElementById("form1:htmlKozaNo").innerHTML 			= "";
	document.getElementById("form1:htmlKozaNameKana").innerHTML 	= "";
	// マウスオーバー表示用
	document.getElementById("form1:htmlBnkName").title 				= "";
	document.getElementById("form1:htmlStnName").title 				= "";
	document.getElementById("form1:htmlYokinSmkName").title 		= "";
	document.getElementById("form1:htmlKozaNo").title 				= "";
	document.getElementById("form1:htmlKozaNameKana").title 		= "";
}

// Ajaxで取得した口座情報をセットします
function setVal(value) {
	document.getElementById("form1:htmlKozaIdNoHidden").value 		= value['kozaIdNoHidden'];
	document.getElementById("form1:htmlBnkCd").innerHTML 			= value['bnkCd'];
	document.getElementById("form1:htmlBnkName").innerHTML 			= value['bnkName'];
	document.getElementById("form1:htmlStnCd").innerHTML 			= value['stnCd'];
	document.getElementById("form1:htmlStnName").innerHTML 			= value['stnName'];
	document.getElementById("form1:htmlYokinSmkName").innerHTML 	= value['yokinSmkName'];
	document.getElementById("form1:htmlKozaNo").innerHTML 			= value['kozaNo'];
	document.getElementById("form1:htmlKozaNameKana").innerHTML 	= value['kozaNameKana'];	
	// マウスオーバー表示用
	document.getElementById("form1:htmlBnkName").title 				= value['bnkName'];
	document.getElementById("form1:htmlStnName").title 				= value['stnName'];
	document.getElementById("form1:htmlYokinSmkName").title 		= value['yokinSmkName'];
	document.getElementById("form1:htmlKozaNo").title 				= value['kozaNo'];
	document.getElementById("form1:htmlKozaNameKana").title 		= value['kozaNameKana'];
}

function loadFunc() {
	_lastKeiyakusakiCd = document.getElementById('form1:htmlKeiyakusakiCd').value;
	func_1(document.getElementById('form1:htmlKeiyakusakiCd'), '');
}

window.attachEvent("onload", attachFormatNumber);
window.attachEvent("onload", loadFunc);
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="return loadFunc();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kab00803T01.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">
	<!-- ヘッダーインクルード -->
	<jsp:include page="../inc/header.jsp" />

	<!-- ヘッダーへのデータセット領域 -->
	<div style="display:none;"><hx:commandExButton type="submit"
		value="閉じる" styleClass="commandExButton" id="closeDisp"
		action="#{pc_Kab00803T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
		styleClass="outputText" id="htmlFuncId"
		value="#{pc_Kab00803T01.funcId}"></h:outputText> <h:outputText
		styleClass="outputText" id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
		styleClass="outputText" id="htmlScrnName"
		value="#{pc_Kab00803T01.screenName}"></h:outputText></div>

	<!--↓outer↓-->
	<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
		id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText></FIELDSET>

	<!--↓content↓-->
	<DIV class="head_button_area">
	<!-- ↓ここに戻る／閉じるボタンを配置 --> 
	<hx:commandExButton
		type="submit" value="戻る" styleClass="commandExButton"
		id="returnDisp" action="#{pc_Kab00803T01.doReturnDispAction}"
		rendered="#{pc_Kab00803T01.kab00803.propReturnDisp.rendered}" tabindex="27"></hx:commandExButton>
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	<DIV id="content">
	<DIV class="column" align="center">
	
	<!-- ↓ここにコンポーネントを配置 -->
	
	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="880" style="margin-top: 20px;">
		<TBODY>
			<TR>
				<TH class="v_a" width="140"><h:outputText styleClass="outputText"
							id="lblLeaseNo"
							style="#{pc_Kab00803T01.kab00803.propLeaseNo.labelStyle}"
							value="#{pc_Kab00803T01.kab00803.propLeaseNo.labelName}"></h:outputText></TH>
				<TD><h:inputText styleClass="inputText" id="htmlLeaseNo" size="20"
							style="#{pc_Kab00803T01.kab00803.propLeaseNo.style}"
							value="#{pc_Kab00803T01.kab00803.propLeaseNo.stringValue}"
							readonly="#{pc_Kab00803T01.kab00803.propLeaseNo.readonly}"
							maxlength="#{pc_Kab00803T01.kab00803.propLeaseNo.maxLength}"
							tabindex="1"
							disabled="#{pc_Kab00803T01.kab00803.propLeaseNo.disabled}"></h:inputText></TD>
			</TR>
		</TBODY>
	</TABLE>		
	
	<TABLE border="0" cellpadding="0" cellspacing="0" width="880" height="407" style="margin-top: 10px;">
		<TBODY>
			<TR>
				<TD align="left" height="27">
					<hx:commandExButton type="submit" style="width: 164px" value="契約情報"
							styleClass="tab_head_on" id="keiyakuInfo" tabindex="2"></hx:commandExButton><hx:commandExButton
							type="submit" style="width: 164px" value="管理情報"
							styleClass="tab_head_off" id="kanriInfo"
							action="#{pc_Kab00803T01.doKanriInfoAction}" tabindex="3">
						</hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD height="100%" align="left" valign="top">
				<TABLE border="1" cellpadding="20" cellspacing="0" height="380" width="880" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center"  valign="top">
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
									width="860" style="margin-top: 20px;">
									<TBODY>
										<TR>
											<TH class="v_b" width="140" colspan="2"><h:outputText styleClass="outputText"
												id="lblKeiyakuDate"
												value="#{pc_Kab00803T01.propKeiyakuDate.labelName}"
												style="#{pc_Kab00803T01.propKeiyakuDate.labelStyle}"></h:outputText>
											</TH>
											<TD colspan="3" width="720"><h:inputText styleClass="inputText"
												id="htmlKeiyakuDate" size="10"
												value="#{pc_Kab00803T01.propKeiyakuDate.dateValue}"
												style="#{pc_Kab00803T01.propKeiyakuDate.style}"
												readonly="#{pc_Kab00803T01.propKeiyakuDate.readonly}" tabindex="4">
												<f:convertDateTime pattern="yyyy/MM/dd" />
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText></TD>
										</TR>
										<TR>
											<TH class="v_c" colspan="2"><h:outputText styleClass="outputText"
												id="lblKeiyakuBukkenName"
												value="#{pc_Kab00803T01.propKeiyakuBukkenName.labelName}"
												style="#{pc_Kab00803T01.propKeiyakuBukkenName.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
												id="htmlKeiyakuBukkenName" size="100"
												value="#{pc_Kab00803T01.propKeiyakuBukkenName.stringValue}"
												style="#{pc_Kab00803T01.propKeiyakuBukkenName.style}"
												maxlength="#{pc_Kab00803T01.propKeiyakuBukkenName.maxLength}"
												readonly="#{pc_Kab00803T01.propKeiyakuBukkenName.readonly}" tabindex="5"></h:inputText></TD>
										</TR>
										<TR>
											<TH class="v_d" colspan="2"><h:outputText styleClass="outputText"
												id="lblKeiyakuBukkenNameRyak"
												value="#{pc_Kab00803T01.propKeiyakuBukkenNameRyak.labelName}"
												style="#{pc_Kab00803T01.propKeiyakuBukkenNameRyak.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
												id="htmlKeiyakuBukkenNameRyak" size="20"
												value="#{pc_Kab00803T01.propKeiyakuBukkenNameRyak.stringValue}"
												style="#{pc_Kab00803T01.propKeiyakuBukkenNameRyak.style}"
												maxlength="#{pc_Kab00803T01.propKeiyakuBukkenNameRyak.maxLength}"
												readonly="#{pc_Kab00803T01.propKeiyakuBukkenNameRyak.readonly}" tabindex="6"></h:inputText></TD>
										</TR>
										<TR>		
											<TH class="v_e" colspan="2"><h:outputText styleClass="outputText"
												id="lblLeaseSsnBnrCd"
												value="#{pc_Kab00803T01.propLeaseSsnBnrCd.labelName}"
												style="#{pc_Kab00803T01.propLeaseSsnBnrCd.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlLeaseSsnBnrCd"
													value="#{pc_Kab00803T01.propLeaseSsnBnrCd.stringValue}"
													disabled="#{pc_Kab00803T01.propLeaseSsnBnrCd.disabled}"
													readonly="#{pc_Kab00803T01.propLeaseSsnBnrCd.readonly}" tabindex="7">
													<f:selectItems
														value="#{pc_Kab00803T01.propLeaseSsnBnrCd.list}" />
												</h:selectOneMenu></TD>
										</TR>
										<TR>		
											<TH class="v_g" colspan="2"><h:outputText styleClass="outputText"
												id="lblZokaRiyuCd"
												value="#{pc_Kab00803T01.propZokaRiyuCd.labelName}"
												style="#{pc_Kab00803T01.propZokaRiyuCd.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlZokaRiyuCd"
													value="#{pc_Kab00803T01.propZokaRiyuCd.value}"
													disabled="#{pc_Kab00803T01.propZokaRiyuCd.disabled}"
													readonly="#{pc_Kab00803T01.propZokaRiyuCd.readonly}" tabindex="8">
													<f:selectItems
														value="#{pc_Kab00803T01.propZokaRiyuCd.list}" />
												</h:selectOneMenu></TD>
										</TR>
										<TR>
											<TH class="v_f" colspan="2"><h:outputText
													styleClass="outputText" id="lblLeaseStartDate"
													style="#{pc_Kab00803T01.propLblLeaseKikan.labelStyle}"
													value="#{pc_Kab00803T01.propLblLeaseKikan.name}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlLeaseStartDate" size="10"
													value="#{pc_Kab00803T01.propLeaseStartDate.dateValue}"
													style="#{pc_Kab00803T01.propLeaseStartDate.style}"
													readonly="#{pc_Kab00803T01.propLeaseStartDate.readonly}"
													tabindex="9">
													<f:convertDateTime pattern="yyyy/MM/dd" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<hx:inputHelperDatePicker />
												</h:inputText>～<h:inputText styleClass="inputText"
												id="htmlLeaseEndDate" size="10"
												value="#{pc_Kab00803T01.propLeaseEndDate.dateValue}"
												style="#{pc_Kab00803T01.propLeaseEndDate.style}"
												readonly="#{pc_Kab00803T01.propLeaseEndDate.readonly}" tabindex="10">
												<f:convertDateTime pattern="yyyy/MM/dd" />
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText></TD>
										</TR>
										<TR>
											<TH class="v_a" colspan="2"><h:outputText styleClass="outputText"
												id="lblLeaseRyo" value="#{pc_Kab00803T01.propLeaseRyo.labelName}"
												style="#{pc_Kab00803T01.propLeaseRyo.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlLeaseRyo" size="13"
													value="#{pc_Kab00803T01.propLeaseRyo.stringValue}"
													style="padding-right: 3px; text-align: right; #{pc_Kab00803T01.propLeaseRyo.style}"
													maxlength="#{pc_Kab00803T01.propLeaseRyo.maxLength}"
													readonly="#{pc_Kab00803T01.propLeaseRyo.readonly}"
													tabindex="11">
													<hx:inputHelperAssist errorClass="inputText_Error" />
												</h:inputText></TD>
										</TR>
										<TR>	
											<TH class="v_b" width="140" colspan="2"><h:outputText styleClass="outputText"
												id="lblShohizeiGaku"
												value="#{pc_Kab00803T01.propShohizeiGaku.labelName}"
												style="#{pc_Kab00803T01.propShohizeiGaku.labelStyle}"></h:outputText></TH>
											<TD width="280"><h:inputText styleClass="inputText" id="htmlShohizeiGaku"
												size="13" value="#{pc_Kab00803T01.propShohizeiGaku.stringValue}"
												style="padding-right: 3px; text-align: right; #{pc_Kab00803T01.propShohizeiGaku.style}"
												maxlength="#{pc_Kab00803T01.propShohizeiGaku.maxLength}"
												readonly="#{pc_Kab00803T01.propShohizeiGaku.readonly}" tabindex="12">
												<hx:inputHelperAssist errorClass="inputText_Error" />
											</h:inputText></TD>
											<TH class="v_b" width="140"><h:outputText styleClass="outputText"
																		id="lblShohizeiKbn"
																		style="#{pc_Kab00803T01.propShohizeiKbn.labelStyle}"
																		value="#{pc_Kab00803T01.propShohizeiKbn.labelName}"></h:outputText></TH>
											<TD width="300"><h:selectOneMenu styleClass="selectOneMenu"
																		id="htmlShohizeiKbn"
																		value="#{pc_Kab00803T01.propShohizeiKbn.stringValue}"
																		disabled="#{pc_Kab00803T01.propShohizeiKbn.disabled}" tabindex="13">
																		<f:selectItems
																			value="#{pc_Kab00803T01.propShohizeiKbn.list}" />
																	</h:selectOneMenu></TD>
										</TR>
										<TR>
											<TH class="v_c" colspan="2"><h:outputText styleClass="outputText"
												id="lblKeiyakuNo" value="#{pc_Kab00803T01.propKeiyakuNo.labelName}"
												style="#{pc_Kab00803T01.propKeiyakuNo.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
												id="htmlKeiyakuNo" size="40"
												value="#{pc_Kab00803T01.propKeiyakuNo.stringValue}"
												style="#{pc_Kab00803T01.propKeiyakuNo.style}"
												maxlength="#{pc_Kab00803T01.propKeiyakuNo.maxLength}"
												readonly="#{pc_Kab00803T01.propKeiyakuNo.readonly}" tabindex="14"></h:inputText></TD>
										</TR>
									
										<TR>
											<TH class="v_d" width="140" colspan="2"><h:outputText styleClass="outputText"
												id="lblKeiyakusakiCd"
												value="#{pc_Kab00803T01.propKeiyakusakiCd.labelName}"
												style="#{pc_Kab00803T01.propKeiyakusakiCd.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
												id="htmlKeiyakusakiCd" size="20"
												value="#{pc_Kab00803T01.propKeiyakusakiCd.stringValue}"
												style="#{pc_Kab00803T01.propKeiyakusakiCd.style}"
												maxlength="#{pc_Kab00803T01.propKeiyakusakiCd.maxLength}"
												onblur="return func_1(this, event);"
												readonly="#{pc_Kab00803T01.propKeiyakusakiCd.readonly}" tabindex="15"></h:inputText><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchKeiyakusakiCd"
													action="#{pc_Kab00803T01.doSearchKeiyakusakiCdAction}" tabindex="16"></hx:commandExButton><h:outputText
													styleClass="outputText" id="htmlKeiyakusakiName"></h:outputText></TD>
										</TR>
										<TR>
											<TH width="140" class="group_label_top" colspan="2"><h:outputText
												styleClass="outputText" id="lblShiharaisakiKoza" value="支払先口座"></h:outputText></TH>
												<TD colspan="3"><hx:commandExButton
													type="submit" styleClass="commandExButton" id="searchKoza"
													action="#{pc_Kab00803T01.doSearchKozaAction}" value="口座検索"
													tabindex="17"></hx:commandExButton> <!-- 口座検索戻り時用の隠しボタン -->
												<DIV style="display:none;"><hx:commandExButton type="submit"
													styleClass="commandExButton" id="hidden" value=""
													action="#{pc_Kab00803T01.doHiddenAction}"></hx:commandExButton>
												</DIV>
												</TD>
											</TR>
										<TR>
											<TH width="20" class="group_label"></TH>
											<TH width="120" class="v_c"><h:outputText
													styleClass="outputText" id="lblShiharaisakiBnk"
													style="#{pc_Kab00803T01.propBnkCd.labelStyle}"
													value="#{pc_Kab00803T01.propBnkCd.labelName}"></h:outputText></TH>
											<TD width="280">
											<h:outputText styleClass="outputText" id="htmlBnkCd"></h:outputText>
											<h:outputText styleClass="outputText" id="htmlBnkName"></h:outputText>
											</TD>
											<TH width="140" class="v_c"><h:outputText
													styleClass="outputText" id="lblShiharaisakiBnkStn"
													style="#{pc_Kab00803T01.propStnCd.labelStyle}"
													value="#{pc_Kab00803T01.propStnCd.labelName}"></h:outputText></TH>
											<TD width="300">
											<h:outputText styleClass="outputText" id="htmlStnCd"></h:outputText>
											<h:outputText styleClass="outputText" id="htmlStnName"></h:outputText>
											</TD>
										</TR>
										<TR>
											<TH width="20" class="group_label_bottom"></TH>
											<TH width="120" class="v_c">
											<h:outputText
													styleClass="outputText" id="lblShiharaisakiYokinKoza"
													style="#{pc_Kab00803T01.propKozaNo.labelStyle}"
													value="#{pc_Kab00803T01.propKozaNo.labelName}"></h:outputText></TH>
											<TD>
											<h:outputText styleClass="outputText" id="htmlYokinSmkName"></h:outputText>
											<h:outputText styleClass="outputText" id="htmlKozaNo"></h:outputText>
											</TD>
											<TH width="120" class="v_c"><h:outputText
													styleClass="outputText" id="lblKozaMeiginin"
													style="#{pc_Kab00803T01.propKozaNameKana.labelStyle}"
													value="#{pc_Kab00803T01.propKozaNameKana.labelName}"></h:outputText></TH>
											<TD>
											<DIV style="width:300px;white-space:nowrap;overflow:hidden;display:block;">
											<h:outputText styleClass="outputText" id="htmlKozaNameKana"></h:outputText>
											</DIV>
											</TD>
										</TR>
										<TR>
											<TH class="v_f" colspan="2"><h:outputText
													styleClass="outputText" id="lblShiharaiNengetsu"
													value="#{pc_Kab00803T01.propLblShiharaiKikan.name}"
													style="#{pc_Kab00803T01.propLblShiharaiKikan.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlShiharaiStartNengetsu" size="10"
													style="#{pc_Kab00803T01.propShiharaiStartNengetsu.style}"
													value="#{pc_Kab00803T01.propShiharaiStartNengetsu.dateValue}"
													readonly="#{pc_Kab00803T01.propShiharaiStartNengetsu.readonly}" tabindex="18">
													<f:convertDateTime pattern="yyyy/MM" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText>～<h:inputText styleClass="inputText"
													id="htmlShiharaiEndNengetsu" size="10"
													style="#{pc_Kab00803T01.propShiharaiEndNengetsu.style}"
													value="#{pc_Kab00803T01.propShiharaiEndNengetsu.dateValue}"
													readonly="#{pc_Kab00803T01.propShiharaiEndNengetsu.readonly}" tabindex="19">
													<f:convertDateTime pattern="yyyy/MM" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
										</TR>
										<TR>
											<TH class="v_f" colspan="2"><h:outputText
													styleClass="outputText" id="lblShiharaiKbn"
													style="#{pc_Kab00803T01.propShiharaiKbn.labelStyle}"
													value="#{pc_Kab00803T01.propShiharaiKbn.labelName}"></h:outputText></TH>
											<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlShiharaiKbn"
													style="#{pc_Kab00803T01.propShiharaiKbn.style}"
													value="#{pc_Kab00803T01.propShiharaiKbn.stringValue}" tabindex="20">
													<f:selectItems
														value="#{pc_Kab00803T01.propShiharaiKbn.list}" />
												</h:selectOneMenu></TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>

			<TABLE width="880" border="0" style="" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="登録"
								styleClass="commandExButton_dat" id="register"
								action="#{pc_Kab00803T01.doRegisterAction}"
								confirm="#{msg.SY_MSG_0002W}"
								rendered="#{pc_Kab00803T01.kab00803.propRegister.rendered}" tabindex="21">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="更新"
								styleClass="commandExButton_dat" id="update"
								confirm="#{msg.SY_MSG_0003W}"
								action="#{pc_Kab00803T01.doUpdateAction}"
								rendered="#{pc_Kab00803T01.kab00803.propUpdate.rendered}" tabindex="22">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="削除"
								styleClass="commandExButton_dat" id="delete"
								confirm="#{msg.SY_MSG_0004W}"
								action="#{pc_Kab00803T01.doDeleteAction}"
								rendered="#{pc_Kab00803T01.kab00803.propDelete.rendered}" tabindex="23">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="クリア"
								styleClass="commandExButton_etc" id="clear"
								action="#{pc_Kab00803T01.doClearAction}"
								rendered="#{pc_Kab00803T01.kab00803.propClear.rendered}" tabindex="24">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="資産情報"
							styleClass="commandExButton_etc" id="shisanInfo"
							action="#{pc_Kab00803T01.doShisanInfoAction}"
							rendered="#{pc_Kab00803T01.kab00803.propShisanInfo.rendered}"
							tabindex="25"
							disabled="#{pc_Kab00803T01.kab00803.propShisanInfo.disabled}">
						</hx:commandExButton>
							<hx:commandExButton type="submit" value="支払情報"
							styleClass="commandExButton_etc" id="shiharaiInfo"
							action="#{pc_Kab00803T01.doShiharaiInfoAction}"
							rendered="#{pc_Kab00803T01.kab00803.propShiharaiInfo.rendered}"
							tabindex="26"
							disabled="#{pc_Kab00803T01.kab00803.propShiharaiInfo.disabled}">
						</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

	<!-- ↑ここにコンポーネントを配置 --></DIV>
	</DIV>
	<!--↑content↑-->
	</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				value="htmlLeaseRyo=###,###,###,##0;###,###,###,##0 | htmlShohizeiGaku=###,###,###,##0;###,###,###,##0"
				id="htmlFormatNumberOption"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab00803T01.kab00803.propExecutableDelete.integerValue}"
				id="htmlExecutableDelete">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab00803T01.kab00803.propExecutableShiharaiInfo.integerValue}"
				id="htmlExecutableShiharaiInfo">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab00803T01.kab00803.propExecutableKbn.stringValue}"
				id="htmlExecutableKbn"></h:inputHidden>
			<h:inputHidden id="htmlKaikeiNendHidden"
				value="#{pc_Kab00803T01.propKaikeiNendHidden.stringValue}">
			</h:inputHidden>
			<h:inputHidden id="htmlKozaIdNoHidden"
				value="#{pc_Kab00803T01.propKozaIdNoHidden.stringValue}">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

