<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos01203T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<TITLE>Cos01203T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<SCRIPT type="text/javascript">
function confirmOk() {	
	if (document.getElementById('form1:htmlpropExecutableMode').value == "delete") {
		indirectClick('performDelete');
	}
}
function confirmCancel() {
	document.getElementById('form1:htmlpropExecutableMode').value = "";
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cos01203T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cos01203T01.commonBean.doCloseDispAction}"></hx:commandExButton></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<table border="0" cellpadding="0" cellspacing="0" width="98%">
				<tr>
					<td align="left">
					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
						id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false"
						style="color: red; font-size: 8pt; font-weight: bold">
					</h:outputText></FIELDSET>
					</td>
					<td align="right"><hx:commandExButton type="submit" value="戻る"
						id="returnDisp" styleClass="commandExButton"
						action="#{pc_Cos01203T01.commonBean.doReturnDispAction}" immediate="true">
					</hx:commandExButton></td>
				</tr>
			</table>

			<!--↑head↑--> <!--↓content↓-->
			<DIV id="content" align="center">

			<DIV class="column" align="center">
			<TABLE width="800">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%">
							<TBODY>
								<TR>
									<TD align="center">
									<TABLE class="table" width="400">
										<TBODY>
											<TR>
												<TH class="v_a" nowrap width="160"><h:outputText
													styleClass="outputText" id="labelTabAppId"
													value="#{pc_Cos01203T01.commonBean.propTabAppId.labelName}"
													style="#{pc_Cos01203T01.commonBean.propTabAppId.labelStyle}"></h:outputText></TH>
												<TD class="table" width="200" nowrap><h:inputText
													styleClass="inputText" id="htmlTabAppId"
													value="#{pc_Cos01203T01.commonBean.propTabAppId.stringValue}"
													style="#{pc_Cos01203T01.commonBean.propTabAppId.style}"
													size="30"
													disabled="#{pc_Cos01203T01.commonBean.propTabAppId.disabled}"
													maxlength="#{pc_Cos01203T01.commonBean.propTabAppId.max}"></h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR noshade class="hr" align="center" width="100%">
			<%-- タブ --%>
			<table border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD nowrap="nowrap" align="left"><hx:commandExButton type="submit"
							action="#{pc_Cos01203T01.commonBean.doHtmlLinkAppAction}"
							value="#{pc_Cos01203T01.commonBean.propAppTabBtn.name}"
							disabled="#{pc_Cos01203T01.commonBean.propAppTabBtn.disabled}"
							rendered="#{pc_Cos01203T01.commonBean.propAppTabBtn.rendered}"
							styleClass="#{pc_Cos01203T01.commonBean.propAppTabBtn.style}"
							id="htmlAppTabBtn" style="width: 20%;"></hx:commandExButton><hx:commandExButton
							type="submit" action="#{pc_Cos01203T01.commonBean.doHtmlLinkScrnAction}"
							value="#{pc_Cos01203T01.commonBean.propScrnTabBtn.name}"
							disabled="#{pc_Cos01203T01.commonBean.propScrnTabBtn.disabled}"
							rendered="#{pc_Cos01203T01.commonBean.propScrnTabBtn.rendered}"
							styleClass="#{pc_Cos01203T01.commonBean.propScrnTabBtn.style}"
							id="htmlScrnTabBtn" style="width: 20%;"></hx:commandExButton><hx:commandExButton
							type="submit" action="#{pc_Cos01203T01.commonBean.doHtmlLinkPdfAction}"
							value="#{pc_Cos01203T01.commonBean.propPdfTabBtn.name}"
							disabled="#{pc_Cos01203T01.commonBean.propPdfTabBtn.disabled}"
							rendered="#{pc_Cos01203T01.commonBean.propPdfTabBtn.rendered}"
							styleClass="#{pc_Cos01203T01.commonBean.propPdfTabBtn.style}"
							id="htmlPdfTabBtn" style="width: 20%;"></hx:commandExButton></TD>
					<TR>
						<TD>
						<table width="100%" class="tab_body" border="0" cellpadding="0"
							cellspacing="0">
							<tr>
								<td><%-- ↑タブ --%>
								<CENTER>
								<TABLE width="900" border="0" cellpadding="0" cellspacing="0"
									align="center" style="margin-top: 10px;margin-bottom: 10px;">
									<TBODY>
										<TR>
											<TD align="center" width="100%" nowrap>
											<TABLE align="center" border="0" cellpadding="0"
												cellspacing="0" width="800">
												<TBODY>
													<TR>
														<TD nowrap>
														<TABLE class="table" width="100%">
															<TBODY>
																<TR>
																	<TH nowrap class="v_b" width="25%"><h:outputText
																		styleClass="outputText" id="labelAppName"
																		value="#{pc_Cos01203T01.propAppName.labelName}"
																		style="#{pc_Cos01203T01.propAppName.labelStyle}"></h:outputText></TH>

																	<TD nowrap colspan="4"><h:inputText
																		styleClass="inputText" id="htmlAppName"
																		value="#{pc_Cos01203T01.propAppName.stringValue}"
																		style="#{pc_Cos01203T01.propAppName.style}" size="50"
																		maxlength="#{pc_Cos01203T01.propAppName.max}"></h:inputText></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_c"><h:outputText
																		styleClass="outputText" id="labelAppNameDef"
																		value="#{pc_Cos01203T01.propAppNameDef.labelName}"
																		style="#{pc_Cos01203T01.propAppNameDef.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4"><h:outputText
																		styleClass="outputText" id="htmlAppNameDef"
																		value="#{pc_Cos01203T01.propAppNameDef.stringValue}"
																		style="#{pc_Cos01203T01.propAppNameDef.style}"></h:outputText></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_d"><h:outputText
																		styleClass="outputText" id="labelPrdList"
																		value="#{pc_Cos01203T01.propPrdList.labelName}"
																		style="#{pc_Cos01203T01.propPrdList.labelStyle}"></h:outputText></TH>
																	<TD width="13%"><h:selectOneMenu
																		styleClass="selectOneMenu" id="htmlPrdList"
																		value="#{pc_Cos01203T01.propPrdList.value}"
																		disabled="#{pc_Cos01203T01.propPrdList.disabled}">
																		<f:selectItems
																			value="#{pc_Cos01203T01.propPrdList.list}" />
																	</h:selectOneMenu></TD>
																	<TD width="13%"><hx:commandExButton type="submit"
																		value="選択" styleClass="commandExButton" id="select"
																		action="#{pc_Cos01203T01.doSelectAction}"
																		disabled="#{pc_Cos01203T01.propSelectBtn.disabled}">
																	</hx:commandExButton><hx:commandExButton type="submit"
																		value="解除" styleClass="commandExButton" id="unselect"
																		action="#{pc_Cos01203T01.doUnSelectAction}"
																		disabled="#{pc_Cos01203T01.propUnSelectBtn.disabled}">
																	</hx:commandExButton></TD>
																	<TH width="14%"><h:outputText styleClass="outputText"
																		id="labelSubList"
																		value="#{pc_Cos01203T01.propSubList.labelName}"
																		style="#{pc_Cos01203T01.propSubList.labelStyle}"></h:outputText></TH>
																	<TD width="35%"><h:selectOneMenu
																		styleClass="selectOneMenu" id="htmlSubList"
																		value="#{pc_Cos01203T01.propSubList.value}"
																		disabled="#{pc_Cos01203T01.propSubList.disabled}">
																		<f:selectItems
																			value="#{pc_Cos01203T01.propSubList.list}" />
																	</h:selectOneMenu></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_e"><h:outputText
																		styleClass="outputText" id="labelSysKbn"
																		value="#{pc_Cos01203T01.propSysKbn.labelName}"
																		style="#{pc_Cos01203T01.propSysKbn.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4"><h:selectOneRadio
																		styleClass="selectOneRadio" id="htmlSysKbn"
																		value="#{pc_Cos01203T01.propSysKbn.value}"
																		disabled="#{pc_Cos01203T01.propSysKbn.disabled}">
																		<f:selectItems
																			value="#{pc_Cos01203T01.propSysKbn.list}" />
																	</h:selectOneRadio></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_f"><h:outputText
																		styleClass="outputText" id="labelFuncToolKbn"
																		value="#{pc_Cos01203T01.propFuncToolKbn.labelName}"
																		style="#{pc_Cos01203T01.propFuncToolKbn.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4">
																	<TABLE class="clear_border" width="100%" border="0"
																		cellpadding="0" cellspacing="0">
																		<TBODY>
																			<TR>
																				<TD width="160"><h:selectOneRadio
																					styleClass="selectOneRadio" id="htmlFuncToolKbn"
																					value="#{pc_Cos01203T01.propFuncToolKbn.value}"
																					disabled="#{pc_Cos01203T01.propFuncToolKbn.disabled}">
																					<f:selectItems
																						value="#{pc_Cos01203T01.propFuncToolKbn.list}" />
																				</h:selectOneRadio></TD>
																				<TD><hx:commandExButton type="submit" value="選択"
																					styleClass="commandExButton" id="funcToolSelect"
																					disabled="#{pc_Cos01203T01.propFuncToolSelectBtn.disabled}"
																					action="#{pc_Cos01203T01.doFuncToolSelectAction}"></hx:commandExButton><hx:commandExButton
																					type="submit" value="解除"
																					styleClass="commandExButton" id="funcToolRelease"
																					disabled="#{pc_Cos01203T01.propFuncToolReleaseBtn.disabled}"
																					action="#{pc_Cos01203T01.doFuncToolReleaseAction}"></hx:commandExButton>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																</TR>
																<TR>
																	<TH nowrap class="v_g"><h:outputText
																		styleClass="outputText" id="labelScrnKbn"
																		value="#{pc_Cos01203T01.propScrnKbn.labelName}"
																		style="#{pc_Cos01203T01.propScrnKbn.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4"><h:selectOneRadio
																		styleClass="selectOneRadio" id="htmlScrnKbn"
																		value="#{pc_Cos01203T01.propScrnKbn.value}"
																		disabled="#{pc_Cos01203T01.propScrnKbn.disabled}">
																		<f:selectItems
																			value="#{pc_Cos01203T01.propScrnKbn.list}" />
																	</h:selectOneRadio></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_a"><h:outputText
																		styleClass="outputText" id="labelAppPath"
																		value="#{pc_Cos01203T01.propAppPath.labelName}"
																		style="#{pc_Cos01203T01.propAppPath.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4"><h:inputText
																		styleClass="inputText" id="htmlAppPath"
																		value="#{pc_Cos01203T01.propAppPath.stringValue}"
																		style="#{pc_Cos01203T01.propAppPath.style}" size="50"
																		disabled="#{pc_Cos01203T01.propAppPath.disabled}"
																		maxlength="#{pc_Cos01203T01.propAppPath.max}"></h:inputText>
																	<h:outputText styleClass="outputText"
																		id="labelAppPathDesc"
																		value="#{pc_Cos01203T01.propAppPathDesc.stringValue}"></h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TH nowrap class="v_b"><h:outputText
																		styleClass="outputText" id="labelFlg" value="権限利用フラグ"
																		style="#{pc_Cos01203T01.propTabFlg.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4"><h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox" id="htmlTabFlg"
																		style="#{pc_Cos01203T01.propTabFlg.style}"
																		value="#{pc_Cos01203T01.propTabFlg.checked}"
																		disabled="#{pc_Cos01203T01.propTabFlg.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="labelTabFlg"
																		value="#{pc_Cos01203T01.propTabFlg.labelName}"
																		style="#{pc_Cos01203T01.propTabFlg.labelStyle}">
																	</h:outputText> <h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox" id="htmlBtnFlg"
																		style="#{pc_Cos01203T01.propBtnFlg.style}"
																		value="#{pc_Cos01203T01.propBtnFlg.checked}"
																		disabled="#{pc_Cos01203T01.propBtnFlg.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="labelBtnFlg"
																		value="#{pc_Cos01203T01.propBtnFlg.labelName}"
																		style="#{pc_Cos01203T01.propBtnFlg.labelStyle}">
																	</h:outputText> <h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox" id="htmlItemFlg"
																		style="#{pc_Cos01203T01.propItemFlg.style}"
																		value="#{pc_Cos01203T01.propItemFlg.checked}"
																		disabled="#{pc_Cos01203T01.propItemFlg.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="labelItemFlg"
																		value="#{pc_Cos01203T01.propItemFlg.labelName}"
																		style="#{pc_Cos01203T01.propItemFlg.labelStyle}">
																	</h:outputText></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_c"><h:outputText
																		styleClass="outputText" id="labelToolPath"
																		value="#{pc_Cos01203T01.propToolPath.labelName}"
																		style="#{pc_Cos01203T01.propToolPath.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4"><h:inputText
																		styleClass="inputText" id="htmlToolPath"
																		value="#{pc_Cos01203T01.propToolPath.stringValue}"
																		style="#{pc_Cos01203T01.propToolPath.style}" size="50"
																		disabled="#{pc_Cos01203T01.propToolPath.disabled}"
																		maxlength="#{pc_Cos01203T01.propToolPath.max}"></h:inputText></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_d"><h:outputText
																		styleClass="outputText" id="labelToolParameter"
																		value="#{pc_Cos01203T01.propToolParameter.labelName}"
																		style="#{pc_Cos01203T01.propToolParameter.labelStyle}"></h:outputText></TH>
																	<TD nowrap colspan="4"><h:inputText
																		styleClass="inputText" id="htmlToolParameter"
																		value="#{pc_Cos01203T01.propToolParameter.stringValue}"
																		style="#{pc_Cos01203T01.propToolParameter.style}"
																		size="50"
																		disabled="#{pc_Cos01203T01.propToolParameter.disabled}"
																		maxlength="#{pc_Cos01203T01.propToolParameter.max}"></h:inputText></TD>
																</TR>
																<TR>
																	<TH nowrap class="v_e"><h:outputText
																		styleClass="outputText" id="labelNote"
																		value="#{pc_Cos01203T01.propNote.labelName}"
																		style="#{pc_Cos01203T01.propNote.labelStyle}">
																	</h:outputText></TH>
																	<TD nowrap colspan="4"><h:inputTextarea
																		styleClass="inputTextarea" id="htmlNote"
																		disabled="#{pc_Cos01203T01.propNote.disabled}"
																		style="#{pc_Cos01203T01.propNote.style}" cols="50"
																		value="#{pc_Cos01203T01.propNote.stringValue}">
																	</h:inputTextarea></TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
													<TR>
														<TD nowrap>
														<TABLE align="center" class="button_bar" width="100%">
															<TBODY>
																<TR>
																	<TD align="center" nowrap><hx:commandExButton
																		type="submit" value="登録"
																		styleClass="commandExButton_dat" id="register"
																		action="#{pc_Cos01203T01.doRegisterAction}"
																		rendered="#{pc_Cos01203T01.propRegisterBtn.rendered}"
																		disabled="#{pc_Cos01203T01.propRegisterBtn.disabled}"
																		confirm="#{msg.SY_MSG_0002W}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="更新へ" styleClass="commandExButton_dat"
																		id="linkUpdate"
																		action="#{pc_Cos01203T01.doLinkUpdateAction}"
																		rendered="#{pc_Cos01203T01.propLinkUpdateBtn.rendered}"
																		disabled="#{pc_Cos01203T01.propLinkUpdateBtn.disabled}"
																		confirm="#{msg.SY_MSG_0002W}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="更新" styleClass="commandExButton_dat"
																		id="update" action="#{pc_Cos01203T01.doUpdateAction}"
																		rendered="#{pc_Cos01203T01.propUpdateBtn.rendered}"
																		disabled="#{pc_Cos01203T01.propUpdateBtn.disabled}"
																		confirm="#{msg.SY_MSG_0003W}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="削除" styleClass="commandExButton_dat"
																		id="delete" action="#{pc_Cos01203T01.doDeleteAction}"
																		rendered="#{pc_Cos01203T01.propDeleteBtn.rendered}"
																		disabled="#{pc_Cos01203T01.propDeleteBtn.disabled}"
																		confirm="#{msg.SY_MSG_0004W}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="クリア" id="clear"
																		styleClass="commandExButton_etc"
																		action="#{pc_Cos01203T01.doClearAction}">
																	</hx:commandExButton></TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								</CENTER>

								</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>





			</DIV>
			</DIV>
			</DIV>

			<h:inputHidden
				value="#{pc_Cos01203T01.propExecutableMode.stringValue}"
				id="htmlpropExecutableMode"></h:inputHidden>

			<hx:commandExButton type="submit" value="実行"
				styleClass="commandExButton" id="performDelete"
				style="visibility: hidden"></hx:commandExButton>

			<!--↑content↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<!--↑outer↑-->
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

