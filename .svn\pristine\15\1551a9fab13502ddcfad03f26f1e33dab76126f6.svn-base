<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea01201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea01201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	
<LINK rel="stylesheet" type="text/css" href="../../rev/ke/inc/gakuenKE.css">	

<style type="text/css">
<!--
 .setWidth  TD {width: 113px; white-space: nowrap;}
-->
</style>

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">
//処理区分クリック時
function func_1(thisObj, thisEvent) {
	//編成期間名称を取得する
	getHenseiKikanName(thisObj, thisEvent);
}
//予算単位の「ク」ボタンクリック時
function func_2(thisObj, thisEvent) {
	//リストボックスの選択行をクリアする
	clearListBox("form1:htmlYsnTCd");
}
//目的コードロストフォーカス時
function func_3(thisObj, thisEvent) {
	//目的名称を取得する
	getMokuName(thisObj, thisEvent);
}
//科目コードロストフォーカス時
function func_4(thisObj, thisEvent) {
	//科目名称を取得する
	getKmkName(thisObj, thisEvent);
}
//編成期間名称を取得する
function getHenseiKikanName() {
	var henseiKikanName = "";
	var syoriKbn = document.getElementsByName("form1:htmlSyoriKbn");
	if (document.getElementById("form1:htmlKaikeiNendo").innerHTML != "") {
		if (syoriKbn[1].checked) henseiKikanName = "経理査定";
		if (syoriKbn[2].checked) henseiKikanName = "経理決定";
	}
	document.getElementById("form1:htmlHenseiKikanName").innerHTML = henseiKikanName;
}
//目的名称を取得する
function getMokuName(thisObj, thisEvent) {
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMokuName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").innerHTML;
	args['code2'] = document.getElementById("form1:htmlMokuCd").value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
//科目名称を取得する
function getKmkName(thisObj, thisEvent) {
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").innerHTML;
	args['code2'] = document.getElementById("form1:htmlKmkCd").value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
//フォームのサブミット時
//FWからsubmit時にコールバック
function submitMethod() {
	//予算単位リストボックの内容を保管
	//（※storeListBox関数はgakuenKE.jsに含まれる）
	storeListBox("form1:htmlYsnTCd","form1:htmlYsnTCdHidden");
	return true;
}
//検索ボタンクリック時の確認メッセージでOK選択時
function confirmOk() {
	var value = document.getElementById('form1:htmlExecutableSearchHidden').value;
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}			
//検索ボタンクリック時の確認メッセージでキャンセル選択時
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea01201.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea01201.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea01201.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea01201.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton styleClass="commandExButton" type="submit"
	id="register"
	value="新規登録"
	action="#{pc_Kea01201.doRegisterAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="145" class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblKaikeiNendo"
										value="#{pc_Kea01201.propKaikeiNendo.name}"
										style="#{pc_Kea01201.propKaikeiNendo.labelStyle}"></h:outputText><h:outputText styleClass="outputText"
										id="lblHenseiJotai"
										value="#{pc_Kea01201.propHenseiJotai.name}"
										style="#{pc_Kea01201.propHenseiJotai.labelStyle}"></h:outputText></TH>
									<TD width="650">
										<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
											<TR>
												<TD width="55" nowrap><h:outputText styleClass="outputText"
													id="htmlKaikeiNendo"
													value="#{pc_Kea01201.propKaikeiNendo.dateValue}"
													style="#{pc_Kea01201.propKaikeiNendo.style}">
													<f:convertDateTime pattern="yyyy" /></h:outputText><h:outputText styleClass="outputText"
													id="htmlKaikeiNendoDsp"
													value="#{pc_Kea01201.propKaikeiNendoDsp.stringValue}"
													style="#{pc_Kea01201.propKaikeiNendoDsp.style}"></h:outputText></TD>
												<TD width="86" align="left" nowrap><h:outputText styleClass="outputText"
													id="htmlHenseiJotai"
													value="#{pc_Kea01201.propHenseiJotai.stringValue}"
													style="#{pc_Kea01201.propHenseiJotai.style}"></h:outputText></TD>
												<TD><h:outputText styleClass="outputText"
													id="htmlHenseiKikanName"
													value="#{pc_Kea01201.propHenseiKikanName.stringValue}"
													style="#{pc_Kea01201.propHenseiKikanName.style}"></h:outputText><h:outputText styleClass="outputText"
													id="htmlHenseiKikan"
													value="#{pc_Kea01201.propHenseiKikan.stringValue}"
													style="#{pc_Kea01201.propHenseiKikan.style}"></h:outputText></TD>
											</TR>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblSyoriKbn"
										value="#{pc_Kea01201.propSyoriKbn.name}"
										style="#{pc_Kea01201.propSyoriKbn.labelStyle}"></h:outputText></TH>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
											<TR>
												<TD><h:selectOneRadio styleClass="selectOneRadio setWidth" disabledClass="selectOneRadio_Disabled"
													id="htmlSyoriKbn" tabindex="1"
													value="#{pc_Kea01201.propSyoriKbn.stringValue}"
													onclick="return func_1(this, event);"
													disabled="#{pc_Kea01201.propSyoriKbn.disabled}"
													readonly="#{pc_Kea01201.propSyoriKbn.readonly}"
													rendered="#{pc_Kea01201.propSyoriKbn.rendered}">
													<f:selectItems value="#{pc_Kea01201.propSyoriKbn.list}" /></h:selectOneRadio></TD>
												<TD>（<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlTorikeshi" tabindex="2"
													value="#{pc_Kea01201.propTorikeshi.checked}"
													style="#{pc_Kea01201.propTorikeshi.style}"></h:selectBooleanCheckbox><h:outputText styleClass="outputText"
													id="lblTorikeshi"
													value="#{pc_Kea01201.propTorikeshi.name}"
													style="#{pc_Kea01201.propTorikeshi.labelStyle}"></h:outputText>）</TD>
											</TR>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblKmkKbn"
										value="#{pc_Kea01201.propKmkKbn.name}"
										style="#{pc_Kea01201.propKmkKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth" disabledClass="selectOneRadio_Disabled"
										id="htmlKmkKbn"	tabindex="3"
										value="#{pc_Kea01201.propKmkKbn.stringValue}"
										disabled="#{pc_Kea01201.propKmkKbn.disabled}"
										readonly="#{pc_Kea01201.propKmkKbn.readonly}"
										rendered="#{pc_Kea01201.propKmkKbn.rendered}">
										<f:selectItems value="#{pc_Kea01201.propKmkKbn.list}" /></h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblShikinShohiKbn"
										value="#{pc_Kea01201.propShikinShohiKbn.name}"
										style="#{pc_Kea01201.propShikinShohiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectManyCheckbox styleClass="selectManyCheckbox setWidth" disabledClass="selectManyCheckbox_Disabled"
										id="htmlShikinShohiKbn" tabindex="4"
										value="#{pc_Kea01201.propShikinShohiKbn.stringValue}"
										disabled="#{pc_Kea01201.propShikinShohiKbn.disabled}"
										readonly="#{pc_Kea01201.propShikinShohiKbn.readonly}"
										rendered="#{pc_Kea01201.propShikinShohiKbn.rendered}">
										<f:selectItems value="#{pc_Kea01201.propShikinShohiKbn.list}" /></h:selectManyCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_e" width="145"><h:outputText styleClass="outputText"
										id="lblYsnTCd"
										value="#{pc_Kea01201.propYsnTCd.labelName}"
										style="#{pc_Kea01201.propYsnTCd.labelStyle}"></h:outputText></TH>
									<TD width="650">
									<TABLE class="clear_border" border="0" cellpadding="0" cellspacing="0" width="100%" height="75">
										<TBODY>
											<TR>
												<TD width="500"><h:selectManyListbox styleClass="selectManyListbox"
													id="htmlYsnTCd" size="5" tabindex="5"
													value="#{pc_Kea01201.propYsnTCd.stringValue}"
													style="width: 515px"
													disabled="#{pc_Kea01201.propYsnTCd.disabled}"
													readonly="#{pc_Kea01201.propYsnTCd.readonly}"
													rendered="#{pc_Kea01201.propYsnTCd.rendered}">
													<f:selectItems value="#{pc_Kea01201.propYsnTCd.list}" /></h:selectManyListbox></TD>
												<TD width="20" height="75">
													<TABLE border="0" cellpadding="0" cellspacing="0" width="20" height="75">
														<TR>
															<TD height="15"><hx:commandExButton styleClass="commandExButton_search" type="submit"
																id="searchYsnTCd" tabindex="6"
																action="#{pc_Kea01201.doSearchYsnTCdAction}"></hx:commandExButton></TD>
														</TR>
														<TR>
															<TD height="50">
															</TD>
														</TR>
														<TR>
															<TD height="15"><hx:commandExButton styleClass="commandExButton_listclear" type="button"
															id="clearYsnTCd" tabindex="7"
															onclick="return func_2(this, event);"></hx:commandExButton></TD>
														</TR>
													</TABLE></TD>
												<TD width="120"></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_f"><h:outputText styleClass="outputText"
										id="lblMokuCd"
										value="#{pc_Kea01201.propMokuCd.labelName}"
										style="#{pc_Kea01201.propMokuCd.labelStyle}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlMokuCd" size="14" tabindex="8"
												value="#{pc_Kea01201.propMokuCd.stringValue}"
												style="#{pc_Kea01201.propMokuCd.style}"
												onblur="return func_3(this, event);"
												maxlength="#{pc_Kea01201.propMokuCd.maxLength}"
												disabled="#{pc_Kea01201.propMokuCd.disabled}"
												readonly="#{pc_Kea01201.propMokuCd.readonly}"
												rendered="#{pc_Kea01201.propMokuCd.rendered}"></h:inputText><hx:commandExButton
												type="submit" styleClass="commandExButton_search"
												id="searchMokuCd" tabindex="9"
												action="#{pc_Kea01201.doSearchMokuCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText"
												id="htmlMokuName"
												value="#{pc_Kea01201.propMokuName.stringValue}"
												style="#{pc_Kea01201.propMokuName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_g"><h:outputText styleClass="outputText"
										id="lblKmkCd"
										value="#{pc_Kea01201.propKmkCd.labelName}"
										style="#{pc_Kea01201.propKmkCd.labelStyle}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlKmkCd" size="14" tabindex="10"
												value="#{pc_Kea01201.propKmkCd.stringValue}"
												style="#{pc_Kea01201.propKmkCd.style}"
												onblur="return func_4(this, event);"
												maxlength="#{pc_Kea01201.propKmkCd.maxLength}"
												disabled="#{pc_Kea01201.propKmkCd.disabled}"
												readonly="#{pc_Kea01201.propKmkCd.readonly}"
												rendered="#{pc_Kea01201.propKmkCd.rendered}"></h:inputText><hx:commandExButton
												type="submit" styleClass="commandExButton_search"
												id="searchKmkCd" tabindex="11"
												action="#{pc_Kea01201.doSearchKmkCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText"
												id="htmlKmkName"
												value="#{pc_Kea01201.propKmkName.stringValue}"
												style="#{pc_Kea01201.propKmkName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_a"><h:outputText styleClass="outputText"
										id="lblKomokuNaiyo"
										value="#{pc_Kea01201.propKomokuNaiyo.labelName}"
										style="#{pc_Kea01201.propKomokuNaiyo.labelStyle}"></h:outputText></TH>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border" width="300"><h:inputText styleClass="inputText"
													id="htmlKomokuNaiyo"  size="80" tabindex="12"
													value="#{pc_Kea01201.propKomokuNaiyo.stringValue}"
													style="#{pc_Kea01201.propKomokuNaiyo.style}"
													disabled="#{pc_Kea01201.propKomokuNaiyo.disabled}"
													maxlength="#{pc_Kea01201.propKomokuNaiyo.maxLength}"
													readonly="#{pc_Kea01201.propKomokuNaiyo.readonly}"
													rendered="#{pc_Kea01201.propKomokuNaiyo.rendered}"></h:inputText></TD>
												<TD class="clear_border"><h:outputText styleClass="outputText"
													id="lblKomokuNaiyoType"
													value="（部分一致）"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblHyojiKbn"
										value="#{pc_Kea01201.propHyojiKbn.labelName}"
										style="#{pc_Kea01201.propHyojiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled" styleClass="selectOneRadio setWidth"
										id="htmlHyojiKbn" tabindex="13"
										value="#{pc_Kea01201.propHyojiKbn.stringValue}"
										disabled="#{pc_Kea01201.propHyojiKbn.disabled}"
										readonly="#{pc_Kea01201.propHyojiKbn.readonly}"
										rendered="#{pc_Kea01201.propHyojiKbn.rendered}">
										<f:selectItems value="#{pc_Kea01201.propHyojiKbn.list}" /></h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD><hx:commandExButton
											type="submit" styleClass="commandExButton_dat" id="search" tabindex="14" value="検索"   action="#{pc_Kea01201.doSearchAction}"></hx:commandExButton><hx:commandExButton
											type="submit" styleClass="commandExButton_etc" id="clear"  tabindex="15" value="クリア" 	action="#{pc_Kea01201.doClearAction}" ></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden id="htmlExecutableSearchHidden" value="#{pc_Kea01201.propExecutableSearchHidden.integerValue}"><f:convertNumber /></h:inputHidden>
			<h:inputHidden id="htmlYsnTCdHidden" value="#{pc_Kea01201.propYsnTCdHidden.stringValue}"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>