<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea04101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.framework.property.Checkable"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea04101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	
<LINK rel="stylesheet" type="text/css" href="../../rev/ke/inc/gakuenKE.css"  >	
<style type="text/css">
<!--
.setWidth TD {width: 150px; white-space: nowrap;}
-->
</style>

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
	// 帳票タイトル
    var syukeiKbn	= document.getElementById('form1:htmlSyukeiKbn').value;

	if (syukeiKbn == "<%=Checkable.NO_SELECT_VALUE%>") {
		document.getElementById('form1:htmlTitle').value = "";
	}
	if (document.getElementById('form1:htmlTitle' + '_' + syukeiKbn) != null) {
		document.getElementById('form1:htmlTitle').value = document.getElementById('form1:htmlTitle' + '_' + syukeiKbn).value;
	} else {
		document.getElementById('form1:htmlTitle').value = "";
	}
}
function func_4(thisObj, thisEvent) {
	// 目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMokuNameFrom";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMokuCdFrom").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_6(thisObj, thisEvent) {
	// 目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMokuNameTo";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMokuCdTo").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_7(thisObj, thisEvent) {
	// 科目名称を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkNameFrom";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlKmkCdFrom").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_9(thisObj, thisEvent) {
	// 科目名称を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkNameTo";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlKmkCdTo").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function copy_mokutekiTo(thisObj, thisEvent) {
	// 目的コード(from)を目的コード(to)にコピーする
	if(document.getElementById("form1:htmlMokuCdTo").value == ""){
			document.getElementById("form1:htmlMokuCdTo").value = document.getElementById("form1:htmlMokuCdFrom").value;
			func_6(document.getElementById("form1:htmlMokuCdTo"), "");
	}
}
function copy_kamokuTo(thisObj, thisEvent) {
	// 科目コード(from)を科目コード(to)にコピーする
	if(document.getElementById("form1:htmlKmkCdTo").value == ""){
			document.getElementById("form1:htmlKmkCdTo").value = document.getElementById("form1:htmlKmkCdFrom").value;
			func_9(document.getElementById("form1:htmlKmkCdTo"), "");
	}
}
// クリアボタンクリック時
function func_10(thisObj, thisEvent) {
	//リストボックスの選択行をクリアする
	clearListBox("form1:htmlYsnTList");
}
// フォームのサブミット時
//FWからsubmit時にコールバック
function submitMethod() {
	//予算単位リストボックの内容を保管
	//（※storeListBox関数はgakuenKE.jsに含まれる）
	storeListBox("form1:htmlYsnTList","form1:htmlYsnTCdHidden");
	return true;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_1(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea04101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea04101.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea04101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea04101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- レイアウト対応、全角スペース -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Kea04101.propKaikeiNendo.labelName}"
										style="#{pc_Kea04101.propKaikeiNendo.labelStyle}"></h:outputText></TH>
									<TD width="650"><h:inputText styleClass="inputText"
										id="htmlKaikeiNendo" size="6"
										disabled="#{pc_Kea04101.propKaikeiNendo.disabled}"
										readonly="#{pc_Kea04101.propKaikeiNendo.readonly}"
										style="#{pc_Kea04101.propKaikeiNendo.style}"
										value="#{pc_Kea04101.propKaikeiNendo.dateValue}" tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblHoseiKaiji"
										value="#{pc_Kea04101.propHoseiKaiji.labelName}"
										style="#{pc_Kea04101.propHoseiKaiji.labelStyle}"></h:outputText></TH>
									<TD width="450"><h:inputText styleClass="inputText"
										id="htmlHoseiKaiji" size="3"
										disabled="#{pc_Kea04101.propHoseiKaiji.disabled}"
										maxlength="#{pc_Kea04101.propHoseiKaiji.maxLength}"
										readonly="#{pc_Kea04101.propHoseiKaiji.readonly}"
										style="#{pc_Kea04101.propHoseiKaiji.style}" tabindex="2"
										value="#{pc_Kea04101.propHoseiKaiji.integerValue}">
										<f:convertNumber pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_c" colspan="2"><h:outputText 
										styleClass="outputText" id="lblShikinShohiKbn" 
										value="#{pc_Kea04101.propShikinShohiKbn.labelName}" 
										style="#{pc_Kea04101.propShikinShohiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox setWidth"
										id="htmlShikinShohiKbn"
										disabled="#{pc_Kea04101.propShikinShohiKbn.disabled}"
										readonly="#{pc_Kea04101.propShikinShohiKbn.readonly}"
										value="#{pc_Kea04101.propShikinShohiKbn.stringValue}"
										tabindex="3" layout="lineDirection">
										<f:selectItems value="#{pc_Kea04101.propShikinShohiKbn.list}" />
									</h:selectManyCheckbox></TD>
									
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblChushutsuJoken"
										value="#{pc_Kea04101.propChushutsuJoken.labelName}"
										style="#{pc_Kea04101.propChushutsuJoken.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlChushutsuJoken"
										tabindex="4"
										disabled="#{pc_Kea04101.propChushutsuJoken.disabled}"
										readonly="#{pc_Kea04101.propChushutsuJoken.readonly}"
										value="#{pc_Kea04101.propChushutsuJoken.stringValue}" tabindex="5">
										<f:selectItems value="#{pc_Kea04101.propChushutsuJoken.list}" />
									</h:selectOneRadio></TD>
									
								</TR>
								<TR>
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblSyukeiKbn"
										value="#{pc_Kea04101.propSyukeiKbn.labelName}"
										style="#{pc_Kea04101.propSyukeiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSyukeiKbn" style="width:150px"
										disabled="#{pc_Kea04101.propSyukeiKbn.disabled}"
										readonly="#{pc_Kea04101.propSyukeiKbn.readonly}"
										value="#{pc_Kea04101.propSyukeiKbn.stringValue}" tabindex="6"
										onchange="return func_1(this, event);">
										<f:selectItems value="#{pc_Kea04101.propSyukeiKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_f" colspan="2" rowspan="2"><h:outputText
										styleClass="outputText" id="lblYsnTCd"
										value="#{pc_Kea04101.propYsnTList.labelName}"
										style="#{pc_Kea04101.propYsnTList.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlYsnT"
										value="（予算単位／科目ｏｒ目的、予算単位別の場合）"></h:outputText></TD>
								</TR>
								<TR><TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border" width="550">
													<h:selectManyListbox
													styleClass="selectManyListbox" id="htmlYsnTList" size="7"
													style="width:99%"
													disabled="#{pc_Kea04101.propYsnTList.disabled}"
													readonly="#{pc_Kea04101.propYsnTList.readonly}"
													value="#{pc_Kea04101.propYsnTList.value}" tabindex="7">
													<f:selectItems value="#{pc_Kea04101.propYsnTList.list}" />
													</h:selectManyListbox>
												</TD>
												<TD class="clear_border" width="100"><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchYsnTCd2" tabindex="8" action="#{pc_Kea04101.doSearchYsnTCd2Action}"></hx:commandExButton><BR>
												<BR>
												<BR>
												<hx:commandExButton type="button"
													styleClass="commandExButton_listclear" id="clearYsnTCd"
													tabindex="9" onclick="return func_10(this, event);"></hx:commandExButton>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2"><h:outputText
										styleClass="outputText" id="lblMoku"
										value="#{pc_Kea04101.propMokuCd.labelName}"
										style="#{pc_Kea04101.propMokuCd.labelStyle}"></h:outputText></TH>
									<TD>
									
									
									<h:outputText styleClass="outputText" id="htmlMoku"
										value="（目的／予算単位別の場合）"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_g" width="130"><h:outputText
										styleClass="outputText" id="lblMokuFrom" value="開始"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText
												styleClass="inputText" id="htmlMokuCdFrom" size="15"
												disabled="#{pc_Kea04101.propMokuCdFrom.disabled}"
												maxlength="#{pc_Kea04101.propMokuCdFrom.maxLength}"
												readonly="#{pc_Kea04101.propMokuCdFrom.readonly}"
												style="#{pc_Kea04101.propMokuCdFrom.style}"
												value="#{pc_Kea04101.propMokuCdFrom.stringValue}"
												tabindex="10" onblur="func_4(this, event); copy_mokutekiTo(this, event);"></h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" id="searchMokuCdFrom" tabindex="11"
												action="#{pc_Kea04101.doSearchMokuCdFromAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMokuNameFrom"
												style="#{pc_Kea04101.propMokuNameFrom.style}"
												value="#{pc_Kea04101.propMokuNameFrom.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_bottom"></TH>
									<TH class="v_a"><h:outputText styleClass="outputText"
										id="lblMokuTo" value="終了"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText
												styleClass="inputText" id="htmlMokuCdTo" size="15"
												disabled="#{pc_Kea04101.propMokuCdTo.disabled}"
												maxlength="#{pc_Kea04101.propMokuCdTo.maxLength}"
												readonly="#{pc_Kea04101.propMokuCdTo.readonly}"
												style="#{pc_Kea04101.propMokuCdTo.style}"
												value="#{pc_Kea04101.propMokuCdTo.stringValue}"
												tabindex="12" onblur="return func_6(this, event);"></h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" id="searchMokuCdTo" tabindex="13"
												action="#{pc_Kea04101.doSearchMokuCdToAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMokuNameTo"
												style="#{pc_Kea04101.propMokuNameTo.style}"
												value="#{pc_Kea04101.propMokuNameTo.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2"><h:outputText
										styleClass="outputText" id="lblKmk"
										value="#{pc_Kea04101.propKmkCd.labelName}"
										style="#{pc_Kea04101.propKmkCd.labelStyle}"></h:outputText></TH>
									<TD>
									
									
									<h:outputText styleClass="outputText" id="htmlKmk"
										value="（科目／予算単位別の場合）"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_b" width="130"><h:outputText
										styleClass="outputText" id="lblKmkFrom" value="開始"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText
												styleClass="inputText" id="htmlKmkCdFrom" size="15"
												disabled="#{pc_Kea04101.propKmkCdFrom.disabled}"
												maxlength="#{pc_Kea04101.propKmkCdFrom.maxLength}"
												readonly="#{pc_Kea04101.propKmkCdFrom.readonly}"
												style="#{pc_Kea04101.propKmkCdFrom.style}"
												value="#{pc_Kea04101.propKmkCdFrom.stringValue}"
												tabindex="14" onblur="func_7(this, event); copy_kamokuTo(this, event);"></h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" id="searchKmkCdFrom" tabindex="15"
												action="#{pc_Kea04101.doSearchKmkCdFromAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlKmkNameFrom"
												style="#{pc_Kea04101.propKmkNameFrom.style}"
												value="#{pc_Kea04101.propKmkNameFrom.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_bottom"></TH>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblKmkTo" value="終了"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText
												styleClass="inputText" id="htmlKmkCdTo" size="15"
												disabled="#{pc_Kea04101.propKmkCdTo.disabled}"
												maxlength="#{pc_Kea04101.propKmkCdTo.maxLength}"
												readonly="#{pc_Kea04101.propKmkCdTo.readonly}"
												style="#{pc_Kea04101.propKmkCdTo.style}"
												value="#{pc_Kea04101.propKmkCdTo.stringValue}"
												tabindex="16" onblur="return func_9(this, event);"></h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" id="searchKmkCdTo" tabindex="17"
												action="#{pc_Kea04101.doSearchKmkCdToAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlKmkNameTo"
												style="#{pc_Kea04101.propKmkNameTo.style}"
												value="#{pc_Kea04101.propKmkNameTo.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblOutOrder"
										value="#{pc_Kea04101.propOutOrder.labelName}"
										style="#{pc_Kea04101.propOutOrder.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlOutOrder"
										tabindex="18"
										disabled="#{pc_Kea04101.propOutOrder.disabled}"
										readonly="#{pc_Kea04101.propOutOrder.readonly}"
										value="#{pc_Kea04101.propOutOrder.stringValue}">
										<f:selectItems value="#{pc_Kea04101.propOutOrder.list}" />
									</h:selectOneRadio></TD>
									
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblKingakuTani"
										value="#{pc_Kea04101.propKingakuTani.labelName}"
										style="#{pc_Kea04101.propKingakuTani.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlKingakuTani"
										tabindex="19"
										disabled="#{pc_Kea04101.propKingakuTani.disabled}"
										readonly="#{pc_Kea04101.propKingakuTani.readonly}"
										value="#{pc_Kea04101.propKingakuTani.stringValue}">
										<f:selectItems value="#{pc_Kea04101.propKingakuTani.list}" />
									</h:selectOneRadio></TD>
									
								</TR>
								<TR>
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblTitle"
										value="#{pc_Kea04101.propTitle.labelName}"
										style="#{pc_Kea04101.propTitle.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlTitle"
										size="85" disabled="#{pc_Kea04101.propTitle.disabled}"
										maxlength="#{pc_Kea04101.propTitle.maxLength}"
										readonly="#{pc_Kea04101.propTitle.readonly}"
										style="#{pc_Kea04101.propTitle.style}"
										value="#{pc_Kea04101.propTitle.stringValue}" tabindex="20"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="800" class="button_bar" cellpadding="1" cellspacing="0">
							<TBODY>
								<TR>
									<TD>
										<hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout" tabindex="30"
										action="#{pc_Kea04101.doPdfoutAction}"
										confirm="#{msg.SY_MSG_0019W}"
										rendered="#{pc_Kea04101.propPdfout.rendered}"
										disabled="#{pc_Kea04101.propPdfout.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="EXCEL作成"
										styleClass="commandExButton_out" id="excelout" tabindex="31"
										action="#{pc_Kea04101.doExcelOutAction}"
										confirm="#{msg.SY_MSG_0027W}"
										rendered="#{pc_Kea04101.propExcelout.rendered}"
										disabled="#{pc_Kea04101.propExcelout.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="CSV作成"
										styleClass="commandExButton_out" id="csvout" tabindex="32"
										action="#{pc_Kea04101.doCsvoutAction}"
										confirm="#{msg.SY_MSG_0020W}"
										rendered="#{pc_Kea04101.propCsvout.rendered}"
										disabled="#{pc_Kea04101.propCsvout.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="出力項目指定"
										styleClass="commandExButton_out" id="setoutput" tabindex="33"
										action="#{pc_Kea04101.doSetoutputAction}"
										rendered="#{pc_Kea04101.propSetoutput.rendered}"
										disabled="#{pc_Kea04101.propSetoutput.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="印刷"
										styleClass="commandExButton_out" id="print" tabindex="34"
										action="#{pc_Kea04101.doPrintAction}"
										confirm="#{msg.SY_MSG_0022W}"
										rendered="#{pc_Kea04101.propPrint.rendered}"
										disabled="#{pc_Kea04101.propPrint.disabled}"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<c:forEach items="${pc_Kea04101.titleMap}" var="form_title">		 
					 <input type=hidden id="<c:out value="${form_title.key}"/>"
					  name="<c:out value="${form_title.key}"/>"
					  value="<c:out value="${form_title.value}"/>">
			</c:forEach>
</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kea04101.propYsnTCdHidden.stringValue}"
				id="htmlYsnTCdHidden"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

