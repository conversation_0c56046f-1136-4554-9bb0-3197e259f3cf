<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KME_STJK_ERR" name="卒業条件エラー" prod_id="KM" description="『卒業判定』、『卒業見込判定』で卒業条件エラーとなった情報を持ちます。
『期末処理』で削除されます。">
<STATMENT><![CDATA[
KME_STJK_ERR
]]></STATMENT>
<COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生を一意に識別する為の番号です"/><COLUMN id="SOTGYO_HANTEI_KBN" name="卒業判定区分" type="string" length="1" lengthDP="0" byteLength="1" description="卒業判定か、卒業見込判定かを表す区分が設定されます。"/><COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="エラーとなった学生のみなし入学年度が設定されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="エラーとなった学生の入学学期が設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="エラーとなった学生のカリキュラム学科組織コードが設定されます。"/><COLUMN id="JOKEN_CD" name="条件コード" type="string" length="5" lengthDP="0" byteLength="5" description="エラーとなった卒業条件もしくは卒業見込条件コードが設定されます。"/><COLUMN id="FUSOK_SU" name="不足数" type="number" length="8" lengthDP="5" byteLength="0" description="この条件で不足した単位数または科目数またはＧＰＡ値が設定されます。"/><COLUMN id="HANTEI_FUSOKU_JOKEN_KBN" name="判定時不足条件区分" type="string" length="1" lengthDP="0" byteLength="1" description="卒業・進級判定エラーの不足数が単位数であるか、科目数であるか、ＧＰＡ値であるかを表します。"><CODE><CASE value="1" display="単位数"/><CASE value="2" display="科目数"/><CASE value="3" display="ＧＰＡ"/></CODE></COLUMN><COLUMN id="YOSO_NO" name="要素ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="卒業条件エラー、卒業見込条件エラーになった条件コードの対象要素ＮＯが設定されます。"/>
</TABLE>
