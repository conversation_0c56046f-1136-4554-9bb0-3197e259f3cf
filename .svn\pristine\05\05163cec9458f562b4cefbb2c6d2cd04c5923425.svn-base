<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="COB_SOT_GAK" name="卒業生学籍台帳" prod_id="CO" description="卒業時点の学籍情報です。">
<STATMENT><![CDATA[
(SELECT SEKI.KANRI_NO, SEKI.IDO_NO, SEKI.GAKUSEKI_CD, SEKI.SZK_GAKKA_CD, SEKI.CUR_GAKKA_CD, SEKI.NYUGAK_NENDO_CUR, SEKI.NYUGAK_GAKKI_NO_CUR, SEKI.NYUGAK_GAKKI_CUR_NAME, SEKI.GAKUNEN, SEKI.SEMESTER, SEKI.IDO_KEKKA_KBN, SEKI.IDO_START_DATE, SEKI.IDO_SBT_KBN FROM COB_SOT_GAK SEKI INNER JOIN (SELECT SEKI.KANRI_NO, MAX(SEKI.IDO_START_DATE) AS IDO_START_DATE FROM COB_SOT_GAK SEKI WHERE SEKI.IDO_KEKKA_KBN = '2' GROUP BY SEKI.KANRI_NO) IDO ON SEKI.KANRI_NO = IDO.KANRI_NO AND SEKI.IDO_START_DATE = IDO.IDO_START_DATE)
]]></STATMENT>
<COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生を一意に識別する為の番号です。"/><COLUMN id="IDO_NO" name="異動ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="異動の番号です。異動が登録される時に、システムが自動的に番号を振ります。"/><COLUMN id="GAKUSEKI_CD" name="学籍番号" type="string" length="10" lengthDP="0" byteLength="10" description="学籍番号です。"/><COLUMN id="SZK_GAKKA_CD" name="所属学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="卒業生が所属する学科組織のコードが設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="卒業生のみなし入学年度、学期に配当されているカリキュラム学科組織のうち、最下位の学科組織コードが設定されます。"/><COLUMN id="NYUGAK_NENDO_CUR" name="みなし入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="卒業生のカリキュラムの基準となる年度が設定されます。通常の学生は入学年度と同じになりますが、編入生等で実際の入学年度とカリキュラムの年度が異なる場合に、みなし入学年度にカリキュラムの基準となる年度が設定されます。"/><COLUMN id="NYUGAK_GAKKI_NO_CUR" name="みなし入学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="卒業生のカリキュラムの基準となる学期が設定されます。通常の学生は入学学期と同じになりますが、編入生等で実際の入学学期とカリキュラムの学期が異なる場合に、みなし入学期にカリキュラムの基準となる学期が設定されます。"/><COLUMN id="NYUGAK_GAKKI_CUR_NAME" name="みなし入学期名称" type="string" length="10" lengthDP="0" byteLength="30" description="卒業生のみなし入学期NOに対する学期名称が設定されます。"/><COLUMN id="GAKUNEN" name="学年" type="number" length="1" lengthDP="0" byteLength="0" description="卒業生の学年が設定されます。"/><COLUMN id="SEMESTER" name="セメスタ" type="number" length="2" lengthDP="0" byteLength="0" description="卒業生のセメスタが設定されます。"/><COLUMN id="IDO_KEKKA_KBN" name="異動申請結果区分" type="string" length="1" lengthDP="0" byteLength="1" description="異動出学申請の結果が設定されます。"><CODE><CASE value="1" display="申請中"/><CASE value="2" display="許可"/><CASE value="3" display="却下"/></CODE></COLUMN><COLUMN id="IDO_START_DATE" name="異動開始日" type="date" length="0" lengthDP="0" byteLength="0" description="学籍の開始日が設定されます。"/><COLUMN id="IDO_SBT_KBN" name="異動種別区分" type="string" length="1" lengthDP="0" byteLength="1" description="学籍の異動に関して、システム側で識別するシステム固定のコードが設定されます。"><CODE><CASE value="A" display="休学"/><CASE value="B" display="留学"/><CASE value="C" display="転科"/><CASE value="F" display="進級"/><CASE value="G" display="留年"/><CASE value="Z" display="入学"/><CASE value="1" display="卒業"/><CASE value="2" display="退学"/><CASE value="3" display="除籍"/><CASE value="4" display="入学辞退"/><CASE value="5" display="転出学"/><CASE value="6" display="聴講修了"/><CASE value="7" display="専攻修了"/></CODE></COLUMN>
</TABLE>
