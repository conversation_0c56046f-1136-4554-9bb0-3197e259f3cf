<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos03501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=Shift_JIS">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../theme/Master.css" rel="stylesheet"
	type="text/css">
<TITLE>Cos03501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<script language="JavaScript">
	
		
//一括チェック	
function func_check_on(thisObj, thisEvent, flg) {
	if(flg == 0){
		check('htmlAppList','htmlAppChecked');
	}else if(flg == 1){
		check('htmlAppList','htmlAppChecked');
	}
}

//一括解除	
function func_check_off(thisObj, thisEvent, flg) {
	if(flg == 0){
		uncheck('htmlAppList','htmlAppChecked');
	}else if(flg == 1){
		uncheck('htmlAppList','htmlAppChecked');
	}
}
function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1";
	indirectClick('register');
	return true;	
}
function confirmCancel() {
	return false;
}
	
</script>


</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onLoad="resizeTo('1024', '768');">
		<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cos03501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />

		<!-- ヘッダーへのデータセット領域 -->
		<DIV style="display:none;">
			<hx:commandExButton type="submit" value="閉じる"
				styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cos03501.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Cos03501.funcId}"></h:outputText>
			<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
			<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Cos03501.screenName}"></h:outputText>
		</DIV>

		<!--↓outer↓-->
		<DIV class="outer">

		<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
			<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" 
				styleClass="outputText" escape="false">
			</h:outputText>
		</FIELDSET>
		
		<!--↓content↓-->
		<DIV class="head_button_area" >
		<!-- ↓ここに戻る／閉じるボタンを配置 -->

		<!-- ↑ここに戻る／閉じるボタンを配置 -->
		</DIV>

		<DIV id="content">
			<DIV class="column" align="center">
				<TABLE border="0" cellpadding="0" cellspacing="0" width="700">
					<TBODY>
						<TR>
							<TD>
								<TABLE border="0" width="100%" class="button_bar" height="100%">
									<TBODY>
										<TR>
											<TD align="right" height="100%">
												<hx:commandExButton
													type="submit" value="登録対象変更" styleClass="commandExButton"
													id="torokuTaishoHenko" action="#{pc_Cos03501.doTorokuTaishoHenkoAction}">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
					<TBODY>
						<TR>
							<TD>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
									<TR>
										<TH nowrap class="v_a" width="160">
											<h:outputText
												styleClass="outputText" id="lblApplicationID"
												value="#{pc_Cos03501.propApplicationID.labelName}"
												style="#{pc_Cos03501.propApplicationID.labelStyle}">
											</h:outputText>
										</TH>
										<TD align="left">
											<h:outputText styleClass="outputText" id="htmlApplicationID"
												value="#{pc_Cos03501.propApplicationID.stringValue}"
												style="#{pc_Cos03501.propApplicationID.style}">
											</h:outputText></TD>
									</TR>
									<TR>
										<TH nowrap  class="v_a">
											<h:outputText
												styleClass="outputText" id="lblApplicationName"
												value="#{pc_Cos03501.propApplicationName.labelName}"
												style="#{pc_Cos03501.propApplicationName.labelStyle}">
											</h:outputText>
										</TH>
										<TD align="left">
											<h:outputText styleClass="outputText" id="htmlApplicationName"
												value="#{pc_Cos03501.propApplicationName.stringValue}"
												style="#{pc_Cos03501.propApplicationName.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap  class="v_a">
											<h:outputText
												styleClass="outputText" id="lblDisplayID"
												value="#{pc_Cos03501.propDisplayID.labelName}"
												style="#{pc_Cos03501.propDisplayID.labelStyle}">
											</h:outputText>
										</TH>
										<TD align="left">
											<h:outputText styleClass="outputText" id="htmlDisplayID"
												value="#{pc_Cos03501.propDisplayID.stringValue}"
												style="#{pc_Cos03501.propDisplayID.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap  class="v_a">
											<h:outputText
												styleClass="outputText" id="lblDisplayName"
												value="#{pc_Cos03501.propDisplayName.labelName}"
												style="#{pc_Cos03501.propDisplayName.labelStyle}">
											</h:outputText>
										</TH>
										<TD align="left">
											<h:outputText styleClass="outputText" id="htmlDisplayName"
												value="#{pc_Cos03501.propDisplayName.stringValue}"
												style="#{pc_Cos03501.propDisplayName.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap  class="v_a">
											<h:outputText
												styleClass="outputText" id="lblTitle"
												value="#{pc_Cos03501.propTitle.labelName}"
												style="#{pc_Cos03501.propTitle.labelStyle}">
											</h:outputText>
										</TH>
										<TD align="left">
											<h:inputText styleClass="inputText"
												id="htmlTitle" size="50"
												value="#{pc_Cos03501.propTitle.stringValue}"
												style="#{pc_Cos03501.propTitle.style}"
												disabled="#{pc_Cos03501.propTitle.disabled}"
												maxlength="#{pc_Cos03501.propTitle.maxLength}"
												tabindex="1">
											</h:inputText>
										</TD>
									</TR>
				                    <TR>
										<TH nowrap class="v_a">
											<h:outputText styleClass="outputText" id="lblInputFile"
											value="#{pc_Cos03501.propInputFile.labelName}"
											style="#{pc_Cos03501.propInputFile.labelStyle}"></h:outputText>
										</TH>
										<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
												<TBODY>
													<TR>
														<TD class="clear_border">
															<hx:fileupload styleClass="fileupload"
																id="htmlInputFile"
																value="#{pc_Cos03501.propInputFile.value}"
																disabled=""
																readonly=""
																style="#{pc_Cos03501.propInputFile.style};width:600px"
																tabindex="2">
															<hx:fileProp name="fileName"
																value="#{pc_Cos03501.propInputFile.fileName}" />
															<hx:fileProp name="contentType"
																value="#{pc_Cos03501.propInputFile.contentType}" />
															</hx:fileupload>
														</TD>
													</TR>
													<TR>
														<TD class="clear_border">
															<h:outputText 
																styleClass="outputText" 
																id="htmlPreInputFile" 
																value="#{pc_Cos03501.propPreInputFile.value}" 
																style="#{pc_Cos03501.propPreInputFile.style}">
															</h:outputText>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
				                    </TR>
									<TR>
										<TH nowrap  class="v_a"><h:outputText
											styleClass="outputText" id="text1" value="登録・更新の通知"></h:outputText>
										</TH>
										<TD>
											<h:selectBooleanCheckbox
												styleClass="selectBooleanCheckbox"
												id="htmlRegistNoteFlag"
												disabled="#{pc_Cos03501.propRegistNoteFlag.disabled}"
												readonly="#{pc_Cos03501.propRegistNoteFlag.readonly}"
												rendered="#{pc_Cos03501.propRegistNoteFlag.rendered}"
												required="#{pc_Cos03501.propRegistNoteFlag.required}"
												style="#{pc_Cos03501.propRegistNoteFlag.style}"
												value="#{pc_Cos03501.propRegistNoteFlag.checked}"
												tabindex="3">
											</h:selectBooleanCheckbox><h:outputText styleClass="outputText"
												id="text2" value="登録後初回起動時に通知する">
											</h:outputText>&nbsp;&nbsp;
											<h:selectBooleanCheckbox
												styleClass="selectBooleanCheckbox"
												id="htmlUpdateNoteFlag"
												disabled="#{pc_Cos03501.propUpdateNoteFlag.disabled}"
												readonly="#{pc_Cos03501.propUpdateNoteFlag.readonly}"
												rendered="#{pc_Cos03501.propUpdateNoteFlag.rendered}"
												required="#{pc_Cos03501.propUpdateNoteFlag.required}"
												style="#{pc_Cos03501.propUpdateNoteFlag.style}"
												value="#{pc_Cos03501.propUpdateNoteFlag.checked}"
												tabindex="4">
											</h:selectBooleanCheckbox><h:outputText styleClass="outputText" 
												id="text3" value="更新後初回起動時に通知する">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap class="v_a">
											<h:outputText styleClass="outputText" id="lblRegistMessage"
												value="#{pc_Cos03501.propRegistMessage.labelName}"
												style="#{pc_Cos03501.propRegistMessage.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputTextarea styleClass="inputTextarea"
												id="htmlRegistMessage" cols="70" rows="2"
												disabled="#{pc_Cos03501.propRegistMessage.disabled}"
												value="#{pc_Cos03501.propRegistMessage.stringValue}"
												readonly="#{pc_Cos03501.propRegistMessage.readonly}"
												style="#{pc_Cos03501.propRegistMessage.style}; height: 28px;"
												tabindex="5">
											</h:inputTextarea>
										</TD>
									</TR>
									<TR>
										<TH nowrap class="v_a">
											<h:outputText styleClass="outputText" id="lblUpdateMessage"
												value="#{pc_Cos03501.propUpdateMessage.labelName}"
												style="#{pc_Cos03501.propUpdateMessage.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputTextarea styleClass="inputTextarea"
												id="htmlUpdateMessage" cols="70" rows="2"
												disabled="#{pc_Cos03501.propUpdateMessage.disabled}"
												value="#{pc_Cos03501.propUpdateMessage.stringValue}"
												readonly="#{pc_Cos03501.propUpdateMessage.readonly}"
												style="#{pc_Cos03501.propUpdateMessage.style}; height: 28px;"
												tabindex="6">
											</h:inputTextarea>
										</TD>
									</TR>
								</TABLE>
								<BR>
								<TABLE border="0" width="100%" class="button_bar" height="100%">
									<TBODY>
										<TR>
											<TD align="center" valign="middle" height="100%">
												<hx:commandExButton
													type="submit" value="確定" styleClass="commandExButton_out"
													id="register" action="#{pc_Cos03501.doRegisterAction}"
													confirm="#{msg.SY_MSG_0001W}"
													tabindex="7">
												</hx:commandExButton>
												<hx:commandExButton type="submit" value="削除"
													styleClass="commandExButton_dat" id="delete"
													action="#{pc_Cos03501.doDeleteAction}">
												</hx:commandExButton>
												<hx:commandExButton type="submit" value="クリア"
													styleClass="commandExButton_dat" id="close"
													action="#{pc_Cos03501.doClearAction}"
													tabindex="8">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<h:inputHidden 
					value="#{pc_Cos03501.propHiddenTitle.stringValue}" 
					id="htmlHiddenTitle">
				</h:inputHidden>
				<h:inputHidden
					value="#{pc_Cos03501.propExecutable.integerValue}"
					id="htmlExecutable">
					<f:convertNumber />
				</h:inputHidden>
					<!-- ↑ここにコンポーネントを配置 -->
			</DIV>
		</DIV>
		<!--↑content↑--></DIV>
		<!--↑outer↑-->
		<!-- フッダーインクルード -->
		<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
