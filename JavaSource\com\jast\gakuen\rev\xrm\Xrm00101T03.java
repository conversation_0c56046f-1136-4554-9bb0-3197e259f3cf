package com.jast.gakuen.rev.xrm;

import javax.faces.component.html.HtmlDataTable;
import javax.faces.component.html.HtmlInputHidden;
import javax.faces.component.html.HtmlInputText;
import javax.faces.component.html.HtmlOutputText;
import javax.faces.component.html.HtmlSelectBooleanCheckbox;

import com.ibm.faces.component.html.HtmlCommandExButton;
import com.jast.gakuen.framework.ActionBase;
import com.jast.gakuen.framework.constant.ActionConst;
import com.jast.gakuen.framework.property.PropDataTable;
import com.jast.gakuen.framework.property.PropInputHidden;
import com.jast.gakuen.framework.property.PropInputText;
import com.jast.gakuen.framework.property.PropInputTextarea;
import com.jast.gakuen.framework.property.PropOutputText;
import com.jast.gakuen.framework.property.PropSelectBooleanCheckbox;
import com.jast.gakuen.framework.property.PropSelectOneRadio;
import com.jast.gakuen.framework.util.UtilLog;
import com.jast.gakuen.rev.RevPageCodeBase;
import com.jast.gakuen.rev.gh.constant.GhConstant;
import com.jast.gakuen.rev.xrm.action.Xrm00101T03Act;
import com.ibm.faces.component.html.HtmlScriptCollector;
import javax.faces.component.html.HtmlInputTextarea;
import javax.faces.component.html.HtmlSelectOneRadio;
import javax.faces.component.html.HtmlCommandLink;
/**
 * <AUTHOR>
 *
 */
public class Xrm00101T03 extends RevPageCodeBase {
	
	// 画面ID
	public final static String FORMID = "Xrm00101T03";
	// Actionクラス
	private Xrm00101T03Act action = new Xrm00101T03Act();
	
	protected HtmlOutputText htmlFuncId;
	protected HtmlOutputText htmlLoginId;
	protected HtmlOutputText htmlScrnName;
	protected HtmlOutputText message;
	protected PropInputHidden propKanriNo;
	protected HtmlOutputText htmlCount;
	protected HtmlOutputText lblCount;
	protected HtmlDataTable htmlPaywList;
	protected PropDataTable propPaywList;
	protected PropOutputText propPaywListNendo;
	protected PropOutputText propPaywListPayCd;
	protected PropOutputText propPaywListPatternCd;
	protected PropOutputText propPaywListBunnoKbnCd;
	protected PropOutputText propPaywListPayName;
	protected PropOutputText propPaywListBunkatsuNo;
	protected PropOutputText propPaywListPayLimit;
	protected PropOutputText propPaywListOutputDate;
	protected HtmlSelectBooleanCheckbox htmlPayChecked;
	protected HtmlOutputText htmlPayCd;
	protected HtmlOutputText htmlPatternCd;
	protected HtmlOutputText htmlBunnoKbnCd;
	protected HtmlOutputText lblPaywNendo;
	protected HtmlOutputText htmlPaywNendo;
	protected HtmlOutputText lblPayName;
	protected HtmlOutputText htmlPayName;
	protected HtmlOutputText htmlBunkatsuNo;
	protected HtmlOutputText lblPayLimit;
	protected HtmlOutputText htmlPayLimit;
	protected HtmlOutputText htmpPayCd;
	protected HtmlOutputText htmlGakusekiCdLabel;
	protected HtmlOutputText lblGakseiName;
	protected HtmlOutputText htmlGakseiName;
	protected PropInputText propGakusekiCd;
	protected PropOutputText propGakseiName;
	protected HtmlOutputText lblGakusekiCd;
	protected HtmlInputText htmlGakusekiCd;
	protected HtmlOutputText lblNendo;
	protected HtmlOutputText lblPayCd1;
	protected HtmlOutputText lblPayCd2;
	protected HtmlOutputText lblPatternCd1;
	protected HtmlOutputText lblPatternCd2;
	protected HtmlOutputText lblBunnoKbnCd1;
	protected HtmlOutputText lblBunnoKbnCd2;
	protected HtmlOutputText lblBunkatsuNo1;
	protected HtmlOutputText lblBunkatsuNo2;
	protected HtmlCommandExButton closeDisp;
	protected HtmlCommandExButton htmlPayTab;
	protected HtmlCommandExButton htmlPayGakTab;
	protected HtmlCommandExButton htmlGakseiTab;
	protected HtmlCommandExButton GakSearch;
	protected HtmlCommandExButton pdfou;
	protected HtmlCommandExButton csvou;
	protected HtmlCommandExButton setoutpu;
	protected HtmlCommandExButton prin;
	protected HtmlInputHidden htmlActiveControlSearch;
	protected HtmlInputHidden htmlExecutableSearch;
	protected HtmlInputHidden htmlHidScroll;
	protected PropInputHidden propActiveControlSearch;
	protected PropInputHidden propExecutableSearch;
	protected PropInputText propHakkouDate;
	protected HtmlOutputText lblTushinTxt;
	protected PropInputTextarea propTsushinText;	 
	protected PropSelectBooleanCheckbox propTushinKbn;
	protected PropSelectOneRadio propOutPutKbn;  
	protected HtmlInputText htmlHurikomininCd;
	protected PropInputText propHurikomininCd;
	protected HtmlOutputText lblhurikomininCd;
	//期限のチェックボックス
	protected PropSelectBooleanCheckbox propKigenkbn;
	//納入期限
	protected PropInputText propNonyuDate;
	//有効期限
	protected PropInputText propYukoDate;

	protected HtmlScriptCollector scriptCollector1;
	protected HtmlOutputText lblHakkouDate;
	protected HtmlInputText htmlHakkouDate;
	protected HtmlOutputText lblTsushinText;
	protected HtmlInputTextarea htmlTsushinText;
	protected HtmlOutputText lblOutPutTxt;
	protected HtmlSelectOneRadio radio1;
	protected HtmlOutputText lblOutputDate;
	protected HtmlOutputText htmlOutputDate;
	protected HtmlCommandExButton pdfout;
	protected HtmlSelectBooleanCheckbox htmlTusinKbn;
	protected HtmlOutputText htmlLinkKikanUseChkBox;
	protected HtmlCommandLink linkKikanUseChkBox;
	protected HtmlCommandExButton unselect;
	protected HtmlOutputText htmlpropHurikomininCd;
	protected HtmlCommandExButton popGakSearch;
	/**
	 * getter アクションクラス.
	 * @return action を戻します。
	 */
	protected ActionBase getAction() {
		return action;
	}
	
	/**
	 * getter 画面ID.
	 * @return FORMID を戻します。
	 */
	public String getFormId() {
		return FORMID;
	}
	
	/**
	 * @return null 画面を閉じます。
	 */
	public String doCloseDispAction() {
		this.removeFromSession(this.getFuncId());
		try {
			selfClose();
		} catch (Exception e) {
			UtilLog.error(getClass(), e);
			throw new RuntimeException(e);
		}
		return null;
	}
	
	/**
	 * 戻る処理 <br>
	 *
	 * @return 出力内容設定画面へ戻る。
	 */
	public String doReturnAction() {
		return action.action(this, ActionConst.ACTION_RETURN_DISP);
	}
	
	/**
	 * @return Xrm00101T01.FORMID を戻します。
	 */
	public String doLinkPayTabAction() {
		//this.removeFromSession(this.getFuncId());
		//return Xrm00101T01.FORMID;
		return action.action(this, "payTab");
	}
	
	/**
	 * @return Xrm00101T02.FORMID を戻します。
	 */
	public String doLinkPayGakTabAction() {
		//this.removeFromSession(this.getFuncId());
		//return Xrm00101T02.FORMID;
		return action.action(this, "payGakTab");
	}
	//	発行日付
	public PropInputText getpropHakkouDate() {
		if (propHakkouDate == null) {
			propHakkouDate = new PropInputText();
			propHakkouDate.setName(new String("発行日付"));
			propHakkouDate.setId(new String("htmlHakkouDate"));
			propHakkouDate.setItemId(new String("XrmNyukinErr_NyukinDate"));
			propHakkouDate.setRequired(true);
		}
		return propHakkouDate;
	}
	public void setPropHakkouDate(PropInputText propHakkouDate) {
		this.propHakkouDate = propHakkouDate;
	}
	
	
	/**
	 * @return actionの戻り値 を戻します。
	 */
	public String doPopGakSearchAction() {
		return action.action(this, GhConstant.GHACTION_POPGAKSEARCH);
	}
	/**
	 * @return actionの戻り値 を戻します。
	 */
	public String doSearchAction() {
		return action.action(this, ActionConst.ACTION_SEARCH);
	}
	
	/**
	 * @return actionの戻り値 を戻します。
	 */
	public String doUnselectAction() {
		return action.action(this, ActionConst.ACTION_UNSELECT);
	}
	
	/**
	 * @return actionの戻り値 を戻します。
	 */
	public String doPdfoutAction() {
		return action.action(this, ActionConst.ACTION_EXEC);
	}
	
	/**
	 * @return actionの戻り値 を戻します。
	 */
	public String doCsvoutAction() {
		return action.action(this, ActionConst.ACTION_CSVOUT);
	}
	
	/**
	 * @return actionの戻り値 を戻します。
	 */
	public String doSetoutputAction() {
		return action.action(this, ActionConst.ACTION_SETOUTPUT);
	}
	
	/**
	 * @property0 rows=10
	 */
	public PropDataTable getPropPaywList() {
		if (propPaywList == null) {
			propPaywList = new PropDataTable();
			propPaywList.setRows(10);
		}
		return propPaywList;
	}
	
	/**
	 * @property0 rows=10
	 */
	public void setPropPaywList(PropDataTable propPaywList) {
		this.propPaywList = propPaywList;
	}
	/** 
	 * @property0 itemId="GhbItem_Nouhukinkana
	 * @property1 name="振込依頼人コード"
	 * @property2 id="propHurikomininCd"
	 */

	protected HtmlOutputText getlblhurikomininCd() {
		if (lblhurikomininCd == null) {
			lblhurikomininCd = (HtmlOutputText) findComponentInRoot("lblhurikomininCd");
		}
		return lblhurikomininCd;
	}

	/** 
	 * @property0 itemId="GhbItem_Nouhukinkana"
	 * @property1 id="propHurikomininCd"
	 * @property2 required=true
	 * @property3 name=納付金名称カナ
	 */
	public PropInputText getpropHurikomininCd() {
		if (propHurikomininCd == null) {
			propHurikomininCd = new PropInputText();
			propHurikomininCd.setItemId(new String("GhgFurikomi_FurikomiIraiCd"));
			propHurikomininCd.setId(new String("propHurikomininCd"));
			propHurikomininCd.setRequired(false);
			propHurikomininCd.setName(new String("振込依頼人コード"));
		}
		return propHurikomininCd;
	}
	
	
	public void setpropHurikomininCdl(PropInputText propHurikomininCd) {
		this.propHurikomininCd = propHurikomininCd;
	}
	protected HtmlInputText gethtmlHurikomininCd() {
		if (htmlHurikomininCd == null) {
			htmlHurikomininCd = (HtmlInputText) findComponentInRoot("htmlHurikomininCd");
		}
		return htmlHurikomininCd;
	}

	/**
	 * @property0 itemId="GhgPayh_Nendo"
	 * @property0 id="lblPaywNendo"
	 */
	public PropOutputText getPropPaywListNendo() {
		if (propPaywListNendo == null) {
			propPaywListNendo = new PropOutputText();
			propPaywListNendo.setItemId(new String("GhgPayh_Nendo"));
			propPaywListNendo.setId(new String("lblPaywNendo"));
			propPaywListNendo.setName(new String("割当年度"));
		}
		return propPaywListNendo;
	}
	/**
	 * @property0 itemId="GhgPayh_Nendo"
	 * @property0 id="lblPaywNendo"
	 */
	public void setPropPaywListNendo(PropOutputText propPaywListNendo) {
		this.propPaywListNendo = propPaywListNendo;
	}
	/**
	 * @property0 itemId="GhgPayh_PayCd"
	 * @property1 id="lblPayCd1"
	 */
	public PropOutputText getPropPaywListPayCd() {
		if (propPaywListPayCd == null) {
			propPaywListPayCd = new PropOutputText();
			propPaywListPayCd.setItemId(new String("GhgPayh_PayCd"));
			propPaywListPayCd.setId(new String("lblPayCd1"));
		}
		return propPaywListPayCd;
	}
	
	/**
	 * @property0 itemId="GhgPayh_PayCd"
	 * @property1 id="lblPayCd1"
	 */
	public void setPropPaywListPayCd(PropOutputText propPaywListPayCd) {
		this.propPaywListPayCd = propPaywListPayCd;
	}
	
	/**
	 * @property0 itemId="GhgPayh_PatternCd"
	 * @property1 id="lblPatternCd1"
	 */
	public PropOutputText getPropPaywListPatternCd() {
		if (propPaywListPatternCd == null) {
			propPaywListPatternCd = new PropOutputText();
			propPaywListPatternCd.setItemId(new String("GhgPayh_PatternCd"));
			propPaywListPatternCd.setId(new String("lblPatternCd1"));
		}
		return propPaywListPatternCd;
	}
	
	/**
	 * @property0 itemId="GhgPayh_PatternCd"
	 * @property1 id="lblPatternCd1"
	 */
	public void setPropPaywListPatternCd(PropOutputText propPaywListPatternCd) {
		this.propPaywListPatternCd = propPaywListPatternCd;
	}
	//通信欄(ぺイジー)	
	public PropInputTextarea getPropTsushinText() {
		if (propTsushinText == null) {
			propTsushinText = new PropInputTextarea();
			propTsushinText.setId(new String("htmlTsushinText"));
			propTsushinText.setLabelName(new String("通信欄(ぺイジー)"));
		}
		return propTsushinText;
	}
	public void setPropTsushinText(PropInputTextarea propTsushinText) {
		this.propTsushinText = propTsushinText;
	}
	
	
	/**
	 * @property0 itemId="GhgPayh_BunnoKbnCd"
	 * @property1 id="lblBunnoKbnCd1"
	 */
	public PropOutputText getPropPaywListBunnoKbnCd() {
		if (propPaywListBunnoKbnCd == null) {
			propPaywListBunnoKbnCd = new PropOutputText();
			propPaywListBunnoKbnCd.setItemId(new String("GhgPayh_BunnoKbnCd"));
			propPaywListBunnoKbnCd.setId(new String("lblBunnoKbnCd1"));
		}
		return propPaywListBunnoKbnCd;
	}
	
	/**
	 * @property0 itemId="GhgPayh_BunnoKbnCd"
	 * @property1 id="lblBunnoKbnCd1"
	 */
	public void setPropPaywListBunnoKbnCd(PropOutputText propPaywListBunnoKbnCd) {
		this.propPaywListBunnoKbnCd = propPaywListBunnoKbnCd;
	}
	
	/**
	 * @property0 itemId="GhgPayh_PayName"
	 * @property1 id="lblPayName"
	 */
	public PropOutputText getPropPaywListPayName() {
		if (propPaywListPayName == null) {
			propPaywListPayName = new PropOutputText();
			propPaywListPayName.setItemId(new String("GhgPayh_PayName"));
			propPaywListPayName.setId(new String("lblPayName"));
		}
		return propPaywListPayName;
	}
	
	/**
	 * @property0 itemId="GhgPayh_PayName"
	 * @property1 id="lblPayName"
	 */
	public void setPropPaywListPayName(PropOutputText propPaywListPayName) {
		this.propPaywListPayName = propPaywListPayName;
	}
	
	/**
	 * @property0 itemId="GhgPayhBun_BunkatsuNo"
	 * @property1 id="lblBunkatsuNo1"
	 */
	public PropOutputText getPropPaywListBunkatsuNo() {
		if (propPaywListBunkatsuNo == null) {
			propPaywListBunkatsuNo = new PropOutputText();
			propPaywListBunkatsuNo.setItemId(new String("GhgPayhBun_BunkatsuNo"));
			propPaywListBunkatsuNo.setId(new String("lblBunkatsuNo1"));
		}
		return propPaywListBunkatsuNo;
	}
	
	/**
	 * @property0 itemId="GhgPayhBun_BunkatsuNo"
	 * @property1 id="lblBunkatsuNo1"
	 */
	public void setPropPaywListBunkatsuNo(PropOutputText propPaywListBunkatsuNo) {
		this.propPaywListBunkatsuNo = propPaywListBunkatsuNo;
	}
	
	/**
	 * @property0 itemId="GhgPayhBun_PayLimit"
	 * @property1 id="lblPayLimit"
	 */
	public PropOutputText getPropPaywListPayLimit() {
		if (propPaywListPayLimit == null) {
			propPaywListPayLimit = new PropOutputText();
			propPaywListPayLimit.setItemId(new String("GhgPayhBun_PayLimit"));
			propPaywListPayLimit.setId(new String("lblPayLimit"));
		}
		return propPaywListPayLimit;
	}
	
	/**
	 * @property0 itemId="GhgPayhBun_PayLimit"
	 * @property1 id="lblPayLimit"
	 */
	public void setPropPaywListPayLimit(PropOutputText propPaywListPayLimit) {
		this.propPaywListPayLimit = propPaywListPayLimit;
	}
	
	/**
	 * @return htmpPayCd を戻します。
	 */
	protected HtmlOutputText getHtmpPayCd() {
		if (htmpPayCd == null) {
			htmpPayCd = (HtmlOutputText) findComponentInRoot("htmpPayCd");
		}
		return htmpPayCd;
	}
	/**
	 * @property0 id="htmlExecutableSearch"
	 * @property1 integerValue=0
	 */
	public PropInputHidden getPropExecutableSearch() {
		if (propExecutableSearch == null) {
			propExecutableSearch = new PropInputHidden();
			propExecutableSearch.setId(new String("htmlExecutableSearch"));
			propExecutableSearch.setIntegerValue(new Integer(0));
		}
		return propExecutableSearch;
	}
	
	/**
	 * @property0 id="htmlExecutableSearch"
	 * @property1 integerValue=0
	 */
	public void setPropExecutableSearch(PropInputHidden propExecutableSearch) {
		this.propExecutableSearch = propExecutableSearch;
	}
	
	/**
	 * @property0 id="htmlActiveControlSearch"
	 * @property1 integerValue=0
	 */
	public PropInputHidden getPropActiveControlSearch() {
		if (propActiveControlSearch == null) {
			propActiveControlSearch = new PropInputHidden();
			propActiveControlSearch
			.setId(new String("htmlActiveControlSearch"));
			propActiveControlSearch.setIntegerValue(new Integer(0));
		}
		return propActiveControlSearch;
	}
	
	/**
	 * @property0 id="htmlActiveControlSearch"
	 * @property1 integerValue=0
	 */
	public void setPropActiveControlSearch(
			PropInputHidden propActiveControlSearch) {
		this.propActiveControlSearch = propActiveControlSearch;
	}
	
	/**
	 * @property0 id="lblOutputDate"
	 * @property1 name="出力日"
	 */
	public PropOutputText getPropPaywListOutputDate() {
		if (propPaywListOutputDate == null) {
			propPaywListOutputDate = new PropOutputText();
			propPaywListOutputDate.setId(new String("lblOutputDate"));
			propPaywListOutputDate.setName("出力日");
		}
		return propPaywListOutputDate;
	}
	/**
	 * @property0 id="lblOutputDate"
	 * @property1 name="出力日"
	 */
	public void setPropPaywListOutputDate(PropOutputText propPaywListOutputDate) {
		this.propPaywListOutputDate = propPaywListOutputDate;
	}
	
	
	/**
	 * @return htmlGakusekiCdLabel を戻します。
	 */
	public HtmlOutputText getHtmlGakusekiCdLabel() {
		return htmlGakusekiCdLabel;
	}
	
	/**
	 * @property0 id="htmlGakusekiCd"
	 * @property1 itemId="GheGakseki_GakusekiCd"
	 */
	public PropInputText getPropGakusekiCd() {
		if (propGakusekiCd == null) {
			propGakusekiCd = new PropInputText();
			propGakusekiCd.setId(new String("htmlGakusekiCd"));
			propGakusekiCd.setItemId(new String("GheGakseki_GakusekiCd"));
			propGakusekiCd.setRequired(false);
		}
		return propGakusekiCd;
	}
	/**
	 * @property0 id="htmlGakusekiCd"
	 * @property1 itemId="GheGakseki_GakusekiCd"
	 */
	public void setPropGakusekiCd(PropInputText propGakusekiCd) {
		this.propGakusekiCd = propGakusekiCd;
	}
	
	/**
	 * @property0 longValue=0
	 */
	public PropInputHidden getPropKanriNo() {
		if (propKanriNo == null) {
			propKanriNo = new PropInputHidden();
			propKanriNo.setLongValue(new Long(0));
		}
		return propKanriNo;
	}
	/**
	 * @property0 longValue=0
	 */
	public void setPropKanriNo(PropInputHidden propKanriNo) {
		this.propKanriNo = propKanriNo;
	}
	   //通信区分

	public PropSelectBooleanCheckbox getPropTushinKbn() {
		if (propTushinKbn == null) {
			propTushinKbn = new PropSelectBooleanCheckbox();
			propTushinKbn.setItemId(new String("GhgPayh_SotugyoHanteiFlg"));
			propTushinKbn.setName(new String("通信区分"));
			propTushinKbn.setChecked(false);
		}
		return propTushinKbn;
	}
	//追加
    public void setPropTushinKbn(PropSelectBooleanCheckbox propTushinKbn) {
        this.propTushinKbn = propTushinKbn;
    }
	
	public String doChkAction() {
		return action.action(this, "clickcheakBox");
	}
    //期限区分
	public PropSelectBooleanCheckbox getPropKigenkbn() {
		if (propKigenkbn == null) {
			propKigenkbn = new PropSelectBooleanCheckbox();
			propKigenkbn.setItemId(new String("GhgPayh_SotugyoHanteiFlg"));
			propKigenkbn.setName(new String("期限区分"));
			propKigenkbn.setChecked(false);
		}
		return propKigenkbn;
	}
	//追加
    public void setPropKigenkbn(PropSelectBooleanCheckbox propKigenkbn) {
        this.propKigenkbn = propKigenkbn;
    }

	/**
	 * @property0 itemId="GheGaksei_Name"
	 * @property1 id="htmlGakseiName"
	 * @property2 name="学生氏名"
	 */
	public PropOutputText getPropGakseiName() {
		if (propGakseiName == null) {
			propGakseiName = new PropOutputText();
			propGakseiName.setItemId(new String("GheGaksei_Name"));
			propGakseiName.setId(new String("htmlGakekiName"));
			propGakseiName.setName("学生氏名");
		}
		return propGakseiName;
	}
	//出力区分  
	public PropSelectOneRadio getPropOutPutKbn() {
		if (propOutPutKbn == null) {
			propOutPutKbn = new PropSelectOneRadio();
			propOutPutKbn.setId(new String("htmlOutPutKbn"));
			propOutPutKbn.setStringValue(new String("1"));
			propOutPutKbn.setName(new String("納付金出力条件指定"));
		}
		return propOutPutKbn;
	}
	public void setPropOutPutKbn(PropSelectOneRadio propOutPutKbn) {
		this.propOutPutKbn = propOutPutKbn;
	}
//	納入期限
    public PropInputText getPropNonyuDate() {
        if (propNonyuDate == null) {
        	propNonyuDate = new PropInputText();
        	propNonyuDate.setName(new String("納入期限"));
        	propNonyuDate.setId(new String("htmlNonyuDate"));
        	propNonyuDate.setItemId(new String("XrmNyukinErr_NyukinDate"));
//        	propNonyuDate.setReadonly(true);
        	propNonyuDate.setDisabled(true);

        }
        return propNonyuDate;
    }
    public void setPropNonyuDate(PropInputText propNonyuDate) {
        this.propNonyuDate = propNonyuDate;
    }
//	有効期限
    public PropInputText getPropYukoDate() {
        if (propYukoDate == null) {
        	propYukoDate = new PropInputText();
        	propYukoDate.setName(new String("有効期限"));
        	propYukoDate.setId(new String("htmlYukoDate"));
        	propYukoDate.setItemId(new String("XrmNyukinErr_NyukinDate"));
//        	propYukoDate.setReadonly(true);
        	propYukoDate.setDisabled(true);
        }
        return propYukoDate;
    }
    public void setPropYukoDate(PropInputText propYukoDate) {
        this.propYukoDate = propYukoDate;
    }
	public String doChkKigenAction() {
		return action.action(this, "clickKigencheakBox");
	}

	/**
	 * @property0 itemId="GheGaksei_Name"
	 * @property1 id="htmlGakseiName"
	 * @property2 name="学生氏名"
	 */
	public void setPropGakseiName(PropOutputText propGakseiName) {
		this.propGakseiName = propGakseiName;
	}
	
	/**
	 * @return closeDisp を戻します。
	 */
	protected HtmlCommandExButton getCloseDisp() {
		if (closeDisp == null) {
			closeDisp = (HtmlCommandExButton) findComponentInRoot("closeDisp");
		}
		return closeDisp;
	}
	/**
	 * @return htmlPayTab を戻します。
	 */
	protected HtmlCommandExButton getHtmlPayTab() {
		if (htmlPayTab == null) {
			htmlPayTab = (HtmlCommandExButton) findComponentInRoot("htmlPayTab");
		}
		return htmlPayTab;
	}
	/**
	 * @return lblGakusekiCd を戻します。
	 */
	protected HtmlOutputText getLblGakusekiCd() {
		if (lblGakusekiCd == null) {
			lblGakusekiCd = (HtmlOutputText) findComponentInRoot("lblGakusekiCd");
		}
		return lblGakusekiCd;
	}
	/**
	 * @return htmlGakusekiCd を戻します。
	 */
	protected HtmlInputText getHtmlGakusekiCd() {
		if (htmlGakusekiCd == null) {
			htmlGakusekiCd = (HtmlInputText) findComponentInRoot("htmlGakusekiCd");
		}
		return htmlGakusekiCd;
	}
	/**
	 * @return lblGakseiName を戻します。
	 */
	protected HtmlOutputText getLblGakseiName() {
		if (lblGakseiName == null) {
			lblGakseiName = (HtmlOutputText) findComponentInRoot("lblGakseiName");
		}
		return lblGakseiName;
	}
	/**
	 * @return htmlGakseiName を戻します。
	 */
	protected HtmlOutputText getHtmlGakseiName() {
		if (htmlGakseiName == null) {
			htmlGakseiName = (HtmlOutputText) findComponentInRoot("htmlGakseiName");
		}
		return htmlGakseiName;
	}
	/**
	 * @return pdfout を戻します。
	 */
	protected HtmlCommandExButton getPdfou() {
		if (pdfou == null) {
			pdfou = (HtmlCommandExButton) findComponentInRoot("pdfou");
		}
		return pdfou;
	}
	/**
	 * @return htmlFuncId を戻します。
	 */
	protected HtmlOutputText getHtmlFuncId() {
		if (htmlFuncId == null) {
			htmlFuncId = (HtmlOutputText) findComponentInRoot("htmlFuncId");
		}
		return htmlFuncId;
	}
	/**
	 * @return htmlLoginId を戻します。
	 */
	protected HtmlOutputText getHtmlLoginId() {
		if (htmlLoginId == null) {
			htmlLoginId = (HtmlOutputText) findComponentInRoot("htmlLoginId");
		}
		return htmlLoginId;
	}
	/**
	 * @return htmlScrnName を戻します。
	 */
	protected HtmlOutputText getHtmlScrnName() {
		if (htmlScrnName == null) {
			htmlScrnName = (HtmlOutputText) findComponentInRoot("htmlScrnName");
		}
		return htmlScrnName;
	}
	/**
	 * @return message を戻します。
	 */
	protected HtmlOutputText getMessage() {
		if (message == null) {
			message = (HtmlOutputText) findComponentInRoot("message");
		}
		return message;
	}
	/**
	 * @return htmlPayGakTab を戻します。
	 */
	protected HtmlCommandExButton getHtmlPayGakTab() {
		if (htmlPayGakTab == null) {
			htmlPayGakTab = (HtmlCommandExButton) findComponentInRoot("htmlPayGakTab");
		}
		return htmlPayGakTab;
	}
	/**
	 * @return htmlGakseiTab を戻します。
	 */
	protected HtmlCommandExButton getHtmlGakseiTab() {
		if (htmlGakseiTab == null) {
			htmlGakseiTab = (HtmlCommandExButton) findComponentInRoot("htmlGakseiTab");
		}
		return htmlGakseiTab;
	}
	/**
	 * @return csvout を戻します。
	 */
	protected HtmlCommandExButton getCsvou() {
		if (csvou == null) {
			csvou = (HtmlCommandExButton) findComponentInRoot("csvou");
		}
		return csvou;
	}
	/**
	 * @return setoutput を戻します。
	 */
	protected HtmlCommandExButton getSetoutpu() {
		if (setoutpu == null) {
			setoutpu = (HtmlCommandExButton) findComponentInRoot("setoutpu");
		}
		return setoutpu;
	}
	protected HtmlInputHidden getHtmlActiveControlSearch() {
		if (htmlActiveControlSearch == null) {
			htmlActiveControlSearch = (HtmlInputHidden) findComponentInRoot("htmlActiveControlSearch");
		}
		return htmlActiveControlSearch;
	}
	protected HtmlInputHidden getHtmlExecutableSearch() {
		if (htmlExecutableSearch == null) {
			htmlExecutableSearch = (HtmlInputHidden) findComponentInRoot("htmlExecutableSearch");
		}
		return htmlExecutableSearch;
	}
	protected HtmlCommandExButton getPrin() {
		if (prin == null) {
			prin = (HtmlCommandExButton) findComponentInRoot("prin");
		}
		return prin;
	}
	protected HtmlInputHidden getHtmlHidScroll() {
		if (htmlHidScroll == null) {
			htmlHidScroll = (HtmlInputHidden) findComponentInRoot("htmlHidScroll");
		}
		return htmlHidScroll;
	}
	public String doPrintAction() {
		return action.action(this, ActionConst.ACTION_PRINT);
	}
	protected HtmlOutputText getLblNendo() {
		if (lblNendo == null) {
			lblNendo = (HtmlOutputText) findComponentInRoot("lblNendo");
		}
		return lblNendo;
	}
	protected HtmlOutputText getLblPayCd1() {
		if (lblPayCd1 == null) {
			lblPayCd1 = (HtmlOutputText) findComponentInRoot("lblPayCd1");
		}
		return lblPayCd1;
	}
	protected HtmlOutputText getLblPayCd2() {
		if (lblPayCd2 == null) {
			lblPayCd2 = (HtmlOutputText) findComponentInRoot("lblPayCd2");
		}
		return lblPayCd2;
	}
	protected HtmlOutputText getLblPatternCd1() {
		if (lblPatternCd1 == null) {
			lblPatternCd1 = (HtmlOutputText) findComponentInRoot("lblPatternCd1");
		}
		return lblPatternCd1;
	}
	protected HtmlOutputText getLblPatternCd2() {
		if (lblPatternCd2 == null) {
			lblPatternCd2 = (HtmlOutputText) findComponentInRoot("lblPatternCd2");
		}
		return lblPatternCd2;
	}
	protected HtmlOutputText getLblBunnoKbnCd1() {
		if (lblBunnoKbnCd1 == null) {
			lblBunnoKbnCd1 = (HtmlOutputText) findComponentInRoot("lblBunnoKbnCd1");
		}
		return lblBunnoKbnCd1;
	}
	protected HtmlOutputText getLblBunnoKbnCd2() {
		if (lblBunnoKbnCd2 == null) {
			lblBunnoKbnCd2 = (HtmlOutputText) findComponentInRoot("lblBunnoKbnCd2");
		}
		return lblBunnoKbnCd2;
	}
	protected HtmlOutputText getLblBunkatsuNo1() {
		if (lblBunkatsuNo1 == null) {
			lblBunkatsuNo1 = (HtmlOutputText) findComponentInRoot("lblBunkatsuNo1");
		}
		return lblBunkatsuNo1;
	}
	protected HtmlOutputText getLblBunkatsuNo2() {
		if (lblBunkatsuNo2 == null) {
			lblBunkatsuNo2 = (HtmlOutputText) findComponentInRoot("lblBunkatsuNo2");
		}
		return lblBunkatsuNo2;
	}
	protected HtmlScriptCollector getScriptCollector1() {
		if (scriptCollector1 == null) {
			scriptCollector1 = (HtmlScriptCollector) findComponentInRoot("scriptCollector1");
		}
		return scriptCollector1;
	}
	protected HtmlOutputText getLblHakkouDate() {
		if (lblHakkouDate == null) {
			lblHakkouDate = (HtmlOutputText) findComponentInRoot("lblHakkouDate");
		}
		return lblHakkouDate;
	}
	protected HtmlInputText getHtmlHakkouDate() {
		if (htmlHakkouDate == null) {
			htmlHakkouDate = (HtmlInputText) findComponentInRoot("htmlHakkouDate");
		}
		return htmlHakkouDate;
	}
	protected HtmlOutputText getLblTsushinText() {
		if (lblTsushinText == null) {
			lblTsushinText = (HtmlOutputText) findComponentInRoot("lblTsushinText");
		}
		return lblTsushinText;
	}
	protected HtmlInputTextarea getHtmlTsushinText() {
		if (htmlTsushinText == null) {
			htmlTsushinText = (HtmlInputTextarea) findComponentInRoot("htmlTsushinText");
		}
		return htmlTsushinText;
	}
	protected HtmlOutputText getLblOutPutTxt() {
		if (lblOutPutTxt == null) {
			lblOutPutTxt = (HtmlOutputText) findComponentInRoot("lblOutPutTxt");
		}
		return lblOutPutTxt;
	}
	protected HtmlSelectOneRadio getRadio1() {
		if (radio1 == null) {
			radio1 = (HtmlSelectOneRadio) findComponentInRoot("radio1");
		}
		return radio1;
	}
	protected HtmlOutputText getLblOutputDate() {
		if (lblOutputDate == null) {
			lblOutputDate = (HtmlOutputText) findComponentInRoot("lblOutputDate");
		}
		return lblOutputDate;
	}
	protected HtmlOutputText getHtmlOutputDate() {
		if (htmlOutputDate == null) {
			htmlOutputDate = (HtmlOutputText) findComponentInRoot("htmlOutputDate");
		}
		return htmlOutputDate;
	}
	protected HtmlCommandExButton getPdfout() {
		if (pdfout == null) {
			pdfout = (HtmlCommandExButton) findComponentInRoot("pdfout");
		}
		return pdfout;
	}
	protected HtmlSelectBooleanCheckbox getHtmlTusinKbn() {
		if (htmlTusinKbn == null) {
			htmlTusinKbn = (HtmlSelectBooleanCheckbox) findComponentInRoot("htmlTusinKbn");
		}
		return htmlTusinKbn;
	}
	protected HtmlOutputText getHtmlLinkKikanUseChkBox() {
		if (htmlLinkKikanUseChkBox == null) {
			htmlLinkKikanUseChkBox = (HtmlOutputText) findComponentInRoot("htmlLinkKikanUseChkBox");
		}
		return htmlLinkKikanUseChkBox;
	}
	protected HtmlCommandLink getLinkKikanUseChkBox() {
		if (linkKikanUseChkBox == null) {
			linkKikanUseChkBox = (HtmlCommandLink) findComponentInRoot("linkKikanUseChkBox");
		}
		return linkKikanUseChkBox;
	}
	protected HtmlCommandExButton getUnselect() {
		if (unselect == null) {
			unselect = (HtmlCommandExButton) findComponentInRoot("unselect");
		}
		return unselect;
	}
	protected HtmlOutputText getHtmlpropHurikomininCd() {
		if (htmlpropHurikomininCd == null) {
			htmlpropHurikomininCd = (HtmlOutputText) findComponentInRoot("htmlpropHurikomininCd");
		}
		return htmlpropHurikomininCd;
	}
	protected HtmlCommandExButton getPopGakSearch() {
		if (popGakSearch == null) {
			popGakSearch = (HtmlCommandExButton) findComponentInRoot("popGakSearch");
		}
		return popGakSearch;
	}
}
