<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_NSH_KSG_JKN" name="過年度志願者受験台帳" prod_id="NS" description="過去の志願者の受験情報を蓄積して持ちます。過年度志願者受験台帳は『入試期末処理』にて志願者受験台帳から転記されます。">
<STATMENT><![CDATA[
NSH_KSG_JKN
]]></STATMENT>
<COLUMN id="NYUSHI_NENDO" name="入試年度" type="number" length="4" lengthDP="0" byteLength="0" description="入試年度です。"/><COLUMN id="NYUSHI_GAKKI_NO" name="入試学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="入試が実施される学期の番号です。"/><COLUMN id="JUKEN_CD" name="受験番号" type="string" length="10" lengthDP="0" byteLength="10" description="受験番号です。"/><COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生、志願者等を一意に識別する為の番号です。"/><COLUMN id="SGN_CD" name="志願者番号" type="string" length="10" lengthDP="0" byteLength="10" description="志願者を一意に識別するための番号です。"/><COLUMN id="NYS_SBT_CD" name="入試種別コード" type="string" length="6" lengthDP="0" byteLength="6" description="学生の入試形態を表します。入学年度と合わせて「入試種別」の参照キーとなります。"/><COLUMN id="SIKEN_PLACE_CD" name="試験場コード" type="string" length="2" lengthDP="0" byteLength="2" description="試験場コードです。"/><COLUMN id="SIKEN_PLACE_NAME" name="試験場名称" type="string" length="20" lengthDP="0" byteLength="60" description="試験場名称です。"/><COLUMN id="CTR_JKEN_CD" name="センター試験受験番号" type="string" length="5" lengthDP="0" byteLength="5" description="志願者のセンター試験での受験番号です。センター試験必須の入試種別の場合、必ず設定して下さい。"/><COLUMN id="CTR_PLACE" name="センター試験試験場" type="string" length="6" lengthDP="0" byteLength="6" description="志願者のセンター試験での試験場コードです。センター試験必須の入試種別の場合、必ず設定して下さい。"/><COLUMN id="CTR_HAK_SU" name="センター試験発行回数" type="number" length="1" lengthDP="0" byteLength="0" description="志願者のセンター試験の成績請求票発行回数です。センター試験必須の入試種別の場合、必ず設定して下さい。"/><COLUMN id="SGN_DATE" name="志願者登録日付" type="date" length="0" lengthDP="0" byteLength="0" description="志願者登録処理を行った日時が設定されます。"/><COLUMN id="KESEKI_FLG" name="欠席フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="志願者の入学試験への出欠情報です。『欠席者登録』にて設定されます。||"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SEI_KO_FLG" name="成績公開フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="出身校への通知書等で成績を公開するか否かを示します。"><CODE><CASE value="0" display="公開しない"/><CASE value="1" display="公開する"/></CODE></COLUMN><COLUMN id="GOHI_SBT_CD" name="最終合否種別コード" type="string" length="1" lengthDP="0" byteLength="1" description="合否種別を識別する任意のコードです。|段階選抜実施の場合、最終結果が入ります。"/><COLUMN id="GOHI_DANKAI" name="最終合否段階" type="number" length="2" lengthDP="0" byteLength="0" description="志願者が合格し、入学する学年が入ります。"/><COLUMN id="GOHI_GAKKA_CD" name="合否学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="志願者の最終合否が決定した所属学科組織コードが設定されます。"/><COLUMN id="TUI_GOHI_FLG" name="追加合否フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="追加合否決定者か否かを示します。『合否一括登録』、『合否随時登録』にて追加合否登録を指定した場合にＯＮ（１）が設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="GOHI_DATE" name="最終合否登録日付" type="date" length="0" lengthDP="0" byteLength="0" description="志願者の合否が決定した日付です。『合否通知書』の通知日として使用されます。|段階選抜実施の場合、最終結果が入ります。"/><COLUMN id="GOKAK_GNEN" name="合格学年" type="number" length="1" lengthDP="0" byteLength="0" description="志願者が合格し、入学する学年が入ります。"/><COLUMN id="GOKAK_SEMESTER" name="合格セメスタ" type="number" length="2" lengthDP="0" byteLength="0" description="志願者が合格し、入学するセメスタが設定されます。"/><COLUMN id="NYUGAK_NENDO_CUR" name="みなし入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="学生のカリキュラムの基準となる年度が設定されます。通常の学生は入学年度と同じになりますが、編入生等で実際の入学年度とカリキュラムの年度が異なる場合に、みなし入学年度にカリキュラムの基準となる年度が設定されます。|指定が無い場合は、入試種別のみなし入学年度を設定します。"/><COLUMN id="NYUGAK_GAKKI_NO_CUR" name="みなし入学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="学生のカリキュラムの基準となる学期が設定されます。通常の学生は入学学期と同じになりますが、編入生等で実際の入学学期とカリキュラムの学期が異なる場合に、みなし入学期にカリキュラムの基準となる学期が設定されます。|指定が無い場合は、入試種別のみなし入学期ＮＯを設定します。"/><COLUMN id="NYUGAK_SBT_CD" name="入学種別コード" type="string" length="2" lengthDP="0" byteLength="2" description="入学種別を表す任意の区分です。"/><COLUMN id="SYUGAK_SBT_CD" name="就学種別コード" type="string" length="2" lengthDP="0" byteLength="2" description="就学種別を表す任意の区分です。"/><COLUMN id="JITAI_DATE" name="辞退日付" type="date" length="0" lengthDP="0" byteLength="0" description="合格者が入学を辞退した日付です。"/><COLUMN id="JITAI_CD" name="入学辞退理由コード" type="string" length="2" lengthDP="0" byteLength="2" description="入学辞退理由を表すコードです。"/><COLUMN id="JITAI_RIYU" name="入学辞退理由" type="string" length="30" lengthDP="0" byteLength="90" description="入学辞退理由の内容です。"/><COLUMN id="KEKAKU_RIYU_NO" name="欠格理由ＮＯ" type="number" length="1" lengthDP="0" byteLength="0" description="欠格理由区分をあらわすコードです。"/><COLUMN id="KEKAKU_RIYU" name="欠格理由" type="string" length="30" lengthDP="0" byteLength="90" description="欠格の理由です。"/><COLUMN id="NGAK_KAKT_FLG" name="入学確定フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="入学手続が完了し、入学者として新入生台帳に登録されたか否かを示します。『入学者確定』にて指定された手続項目が全て完了している合格者に対してON(1)が設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="GOHI_OUT_FLG" name="合否通知書出力済みフラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="合否通知書を出力したか否かを示します。『合否通知書』にて合否通知書を出力した場合にON(1)が設定されます。但し、合否通知書出力後、『合否随時登録』等で合否を変更した場合、OFF(0)にクリアされます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="NGAK_OUT_FLG" name="入学許可書出力済みフラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="入学許可書を出力したか否かを示します。『入学許可書』にて入学許可書を出力した場合にＯＮ（１）が設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="MIN_SCORE_FLG" name="必要最低点フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="必要最低点か否かの情報を持ちます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN>
</TABLE>
