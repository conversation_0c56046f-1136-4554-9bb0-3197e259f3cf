<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghb00602.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Ghb00602.jsp</TITLE>

    <script language="JavaScript">
    
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('exec');
	}
			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlConfKbn').value = 0;
	}
	
	</script>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghb00602.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">
        
			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />		

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Ghb00602.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghb00602.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghb00602.screenName}"></h:outputText>
			</div>			

			<!--↓OUTER↓-->
			<DIV class="outer">
				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>
				
				<DIV class="head_button_area" >
					<hx:commandExButton type="submit" 
						value="戻る" 
						styleClass="commandExButton_etc"
						id="returnDisp" 
						action="#{pc_Ghb00602.doReturnDispAction1}">
					</hx:commandExButton>
				</DIV>
							
				<!--↓CONTENT↓-->
				<DIV id="content">
					<DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD align="center" valign="top">
				                        <TABLE border="0" cellpadding="0" cellspacing="0" width="400px">
				                            <TBODY>
				                                <TR align="center" valign="top">
				                                    <TD height="140px">
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
															<TBODY>
																<TR>
																	<TH align="center" width="150px" nowrap class="v_a">
																		<h:outputText styleClass="outputText" id="lblMotoNend"
																			value="#{pc_Ghb00602.propMotoNendo.labelName}"
																			style="#{pc_Ghb00602.propMotoNendo.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD width="*">
																		<h:inputText styleClass="inputText" id="htmlMotoNendo"
																			size="4" 
																			value="#{pc_Ghb00602.propMotoNendo.dateValue}"
																			style="#{pc_Ghb00602.propMotoNendo.style}"
																			maxlength="#{pc_Ghb00602.propMotoNendo.maxLength}">		
																			<hx:inputHelperAssist imeMode="inactive"
																				errorClass="inputText_Error" promptCharacter="_" />
																			<f:convertDateTime pattern="yyyy" />
																		</h:inputText>
																	</TD>
																</TR>
																<TR>
																	<TH align="center" width="150px" nowrap class="v_b">
																		<h:outputText styleClass="outputText"
																			id="lblSakiNendo"
																			value="#{pc_Ghb00602.propSakiNendo.labelName}"
																			style="#{pc_Ghb00602.propSakiNendo.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD width="*">
																		<h:inputText styleClass="inputText" id="htmlSakiNendo"
																			value="#{pc_Ghb00602.propSakiNendo.dateValue}"
																			style="#{pc_Ghb00602.propSakiNendo.style}" size="4"
																			maxlength="#{pc_Ghb00602.propSakiNendo.maxLength}">
																			<hx:inputHelperAssist imeMode="inactive"
																				errorClass="inputText_Error" promptCharacter="_" />
																			<f:convertDateTime pattern="yyyy" />
																		</h:inputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
 													</TD>
												</TR>
												<TR>
													<TD>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD align="center">
																		<hx:commandExButton type="submit" value="確定"
																			styleClass="commandExButton_dat" id="exec"
																			action="#{pc_Ghb00602.doExecAction}">
																		</hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
				                                </TR>
				                            </TBODY>
				                        </TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
                    </DIV>
                </DIV>
 				<!--↑CONTENT↑-->
			
			</DIV>
			<!--↑outer↑-->
			
			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Ghb00602.propConfKbn.integerValue}"
				id="htmlConfKbn">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
</BODY>

<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
