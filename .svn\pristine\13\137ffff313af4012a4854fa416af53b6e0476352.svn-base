<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea03001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.framework.property.Checkable"%>
<%@ page import="com.jast.gakuen.rev.ke.constant.code.TaishoKingaku"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea03001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="../ke/inc/gakuenKE.css"  >
<style type="text/css">
<!--
.setWidth TD {width: 150px; white-space: nowrap;}
-->
</style>

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
	// 帳票タイトル
    var listSyurui	= document.getElementById('form1:htmlListSyurui').value;

	if (listSyurui == "<%=Checkable.NO_SELECT_VALUE%>") {
		document.getElementById('form1:htmlTitle').value = "";
	}
	if (document.getElementById('form1:htmlTitle' + '_' + listSyurui) != null) {
		document.getElementById('form1:htmlTitle').value = document.getElementById('form1:htmlTitle' + '_' + listSyurui).value;
	} else {
		document.getElementById('form1:htmlTitle').value = "";
	}
	// 対象金額に合わせて査定元を変更 HEX-2030
	if  (listSyurui != "<%=Checkable.NO_SELECT_VALUE%>") {
		if (listSyurui == "<%=TaishoKingaku.BUMONSATEI.getCode()%>") {
			document.getElementById('form1:htmlYsnTCd').value = document.getElementById('form1:htmlSateiMotoYsnTCdHidden').value;
		} else {
			document.getElementById('form1:htmlYsnTCd').value = "";
		}
		//査定元名称を取得する
		func_2(document.getElementById('form1:htmlYsnTCd'), "");
	}
}
function func_2(thisObj, thisEvent) {
	// 予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlYsnTCd").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_4(thisObj, thisEvent) {
	// 目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMokuNameFrom";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMokuCdFrom").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_6(thisObj, thisEvent) {
	// 目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMokuNameTo";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMokuCdTo").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function copy_mokutekiTo(thisObj, thisEvent) {
	// 目的コード(from)を目的コード(to)にコピーする
	if(document.getElementById("form1:htmlMokuCdTo").value == ""){
			document.getElementById("form1:htmlMokuCdTo").value = document.getElementById("form1:htmlMokuCdFrom").value;
			func_6(document.getElementById("form1:htmlMokuCdTo"), "");
	}
}
// クリアボタンクリック時
function func_10(thisObj, thisEvent) {
	//リストボックスの選択行をクリアする
	clearListBox("form1:htmlYsnTList");
}
// フォームのサブミット時
//FWからsubmit時にコールバック
function submitMethod() {
	//予算単位リストボックの内容を保管
	//（※storeListBox関数はgakuenKE.jsに含まれる）
	storeListBox("form1:htmlYsnTList","form1:htmlYsnTCdHidden");
	return true;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_1(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea03001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea03001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea03001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea03001.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- レイアウト対応、全角スペース -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Kea03001.propKaikeiNendo.labelName}"
										style="#{pc_Kea03001.propKaikeiNendo.labelStyle}"></h:outputText></TH>
									<TD width="650"><h:inputText styleClass="inputText"
										id="htmlKaikeiNendo" size="6"
										disabled="#{pc_Kea03001.propKaikeiNendo.disabled}"
										readonly="#{pc_Kea03001.propKaikeiNendo.readonly}"
										style="#{pc_Kea03001.propKaikeiNendo.style}"
										value="#{pc_Kea03001.propKaikeiNendo.dateValue}" tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_b" colspan="2"><h:outputText 
										styleClass="outputText" id="lblShikinShohiKbn" 
										value="#{pc_Kea03001.propShikinShohiKbn.labelName}" 
										style="#{pc_Kea03001.propShikinShohiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox setWidth"
										id="htmlShikinShohiKbn"
										disabled="#{pc_Kea03001.propShikinShohiKbn.disabled}"
										readonly="#{pc_Kea03001.propShikinShohiKbn.readonly}"
										value="#{pc_Kea03001.propShikinShohiKbn.stringValue}"
										tabindex="2" layout="lineDirection">
										<f:selectItems value="#{pc_Kea03001.propShikinShohiKbn.list}" />
									</h:selectManyCheckbox></TD>
									
								</TR>
								<TR>
									<TH width="150" class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblListSyurui"
										value="#{pc_Kea03001.propListSyurui.labelName}"
										style="#{pc_Kea03001.propListSyurui.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlListSyurui" style="width:150px"
										disabled="#{pc_Kea03001.propListSyurui.disabled}"
										readonly="#{pc_Kea03001.propListSyurui.readonly}"
										value="#{pc_Kea03001.propListSyurui.stringValue}" tabindex="3"
										onchange="return func_1(this, event);">
										<f:selectItems value="#{pc_Kea03001.propListSyurui.list}" />
									</h:selectOneMenu></TD>
									
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblSateiSaki"
										value="#{pc_Kea03001.propYsnTCd.labelName}"
										style="#{pc_Kea03001.propYsnTCd.labelStyle}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText
												styleClass="inputText" id="htmlYsnTCd" size="15"
												disabled="#{pc_Kea03001.propYsnTCd.disabled}"
												maxlength="#{pc_Kea03001.propYsnTCd.maxLength}"
												readonly="#{pc_Kea03001.propYsnTCd.readonly}"
												style="#{pc_Kea03001.propYsnTCd.style}"
												value="#{pc_Kea03001.propYsnTCd.stringValue}" tabindex="5"
												onblur="return func_2(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchYsnTCd1"
												tabindex="6" action="#{pc_Kea03001.doSearchYsnTCd1Action}"></hx:commandExButton>
											<h:outputText
												styleClass="outputText" id="htmlYsnTName"
												value="#{pc_Kea03001.propYsnTName.stringValue}"
												style="#{pc_Kea03001.propYsnTName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblYsnTCd"
										value="#{pc_Kea03001.propYsnTList.labelName}"
										style="#{pc_Kea03001.propYsnTList.labelStyle}"></h:outputText></TH><TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border" width="550">
													<h:selectManyListbox
													styleClass="selectManyListbox" id="htmlYsnTList" size="7"
													style="width:99%"
													disabled="#{pc_Kea03001.propYsnTList.disabled}"
													readonly="#{pc_Kea03001.propYsnTList.readonly}"
													value="#{pc_Kea03001.propYsnTList.value}" tabindex="7">
													<f:selectItems value="#{pc_Kea03001.propYsnTList.list}" />
													</h:selectManyListbox>
												</TD>
												<TD class="clear_border" width="100"><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchYsnTCd2" tabindex="8" action="#{pc_Kea03001.doSearchYsnTCd2Action}"></hx:commandExButton><BR>
												<BR>
												<BR>
												<hx:commandExButton type="button"
													styleClass="commandExButton_listclear" id="clearYsnTCd"
													tabindex="9" onclick="return func_10(this, event);"></hx:commandExButton>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2"><h:outputText
										styleClass="outputText" id="lblMoku"
										value="#{pc_Kea03001.propMokuCd.labelName}"
										style="#{pc_Kea03001.propMokuCd.labelStyle}"></h:outputText></TH>
									<TD></TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_f" width="130"><h:outputText
										styleClass="outputText" id="lblMokuFrom" value="開始"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText
												styleClass="inputText" id="htmlMokuCdFrom" size="15"
												disabled="#{pc_Kea03001.propMokuCdFrom.disabled}"
												maxlength="#{pc_Kea03001.propMokuCdFrom.maxLength}"
												readonly="#{pc_Kea03001.propMokuCdFrom.readonly}"
												style="#{pc_Kea03001.propMokuCdFrom.style}"
												value="#{pc_Kea03001.propMokuCdFrom.stringValue}"
												tabindex="10" onblur="func_4(this, event); copy_mokutekiTo(this, event);"></h:inputText><hx:commandExButton
												type="submit" 
												styleClass="commandExButton_search" id="searchMokuCdFrom" tabindex="11"
												action="#{pc_Kea03001.doSearchMokuCdFromAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMokuNameFrom"
												style="#{pc_Kea03001.propMokuNameFrom.style}"
												value="#{pc_Kea03001.propMokuNameFrom.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_bottom"></TH>
									<TH class="v_g"><h:outputText styleClass="outputText"
										id="lblMokuTo" value="終了"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText
												styleClass="inputText" id="htmlMokuCdTo" size="15"
												disabled="#{pc_Kea03001.propMokuCdTo.disabled}"
												maxlength="#{pc_Kea03001.propMokuCdTo.maxLength}"
												readonly="#{pc_Kea03001.propMokuCdTo.readonly}"
												style="#{pc_Kea03001.propMokuCdTo.style}"
												value="#{pc_Kea03001.propMokuCdTo.stringValue}"
												tabindex="12" onblur="return func_6(this, event);"></h:inputText><hx:commandExButton
												type="submit" 
												styleClass="commandExButton_search" id="searchMokuCdTo" tabindex="13"
												action="#{pc_Kea03001.doSearchMokuCdToAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMokuNameTo"
												style="#{pc_Kea03001.propMokuNameTo.style}"
												value="#{pc_Kea03001.propMokuNameTo.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH width="150" class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblJiNendoShinsei"
										value="#{pc_Kea03001.propJiNendoShinsei.name}"
										style="#{pc_Kea03001.propJiNendoShinsei.labelStyle}"></h:outputText></TH>
									<TD><h:selectManyCheckbox
										styleClass="selectManyCheckbox setWidth"
										disabledClass="selectManyCheckbox_Disabled"
										id="htmlJiNendoShinsei" tabindex="13"
										value="#{pc_Kea03001.propJiNendoShinsei.value}"
										disabled="#{pc_Kea03001.propJiNendoShinsei.disabled}"
										readonly="#{pc_Kea03001.propJiNendoShinsei.readonly}"
										rendered="#{pc_Kea03001.propJiNendoShinsei.rendered}">
										<f:selectItems value="#{pc_Kea03001.propJiNendoShinsei.list}" />
									</h:selectManyCheckbox></TD>
								</TR>
								<TR>
									<TH class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblSortType"
										value="#{pc_Kea03001.propSortType.labelName}"
										style="#{pc_Kea03001.propSortType.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlSortType"
										tabindex="18"
										disabled="#{pc_Kea03001.propKingakuTani.disabled}"
										readonly="#{pc_Kea03001.propKingakuTani.readonly}"
										value="#{pc_Kea03001.propSortType.stringValue}">
										<f:selectItems value="#{pc_Kea03001.propSortType.list}" />
									</h:selectOneRadio></TD>
									
								</TR>
								<TR>
									<TH class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblKingakuTani"
										value="#{pc_Kea03001.propKingakuTani.labelName}"
										style="#{pc_Kea03001.propKingakuTani.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlKingakuTani"
										tabindex="18"
										disabled="#{pc_Kea03001.propKingakuTani.disabled}"
										readonly="#{pc_Kea03001.propKingakuTani.readonly}"
										value="#{pc_Kea03001.propKingakuTani.stringValue}">
										<f:selectItems value="#{pc_Kea03001.propKingakuTani.list}" />
									</h:selectOneRadio></TD>
									
								</TR>
								<TR>
									<TH class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblTitle"
										value="#{pc_Kea03001.propTitle.labelName}"
										style="#{pc_Kea03001.propTitle.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlTitle"
										size="85" disabled="#{pc_Kea03001.propTitle.disabled}"
										maxlength="#{pc_Kea03001.propTitle.maxLength}"
										readonly="#{pc_Kea03001.propTitle.readonly}"
										style="#{pc_Kea03001.propTitle.style}"
										value="#{pc_Kea03001.propTitle.stringValue}" tabindex="19"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="800" class="button_bar" cellpadding="1" cellspacing="0">
							<TBODY>
								<TR>
									<TD>
										<hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout" tabindex="20"
										action="#{pc_Kea03001.doPdfoutAction}"
										confirm="#{msg.SY_MSG_0019W}"
										rendered="#{pc_Kea03001.propPdfout.rendered}"
										disabled="#{pc_Kea03001.propPdfout.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="EXCEL作成"
										styleClass="commandExButton_out" id="excelout" tabindex="21"
										action="#{pc_Kea03001.doExcelOutAction}"
										confirm="#{msg.SY_MSG_0027W}"
										rendered="#{pc_Kea03001.propExcelout.rendered}"
										disabled="#{pc_Kea03001.propExcelout.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="CSV作成"
										styleClass="commandExButton_out" id="csvout" tabindex="22"
										action="#{pc_Kea03001.doCsvoutAction}"
										confirm="#{msg.SY_MSG_0020W}"
										rendered="#{pc_Kea03001.propCsvout.rendered}"
										disabled="#{pc_Kea03001.propCsvout.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="出力項目指定"
										styleClass="commandExButton_out" id="setoutput" tabindex="23"
										action="#{pc_Kea03001.doSetoutputAction}"
										rendered="#{pc_Kea03001.propSetoutput.rendered}"
										disabled="#{pc_Kea03001.propSetoutput.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="印刷"
										styleClass="commandExButton_out" id="print" tabindex="24"
										action="#{pc_Kea03001.doPrintAction}"
										confirm="#{msg.SY_MSG_0022W}"
										rendered="#{pc_Kea03001.propPrint.rendered}"
										disabled="#{pc_Kea03001.propPrint.disabled}"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<c:forEach items="${pc_Kea03001.titleMap}" var="form_title">
					 <input type=hidden id="<c:out value="${form_title.key}"/>"
					  name="<c:out value="${form_title.key}"/>"
					  value="<c:out value="${form_title.value}"/>">
			</c:forEach>
</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kea03001.propYsnTCdHidden.stringValue}"
				id="htmlYsnTCdHidden"></h:inputHidden>
			<h:inputHidden value="#{pc_Kea03001.propSateiMotoYsnTCdHidden.stringValue}"
				id="htmlSateiMotoYsnTCdHidden"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

