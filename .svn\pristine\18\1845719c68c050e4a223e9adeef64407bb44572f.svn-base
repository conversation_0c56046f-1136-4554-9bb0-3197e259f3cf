<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nse00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nse00701.jsp</TITLE>
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<SCRIPT type="text/javascript">
</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nse00701.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nse00701.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nse00701.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nse00701.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4"><TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="600">
							<TBODY>
								<TR>
									<TH class="v_a" width="145"><h:outputText
										styleClass="outputText" id="lblNyushiNendo"
										value="#{pc_Nse00701.propNyushiNendo.labelName}"
										style="#{pc_Nse00701.propNyushiNendo.labelStyle}"></h:outputText></TH>
									<TD width="155"><h:inputText styleClass="inputText"
										id="htmlNyushiNendo"
										value="#{pc_Nse00701.propNyushiNendo.dateValue}" size="4"
										style="#{pc_Nse00701.propNyushiNendo.style}">
										<hx:inputHelperAssist imeMode="inactive"
											errorClass="inputText_Error" promptCharacter="_"/>
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH width="145" class="v_b"><h:outputText
										styleClass="outputText"
										value="#{pc_Nse00701.propNyushiGakkiNo.labelName}"
										id="lblNyushiGakkiNo" style="#{pc_Nse00701.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
									<TD width="155"><h:inputText styleClass="inputText"
										id="htmlNyushiGakkiNo" maxlength="2"
										value="#{pc_Nse00701.propNyushiGakkiNo.integerValue}" size="2"
										style="#{pc_Nse00701.propNyushiGakkiNo.style}">
										<f:convertNumber pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="600" >
							<TBODY>
								<TR>
									<TH class="v_c" colspan="2" width="140"><h:outputText
										styleClass="outputText" id="lblBunmenKbn" value="帳票区分"
										style="#{pc_Nse00701.propBunmenKbn.labelStyle}"></h:outputText></TH>
									<TD width="455"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlBunmenKbn" style="width:200px"
										value="#{pc_Nse00701.propBunmenKbn.value}">
										<f:selectItems value="#{pc_Nse00701.propBunmenKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2" width="140"><h:outputText
										styleClass="outputText" id="lblOutKbn" value="出力指定"></h:outputText></TH>
									<TD width="455"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectManyCheckbox" id="htmlOutKbn"
										layout="pageDirection" value="#{pc_Nse00701.propOutKbn.value}">
										<f:selectItem itemValue="0" itemLabel="未作成の発行番号について作成する" />
										<f:selectItem itemValue="1" itemLabel="発行番号を指定して作成する" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label_bottom" width="20"></TH>
									<TH class="v_d" width="120"><h:outputText
										styleClass="outputText" id="lblHakkoNo"
										value="#{pc_Nse00701.propHakkoNoLbl.labelName}"></h:outputText></TH>
									<TD width="455">
									<TABLE width="250" border="0" cellpadding="0" cellspacing="0">
										<TR>
											<TD width="80" class="clear_border"><h:inputText
												styleClass="inputText" id="htmlHakkoNoFrom"
												value="#{pc_Nse00701.propHakkoNoFrom.integerValue}" maxlength="6"
												size="6" style="#{pc_Nse00701.propHakkoNoFrom.style}">
											<f:convertNumber pattern="#####0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText></TD>
											<TD class="clear_border">　<h:outputText
												styleClass="outputText" id="lblA" value="～"></h:outputText></TD>
											<TD class="clear_border"> <h:inputText
												styleClass="inputText" id="htmlHakkoNoTo"
												value="#{pc_Nse00701.propHakkoNoTo.integerValue}" maxlength="6"
												size="6" style="#{pc_Nse00701.propHakkoNoTo.style}">
											<f:convertNumber pattern="#####0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText></TD>
										</TR>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="600" border="0" cellpadding="0" cellspacing="0"
							style="" class="table">
							<TBODY>
								<TR>
									<TH class="v_e" width="150"><h:outputText
										styleClass="outputText" id="lblTitle"
										value="#{pc_Nse00701.propTitle.labelName}" style="#{pc_Nse00701.propTitle.labelStyle}"></h:outputText></TH>
									<TD width="455"><h:inputText styleClass="inputText"
										id="htmlTitle" maxlength="#{pc_Nse00701.propTitle.maxLength}"
										size="60" value="#{pc_Nse00701.propTitle.stringValue}"
										style="#{pc_Nse00701.propTitle.style}">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="600" class="button_bar" >
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout"
										action="#{pc_Nse00701.doPdfOutAction}"
										confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
                                        <hx:commandExButton
										type="submit" value="CSV作成" styleClass="commandExButton_out"
										id="csvout" action="#{pc_Nse00701.doCsvOutAction}"
										confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
                                        <hx:commandExButton
										type="submit" value="出力項目指定" styleClass="commandExButton_out"
										id="setoutput" action="#{pc_Nse00701.doSetOutPutAction}"></hx:commandExButton>
                                    </TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

