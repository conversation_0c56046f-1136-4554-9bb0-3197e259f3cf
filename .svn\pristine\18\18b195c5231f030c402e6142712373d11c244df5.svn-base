<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmb02401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Kmb02401.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--    title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >  
<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:propExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:propExecutableSearch').value = "0";
}

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します


//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します



}</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmb02401.onPageLoadBegin}">
    <h:form styleClass="form" id="form1">
        <!-- ヘッダーインクルード -->
        <jsp:include page ="../inc/header.jsp" />
        <!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmb02401.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmb02401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmb02401.screenName}"></h:outputText></div>
    <!--↓outer↓-->
    <DIV class="outer">
    
    <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
        styleClass="outputText" escape="false">
    </h:outputText>
    </FIELDSET>
    <br>

    <!--↓content↓-->
    <DIV id="content">
        <DIV class="column" align="center">
            <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="816">
                <TBODY>
                    <TR>
                        <TH class="v_a" width="150"><h:outputText styleClass="outputText"
                            id="lblNyugakuNendo"
                            value="#{pc_Kmb02401.propNyugakuNendo.labelName}"
                            style="#{pc_Kmb02401.propNyugakuNendo.labelStyle}">
                        </h:outputText></TH>
                        <TD width="175"><h:inputText
                            id="htmlNyugakuNendo" styleClass="inputText"
                            readonly="#{pc_Kmb02401.propNyugakuNendo.readonly}"
                            style="#{pc_Kmb02401.propNyugakuNendo.style}"
                            value="#{pc_Kmb02401.propNyugakuNendo.dateValue}"
                            disabled="#{pc_Kmb02401.propNyugakuNendo.disabled}" size="4"
                            tabindex="1"
                            maxlength="#{pc_Kmb02401.propNyugakuGakkiNo.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
                        </h:inputText></TD>
                        <TH class="v_a" width="150"><h:outputText styleClass="outputText"
                            id="lblNyugakuGakkiNo"
                            value="#{pc_Kmb02401.propNyugakuGakkiNo.labelName}"
                            style="#{pc_Kmb02401.propNyugakuGakkiNo.labelStyle}"></h:outputText></TH>
                        <TD width="241"><h:inputText id="htmlNyugakuGakkiNo"
                            styleClass="inputText"
                            readonly="#{pc_Kmb02401.propNyugakuGakkiNo.readonly}"
                            style="#{pc_Kmb02401.propNyugakuGakkiNo.style}"
                            value="#{pc_Kmb02401.propNyugakuGakkiNo.integerValue}"
                            maxlength="#{pc_Kmb02401.propNyugakuGakkiNo.maxLength}"
                            disabled="#{pc_Kmb02401.propNyugakuGakkiNo.disabled}" size="2" tabindex="2">
                            <f:convertNumber type="number" pattern="#0"/>
                            <hx:inputHelperAssist errorClass="inputText_Error"
                                promptCharacter="_" />
                        </h:inputText></TD>
						<TD width="100"
							style="background-color: transparent; text-align: right"
							class="clear_border"><hx:commandExButton type="submit" value="選択"
							styleClass="commandExButton" id="selectNendoGakki" tabindex="3"
							disabled="#{pc_Kmb02401.propSelectNendoGakki.disabled}"
							action="#{pc_Kmb02401.doSelectNendoGakkiAction}">
						</hx:commandExButton><hx:commandExButton type="submit" value="解除"
							styleClass="commandExButton" id="cancelNendoGakki" tabindex="4"
							disabled="#{pc_Kmb02401.propCancelNendoGakki.disabled}"
							action="#{pc_Kmb02401.doCancelNendoGakkiAction}">
						</hx:commandExButton></TD>
					</TR>
                </TBODY>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="816">
                <TBODY>
                    <TR>
                        <TH class="v_b" width="150"><h:outputText styleClass="outputText"
                            id="lblCurGakka" value="#{pc_Kmb02401.propCurGakka.labelName}"
                            style="#{pc_Kmb02401.propCurGakka.labelStyle}"></h:outputText></TH>
                        <TD colspan=3 width="566"><h:selectOneMenu
							styleClass="selectOneMenu" id="htmlCurGakka"
							value="#{pc_Kmb02401.propCurGakka.value}"
							disabled="#{pc_Kmb02401.propCurGakka.disabled}" tabindex="5"
							style="#{pc_Kmb02401.propCurGakka.style};width:550px">
							<f:selectItems value="#{pc_Kmb02401.propCurGakka.list}" />
						</h:selectOneMenu>
                        </TD>
						<TD width="100"
							style="background-color: transparent; text-align: right"
							class="clear_border"><hx:commandExButton type="submit" value="検索"
							disabled="#{pc_Kmb02401.propSearch.disabled}"
							styleClass="commandExButton" id="search" tabindex="6"
							action="#{pc_Kmb02401.doSearchAction}"></hx:commandExButton> <hx:commandExButton
							type="submit" value="解除"
							disabled="#{pc_Kmb02401.propCancel.disabled}"
							styleClass="commandExButton" id="cancel" tabindex="7"
							action="#{pc_Kmb02401.doCancelAction}"></hx:commandExButton></TD>
					</TR>
                </TBODY>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE border="0" class="button_bar" width="800">           
                <TBODY>
                    <TR>
                        <TD><hx:commandExButton type="submit"
                            value="新規登録" disabled="#{pc_Kmb02401.propDspEntrypage.disabled}" styleClass="commandExButton_dat" id="dspEntrypage"
                            tabindex="8" action="#{pc_Kmb02401.doDspEntrypageAction}"></hx:commandExButton>
                            <hx:commandExButton type="submit"
                            value="卒業見込条件コピーへ" disabled="#{pc_Kmb02401.propDspSotugyomikomiJokenCopypage.disabled}" styleClass="commandExButton_large4"
                            id="dspSotugyomikomiJokenCopypage" tabindex="9"
                            style="width: 146px" action="#{pc_Kmb02401.doDspSotugyomikomiJokenCopypageAction}"></hx:commandExButton>
                            <hx:commandExButton
                            type="submit" value="ＯＲ条件設定へ" disabled="#{pc_Kmb02401.propDspOrJokenpage.disabled}" styleClass="commandExButton_large1"
                            id="dspOrJokenpage" tabindex="10" style="width: 100px" action="#{pc_Kmb02401.doDspOrJokenpageAction}"></hx:commandExButton></TD>
                    </TR>
                </TBODY>
            </TABLE>
            <TABLE border="0" cellpadding="0" cellspacing="0" width="800">
                <TBODY>
                    <TR>
                        <TD align="right"><h:outputText styleClass="outputText"
                            id="htmlJokenListTotal"
                            value="#{pc_Kmb02401.propJokenListTotal.stringValue}">
                        </h:outputText>
                        </TD>
                    </TR>
                    <TR>
                        <TD width="900">
                        <DIV style="height:300px" class="listScroll"><h:dataTable
                            columnClasses="columnClass1" headerClass="headerClass"
                            footerClass="footerClass" rowClasses="#{pc_Kmb02401.propJokenList.rowClasses}"
                            styleClass="meisai_scroll" id="htmlJokenList"
                            value="#{pc_Kmb02401.propJokenList.list}" var="varlist"
                            width="800">
                            <h:column id="column1">
                                <f:facet name="header">
                                    <h:outputText styleClass="outputText" value="ＯＲ条件"
                                        id="lblOrJokenColumn"></h:outputText>
                                </f:facet>
                                <h:outputText styleClass="outputText" id="htmlOrJokenColumn"
                                    value="#{varlist.orJoken}"></h:outputText>
                                <f:attribute value="80" name="width" />
                            </h:column>
                            <h:column id="column2">
                                <f:facet name="header">
                                    <h:outputText styleClass="outputText" value="条件コード"
                                        id="lblJokenCodeColumn"></h:outputText>
                                </f:facet>
                                <h:outputText styleClass="outputText" id="htmlJokenCodeColumn"
                                    value="#{varlist.jokenCode}"></h:outputText>
                                <f:attribute value="80" name="width" />
                            </h:column>
                            <h:column id="column3">
                                <f:facet name="header">
                                    <h:outputText styleClass="outputText" value="タイトル"
                                        id="lblTitleColumn"></h:outputText>
                                </f:facet>
                                <h:outputText styleClass="outputText" id="htmlTitleColumn"
                                    value="#{varlist.title}"></h:outputText>
								<f:attribute value="550" name="width" />
							</h:column>
                            <h:column id="column4">
                                <f:facet name="header">
                                </f:facet>
                                <hx:commandExButton type="submit" value="編集"
                                    styleClass="commandExButton" id="editJoken"
                                    action="#{pc_Kmb02401.doEditJokenAction}" tabindex="11"></hx:commandExButton>
                                <hx:commandExButton type="submit" value="コピー"
                                    styleClass="commandExButton" id="copyJoken"
                                    action="#{pc_Kmb02401.doCopyJokenAction}" tabindex="11"></hx:commandExButton>
								<f:attribute value="90" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
                        </h:dataTable>
                        </DIV>
                        </TD>
                    </TR>
                </TBODY>
            </TABLE>
        </DIV>
    </DIV>
    <!--↑content↑-->
    </DIV>
    <!--↑outer↑-->
			<h:inputHidden
				value="#{pc_Kmb02401.propExecutableSearch.integerValue}"
				id="propExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
    </h:form>
    <!-- フッターインクルード -->
    <jsp:include page ="../inc/footer.jsp" />
    </hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
