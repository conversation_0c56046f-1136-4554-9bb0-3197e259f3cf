<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea30001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.framework.property.Checkable"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea30001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >
<LINK rel="stylesheet" type="text/css" href="../../rev/ke/inc/gakuenKE.css"  >	

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">

// クリアボタンクリック時
function func_1(thisObj, thisEvent) {
	//リストボックスの選択行をクリアする
	clearListBox("form1:htmlYsnTList");
}
// フォームのサブミット時
//FWからsubmit時にコールバック
function submitMethod() {
	//予算単位リストボックの内容を保管
	//（※storeListBox関数はgakuenKE.jsに含まれる）
	storeListBox("form1:htmlYsnTList","form1:htmlYsnTCdHidden");
	return true;
}

function func_2(thisObj, thisEvent) {
	var outKbn;
	target = document.getElementsByName('form1:htmlOutputKbn');
	for (i = 0; i < target.length; i++) {
		if (target[i].checked) {
			outKbn = target[i].value;
		}
	}
	var syurui = document.getElementById('form1:htmlListSyurui').value;

	if (syurui == "<%=Checkable.NO_SELECT_VALUE%>") {
		document.getElementById('form1:htmlTitle').value = "";
	}

	if (document.getElementById('form1:htmlTitle' + '_' + outKbn + '_' + syurui) != null) {
		document.getElementById('form1:htmlTitle').value = document.getElementById('form1:htmlTitle' + '_' + outKbn + '_' + syurui).value;
	}
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_2(this,event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea30001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">
<style type="text/css">
<!--
.setWidth TD {width: 150px; white-space: nowrap;}
-->
</style>
<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea30001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea30001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea30001.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Kea30001.propKaikeiNendo.labelName}"
										style="#{pc_Kea30001.propKaikeiNendo.labelStyle}"></h:outputText></TH><TD width="650"><h:inputText
										styleClass="inputText" id="htmlKaikeiNendo" size="5"
										disabled="#{pc_Kea30001.propKaikeiNendo.disabled}"
										readonly="#{pc_Kea30001.propKaikeiNendo.readonly}"
										style="#{pc_Kea30001.propKaikeiNendo.style}"
										value="#{pc_Kea30001.propKaikeiNendo.dateValue}" tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblThisHoseiKaiji"
										value="#{pc_Kea30001.propThisHoseiKaiji.labelName}"
										style="#{pc_Kea30001.propThisHoseiKaiji.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlThisHoseiKaiji"
										size="3" disabled="#{pc_Kea30001.propThisHoseiKaiji.disabled}"
										readonly="#{pc_Kea30001.propThisHoseiKaiji.readonly}"
										style="#{pc_Kea30001.propThisHoseiKaiji.style}"
										value="#{pc_Kea30001.propThisHoseiKaiji.integerValue}"
										tabindex="2">
										<f:convertNumber pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblLastHoseiKaiji"
										value="#{pc_Kea30001.propLastHoseiKaiji.labelName}"
										style="#{pc_Kea30001.propLastHoseiKaiji.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlLastHoseiKaiji"
										size="3" disabled="#{pc_Kea30001.propLastHoseiKaiji.disabled}"
										readonly="#{pc_Kea30001.propLastHoseiKaiji.readonly}"
										style="#{pc_Kea30001.propLastHoseiKaiji.style}"
										value="#{pc_Kea30001.propLastHoseiKaiji.integerValue}"
										tabindex="2">
										<f:convertNumber pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblListSyurui"
										value="#{pc_Kea30001.propListSyurui.name}"
										style="#{pc_Kea30001.propListSyurui.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlListSyurui" style=""
										disabled="#{pc_Kea30001.propListSyurui.disabled}"
										readonly="#{pc_Kea30001.propListSyurui.readonly}"
										value="#{pc_Kea30001.propListSyurui.value}" tabindex="4" onchange="return func_2(this, event);">
										<f:selectItems value="#{pc_Kea30001.propListSyurui.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblKaikeiTani"
										value="#{pc_Kea30001.propKaikeiTani.name}"
										style="#{pc_Kea30001.propKaikeiTani.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlKaikeiTani" style=""
										disabled="#{pc_Kea30001.propKaikeiTani.disabled}"
										readonly="#{pc_Kea30001.propKaikeiTani.readonly}"
										value="#{pc_Kea30001.propKaikeiTani.value}" tabindex="5">
										<f:selectItems value="#{pc_Kea30001.propKaikeiTani.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblGakSyukeiKbn"
										value="#{pc_Kea30001.propGakSyukeiKbn.name}"
										style="#{pc_Kea30001.propGakSyukeiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakSyukeiKbn" style=""
										disabled="#{pc_Kea30001.propGakSyukeiKbn.disabled}"
										readonly="#{pc_Kea30001.propGakSyukeiKbn.readonly}"
										value="#{pc_Kea30001.propGakSyukeiKbn.value}" tabindex="6">
										<f:selectItems value="#{pc_Kea30001.propGakSyukeiKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_f" colspan="2"><h:outputText
										styleClass="outputText" id="lblYsnT"
										value="#{pc_Kea30001.propYsnT.name}"
										style="#{pc_Kea30001.propYsnT.labelStyle}"></h:outputText></TH>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border" width="550"><h:selectManyListbox
													styleClass="selectManyListbox" id="htmlYsnTList" size="7"
													style="width:99%"
													disabled="#{pc_Kea30001.propYsnT.disabled}"
													readonly="#{pc_Kea30001.propYsnT.readonly}"
													value="#{pc_Kea30001.propYsnT.value}" tabindex="7">
													<f:selectItems value="#{pc_Kea30001.propYsnT.list}" />
												</h:selectManyListbox></TD>
												<TD class="clear_border" width="100" align="left"><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchYosanTani" tabindex="8"
													action="#{pc_Kea30001.doSearchYosanTaniAction}"
													></hx:commandExButton><BR>
												<BR>
												<BR>
												<hx:commandExButton type="button"
													styleClass="commandExButton_listclear" id="clear"
													tabindex="9" onclick="return func_1(this, event);"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_g" colspan="2"><h:outputText
										styleClass="outputText" id="lblTeisyutsuSaki"
										value="#{pc_Kea30001.propTeisyutsuSaki.name}"
										style="#{pc_Kea30001.propTeisyutsuSaki.labelStyle}"></h:outputText></TH>
									<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlTeisyutsuSaki"
										style="width:300px" tabindex="10"
										value="#{pc_Kea30001.propTeisyutsuSaki.stringValue}"
										disabled="#{pc_Kea30001.propTeisyutsuSaki.disabled}"
										readonly="#{pc_Kea30001.propTeisyutsuSaki.readonly}">
										<f:selectItems value="#{pc_Kea30001.propTeisyutsuSaki.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblTaishoYsn"
										value="#{pc_Kea30001.propTaishoYsn.name}"
										style="#{pc_Kea30001.propTaishoYsn.labelStyle}"></h:outputText></TH>
									<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlTaishoYsn"
										style="width:300px" tabindex="10"
										disabled="#{pc_Kea30001.propTaishoYsn.disabled}"
										readonly="#{pc_Kea30001.propTaishoYsn.readonly}"
										value="#{pc_Kea30001.propTaishoYsn.stringValue}">
										<f:selectItems value="#{pc_Kea30001.propTaishoYsn.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblOutputKbn"
										value="#{pc_Kea30001.propOutputKbn.name}"
										style="#{pc_Kea30001.propOutputKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlOutputKbn"
										style="width:300px" tabindex="11"
										disabled="#{pc_Kea30001.propOutputKbn.disabled}"
										readonly="#{pc_Kea30001.propOutputKbn.readonly}"
										value="#{pc_Kea30001.propOutputKbn.stringValue}" onclick="return func_2(this, event);">
										<f:selectItems value="#{pc_Kea30001.propOutputKbn.list}" />
									</h:selectOneRadio></TD>

								</TR>
								<TR>
									<TH class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblLevelMoku"
										value="#{pc_Kea30001.propLevelMoku.name}"
										style="#{pc_Kea30001.propLevelMoku.style}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlLevelMoku" style=""
										value="#{pc_Kea30001.propLevelMoku.value}"
										disabled="#{pc_Kea30001.propLevelMoku.disabled}"
										readonly="#{pc_Kea30001.propLevelMoku.readonly}" tabindex="12">
										<f:selectItems value="#{pc_Kea30001.propLevelMoku.list}" />
									</h:selectOneMenu>
									<h:outputText styleClass="outputText" id="lblLevelMokuChu" value="(出力区分が目的別のとき)"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblLevel"
										value="#{pc_Kea30001.propLevel.name}"
										style="#{pc_Kea30001.propLevel.style}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlLevel"
										style="" disabled="#{pc_Kea30001.propLevel.disabled}"
										readonly="#{pc_Kea30001.propLevel.readonly}"
										value="#{pc_Kea30001.propLevel.value}" tabindex="13">
										<f:selectItems value="#{pc_Kea30001.propLevel.list}" />
									</h:selectOneMenu>
									<h:outputText styleClass="outputText" id="lblLevelChu" value="(出力区分が科目別のとき)"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblChushutuJoken"
										value="#{pc_Kea30001.propChushutuJoken.name}"
										style="#{pc_Kea30001.propChushutuJoken.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlChushutuJoken"
										style="width:300px" disabled="#{pc_Kea30001.propChushutuJoken.disabled}"
										readonly="#{pc_Kea30001.propChushutuJoken.readonly}"
										value="#{pc_Kea30001.propChushutuJoken.stringValue}" tabindex="14">
										<f:selectItems value="#{pc_Kea30001.propChushutuJoken.list}" />
									</h:selectOneRadio></TD>

								</TR>
								<TR>
									<TH class="v_f" colspan="2"><h:outputText
										styleClass="outputText" id="lblMakeKbn"
										value="#{pc_Kea30001.propMakeKbn.name}"
										style="#{pc_Kea30001.propMakeKbn.labelStyle}"></h:outputText></TH>
									<TD>
									<h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox  setWidth" id="htmlMakeKbn"
										value="#{pc_Kea30001.propMakeKbn.checked}"
										readonly="#{pc_Kea30001.propMakeKbn.readonly}"
										disabled="#{pc_Kea30001.propMakeKbn.disabled}" tabindex="15">
									</h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="textMakeKbn"
										value="公的">
									</h:outputText></TD>	
								</TR>
								<TR>
									<TH class="v_g" colspan="2"><h:outputText
										styleClass="outputText" id="lblKingakuTani"
										value="#{pc_Kea30001.propKingakuTani.name}"
										style="#{pc_Kea30001.propKingakuTani.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlKingakuTani"
										style="width:300px" disabled="#{pc_Kea30001.propKingakuTani.disabled}"
										readonly="#{pc_Kea30001.propKingakuTani.readonly}"
										value="#{pc_Kea30001.propKingakuTani.stringValue}" tabindex="16">
										<f:selectItems value="#{pc_Kea30001.propKingakuTani.list}" />
									</h:selectOneRadio></TD>

								</TR>
								<TR>
									<TH class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblTitle"
										value="#{pc_Kea30001.propTitle.labelName}"
										style="#{pc_Kea30001.propTitle.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlTitle"
										size="85" disabled="#{pc_Kea30001.propTitle.disabled}"
										maxlength="#{pc_Kea30001.propTitle.maxLength}"
										readonly="#{pc_Kea30001.propTitle.readonly}"
										style="#{pc_Kea30001.propTitle.style}"
										value="#{pc_Kea30001.propTitle.stringValue}" tabindex="17"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="800" class="button_bar" cellpadding="1"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout" action="#{pc_Kea30001.doPdfoutAction}" confirm="#{msg.SY_MSG_0019W}" tabindex="18"></hx:commandExButton>
										<hx:commandExButton type="submit" value="EXCEL作成" 
										styleClass="commandExButton_out" id="excelout" confirm="#{msg.SY_MSG_0027W}" action="#{pc_Kea30001.doExcelOutAction}" disabled="#{pc_Kea30001.propExcelout.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="CSV作成" styleClass="commandExButton_out"
										id="csvout" action="#{pc_Kea30001.doCsvoutAction}" confirm="#{msg.SY_MSG_0020W}" tabindex="19"></hx:commandExButton>
										<hx:commandExButton type="submit" value="出力項目指定"
										styleClass="commandExButton_out" id="setoutput"
										action="#{pc_Kea30001.doSetoutputAction}" tabindex="20"></hx:commandExButton>
										<hx:commandExButton type="submit" value="印刷" styleClass="commandExButton_out"
										id="print" action="#{pc_Kea30001.doPrintAction}" confirm="#{msg.SY_MSG_0022W}" tabindex="21"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<c:forEach items="${pc_Kea30001.titleMap}" var="form_title">		 
					 <input type=hidden id="<c:out value="${form_title.key}"/>"
					  name="<c:out value="${form_title.key}"/>"
					  value="<c:out value="${form_title.value}"/>">
			</c:forEach>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kea30001.propYsnTCdHidden.stringValue}"
				id="htmlYsnTCdHidden"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

