<%--
  学籍情報照会（基本）

  <AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob00302T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>

<%@page import="com.jast.gakuen.rev.co.Cob00302T01"%>
<%@page import="com.jast.gakuen.framework.util.UtilStr"%>
<%@page import="com.jast.gakuen.framework.util.UtilSystem"%>

<%@ page language="java" contentType="text/html; charset=UTF-8"
  pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob00302T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
  title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
// 画面ロード時の学生名称再取得
function loadAction(event){
//  doCalculateAgeAjax(document.getElementById('form1:htmlBirth'), event, 'form1:lblAge');
}

// 年齢を取得する
function doCalculateAgeAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/co/CalculateAgeAJAX";
  var args = new Array();
  args['birthDay'] = thisObj.value;
  args['sai'] = "1";
  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}
</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cob00302T01.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Cob00302T01">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
  value="閉じる" styleClass="commandExButton" id="closeDisp"
  action="#{pc_Cob00302T01.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId"
  value="#{pc_Cob00302T01.funcId}"></h:outputText> <h:outputText
  styleClass="outputText" id="htmlLoginId"
  value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
  styleClass="outputText" id="htmlScrnName"
  value="#{pc_Cob00302T01.screenName}"></h:outputText></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
  id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText></FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<hx:commandExButton
	type="submit" value="プロファイル"
	styleClass="commandExButton" id="referProfile"
	action="#{pc_Cob00302T01.cob00302.doReferProfileAction}">
</hx:commandExButton>
<hx:commandExButton type="submit"
  value="戻る" styleClass="commandExButton" id="returnDisp"
  action="#{pc_Cob00302T01.doReturnDispAction}"></hx:commandExButton></DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

  <TABLE border="0" cellpadding="5">
    <TBODY>
      <TR>
        <TD width="870">
        <TABLE class="table" width="100%">
          <TBODY>
            <TR align="center" valign="middle">
              <TH nowrap class="v_a" width="190">
              <!--学籍番号 -->
                <h:outputText styleClass="outputText" id="lblDspGaksekiCd"
                value="#{pc_Cob00302T01.cob00302.propGakusekiCd.labelName}"
                style="#{pc_Cob00302T01.cob00302.propGakusekiCd.labelStyle}"></h:outputText></TH>
              <TD width="500"><h:outputText styleClass="outputText" id="lblGaksekiCd"
                value="#{pc_Cob00302T01.cob00302.propGakusekiCd.stringValue}"
                style="#{pc_Cob00302T01.cob00302.propGakusekiCd.style}"></h:outputText>
              </TD>
              <TH nowrap class="v_a" width="190">
              <!--学生氏名 -->
                <h:outputText styleClass="outputText" id="lblDspName"
                value="#{pc_Cob00302T01.cob00302.propName.labelName}"
                style="#{pc_Cob00302T01.cob00302.propName.labelStyle}"></h:outputText></TH>
              <TD width="500"><h:outputText styleClass="outputText" id="lblName"
                value="#{pc_Cob00302T01.cob00302.propName.displayValue}"
                title="#{pc_Cob00302T01.cob00302.propName.stringValue}"
                style="#{pc_Cob00302T01.cob00302.propName.style}"></h:outputText>
              </TD>
            </TR>
            <TR>
              <TH nowrap class="v_b" width="190">
              <!-- 学籍状況 -->
                <h:outputText styleClass="outputText" id="lblDspGakJokyo"
                value="#{pc_Cob00302T01.cob00302.propGakJokyo.labelName}"
                style="#{pc_Cob00302T01.cob00302.propGakJokyo.labelStyle}"></h:outputText></TH>
              <TD width="500"><h:outputText styleClass="outputText" id="lblGakJokyo"
                value="#{pc_Cob00302T01.cob00302.propGakJokyo.stringValue}"></h:outputText></TD>
              <TH nowrap class="v_b" width="190">
              <!-- 旧学籍番号 -->
                <h:outputText styleClass="outputText" id="lblDspKyuGaksekiCd"
                value="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.labelName}"
                style="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.labelStyle}"></h:outputText></TH>
              <TD width="500"><h:outputText styleClass="outputText" id="lblKyuGaksekiCd"
                value="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.stringValue}"></h:outputText></TD>
            </TR>
          </TBODY>
        </TABLE>
        <BR>
        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TBODY>
            <TR>
              <TD>
              <TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
                style="border-bottom-style: none; ">
                <TBODY>
                  <TR>
                    <TD class="tab_head_on" width="58px"><hx:commandExButton
                      type="button" styleClass="tab_head_on" id="tabCob00301T01"
                      value="#{pc_Cob00302T01.cob00302.propTabNameKihon.stringValue}"
                      style="width: 100%"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T02" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameShozoku.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T02Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="*"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T03" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameSnkCls.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T03Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T04" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameAddr.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T04Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T05" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameKAddr.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T05Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T06" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameHsy.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T06Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T07" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameAtsk.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T07Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T08" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameRyugak.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T08Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="57px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T09" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameKyoin.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T09Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="57px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T10" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameIdo.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T10Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="57px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T11" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameNyushi.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T11Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="57px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T12" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameHantei.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T12Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T13" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameClub.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T13Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T14" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameSonota.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T14Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="58px"><hx:commandExButton
                      type="submit" styleClass="tab_head_off" id="tabCob00301T15" style="width:100%"
                      value="#{pc_Cob00302T01.cob00302.propTabNameJiyu.stringValue}"
                      action="#{pc_Cob00302T01.doTabCob00302T15Action}"></hx:commandExButton></TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
            <TR>
              <TD>
              <TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0"
                width="100%" style="border-top-style: none; ">
                <TBODY>
                  <TR>
                    <TD width="100%">
                    <div style="height: 440px">
                    <BR>
                    <TABLE class="table" width="840">
                      <TBODY>
                        <TR>
                          <TH nowrap class="v_b" width="180">
                          <!-- 学生氏名（カナ） -->
                            <h:outputText styleClass="outputText" id="lblDspNameKana"
                            value="#{pc_Cob00302T01.propNameKana.labelName}"
                            style="#{pc_Cob00302T01.propNameKana.labelStyle}"></h:outputText></TH>
                          <TD width="660"><h:outputText styleClass="outputText" id="htmlNameKana"
                            value="#{pc_Cob00302T01.propNameKana.stringValue}"
                            style="#{pc_Cob00302T01.propNameKana.style}"></h:outputText></TD>
                          <TH nowrap class="v_a" width="100"><h:outputText styleClass="outputText"
                            id="lblGakuseiImg"
                            value="学生（写真）"></h:outputText></TH>
                        </TR>
                        <TR>
                          <TH nowrap class="v_c">
                          <!-- 学生氏名（英語） -->
                            <h:outputText styleClass="outputText" id="lblDspNameEng"
                            value="#{pc_Cob00302T01.propNameEng.labelName}"
                            style="#{pc_Cob00302T01.propNameEng.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlNameEng"
                            value="#{pc_Cob00302T01.propNameEng.stringValue}"
                            style="#{pc_Cob00302T01.propNameEng.style}"></h:outputText></TD>
                          <TD rowspan="6">
<%// PageCodeを取得し、initでセットしたリストを取得、requestへ格納する
            Cob00302T01 pagecode = (Cob00302T01) UtilSystem.getManagedBean(Cob00302T01.class);
            String fileName = UtilStr.cnvNull(pagecode.getPropFileName().getStringValue());
            String param = "";
            param += "?formId=" + pagecode.getFormId();
            request.setAttribute("shashinUrl", request.getContextPath()+"/faces/GetImage" + param);
%>
<% if (!fileName.equals("")) {
            String pictDisp = UtilStr.cnvNull(pagecode.getPropHidPictDisp().getStringValue());
%>
<c:out value="<img src=" escapeXml = "false"></c:out><c:out value="'" escapeXml="false"></c:out><c:out value="${shashinUrl}"></c:out><c:out value="'" escapeXml="false"></c:out>
<%		if (pictDisp.equals("0")) { %>
	        <c:out value=" width='100' height='115'>" escapeXml="false"></c:out>
<%		} else if (pictDisp.equals("1")) { %>
	        <c:out value=" height='115'>" escapeXml="false"></c:out>
<%		} else if (pictDisp.equals("2")) { %>
	        <c:out value=" width='100'>" escapeXml="false"></c:out>
<%		} %>
<% } else { %>
<BR>
<% } %>
</TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_d">
                          <!-- 学生氏名（WEB） -->
                            <h:outputText styleClass="outputText" id="lblDspNameWeb"
                            value="#{pc_Cob00302T01.propNameWeb.labelName}"
                            style="#{pc_Cob00302T01.propNameWeb.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlNameWeb"
                            value="#{pc_Cob00302T01.propNameWeb.stringValue}"
                            style="#{pc_Cob00302T01.propNameWeb.style}"></h:outputText></TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_e">
                          <!-- 旧姓氏名 -->
                            <h:outputText styleClass="outputText" id="lblDspOldName"
                            value="#{pc_Cob00302T01.propOldName.labelName}"
                            style="#{pc_Cob00302T01.propOldName.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlOldName"
                            value="#{pc_Cob00302T01.propOldName.stringValue}"
                            style="#{pc_Cob00302T01.propOldName.style}"></h:outputText></TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_f">
                          <!-- 旧姓氏名（カナ） -->
                            <h:outputText styleClass="outputText" id="lblDspOldNameKana"
                            value="#{pc_Cob00302T01.propOldNameKana.labelName}"
                            style="#{pc_Cob00302T01.propOldNameKana.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlOldNameKana"
                            value="#{pc_Cob00302T01.propOldNameKana.stringValue}"
                            style="#{pc_Cob00302T01.propOldNameKana.style}"></h:outputText></TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_g" style="border-bottom-style: none; height: 35px">
                          <!-- 学生顔写真 -->
                            <h:outputText styleClass="outputText" id="lblDspGakuseiPicture"
                              value="#{pc_Cob00302T01.propFileName.labelName}"></h:outputText>
                          </TH>
                          <TD><h:outputText styleClass="outputText" id="lblGakuseiPicture"
                            value="#{pc_Cob00302T01.propFileName.displayValue}"
                            title="#{pc_Cob00302T01.propFileName.stringValue}"
                            style="#{pc_Cob00302T01.propFileName.style}"></h:outputText></TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    <BR>
                    <TABLE class="table" width="840">
                      <TBODY>
                        <TR>
                          <TH nowrap class="v_a" width="180">
                          <!-- 国籍コード -->
                            <h:outputText styleClass="outputText" id="lblDspCountryCd"
                            value="#{pc_Cob00302T01.propCountryCd.labelName}"
                            style="#{pc_Cob00302T01.propCountryCd.labelStyle}"></h:outputText></TH>
                          <TD width="240"><h:outputText styleClass="outputText" id="htmlCountryCd"
                            value="#{pc_Cob00302T01.propCountryCd.stringValue}"
                            style="#{pc_Cob00302T01.propCountryCd.style}"></h:outputText></TD>
                          <TH nowrap class="v_a" width="180">
                          <!-- 国籍名称 -->
                            <h:outputText styleClass="outputText" id="lblDspCountryName"
                            value="#{pc_Cob00302T01.propCountryName.labelName}"
                            style="#{pc_Cob00302T01.propCountryName.labelStyle}"></h:outputText></TH>
                          <TD width="240"><h:outputText styleClass="outputText" id="lblCountryName"
                            value="#{pc_Cob00302T01.propCountryName.stringValue}"
                            style="#{pc_Cob00302T01.propCountryName.style}"></h:outputText></TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_a">
                          <!-- 出身地コード -->
                            <h:outputText styleClass="outputText" id="lblDspSyussinCd"
                            value="#{pc_Cob00302T01.propSyussinCd.labelName}"
                            style="#{pc_Cob00302T01.propSyussinCd.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlSyussinCd"
                            value="#{pc_Cob00302T01.propSyussinCd.stringValue}"
                            style="#{pc_Cob00302T01.propSyussinCd.style}"></h:outputText></TD>
                          <TH nowrap class="v_a">
                          <!-- 出身地名称 -->
                            <h:outputText styleClass="outputText" id="lblDspSyussinName"
                            value="#{pc_Cob00302T01.propSyussinName.labelName}"
                            style="#{pc_Cob00302T01.propSyussinName.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="lblSyussinName"
                            value="#{pc_Cob00302T01.propSyussinName.stringValue}"
                            style="#{pc_Cob00302T01.propSyussinName.style}"></h:outputText></TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_b">
                          <!-- 本籍地コード -->
                            <h:outputText styleClass="outputText" id="lblDspHonsekiCd"
                            value="#{pc_Cob00302T01.propHonsekiCd.labelName}"
                            style="#{pc_Cob00302T01.propHonsekiCd.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlHonsekiCd"
                            value="#{pc_Cob00302T01.propHonsekiCd.stringValue}"
                            style="#{pc_Cob00302T01.propHonsekiCd.style}"></h:outputText></TD>
                          <TH nowrap class="v_b">
                          <!-- 本籍地名称 -->
                            <h:outputText styleClass="outputText" id="lblDspHonsekiName"
                            value="#{pc_Cob00302T01.propHonsekiName.labelName}"
                            style="#{pc_Cob00302T01.propHonsekiName.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="lblHonsekiName"
                            value="#{pc_Cob00302T01.propHonsekiName.stringValue}"
                            style="#{pc_Cob00302T01.propHonsekiName.style}"></h:outputText></TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_c">
                          <!-- 生年月日 -->
                            <h:outputText styleClass="outputText" id="lblDspBirth"
                            value="#{pc_Cob00302T01.propBirth.labelName}"
                            style="#{pc_Cob00302T01.propBirth.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlBirth"
                            value="#{pc_Cob00302T01.propBirth.dateValue}"
                            style="#{pc_Cob00302T01.propBirth.style}"><f:convertDateTime /></h:outputText></TD>
                          <TH nowrap class="v_c">
                          <!-- 年齢 -->
                            <h:outputText styleClass="outputText" id="lblDspAge"
                            value="#{pc_Cob00302T01.propAge.labelName}"
                            style="#{pc_Cob00302T01.propAge.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="lblAge"
                            value="#{pc_Cob00302T01.propAge.stringValue}"
                            style="#{pc_Cob00302T01.propAge.style}"></h:outputText></TD>
                        </TR>
                        <TR>
                          <TH nowrap class="v_b">
                          <!-- 性別 -->
                            <h:outputText styleClass="outputText" id="lblDspSeibetsu"
                            value="#{pc_Cob00302T01.propSeibetsu.labelName}"
                            style="#{pc_Cob00302T01.propSeibetsu.labelStyle}"></h:outputText></TH>
                          <TD colspan="3"><h:outputText styleClass="outputText" id="lblSeibetsu"
                            value="#{pc_Cob00302T01.propSeibetsu.stringValue}"
                            style="#{pc_Cob00302T01.propSeibetsu.style}"></h:outputText></TD>
                        </TR>
                        <TH nowrap class="v_d">
                          <!-- 学部卒業区分 -->
                            <h:outputText styleClass="outputText" id="lblDspGakubuSotKbn"
                            value="#{pc_Cob00302T01.propGakubuSotKbn.labelName}"
                            style="#{pc_Cob00302T01.propGakubuSotKbn.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlGakubuSotKbn"
                            value="#{pc_Cob00302T01.propGakubuSotKbn.stringValue}"
                            style="#{pc_Cob00302T01.propGakubuSotKbn.style}"></h:outputText></TD>
                          <TH nowrap class="v_d">
                          <!-- 社会人 -->
                            <h:outputText styleClass="outputText" id="lblDspShakaijin"
                            value="#{pc_Cob00302T01.propShakaijin.labelName}"
                            style="#{pc_Cob00302T01.propShakaijin.labelStyle}"></h:outputText></TH>
                          <TD><h:outputText styleClass="outputText" id="htmlShakaijin"
                            value="#{pc_Cob00302T01.propShakaijin.stringValue}"
                            style="#{pc_Cob00302T01.propShakaijin.style}"></h:outputText></TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    <BR>
                    </div>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        </TD>
      </TR>
    </TBODY>
  </TABLE>

<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--></DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/footer.jsp" />

</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
