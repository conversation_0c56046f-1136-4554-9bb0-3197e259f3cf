<%--
  卒業生学科組織設定（住所情報）

  <AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coe00201T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Coe00201T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">

// 画面ロード時処理
function loadAction(event){
  // 郵便番号入力補助
  setZipMenu('form1:htmlAddrNo','form1:htmlAddr1','form1:htmlAddrKana1');
}

// 住所検索画面（引数：①②郵便番号、③④住所１、⑤住所カナ１）
function openJyusyoSubWindow(field1, field2, field3) {
  var zipNo = document.getElementById(field1).value;
  zipNo = encodeURIComponent(zipNo);
  var add = document.getElementById(field2).value;
  add = encodeURIComponent(add);
  var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp"
    +"?"
    +"zipNo=" + field1
    +"&zipNoValue=" + zipNo
    +"&jyusyoKanji=" + field2
    +"&jyusyoValue=" + add
    +"&jyusyoKana=" + field3
    +"&chihoCd=";
  openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");
  return true;
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// 確定時のクリック処理
function onClickUpdate(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// 削除時のクリック処理
function onClickDelete(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}
// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {

}

window.attachEvent('onload', endload);
  function endload() {
  changeScrollPosition('scroll','listScroll');
}
</SCRIPT></HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Coe00201T02.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Coe00201T02">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Coe00201T02.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Coe00201T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Coe00201T02.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
  <TABLE border="0" cellpadding="5">
    <TBODY>
      <TR>
        <TD width="870">
        <TABLE class="table" width="100%">
          <TBODY>
            <TR align="center" valign="middle">
              <!-- 入学年度 -->
              <TH nowrap class="v_a" width="150">
                <h:outputText
                  id="lblNyugakNendo"
                  styleClass="outputText"
                  value="#{pc_Coe00201T01.coe00201.propNyugakNendo.labelName}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakNendo.labelStyle}">
                </h:outputText>
              </TH>
              <TD width="200">
                <h:inputText
                  id="htmlNyugakNendo"
                  styleClass="inputText"
                  size="4"
                  disabled="#{pc_Coe00201T01.coe00201.propNyugakNendo.disabled}"
                  value="#{pc_Coe00201T01.coe00201.propNyugakNendo.dateValue}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakNendo.style}">
            	  <hx:inputHelperAssist errorClass="inputText_Error"
					imeMode="inactive" promptCharacter="_" />
				  <f:convertDateTime pattern="yyyy" />
                </h:inputText>
              </TD>
              <!-- 入学学期ＮＯ -->
              <TH nowrap class="v_b" width="150">
                <h:outputText
                  id="lblNyugakGakkiNo"
                  styleClass="outputText"
                  value="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.labelName}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.labelStyle}">
                </h:outputText>
              </TH>
              <TD width="200">
                <h:inputText
                  id="htmlNyugakGakkiNo"
                  styleClass="inputText"
                  size="2"
                  maxlength="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.maxLength}"
                  disabled="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.disabled}"
                  value="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.integerValue}"
                  style="#{pc_Coe00201T01.coe00201.propNyugakGakkiNo.style}">
				  <hx:inputHelperAssist errorClass="inputText_Error"
					imeMode="inactive" promptCharacter="_" />
				  <f:convertNumber type="number" pattern="#0"/>
                </h:inputText>
              </TD>
              <TD style="background-color: transparent; text-align: right" class="clear_border">
                <!-- 選択ボタン -->
                <hx:commandExButton
                  type="submit"
                  id="select"
                  styleClass="commandExButton"
                  value="選　択"
                  disabled="#{pc_Coe00201T01.coe00201.propSelect.disabled}"
                  action="#{pc_Coe00201T01.doSelectAction}">
                </hx:commandExButton>
                <!-- 解除ボタン -->
                <hx:commandExButton
                  type="submit"
                  id="unselect"
                  styleClass="commandExButton"
                  value="解　除"
                  disabled="#{pc_Coe00201T01.coe00201.propUnSelect.disabled}"
                  action="#{pc_Coe00201T01.doUnselectAction}">
                </hx:commandExButton>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
		<!-- ↓レコードカウントのテーブル -->
		<TABLE border="0" align="center" width="870">
			<TBODY>
				<TR>
					<TD align="right" nowrap class="outputText" width="100%">
						<h:outputText
							styleClass="outputText"
							id="lblSotGakkaSskListCnt" 
							value="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.listCount}">
						</h:outputText>件
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		<!-- ↓データテーブルのテーブル -->
		<TABLE border="0" width="870">
		  <TBODY>
			<TR>
			  <TD>
				<DIV class="listScroll" id="listScroll" style="height:130px;" onscroll="setScrollPosition('scroll',this);">
				  <h:dataTable
					border="0" cellpadding="0" cellspacing="0"
					columnClasses="columnClass"
					headerClass="headerClass"
					footerClass="footerClass" 
					rowClasses="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.rowClasses}"
					styleClass="meisai_scroll" id="htmlSotGakkaSskList"
					value="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.list}" var="varlist">
					<h:column id="column1">
						<f:facet name="header">
							<h:outputText styleClass="outputText" id="lblszkGakkaCd_head"
								value="コード"></h:outputText>
						</f:facet>
						<h:outputText styleClass="outputText" id="lblszkGakkaCd_list"
							value="#{varlist.szkGakkaCd}"></h:outputText>
						<f:attribute value="100" name="width" />
					</h:column>
					<h:column id="column2">
						<f:facet name="header">
							<h:outputText styleClass="outputText" id="lblszkGakkaName_head"
								value="所属学科組織名称"></h:outputText>
						</f:facet>
						<h:outputText styleClass="outputText" id="lblszkGakkaName_list"
							value="#{varlist.szkGakkaName}"></h:outputText>
						<f:attribute value="710" name="width" />
					</h:column>
					<h:column id="column3">
						<f:facet name="header">
						</f:facet>
						<hx:commandExButton
							type="submit"value="選択"
							styleClass="commandExButton"
							id="button_select"
							action="#{pc_Coe00201T01.doSelectList}">
						</hx:commandExButton>
						<f:attribute value="40" name="width" />
					</h:column>
				  </h:dataTable>
				</DIV>
			  </TD>
			</TR>
		  </TBODY>
		</TABLE>
		<!-- データテーブルと入力フォームの間の余白 -->
        <DIV class="linespace" id="linespace" style="height:10px;"></DIV>
        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TBODY>
            <TR align="left">
              <TD>
              <TABLE border="0" cellpadding="0" cellspacing="0"
                style="border-bottom-style: none; ">
                <TBODY>
                  <TR>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="submit"
                        id="tabCoe00201T01"
                        styleClass="tab_head_off"
                        value="#{pc_Coe00201T01.coe00201.propTabNameKihon.stringValue}"
                        action="#{pc_Coe00201T02.doTabCoe00201T01Action}"
                        style="width: 100%">
                      </hx:commandExButton>
                    </TD>
                    <TD class="tab_head_on" width="100">
                      <hx:commandExButton
                        type="button"
                        id="tabCoe00201T02"
                        styleClass="tab_head_on"
                        value="#{pc_Coe00201T01.coe00201.propTabNameAddr.stringValue}"
                        style="width:100%">
                      </hx:commandExButton></TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="submit"
                        id="tabCoe00201T03"
                        styleClass="tab_head_off"
                        value="#{pc_Coe00201T01.coe00201.propTabNameDaihyo.stringValue}"
                        action="#{pc_Coe00201T02.doTabCoe00201T03Action}"
                        style="width:100%">
                      </hx:commandExButton>
                    </TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="submit"
                        id="tabCoe00201T04"
                        styleClass="tab_head_off"
                        value="#{pc_Coe00201T01.coe00201.propTabNameMeisai.stringValue}"
                        action="#{pc_Coe00201T01.doTabCoe00201T04Action}"
                        style="width:100%">
                      </hx:commandExButton>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
            <TR>
              <TD>
              <TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" width="100%">
                <TBODY>
                  <TR>
                    <TD width="100%">
                    <div style="height: 318px">
                    <TABLE class="table" width="853">
                      <TBODY>
                        <TR style="height:10px;">
                        </TR>
                          <!-- 郵便番号 -->
                          <TH nowrap class="v_a" width="130">
                            <h:outputText
                              id="lblAddrNo"
                              styleClass="outputText"
                              value="#{pc_Coe00201T02.propAddrNo.labelName}"
                              style="#{pc_Coe00201T02.propAddrNo.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddrNo"
                              styleClass="inputText"
                              size="7"
                              maxlength="#{pc_Coe00201T02.propAddrNo.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddrNo.disabled}"
                              value="#{pc_Coe00201T02.propAddrNo.stringValue}"
                              style="#{pc_Coe00201T02.propAddrNo.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 住所 -->
                          <TH nowrap class="v_b" rowspan="3">
                            <h:outputText
                              id="lblAddr1"
                              styleClass="outputText"
                              value="#{pc_Coe00201T02.propAddr1.labelName}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddr1"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T02.propAddr1.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddr1.disabled}"
                              value="#{pc_Coe00201T02.propAddr1.stringValue}"
                              style="#{pc_Coe00201T02.propAddr1.style}">
                            </h:inputText>
                            <hx:commandExButton
                              type="button"
                              id="addrSearch"
                              styleClass="commandExButton_search"
                              value="〒"
                              onclick="openJyusyoSubWindow('form1:htmlAddrNo', 'form1:htmlAddr1', 'form1:htmlAddrKana1');">
                            </hx:commandExButton>
                            <h:outputText
                              styleClass="outputText"
                              id="text1"
                              value="（都道府県市区町村大字）">
                            </h:outputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddr2"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T02.propAddr2.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddr2.disabled}"
                              value="#{pc_Coe00201T02.propAddr2.stringValue}"
                              style="#{pc_Coe00201T02.propAddr2.style}">
                            </h:inputText>
                            <h:outputText
                              id="text2"
                              styleClass="outputText"
                              value="（丁目・字以下）">
                            </h:outputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddr3"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T02.propAddr3.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddr3.disabled}"
                              value="#{pc_Coe00201T02.propAddr3.stringValue}"
                              style="#{pc_Coe00201T02.propAddr3.style}">
                            </h:inputText>
                            <h:outputText
                              id="text3"
                              styleClass="outputText"
                              value="（マンション/ビル名 号室）">
                            </h:outputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 住所カナ -->
                          <TH nowrap class="v_c" rowspan="3">
                            <h:outputText
                              id="lblAddrKana1"
                              styleClass="outputText"
                              value="#{pc_Coe00201T02.propAddrKana1.labelName}"
                              style="#{pc_Coe00201T02.propAddrKana1.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddrKana1"
                              styleClass="inputText"
                              size="100"
                              maxlength="#{pc_Coe00201T02.propAddrKana1.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddrKana1.disabled}"
                              value="#{pc_Coe00201T02.propAddrKana1.stringValue}"
                              style="#{pc_Coe00201T02.propAddrKana1.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddrKana2"
                              styleClass="inputText"
                              size="100"
                              maxlength="#{pc_Coe00201T02.propAddrKana2.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddrKana2.disabled}"
                              value="#{pc_Coe00201T02.propAddrKana2.stringValue}"
                              style="#{pc_Coe00201T02.propAddrKana2.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddrKana3"
                              styleClass="inputText"
                              size="100"
                              maxlength="#{pc_Coe00201T02.propAddrKana3.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddrKana3.disabled}"
                              value="#{pc_Coe00201T02.propAddrKana3.stringValue}"
                              style="#{pc_Coe00201T02.propAddrKana3.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 住所英語 -->
                          <TH nowrap class="v_d" rowspan="3">
                            <h:outputText
                              id="lblAddrEng1"
                              styleClass="outputText"
                              value="#{pc_Coe00201T02.propAddrEng1.labelName}"
                              style="#{pc_Coe00201T02.propAddrEng1.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddrEng1"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T02.propAddrEng1.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddrEng1.disabled}"
                              value="#{pc_Coe00201T02.propAddrEng1.stringValue}"
                              style="#{pc_Coe00201T02.propAddrEng1.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddrEng2"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T02.propAddrEng2.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddrEng2.disabled}"
                              value="#{pc_Coe00201T02.propAddrEng2.stringValue}"
                              style="#{pc_Coe00201T02.propAddrEng2.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlAddrEng3"
                              styleClass="inputText"
                              size="50"
                              maxlength="#{pc_Coe00201T02.propAddrEng3.maxLength}"
                              disabled="#{pc_Coe00201T02.propAddrEng3.disabled}"
                              value="#{pc_Coe00201T02.propAddrEng3.stringValue}"
                              style="#{pc_Coe00201T02.propAddrEng3.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    </div>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
          <TBODY>
            <TR align="right">
              <TD align="center">
                <hx:commandExButton
                  type="submit"
                  id="update"
                  styleClass="commandExButton_dat"
                  value="確定"
                  disabled="#{pc_Coe00201T01.coe00201.propUpdate.disabled}"
                  onclick="return onClickUpdate('#{msg.SY_MSG_0001W}');"
                  action="#{pc_Coe00201T02.doUpdateAction}">
                </hx:commandExButton>
                <hx:commandExButton
                  type="submit"
                  id="delete"
                  styleClass="commandExButton_dat"
                  value="削除"
                  disabled="#{pc_Coe00201T01.coe00201.propDelete.disabled}"
                  onclick="return onClickDelete('#{msg.SY_MSG_0004W}');"
                  action="#{pc_Coe00201T02.doDeleteAction}">
                </hx:commandExButton>
                <hx:commandExButton
                  type="submit"
                  id="clear"
                  styleClass="commandExButton_etc"
                  value="クリア"
                  disabled="#{pc_Coe00201T01.coe00201.propClear.disabled}"
                  action="#{pc_Coe00201T02.doClearAction}">
                </hx:commandExButton>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        </TD>
      </TR>
    </TBODY>
  </TABLE>
  <h:inputHidden id="htmlHidButtonKbn" value="#{pc_Coe00201T01.coe00201.propHidButtonKbn.integerValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidAction" value="#{pc_Coe00201T01.coe00201.propHidAction.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidInsUpdKbn" value="#{pc_Coe00201T01.coe00201.propHidInsUpdKbn.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidErrMessage" value="#{pc_Coe00201T01.coe00201.propHidErrMessage.value}"></h:inputHidden>
  <h:inputHidden id="scroll" value="#{pc_Coe00201T01.coe00201.propSotGakkaSskList.scrollPosition}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
