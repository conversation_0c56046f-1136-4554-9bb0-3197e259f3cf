<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kac00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>修繕検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_5(thisObj, thisEvent) {
	// 設置場所名称＿略称を取得する																				
	var servlet = "rev/ka/KazSetiAJAX";
	var args = new Array();
	args['code'] = document.getElementById('form1:htmlSetchiBashoCd').value;
	var target = "form1:htmlSetchiBashoNameRyak";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_6(thisObj, thisEvent) {
	// 管理部門名称＿略称を取得する																				
	// 管理部門名称を取得する
	var servlet = "rev/ka/KaCogYosanTaniAJAX";
	var target = "form1:htmlKanriBmnNameRyak";
	var code = new Array();
	code['code1'] = '0';
	code['code2'] = '1';
	code['code3'] = '';
	code['code4'] = thisObj.value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code );
}

function func_8(thisObj, thisEvent) {
	// 取引先名称（取得先名称）を取得する
	var servlet = "rev/co/CogToriKihonAJAX";
	var target = "form1:htmlShutokusakiCdName";
	var code = new Array();
	code['code1'] = thisObj.value;
	code['code2'] = "0";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function loadFunc(){
	// Ajaxの起動
	func_5(document.getElementById('form1:htmlSetchiBashoCd'), "");
	func_6(document.getElementById('form1:htmlKanriBmnCd'), '');
	func_8(document.getElementById('form1:htmlShutokusakiCd'), '');
}

window.attachEvent("onload", loadFunc);
window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kac00701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kac00701.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kac00701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kac00701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area" >
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<hx:commandExButton type="submit" value="新規登録"
							styleClass="commandExButton" id="register"
							action="#{pc_Kac00701.doRegisterAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="820" style="margin-top: 10px;">
				<TBODY>
					<TR>
						<TH rowspan="18" width="20" align="center">
							<h:outputText styleClass="outputText" id="lblSsn1" value="資"></h:outputText><BR>
							<h:outputText styleClass="outputText" id="lblSsn2" value="産"></h:outputText>
						</TH>
						<TH class="v_a" colspan="2">
						<h:outputText styleClass="outputText"
							id="lblSsnSyurui" value="#{pc_Kac00701.propSsnSyurui.labelName}"
							style="#{pc_Kac00701.propSsnSyurui.labelStyle}"></h:outputText>
						</TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="261">
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlSyurui"
											disabled="#{pc_Kac00701.propSsnSyurui.disabled}"
											readonly="#{pc_Kac00701.propSsnSyurui.readonly}"
											value="#{pc_Kac00701.propSsnSyurui.stringValue}">
											<f:selectItems value="#{pc_Kac00701.propSsnSyurui.list}" />
										</h:selectOneRadio>
									</TD>
									<TD class="clear_border" 
										style="background-color:transparent">
										<hx:commandExButton type="submit" value="選択"
											styleClass="commandExButton" id="select"
											action="#{pc_Kac00701.doSelectAction}"
											disabled="#{pc_Kac00701.propSelect.disabled}">
										</hx:commandExButton>
										<hx:commandExButton type="submit" value="解除"
											styleClass="commandExButton" id="kaijo"
											disabled="#{pc_Kac00701.propKaijo.disabled}"
											action="#{pc_Kac00701.doKaijoAction}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR id="row0" style="${pc_Kac00701.trSsnKbn}">
						<TH class="v_b" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblSsnKbn" value="#{pc_Kac00701.propSsnKbn.labelName}"
								style="#{pc_Kac00701.propSsnKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609"><h:selectManyCheckbox
							disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlSsnKbn"
							disabled="#{pc_Kac00701.propSsnKbn.disabled}"
							readonly="#{pc_Kac00701.propSsnKbn.readonly}"
							value="#{pc_Kac00701.propSsnKbn.stringValue}"
							style="#{pc_Kac00701.propSsnKbn.style}">
							<f:selectItems value="#{pc_Kac00701.propSsnKbn.list}" />
						</h:selectManyCheckbox></TD>
					</TR>
					<TR style="${pc_Kac00701.trSsnNo}">
						<TH class="v_c" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblSsnNo"
							value="#{pc_Kac00701.propSsnNo.labelName}"
							style="#{pc_Kac00701.propSsnNo.labelStyle}">
						</h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText" id="htmlSsnNo"
							size="20" value="#{pc_Kac00701.propSsnNo.stringValue}"
							style="#{pc_Kac00701.propSsnNo.style}"
							maxlength="#{pc_Kac00701.propSsnNo.maxLength}"
							disabled="#{pc_Kac00701.propSsnNo.disabled}"
							readonly="#{pc_Kac00701.propSsnNo.readonly}"
							rendered="#{pc_Kac00701.propSsnNo.rendered}">
						</h:inputText>（前方一致）</TD>
					</TR>
					<TR style="${pc_Kac00701.trBhnNo}">
						<TH class="v_b" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblBhnNo"
							value="#{pc_Kac00701.propBhnNo.labelName}"
							style="#{pc_Kac00701.propBhnNo.labelStyle}">
						</h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText" id="htmlBhnNo"
							size="20" value="#{pc_Kac00701.propBhnNo.stringValue}"
							style="#{pc_Kac00701.propBhnNo.style}"
							maxlength="#{pc_Kac00701.propBhnNo.maxLength}"
							disabled="#{pc_Kac00701.propBhnNo.disabled}"
							readonly="#{pc_Kac00701.propBhnNo.readonly}"
							rendered="#{pc_Kac00701.propBhnNo.rendered}">
						</h:inputText>（前方一致）</TD>
					</TR>
					<TR style="${pc_Kac00701.trEdaNo}">
						<TH class="v_c" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblEdaNo"
							value="#{pc_Kac00701.propEdaNo.labelName}"
							style="#{pc_Kac00701.propEdaNo.labelStyle}">
						</h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText" id="htmlEdaNo"
							size="5" value="#{pc_Kac00701.propEdaNo.stringValue}"
							maxlength="#{pc_Kac00701.propEdaNo.maxLength}"
							disabled="#{pc_Kac00701.propEdaNo.disabled}"
							readonly="#{pc_Kac00701.propEdaNo.readonly}"
							rendered="#{pc_Kac00701.propEdaNo.rendered}"
							style="padding-right: 3px; text-align: right; #{pc_Kac00701.propEdaNo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
					<TR style="${pc_Kac00701.trLeaseNo}">
						<TH class="v_b" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblLeaseNo"
							value="#{pc_Kac00701.propLeaseNo.labelName}"
							style="#{pc_Kac00701.propLeaseNo.labelStyle}">
						</h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText"
							id="htmlLeaseNo" size="20"
							value="#{pc_Kac00701.propLeaseNo.stringValue}"
							style="#{pc_Kac00701.propLeaseNo.style}"
							maxlength="#{pc_Kac00701.propLeaseNo.maxLength}"
							disabled="#{pc_Kac00701.propLeaseNo.disabled}"
							readonly="#{pc_Kac00701.propLeaseNo.readonly}"
							rendered="#{pc_Kac00701.propLeaseNo.rendered}">
						</h:inputText>（前方一致）</TD>
					</TR>
					<TR style="${pc_Kac00701.trLeaseSsnNo}">
						<TH class="v_c" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblLeaseSsnNo"
							value="#{pc_Kac00701.propLeaseSsnNo.labelName}"
							style="#{pc_Kac00701.propLeaseSsnNo.labelStyle}">
						</h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText"
							id="htmlLeaseSsnNo" size="3"
							value="#{pc_Kac00701.propLeaseSsnNo.stringValue}"
							maxlength="#{pc_Kac00701.propLeaseSsnNo.maxLength}"
							disabled="#{pc_Kac00701.propLeaseSsnNo.disabled}"
							readonly="#{pc_Kac00701.propLeaseSsnNo.readonly}"
							rendered="#{pc_Kac00701.propLeaseSsnNo.rendered}"
							style="padding-right: 3px; text-align: right; #{pc_Kac00701.propLeaseSsnNo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
					<TR style="${pc_Kac00701.trShutokuDate}">
						<TH class="v_e" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblShutokuDate"
								value="#{pc_Kac00701.propShutokuDateFrom.labelName}"
								style="#{pc_Kac00701.propShutokuDateFrom.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText"
							id="htmlShutokuDateFrom" size="10"
							value="#{pc_Kac00701.propShutokuDateFrom.dateValue}"
							style="#{pc_Kac00701.propShutokuDateFrom.style}"
							disabled="#{pc_Kac00701.propShutokuDateFrom.disabled}"
							readonly="#{pc_Kac00701.propShutokuDateFrom.readonly}"
							rendered="#{pc_Kac00701.propShutokuDateFrom.rendered}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText>～<h:inputText styleClass="inputText"
							id="htmlShutokuDateTo" size="10"
							value="#{pc_Kac00701.propShutokuDateTo.dateValue}"
							style="#{pc_Kac00701.propShutokuDateTo.style}"
							disabled="#{pc_Kac00701.propShutokuDateTo.disabled}"
							readonly="#{pc_Kac00701.propShutokuDateTo.readonly}"
							rendered="#{pc_Kac00701.propShutokuDateTo.rendered}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
					</TR>
					<TR style="${pc_Kac00701.trLeaseKikan}">
						<TD colspan="2" class="group_label_top" nowrap>
							<h:outputText 
								styleClass="outputText" 
								id="lblLeaseKikan" 
								value="リース期間">
							</h:outputText>
						</TD>
						<TD>
						</TD>
					</TR>
					<TR style="${pc_Kac00701.trLeaseStartDate}">
						<TD class="group_label" width="30">
						</TD>
						<TH class="v_e">
							<h:outputText styleClass="outputText"
								id="lblLeaseStartDate"
								value="#{pc_Kac00701.propLeaseStartDateFrom.labelName}"
								style="#{pc_Kac00701.propLeaseStartDateFrom.labelStyle}"></h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText"
							id="htmlLeaseStartDateFrom" size="10"
							value="#{pc_Kac00701.propLeaseStartDateFrom.dateValue}"
							style="#{pc_Kac00701.propLeaseStartDateFrom.style}"
							disabled="#{pc_Kac00701.propLeaseStartDateFrom.disabled}"
							readonly="#{pc_Kac00701.propLeaseStartDateFrom.readonly}"
							rendered="#{pc_Kac00701.propLeaseStartDateFrom.rendered}">
									<f:convertDateTime pattern="yyyy/MM/dd" />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
									<hx:inputHelperDatePicker />
								</h:inputText>～<h:inputText styleClass="inputText"
									id="htmlLeaseStartDateTo" size="10"
									value="#{pc_Kac00701.propLeaseStartDateTo.dateValue}"
									style="#{pc_Kac00701.propLeaseStartDateTo.style}"
									disabled="#{pc_Kac00701.propLeaseStartDateTo.disabled}"
									readonly="#{pc_Kac00701.propLeaseStartDateTo.readonly}"
									rendered="#{pc_Kac00701.propLeaseStartDateTo.rendered}">
									<f:convertDateTime pattern="yyyy/MM/dd" />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
									<hx:inputHelperDatePicker />
								</h:inputText>
						</TD>
					</TR>
					<TR style="${pc_Kac00701.trLeaseEndDate}">
						<TD class="group_label" width="30">
						</TD>
						<TH class="v_e">
						<h:outputText styleClass="outputText"
							id="lblLeaseEndDate"
							value="#{pc_Kac00701.propLeaseEndDateFrom.labelName}"
							style="#{pc_Kac00701.propLeaseEndDateFrom.labelStyle}">
						</h:outputText>
						</TH>
						<TD width="609"><h:inputText styleClass="inputText"
							id="htmlLeaseEndDateFrom" size="10"
							value="#{pc_Kac00701.propLeaseEndDateFrom.dateValue}"
							style="#{pc_Kac00701.propLeaseEndDateFrom.style}"
							disabled="#{pc_Kac00701.propLeaseEndDateFrom.disabled}"
							readonly="#{pc_Kac00701.propLeaseEndDateFrom.readonly}"
							rendered="#{pc_Kac00701.propLeaseEndDateFrom.rendered}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<hx:inputHelperDatePicker />
							</h:inputText>～<h:inputText styleClass="inputText"
								id="htmlLeaseEndDateTo" size="10"
								value="#{pc_Kac00701.propLeaseEndDateTo.dateValue}"
								style="#{pc_Kac00701.propLeaseEndDateTo.style}"
								disabled="#{pc_Kac00701.propLeaseEndDateTo.disabled}"
								readonly="#{pc_Kac00701.propLeaseEndDateTo.readonly}"
								rendered="#{pc_Kac00701.propLeaseEndDateTo.rendered}">
								<f:convertDateTime pattern="yyyy/MM/dd" />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<hx:inputHelperDatePicker />
								</h:inputText>
						</TD>
					</TR>
					<TR style="${pc_Kac00701.trSsnBunrui}">
						<TH class="v_d" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblSsnBunrui" value="#{pc_Kac00701.propSsnBunrui.labelName}"
								style="#{pc_Kac00701.propSsnBunrui.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:selectOneMenu
								styleClass="selectOneMenu" id="htmlSsnBunrui"
								disabled="#{pc_Kac00701.propSsnBunrui.disabled}"
								readonly="#{pc_Kac00701.propSsnBunrui.readonly}"
								rendered="#{pc_Kac00701.propSsnBunrui.rendered}"
								value="#{pc_Kac00701.propSsnBunrui.value}">
								<f:selectItems value="#{pc_Kac00701.propSsnBunrui.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
					<TR style="${pc_Kac00701.trBhnBunrui}">
						<TH class="v_d" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblBhnBunrui" value="#{pc_Kac00701.propBhnBunrui.labelName}"
								style="#{pc_Kac00701.propBhnBunrui.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:selectOneMenu
								styleClass="selectOneMenu" id="htmlBhnBunrui"
								disabled="#{pc_Kac00701.propBhnBunrui.disabled}"
								readonly="#{pc_Kac00701.propBhnBunrui.readonly}"
								rendered="#{pc_Kac00701.propBhnBunrui.rendered}"
								value="#{pc_Kac00701.propBhnBunrui.value}">
								<f:selectItems value="#{pc_Kac00701.propBhnBunrui.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
					<TR style="${pc_Kac00701.trLeaseSsnBunrui}">
						<TH class="v_d" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblLeaseSsnBunrui"
								value="#{pc_Kac00701.propLeaseSsnBunrui.labelName}"
								style="#{pc_Kac00701.propLeaseSsnBunrui.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:selectOneMenu
								styleClass="selectOneMenu" id="htmlLeaseSsnBunrui"
								disabled="#{pc_Kac00701.propLeaseSsnBunrui.disabled}"
								readonly="#{pc_Kac00701.propLeaseSsnBunrui.readonly}"
								rendered="#{pc_Kac00701.propLeaseSsnBunrui.rendered}"
								value="#{pc_Kac00701.propLeaseSsnBunrui.value}">
								<f:selectItems value="#{pc_Kac00701.propLeaseSsnBunrui.list}" />
								</h:selectOneMenu>
						</TD>
					</TR>
					<TR>
						<TH class="v_d" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblSsnName" value="#{pc_Kac00701.propSsnName.labelName}"
								style="#{pc_Kac00701.propSsnName.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="10">
									<h:inputText styleClass="inputText"
										id="htmlSsnName" size="70"
										value="#{pc_Kac00701.propSsnName.stringValue}"
										style="#{pc_Kac00701.propSsnName.style}"
										maxlength="#{pc_Kac00701.propSsnName.maxLength}"
										disabled="#{pc_Kac00701.propSsnName.disabled}"
										readonly="#{pc_Kac00701.propSsnName.readonly}"
										rendered="#{pc_Kac00701.propSsnName.rendered}">
									</h:inputText>
									</TD>
									<TD>
										<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSsnNameFindType"
										disabled="#{pc_Kac00701.propSsnNameFindType.disabled}"
										readonly="#{pc_Kac00701.propSsnNameFindType.readonly}"
										value="#{pc_Kac00701.propSsnNameFindType.stringValue}"
										rendered="#{pc_Kac00701.propSsnNameFindType.rendered}"><f:selectItems value="#{pc_Kac00701.propSsnNameFindType.list}" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblSetchiBashoCd"
								value="#{pc_Kac00701.propSetchiBashoCd.labelName}"
								style="#{pc_Kac00701.propSetchiBashoCd.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:inputText 
								styleClass="inputText"
								id="htmlSetchiBashoCd" 
								size="10"
								value="#{pc_Kac00701.propSetchiBashoCd.stringValue}"
								style="#{pc_Kac00701.propSetchiBashoCd.style}"
								maxlength="#{pc_Kac00701.propSetchiBashoCd.maxLength}"
								disabled="#{pc_Kac00701.propSetchiBashoCd.disabled}"
								readonly="#{pc_Kac00701.propSetchiBashoCd.readonly}"
								rendered="#{pc_Kac00701.propSetchiBashoCd.rendered}"
								onblur="return func_5(this, event);">
							</h:inputText>&#160;&#160;(前方一致)
							<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="searchSetchiBashoCd"
							action="#{pc_Kac00701.doSearchSetchiBashoCdAction}"
							disabled="#{pc_Kac00701.propSearchSetchiBashoCd.disabled}"
							style="#{pc_Kac00701.propSearchSetchiBashoCd.style}">
						</hx:commandExButton>
							<h:outputText
								styleClass="outputText" id="htmlSetchiBashoNameRyak">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblKanriBmnCd"
								value="#{pc_Kac00701.propKanriBmnCd.labelName}"
								style="#{pc_Kac00701.propKanriBmnCd.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:inputText styleClass="inputText"
								id="htmlKanriBmnCd" size="10"
								value="#{pc_Kac00701.propKanriBmnCd.stringValue}"
								style="#{pc_Kac00701.propKanriBmnCd.style}"
								maxlength="#{pc_Kac00701.propKanriBmnCd.maxLength}"
								onblur="return func_6(this, event);"
								disabled="#{pc_Kac00701.propKanriBmnCd.disabled}"
								readonly="#{pc_Kac00701.propKanriBmnCd.readonly}"
								rendered="#{pc_Kac00701.propKanriBmnCd.rendered}">
							</h:inputText>&#160;&#160;(前方一致)
							<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="searchKanriBmnCd"
							action="#{pc_Kac00701.doSearchKanriBmnCdAction}"
							disabled="#{pc_Kac00701.propSearchKanriBmnCd.disabled}"
							style="#{pc_Kac00701.propSearchKanriBmnCd.style}">
						</hx:commandExButton>
							<h:outputText
								styleClass="outputText" id="htmlKanriBmnNameRyak">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblKyokanKbn" value="#{pc_Kac00701.propKyokanKbn.labelName}"
								style="#{pc_Kac00701.propKyokanKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlKyokanKbn"
							disabled="#{pc_Kac00701.propKyokanKbn.disabled}"
							readonly="#{pc_Kac00701.propKyokanKbn.readonly}"
							value="#{pc_Kac00701.propKyokanKbn.stringValue}"
							style="#{pc_Kac00701.propKyokanKbn.style}">
							<f:selectItems value="#{pc_Kac00701.propKyokanKbn.list}" />
						</h:selectManyCheckbox>
						</TD>
					</TR>
					<TR>
						<TH rowspan="13" class="v_d" width="20" align="center">
							<h:outputText styleClass="outputText" id="lblSyzn1" value="修"></h:outputText><BR>
							<h:outputText styleClass="outputText" id="lblSyzn2" value="繕"></h:outputText>
						</TH>
						<TH class="v_e" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblShuzenDate"
								value="#{pc_Kac00701.propShuzenDateFrom.labelName}"
								style="#{pc_Kac00701.propShuzenDateFrom.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:inputText styleClass="inputText"
								id="htmlShuzenDateFrom" size="10"
								value="#{pc_Kac00701.propShuzenDateFrom.dateValue}"
								style="#{pc_Kac00701.propShuzenDateFrom.style}"
								disabled="#{pc_Kac00701.propShuzenDateFrom.disabled}"
								readonly="#{pc_Kac00701.propShuzenDateFrom.readonly}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<hx:inputHelperDatePicker />
							</h:inputText>～<h:inputText styleClass="inputText"
								id="htmlShuzenDateTo" size="10"
								value="#{pc_Kac00701.propShuzenDateTo.dateValue}"
								style="#{pc_Kac00701.propShuzenDateTo.style}"
								disabled="#{pc_Kac00701.propShuzenDateTo.disabled}"
								readonly="#{pc_Kac00701.propShuzenDateTo.readonly}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_d" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblKenmeiName"
							value="#{pc_Kac00701.propKenmeiName.labelName}"
							style="#{pc_Kac00701.propKenmeiName.labelStyle}">
						</h:outputText>
						</TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="10">
									<h:inputText styleClass="inputText" id="htmlKenmeiName"
									size="60" value="#{pc_Kac00701.propKenmeiName.stringValue}"
									style="#{pc_Kac00701.propKenmeiName.style}"
									maxlength="#{pc_Kac00701.propKenmeiName.maxLength}"
									disabled="#{pc_Kac00701.propKenmeiName.disabled}"
									readonly="#{pc_Kac00701.propKenmeiName.readonly}"
									rendered="#{pc_Kac00701.propKenmeiName.rendered}">
								</h:inputText>
									</TD>
									<TD>
										<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlKenmeiNameFindType"
										disabled="#{pc_Kac00701.propKenmeiNameFindType.disabled}"
										readonly="#{pc_Kac00701.propKenmeiNameFindType.readonly}"
										value="#{pc_Kac00701.propKenmeiNameFindType.stringValue}"><f:selectItems
											value="#{pc_Kac00701.propKenmeiNameFindType.list}" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR style="${pc_Kac00701.trShuzenKbn}">
						<TH class="v_a" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblShuzenKbn" value="#{pc_Kac00701.propShuzenKbn.labelName}"
								style="#{pc_Kac00701.propShuzenKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlShuzenKbn"
							disabled="#{pc_Kac00701.propShuzenKbn.disabled}"
							readonly="#{pc_Kac00701.propShuzenKbn.readonly}"
							value="#{pc_Kac00701.propShuzenKbn.stringValue}"
							style="#{pc_Kac00701.propShuzenKbn.style}">
							<f:selectItems value="#{pc_Kac00701.propShuzenKbn.list}" />
						</h:selectManyCheckbox>
						</TD>
					</TR>
					<TR id="row9"  style="${pc_Kac00701.trSsnHenkoKbn}">
						<TH class="v_b" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblSsnHenkoKbn"
								value="#{pc_Kac00701.propSsnHenkoKbn.labelName}"
								style="#{pc_Kac00701.propSsnHenkoKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="609">
							<h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlSsnHenkoKbn"
							disabled="#{pc_Kac00701.propSsnHenkoKbn.disabled}"
							readonly="#{pc_Kac00701.propSsnHenkoKbn.readonly}"
							value="#{pc_Kac00701.propSsnHenkoKbn.stringValue}"
							style="#{pc_Kac00701.propSsnHenkoKbn.style}">
							<f:selectItems value="#{pc_Kac00701.propSsnHenkoKbn.list}" />
						</h:selectManyCheckbox>
						</TD>
					</TR>
					<TR>
						<TH class="v_d" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblSinseiRiyu"
								style="#{pc_Kac00701.propSinseiRiyu.labelStyle}"
								value="#{pc_Kac00701.propSinseiRiyu.labelName}">
							</h:outputText>
						</TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="10">
										<h:inputText styleClass="inputText" id="htmlSinseiRiyu"
									size="70" value="#{pc_Kac00701.propSinseiRiyu.stringValue}"
									style="#{pc_Kac00701.propSinseiRiyu.style}"
									maxlength="#{pc_Kac00701.propSinseiRiyu.maxLength}"
									disabled="#{pc_Kac00701.propSinseiRiyu.disabled}"
									readonly="#{pc_Kac00701.propSinseiRiyu.readonly}"
									rendered="#{pc_Kac00701.propSinseiRiyu.rendered}">
								</h:inputText>
									</TD>
									<TD>								
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlSinseiRiyuFindType"
											disabled="#{pc_Kac00701.propSinseiRiyuFindType.disabled}"
											readonly="#{pc_Kac00701.propSinseiRiyuFindType.readonly}"
											value="#{pc_Kac00701.propSinseiRiyuFindType.stringValue}">
											<f:selectItems value="#{pc_Kac00701.propSinseiRiyuFindType.list}" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_e" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblShutokusakiCd"
							value="#{pc_Kac00701.propShutokusakiCd.labelName}"
							style="#{pc_Kac00701.propShutokusakiCd.labelStyle}"></h:outputText>
						</TH>
						<TD>
							<h:inputText styleClass="inputText" id="htmlShutokusakiCd"
							size="20" value="#{pc_Kac00701.propShutokusakiCd.stringValue}"
							style="#{pc_Kac00701.propShutokusakiCd.style}"
							maxlength="#{pc_Kac00701.propShutokusakiCd.maxLength}"
							disabled="#{pc_Kac00701.propShutokusakiCd.disabled}"
							readonly="#{pc_Kac00701.propShutokusakiCd.readonly}"
							onblur="return func_8(this, event);"
							rendered="#{pc_Kac00701.propShutokusakiCd.rendered}">
						</h:inputText>&#160;&#160;(前方一致)
							<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="searchShutokusakiCd"
							action="#{pc_Kac00701.doSearchShutokusakiCdAction}"
							disabled="#{pc_Kac00701.propSearchShutokusakiCd.disabled}"
							style="#{pc_Kac00701.propSearchShutokusakiCd.style}">
						</hx:commandExButton>
							<h:outputText
								styleClass="outputText" id="htmlShutokusakiCdName">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblShutokusakiName"
							value="#{pc_Kac00701.propShutokusakiName.labelName}"
							style="#{pc_Kac00701.propShutokusakiName.labelStyle}">
						</h:outputText>
						</TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="10">
										<h:inputText styleClass="inputText"
											id="htmlShutokusakiName" size="60"
											value="#{pc_Kac00701.propShutokusakiName.stringValue}"
											style="#{pc_Kac00701.propShutokusakiName.style}"
											maxlength="#{pc_Kac00701.propShutokusakiName.maxLength}"
											disabled="#{pc_Kac00701.propShutokusakiName.disabled}"
											readonly="#{pc_Kac00701.propShutokusakiName.readonly}"
											rendered="#{pc_Kac00701.propShutokusakiName.rendered}">
										</h:inputText>
									</TD>
									<TD>
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlShutokusakiNameFindType"
											disabled="#{pc_Kac00701.propShutokusakiNameFindType.disabled}"
											readonly="#{pc_Kac00701.propShutokusakiNameFindType.readonly}"
											value="#{pc_Kac00701.propShutokusakiNameFindType.stringValue}">
											<f:selectItems
												value="#{pc_Kac00701.propShutokusakiNameFindType.list}" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_c" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblKeiyakuDate"
								value="#{pc_Kac00701.propKeiyakuDateFrom.labelName}"
								style="#{pc_Kac00701.propKeiyakuDateFrom.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText styleClass="inputText"
								id="htmlKeiyakuDateFrom" size="10"
								value="#{pc_Kac00701.propKeiyakuDateFrom.dateValue}"
								style="#{pc_Kac00701.propKeiyakuDateFrom.style}"
								disabled="#{pc_Kac00701.propKeiyakuDateFrom.disabled}"
								readonly="#{pc_Kac00701.propKeiyakuDateFrom.readonly}">
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<f:convertDateTime pattern="yyyy/MM/dd" />
								<hx:inputHelperDatePicker />
							</h:inputText>
							～
							<h:inputText styleClass="inputText"
								id="htmlKeiyakuDateTo" size="10"
								value="#{pc_Kac00701.propKeiyakuDateTo.dateValue}"
								style="#{pc_Kac00701.propKeiyakuDateTo.style}"
								disabled="#{pc_Kac00701.propKeiyakuDateTo.disabled}"
								readonly="#{pc_Kac00701.propKeiyakuDateTo.readonly}">
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<f:convertDateTime pattern="yyyy/MM/dd" />
								<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_d" width="189" colspan="2">
							<h:outputText styleClass="outputText" id="lblShutokuRingiNo"
							value="#{pc_Kac00701.propShutokuRingiNo.labelName}"
							style="#{pc_Kac00701.propShutokuRingiNo.labelStyle}">
						</h:outputText>
						</TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="10">
										<h:inputText styleClass="inputText" id="htmlShutokuRingiNo"
									size="40" value="#{pc_Kac00701.propShutokuRingiNo.stringValue}"
									style="#{pc_Kac00701.propShutokuRingiNo.style}"
									maxlength="#{pc_Kac00701.propShutokuRingiNo.maxLength}"
									disabled="#{pc_Kac00701.propShutokuRingiNo.disabled}"
									readonly="#{pc_Kac00701.propShutokuRingiNo.readonly}"
									rendered="#{pc_Kac00701.propShutokuRingiNo.rendered}">
								</h:inputText>
									</TD>
									<TD>
										<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlShutokuRingiNoFindType"
										disabled="#{pc_Kac00701.propShutokuRingiNoFindType.disabled}"
										readonly="#{pc_Kac00701.propShutokuRingiNoFindType.readonly}"
										value="#{pc_Kac00701.propShutokuRingiNoFindType.stringValue}">
										<f:selectItems
											value="#{pc_Kac00701.propShutokuRingiNoFindType.list}" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="189" colspan="2">
							<h:outputText styleClass="outputText"
							id="lblShutokuDenpyoKanriNo"
							style="#{pc_Kac00701.propShutokuDenpyoKanriNo.labelStyle}"
							value="#{pc_Kac00701.propShutokuDenpyoKanriNo.labelName}"></h:outputText>
							</TH>
						<TD width="609">
							<h:inputText styleClass="inputText"
								id="htmlShutokuDenpyoKanriNo" size="30"
								disabled="#{pc_Kac00701.propShutokuDenpyoKanriNo.disabled}"
								maxlength="#{pc_Kac00701.propShutokuDenpyoKanriNo.maxLength}"
								readonly="#{pc_Kac00701.propShutokuDenpyoKanriNo.readonly}"
								rendered="#{pc_Kac00701.propShutokuDenpyoKanriNo.rendered}"
								style="#{pc_Kac00701.propShutokuDenpyoKanriNo.style}"
								value="#{pc_Kac00701.propShutokuDenpyoKanriNo.stringValue}">
							</h:inputText>&#160;&#160;(前方一致)
						</TD>
					</TR>
					<TR>
						<TH class="v_f" width="189" colspan="2">
							<h:outputText styleClass="outputText"
								id="lblBiko" style="#{pc_Kac00701.propBiko.labelStyle}"
								value="#{pc_Kac00701.propBiko.labelName}"></h:outputText>
						</TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="10">
									<h:inputText styleClass="inputText" id="htmlBiko" size="70"
										disabled="#{pc_Kac00701.propBiko.disabled}"
										maxlength="#{pc_Kac00701.propBiko.maxLength}"
										readonly="#{pc_Kac00701.propBiko.readonly}"
										rendered="#{pc_Kac00701.propBiko.rendered}"
										style="#{pc_Kac00701.propBiko.style}"
										value="#{pc_Kac00701.propBiko.stringValue}">
									</h:inputText></TD>	
									<TD>	
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlBikoFindType"
											disabled="#{pc_Kac00701.propBikoFindType.disabled}"
											readonly="#{pc_Kac00701.propBikoFindType.readonly}"
											value="#{pc_Kac00701.propBikoFindType.stringValue}">
											<f:selectItems value="#{pc_Kac00701.propBikoFindType.list}" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="820" style="margin-top: 10px;" class="button_bar">
				<TR>
					<TD>
						<hx:commandExButton type="submit" value="検索"
						styleClass="commandExButton_dat" id="search"
						action="#{pc_Kac00701.doSearchAction}"
						disabled="#{pc_Kac00701.propSearch.disabled}">
					</hx:commandExButton>
						<hx:commandExButton type="submit" value="クリア"
						styleClass="commandExButton_etc" id="clear"
						action="#{pc_Kac00701.doClearAction}"
						disabled="#{pc_Kac00701.propClear.disabled}">
					</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kac00701.propHiddenSsnSyurui.stringValue}"
				id="htmlHiddenSsnSyurui">
			</h:inputHidden>
			<h:inputHidden value="htmlEdaNo=####0;####0 | htmlLeaseSsnNo=##0;##0 | htmlChotNendo=####;#### "
				id="htmlFormatNumberOption">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kac00701.propExecutableSearch.integerValue}" 
				id="htmlExecutableSearch">
			<f:convertNumber /></h:inputHidden>

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
</HTML>

