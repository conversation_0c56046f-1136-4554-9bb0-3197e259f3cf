<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<TITLE>Cos00501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
}

function confirmOk() {
// はい」を選択された場合、実行される処理
	document.getElementById('form1:propExecutableBtnRegister').value = "1";
	indirectClick('register');
	return;
}


function confirmCancel() {
//「いいえ」を選択された場合、実行される処理
	alert('実行を中断しました。');
	return;
}




function func_2(thisObj, thisEvent ,thisValue) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
	if (thisValue == '0'){
		document.getElementById('form1:htmlUpwdLabel3').style.color='#000000';
	}else{
		document.getElementById('form1:htmlUpwdLabel3').style.color='#FF0000';
	}

}</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cos00501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!--↓outer↓-->
			<DIV class="outer"><!--↓head↓--><!--↓head↓--><!-- ヘッダーインクルード --><jsp:include
				page="../../rev/inc/header.jsp">
				<hx:panelBox styleClass="panelBox" id="boxHeader"></hx:panelBox>
			</jsp:include><!-- ヘッダーへのデータセット領域 -->
			<DIV style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cos00501.doCloseDispAction}"></hx:commandExButton><h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cos00501.funcId}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cos00501.screenName}"></h:outputText></DIV>
			<CENTER>
			<TABLE border="0" width="950">
				<TBODY>
					<TR>
						<TD width="900" align="left">
						<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND><h:outputText
							id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
							styleClass="outputText" escape="false"
							style="color: red; font-size: 8pt; font-weight: bold">
						</h:outputText></FIELDSET>
						</TD>
						<TD width="50" align="right"></TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>
			<!--↑head↑--><!--↓content↓-->
			<DIV id="content">


			<DIV class="column">
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center" height="">
						<table width="100%" border="0" cellpadding="3" cellspacing="0">
							<TR>
								<TD>
								<TABLE border="0" width="950" class="" cellspacing="0">
									<TBODY>
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" id="htmlFsLabel1"
												value="[ファイルのセキュリティ]"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" style="" align="left" class="v_a"><h:outputText
															styleClass="outputText" id="htmlFsLabel2"
															value="#{pc_Cos00501.propPdfRadioPwd.labelName}"
															style="#{pc_Cos00501.propPdfRadioPwd.labelStyle}"
															title="#{pc_Cos00501.propPdfRadioOut.value}"></h:outputText></TH>
														<TD colspan="2" width="590" align="left"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlPdfRadioPwd"
															value="#{pc_Cos00501.propPdfRadioPwd.value}"
															style="#{pc_Cos00501.propPdfRadioPwd.style}">
															<f:selectItems
																value="#{pc_Cos00501.propPdfRadioPwd.list}" />
														</h:selectOneRadio></TD>
													</TR>
													<TR>
														<TH width="297" align="left" class="v_b"><h:outputText
															styleClass="outputText" id="htmlFsLabel3"
															value="#{pc_Cos00501.propCsvRadio.labelName}"
															style="#{pc_Cos00501.propCsvRadio.labelStyle}"
															title="#{pc_Cos00501.propCsvRadioOut.value}"></h:outputText></TH>
														<TD colspan="2" width="590" align="left"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlCsvRadio"
															value="#{pc_Cos00501.propCsvRadio.value}">
															<f:selectItems value="#{pc_Cos00501.propCsvRadio.list}" />
														</h:selectOneRadio></TD>
													</TR>
													<TR>
														<TH width="297" align="left" class="v_c"><h:outputText
															styleClass="outputText" id="htmlFsLabel4"
															value="#{pc_Cos00501.propTxtRadio.labelName}"
															style="#{pc_Cos00501.propTxtRadio.labelStyle}"
															title="#{pc_Cos00501.propTxtRadioOut.value}"></h:outputText></TH>
														<TD colspan="2" width="590" align="left"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlTxtRadio"
															value="#{pc_Cos00501.propTxtRadio.value}">
															<f:selectItems value="#{pc_Cos00501.propTxtRadio.list}" />
														</h:selectOneRadio></TD>
													</TR>
													<TR>
														<TH width="297" align="left" class="v_c"><h:outputText
															styleClass="outputText" id="htmlExcelLabel1"
															value="#{pc_Cos00501.propExcelRadio.labelName}"
															style="#{pc_Cos00501.propExcelRadio.labelStyle}"
															title="#{pc_Cos00501.propExcelRadioOut.value}"></h:outputText></TH>
														<TD colspan="2" width="590" align="left"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlExcelRadio"
															value="#{pc_Cos00501.propExcelRadio.value}">
															<f:selectItems value="#{pc_Cos00501.propExcelRadio.list}" />
														</h:selectOneRadio></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" id="htmlUsLabel1"
												value="[ユーザセッションに関する設定]"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" align="left" class="v_a"><h:outputText
															styleClass="outputText" id="htmlUsLabel2"
															value="#{pc_Cos00501.propSessionTo.labelName}"
															style="#{pc_Cos00501.propSessionTo.labelStyle}"
															title="#{pc_Cos00501.propSessionToOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:inputText
															styleClass="inputText" id="htmlSessionTo"
															value="#{pc_Cos00501.propSessionTo.integerValue}"
															maxlength="#{pc_Cos00501.propSessionTo.maxLength}"
															style="#{pc_Cos00501.propSessionTo.style}">
															<f:convertNumber type="number" pattern="###0"
																integerOnly="true" />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" value="[バッチ]" id="htmlBtLabel1"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" align="left" class="v_a"><h:outputText
															styleClass="outputText" id="htmlBtLabel2"
															value="#{pc_Cos00501.propBatJidoSy.labelName}"
															style="#{pc_Cos00501.propBatJidoSy.labelStyle}"
															title="#{pc_Cos00501.propBatJidoSyOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:inputText
															styleClass="inputText" id="htmlBatJidoSy"
															value="#{pc_Cos00501.propBatJidoSy.integerValue}"
															maxlength="#{pc_Cos00501.propBatJidoSy.maxLength}"
															style="#{pc_Cos00501.propBatJidoSy.style}">
															<f:convertNumber integerOnly="true" type="number"
																pattern="###0" />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" id="htmlUpwdLabel1"
												value="[ユーザパスワード]"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" align="left" class="v_a"><h:outputText
															styleClass="outputText" id="htmlUpwdLabel2"
															value="#{pc_Cos00501.propUsPwdRadio.labelName}"
															style="#{pc_Cos00501.propUsPwdRadio.labelStyle}"
															title="#{pc_Cos00501.propUsPwdRadioOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlSpwdRadio"
															value="#{pc_Cos00501.propUsPwdRadio.value}"
															onclick="return func_2(this, event ,value);">
															<f:selectItems value="#{pc_Cos00501.propUsPwdRadio.list}" />
														</h:selectOneRadio></TD>
													</TR>
													<TR>
														<TH align="left" class="v_b"><h:outputText
															styleClass="outputText" id="htmlUpwdLabel3"
															value="#{pc_Cos00501.propPPwdZidoSmoji.labelName}"
															style="#{pc_Cos00501.propPPwdZidoSmoji.labelStyle}"
															title="#{pc_Cos00501.propPPwdZidoSmojiOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:inputText
															styleClass="inputText" id="htmlPwdZidoSmoji"
															value="#{pc_Cos00501.propPPwdZidoSmoji.integerValue}"
															maxlength="#{pc_Cos00501.propPPwdZidoSmoji.maxLength}"
															style="#{pc_Cos00501.propPPwdZidoSmoji.style}">
															<f:convertNumber integerOnly="true" type="number"
																pattern="#0" />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
														</h:inputText></TD>
													</TR>
													<TR>
														<TH align="left" class="v_c"><h:outputText
															styleClass="outputText" id="htmlUpwdLabel4"
															value="#{pc_Cos00501.propPwdMin.labelName}"
															style="#{pc_Cos00501.propPwdMin.labelStyle}"
															title="#{pc_Cos00501.propPwdMinOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:inputText
															styleClass="inputText" id="htmlPwdMin"
															value="#{pc_Cos00501.propPwdMin.integerValue}"
															maxlength="#{pc_Cos00501.propPwdMin.maxLength}"
															style="#{pc_Cos00501.propPwdMin.style}">
															<f:convertNumber integerOnly="true" type="number"
																pattern="#0" />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
														</h:inputText></TD>
													</TR>
													<TR>
														<TH align="left" class="v_d"><h:outputText
															styleClass="outputText" id="htmlUpwdLabel5"
															value="#{pc_Cos00501.propPwdMax1.labelName}"
															style="#{pc_Cos00501.propPwdMax1.labelStyle}"
															title="#{pc_Cos00501.propPwdMax1Out.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:inputText
															styleClass="inputText" id="htmlPwdMax"
															value="#{pc_Cos00501.propPwdMax1.integerValue}"
															maxlength="#{pc_Cos00501.propSessionTo.maxLength}"
															style="#{pc_Cos00501.propPwdMax1.style}">
															<f:convertNumber integerOnly="true" type="number"
																pattern="#0" />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
														</h:inputText></TD>
													</TR>
													<TR>
														<TH align="left" class="v_e" height="8"><h:outputText
															styleClass="outputText" id="htmlUpwdLabel6"
															value="#{pc_Cos00501.propPwdRrRadio.labelName}"
															style="#{pc_Cos00501.propPwdRrRadio.labelStyle}"
															title="#{pc_Cos00501.propPwdRrRadioOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590" height="8"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlPwdRegDRadio"
															value="#{pc_Cos00501.propPwdRrRadio.value}">
															<f:selectItems value="#{pc_Cos00501.propPwdRrRadio.list}" />
														</h:selectOneRadio></TD>
													</TR>
													<TR>
														<TH align="left" class="v_f" height="33"><h:outputText
															styleClass="outputText" id="htmlUpwdLabel7"
															value="#{pc_Cos00501.propPwdkyoka.labelName}"
															style="#{pc_Cos00501.propPwdkyoka.labelStyle}"
															title="#{pc_Cos00501.propPwdkyokaOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590" height="33"><h:inputTextarea
															styleClass="inputTextarea" id="htmlPwdkyoka" cols="70"
															value="#{pc_Cos00501.propPwdkyoka.stringValue}"
															style="#{pc_Cos00501.propPwdkyoka.style}"></h:inputTextarea></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" id="htmlLogLabel1" value="[ログ出力]"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" align="left" class="v_a"><h:outputText
															styleClass="outputText" id="htmlLogLabel2"
															value="#{pc_Cos00501.propLog4jset1Radio.labelName}"
															style="#{pc_Cos00501.propLog4jset1Radio.labelStyle}"
															title="#{pc_Cos00501.propLog4jset1RadioOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlLog4jSqlLogRadio"
															value="#{pc_Cos00501.propLog4jset1Radio.value}">
															<f:selectItems
																value="#{pc_Cos00501.propLog4jset1Radio.list}" />
														</h:selectOneRadio></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" id="htmlMyHelpLabel" value="[マイヘルプに関する設定]"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" align="left" class="v_a">
															<span align="right">
																<h:outputText
																	styleClass="outputText" id="htmlMyHelpHeightLabel"
																	value="#{pc_Cos00501.propMyHelpHeight.labelName}"
																	style="#{pc_Cos00501.propMyHelpHeight.labelStyle}"
																	title="#{pc_Cos00501.propMyHelpHeightOut.value}">
																</h:outputText>
														</TH>
														<TD colspan="2" align="left" width="590">
															<h:inputText
																styleClass="inputText" id="htmlMyHelpHeight" size ="4"
																value="#{pc_Cos00501.propMyHelpHeight.integerValue}"
																maxlength="#{pc_Cos00501.propMyHelpHeight.maxLength}"
																style="#{pc_Cos00501.propMyHelpHeight.style}">
																<f:convertNumber type="number" pattern="###0" integerOnly="true" />
																<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
															</h:inputText>
														</TD>
													</TR>
													<TR>
														<TH width="300" align="left" class="v_a">
															<span align="right">
																<h:outputText
																	styleClass="outputText" id="htmlMyHelpWidthLabel"
																	value="#{pc_Cos00501.propMyHelpWidth.labelName}"
																	style="#{pc_Cos00501.propMyHelpWidth.labelStyle}"
																	title="#{pc_Cos00501.propMyHelpWidthOut.value}">
																</h:outputText>
														</TH>
														<TD colspan="2" align="left" width="590">
															<h:inputText
																styleClass="inputText" id="htmlWidth" size ="4"
																value="#{pc_Cos00501.propMyHelpWidth.integerValue}"
																maxlength="#{pc_Cos00501.propMyHelpWidth.maxLength}"
																style="#{pc_Cos00501.propMyHelpWidth.style}">
																<f:convertNumber type="number" pattern="###0" integerOnly="true" />
																<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
															</h:inputText>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" id="htmlWasLogRootLabel1"
												value="[パス]"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" align="left" class="v_a"><h:outputText
															styleClass="outputText" id="htmlWasLogRootLabel2"
															value="#{pc_Cos00501.propWasLogRoot.labelName}"
															style="#{pc_Cos00501.propWasLogRoot.labelStyle}"
															title="#{pc_Cos00501.propWasLogRootOut.value}"></h:outputText></TH>
														<TD colspan="2" align="left" width="590"><h:inputText
															styleClass="inputText" id="htmlWasLogRoot" size="90"
															value="#{pc_Cos00501.propWasLogRoot.stringValue}"
															style="#{pc_Cos00501.propWasLogRoot.style}">
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										
										
										
										
										
										<TR>
											<TD width="310" colspan="2" align="left"><h:outputText
												styleClass="outputText" id="htmlExcelOutKbnLabel1"
												value="[ＥＸＣＥＬ出力の設定]"></h:outputText></TD>
										</TR>
										<TR>
											<TD width="310" colspan="2" align="center">
											<TABLE border="0" width="950" class="table">
												<TBODY>
													<TR>
														<TH width="300" style="" align="left" class="v_a"><h:outputText
															styleClass="outputText" id="htmlExcelZipKbnLabel"
															value="#{pc_Cos00501.propExcelZipKbn.labelName}"
															style="#{pc_Cos00501.propExcelZipKbn.labelStyle}"
															title="#{pc_Cos00501.propExcelZipKbnOut.value}"></h:outputText></TH>
														<TD colspan="2" width="590" align="left"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlExcelZipKbn"
															value="#{pc_Cos00501.propExcelZipKbn.value}"
															style="#{pc_Cos00501.propExcelZipKbn.style}">
															<f:selectItems
																value="#{pc_Cos00501.propExcelZipKbn.list}" />
														</h:selectOneRadio></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
										
										


										
										

													
													
													
										<TR>
											<TD width="310" colspan="2" align="center"></TD>
										</TR>
									</TBODY>
								</TABLE>
								</TD>
							</TR>
						</table>
						<BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<CENTER>
						<TABLE class="button_bar" width="950">
							<TR>
								<TD align="center"><hx:commandExButton type="submit" value="確定"
									styleClass="commandExButton_dat" id="update"
									action="#{pc_Cos00501.doUpdateAction}"
									confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton><hx:commandExButton
									type="submit" value="クリア" styleClass="commandExButton_etc"
									id="clear" action="#{pc_Cos00501.doClearAction}"></hx:commandExButton></TD>
							</TR>
						</TABLE>
						</CENTER>
						</TD>
					</TR>
					<TR>
						<TD><%-- ↑↑↑ コンテンツ ↑↑↑ --%></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--><!--↓foot↓--><jsp:include page="../inc/footer.jsp" />
			<!--↑foot↑--></DIV>
			<!--↑outer↑-->
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

