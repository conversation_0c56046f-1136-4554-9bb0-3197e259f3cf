<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos01203T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<TITLE>Cos01203T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlpropExecutableUpdate').value = "1";
	if (document.getElementById('form1:htmlpropExecutableMode').value == "update") {
		indirectClick('update');

	} else if (document.getElementById('form1:htmlpropExecutableMode').value == "delete") {
		indirectClick('delete');
	}
}
function confirmCancel() {
	document.getElementById('form1:htmlpropExecutableUpdate').value = "0";
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cos01203T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cos01203T02.commonBean.doCloseDispAction}"></hx:commandExButton></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<table border="0" cellpadding="0" cellspacing="0" width="98%">
				<tr>
					<td align="left">
					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
						id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false"
						style="color: red; font-size: 8pt; font-weight: bold">
					</h:outputText></FIELDSET>
					</td>
					<td align="right"><hx:commandExButton type="submit" value="戻る"
						id="returnDisp" styleClass="commandExButton"
						action="#{pc_Cos01203T02.commonBean.doReturnDispAction}" immediate="true">
					</hx:commandExButton></td>
				</tr>
			</table>

			<!--↑head↑--> <!--↓content↓-->
			<DIV id="content">

			<DIV class="column" align="left">

			<TABLE width="800">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%">
							<TBODY>
								<TR>
									<TD align="center">
									<TABLE class="table" width="400">
										<TBODY>
											<TR>
												<TH class="v_a" nowrap width="160"><h:outputText
													styleClass="outputText" id="labelTabAppId"
													value="#{pc_Cos01203T02.commonBean.propTabAppId.labelName}"
													style="#{pc_Cos01203T02.commonBean.propTabAppId.labelStyle}"></h:outputText></TH>
												<TD class="table" width="200" nowrap><h:inputText
													styleClass="inputText" id="htmlTabAppId"
													value="#{pc_Cos01203T02.commonBean.propTabAppId.stringValue}"
													style="#{pc_Cos01203T02.commonBean.propTabAppId.style}"
													size="30"
													disabled="#{pc_Cos01203T02.commonBean.propTabAppId.disabled}"
													maxlength="#{pc_Cos01203T02.commonBean.propTabAppId.max}"></h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<%-- タブ --%>
			<HR noshade class="hr" align="center" width="100%">
			<table border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD nowrap="nowrap" align="left"><hx:commandExButton type="submit"
							action="#{pc_Cos01203T02.commonBean.doHtmlLinkAppAction}"
							value="#{pc_Cos01203T02.commonBean.propAppTabBtn.name}"
							disabled="#{pc_Cos01203T02.commonBean.propAppTabBtn.disabled}"
							rendered="#{pc_Cos01203T02.commonBean.propAppTabBtn.rendered}"
							styleClass="#{pc_Cos01203T02.commonBean.propAppTabBtn.style}"
							id="htmlAppTabBtn" style="width: 20%;"></hx:commandExButton><hx:commandExButton
							type="submit" action="#{pc_Cos01203T02.commonBean.doHtmlLinkScrnAction}"
							value="#{pc_Cos01203T02.commonBean.propScrnTabBtn.name}"
							disabled="#{pc_Cos01203T02.commonBean.propScrnTabBtn.disabled}"
							rendered="#{pc_Cos01203T02.commonBean.propScrnTabBtn.rendered}"
							styleClass="#{pc_Cos01203T02.commonBean.propScrnTabBtn.style}"
							id="htmlScrnTabBtn" style="width: 20%;"></hx:commandExButton><hx:commandExButton
							type="submit" action="#{pc_Cos01203T02.commonBean.doHtmlLinkPdfAction}"
							value="#{pc_Cos01203T02.commonBean.propPdfTabBtn.name}"
							disabled="#{pc_Cos01203T02.commonBean.propPdfTabBtn.disabled}"
							rendered="#{pc_Cos01203T02.commonBean.propPdfTabBtn.rendered}"
							styleClass="#{pc_Cos01203T02.commonBean.propPdfTabBtn.style}"
							id="htmlPdfTabBtn" style="width: 20%;"></hx:commandExButton></TD>
					<TR>
						<TD>
						<table width="100%" class="tab_body" border="0" cellpadding="0"
							cellspacing="0">
							<tr>
								<td><%-- ↑タブ --%>
								<CENTER>
								<TABLE width="940">
									<TBODY>
										<TR>
											<TD align="center" nowrap>
											<TABLE border="0" cellpadding="0" cellspacing="0" width="700"
												style="margin-top: 10px;">
												<TBODY>
													<TR>
														<TD align="right"><h:outputText styleClass="outputText"
															id="htmlListCnt"
															value="#{pc_Cos01203T02.propScrnList.listCount}">
														</h:outputText>&nbsp;件</TD>
													</TR>
													<TR>
														<TD nowrap>
														<DIV style="height:100pt" id="listScroll"
															class="listScroll"><h:dataTable border="0"
															cellpadding="2" cellspacing="0"
															columnClasses="columnClass1" headerClass="headerClass"
															styleClass="meisai_scroll" footerClass="footerClass"
															rowClasses="#{pc_Cos01203T02.propScrnList.rowClasses}"
															styleClass="meisai_scroll" id="htmlScrnList"
															value="#{pc_Cos01203T02.propScrnList.list}"
															first="#{pc_Cos01203T02.propScrnList.first}"
															rows="#{pc_Cos01203T02.propScrnList.rows}" var="varlist">
															<h:column id="column1">
																<f:facet name="header">
																	<h:outputText styleClass="outputText"
																		id="labelListScrnId"
																		style="#{pc_Cos01203T02.propListScrnId.labelStyle}"
																		value="#{pc_Cos01203T02.propListScrnId.labelName}"></h:outputText>
																</f:facet>
																<f:attribute value="110" name="width" />
																<f:attribute value="true" name="nowrap" />
																<h:outputText styleClass="outputText"
																	id="htmlListScrnId" title="#{varlist.scrnIdOut.value}"
																	value="#{varlist.scrnIdOut.displayValue}">
																</h:outputText>
															</h:column>
															<h:column id="column2">
																<f:facet name="header">
																	<h:outputText styleClass="outputText"
																		value="#{pc_Cos01203T02.propListScrnName.labelName}"
																		id="labelListScrnName"
																		style="#{pc_Cos01203T02.propListScrnName.labelStyle}"></h:outputText>
																</f:facet>
																<h:outputText styleClass="outputText"
																	id="htmlListScrnName"
																	title="#{varlist.scrnNameOut.value}"
																	value="#{varlist.scrnNameOut.displayValue}">
																</h:outputText>
																<f:attribute value="350" name="width" />
																<f:attribute value="true" name="nowrap" />
															</h:column>
															<h:column id="column10">
																<f:facet name="header">
																	<h:outputText styleClass="outputText"
																		value="#{pc_Cos01203T02.propListScrnNameDef.labelName}"
																		id="labelListScrnNameDef"
																		style="#{pc_Cos01203T02.propListScrnNameDef.labelStyle}"></h:outputText>
																</f:facet>
																<f:attribute value="350" name="width" />
																<f:attribute value="true" name="nowrap" />
																<h:outputText styleClass="outputText"
																	id="htmlListscrnNameDef"
																	title="#{varlist.scrnNameDefOut.value}"
																	value="#{varlist.scrnNameDefOut.displayValue}">
																</h:outputText>
															</h:column>
															<h:column id="column3">
																<f:facet name="header">
																</f:facet>
																<hx:commandExButton type="submit" value="選択"
																	styleClass="commandExButton" id="select"
																	action="#{pc_Cos01203T02.doSelectAction}"></hx:commandExButton>
																<f:attribute value="40" name="width" />
																<f:attribute value="true" name="nowrap" />
															</h:column>
														</h:dataTable></DIV>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								<HR noshade class="hr" align="center" width="100%">
								<TABLE width="940">
									<TBODY>
										<TR>
											<TD align="center" nowrap>
											<TABLE width="800">
												<TBODY>
													<TR>
														<TD align="center" nowrap>
														<TABLE class="table">
															<TBODY>
																<TR>
																	<TD class="group_label_top" nowrap colspan="2"><h:outputText
																		styleClass="outputText" id="labelScrnId"
																		style="#{pc_Cos01203T02.propListScrnId.labelStyle}"
																		value="#{pc_Cos01203T02.propListScrnId.labelName}">
																	</h:outputText></TD>
																	<TD class="group_item" nowrap></TD>
																</TR>
																<TR>
																	<TD class="group_label" width="20" nowrap></TD>
																	<TH nowrap width="140" class="v_a" ><h:outputText
																		styleClass="outputText" id="labelAppId"
																		style="#{pc_Cos01203T02.propAppId.labelStyle}"
																		value="#{pc_Cos01203T02.propAppId.labelName}">
																	</h:outputText></TH>
																	<TD nowrap width="100"><h:outputText
																		styleClass="outputText" id="htmlAppId"
																		style="#{pc_Cos01203T02.propAppId.style}"
																		value="#{pc_Cos01203T02.propAppId.stringValue}">
																	</h:outputText></TD>
																</TR>
																<TR>
																	<TD class="group_label_bottom" nowrap></TD>
																	<TH class="v_b" nowrap><h:outputText styleClass="outputText"
																		id="text2"
																		value="#{pc_Cos01203T02.propScrnNo.labelName}"
																		style="#{pc_Cos01203T02.propScrnNo.labelStyle}">
																	</h:outputText></TH>
																	<TD width="400" nowrap><h:inputText
																		styleClass="inputText" id="htmlScrnNo"
																		value="#{pc_Cos01203T02.propScrnNo.integerValue}"
																		style="#{pc_Cos01203T02.propScrnNo.style}" size="3"
																		disabled="#{pc_Cos01203T02.propScrnNo.disabled}"
																		maxlength="#{pc_Cos01203T02.propScrnNo.maxLength}">
																		<f:convertNumber integerOnly="true" type="number"
																			pattern="#0" />
																		<hx:inputHelperAssist errorClass="inputText_Error"
																			promptCharacter="" />
																	</h:inputText></TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
													<TR>
														<TD nowrap></TD>
													</TR>
													<TR>
														<TD align="center" nowrap>
														<TABLE class="table">
															<TBODY>
																<TR>
																	<TH class="v_b" nowrap width="160"><h:outputText
																		styleClass="outputText" id="labelScrnName"
																		style="#{pc_Cos01203T02.propScrnName.labelStyle}"
																		value="#{pc_Cos01203T02.propScrnName.labelName}">
																	</h:outputText></TH>
																	<TD nowrap width="400"><h:inputText
																		styleClass="inputText" id="htmlScrnNameInput"
																		size="50" style="#{pc_Cos01203T02.propScrnName.style}"
																		value="#{pc_Cos01203T02.propScrnName.stringValue}"
																		disabled="#{pc_Cos01203T02.propScrnName.disabled}"
																		maxlength="#{pc_Cos01203T02.propScrnName.max}">
																	</h:inputText></TD>
																</TR>
																<TR>
																	<TH class="v_c" nowrap><h:outputText
																		styleClass="outputText" id="labelScrnNameDef"
																		style="#{pc_Cos01203T02.propScrnNameDef.labelStyle}"
																		value="#{pc_Cos01203T02.propScrnNameDef.labelName}">
																	</h:outputText></TH>
																	<TD nowrap><h:outputText styleClass="outputText"
																		id="htmlScrnNameDef"
																		value="#{pc_Cos01203T02.propScrnNameDef.stringValue}"
																		style="#{pc_Cos01203T02.propScrnNameDef.style}"></h:outputText></TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								<TABLE class="button_bar" width="100%">
									<TBODY>
										<TR class="bg">
											<TD align="center" class="button_bar" nowrap><hx:commandExButton
												type="submit" value="確定" styleClass="commandExButton_dat"
												id="update" action="#{pc_Cos01203T02.doUpdateAction}"
												disabled="#{pc_Cos01203T02.propUpdateBtn.disabled}"></hx:commandExButton>&nbsp;<hx:commandExButton
												type="submit" value="削除" styleClass="commandExButton_dat"
												id="delete" action="#{pc_Cos01203T02.doDeleteAction}"
												disabled="#{pc_Cos01203T02.propDeleteBtn.disabled}"></hx:commandExButton>&nbsp;<hx:commandExButton
												type="submit" value="クリア" id="clear"
												styleClass="commandExButton_etc"
												action="#{pc_Cos01203T02.doClearAction}"></hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								</CENTER>
								</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<h:inputHidden
				value="#{pc_Cos01203T02.propExecutableUpdate.integerValue}"
				id="htmlpropExecutableUpdate">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Cos01203T02.propExecutableMode.stringValue}"
				id="htmlpropExecutableMode"></h:inputHidden>
			<!--↑content↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<!--↑outer↑-->
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

