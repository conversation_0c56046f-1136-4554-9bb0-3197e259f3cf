<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb41001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>月次締め</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css" >

<SCRIPT type="text/javascript">

function confirmOk() {
	var executalbe = document.getElementById('form1:htmlExecutable').value;
	if (executalbe == '1') {
		indirectClick('getsuShukei');
	} else if (executalbe == '2') {
		indirectClick('getsuShime');
	} else if (executalbe == '3') {
		indirectClick('shimeKaijyo');
	}
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutable').value = "0";
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb41001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる"
					styleClass="commandExButton" 
					id="closeDisp"
					action="#{pc_Keb41001.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb41001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb41001.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE width="900" 
		   border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   class="table">
		<TBODY>
			<TR>
				<TH class="v_a" width="160">
					<h:outputText styleClass="outputText" 
								  id="lblKaikeiNendo" 
								  style="#{pc_Keb41001.propKaikeiNendo.labelStyle}" 
								  value="#{pc_Keb41001.propKaikeiNendo.labelName}">
					</h:outputText>
				</TH>
				<TD width="740">
					<h:inputText styleClass="inputText" 
								 id="htmlKaikeiNendo" 
								 maxlength="#{pc_Keb41001.propKaikeiNendo.maxLength}" 
								 value="#{pc_Keb41001.propKaikeiNendo.dateValue}" 
								 style="#{pc_Keb41001.propKaikeiNendo.style}" 
								 size="4">
						<f:convertDateTime pattern="yyyy" />
						<hx:inputHelperAssist errorClass="inputText_Error" 
											  promptCharacter="_" />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_a" width="">
					<h:outputText styleClass="outputText" 
								  id="lblKaikeiTsuki" 
								  style="#{pc_Keb41001.propKaikeiTsuki.labelStyle}" 
								  value="#{pc_Keb41001.propKaikeiTsuki.labelName}" 
								  rendered="#{pc_Keb41001.propKaikeiTsuki.rendered}">
					</h:outputText>
				</TH>
				<TD>
					<h:inputText styleClass="inputText" 
								 id="htmlKaikeiTsuki" 
								 size="2" 
								 value="#{pc_Keb41001.propKaikeiTsuki.dateValue}" 
								 style="#{pc_Keb41001.propKaikeiTsuki.style}" 
								 disabled="#{pc_Keb41001.propKaikeiTsuki.disabled}" 
								 readonly="#{pc_Keb41001.propKaikeiTsuki.readonly}" 
								 rendered="#{pc_Keb41001.propKaikeiTsuki.rendered}">
						<f:convertDateTime pattern="MM" />
						<hx:inputHelperAssist errorClass="inputText_Error" 
											  promptCharacter="_" />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_a" width="">
					<h:outputText id="lblShimeKbn" 
								  styleClass="outputText" 
								  style="#{pc_Keb41001.propShimeKbn.labelStyle}" 
								  value="#{pc_Keb41001.propShimeKbn.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:selectOneMenu styleClass="selectOneMenu" 
									 id="htmlShimeKbn" 
									 value="#{pc_Keb41001.propShimeKbn.value}" 
									 style="#{pc_Keb41001.propShimeKbn.style}" 
									 disabled="#{pc_Keb41001.propShimeKbn.disabled}" 
									 readonly="#{pc_Keb41001.propShimeKbn.readonly}" 
									 rendered="#{pc_Keb41001.propShimeKbn.rendered}">
						<f:selectItems value="#{pc_Keb41001.propShimeKbn.list}" />
					</h:selectOneMenu>
				</TD>
			</TR>
			<TR>
				<TH class="v_f" width="">
					<h:outputText styleClass="outputText" 
								  id="lblKaikeiTaniBarance" 
								  style="#{pc_Keb41001.propKaikeiTaniBarance.labelStyle}" 
								  value="#{pc_Keb41001.propKaikeiTaniBarance.name}">
					</h:outputText>
				</TH>
				<TD>
					<TABLE border="" class="clear_border">
						<TBODY>
							<TR>
								<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
													  styleClass="selectOneRadio" 
													  id="htmlKaikeiTaniBarance"
													  disabled="#{pc_Keb41001.propKaikeiTaniBarance.disabled}"
													  value="#{pc_Keb41001.propKaikeiTaniBarance.value}"
													  readonly="#{pc_Keb41001.propKaikeiTaniBarance.readonly}">
										<f:selectItem itemValue="1" itemLabel="締月のみチェック" />
										<f:selectItem itemValue="2" itemLabel="４月からチェック" />
										<f:selectItem itemValue="3" itemLabel="チェックしない" />
									</h:selectOneRadio></TD>									
								<TD>	
									<h:outputText styleClass="outputText" value="　（月次締めの場合のみ有効）" />
								</TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
			<TR>
				<TH class="v_f" width="">
					<h:outputText styleClass="outputText" 
								  id="lblExistSikakeDenpyo" 
								  style="#{pc_Keb41001.propExistSikakeDenpyo.labelStyle}" 
								  value="#{pc_Keb41001.propExistSikakeDenpyo.name}">
					</h:outputText>
				</TH>
				<TD>
					<TABLE border="" class="clear_border">
						<TBODY>
							<TR>
								<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
													  styleClass="selectOneRadio" 
													  id="htmlExistSikakeDenpyo"
													  value="#{pc_Keb41001.propExistSikakeDenpyo.value}">
										<f:selectItem itemValue="1" itemLabel="エラーとする" />
										<f:selectItem itemValue="2" itemLabel="エラーとしない" />
									</h:selectOneRadio></TD>
								<TD>	
									<h:outputText styleClass="outputText" value="　（月次集計の場合のみ有効）" />
								</TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
			<TR>
				<TH class="v_f" width="">
					<h:outputText styleClass="outputText" 
								  id="lblKarikihyoCheck" 
								  style="#{pc_Keb41001.propKarikihyoCheck.labelStyle}" 
								  value="#{pc_Keb41001.propKarikihyoCheck.name}">
					</h:outputText>
				</TH>
				<TD>
					<TABLE border="" class="clear_border">
						<TBODY>
							<TR>
								<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
													  styleClass="selectOneRadio" 
													  id="htmlKarikihyoCheck"
													  value="#{pc_Keb41001.propKarikihyoCheck.stringValue}">
										<f:selectItem itemValue="1" itemLabel="エラーとする" />
										<f:selectItem itemValue="2" itemLabel="エラーとしない" />
									</h:selectOneRadio>
								<TD>	
									<h:outputText styleClass="outputText" value="　（月次集計で仕掛伝票をエラーとする場合、および月次締めの場合に有効）" />
								</TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style="margin-top:10px" 
		   class="button_bar" 
		   width="900">
		<TBODY>
			<TR>
				<TD align="center" style="margin-top:10px;">
					<hx:commandExButton type="submit" 
										value="月次集計" 
										styleClass="commandExButton_out" 
										id="getsuShukei" 
										rendered="#{pc_Keb41001.propGetsuShukei.rendered}" 
										disabled="#{pc_Keb41001.propGetsuShukei.disabled}" 
										style="#{pc_Keb41001.propGetsuShukei.style}" 
										action="#{pc_Keb41001.doGetsuShukeiAction}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="月次締め" 
										styleClass="commandExButton_out" 
										id="getsuShime" 
										rendered="#{pc_Keb41001.propGetsuShime.rendered}" 
										disabled="#{pc_Keb41001.propGetsuShime.disabled}" 
										style="#{pc_Keb41001.propGetsuShime.style}" 
										action="#{pc_Keb41001.doGetsuShimeAction}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="締め解除" 
										styleClass="commandExButton_out" 
										id="shimeKaijyo" 
										rendered="#{pc_Keb41001.propShimeKaijyo.rendered}" 
										disabled="#{pc_Keb41001.propShimeKaijyo.disabled}" 
										style="#{pc_Keb41001.propShimeKaijyo.style}" 
										action="#{pc_Keb41001.doShimeKaijyoAction}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   width="" 
		   class="900">
		<TBODY>
			<TR>
				<TD width="450" align="left">
					<h:outputText styleClass="outputText" 
								  id="lblUpdateRireki" 
								  value="【更新履歴】">
					</h:outputText>
				</TD>
				<TD width="450" align="right">
					<h:outputText styleClass="outputText" 
								  id="htmlListCount" 
								  value="#{pc_Keb41001.propUpdateRirekiList.listCount}件" 
								  style="font-size: 8pt">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TD width="900" colspan="2">
					<DIV id="listScroll" 
						 onscroll="setScrollPosition('scroll',this);" 
						 class="listScroll" 
						 style="height:340px">
						<h:dataTable border="0" 
									 cellpadding="2" 
									 cellspacing="0" 
									 headerClass="headerClass" 
									 footerClass="footerClass" 
									 rowClasses="#{pc_Keb41001.propUpdateRirekiList.rowClasses}" 
									 styleClass="meisai_scroll" 
									 id="htmlUpdateRirekiList" 
									 width="880" 
									 value="#{pc_Keb41001.propUpdateRirekiList.list}" 
									 var="varlist" 
									 style="#{pc_Keb41001.propUpdateRirekiList.style}; table-layout: fixed;" 
									 first="#{pc_Keb41001.propUpdateRirekiList.first}" 
									 rows="#{pc_Keb41001.propUpdateRirekiList.rows}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblListSyoriNengetsu" 
												  styleClass="outputText" 
												  value="会計年月">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  id="htmlSyoriNengetsu" 
											  value="#{varlist.syoriNengetsu}">
								</h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle" name="style" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblListSyoriKbn" 
												  styleClass="outputText" 
												  value="処理区分">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  id="htmlListSyoriKbn" 
											  value="#{varlist.syoriKbn}">
								</h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle" name="style" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblListShimeKbn" 
												  styleClass="outputText" 
												  value="締め区分">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  id="htmlListShimeKbn" 
											  style="white-space:nowrap;" 
											  value="#{varlist.shimeKbn}" 
											  title="#{varlist.shimeKbn}">
								</h:outputText>
								<f:attribute value="250" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblListSyoriDate" 
												  styleClass="outputText" 
												  value="処理日時">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  id="htmlListSyoriDate" 
											  value="#{varlist.syoriDate}">
								</h:outputText>
								<f:attribute value="130" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle" name="style" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblListJissiUser" 
												  styleClass="outputText" 
												  value="実施者" 
												  style="text-align: center">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  id="htmlListJissiUser" 
											  style="white-space:nowrap;" 
											  value="#{varlist.jissiUser}" 
											  title="#{varlist.jissiUser}">
								</h:outputText>
								<f:attribute value="" name="width" />
							</h:column>
						</h:dataTable>
					</DIV>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden value="#{pc_Keb41001.propExecutable.integerValue}" 
			   id="htmlExecutable">
</h:inputHidden>
<h:inputHidden value="#{pc_Keb41001.propUpdateRirekiList.scrollPosition}" 
			   id="scroll">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>
