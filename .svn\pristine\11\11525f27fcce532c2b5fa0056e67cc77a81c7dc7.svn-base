<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaz00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>地目登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY>
<hx:scriptCollector id="scriptCollector1"
	preRender="#{pc_Kaz00401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
	<hx:commandExButton type="submit"
		value="閉じる"
		styleClass="commandExButton"
		id="closeDisp"
		action="#{pc_Kaz00401.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText
		styleClass="outputText" id="htmlFuncId"
		value="#{pc_Kaz00401.funcId}">
	</h:outputText>
	<h:outputText
		styleClass="outputText" id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}">
	</h:outputText>
	<h:outputText
		styleClass="outputText" id="htmlScrnName"
		value="#{pc_Kaz00401.screenName}">
	</h:outputText>
</div>
<!--↓outer↓-->

<DIV class="outer">
<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
	<h:outputText
		id="message"
		value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText>
</FIELDSET>
<!--↓content↓-->
<DIV class="head_button_area">
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、全角スペースを配置-->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" width="830">
		<TBODY>
			<TR>
				<TD align="right"><h:outputText 
					styleClass="outputText"
					id="htmlListCount" value="#{pc_Kaz00401.propCmk.listCount}"
					style="font-size: 8pt">
					</h:outputText><h:outputText
					styleClass="outputText" id="text1" value="件">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TD valign="top" height="120">
				<DIV class="listScroll" style="height:339px; overflow-y:scroll"
					id="listScroll" onscroll="setScrollPosition('scroll',this);">
				<h:dataTable
					var="varlist" value="#{pc_Kaz00401.propCmk.list}"
					rows="#{pc_Kaz00401.propCmk.rows}" cellpadding="2" width="810"
					rowClasses="#{pc_Kaz00401.propCmk.rowClasses}"
					first="#{pc_Kaz00401.propCmk.first}" cellspacing="0"
					headerClass="headerClass" footerClass="footerClass" id="table1"
					styleClass="meisai_scroll" columnClasses="columnClass1">
					<h:column id="column1">
						<f:facet name="header">
							<h:outputText id="lblListCmkCd" styleClass="outputText"
								value="#{pc_Kaz00401.propCmkCd.name}" style="text-align: center"></h:outputText>
						</f:facet>
						<h:outputText styleClass="outputText" id="htmlListCmkCd"
							value="#{varlist.cmkCd}"></h:outputText>
						<f:attribute value="180" name="width" />
						<f:attribute value="text-align: left" name="style" />
					</h:column>
					<h:column id="column2">
						<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListCmkName"
										value="#{pc_Kaz00401.propCmkName.name}"
										style="text-align: center"></h:outputText>
								</f:facet>
						<hx:jspPanel id="jspPanel1">
							<DIV style="width:512px;white-space:nowrap;overflow:hidden;display:block;">
							<h:outputText styleClass="outputText" id="htmlListCmkName"
										value="#{varlist.cmkName.stringValue}"
										title="#{varlist.cmkNameRyak.stringValue}"
										style="white-space:nowrap;"></h:outputText>
							</DIV>
						</hx:jspPanel>
						<f:attribute value="512" name="width" />
						<f:attribute value="text-align: left" name="style" />
						<f:attribute value="true" name="nowrap" />
					</h:column>
					<h:column id="column3">
						<f:facet name="header">
							<h:outputText styleClass="outputText" value="状況"
								id="lblListYukoMukoFlg" style="text-align: center"></h:outputText>
						</f:facet>
						<h:outputText styleClass="outputText" id="htmlListYukoMukoFlg"
							value="#{varlist.yukoMuko.stringValue}"
                            title="#{varlist.yukoMuko.stringValue}"
							style="white-space:nowrap;"></h:outputText>
						<f:attribute value="80" name="width" />
						<f:attribute value="text-align: left; vertical-align: middle"
							name="style" />
					</h:column>
					<h:column id="column4">
						<hx:commandExButton type="submit" styleClass="commandExButton"
							id="select" action="#{pc_Kaz00401.doSelectAction}" value="選択"
							disabled="#{pc_Kaz00401.propSelect.disabled}"
							rendered="#{pc_Kaz00401.propSelect.rendered}"
							style="#{pc_Kaz00401.propSelect.style};width:38px;"></hx:commandExButton>
						<f:facet name="header">
						</f:facet>
						<f:attribute value="38" name="width" />
						<f:attribute value="text-align: center; vertical-align: middle"
							name="style" />
					</h:column>
				</h:dataTable>
				</DIV>
			</TR>
		</TBODY>
	</TABLE>

	<CENTER></CENTER>
	<DIV align="left">
		<TABLE border="0" class="table" width="830" cellpadding="0"
			cellspacing="0" style="margin-left:55px;margin-top:10px">
			<TBODY>
				<TR>
					<TH class="v_a" align="left" width="20%"><h:outputText
						styleClass="outputText" id="lblCmkCd"
						value="#{pc_Kaz00401.propCmkCd.labelName}"
						style="#{pc_Kaz00401.propCmkCd.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="658"><h:inputText
						rendered="#{pc_Kaz00401.propCmkCd.rendered}" id="htmlCmkCd"
						readonly="#{pc_Kaz00401.propCmkCd.readonly}"
						style="#{pc_Kaz00401.propCmkCd.style}" size="2"
						value="#{pc_Kaz00401.propCmkCd.stringValue}"
						maxlength="#{pc_Kaz00401.propCmkCd.maxLength}"
						disabled="#{pc_Kaz00401.propCmkCd.disabled}"
						styleClass="inputText">
						<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</DIV>

	<TABLE border="0" class="table" width="830" cellpadding="0"
			cellspacing="0" style="margin-top:10px">
		<TBODY>
			<TR>
				<TH class="v_b" align="left" width="20%">
					<h:outputText
						styleClass="outputText" id="lblCmkName"
						value="#{pc_Kaz00401.propCmkName.labelName}"
						style="#{pc_Kaz00401.propCmkName.labelStyle}">
					</h:outputText>
				</TH>
				<TD width="439">
					<h:inputText
						rendered="#{pc_Kaz00401.propCmkName.rendered}"
						id="htmlCmkName"
						readonly="#{pc_Kaz00401.propCmkName.readonly}"
						style="#{pc_Kaz00401.propCmkName.style}" size="50"
						value="#{pc_Kaz00401.propCmkName.stringValue}"
						maxlength="#{pc_Kaz00401.propCmkName.maxLength}"
						disabled="#{pc_Kaz00401.propCmkName.disabled}" styleClass="inputText">
						<hx:inputHelperAssist
							imeMode="#{pc_Kaz00401.propCmkName.imeMode}"
							errorClass="inputText_Error" />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_c" align="left" width="20%">
					<h:outputText
						styleClass="outputText" id="lblCmkNameRyak"
						style="#{pc_Kaz00401.propCmkNameRyak.labelStyle}"
						value="#{pc_Kaz00401.propCmkNameRyak.labelName}">
					</h:outputText>
				</TH>
				<TD width="439">
					<h:inputText
						rendered="#{pc_Kaz00401.propCmkNameRyak.rendered}"
						id="htmlCmkNameRyak" styleClass="inputText"
						readonly="#{pc_Kaz00401.propCmkNameRyak.readonly}"
						style="#{pc_Kaz00401.propCmkNameRyak.style}" size="20"
						value="#{pc_Kaz00401.propCmkNameRyak.stringValue}"
						maxlength="#{pc_Kaz00401.propCmkNameRyak.maxLength}"
						disabled="#{pc_Kaz00401.propCmkNameRyak.disabled}">
						<hx:inputHelperAssist errorClass="inputText_Error" />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_d" align="left" width="20%">
					<h:outputText
						styleClass="outputText" id="lblYukoMukoFlg"
						style="#{pc_Kaz00401.propYukoMukoFlg.labelStyle}"
						value="#{pc_Kaz00401.propYukoMukoFlg.labelName}">
					</h:outputText>
				</TH>
				<TD width="439">
					<h:selectOneRadio
						rendered="#{pc_Kaz00401.propYukoMukoFlg.rendered}"
						id="htmlYukoMukoFlg"
						readonly="#{pc_Kaz00401.propYukoMukoFlg.readonly}"
						styleClass="selectOneRadio"
						style="#{pc_Kaz00401.propYukoMukoFlg.style}"
						disabledClass="selectOneRadio_Disabled"
						value="#{pc_Kaz00401.propYukoMukoFlg.stringValue}"
						disabled="#{pc_Kaz00401.propYukoMukoFlg.disabled}">
						<f:selectItem itemValue="1" itemLabel="有効" />
						<f:selectItem itemValue="0" itemLabel="無効" />
					</h:selectOneRadio>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<BR>
	<TABLE width="830" class="button_bar">
		<TR>
			<TD align="center" colspan="6">
				<hx:commandExButton
					type="submit"
					value="登録"
					styleClass="commandExButton_dat"
					id="register"
					confirm="#{msg.SY_MSG_0002W}"
					action="#{pc_Kaz00401.doRegisterAction}"
					disabled="#{pc_Kaz00401.propRegister.disabled}"
					rendered="#{pc_Kaz00401.propRegister.rendered}"
					style="#{pc_Kaz00401.propRegister.style}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="更新"
					styleClass="commandExButton_dat"
					id="update"
					confirm="#{msg.SY_MSG_0003W}"
					action="#{pc_Kaz00401.doUpdateAction}"
					disabled="#{pc_Kaz00401.propUpdate.disabled}"
					rendered="#{pc_Kaz00401.propUpdate.rendered}"
					style="#{pc_Kaz00401.propUpdate.style}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="削除"
					styleClass="commandExButton_dat"
					confirm="#{msg.SY_MSG_0004W}"
					id="delete"
					action="#{pc_Kaz00401.doDeleteAction}"
					disabled="#{pc_Kaz00401.propDelete.disabled}"
					rendered="#{pc_Kaz00401.propDelete.rendered}"
					style="#{pc_Kaz00401.propDelete.style}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="クリア"
					styleClass="commandExButton_etc"
					id="clear"
					action="#{pc_Kaz00401.doClearAction}"
					disabled="#{pc_Kaz00401.propClear.disabled}"
					rendered="#{pc_Kaz00401.propClear.rendered}"
					style="#{pc_Kaz00401.propClear.style}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="CSV作成"
					styleClass="commandExButton_out"
					id="csvout"
					action="#{pc_Kaz00401.doCsvoutAction}"
					confirm="#{msg.SY_MSG_0020W}"
					disabled="#{pc_Kaz00401.propCsvout.disabled}"
					rendered="#{pc_Kaz00401.propCsvout.rendered}"
					style="#{pc_Kaz00401.propCsvout.style}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="出力項目指定"
					styleClass="commandExButton_out"
					id="setoutput"
					action="#{pc_Kaz00401.doSetoutputAction}"
					disabled="#{pc_Kaz00401.propSetoutput.disabled}"
					rendered="#{pc_Kaz00401.propSetoutput.rendered}"
					style="#{pc_Kaz00401.propSetoutput.style}">
				</hx:commandExButton>
			</TD>
		</TR>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/footer.jsp" />
<h:inputHidden
	id="scroll"
	value="#{pc_Kaz00401.propCmk.scrollPosition}">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>
