<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaf00102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>優先順位指定(変更情報一覧)</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
	self.close();
	return true;

}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kaf00102.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kaf00102.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kaf00102.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kaf00102.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> &#160; <!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="580" style="margin-top: 20px;">
				<TBODY>
					<TR>
						<TH width="100" class="v_a">
						<CENTER>優先順位</CENTER>
						</TH>
						<TH width="280" class="v_b">
						<CENTER>項目</CENTER>
						</TH>
						<TH width="200" class="v_c">
						<CENTER>ソート順</CENTER>
						</TH>
					</TR>
					<TR style="${pc_Kaf00102.tr1Style}">
						<TD>
						<CENTER>1</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlFirstOut"
							value="#{pc_Kaf00102.propFirstOut.stringValue}"
							rendered="#{pc_Kaf00102.propFirstOut.rendered}">
							<f:selectItems value="#{pc_Kaf00102.propFirstOut.list}" />
						</h:selectOneMenu><h:outputText styleClass="outputText"
							id="htmlSsnKbn" value="資産区分"
							rendered="#{pc_Kaf00102.propSsnKbn.rendered}"></h:outputText></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFirstOutJijyo"
							value="#{pc_Kaf00102.propFirstOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr2Style}">
						<TD>
						<CENTER>2</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlSecondOut"
							value="#{pc_Kaf00102.propSecondOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propSecondOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSecondOutJijyo"
							value="#{pc_Kaf00102.propSecondOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr3Style}">
						<TD>
						<CENTER>3</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlThirdOut"
							value="#{pc_Kaf00102.propThirdOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propThirdOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlThirdOutJijyo"
							value="#{pc_Kaf00102.propThirdOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr4Style}">
						<TD>
						<CENTER>4</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlFourthOut"
							value="#{pc_Kaf00102.propFourthOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propFourthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFourthOutJijyo"
							value="#{pc_Kaf00102.propFourthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr5Style}">
						<TD>
						<CENTER>5</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlFifthOut"
							value="#{pc_Kaf00102.propFifthOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propFifthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFifthOutJijyo"
							value="#{pc_Kaf00102.propFifthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr6Style}">
						<TD>
						<CENTER>6</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlSixthOut"
							value="#{pc_Kaf00102.propSixthOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propSixthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSixthOutJijyo"
							value="#{pc_Kaf00102.propSixthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr7Style}">
						<TD>
						<CENTER>7</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSeventhOut"
							value="#{pc_Kaf00102.propSeventhOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propSeventhOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSeventhOutJijyo"
							value="#{pc_Kaf00102.propSeventhOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr8Style}">
						<TD>
						<CENTER>8</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlEighthOut"
							value="#{pc_Kaf00102.propEighthOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propEighthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlEighthOutJijyo"
							value="#{pc_Kaf00102.propEighthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr9Style}">
						<TD>
						<CENTER>9</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlNinthOut"
							value="#{pc_Kaf00102.propNinthOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propNinthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlNinthOutJijyo"
							value="#{pc_Kaf00102.propNinthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kaf00102.tr10Style}">
						<TD>
						<CENTER>10</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlTenthOut"
							value="#{pc_Kaf00102.propTenthOut.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propTenthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlTenthOutJijyo"
							value="#{pc_Kaf00102.propTenthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR class="clear_border">
						<TD colspan="3" style="background-color:transparent;"></TD>
					</TR>
					<TR style="${pc_Kaf00102.trKaipageStyle}">
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblKaiPageSitei" value="改頁指定"></h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlKaiPageSitei"
							value="#{pc_Kaf00102.propKaiPageSitei.stringValue}">
							<f:selectItems value="#{pc_Kaf00102.propKaiPageSitei.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="580" class="button_bar">
				<TR>
					<TD>
						<hx:commandExButton
							type="submit"
							value="選択"
							styleClass="commandExButton_etc"
							id="select" 
							action="#{pc_Kaf00102.doSelectAction}">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="キャンセル"
							styleClass="commandExButton_etc"
							id="cancle" 
							onclick="return func_1(this, event);">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/childFooter.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

