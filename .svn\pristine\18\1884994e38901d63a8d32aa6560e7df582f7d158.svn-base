<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/kk/Kka00201T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kka00201T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT type="text/javascript">

// 画面ロード時の名称再取得
function loadAction(event){
  doKaiinAjax(document.getElementById('form1:htmlKaiinNo'), event, 'form1:htmlKaiinNm');
}

// 会員氏名を取得する
function doKaiinAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/kk/VKkaKaiDaiAJAX";
  var args = new Array();
  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}
// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById("form1:htmlHidButtonKbn").value = "0";
}

</SCRIPT></HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kka00201T02.onPageLoadBegin}">
<!-- <gakuen:itemStateCtrl managedbean="pc_Kka00201T02"> -->
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Kka00201T02.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kka00201T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kka00201T02.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="自由設定"
  styleClass="commandExButton" id="freeEdit" tabindex="1"
  disabled="#{pc_Kka00201T01.kka00201.propFreeBtn.disabled}"
  action="#{pc_Kka00201T02.doFreeAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

  <TABLE border="0" cellpadding="5">
    <TBODY>
      <TR>
        <TD width="870">
        <TABLE class="table" width="100%">
          <TBODY>
            <TR align="center" valign="middle">
        	 <TH class="v_a" nowrap  width="150">
             <!-- 登録対象 -->
               <h:outputText styleClass="outputText" id="lblTorokuTaisyo"
               value="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.labelName}"
               style="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.labelStyle}"></h:outputText></TH>
             <TD width="540"><h:selectOneRadio
               id="htmlTorokuTaisyo" disabledClass="selectOneRadio_Disabled"
               styleClass="selectOneRadio" tabindex="2"
               value="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.stringValue}"
               disabled="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.disabled}">
               <f:selectItems value="#{pc_Kka00201T01.kka00201.propTorokuTaisyo.list}" /></h:selectOneRadio>
             </TD>

              <TD style="background-color: transparent; text-align: right"
                class="clear_border"><hx:commandExButton type="submit"
                value="選　択" styleClass="commandExButton" id="select" tabindex="5"
                disabled="#{pc_Kka00201T01.kka00201.propSelect.disabled}"
                action="#{pc_Kka00201T01.doSelectAction}"></hx:commandExButton>
              <hx:commandExButton type="submit" value="解　除" tabindex="6"
                styleClass="commandExButton" id="unselect"
                disabled="#{pc_Kka00201T01.kka00201.propUnSelect.disabled}"
                action="#{pc_Kka00201T01.doUnselectAction}"></hx:commandExButton>
              </TD>
            </TR>
            <TH nowrap class="v_a">
            <!--会員番号 -->
              <h:outputText styleClass="outputText" id="lblKaiinNo"
              value="#{pc_Kka00201T01.kka00201.propKaiinNo.labelName}"
              style="#{pc_Kka00201T01.kka00201.propKaiinNo.labelStyle}"></h:outputText></TH>
            <TD><h:inputText styleClass="inputText"
              id="htmlKaiinNo" size="18" tabindex="3"
              maxlength="#{pc_Kka00201T01.kka00201.propKaiinNo.maxLength}"
              disabled="#{pc_Kka00201T01.kka00201.propKaiinNo.disabled}"
              value="#{pc_Kka00201T01.kka00201.propKaiinNo.stringValue}"
              readonly="#{pc_Kka00201T01.kka00201.propKaiinNo.readonly}"
              style="#{pc_Kka00201T01.kka00201.propKaiinNo.style}"
              onblur="return doKaiinAjax(this, event, 'form1:htmlKaiinNm');"></h:inputText>
              <hx:commandExButton type="submit" value="検" tabindex="4"
              styleClass="commandExButton_search" id="btnKaiin"
              disabled="#{pc_Kka00201T01.kka00201.propKaiinNo.disabled}"
              action="#{pc_Kka00201T01.doKaiinSubAction}">
              </hx:commandExButton>
              <h:inputText styleClass="likeOutput"
                id="htmlKaiinNm" size="55"
                readonly="true" tabindex="-1"
                value="#{pc_Kka00201T01.kka00201.propName.stringValue}"></h:inputText>
            </TD>
            <TR>
              <TH nowrap class="v_b">
              <!-- 卒業校 -->
                <h:outputText styleClass="outputText" id="lblGakJokyo"
                value="#{pc_Kka00201T01.kka00201.propGakko.labelName}"
                style="#{pc_Kka00201T01.kka00201.propGakko.labelStyle}"></h:outputText></TH>
              <TD><h:outputText styleClass="outputText" id="htmlGakoOut"
                value="#{pc_Kka00201T01.kka00201.propGakko.stringValue}"></h:outputText></TD>
            </TR>
          </TBODY>
        </TABLE>
        <BR>
        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TBODY>
            <TR>
              <TD>
              <TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
                style="border-bottom-style: none; ">
                <TBODY>
                  <TR>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="37"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T01" style="width: 100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKihon.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T01Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_on" width="8%"><hx:commandExButton tabindex="7"
                      type="button" styleClass="tab_head_on" id="tabKka00201T02" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameZaigakuji.stringValue}"
                      ></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="38"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T03" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameZemi.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T03Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="39"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T04" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameSyussin.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T04Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="40"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T05" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameAddr.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T05Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="41"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T06" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKiseisaki.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T06Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="42"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T07" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameSinzoku.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T07Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="43"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T08" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKeireki.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T08Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="44"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T09" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameGroup.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T09Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="45"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T10" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameKizou.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T10Action}"></hx:commandExButton></TD>
                    <TD class="tab_head_off" width="8%"><hx:commandExButton tabindex="46"
                      type="submit" styleClass="tab_head_off" id="tabKka00201T11" style="width:100%"
                      value="#{pc_Kka00201T01.kka00201.propTabNameSonota.stringValue}"
                      action="#{pc_Kka00201T02.doTabKka00201T11Action}"></hx:commandExButton></TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
            <TR>
              <TD>
              <TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0"
                width="100%" style="border-top-style: none; ">
                <TBODY>
                  <TR>
                    <TD width="100%">
                    <div style="height: 395px">
						<TABLE class="table" style="margin-top:15px" width="820">
							<TBODY>
								<TR>
									<TH class="v_a" nowrap width="187">
									<!-- 卒業年度 -->
										<h:outputText
										styleClass="outputText" id="lblSotNendo"
										value="#{pc_Kka00201T02.propSotNendo.labelName}"
										style="#{pc_Kka00201T02.propSotNendo.labelStyle}"></h:outputText></TH>
									<TD width="224"><h:inputText styleClass="inputText"
										id="htmlSotNendo" size="4" tabindex="8"
										value="#{pc_Kka00201T02.propSotNendo.dateValue}"
										disabled="#{pc_Kka00201T02.propSotNendo.disabled}"
										style="#{pc_Kka00201T02.propSotNendo.style}"
										maxlength="#{pc_Kka00201T02.propSotNendo.maxLength}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH nowrap width="186">
									<!-- 卒業学期ＮＯ -->
										<h:outputText
										styleClass="outputText" id="lblSotGakkiNo"
										value="#{pc_Kka00201T02.propSotGakkiNo.labelName}"
										style="#{pc_Kka00201T02.propSotGakkiNo.labelStyle}"></h:outputText></TH>
									<TD width="225"><h:inputText styleClass="inputText"
										id="htmlSotGakkiNo" size="2" tabindex="9"
										value="#{pc_Kka00201T02.propSotGakkiNo.integerValue}"
										style="#{pc_Kka00201T02.propSotGakkiNo.style}"
										disabled="#{pc_Kka00201T02.propSotGakkiNo.disabled}"
										maxlength="#{pc_Kka00201T02.propSotGakkiNo.maxLength}">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText> <hx:commandExButton type="submit" tabindex="10"
										value="選択" styleClass="commandExButton" id="sotSelect"
										disabled="#{pc_Kka00201T02.propSotSelectBtn.disabled}"
										action="#{pc_Kka00201T02.doSotSelectAction}"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_a" width="186">
									<!-- 卒業学期名称 -->
										<h:outputText
										styleClass="outputText" id="lblSotGakkiMei"
										value="#{pc_Kka00201T02.propSotGakkiMei.labelName}"></h:outputText></TH>
									<TD width="224"><h:inputText styleClass="inputText"
										id="htmlSotGakkiMei" size="20" tabindex="11"
										value="#{pc_Kka00201T02.propSotGakkiMei.stringValue}"
										style="#{pc_Kka00201T02.propSotGakkiMei.style}"
										disabled="#{pc_Kka00201T02.propSotGakkiMei.disabled}"
										maxlength="#{pc_Kka00201T02.propSotGakkiMei.maxLength}"></h:inputText></TD>
									<TH width="186">
									<!--学籍番号 -->
										<h:outputText styleClass="outputText" id="lblGaksekiNo" 
										value="#{pc_Kka00201T02.propGakusekiNo.labelName}"
										style="#{pc_Kka00201T02.propGakusekiNo.labelStyle}"></h:outputText></TH>
									<TD width="225"><h:inputText styleClass="inputText"
										id="htmlGakusekiNo" size="18" tabindex="12"
										maxlength="#{pc_Kka00201T02.propGakusekiNo.maxLength}"
										disabled="#{pc_Kka00201T02.propGakusekiNo.disabled}"
										value="#{pc_Kka00201T02.propGakusekiNo.stringValue}"
										style="#{pc_Kka00201T02.propGakusekiNo.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_a" nowrap width="187">
									<!-- 入学年度 -->
										<h:outputText
										styleClass="outputText" id="lblNyuNendo"
										value="#{pc_Kka00201T02.propNyuNendo.labelName}"></h:outputText></TH>
									<TD width="224"><h:inputText styleClass="inputText"
										id="htmlNyuNendo" size="4" tabindex="13"
										value="#{pc_Kka00201T02.propNyuNendo.dateValue}"
										disabled="#{pc_Kka00201T02.propNyuNendo.disabled}"
										style="#{pc_Kka00201T02.propNyuNendo.style}"
										maxlength="#{pc_Kka00201T02.propNyuNendo.maxLength}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH nowrap width="186">
									<!-- 入学学期ＮＯ -->
										<h:outputText
										styleClass="outputText" id="lblNyuGakkiNo"
										value="#{pc_Kka00201T02.propNyuGakkiNo.labelName}"></h:outputText></TH>
									<TD width="225"><h:inputText styleClass="inputText"
										id="htmlNyuGakkiNo" size="2" tabindex="14"
										value="#{pc_Kka00201T02.propNyuGakkiNo.integerValue}"
										disabled="#{pc_Kka00201T02.propNyuGakkiNo.disabled}"
										style="#{pc_Kka00201T02.propNyuGakkiNo.style}"
										maxlength="#{pc_Kka00201T02.propNyuGakkiNo.maxLength}">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_a" nowrap width="186">
									<!-- 入学学期名称 -->
										<h:outputText
										styleClass="outputText" id="lblNyuGakkiMei"
										value="#{pc_Kka00201T02.propNyuGakkiMei.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNyuGakkiMei" size="20" tabindex="15"
										value="#{pc_Kka00201T02.propNyuGakkiMei.stringValue}"
										disabled="#{pc_Kka00201T02.propNyuGakkiMei.disabled}"
										style="#{pc_Kka00201T02.propNyuGakkiMei.style}"
										maxlength="#{pc_Kka00201T02.propNyuGakkiMei.maxLength}"></h:inputText></TD>
								</TR>
								
								
								<TR>
									<TH class="v_a" nowrap width="186">
									<!-- 入学日付 -->
										<h:outputText
										styleClass="outputText" id="lblNyuDate"
										value="#{pc_Kka00201T02.propNyuDate.labelName}"
										style="#{pc_Kka00201T02.propNyuDate.labelStyle}"></h:outputText></TH>
									<TD width="224"><h:inputText styleClass="inputText" id="htmlNyuDate"
										value="#{pc_Kka00201T02.propNyuDate.dateValue}" tabindex="16"
										disabled="#{pc_Kka00201T02.propNyuDate.disabled}"
										style="#{pc_Kka00201T02.propNyuDate.style}"
										size="10">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist imeMode="inactive" errorClass="inputText_Error"
											promptCharacter="_" /></h:inputText></TD>
									<TH nowrap width="186">
									<!-- 卒業日付 -->
										<h:outputText
										styleClass="outputText" id="lblSotDate"
										value="#{pc_Kka00201T02.propSotDate.labelName}"
										style="#{pc_Kka00201T02.propSotDate.labelStyle}"></h:outputText></TH>
									<TD width="224"><h:inputText styleClass="inputText" id="htmlSotDate"
										value="#{pc_Kka00201T02.propSotDate.dateValue}" tabindex="17"
										disabled="#{pc_Kka00201T02.propSotDate.disabled}"
										style="#{pc_Kka00201T02.propSotDate.style}"
										size="10">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist imeMode="inactive" errorClass="inputText_Error"
											promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_a" nowrap width="187">
									<!-- みなし入学年度 -->
										<h:outputText
										styleClass="outputText" id="lblMinasiNyuNendo"
										value="#{pc_Kka00201T02.propMinasiNyuNendo.labelName}"
										style="#{pc_Kka00201T02.propMinasiNyuNendo.labelStyle}"></h:outputText></TH>
									<TD width="224"><h:inputText styleClass="inputText"
										id="htmlMinasiNyuNendo" size="4" tabindex="18"
										value="#{pc_Kka00201T02.propMinasiNyuNendo.dateValue}"
										style="#{pc_Kka00201T02.propMinasiNyuNendo.style}"
										maxlength="#{pc_Kka00201T02.propMinasiNyuNendo.maxLength}"
										disabled="#{pc_Kka00201T02.propMinasiNyuNendo.disabled}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH nowrap width="186">
									<!-- みなし入学学期ＮＯ -->
										<h:outputText
										styleClass="outputText" id="lblMinasiNyuGakkiNo"
										value="#{pc_Kka00201T02.propMinasiNyuGakkiNo.labelName}"
										style="#{pc_Kka00201T02.propMinasiNyuGakkiNo.labelStyle}"></h:outputText></TH>
									<TD width="225"><h:inputText styleClass="inputText"
										id="htmlMinasiNyuGakkiNo" size="2" tabindex="19"
										value="#{pc_Kka00201T02.propMinasiNyuGakkiNo.integerValue}"
										style="#{pc_Kka00201T02.propMinasiNyuGakkiNo.style}"
										maxlength="#{pc_Kka00201T02.propMinasiNyuGakkiNo.maxLength}"
										disabled="#{pc_Kka00201T02.propMinasiNyuGakkiNo.disabled}">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText> <hx:commandExButton type="submit"
										value="選択" styleClass="commandExButton" tabindex="20"
										disabled="#{pc_Kka00201T02.propMinasiSelectBtn.disabled}"
										id="minasiSelect" action="#{pc_Kka00201T02.doMinasiSelectAction}"></hx:commandExButton>
										<hx:commandExButton	type="submit" value="解除" styleClass="commandExButton"
										id="minasiUnselect" tabindex="21"
										disabled="#{pc_Kka00201T02.propMinasiUnSelectBtn.disabled}"
										action="#{pc_Kka00201T02.doMinasiUnselectAction}"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_a">
									<!-- みなし入学学期名称 -->
										<h:outputText
										styleClass="outputText" id="lblMinasiNyuGakkiName"
										value="#{pc_Kka00201T02.propMinasiNyuGakkiName.labelName}"
										style="#{pc_Kka00201T02.propMinasiNyuGakkiName.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlMinasiNyuGakkiName" tabindex="22"
										value="#{pc_Kka00201T02.propMinasiNyuGakkiName.value}"
										style="#{pc_Kka00201T02.propMinasiNyuGakkiName.style}"
										maxlength="#{pc_Kka00201T02.propMinasiNyuGakkiName.maxLength}"
										disabled="#{pc_Kka00201T02.propMinasiNyuGakkiName.disabled}"
										size="20"></h:inputText></TD>
									<TD colspan="2" class="clear_border"><BR>
									</TD>
								</TR>
								<TR>
									<TH class="v_a" nowrap width="187">
									<!-- 卒業時所属学科組織 -->
										<h:outputText
										styleClass="outputText" id="lblSotSzksGakka"
										value="#{pc_Kka00201T02.propSotSzksGakkaList.labelName}"
										style="#{pc_Kka00201T02.propSotSzksGakkaList.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu tabindex="23"
										styleClass="selectOneMenu" id="htmlSotSzksGakkaList"
										disabled="#{pc_Kka00201T02.propSotSzksGakkaList.disabled}"
										value="#{pc_Kka00201T02.propSotSzksGakkaList.value}"
										style="width:615px;">
										<f:selectItems
											value="#{pc_Kka00201T02.propSotSzksGakkaList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_a" nowrap width="187">
									<!-- 卒業時カリキュラム学科組織 -->
										<h:outputText
										styleClass="outputText" id="lblSotCurGakka"
										value="#{pc_Kka00201T02.propSotCurGakkaList.labelName}"
										style="#{pc_Kka00201T02.propSotCurGakkaList.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu tabindex="24"
										styleClass="selectOneMenu" id="htmlSotCurGakkaList"
										disabled="#{pc_Kka00201T02.propSotCurGakkaList.disabled}"
										value="#{pc_Kka00201T02.propSotCurGakkaList.value}"
										style="width:615px;">
										<f:selectItems
											value="#{pc_Kka00201T02.propSotCurGakkaList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_a" nowrap width="187">
									<!-- 卒業時学年 -->
										<h:outputText
										styleClass="outputText" id="lblSotGakunen"
										value="#{pc_Kka00201T02.propSotGakunenList.labelName}"
										style="#{pc_Kka00201T02.propSotGakunenList.labelStyle}"></h:outputText></TH>
									<TD width="224"><h:selectOneMenu tabindex="25"
										styleClass="selectOneMenu" id="htmlSotGakunenList"
										disabled="#{pc_Kka00201T02.propSotGakunenList.disabled}"
										style="#{pc_Kka00201T02.propSotGakunenList.style};width:140px"
										value="#{pc_Kka00201T02.propSotGakunenList.value}"
										style="width:205px;">
										<f:selectItems
											value="#{pc_Kka00201T02.propSotGakunenList.list}" />
									</h:selectOneMenu></TD>
									<TH nowrap width="186">
									<!-- 卒業時セメスタ -->
										<h:outputText
										styleClass="outputText" id="lblSotSemester"
										value="#{pc_Kka00201T02.propSotSemesterList.labelName}"></h:outputText></TH>
									<TD width="225"><h:selectOneMenu tabindex="26"
										styleClass="selectOneMenu" id="htmlSotSemesterList"
										disabled="#{pc_Kka00201T02.propSotSemesterList.disabled}"
										style="#{pc_Kka00201T02.propSotSemesterList.style};width:140px"
										value="#{pc_Kka00201T02.propSotSemesterList.value}"
										style="width:205px;">
										<f:selectItems
											value="#{pc_Kka00201T02.propSotSemesterList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH nowrap class="v_a">
									<!-- 異動出学種別マスタ -->
										<h:outputText
										styleClass="outputText" id="lblIdoSyutgakSbtMst"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtMst.labelName}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtMst.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlIdoSyutgakSbtMst" style="width:280px;" tabindex="27"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtMst.stringValue}"
										disabled="#{pc_Kka00201T02.propIdoSyutgakSbtMst.disabled}">
										<f:selectItems
											value="#{pc_Kka00201T02.propIdoSyutgakSbtMst.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit" tabindex="28"
										value="選択" styleClass="commandExButton" id="select2"
										disabled="#{pc_Kka00201T02.propIdoSyutgakSbtMst.disabled}"
										action="#{pc_Kka00201T02.doIdoSelectAction}"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_a">
									<!-- 異動出学種別区分 -->
										<h:outputText
										styleClass="outputText" id="lblIdoSyutgakSbtKbn"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtKbn.labelName}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtKbn.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlIdoSyutgakSbtKbn" style="width:150px;" tabindex="29"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtKbn.stringValue}"
										disabled="#{pc_Kka00201T02.propIdoSyutgakSbtKbn.disabled}">
										<f:selectItems
											value="#{pc_Kka00201T02.propIdoSyutgakSbtKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH nowrap class="v_a">
									<!-- 異動出学種別コード -->
										<h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtCd"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtCd.labelName}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtCd.labelStyle}"></h:outputText>
									</TH>
									<TD><h:inputText styleClass="inputText" size="4"
										id="htmlIdoSyutgakSbtCd" tabindex="30"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtCd.stringValue}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtCd.style}"
										disabled="#{pc_Kka00201T02.propIdoSyutgakSbtCd.disabled}"
										maxlength="#{pc_Kka00201T02.propIdoSyutgakSbtCd.maxLength}"></h:inputText>
									</TD>
									<TH nowrap>
									<!-- 異動出学種別名称 -->
										<h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtName"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtName.labelName}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtName.labelStyle}"></h:outputText>
									</TH>
									<TD><h:inputText styleClass="inputText" size="25"
										id="htmlIdoSyutgakSbtName" tabindex="31"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtName.stringValue}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtName.style}"
										disabled="#{pc_Kka00201T02.propIdoSyutgakSbtName.disabled}"
										maxlength="#{pc_Kka00201T02.propIdoSyutgakSbtName.maxLength}"></h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_a">
									<!-- 異動出学種別名称英語 -->
										<h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtNameEng"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtNameEng.labelName}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtNameEng.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										size="70" id="htmlIdoSyutgakSbtNameEng" tabindex="32"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtNameEng.stringValue}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtNameEng.style}"
										disabled="#{pc_Kka00201T02.propIdoSyutgakSbtNameEng.disabled}"
										maxlength="#{pc_Kka00201T02.propIdoSyutgakSbtNameEng.maxLength}"></h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_a">
									<!-- 異動出学種別対外名称 -->
										<h:outputText
										styleClass="outputText" id="lblpropIdoSyutgakSbtNameGai"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtNameGai.labelName}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtNameGai.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										size="25" id="htmlIdoSyutgakSbtNameGai" tabindex="33"
										value="#{pc_Kka00201T02.propIdoSyutgakSbtNameGai.stringValue}"
										style="#{pc_Kka00201T02.propIdoSyutgakSbtNameGai.style}"
										disabled="#{pc_Kka00201T02.propIdoSyutgakSbtNameGai.disabled}"
										maxlength="#{pc_Kka00201T02.propIdoSyutgakSbtNameGai.maxLength}"></h:inputText>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="820">
	                      <TBODY>
	                        <TR align="right">
	                          <TD align="center"><hx:commandExButton tabindex="34"
	                            type="submit" value="クリア" styleClass="commandExButton_etc"
	                            id="clear" disabled="#{pc_Kka00201T02.propClearBtn.disabled}"
	                            onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
	                            action="#{pc_Kka00201T02.doClearAction}"></hx:commandExButton></TD>
	                        </TR>
	                      </TBODY>
	                    </TABLE>
                    </div>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
          <TBODY>
            <TR align="right">
              <TD align="center"><hx:commandExButton tabindex="35"
                type="submit" value="確定" styleClass="commandExButton_dat"
                id="kakutei" disabled="#{pc_Kka00201T01.kka00201.propKakuteiBtn.disabled}"
                confirm="#{msg.SY_MSG_0001W}"
                action="#{pc_Kka00201T02.doKakuteiAction}"></hx:commandExButton>
                <hx:commandExButton type="submit" value="削除" styleClass="commandExButton_dat"
                id="delete" disabled="#{pc_Kka00201T01.kka00201.propDeleteBtn.disabled}"
                confirm="#{msg.SY_MSG_0004W}" tabindex="36"
                action="#{pc_Kka00201T02.doDeleteAction}"></hx:commandExButton></TD>
            </TR>
          </TBODY>
        </TABLE>
        </TD>
      </TR>
    </TBODY>
  </TABLE>
  <h:inputHidden id="htmlHidButtonKbn" value="#{pc_Kka00201T01.kka00201.propHidButtonKbn.integerValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidAction" value="#{pc_Kka00201T01.kka00201.propHidAction.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidErrMessage" value="#{pc_Kka00201T01.kka00201.propHidErrMessage.value}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

</h:form>
<!-- </gakuen:itemStateCtrl> -->
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
