<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KKA_KAI_DAI" name="会員台帳" prod_id="KK" description="校友会会員の基本情報を持っています。|『会員情報登録』、『会員情報一括登録』、『会員番号付番』にて作成されます。">
<STATMENT><![CDATA[
KKA_KAI_DAI
]]></STATMENT>
<COLUMN id="KAIIN_NO" name="会員番号" type="string" length="10" lengthDP="0" byteLength="10" description="会員を識別するためのコードです。|会員番号は必ずユニークであることを原則とします。"/><COLUMN id="KAIIN_SBT_CD" name="会員種別コード" type="string" length="2" lengthDP="0" byteLength="2" description="会員の会員種別コードです。"/><COLUMN id="KAIGAI_ZAIJU_FLG" name="海外在住フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="会員が海外に在住しているか否かを識別するための区分です。|海外在住フラグが1（海外在住）の会員の宛名ラベル、宛名はがきには、英語住所１、２、３を使用するようにデフォルト設定にて設定することができます。|"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="MEIBO_KEISAI_FLG" name="名簿掲載フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="会員情報を名簿に掲載するか否かを識別するための区分です。|名簿掲載区分が0（掲載しない）の会員情報は校友会名簿に出力されません。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="LABEL_OUT_FLG" name="宛名ラベル出力フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="宛名ラベルを出力するか否かを識別するためのフラグです。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="MEIBO_OUT_TAISYO_FLG" name="名簿発送対象フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="校友会名簿を発送するか否かを識別するためのフラグです。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="MEIBO_HASSO_ZUMI_FLG" name="名簿発送済フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="校友会名簿を発送したか否かを識別するためのフラグです。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="KAIHO_TAISYO_FLG" name="校友会会報発送対象フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="校友会会報を発送するか否かを識別するためのフラグです。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="KAIHO_HASSO_ZUMI_FLG" name="校友会会報発送済フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="校友会会報を発送したか否かを識別するための区分です。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="FURIKOMI_NO" name="振込番号" type="string" length="8" lengthDP="0" byteLength="8" description="各種徴収金振込用紙に出力する振込依頼人を識別するための番号です。|システムで連番を付番します。|※Exでは、未使用です。"/><COLUMN id="KOJIN_FLG" name="故人フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="会員が故人であるか否かを識別します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SEIKYO_DATE" name="逝去日付" type="date" length="0" lengthDP="0" byteLength="0" description="会員が逝去した日付です。"/><COLUMN id="FUMEI_FLG" name="消息不明フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="会員の消息が不明であるか否かを識別します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="FUMEI_YM" name="消息不明年月" type="string" length="6" lengthDP="0" byteLength="6" description="会員の消息不明となった年月です。"/><COLUMN id="DELETE_FLG" name="削除フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="会員統合処理にて、他の会員情報に集約されたかどうかを識別します。|削除フラグが1（削除対象）の会員情報は、年次更新処理にて会員マスタより削除されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="JIMUKYOKU_CD" name="事務局コード" type="string" length="4" lengthDP="0" byteLength="4" description="会員が所属する事務局のコードが設定されます。"/><COLUMN id="NOFU_TEISHI_END_NEN" name="納付書停止終了年" type="string" length="4" lengthDP="0" byteLength="4" description="納付書出力を停止する終了年です。|※Exでは、未使用です。"/><COLUMN id="MEMO" name="会員メモ" type="string" length="1024" lengthDP="0" byteLength="3072" description="会員メモです。"/>
</TABLE>
