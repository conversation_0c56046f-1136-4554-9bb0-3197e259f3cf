<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<SCRIPT type="text/javascript">
function openPCos0401() {
	setTarget("xxxx");
	openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
	return true;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cos00701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!--↓outer↓-->
			<DIV class="outer"><!--↓head↓--><!-- ヘッダーインクルード --><jsp:include
				page="../../rev/inc/header.jsp">
				<hx:panelBox styleClass="panelBox" id="boxHeader"></hx:panelBox>
			</jsp:include><!-- ヘッダーへのデータセット領域 -->
			<DIV style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cos00701.doCloseDispAction}"></hx:commandExButton><h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cos00701.funcId}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cos00701.screenName}"></h:outputText></DIV>
			<CENTER>
			<TABLE border="0" width="950">
				<TBODY>
					<TR>
						<TD width="900" align="left">
						<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND><h:outputText
							id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
							styleClass="outputText" escape="false"
							style="color: red; font-size: 8pt; font-weight: bold">
						</h:outputText></FIELDSET>
						</TD>
						<TD width="50" align="right"></TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>
			<!--↑head↑--><!--↓content↓-->
			<DIV id="content">

			<DIV class="column" align="right">
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR align="right">
						<TD height="405" align="center">
						<TABLE border="0" cellpadding="0" width="790" class="table" cellspacing="0">
							<TBODY>
								<TR>
									<TH class="v_a" width="140" valign="top">
										<h:outputText
											styleClass="outputText" id="htmlInputFileTupeLabel"
											value="#{pc_Cos00701.propFileKbn.labelName}"
											style="#{pc_Cos00701.propFileKbn.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="620" colspan="10">
										<h:selectOneMenu
											styleClass="selectOneMenu" id="htmlFileKbn"
											disabled="#{pc_Cos00701.propFileKbn.disabled}"
											value="#{pc_Cos00701.propFileKbn.value}"
											style="width: 200px">
											<f:selectItems value="#{pc_Cos00701.propFileKbn.list}" />
										</h:selectOneMenu>
										<hx:commandExButton
											type="submit" value="選択" styleClass="commandExButton" id="select"
											disabled="#{pc_Cos00701.propFileKbn.disabled}"
											action="#{pc_Cos00701.doSelectAction}">
										</hx:commandExButton>
										<hx:commandExButton
											type="submit" value="解除" styleClass="commandExButton" id="unselect"
											disabled="#{!pc_Cos00701.propFileKbn.disabled}"
											action="#{pc_Cos00701.doUnselectAction}">
										</hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH class="v_b" width="140">
										<h:outputText
											styleClass="outputText" id="htmlInputFileLabel"
											value="#{pc_Cos00701.propInputFile.labelName}"
											style="#{pc_Cos00701.propInputFile.labelStyle}">
										</h:outputText>
										<BR>
										<h:outputText
											styleClass="outputText"
											id="htmlInputFileOldLable"
											value="#{pc_Cos00701.propInputFileOld.labelName}"
											style="#{pc_Cos00701.propInputFileOld.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="620" colspan="10">
										<hx:fileupload
											styleClass="fileupload" id="htmlInputFile"
											disabled="#{!pc_Cos00701.propFileKbn.disabled}"
											value="#{pc_Cos00701.propInputFile.value}"
											style="width:610px;">
											<hx:fileProp name="fileName" value="#{pc_Cos00701.propInputFile.fileName}" />
											<hx:fileProp name="contentType" value="#{pc_Cos00701.propInputFile.contentType}" />
										</hx:fileupload>
										<BR>
										<h:outputText
											styleClass="outputText" id="htmlInputFileOld"
											value="#{pc_Cos00701.propInputFileOld.stringValue}">
										</h:outputText>
										<br>
									</TD>
								</TR>
								<TR>
									<TH class="v_c" width="140" valign="top">
										<h:outputText
											styleClass="outputText" id="htmlInDataRegistKbnTypeLabel"
											value="#{pc_Cos00701.propInDataRegistKbnType.labelName}"
											style="#{pc_Cos00701.propInDataRegistKbnType.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="10" width="620">
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlInDataRegistKbnType"
											layout="pageDirection"
											disabled="#{pc_Cos00701.propInDataRegistKbnType.disabled}"
											value="#{pc_Cos00701.propInDataRegistKbnType.value}">
											<f:selectItems value="#{pc_Cos00701.propInDataRegistKbnType.list}" />
										</h:selectOneRadio>
									</TD>
								</TR>
								<TR>
									<TH class="v_d" width="140">
										<h:outputText
											styleClass="outputText" id="htmlSyoriKbnLabel"
											value="#{pc_Cos00701.propSyoriKbn.labelName}"
											style="#{pc_Cos00701.propSyoriKbn.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="10" width="620">
										<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
											disabled="#{!pc_Cos00701.propFileKbn.disabled}"
											value="#{pc_Cos00701.propSyoriKbn.checked}"
											style="#{pc_Cos00701.propSyoriKbn.style}">
										</h:selectBooleanCheckbox>
										<h:outputText
											styleClass="outputText" id="htmlSyoriKbnChkname"
											style="#{pc_Cos00701.propSyoriKbnChkName.labelStyle}"
											value="#{pc_Cos00701.propSyoriKbnChkName.stringValue}">
										</h:outputText>
									</TD>
								</TR>

								<TR>
									<TH class="v_e" width="140" valign="top">
										<h:outputText
											styleClass="outputText" id="htmlChkListLabel"
											value="#{pc_Cos00701.propChkListNormal.labelName}"
											style="#{pc_Cos00701.propChkListNormal.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="620" colspan="10">
										<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlChkListNormal"
											disabled="#{!pc_Cos00701.propFileKbn.disabled}"
											value="#{pc_Cos00701.propChkListNormal.checked}"
											style="#{pc_Cos00701.propChkListNormal.style}">
										</h:selectBooleanCheckbox>
										<h:outputText
											styleClass="outputText" id="htmlChkListNormalChkName"
											style="#{pc_Cos00701.propChkListNormalChkName.labelStyle}"
											value="#{pc_Cos00701.propChkListNormalChkName.stringValue}">
										</h:outputText>
										<BR>
										<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox"
											id="htmlChkListError"
											disabled="#{!pc_Cos00701.propFileKbn.disabled}"
											value="#{pc_Cos00701.propChkListError.checked}"
											style="#{pc_Cos00701.propChkListError.style}">
										</h:selectBooleanCheckbox>
										<h:outputText
											styleClass="outputText" id="htmlChkListErrorChkName"
											style="#{pc_Cos00701.propChkListErrorChkName.labelStyle}"
											value="#{pc_Cos00701.propChkListErrorChkName.stringValue}">
										</h:outputText>
										<BR>
										<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox"
											id="htmlChkListWarning"
											disabled="#{!pc_Cos00701.propFileKbn.disabled}"
											value="#{pc_Cos00701.propChkListWarning.checked}"
											style="#{pc_Cos00701.propChkListWarning.style}">
										</h:selectBooleanCheckbox>
										<h:outputText
											styleClass="outputText" id="htmlChkListWarningChkName"
											style="#{pc_Cos00701.propChkListWarningChkName.labelStyle}"
											value="#{pc_Cos00701.propChkListWarningChkName.stringValue}">
										</h:outputText>
										<BR>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE class="button_bar" width="600">
							<TR>
								<TD align="center" width="600">
									<hx:commandExButton type="submit"
										onclick="return openPCos0401();" value="入力項目指定"
										styleClass="commandExButton_dat" id="setinput"
										action="#{pc_Cos00701.doSetinputAction}">
									</hx:commandExButton>　
									<hx:commandExButton
										type="submit" value="実行" styleClass="commandExButton_dat" id="exec"
										disabled="#{!pc_Cos00701.propFileKbn.disabled}"
										action="#{pc_Cos00701.doExecAction}">
									</hx:commandExButton>
								</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--><!--↓foot↓--><jsp:include page="../inc/footer.jsp" />
			<!--↑foot↑--></DIV>
			<!--↑outer↑-->
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>


</HTML>

