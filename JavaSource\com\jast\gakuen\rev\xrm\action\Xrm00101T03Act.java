package com.jast.gakuen.rev.xrm.action;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.jast.gakuen.framework.PageCodeBaseEx;
import com.jast.gakuen.framework.batch.BatchConst;
import com.jast.gakuen.framework.batch.BatchLogic;
import com.jast.gakuen.framework.constant.ActionConst;
import com.jast.gakuen.framework.constant.SyMsgConst;
import com.jast.gakuen.framework.db.DbException;
import com.jast.gakuen.framework.util.CheckComponent;
import com.jast.gakuen.framework.util.MessageList;
import com.jast.gakuen.framework.util.UtilDate;
import com.jast.gakuen.framework.util.UtilIni;
import com.jast.gakuen.framework.util.UtilLog;
import com.jast.gakuen.framework.util.UtilProperty;
import com.jast.gakuen.framework.util.UtilStr;
import com.jast.gakuen.framework.util.UtilSystem;
import com.jast.gakuen.rev.RevActionBase;
import com.jast.gakuen.rev.co.Cos00401;
import com.jast.gakuen.rev.co.util.UtilCombBoxMaker;
import com.jast.gakuen.rev.co.util.UtilCosOpt;
import com.jast.gakuen.rev.gh.PGhz0301;
import com.jast.gakuen.rev.gh.constant.GhConstant;
import com.jast.gakuen.rev.gh.constant.GhMsgConst;
import com.jast.gakuen.rev.gh.db.dao.GhiEnnoDAO;
import com.jast.gakuen.rev.gh.db.dao.JoinPaywBunInfoDAO;
import com.jast.gakuen.rev.gh.db.dao.VGaksekiDAO;
import com.jast.gakuen.rev.gh.db.dao.VSotGaksekiDAO;
import com.jast.gakuen.rev.gh.db.entity.GhgFurikomiAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhAR;
import com.jast.gakuen.rev.gh.db.entity.GhiEnnoAR;
import com.jast.gakuen.rev.gh.db.entity.JoinPaywBunInfoAR;
import com.jast.gakuen.rev.gh.db.entity.VGaksekiAR;
import com.jast.gakuen.rev.gh.db.entity.VSotGaksekiAR;
import com.jast.gakuen.rev.gh.util.UtilGhCombBoxMaker;
import com.jast.gakuen.rev.gh.util.UtilGhFormat;
import com.jast.gakuen.rev.gh.util.UtilGhGakuseki;
import com.jast.gakuen.rev.gh.util.UtilGhPdf;
import com.jast.gakuen.rev.xrm.Xrm00101T01;
import com.jast.gakuen.rev.xrm.Xrm00101T02;
import com.jast.gakuen.rev.xrm.Xrm00101T03;
import com.jast.gakuen.rev.xrm.action.bean.Xrm00101L01Bean;
import com.jast.gakuen.rev.xrm.constant.XrmConst;
import com.jast.gakuen.rev.xrm.constant.XrmIniConst;
import com.jast.gakuen.rev.xrm.db.dao.XrmGhgFurikomiDAO;
import com.jast.gakuen.rev.xrm.util.XrmUtilConvert;
import com.jast.gakuen.rev.xrx.db.dao.XrxMeiKanriKmkDAO;
import com.jast.gakuen.rev.xrx.db.entity.XrxMeiKanriKmkAR;
import com.jast.gakuen.system.co.constant.code.ShutsuryokuHohoKbn;
import com.jast.gakuen.system.co.db.dao.CobGaksekiDAO;
import com.jast.gakuen.system.co.db.entity.CobGaksekiAR;

/**
 * 請求書出力アクションクラス
 * <br>
 * <AUTHOR>
 */
public class Xrm00101T03Act extends RevActionBase {
	
	//エラーメッセージ用文字列
	private final String NOCHECK_ERR_MSG = "納付金";
	private final String ERR_MSG_TSUSHIN_TEXT_1 = "備考欄は1行に32byte以内で";
	private final String ERR_MSG_TSUSHIN_TEXT_2 = "備考欄は6行以内で";	
	private final String ERR_MSG_FURIKOMI_CD ="指定した振込依頼人番号";
	private final String ERR_NOT_SYORI ="処理対象外の振込依頼人コードです。" ;
	private final String ERR_MSG_KIGEN_TEXT_3 = "納入期限を直接入力時は、納入期限/有効期限";	
	//納入期限のエラーメッセージ追加
	private final String ERR_MSG_PAYDATE_1 = "納入期限";
	private final String ERR_MSG_YUKOULIMIT_1 = "有効期限";
	
	/**
	 * 画面初期化処理<br>
	 * 画面の初期化を行う。
	 * <br>
	 * @param  pagecode	 	(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 */
	protected String init(PageCodeBaseEx pagecode) {
		Xrm00101T03 pc = (Xrm00101T03) pagecode;
		
		//画面初期化
		try {
			
			//初期値設定
			setInitItemValue(pc);
			
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * 初期値セット<br>
	 * 各項目に初期値を設定する。
	 * <br>
	 * @param  pc (Xrm00101T03)ページコード
	 * @throws DbException
	 */
	private void setInitItemValue(Xrm00101T03 pc) throws DbException {
		
		UtilCombBoxMaker utilCombBoxMaker = new UtilCombBoxMaker();
		UtilGhCombBoxMaker ghUtilCombBoxMaker = new UtilGhCombBoxMaker();
		
		//		//発行日付
		//		Date now = nowDate();
		//		pc.getpropHakkouDate().setDateValue(now);
		
		//		//通信欄
		//		pc.getPropTsushinText().setDisabled(true);
		//		// 通信欄(ぺイジー)の初期値設定を追加 2014.04.10 y.taguchi ADD START
		//		String tsushinText = "";
		//		// 画面．通信欄(ぺイジー)を設定
		//		pc.getPropTsushinText().setStringValue(tsushinText);
		//振込依頼人コード
		pc.getpropHurikomininCd().setStringValue("");
		
		//割当済納付金一覧
		pc.getPropPaywList().setListbean(new Xrm00101L01Bean());
		pc.getPropPaywList().setRows(0);
		pc.getPropPaywList().setList(new ArrayList());
		
		//オプションテーブルより前回入力値を取得
		//(データが存在しない場合は初期値を設定)
		loadDefaultItemValueOption(pc);
		
		//		if(pc.getPropOutPutKbn().getValue()==null){
		//			//出力区分
		//			pc.getPropOutPutKbn().setValue("1");		
		//		}
		
		//フォーカスセット
		UtilSystem.getDisplayInfo().setTargetFocusId(
				pc.getPropGakusekiCd().getId());
		
		//ロック項目設定
		pc.getPropActiveControlSearch().setIntegerValue(
				new Integer(GhConstant.GH_CTR_ENABLED));
		
	}
	
	/**
	 * アクション(選択)<br>
	 * 入力された学費年度・学籍番号に割り当てられている納付金の一覧を表示する。
	 * <br>
	 * @param   pagecode 	(PageCodeBaseEx)ページコード
	 * @return  String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws  Exception
	 */
	protected String search(PageCodeBaseEx pagecode) throws DbException {
		
		try {
			Xrm00101T03 pc = (Xrm00101T03)pagecode;
			
			//入力チェック
			MessageList message = checkSelectErr(pc);
			
			if (message.size() != 0) {
				//入力チェックでエラーが有る場合
				UtilSystem.getDisplayInfo()
				.setDisplayMessage(message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			//割当済納付金一覧の設定
			if (!setPaywList(pc)) {
				return ActionConst.RET_FALSE;
			}
			
			//ロック項目設定
			//学籍番号
			pc.getPropGakusekiCd().setDisabled(true);
			//振込依頼人コード
			pc.getpropHurikomininCd().setDisabled(true);
			
			//各ボタン
			pc.getPropActiveControlSearch().setIntegerValue(
					new Integer(GhConstant.GH_CTR_DISABLED));
			
		} catch (Exception e) {
			UtilLog.error(this.getClass(), e);
			throw new RuntimeException(e);
		}
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * 入力チェック<br>
	 * 入力チェックを行う。
	 * <br>
	 * @param  pc		 (Xrm00101T03)ページコード
	 * @return chkMsg	 (MessageList)メッセージリスト
	 * @throws DbException
	 */
	private MessageList checkSelectErr(Xrm00101T03 pc) throws DbException {
		
		CheckComponent cc = new CheckComponent();
		
		//学籍番号
		if(pc.getpropHurikomininCd().getStringValue().equals("")){
			cc.addComponent(pc.getPropGakusekiCd());
		}
		MessageList chkMsg =  cc.check();
		
		if (chkMsg.size() != 0) {
			//入力チェックでエラーが有る場合
			return chkMsg;
		}
		
		//--------------------------------------------------
		//  学生存在チェック
		//--------------------------------------------------
		//管理NO取得用[管理NO,存在VIEW区分]
		String gakInfo[] = new String[2];
		//管理NO
		String kanriNo = null;
		//学生存在VIEW情報
		String viewKbn = null;
		//学籍コード
		String gakusekiCd = UtilStr.cnvNull(
				pc.getPropGakusekiCd().getStringValue());
		
		//振込依頼人コードでの検索
		if(!pc.getpropHurikomininCd().getStringValue().equals("")){
			MessageList message = new MessageList();
			long lKanriNo =0;
			XrmGhgFurikomiDAO xrmGhgFurikomiDAO =(XrmGhgFurikomiDAO)getDbs().getDao(XrmGhgFurikomiDAO.class);    			
			String hurikomiCd = pc.getpropHurikomininCd().getStringValue();
			//玉川大学は連番を使用しないため0固定
			GhgFurikomiAR ghgFurikomiAR = xrmGhgFurikomiDAO.findByPrimaryKey(hurikomiCd,0);
			if(ghgFurikomiAR==null){
				setInitGakInfo(pc);         
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0033E,ERR_MSG_FURIKOMI_CD));
				//メッセージをセット
				return message;
				
			}
			
			lKanriNo = ghgFurikomiAR.getKanriNo();
			CobGaksekiDAO cobGaksekiDAO =(CobGaksekiDAO)getDbs().getDao(CobGaksekiDAO.class);
			CobGaksekiAR cobGaksekiAR = cobGaksekiDAO.findValidGakusekiByKanriNo(lKanriNo);
			String gakusekin = cobGaksekiAR.getGakusekiCd();
			if(gakusekin==null){
				setInitGakInfo(pc);         
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0042E));
				//メッセージをセット
				return message;
				
			}
			
			//学籍番号が入力されていない場合 			
			if(gakusekiCd.equals("")){
				gakusekiCd = gakusekin;
				pc.getPropGakusekiCd().setStringValue(gakusekin); 
			}
			
			//学籍番号と振込依頼人コード両方入力されている場合、学籍番号が合致しない場合エラー
			if(!gakusekiCd.equals("")&&!gakusekiCd.equals(gakusekin)){
				setInitGakInfo(pc);         
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0042E));
				//メッセージをセット
				return message;
			}
			
		}
		
		
		//管理NO取得 
		UtilGhGakuseki ghUtilGakuseki = new UtilGhGakuseki();
		gakInfo = ghUtilGakuseki.getGakSotKanriNo(getDbs(),gakusekiCd);
		
		kanriNo = gakInfo[0];
		viewKbn = gakInfo[1];
		
		//--------------------------------------
		//学籍情報が存在しない場合
		//--------------------------------------
		if (viewKbn == null) {
			
			//メッセージをセット
			chkMsg.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0042E));
			UtilSystem.getDisplayInfo().setTargetFocusId(
					pc.getPropGakusekiCd().getId());
			
			//--------------------------------------
			//学籍VIEWに存在する場合
			//--------------------------------------
		} else if (GhConstant.GH_ZAIGAK.equals(viewKbn)) {
			
			//学生情報取得
			VGaksekiDAO vGaksekiDAO = 
				(VGaksekiDAO)getDbs().getDao(VGaksekiDAO.class);
			VGaksekiAR vGaksekiAR = 
				vGaksekiDAO.findByKanriNo(Long.parseLong(kanriNo));
			
			//管理番号
			pc.getPropKanriNo().setStringValue(kanriNo);
			//学生氏名
			pc.getPropGakseiName().setStringValue(vGaksekiAR.getName());
			
			//--------------------------------------
			//管理内出学者VIEWに存在する場合
			//--------------------------------------
		} else if ((GhConstant.GH_SYTGAK.equals(viewKbn))) {
			//DAO生成
			//卒業生VIEW
			VSotGaksekiDAO vSotGaksekiDAO = 
				(VSotGaksekiDAO)getDbs().getDao(VSotGaksekiDAO.class);
			//卒業生情報取得
			VSotGaksekiAR vSotGaksekiAR = 
				vSotGaksekiDAO.findByKanriNo(Long.parseLong(kanriNo));
			
			//管理番号
			pc.getPropKanriNo().setStringValue(kanriNo);
			//学生氏名
			pc.getPropGakseiName().setStringValue(vSotGaksekiAR.getName());
		}
		
		return chkMsg;
	}
	
	/**
	 * 配当済納付金一覧設定<br>
	 * 配当情報を取得し、一覧に設定する。
	 * <br>
	 * @param  pc		 	(Xrm00101T03)ページコード
	 * @return				(boolean) true：正常 false：異常
	 * @throws DbException
	 */
	private boolean setPaywList(Xrm00101T03 pc) throws DbException {
		
		try {
			
			//管理番号
			long kanriNo = Long.parseLong(pc.getPropKanriNo().getStringValue());
			
			//DAO生成
			//割当分納情報(結合DAO)
			JoinPaywBunInfoDAO joinPaywBunInfoDAO =
				(JoinPaywBunInfoDAO)getDbs().getDao(JoinPaywBunInfoDAO.class);
			
			List paywList = new ArrayList();
			if(!pc.getpropHurikomininCd().getStringValue().equals("")){		
				GhgPayhAR ghgPayhAR = null;
				XrmGhgFurikomiDAO xrmGhgFurikomiDAO =(XrmGhgFurikomiDAO)getDbs().getDao(XrmGhgFurikomiDAO.class);    			
				String hurikomiCd = pc.getpropHurikomininCd().getStringValue();
				//玉川大学は連番を使用しないため0固定
				GhgFurikomiAR ghgFurikomiAR = xrmGhgFurikomiDAO.findByPrimaryKey(hurikomiCd,0);
				JoinPaywBunInfoAR joinPaywBunInfoAR = joinPaywBunInfoDAO.findByNendoToBunkatsuNoNoNullPayEnd(ghgFurikomiAR.getNendo(),
						ghgFurikomiAR.getKanriNo(),
						ghgFurikomiAR.getPayCd(),
						ghgFurikomiAR.getPatternCd(),
						ghgFurikomiAR.getBunnoKbnCd(),
						ghgFurikomiAR.getBunkatsuNo());
				if(joinPaywBunInfoAR==null){
					//処理対象が存在しない場合エラー
					MessageList message = new MessageList();
					message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0032E));
					UtilSystem.getDisplayInfo().setDisplayMessage(
							message.getErrMsg());
					return false;
				}
				//納付金配当ARを取得
				ghgPayhAR = joinPaywBunInfoAR.getGhgPayhAR();
				ghgPayhAR = joinPaywBunInfoAR.getGhgPayhAR();
				if (ghgPayhAR == null) {
					//納付金配当が存在しない場合、処理対象外
					return false;
				} else {
					if(ghgPayhAR.getOutputKbn() 
							== ShutsuryokuHohoKbn.OUTTARGET.getCode()){
						//出力方法が出力しない場合、処理対象外
						UtilSystem.getDisplayInfo()
						.setDisplayMessage(ERR_NOT_SYORI);
						return false;
					}
				}
				paywList.add(joinPaywBunInfoAR);
			}else{
				//割当済納付金情報を取得する (管理番号指定検索)
				List wPaywList = joinPaywBunInfoDAO.findByKanriNoNullPayEnd(kanriNo);
				//出力方法区分チェック
				JoinPaywBunInfoAR joinPaywBunInfoAR = null;
				GhgPayhAR ghgPayhAR = null;
				for (int i = 0; i < wPaywList.size(); i ++) {
					joinPaywBunInfoAR = (JoinPaywBunInfoAR) wPaywList.get(i);
					//納付金配当ARを取得
					ghgPayhAR = joinPaywBunInfoAR.getGhgPayhAR();
					if (ghgPayhAR == null) {
						//納付金配当が存在しない場合、処理対象外
						continue;
					} else {
						if(ghgPayhAR.getOutputKbn() 
								== ShutsuryokuHohoKbn.OUTTARGET.getCode()){
							//出力方法が出力しない場合、処理対象外
							continue;
						}
					}
					paywList.add(joinPaywBunInfoAR);
				}
			}
			if (paywList.size() == 0) {
				//該当データが存在しない場合エラー
				MessageList message = new MessageList();
				message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0032E));
				//学籍番号にフォーカスをセット
				UtilSystem.getDisplayInfo().setTargetFocusId(
						pc.getPropGakusekiCd().getId());
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return false;
			}
			
			if (paywList.size() > 0) {
				//割当済納付金情報が存在する場合
				
				//データ件数のエラーチェック
				//iniファイルよりMax件数取得
				int WarningCount =
					Integer.parseInt(UtilIni.getParameter(
							GhConstant.GH_SECTION_MAX, GhConstant.GH_KEY_MAXSEARCH1));
				//iniファイルよりMax件数取得
				int ErrorCount =
					Integer.parseInt(UtilIni.getParameter(
							GhConstant.GH_SECTION_MAX, GhConstant.GH_KEY_MAXSEARCH2));
				
				if (paywList.size() > ErrorCount) {
					//検索結果がエラー件数以上の場合
					UtilSystem.getDisplayInfo().setDisplayMessage(
							UtilProperty.getMsgString(
									SyMsgConst.SY_MSG_0034E,String.valueOf(
											ErrorCount)));
					return false;
					
				} else if (paywList.size() > WarningCount  &&
						pc.getPropExecutableSearch()
						.getIntegerValue().intValue() == 0) {
					//検索結果がエラー件数未満かつ警告件数以上の場合
					UtilSystem.getDisplayInfo().setConfirmMessage(
							UtilProperty.getMsgString(
									SyMsgConst.SY_MSG_0016W,
									String.valueOf(paywList.size())));
					//二度押しチェックの為のボタンの初期化を行なう
					pc.getPropExecutableSearch()
					.setIntegerValue(new Integer(1));
					return false;
					
				} else {
					//二度押しチェックの為のボタンの初期化を行なう
					pc.getPropExecutableSearch()
					.setIntegerValue(new Integer(1));
				}
				
				//リストにデータをセット
				ArrayList payArrList = createPaywList(paywList);
				pc.getPropPaywList().setList(payArrList);
				UtilGhFormat.setRows(pc.getPropPaywList(),
						payArrList);
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		return true;
	}
	
	/**
	 * 割当済納付金一覧リスト作成<br>
	 * 配当情報を画面表示用に編集する。
	 * <br>
	 * @param  paywList			(List)割当済納付金一覧
	 * @return payhArrayList	(ArrayList)割当済納付金情報
	 * @throws DbException
	 */
	private ArrayList createPaywList(List paywList) throws DbException {
		
		//割当済納付金一覧リスト
		ArrayList paywArrayList = new ArrayList();		
		
		//延納情報DAO
		GhiEnnoDAO ghiEnnoDAO =
			(GhiEnnoDAO) getDbs().getDao(GhiEnnoDAO.class);
		
		//画面表示用Beanに値を設定する
		JoinPaywBunInfoAR joinPaywBunInfoAR = null;
		for (int i = 0; i < paywList.size(); i++) {
			
			joinPaywBunInfoAR = (JoinPaywBunInfoAR)paywList.get(i);
			
			int nendo = 0;
			String payCd = "";
			String patternCd = "";
			int bunnoKbnCd = 0;
			int bunkatsuNo = 0;
			String payLimit = "";
			String outputDate = "";
			if (joinPaywBunInfoAR != null) {
				nendo = joinPaywBunInfoAR.getNendo();
				payCd = joinPaywBunInfoAR.getPayCd();
				patternCd = joinPaywBunInfoAR.getPatternCd();
				bunnoKbnCd = joinPaywBunInfoAR.getBunnoKbnCd();
				bunkatsuNo = joinPaywBunInfoAR.getBunkatsuNo();
				Date payLimitDate = joinPaywBunInfoAR.getPayLimit();
				if (payLimitDate != null) {
					payLimit = UtilDate.editDate(payLimitDate, 2);
				}
				
				
				//延納者の場合は延納期限を設定する
				//最新の有効な延納データを取得
				GhiEnnoAR ghiEnnoAR = ghiEnnoDAO.findByActiveEnnoInfo(nendo,joinPaywBunInfoAR.getKanriNo(), 
						payCd,patternCd,bunnoKbnCd,bunkatsuNo);
				
				if (ghiEnnoAR != null) {
					//延納受理日
					Date ennouJuriDate = ghiEnnoAR.getEnnouJuriDate();
					//延納期限
					Date ennouLimit = ghiEnnoAR.getEnnouLimit();
					//延納完了日
					Date ennouKanryoDate = ghiEnnoAR.getEnnouKanryoDate();
					
					if (ennouKanryoDate == null
							&& ennouJuriDate != null) {
						//延納状態が｢受理｣の場合(完了日付が入力されていない場合)
						
						//納入期限に納付金延納.延納期限を設定
						payLimit = UtilDate.editDate(ennouLimit, 2);;
					}
				}
				
				Date outputDateDate = joinPaywBunInfoAR.getPayOutputDate();
				if (outputDateDate != null) {
					outputDate = UtilDate.editDate(outputDateDate, 2);
				}
			}
			
			//納付金配当ARを取得
			GhgPayhAR ghgPayhAR = joinPaywBunInfoAR.getGhgPayhAR();
			//納付金名称
			String payName = "";
			if (ghgPayhAR != null) {
				payName = ghgPayhAR.getPayName();
			}
			
			Xrm00101L01Bean bean = new Xrm00101L01Bean();
			
			//年度
			bean.setNendo(String.valueOf(nendo));
			//納付金コード
			bean.setPayCd(payCd);
			//パターンコード
			bean.setPatternCd(patternCd);
			//分納区分コード
			bean.setBunnoKbnCd(String.valueOf(bunnoKbnCd));
			//納付金名称
			bean.setPayName(payName);
			//分割NO
			bean.setBunkatsuNo(String.valueOf(bunkatsuNo));
			//納付期限
			bean.setPayLimit(payLimit);
			//出力日
			bean.setOutputDate(outputDate);
			
			paywArrayList.add(bean);
		}
		
		return paywArrayList;
	}
	
	/**
	 * アクション(解除)<br>
	 * ロック項目の解除を行う。
	 * <br>
	 * @param  pagecode (PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws DbException
	 */
	protected String unselect(PageCodeBaseEx pagecode) throws DbException {
		Xrm00101T03 pc = (Xrm00101T03)pagecode;
		
		//リスト初期化
		pc.getPropPaywList().setListbean(new Xrm00101L01Bean());
		pc.getPropPaywList().setList(new ArrayList());
		pc.getPropPaywList().setRows(0);
		
		//二度押しチェック
		pc.getPropExecutableSearch().setIntegerValue(new Integer(0));
		
		//学生氏名をクリア
		pc.getPropGakseiName().setStringValue("");
		
		//解除項目設定
		//学籍番号
		pc.getPropGakusekiCd().setDisabled(false);
		//振込依頼人コード
		pc.getpropHurikomininCd().setDisabled(false);
		
		//フォーカスセット
		UtilSystem.getDisplayInfo().setTargetFocusId(
				pc.getPropGakusekiCd().getId());
		
		//各ボタン
		pc.getPropActiveControlSearch().setIntegerValue(
				new Integer(GhConstant.GH_CTR_ENABLED));
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * アクション(実行)<br>
	 * 請求書を作成する。
	 * <br>
	 * @param  pagecode		(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 */
	protected String exec(PageCodeBaseEx pagecode) {
		Xrm00101T03 pc = (Xrm00101T03)pagecode;
		return execXrm00101BAT01(pc, ActionConst.ACTION_EXEC);
	}
	
	
	
	
	/**
	 * バッチ実行<br>
	 * バッチ処理を実行する。
	 * <br>
	 * @param  pc	  		(Xrm00101T03)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 */
	private String execXrm00101BAT01(Xrm00101T03 pc, String action) {
		
		try {
			ArrayList outPayhList = new ArrayList();
			MessageList message = new MessageList();
			
			
			Xrm00101BAT01 bat;
			CheckComponent cc = new CheckComponent();
			
			//発行日付
			cc.addComponent(pc.getpropHakkouDate());
			message =  cc.check();
			
			//通信欄(ぺイジー)を取得
			String strTsushinTextchk = pc.getPropTsushinText().getStringValue();
			//nullチェック
			if(strTsushinTextchk==null){
				strTsushinTextchk="";
			}	
			//OS依存の改行コードを取得
			String crlf = System.getProperty("line.separator");
			//通信欄(ぺイジー)を改行コードをキーに分割
			String[] aryTsushinTextChk = strTsushinTextchk.split(crlf);
			
			int lineByte = 0; // 1行のバイト数
			
			for (int j = 0; j < aryTsushinTextChk.length; j++) {
				
				//1行のバイト数を取得
				lineByte = aryTsushinTextChk[j].getBytes().length;
				
				//通信欄(ぺイジー) 1行の最大文字が32byteを超えている場合エラー				
				if (lineByte > 32) {
					
					//エラーメッセージに追加
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0015E, ERR_MSG_TSUSHIN_TEXT_1));
					
				}
				
			}
			
			//通信欄(ぺイジー) 最大行数が6行を超えている場合エラー
			if (aryTsushinTextChk.length > 6) {
				
				//エラーメッセージに追加
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0015E, ERR_MSG_TSUSHIN_TEXT_2));
				
			}
			//11/13追加：期限直接入力時、指定してない場合エラー
			if(pc.getPropKigenkbn().isChecked()){
				//有効期限、納入期限が存在していない場合
				if(pc.getPropNonyuDate().getDateValue()==null||pc.getPropYukoDate().getDateValue()==null){
					//エラーメッセージに追加
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0008E, ERR_MSG_KIGEN_TEXT_3));
				}else{
					//日付相関チェック
					String addMonth = "";
					String[] errMsg = new String[2];
					//EUC名称種別項目から学費の納入期限範囲を取得
					XrxMeiKanriKmkDAO xrxMeiKanriKmkDAO = (XrxMeiKanriKmkDAO)getDbs().getDao(XrxMeiKanriKmkDAO.class);
					List list = xrxMeiKanriKmkDAO.findBySbtGyomu(XrmConst.M_SBT_PAY_LIMIT, XrmConst.M_GYOMU_CD);
					if (list.size() != 0) {
						XrxMeiKanriKmkAR xrxMeiKanriKmkAR = (XrxMeiKanriKmkAR) list.get(0);
						addMonth = xrxMeiKanriKmkAR.getKmkName1();
					}			
					//エラーメッセージに追加
					errMsg[0] = ERR_MSG_PAYDATE_1 + "+" + addMonth + "ヶ月";
					errMsg[1] = ERR_MSG_YUKOULIMIT_1;
					
					Calendar cal = Calendar.getInstance();
					
					cal.setTime(pc.getPropNonyuDate().getDateValue());
					
					cal.add(Calendar.MONTH, Integer.parseInt(addMonth));
					
					cal.getTime();
					
					Date cmpDate1 = cal.getTime();
					Date cmpDate2 = pc.getPropYukoDate().getDateValue();
					
					//納入期限 + 学費の納入期限範囲 < 有効期限以外の場合エラー
					if (cmpDate1.compareTo(cmpDate2) <= 0) {
						
						message.add(UtilProperty.getMsgString(
								GhMsgConst.GH_MSG_0002E, errMsg));
					}
					//納入期限＞有効期限の場合エラー
					Date cmpDatenonyu= pc.getPropNonyuDate().getDateValue();
					Date cmpDateyuko = pc.getPropYukoDate().getDateValue();
					if(cmpDateyuko.compareTo(cmpDatenonyu) < 0){
						//エラーメッセージに追加
						errMsg = new String[2];
						errMsg[0] =ERR_MSG_YUKOULIMIT_1;
						errMsg[1] =  ERR_MSG_PAYDATE_1;
						message.add(UtilProperty.getMsgString(
								GhMsgConst.GH_MSG_0002E, errMsg));

					}

				}
			}			
			

			
			
			//納付金選択チェック
			List payList = createPayList(pc.getPropPaywList().getList());
			if ((payList == null || payList.size() == 0)) {
				//リストが空で、納付金出力条件指定が「一覧より選択」の場合
				//1件も選択されていない場合エラー
				message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0006E,NOCHECK_ERR_MSG));
				UtilSystem.getDisplayInfo().setDisplayMessage(message.getErrMsg());
				return ActionConst.RET_FALSE;
			}else{
				int cnt = 0;
				if (payList != null){
					//納付金の選択数チェック(1000件以上の選択はエラーとする)
					for (Iterator ite = payList.iterator(); ite.hasNext();) {
						Xrm00101L01Bean paywBean = (Xrm00101L01Bean) ite.next();
						if(paywBean.isPayChecked()){
							cnt++;
						}
						if(cnt > 999){
							//1000件以上なのでエラー
							message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0013E, "納付金は1000件以上"));
							break;
						}
					}
				}
			}
			if (message.size() > 0) {
				//エラーが存在する場合
				//エラーメッセージ出力
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			
			//学生指定区分
			String kbn = "GAK";
			
			bat = new Xrm00101BAT01(BatchConst.BATKBN_P, true);
			//選択納付金対象の管理NO取得
			Map kanriList = getGakuseiInfo(pc.getPropGakusekiCd().getStringValue());
			
			//帳票タイトル
			//帳票タイトルテーブルよりデータ取得
			UtilGhPdf ghUtilPdf = new UtilGhPdf();
			String title = ghUtilPdf.loadDefaultItemValuePdfTitle(
					this.getDbs(), Xrm00101BAT01.PDF_FILEID_CHKLIST);
			
			//出力条件を取得
			//発行日付(java.sql.date)
			java.sql.Date hakkoDate = UtilDate.cnvSqlDate(pc.getpropHakkouDate().getDateValue());
			
			//通信区分チェック
			boolean tsusinKbn = pc.getPropTushinKbn().isChecked();
			//出力区分
			String syutKbn = pc.getPropOutPutKbn().getStringValue();
			
			//学費年度
			int ghNendo = 0;
			
			//通信欄
			String[] aryTsushinText=null;
			if(pc.getPropTushinKbn().isChecked()){
				//通信欄(ぺイジー)を取得
				String strTsushinText = pc.getPropTsushinText().getStringValue();
				//通信欄(ぺイジー)を改行コードをキーに分割
				aryTsushinText = strTsushinText.split(crlf);
			}else{
				String strTsushinText = XrmUtilConvert.toNoNullStr(UtilIni.getProductParameter(XrmIniConst.XRM
						, XrmIniConst.SEIKYUUOUT[0]
												 , XrmIniConst.SEIKYUUOUT[1]));
				
				// INIファイルの改行コード(\n)をOS依存の改行コードにて置換
				strTsushinText = strTsushinText.replaceAll("\\\\n", crlf);
				
				aryTsushinText = strTsushinText.split(crlf);
			}
			//11/13追加：直接入力選択時、納入期限と有効期限を取得(非選択じはnullでバッチへ)
			java.sql.Date nonyuKigen =null;
			java.sql.Date yukoKigen = null;
			if(pc.getPropKigenkbn().isChecked()){
				nonyuKigen= UtilDate.cnvSqlDate(pc.getPropNonyuDate().getDateValue());
				yukoKigen= UtilDate.cnvSqlDate(pc.getPropYukoDate().getDateValue());
			}
			
			//バッチに出力条件を設定
			bat.setOutputConditions(kbn,
					title,
					hakkoDate,
					tsusinKbn,
					aryTsushinText,
					ghNendo,
					syutKbn,
					kanriList,
					payList,
					nonyuKigen,
					yukoKigen
			);
			//オプションテーブルにデータ追加
			saveDefaultItemValue(pc);
			
			
			if (BatchLogic.startBatch(bat)) {
				//バッチ実行確認画面呼び出し (画面オンロード時に新規ウィンドウでオープン)
				Cos00401.open(bat);
				
			} else {
				//「処理実行中です。詳細はバッチ詳細情報を参照して下さい。」
				UtilSystem.getDisplayInfo().setPopupMessage(
						UtilProperty.getMsgString(SyMsgConst.SY_MSG_0010I));
			}
			
			return ActionConst.RET_TRUE;
			
		} catch (Exception e) {
			throw new RuntimeException(e);
			
		}
	}
	/**
	 * バッチ処理対象配当リスト作成<br>
	 * 処理対象チェックがついている配当情報をバッチ引数のリストに設定する。
	 * <br>
	 * @param  payList			(ArrayList)一覧に表示されいている配当情報
	 * @return list			(ArrayList)処理対象の配当情報
	 * @throws DbException
	 */
	private ArrayList createPayList(ArrayList payList) {
		ArrayList list = new ArrayList();
		
		for (Iterator it = payList.iterator(); it.hasNext();) {
			Xrm00101L01Bean payBean = (Xrm00101L01Bean) it.next();
			if (payBean.isPayChecked()) {
				list.add(payBean);
			}
		}
		
		if (list.size() == 0) {
			list = null;
		}
		
		return list;
	}
	
	/**
	 * 学生検索ボタン押下 <br>
	 * 学生の検索を行う。
	 * <br>
	 * @param  pc			(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws DbException
	 */
	protected String popgaksearch(PageCodeBaseEx pagecode) throws DbException {
		// 学費学生検索子実行確認画面呼び出し (画面オンロード時に新規ウィンドウでオープン)
		Xrm00101T03 pc = (Xrm00101T03) pagecode;
		
		//子画面のセッション削除
		PGhz0301 nextSession = 
			(PGhz0301)UtilSystem.getManagedBean(PGhz0301.class);
		nextSession.removeFromSession();
		
		PGhz0301 nextPage = (PGhz0301) UtilSystem
		.getManagedBean(PGhz0301.class);
		
		//学籍番号
		nextPage.getPropGakusekino().setStringValue(
				pc.getPropGakusekiCd().getStringValue());
		
		//学籍番号ID
		nextPage.getPropGakusekiNoIdHidden().setStringValue(
				GhConstant.formId + pc.getPropGakusekiCd().getId());
		
		//共通管轄も検索対象とする
		//@@@@@ (F-SO-6880-00) JAST s.tokuda 2007/05/07 START
		/*
		 nextPage.getPropKankatuStudChkbox().setChecked(false);
		 */
		nextPage.getPropHiddenKankatuStudChkbox().setChecked(false);
		//@@@@@ (F-SO-6880-00) JAST s.tokuda 2007/05/07 END
		
		//卒業生は検索対象とする
		nextPage.getPropSotugyoChkbox().setChecked(false);
		nextPage.getPropSotugyoChkbox().setDisabled(false);
		
		//正常終了
		return PGhz0301.FORMID;
	}
	/**
	 * タブ遷移(納付金指定画面) <br>
	 * 納付金指定タブ画面へ遷移する
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws DbException
	 * @throws Exception
	 */
	protected String payTab(PageCodeBaseEx pagecode) throws DbException,Exception {
		
		//発行日付、通信区分、出力区分、納入区分を３画面で共有する。
		Xrm00101T03 pc = (Xrm00101T03)pagecode;
		
		Xrm00101T01 nextPage = (Xrm00101T01)UtilSystem.getManagedBean(Xrm00101T01.class);
		
		nextPage.setPropHakkouDate(pc.getpropHakkouDate());
		nextPage.setPropTsushinText(pc.getPropTsushinText());
		nextPage.setPropTushinKbn(pc.getPropTushinKbn());
		nextPage.setPropOutPutKbn(pc.getPropOutPutKbn());
		nextPage.setPropNonyuDate(pc.getPropNonyuDate());
		nextPage.setPropYukoDate(pc.getPropYukoDate());
		nextPage.setPropKigenkbn(pc.getPropKigenkbn());
		return Xrm00101T01.FORMID;
	}
	
	/**
	 * タブ遷移(納付金学生指定画面) <br>
	 * 納付金学生指定タブ画面へ遷移する
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws DbException
	 * @throws Exception
	 */
	protected String payGakTab(PageCodeBaseEx pagecode) throws DbException,Exception {
		
		//発行日付、通信区分、出力区分、納入区分を３画面で共有する。
		Xrm00101T03 pc = (Xrm00101T03)pagecode;
		
		Xrm00101T02 nextPage = (Xrm00101T02)UtilSystem.getManagedBean(Xrm00101T02.class);
		
		nextPage.setPropHakkouDate(pc.getpropHakkouDate());
		nextPage.setPropTsushinText(pc.getPropTsushinText());
		nextPage.setPropTushinKbn(pc.getPropTushinKbn());
		nextPage.setPropOutPutKbn(pc.getPropOutPutKbn());
		nextPage.setPropNonyuDate(pc.getPropNonyuDate());
		nextPage.setPropYukoDate(pc.getPropYukoDate());
		nextPage.setPropKigenkbn(pc.getPropKigenkbn());
		
		return Xrm00101T02.FORMID;
	}
	
	/**
	 *通信欄の活性、非活性を操作する<br>
	 *@param pagecode
	 */
	protected String clickcheakBox(PageCodeBaseEx pagecode){
		Xrm00101T03 pc = (Xrm00101T03) pagecode;
		if(pc.getPropTushinKbn().isChecked()){
			pc.getPropTsushinText().setDisabled(false);
			return ActionConst.RET_TRUE;
		}else{
			pc.getPropTsushinText().setDisabled(true);
			return ActionConst.RET_TRUE;
		}
	}
	protected Map getGakuseiInfo(String gakusekiCD) throws DbException{
		
		Map kanriGakMap = new HashMap();
		//該当学籍ビュー取得
		VGaksekiDAO vGaksekiDAO = (VGaksekiDAO)getDbs().getDao(VGaksekiDAO.class);
		VGaksekiAR vGaksekiAR = vGaksekiDAO.findByGakusekiCd(gakusekiCD);
		
		//管理NO取得
		long kanri = vGaksekiAR.getKanriNo();
		String kanriN=Long.toString(kanri);
		
		kanriGakMap.put(kanriN,kanriN);
		
		return kanriGakMap;
	}
	/**
	 *現在日付の取得
	 *@return java.sql.Date 
	 */
	public java.sql.Date nowDate(){
		//	現在日時をsqlDateで取得
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		// java.sql.Dateに変換
		java.sql.Date now = new java.sql.Date(cal.getTimeInMillis());	
		
		return now;
	}
	
	/**
	 * 今回入力値をセーブ<br>
	 * 今回入力値をオプションテーブルに保存する。
	 * <br>
	 * @param  pc 		(Xrm00101T01)ページコード
	 * @throws DbException
	 */
	private void saveDefaultItemValue(Xrm00101T03 pc)
	throws DbException {
		
		UtilCosOpt utilOpt = new UtilCosOpt(
				getDbs(),
				UtilSystem.getMySystemData().getLoginID(),
				UtilSystem.getFuncIdFromFormId(pc.getFormId()),
				Integer.parseInt(
						pc.getFormId().substring(pc.getFormId().length() - 1))
		);
		//データを先読みしてDAOのレコードキャッシュに格納
		utilOpt.preLoad();
		
		//@@@@@ (F-UK-345-00) JAST s.hase 2006/09/13 START
		
		//出力区分
		//		utilOpt.setValue(pc.getPropOutPutKbn().getId(),pc.getPropOutPutKbn().getStringValue());
		
	}
	/**
	 * 前回入力値をロード<br>
	 * 前回入力した値をオプションテーブルから取得し画面に反映する。<br>
	 * オプションテーブルにデータが存在しない場合は、初期値を設定する。<br>
	 * (帳票タイトルについては帳票タイトルより名称を取得し設定する)
	 * <br>
	 * @param  pc 	(Xrm00101T01)ページコード
	 * @throws DbException
	 */
	private void loadDefaultItemValueOption(Xrm00101T03 pc)
	throws DbException {
		
		UtilCosOpt utilOpt = new UtilCosOpt(
				getDbs(),
				UtilSystem.getMySystemData().getLoginID(),
				UtilSystem.getFuncIdFromFormId(pc.getFormId()),
				Integer.parseInt(
						pc.getFormId().substring(pc.getFormId().length() - 1))
		);
		//データを先読みしてDAOのレコードキャッシュに格納
		utilOpt.preLoad();
		
		//出力区分
		//		pc.getPropOutPutKbn().setValue(utilOpt.getValue(pc.getPropOutPutKbn().getId()));
		
	}
	/**
	 * 学生情報初期化<br>
	 * 学生情報の初期化を行う。
	 * <br>
	 * @param  pc			(Xrm00101T03)ページコード
	 */
	private void setInitGakInfo(Xrm00101T03 pc) {
		
		//学生情報
		pc.getPropGakseiName().setStringValue("");
	}
	/**
	 *期限の活性、非活性を操作する<br>
	 *@param pagecode
	 */
	protected String clickKigencheakBox(PageCodeBaseEx pagecode){
		Xrm00101T03 pc = (Xrm00101T03) pagecode;
		if(pc.getPropKigenkbn().isChecked()){
			pc.getPropNonyuDate().setDisabled(false);
			//			pc.getPropNonyuDate().setReadonly(false);
			pc.getPropYukoDate().setDisabled(false);
			//			pc.getPropYukoDate().setReadonly(false);
			return ActionConst.RET_TRUE;
		}else{
			pc.getPropNonyuDate().setDisabled(true);
			//			pc.getPropNonyuDate().setReadonly(true);
			pc.getPropYukoDate().setDisabled(true);
			//			pc.getPropYukoDate().setReadonly(true);
			return ActionConst.RET_TRUE;
		}
	}
	
}
