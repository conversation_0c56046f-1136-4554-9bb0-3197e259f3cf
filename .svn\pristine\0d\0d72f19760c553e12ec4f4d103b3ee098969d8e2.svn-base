<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cog05701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>部門按分組合せ検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_0(message) {
 var param = new Array("入力項目");
 var newMessage = messageCreate(message, param);
 if (confirm(newMessage) == true) {
  // ＯＫボタンを押された
  return true;
 } else {
  // キャンセルボタンを押された
  return false
 }
}


function func_2(thisObj, thisEvent) {
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById('form1:htmlStartKmkCd').value;
	args['code3'] = '0';
	var target = "form1:htmlStartKmkName";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_3(thisObj, thisEvent) {
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById('form1:htmlEndKmkCd').value;
	args['code3'] = '0';
	var target = "form1:htmlEndKmkName";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_4(thisObj, thisEvent) {
	var servlet = "rev/co/CogYosanTaniAJAX";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById('form1:htmlStartYsnTCd').value;
	args['code3'] = '0';
	var target = "form1:htmlStartYsnTName";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_5(thisObj, thisEvent) {
	var servlet = "rev/co/CogYosanTaniAJAX";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById('form1:htmlEndYsnTCd').value;
	args['code3'] = '0';
	var target = "form1:htmlEndYsnTName";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_6(thisObj, thisEvent) {
	var servlet = "rev/co/CogYosanMokuAJAX";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById('form1:htmlStartMokuCd').value;
	args['code3'] = '0';
	var target = "form1:htmlStartMokuName";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_7(thisObj, thisEvent) {
	var servlet = "rev/co/CogYosanMokuAJAX";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = document.getElementById('form1:htmlEndMokuCd').value;
	args['code3'] = '0';
	var target = "form1:htmlEndMokuName";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";	
	indirectClick('search');	
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";	
}

function func_8(thisObj, thisEvent) {
	openModalWindow("", "PCog0101", "<%=com.jast.gakuen.rev.co.PCog0101.getWindowOpenOption() %>");
	setTarget("PCog0101");
	return true;
}

function func_9(thisObj, thisEvent) {
	openModalWindow("", "PCog0201", "<%=com.jast.gakuen.rev.co.PCog0201.getWindowOpenOption() %>");
	setTarget("PCog0201");
	return true;
}

function func_10(thisObj, thisEvent) {
	openModalWindow("", "PCog0301", "<%=com.jast.gakuen.rev.co.PCog0301.getWindowOpenOption() %>");
	setTarget("PCog0301");
	return true;
}
function copy_mokutekiTo(thisObj, thisEvent) {
	// 目的コード(from)を目的コード(to)にコピーする
	if(document.getElementById("form1:htmlEndMokuCd").value == ""){
			document.getElementById("form1:htmlEndMokuCd").value = document.getElementById("form1:htmlStartMokuCd").value;
			func_7(document.getElementById("form1:htmlEndMokuCd"), "");
	}
}
function copy_kamokuTo(thisObj, thisEvent) {
	// 科目コード(from)を科目コード(to)にコピーする
	if(document.getElementById("form1:htmlEndKmkCd").value == ""){
			document.getElementById("form1:htmlEndKmkCd").value = document.getElementById("form1:htmlStartKmkCd").value;
			func_3(document.getElementById("form1:htmlEndKmkCd"), "");
	}
}
function copy_yosanTo(thisObj, thisEvent) {
	// 予算単位コード(from)を予算単位コード(to)にコピーする
	if(document.getElementById("form1:htmlEndYsnTCd").value == ""){
			document.getElementById("form1:htmlEndYsnTCd").value = document.getElementById("form1:htmlStartYsnTCd").value;
			func_5(document.getElementById("form1:htmlEndYsnTCd"), "");
	}
}
function onLoad(){
	// ページ遷移時のイベントを設定	
	func_2('','');	
	func_3('','');
	func_4('','');
	func_5('','');
	func_6('','');
	func_7('','');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="return onLoad();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cog05701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cog05701.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cog05701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cog05701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="新規登録" styleClass="commandExButton"
				id="register" action="#{pc_Cog05701.doRegisterAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" width="80%"
				class="table" style="margin-top:30px">
				<TBODY>
					<TR>
						<TH colspan="2" class="v_a" width="202"><h:outputText
							styleClass="outputText" id="lblKaikeiNendo"
							value="#{pc_Cog05701.propKaikeiNendo.labelName}"
							style="#{pc_Cog05701.propKaikeiNendo.labelStyle}"></h:outputText></TH>
						<TD width="548"><h:inputText styleClass="inputText"
							id="htmlKaikeiNendo"
							value="#{pc_Cog05701.propKaikeiNendo.dateValue}" size="4"
							style="#{pc_Cog05701.propKaikeiNendo.style}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH colspan="2" class="v_b" width="202"><h:outputText
							styleClass="outputText" id="lblSknShohiKbn"
							value="#{pc_Cog05701.propSknShohiKbn.labelName}"
							style="#{pc_Cog05701.propSknShohiKbn.labelStyle}"></h:outputText></TH>
						<TD width="548"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSknShohiKbn"
							value="#{pc_Cog05701.propSknShohiKbn.stringValue}">
							<f:selectItem itemValue="1" itemLabel="資金収支" />
							<f:selectItem itemValue="2" itemLabel="消費収支" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TD colspan="2" class="group_label" width="180"><h:outputText
							id="lblYsnTCd" value="#{pc_Cog05701.propYsnTCd.labelName}"
							styleClass="outputText"></h:outputText></TD>
						<TD class="" width="548"></TD>
					</TR>
					<TR height="" class="">
						<TD class="group_label" width="5"></TD>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblStartYsnTCd" value="#{pc_Cog05701.propStartYsnTCd.name}"></h:outputText></TH>
						<TD width="548" nowrap>
							<DIV style="width:548px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText styleClass="inputText"
									id="htmlStartYsnTCd" size="12"
									value="#{pc_Cog05701.propStartYsnTCd.stringValue}"
									maxlength="#{pc_Cog05701.propStartYsnTCd.maxLength}"
									onblur="func_4(this, event); copy_yosanTo(this, event);"
									style="#{pc_Cog05701.propStartYsnTCd.style}"></h:inputText><hx:commandExButton
									type="submit" styleClass="commandExButton_search"
									id="searchYsnTStart" onclick="return func_8(this, event);"
									action="#{pc_Cog05701.doButtonYsnTStartAction}"></hx:commandExButton>
								<h:outputText
									styleClass="outputText" id="htmlStartYsnTName"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TD class="group_label_bottom"></TD>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblEndYsnTCd" value="#{pc_Cog05701.propEndYsnTCd.name}"></h:outputText></TH>
						<TD width="548" nowrap>
							<DIV style="width:548px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText styleClass="inputText"
									id="htmlEndYsnTCd" size="12"
									value="#{pc_Cog05701.propEndYsnTCd.stringValue}"
									style="#{pc_Cog05701.propEndYsnTCd.style}"
									maxlength="#{pc_Cog05701.propEndYsnTCd.maxLength}"
									onblur="return func_5(this, event);"></h:inputText><hx:commandExButton
									type="submit" styleClass="commandExButton_search"
									id="searchYsnTEnd" onclick="return func_8(this, event);"
									action="#{pc_Cog05701.doButtonYsnTEndAction}"></hx:commandExButton>
								<h:outputText
									styleClass="outputText" id="htmlEndYsnTName"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR style="${pc_Cog05701.trMokutek}">
						<TD colspan="2" class="group_label" width="202"><h:outputText
							styleClass="outputText" id="lblMukoCd"
							value="#{pc_Cog05701.propMokuCd.labelName}"></h:outputText></TD>
						<TD class="" width="548"></TD>
					</TR>
					<TR style="${pc_Cog05701.trMokutek}">
						<TD class="group_label"></TD>
						<TH class="v_e"><h:outputText styleClass="outputText"
							id="lblStartMokuCd" value="#{pc_Cog05701.propStartMokuCd.name}"></h:outputText></TH>
						<TD width="548" nowrap>
							<DIV style="width:548px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText styleClass="inputText"
									id="htmlStartMokuCd" size="12"
									value="#{pc_Cog05701.propStartMokuCd.stringValue}"
									maxlength="#{pc_Cog05701.propStartMokuCd.maxLength}"
									style="#{pc_Cog05701.propStartMokuCd.style}"
									onblur="func_6(this, event); copy_mokutekiTo(this, event);"></h:inputText><hx:commandExButton
									type="submit" styleClass="commandExButton_search"
									id="searchStartMoku" onclick="return func_9(this, event);"
									action="#{pc_Cog05701.doButtonMokuStartAction}"></hx:commandExButton>
								<h:outputText
									styleClass="outputText" id="htmlStartMokuName"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR style="${pc_Cog05701.trMokutek}">
						<TD class="group_label_bottom"></TD>
						<TH class="v_f"><h:outputText styleClass="outputText"
							id="lblEndMokuCd" value="#{pc_Cog05701.propEndMokuCd.name}"></h:outputText></TH>
						<TD width="548" nowrap>
							<DIV style="width:548px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText styleClass="inputText"
									id="htmlEndMokuCd" size="12"
									value="#{pc_Cog05701.propEndMokuCd.stringValue}"
									maxlength="#{pc_Cog05701.propEndMokuCd.maxLength}"
									style="#{pc_Cog05701.propEndMokuCd.style}"
									onblur="return func_7(this, event);"></h:inputText><hx:commandExButton
									type="submit" styleClass="commandExButton_search"
									id="searchEndMoku" onclick="return func_9(this, event);"
									action="#{pc_Cog05701.doButtonMokuEndAction}"></hx:commandExButton>
								<h:outputText
									styleClass="outputText" id="htmlEndMokuName"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TD colspan="2" class="group_label" width="202"><h:outputText
							styleClass="outputText" id="lblKmkCd"
							value="#{pc_Cog05701.propKmkCd.labelName}"></h:outputText></TD>
						<TD class="" width="548"></TD>
					</TR>
					<TR>
						<TD class="group_label"></TD>
						<TH class="v_g"><h:outputText styleClass="outputText"
							id="lblStartKmkCd" value="#{pc_Cog05701.propStartKmkCd.name}"></h:outputText></TH>
						<TD width="548" nowrap>
							<DIV style="width:548px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText styleClass="inputText"
									id="htmlStartKmkCd" size="12"
									style="#{pc_Cog05701.propStartKmkCd.style}"
									maxlength="#{pc_Cog05701.propStartKmkCd.maxLength}"
									value="#{pc_Cog05701.propStartKmkCd.stringValue}"
									onblur="func_2(this, event); copy_kamokuTo(this, event);"></h:inputText><hx:commandExButton
									type="submit" styleClass="commandExButton_search"
									id="searchStartKmk" onclick="return func_10(this, event);"
									action="#{pc_Cog05701.doButtonKmkStartAction}"></hx:commandExButton>
								<h:outputText
									styleClass="outputText" id="htmlStartKmkName"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TD class="group_label_bottom"></TD>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblEndKmkCd" value="#{pc_Cog05701.propEndKmkCd.name}"></h:outputText></TH>
						<TD width="548" nowrap>
							<DIV style="width:548px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText styleClass="inputText"
									id="htmlEndKmkCd" size="12"
									value="#{pc_Cog05701.propEndKmkCd.stringValue}"
									maxlength="#{pc_Cog05701.propEndKmkCd.maxLength}"
									style="#{pc_Cog05701.propEndKmkCd.style}"
									onblur="return func_3(this, event);"></h:inputText><hx:commandExButton
									type="submit" styleClass="commandExButton_search"
									id="searchEndKmk" onclick="return func_10(this, event);"
									action="#{pc_Cog05701.doButtonKmkEndAction}"></hx:commandExButton>
								<h:outputText
									styleClass="outputText" id="htmlEndKmkName"></h:outputText>
							</DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE><BR>

			<TABLE border="0" style="margin-top:10px" class="button_bar"
				width="80%">
				<TBODY>
					<TR>
						<TD width="40%"></TD>
						<TD align="center"><hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search"
							action="#{pc_Cog05701.doSearchAction}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_dat" id="clear"
							action="#{pc_Cog05701.doClearAction}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit" value="CSV作成"
							styleClass="commandExButton_out" id="csvout"
							confirm="#{msg.SY_MSG_0020W}"
							action="#{pc_Cog05701.doCsvoutAction}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit"
							value="出力項目指定" styleClass="commandExButton_out" id="setoutput"
							action="#{pc_Cog05701.doSetoutputAction}"></hx:commandExButton></TD>

						<TD width="40%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Cog05701.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

