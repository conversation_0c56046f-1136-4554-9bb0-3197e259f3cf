<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb40301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@ page import="com.jast.gakuen.framework.property.Checkable"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="../ke/inc/gakuenKE.css">

<style type="text/css">
<!--
 .setWidth TD {width: 80px; white-space: nowrap;}
-->
</style>

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">
//予算単位の「ク」ボタンクリック時
function func_1(thisObj, thisEvent) {
	//リストボックスの選択行をクリアする
	clearListBox("form1:htmlYsnTCd");
}
//フォームのサブミット時
//FWからsubmit時にコールバック
function submitMethod() {
	//予算単位リストボックの内容を保管
	//（※storeListBox関数はgakuenKE.jsに含まれる）
	storeListBox("form1:htmlYsnTCd","form1:htmlYsnTCdHidden");
	return true;
}
//帳票タイトルの表示
function func_2(thisObj, thisEvent) {
	//取引区分
	var torihikiKbn = document.getElementById('form1:htmlTorihikiKbn').value;
	if (torihikiKbn == "<%=Checkable.NO_SELECT_VALUE%>") {
		document.getElementById('form1:htmlTitle').value = "";
	}
	//帳票種類
	var chohyoShurui = document.getElementById('form1:htmlChohyoShurui').value;
	if (chohyoShurui == "<%=Checkable.NO_SELECT_VALUE%>") {
		document.getElementById('form1:htmlTitle').value = "";
	}
	//帳票タイトル
	var titleId = 'form1:htmlTitle' + '_' + chohyoShurui + '_' + torihikiKbn;
	if (document.getElementById(titleId) != null) {
		document.getElementById('form1:htmlTitle').value = document.getElementById(titleId).value;
	}
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="return func_2(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb40301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
	<hx:commandExButton
		styleClass="commandExButton"
		type="submit"
		id="closeDisp"
		value="閉じる"
		action="#{pc_Keb40301.doCloseDispAction}">
	</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb40301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb40301.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText
	id="message" 
	value="#{requestScope.DISPLAY_INFO.displayMessage}" 
	styleClass="outputText" 
	escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
		<TABLE border="0" cellspacing="0" cellpadding="0" class="table" width="700">
			<TBODY>
				<TR>
					<TH class="v_a" width="120">
						<h:outputText
							styleClass="outputText" 
							id="lblKaikeiNendo" 
							style="#{pc_Keb40301.propKaikeiNendo.labelStyle}" 
							value="#{pc_Keb40301.propKaikeiNendo.labelName}" 
							rendered="#{pc_Keb40301.propKaikeiNendo.rendered}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText
							styleClass="inputText" 
							id="htmlKaikeiNendo" 
							size="5"
							tabindex="1"
							value="#{pc_Keb40301.propKaikeiNendo.dateValue}" 
							style="#{pc_Keb40301.propKaikeiNendo.style}" 
							disabled="#{pc_Keb40301.propKaikeiNendo.disabled}" 
							readonly="#{pc_Keb40301.propKaikeiNendo.readonly}" 
							rendered="#{pc_Keb40301.propKaikeiNendo.rendered}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_b">
						<h:outputText
							styleClass="outputText" 
							id="lblKaikeiTsuki" 
							style="#{pc_Keb40301.propKaikeiTsuki.labelStyle}" 
							value="#{pc_Keb40301.propKaikeiTsuki.labelName}" 
							rendered="#{pc_Keb40301.propKaikeiTsuki.rendered}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText styleClass="inputText" id="htmlKaikeiTsuki" size="3"
							tabindex="2" value="#{pc_Keb40301.propKaikeiTsuki.dateValue}"
							style="#{pc_Keb40301.propKaikeiTsuki.style}"
							disabled="#{pc_Keb40301.propKaikeiTsuki.disabled}"
							readonly="#{pc_Keb40301.propKaikeiTsuki.readonly}"
							rendered="#{pc_Keb40301.propKaikeiTsuki.rendered}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime pattern="MM" />
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_c">
						<h:outputText
							styleClass="outputText"
							id="lblShimeKbn"
							value="#{pc_Keb40301.propShimeKbn.labelName}"
							style="#{pc_Keb40301.propShimeKbn.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlShimeKbn"
							tabindex="3"
							disabled="#{pc_Keb40301.propShimeKbn.disabled}"
							rendered="#{pc_Keb40301.propShimeKbn.rendered}"
							value="#{pc_Keb40301.propShimeKbn.stringValue}"
							style="#{pc_Keb40301.propShimeKbn.style}">
							<f:selectItems value="#{pc_Keb40301.propShimeKbn.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_d">
						<h:outputText
							styleClass="outputText"
							id="lblTorihikiKbn"
							value="#{pc_Keb40301.propTorihikiKbn.labelName}"
							style="#{pc_Keb40301.propTorihikiKbn.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlTorihikiKbn"
							tabindex="4"
							onchange="return func_2(this, event);"
							disabled="#{pc_Keb40301.propTorihikiKbn.disabled}"
							rendered="#{pc_Keb40301.propTorihikiKbn.rendered}"
							value="#{pc_Keb40301.propTorihikiKbn.stringValue}"
							style="#{pc_Keb40301.propTorihikiKbn.style}">
							<f:selectItems value="#{pc_Keb40301.propTorihikiKbn.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_e">
						<h:outputText
							styleClass="outputText"
							id="lblChohyoShurui"
							value="#{pc_Keb40301.propChohyoShurui.labelName}"
							style="#{pc_Keb40301.propChohyoShurui.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlChohyoShurui"
							tabindex="5"
							onchange="return func_2(this, event);"
							disabled="#{pc_Keb40301.propChohyoShurui.disabled}"
							rendered="#{pc_Keb40301.propChohyoShurui.rendered}"
							value="#{pc_Keb40301.propChohyoShurui.stringValue}"
							style="#{pc_Keb40301.propChohyoShurui.style}">
							<f:selectItems value="#{pc_Keb40301.propChohyoShurui.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_f">
						<h:outputText
							styleClass="outputText"
							id="lblKaikeiTCd"
							value="#{pc_Keb40301.propKaikeiTCd.labelName}"
							style="#{pc_Keb40301.propKaikeiTCd.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlKaikeiTCd"
							tabindex="6"
							disabled="#{pc_Keb40301.propKaikeiTCd.disabled}"
							rendered="#{pc_Keb40301.propKaikeiTCd.rendered}"
							value="#{pc_Keb40301.propKaikeiTCd.stringValue}"
							style="#{pc_Keb40301.propKaikeiTCd.style}">
							<f:selectItems value="#{pc_Keb40301.propKaikeiTCd.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_g">
						<h:outputText
							styleClass="outputText" 
							id="lblGknSumKbn"
							style="#{pc_Keb40301.propGknSumKbn.labelStyle}" 
							value="#{pc_Keb40301.propGknSumKbn.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlGknSumKbn"
							tabindex="7"
							value="#{pc_Keb40301.propGknSumKbn.value}"
							style="#{pc_Keb40301.propGknSumKbn.style}"
							disabled="#{pc_Keb40301.propGknSumKbn.disabled}"
							readonly="#{pc_Keb40301.propGknSumKbn.readonly}"
							rendered="#{pc_Keb40301.propGknSumKbn.rendered}">
							<f:selectItems value="#{pc_Keb40301.propGknSumKbn.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_b" valign="middle">
						<h:outputText
							styleClass="outputText"
							id="lblYsnTCd"
							value="#{pc_Keb40301.propYsnTCd.labelName}"
							style="#{pc_Keb40301.propYsnTCd.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<TABLE class="clear_border" border="0" cellpadding="0" cellspacing="0" width="100%" height="60">
							<TBODY>
								<TR>
									<TD>
										<h:selectManyListbox
											styleClass="selectManyListbox"
											id="htmlYsnTCd"
											size="4"
											tabindex="8"
											disabled="#{pc_Keb40301.propYsnTCd.disabled}"
											rendered="#{pc_Keb40301.propYsnTCd.rendered}"
											value="#{pc_Keb40301.propYsnTCd.stringValue}"
											style="#{pc_Keb40301.propYsnTCd.style}; width: 515px">
											<f:selectItems value="#{pc_Keb40301.propYsnTCd.list}" />
										</h:selectManyListbox>
									</TD>	
									<TD height="60">
										<TABLE border="0" cellpadding="0" cellspacing="0" width="20" height="60">
											<TR>
												<TD height="17">
													<hx:commandExButton
														styleClass="commandExButton_search"
														type="submit"
														id="searchYsnTCd"
														tabindex="9"
														action="#{pc_Keb40301.doSearchYsnTCdAction}">
													</hx:commandExButton>
												</TD>
											</TR>
											<TR>
												<TD height="36">
												</TD>
											</TR>
											<TR>
												<TD height="17">
													<hx:commandExButton
														styleClass="commandExButton_listclear"
														type="button"
														id="clearYsnTCd"
														tabindex="10"
														onclick="return func_1(this, event);">
													</hx:commandExButton>
												</TD>
											</TR>
										</TABLE>
									</TD>
									<TD width="120"></TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="">
						<h:outputText
							styleClass="outputText" 
							id="lblChushutsuJoken" 
							style="#{pc_Keb40301.propChushutsuJoken.labelStyle}" 
							value="#{pc_Keb40301.propChushutsuJoken.labelName}" 
							rendered="#{pc_Keb40301.propChushutsuJoken.rendered}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneRadio
							styleClass="selectOneRadio setWidth" 
							disabledClass="selectOneRadio_Disabled" 
							id="htmlChushutsuJoken"
							tabindex="11"
							value="#{pc_Keb40301.propChushutsuJoken.value}"
							style="#{pc_Keb40301.propChushutsuJoken.style}"
							disabled="#{pc_Keb40301.propChushutsuJoken.disabled}"
							readonly="#{pc_Keb40301.propChushutsuJoken.readonly}"
							rendered="#{pc_Keb40301.propChushutsuJoken.rendered}">
							<f:selectItems value="#{pc_Keb40301.propChushutsuJoken.list}" />
						</h:selectOneRadio>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="120">
						<h:outputText
							styleClass="outputText"
							id="lblKamokLvl"
							value="#{pc_Keb40301.propKamokLvl.labelName}"
							style="#{pc_Keb40301.propKamokLvl.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlKamokLvl"
							tabindex="12"
							disabled="#{pc_Keb40301.propKamokLvl.disabled}"
							rendered="#{pc_Keb40301.propKamokLvl.rendered}"
							value="#{pc_Keb40301.propKamokLvl.stringValue}"
							style="#{pc_Keb40301.propKamokLvl.style}">
							<f:selectItems value="#{pc_Keb40301.propKamokLvl.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="120">
					<h:outputText styleClass="outputText" id="lblZandakaHanei"
							value="#{pc_Keb40301.propZandakaHanei.labelName}"></h:outputText></TH>
					<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlZandakaHanei"
							disabled="#{pc_Keb40301.propZandakaHanei.disabled}"
							readonly="#{pc_Keb40301.propZandakaHanei.readonly}"
							rendered="#{pc_Keb40301.propZandakaHanei.rendered}"
							style="#{pc_Keb40301.propZandakaHanei.style}"
							value="#{pc_Keb40301.propZandakaHanei.checked}"></h:selectBooleanCheckbox>反映する（取引区分が資金収支、帳票種類が会計単位別の場合のみ）</TD>
				</TR>
			</TBODY>
		</TABLE>
		<BR>
		<TABLE border="0" cellspacing="0" cellpadding="0" class="table" width="700">
			<TBODY>
				<TR>
					<TH class="v_e" width="120">
						<h:outputText
							styleClass="outputText" id="lblTitle"
							value="#{pc_Keb40301.propTitle.labelName}"
							style="#{pc_Keb40301.propTitle.labelStyle}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText
							styleClass="inputText"
							id="htmlTitle"
							size="80"
							tabindex="13"
							disabled="#{pc_Keb40301.propTitle.disabled}"
							maxlength="#{pc_Keb40301.propTitle.maxLength}"
							readonly="#{pc_Keb40301.propTitle.readonly}"
							rendered="#{pc_Keb40301.propTitle.rendered}"
							value="#{pc_Keb40301.propTitle.stringValue}"
							style="#{pc_Keb40301.propTitle.style}">
						</h:inputText>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		<BR>
		<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="700">
			<TBODY>
				<TR>
					<TD>
						<hx:commandExButton
							styleClass="commandExButton_out"
							type="submit"
							id="pdfout"
							tabindex="14"
							disabled="#{pc_Keb40301.propPdfout.disabled}"
							rendered="#{pc_Keb40301.propPdfout.rendered}"
							style="#{pc_Keb40301.propPdfout.style}"
							value="PDF作成"
							confirm="#{msg.SY_MSG_0019W}"
							action="#{pc_Keb40301.doPdfoutAction}">
						</hx:commandExButton>
						<hx:commandExButton 
							styleClass="commandExButton_out" 
							type="submit"
							id="excelout" 
							tabindex="15"
							disabled="#{pc_Keb40301.propExcelout.disabled}"
							rendered="#{pc_Keb40301.propExcelout.rendered}"
							value="EXCEL作成" 
							confirm="#{msg.SY_MSG_0027W}" 
							action="#{pc_Keb40301.doExcelOutAction}">
						</hx:commandExButton>
						<hx:commandExButton
							styleClass="commandExButton_out"
							type="submit"
							id="csvout"
							tabindex="16"
							disabled="#{pc_Keb40301.propCsvout.disabled}"
							rendered="#{pc_Keb40301.propCsvout.rendered}"
							style="#{pc_Keb40301.propCsvout.style}"
							value="CSV作成"
							confirm="#{msg.SY_MSG_0020W}"
							action="#{pc_Keb40301.doCsvoutAction}">
						</hx:commandExButton>
						<hx:commandExButton
							styleClass="commandExButton_out"
							type="submit"
							id="setoutput"
							tabindex="17"
							disabled="#{pc_Keb40301.propSetoutput.disabled}"
							rendered="#{pc_Keb40301.propSetoutput.rendered}"
							style="#{pc_Keb40301.propSetoutput.style}"
							value="出力項目指定"
							action="#{pc_Keb40301.doSetoutputAction}">
						</hx:commandExButton>
						<hx:commandExButton
							styleClass="commandExButton_out"
							type="submit"
							id="print"
							tabindex="18"
							rendered="#{pc_Keb40301.propPrint.rendered}" 
							disabled="#{pc_Keb40301.propPrint.disabled}" 
							style="#{pc_Keb40301.propPrint.style}" 
							value="印刷"
							confirm="#{msg.SY_MSG_0022W}"
							action="#{pc_Keb40301.doPrintAction}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Keb40301.propYsnTCdHidden.stringValue}"
				id="htmlYsnTCdHidden"></h:inputHidden>
			<c:forEach items="${pc_Keb40301.titleMap}" var="form_title">
				<input type=hidden id="${form_title.key}" name="${form_title.key}" value="${form_title.value}">
			</c:forEach>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
