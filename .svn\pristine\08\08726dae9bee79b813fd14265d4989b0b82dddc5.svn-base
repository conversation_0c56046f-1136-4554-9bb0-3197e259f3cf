<%-- 
	卒業生情報登録（専攻クラス）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob01502T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
	if(changeDataFlg == "1"){
	  return confirm(id);
	}
	return true;
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
	if (document.getElementById('form1:htmlHidShoriKbn').value == "update") {
		var kengenCheckFlg = document.getElementById('form1:htmlHidKengenCheckFlg').value;
		if (kengenCheckFlg == "1"){
			//更新後の学籍が権限なしとなるが、処理続行
			document.getElementById("form1:htmlHidButtonKbnAuth").value = "1";
		}
		// 卒業生成績見直しワーニングである場合は、OKをセット
		var sotSskRevDispFlg = document.getElementById('form1:htmlHidSotSskRevDispFlg').value;
		if (sotSskRevDispFlg == "1") {
			document.getElementById("form1:htmlHidSotSskRevBtnFlg").value = "1";
		}
	} else {
		document.getElementById("form1:htmlHidButtonKbnDelete").value = "1";
	}
	var action = document.getElementById("form1:htmlHidShoriKbn").value;
	indirectClick(action);
}

// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById("form1:htmlHidButtonKbnAuth").value = "0";
	document.getElementById("form1:htmlHidKengenCheckFlg").value = "0";
	document.getElementById("form1:htmlHidButtonKbnDelete").value = "0";
	document.getElementById("form1:htmlHidSotSskRevBtnFlg").value = "0";
	document.getElementById("form1:htmlHidSotSskRevDispFlg").value = "0";
}

</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob01502T05.onPageLoadBegin}">
<gakuen:itemStateCtrlDef managedbean="pc_Cob01502T01" property="cob01502" />
<gakuen:itemStateCtrl managedbean="pc_Cob01502T05">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob01502T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob01502T05.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob01502T05.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
			<hx:commandExButton
				type="submit" value="プロファイル"
				styleClass="commandExButton" id="referProfile"
				disabled="#{pc_Cob01502T01.cob01502.propFreeEdit.disabled}"
				action="#{pc_Cob01502T05.doReferProfileAction}">
			</hx:commandExButton>
			<hx:commandExButton
				type="submit" value="自由設定" styleClass="commandExButton"
				id="freeEdit"
				disabled="#{pc_Cob01502T01.cob01502.propFreeEdit.disabled}"
				action="#{pc_Cob01502T05.doFreeEditAction}"></hx:commandExButton> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');"
				action="#{pc_Cob01502T05.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置--></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="950">
						<TABLE class="table" width="100%">
							<TBODY>
								<TR align="center" valign="middle">
									<TH nowrap class="v_a" width="180"><h:outputText styleClass="outputText"
										id="lblGaksekiCd_head"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.labelName}"></h:outputText></TH>
									<TD width="180"><h:inputText styleClass="inputText"
										id="htmlGaksekiCd"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.value}"
										disabled="#{pc_Cob01502T01.cob01502.propGaksekiCd.disabled}"
										style="#{pc_Cob01502T01.cob01502.propGaksekiCd.style}"
										maxlength="#{pc_Cob01502T01.cob01502.propGaksekiCd.maxLength}"
										size="20"
										onchange="onChangeData()"></h:inputText></TD>
									<TH nowrap class="v_a" width="180"><h:outputText styleClass="outputText"
										id="lblGakseiName"
										value="#{pc_Cob01502T01.cob01502.propGakseiName.name}"></h:outputText></TH>
									<TD nowrap><h:outputText styleClass="outputText"
										id="htmlGakseiName"
										value="#{pc_Cob01502T01.cob01502.propGakseiName.value}"></h:outputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSotNendo"
										value="#{pc_Cob01502T01.cob01502.propSotNendo.name}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="htmlSotNendo"
										value="#{pc_Cob01502T01.cob01502.propSotNendo.value}"></h:outputText></TD>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSotGakki"
										value="#{pc_Cob01502T01.cob01502.propSotGakki.name}"></h:outputText></TH>
									<TD nowrap><h:outputText styleClass="outputText"
										id="htmlSotGakki"
										value="#{pc_Cob01502T01.cob01502.propSotGakki.value}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
											style="border-bottom-style: none; ">
											<TBODY>
												<TR>
												<TD width="60px"><hx:commandExButton 
													type="submit" styleClass="tab_head_off" id="tabCob01502T01" style="width:100%"
													value="基本" action="#{pc_Cob01502T05.doTabCob01502T01Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T02" style="width:100%"
													value="出身地" action="#{pc_Cob01502T05.doTabCob01502T02Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T03" style="width:100%"
													value="所属１" action="#{pc_Cob01502T05.doTabCob01502T03Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T04" style="width:100%"
													value="所属２" action="#{pc_Cob01502T05.doTabCob01502T04Action}"></hx:commandExButton></TD>
												<TD width="*"><hx:commandExButton
													type="button" styleClass="tab_head_on" id="tabCob01502T05" style="width:100%"
													value="専攻クラス"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T06" style="width:100%"
													value="現住所" action="#{pc_Cob01502T05.doTabCob01502T06Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T07" style="width:100%"
													value="帰省先" action="#{pc_Cob01502T05.doTabCob01502T07Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T08" style="width:100%"
													value="卒業時" action="#{pc_Cob01502T05.doTabCob01502T08Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T09" style="width:100%"
													value="保証人" action="#{pc_Cob01502T05.doTabCob01502T09Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T11" style="width:100%"
													value="教員" action="#{pc_Cob01502T05.doTabCob01502T11Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T12" style="width:100%"
													value="ゼミ卒論" action="#{pc_Cob01502T05.doTabCob01502T12Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T13" style="width:100%"
													value="異動" action="#{pc_Cob01502T05.doTabCob01502T13Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T14" style="width:100%"
													value="出身校" action="#{pc_Cob01502T05.doTabCob01502T14Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T15" style="width:100%"
													value="入学" action="#{pc_Cob01502T05.doTabCob01502T15Action}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T16" style="width:100%"
													value="資格" action="#{pc_Cob01502T05.doTabCob01502T16Action}"
													disabled="#{pc_Cob01502T01.cob01502.propSikakBtn.disabled}"></hx:commandExButton></TD>
												<TD width="60px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabCob01502T18" style="width:100%"
													value="その他" action="#{pc_Cob01502T05.doTabCob01502T18Action}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" 
										width="100%" style="border-top-style: none; ">
										<TR align="center">
											<TD>
											<div style="height: 400px"><BR>
											<TABLE border="0" cellpadding="0" cellspacing="0" width="822">
												<TR>
													<TD align="center">
													<TABLE width="822" border="0">
														<TR>
															<TD align="left" width="50%"></TD>
															<TD align="right" width="50%"><h:outputText
																styleClass="outputText" id="lblSnkCount"
																value="#{pc_Cob01502T05.propSnkCorList.listCount}件"></h:outputText>
															</TD>
														</TR>
													</TABLE>
													</TD>
												</TR>
												<TR>
													<TD align="center" width="822">
													<DIV class="listScroll" style="height: 100px">
														<h:dataTable border="1" cellpadding="2" cellspacing="0"
														headerClass="headerClass" footerClass="footerClass"
														columnClasses="columnClass1"
														rowClasses="#{pc_Cob01502T05.propSnkCorList.rowClasses}"
														styleClass="meisai_scroll" id="htmlSnkCorList" var="varlist"
														value="#{pc_Cob01502T05.propSnkCorList.list}">
														<h:column id="columnS1">
															<f:facet name="header">
																<h:outputText styleClass="outputText"
																	value="並び順"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText"
																id="lblSnkList_RowNo" value="#{varlist.rowNo}"></h:outputText>
															<f:attribute value="text-align: right" name="style" />
															<f:attribute value="55" name="width" />
														</h:column>
														<h:column id="columnS2">
															<f:facet name="header">
																<h:outputText id="lblSnkList_SbtCd_head"
																	styleClass="outputText"
																	value="#{pc_Cob01502T05.proplblSnkList_SbtCd.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText"
																id="lblSnkList_SbtCd" value="#{varlist.snkSbtCd}"></h:outputText>
															<f:attribute value="100" name="width" />
														</h:column>
														<h:column id="columnS3">
															<f:facet name="header">
																<h:outputText styleClass="outputText"
																	id="lblSnkList_SnkCorSbt_head"
																	value="#{pc_Cob01502T05.proplblSnkList_SnkCorSbt.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText"
																id="lblSnkList_SnkCorSbt"
																value="#{varlist.dispT05SnkSbtName}"
																title="#{varlist.snkSbtName}"></h:outputText>
															<f:attribute value="305" name="width" />
														</h:column>
														<h:column id="columnS4">
															<f:facet name="header">
																<h:outputText styleClass="outputText"
																	id="lblSnkList_CorCd_head"
																	value="#{pc_Cob01502T05.proplblSnkList_CorCd.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText"
																id="lblSnkList_CorCd" value="#{varlist.snkCd}"></h:outputText>
															<f:attribute value="110" name="width" />
														</h:column>
														<h:column id="columnS5">
															<f:facet name="header">
																<h:outputText styleClass="outputText"
																	id="lblSnkList_SnkCor_head"
																	value="#{pc_Cob01502T05.proplblSnkList_SnkCor.name}"></h:outputText>
															</f:facet>
															<f:attribute value="302" name="width" />
															<h:outputText styleClass="outputText"
																id="lblSnkList_SnkCor" value="#{varlist.dispT05SnkName}"
																title="#{varlist.snkName}"></h:outputText>
														</h:column>
													</h:dataTable><BR>
													</DIV>
													</TD>
												</TR>
											</TABLE>
											<TABLE class="button_bar" cellspacing="1" cellpadding="1"
												width="822">
												<TBODY>
													<TR>
														<TD align="center"><hx:commandExButton type="submit"
															value="編集" styleClass="commandExButton_dat" id="snkoEdit"
															action="#{pc_Cob01502T05.doSnkoEditAction}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE border="0" cellpadding="0" cellspacing="0">
												<TR>
													<TD align="center">
													<TABLE width="822" border="0">
														<TR>
															<TD align="left" width="50%"></TD>
															<TD align="right" width="50%"><h:outputText
																styleClass="outputText" id="lblClsCount"
																value="#{pc_Cob01502T05.propClsList.listCount}件"></h:outputText>
															</TD>
														</TR>
													</TABLE>
													</TD>
												</TR>
												<TR>
													<TD align="center" width="822">
													<DIV class="listScroll" style="height: 100px"><h:dataTable
														border="1" cellpadding="2" cellspacing="0"
														columnClasses="columnClass1" headerClass="headerClass"
														footerClass="footerClass"
														rowClasses="#{pc_Cob01502T05.propClsList.rowClasses}"
														styleClass="meisai_scroll" id="htmlClsList"
														value="#{pc_Cob01502T05.propClsList.list}" var="varlist">
														<h:column id="columnC1">
															<f:facet name="header">
																<h:outputText id="lblClsList_SbtCd_head"
																	styleClass="outputText"
																	value="#{pc_Cob01502T05.proplblClsList_SbtCd.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText"
																id="lblClsList_SbtCd" value="#{varlist.clsSbtCd}"></h:outputText>
															<f:attribute value="105" name="width" />
														</h:column>
														<h:column id="columnC2">
															<f:facet name="header">
																<h:outputText styleClass="outputText"
																	id="lblClsList_ClsSbt_head"
																	value="#{pc_Cob01502T05.proplblClsList_ClsSbt.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText"
																id="lblClsList_ClsSbt"
																value="#{varlist.dispT05ClsSbtName}"
																title="#{varlist.clsSbtName}"></h:outputText>
															<f:attribute value="305" name="width" />
														</h:column>
														<h:column id="columnC3">
															<f:facet name="header">
																<h:outputText styleClass="outputText"
																	id="lblClsList_ClsCd_head"
																	value="#{pc_Cob01502T05.proplblClsList_ClsCd.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText"
																id="lblClsList_ClsCd" value="#{varlist.clsCd}"></h:outputText>
															<f:attribute value="110" name="width" />
														</h:column>
														<h:column id="columnC4">
															<f:facet name="header">
																<h:outputText styleClass="outputText"
																	id="lblClsList_Cls_head"
																	value="#{pc_Cob01502T05.proplblClsList_Cls.name}"></h:outputText>
															</f:facet>
															<f:attribute value="302" name="width" />
															<h:outputText styleClass="outputText" id="lblClsList_Cls"
																value="#{varlist.dispT05ClsName}"
																title="#{varlist.clsName}"></h:outputText>
														</h:column>
													</h:dataTable><BR>
													</DIV>
													</TD>
												</TR>
											</TABLE>
											<TABLE class="button_bar" cellspacing="1" cellpadding="1"
												width="822">
												<TBODY>
													<TR>
														<TD align="center"><hx:commandExButton type="submit"
															value="編集" styleClass="commandExButton_dat" id="clsedit"
															action="#{pc_Cob01502T05.doClsEditAction}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
											</div>
											</TD>
										</TR>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE align="center" class="button_bar" cellspacing="1" cellpadding="1" width="100%">
										<TBODY>
											<TR>
												<TD align="center"><hx:commandExButton
													type="submit" value="確定" styleClass="commandExButton_dat"
													id="kakutei"
													rendered="#{pc_Cob01502T01.cob01502.propKakutei.rendered}"
													action="#{pc_Cob01502T05.doKakuteiAction}"></hx:commandExButton>
													<hx:commandExButton type="submit" value="更新" styleClass="commandExButton_dat"
													id="update"
													confirm="#{msg.SY_MSG_0003W}"
													rendered="#{pc_Cob01502T01.cob01502.propUpdate.rendered}"
													action="#{pc_Cob01502T05.doUpdateAction}"></hx:commandExButton>
													<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
													id="delete"
													confirm="#{msg.SY_MSG_0004W}"
													rendered="#{pc_Cob01502T01.cob01502.propDelete.rendered}"
													action="#{pc_Cob01502T05.doDeleteAction}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlHidKanriNo"
				value="#{pc_Cob01502T01.cob01502.propHidKanriNo.longValue}"></h:inputHidden>
			<h:inputHidden id="htmlHidErrMessage"
				value="#{pc_Cob01502T01.cob01502.propHidErrMessage.value}"></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Cob01502T01.cob01502.propHidChangeDataFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidShoriKbn"  value="#{pc_Cob01502T01.cob01502.propHidShoriKbn.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidButtonKbnAuth" value="#{pc_Cob01502T01.cob01502.propHidButtonKbnAuth.integerValue}" ><f:convertNumber /></h:inputHidden>
			<h:inputHidden id="htmlHidKengenCheckFlg" value="#{pc_Cob01502T01.cob01502.propHidKengenCheckFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidButtonKbnDelete" value="#{pc_Cob01502T01.cob01502.propHidButtonKbnDelete.integerValue}" ><f:convertNumber /></h:inputHidden>
			<h:inputHidden id="htmlHidSotSskRevDispFlg" value="#{pc_Cob01502T01.cob01502.propHidSotSskRevDispFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidSotSskRevBtnFlg" value="#{pc_Cob01502T01.cob01502.propHidSotSskRevBtnFlg.integerValue}" ><f:convertNumber /></h:inputHidden>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
