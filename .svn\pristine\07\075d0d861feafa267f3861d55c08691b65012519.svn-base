<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmd00302T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmd00302T05.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
}

// 確認メッセージでOKが押下された場合の処理
function confirmOk() {

	// ボタン押下フラグをセットし、同じ処理が流れないようにする
	var nowBtn = document.getElementById('form1:executable').value;
	document.getElementById('form1:executable').value = eval(nowBtn) + 1;

	if (document.getElementById('form1:register')) {
		indirectClick('register');
	} else if (document.getElementById('form1:update')) {
		indirectClick('update');
	}
}

// 確認メッセージでキャンセルが押下された場合の処理
function confirmCancel() {
	// ボタン押下フラグをクリアする
	document.getElementById('form1:executable').value = 0;
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
	if(changeDataFlg == "1"){
		return doPopupMsg(id);
	}else{
		return true;
	}
	return true;
}

// データチェンジ時
function onCangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmd00302T05.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmd00302T05.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmd00302T05.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmd00302T05.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right">
						<hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="24" action="#{pc_Kmd00302T05.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここに戻るボタンを配置 --></DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="800" valign="top">
							<!--↓タブ間共有テーブル↓-->
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText
												styleClass="outputText" id="lblKanriBusyoName"
												value="#{pc_Kmd00302T01.kmd00302.propKanriBusyoName.labelName}" 
												style="#{pc_Kmd00302T01.kmd00302.propKanriBusyoName.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:outputText styleClass="outputText" id="htmlKanriBusyoName" 
												value="#{pc_Kmd00302T01.kmd00302.propKanriBusyoName.stringValue}"
												style="#{pc_Kmd00302T01.kmd00302.propKanriBusyoName.style}">
											</h:outputText>
										</TD>
									<TD width="100" rowspan="3"
										style="background-color: transparent; text-align: right"
										class="clear_border"><hx:commandExButton type="submit"
										value="確定" styleClass="commandExButton" id="determinateJugyo"
										action="#{pc_Kmd00302T05.doDeterminateJugyoAction}"
										tabindex="2"
										disabled="#{pc_Kmd00302T01.kmd00302.propDeterminateJugyo.disabled}"
										style="#{pc_Kmd00302T01.kmd00302.propDeterminateJugyo.style}"
										rendered="#{pc_Kmd00302T01.kmd00302.propDeterminateJugyo.rendered}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="解除" styleClass="commandExButton" id="releaseJugyo"
										action="#{pc_Kmd00302T05.doReleaseJugyoAction}" tabindex="3"
										style="#{pc_Kmd00302T01.kmd00302.propReleaseJugyo.style}"
										disabled="#{pc_Kmd00302T01.kmd00302.propReleaseJugyo.disabled}"
										rendered="#{pc_Kmd00302T01.kmd00302.propReleaseJugyo.rendered}"
										confirm="#{msg.SY_MSG_0014W}">
									</hx:commandExButton></TD>
								</TR>
									<TR>
										<TH class="v_b" width="150">
											<h:outputText styleClass="outputText" id="lblTargetNendo" 
												value="#{pc_Kmd00302T01.kmd00302.propTargetNendo.labelName}" 
												style="#{pc_Kmd00302T01.kmd00302.propTargetNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:outputText styleClass="outputText" id="htmlTargetNendo" 
												value="#{pc_Kmd00302T01.kmd00302.propTargetNendo.stringValue}"
												style="#{pc_Kmd00302T01.kmd00302.propTargetNendo.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="150">
											<h:outputText styleClass="outputText" id="lblJugyoCd"
												value="#{pc_Kmd00302T01.kmd00302.propJugyoCd.labelName}"
												style="#{pc_Kmd00302T01.kmd00302.propJugyoCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputText styleClass="inputText" id="htmlJugyoCd"
												onchange="onCangeData();" 
												value="#{pc_Kmd00302T01.kmd00302.propJugyoCd.stringValue}" 
												size="10" tabindex="1"
												disabled="#{pc_Kmd00302T01.kmd00302.propJugyoCd.disabled}"
												readonly="#{pc_Kmd00302T01.kmd00302.propJugyoCd.readonly}"
												maxlength="#{pc_Kmd00302T01.kmd00302.propJugyoCd.maxLength}"
												style="#{pc_Kmd00302T01.kmd00302.propJugyoCd.style}">
											</h:inputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<!--↑タブ間共有テーブル↑-->
							<BR>
							<!--↓タブ用テーブル↓-->
							<TABLE border="0" cellpadding="20" cellspacing="0">
								<TBODY>
									<TR>
										<TD width="800" align="left">
										<TABLE border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveKihonTab"
															value="基本情報" styleClass="tab_head_off" tabindex="4"
															action="#{pc_Kmd00302T05.doMoveKihonTabAction}"
															disabled="#{pc_Kmd00302T01.kmd00302.propMoveKihonTab.disabled}"
															style="#{pc_Kmd00302T01.kmd00302.propMoveKihonTab.style}">
														</hx:commandExButton>
													</TD>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveSeigenTab"
															value="受講制限情報" styleClass="tab_head_off" tabindex="5"
															action="#{pc_Kmd00302T05.doMoveSeigenTabAction}"
															disabled="#{pc_Kmd00302T01.kmd00302.propMoveSeigenTab.disabled}"
															style="#{pc_Kmd00302T01.kmd00302.propMoveSeigenTab.style}">
														</hx:commandExButton>
													</TD>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveSiteTab"
															value="受講指定情報" styleClass="tab_head_off" tabindex="6"
															action="#{pc_Kmd00302T05.doMoveSiteTabAction}"
															disabled="#{pc_Kmd00302T01.kmd00302.propMoveSiteTab.disabled}"
															style="#{pc_Kmd00302T01.kmd00302.propMoveSiteTab.style}">
														</hx:commandExButton>
													</TD>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveKomaTab"
															value="授業コマ情報" styleClass="tab_head_off" tabindex="7"
															action="#{pc_Kmd00302T05.doMoveKomaTabAction}"
															disabled="#{pc_Kmd00302T01.kmd00302.propMoveKomaTab.disabled}"
															style="#{pc_Kmd00302T01.kmd00302.propMoveKomaTab.style}">
														</hx:commandExButton>
													</TD>
													<TD class="tab_head_on">
														<hx:commandExButton type="button" id="moveSikenTab"
															value="試験・出欠情報" styleClass="tab_head_on" tabindex="8"
															disabled="#{pc_Kmd00302T01.kmd00302.propMoveSikenTab.disabled}"
															style="#{pc_Kmd00302T01.kmd00302.propMoveSikenTab.style}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TD valign="top">
											<TABLE border="0" cellpadding="0" cellspacing="0"
												class="tab_body" width="100%">
												<TBODY>
													<TR>
														<TD valign="top" height="370">
															<BR>
															<CENTER>
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	class="table" width="720">
																	<TBODY>
																		<TR>
																			<TH class="v_a" width="150">
																				<h:outputText styleClass="outputText" 
																					id="lblSyuKetuKanriFlg" 
																					value="#{pc_Kmd00302T05.propSyuKetuKanriFlg.labelName}" 
																					style="#{pc_Kmd00302T05.propSyuKetuKanriFlg.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:selectOneRadio
																					disabledClass="selectOneRadio_Disabled"
																					onchange="onCangeData();"
																					styleClass="selectOneRadio" id="htmlSyuKetuKanriFlg"
																					value="#{pc_Kmd00302T05.propSyuKetuKanriFlg.value}"
																					layout="lineDirection"
																					disabled="#{pc_Kmd00302T05.propSyuKetuKanriFlg.disabled}"
																					tabindex="9"
																					readonly="#{pc_Kmd00302T05.propSyuKetuKanriFlg.readonly}">
																					<f:selectItems
																						value="#{pc_Kmd00302T05.propSyuKetuKanriFlg.list}" />
																				</h:selectOneRadio>
																			</TD>
																		</TR>
																		<TR>
																			<TH class="v_b" width="150">
																				<h:outputText styleClass="outputText" 
																					id="lblSaitenKbn" 
																					value="#{pc_Kmd00302T05.propSaitenKbn.labelName}" 
																					style="#{pc_Kmd00302T05.propSaitenKbn.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:selectOneRadio
																					disabledClass="selectOneRadio_Disabled"
																					onchange="onCangeData();"
																					styleClass="selectOneRadio" id="htmlSaitenKbn"
																					value="#{pc_Kmd00302T05.propSaitenKbn.value}"
																					layout="lineDirection"
																					disabled="#{pc_Kmd00302T05.propSaitenKbn.disabled}"
																					tabindex="10"
																					readonly="#{pc_Kmd00302T05.propSaitenKbn.readonly}">
																					<f:selectItems
																						value="#{pc_Kmd00302T05.propSaitenKbn.list}" />
																				</h:selectOneRadio>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
																<HR width="100%" class="hr" noshade>
																<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="720">
																	<TBODY>
																		<TR>
																			<TH class="v_c" width="150">
																				<h:outputText styleClass="outputText"
																					id="lblTeikiSiken"
																					value="#{pc_Kmd00302T05.propTeikiSiken.labelName}" 
																					style="#{pc_Kmd00302T05.propTeikiSiken.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD width="330">
																				<h:selectOneRadio
																					disabledClass="selectOneRadio_Disabled"
																					onchange="onCangeData();"
																					styleClass="selectOneRadio" id="htmlTeikiSiken"
																					value="#{pc_Kmd00302T05.propTeikiSiken.value}"
																					layout="lineDirection"
																					disabled="#{pc_Kmd00302T05.propTeikiSiken.disabled}"
																					tabindex="11">
																					<f:selectItems
																						value="#{pc_Kmd00302T05.propTeikiSiken.list}" />
																				</h:selectOneRadio>
																			</TD>
																			<TD>
																				<hx:commandExButton type="submit" value="追加"
																					onclick="onCangeData();"
																					styleClass="commandExButton" id="add"
																					action="#{pc_Kmd00302T05.doAddAction}"
																					disabled="#{pc_Kmd00302T05.propAdd.disabled}"
																					rendered="#{pc_Kmd00302T05.propAdd.rendered}"
																					style="#{pc_Kmd00302T05.propAdd.style}" 
																					tabindex="12">
																				</hx:commandExButton>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
																<!-- ↓データテーブル部↓ -->
																<TABLE border="0" cellpadding="5" width="720">
																	<TBODY>
													                    <TR>
													                        <TD align="right">
													                        	<h:outputText
																					styleClass="outputText" id="htmlSikenListCount"
																					value="#{pc_Kmd00302T05.propSikenListCount.stringValue}"
																					style="#{pc_Kmd00302T05.propSikenListCount.style}">
																				</h:outputText>
													                        </TD>
													                    </TR>
																		<TR>
																			<TD>
																				<DIV style="height:105px" class="listScroll">
																					<h:dataTable border="0" cellpadding="2"
																						cellspacing="0" columnClasses="columnClass"
																						headerClass="headerClass" footerClass="footerClass"
																						rowClasses="#{pc_Kmd00302T05.propSikenList.rowClasses}"
																						styleClass="meisai_scroll" id="htmlSikenList"
																						width="720" value="#{pc_Kmd00302T05.propSikenList.list}"
																						var="varlist">
																						<h:column id="htmlKaikoNendoColumn">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText" value="開講年度"
																									id="lblKaikoNendoDetail">
																								</h:outputText>
																							</f:facet>
																							<h:outputText id="htmlKaikoNendoDetail"
																								value="#{varlist.kaikoNendo}" style="width: 60px">
																							</h:outputText>
																							<f:attribute value="60" name="width" />
																						</h:column>
																						<h:column id="htmlKaikoGakkiNoColumn">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText"
																									id="lblKaikoGakkiNoDetail" value="学期ＮＯ">
																								</h:outputText>
																							</f:facet>
																							<h:outputText id="htmlKaikoGakkiNoDetail"
																								value="#{varlist.kaikoGakkiNo}" style="width: 50px">
																							</h:outputText>
																							<f:attribute value="50" name="width" />
																						</h:column>
																						<h:column id="htmlSikenKbnColumn">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText"
																									id="lblSikenKbnDetail" value="試験区分">
																								</h:outputText>
																							</f:facet>
																							<h:outputText id="htmlSikenKbnDetail"
																								value="#{varlist.sikenKbnName}" style="width: 160px">
																							</h:outputText>
																							<f:attribute value="160" name="width" />
																						</h:column>
																						<h:column id="htmlSikenKaisuColumn">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText"
																									id="lblSikenKaisuDetail" value="試験回数">
																								</h:outputText>
																							</f:facet>
																							<h:outputText id="htmlSikenKaisuDetail"
																								value="#{varlist.sikenKaisu}" style="width: 60px">
																							</h:outputText>
																							<f:attribute value="60" name="width" />
																						</h:column>
																						<h:column id="htmlSyusekiRituMinColumn">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText"
																									id="lblSyusekiRituMinDetail" value="下限出席率">
																								</h:outputText>
																							</f:facet>
																							<h:outputText id="htmlSyusekiRituMinDetail"
																								value="#{varlist.syusekiRituMin}" style="width: 70px">
																							</h:outputText>
																							<f:attribute value="70" name="width" />
																						</h:column>
																						<h:column id="htmlSyusekiMinColumn">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText"
																									id="lblSyusekiMinDetail" value="下限出席数">
																								</h:outputText>
																							</f:facet>
																							<h:outputText id="htmlSyusekiMinDetail"
																								value="#{varlist.syusekiMin}" style="width: 70px">
																							</h:outputText>
																							<f:attribute value="70" name="width" />
																						</h:column>
																						<h:column id="htmlSaitenboColumn">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText"
																									id="lblSaitenboDetail" value="採点簿出力対象">
																								</h:outputText>
																							</f:facet>
																							<h:outputText styleClass="outputText"
																								id="htmlSaitenboDetail"
																								value="#{varlist.dispSaitenboFlg}" style="width: 95px">
																							</h:outputText>
																							<f:attribute value="95" name="width" />
																							<f:attribute value="text-align: center" name="style" />
																						</h:column>
																						<h:column id="column10">
																							<f:facet name="header">
																							</f:facet>
																							<hx:commandExButton type="submit" value="選択"
																								onclick="onCangeData();"
																								styleClass="commandExButton" id="select" tabindex="13"
																								style="width: 35px"
																								action="#{pc_Kmd00302T05.doSelectAction}">
																							</hx:commandExButton>
																							<f:attribute value="35" name="width" />
																						</h:column>
																					</h:dataTable>
																				</DIV>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
																<!-- ↑データテーブル部↑ -->
																<HR width="100%" class="hr" noshade>
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	class="table" width="720">
																	<TBODY>
																		<TR>
																			<TH class="v_a" width="150">
																				<h:outputText styleClass="outputText" id="lblKaikoNendo"
																					value="#{pc_Kmd00302T05.propKaikoNendo.labelName}"
																					style="#{pc_Kmd00302T05.propKaikoNendo.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText styleClass="inputText"
																					id="htmlKaikoNendo"
																					onblur="onCangeData();"
																					value="#{pc_Kmd00302T05.propKaikoNendo.dateValue}"
																					size="4" tabindex="14"
																					disabled="#{pc_Kmd00302T05.propKaikoNendo.disabled}"
																					readonly="#{pc_Kmd00302T05.propKaikoNendo.readonly}"
																					style="#{pc_Kmd00302T05.propKaikoNendo.style}"
																					maxlength="#{pc_Kmd00302T05.propKaikoNendo.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
																				</h:inputText>
																			</TD>
																			<TH class="v_a" width="150">
																				<h:outputText styleClass="outputText"
																					id="lblKaikoGakkiNo"
																					value="#{pc_Kmd00302T05.propKaikoGakkiNo.labelName}"
																					style="#{pc_Kmd00302T05.propKaikoGakkiNo.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText styleClass="inputText"
																					id="htmlKaikoGakkiNo"
																					onblur="onCangeData();"
																					value="#{pc_Kmd00302T05.propKaikoGakkiNo.integerValue}"
																					size="2" tabindex="15"
																					disabled="#{pc_Kmd00302T05.propKaikoGakkiNo.disabled}"
																					readonly="#{pc_Kmd00302T05.propKaikoGakkiNo.readonly}"
																					style="#{pc_Kmd00302T05.propKaikoGakkiNo.style}"
																					maxlength="#{pc_Kmd00302T05.propKaikoGakkiNo.maxLength}">
																					<f:convertNumber type="number" pattern="#0"/>
																					<hx:inputHelperAssist errorClass="inputText_Error"
																						promptCharacter="_" />
																				</h:inputText>
																			</TD>
																		</TR>
																		<TR>
																			<TH class="v_b" width="150">
																				<h:outputText styleClass="outputText" id="lblSikenKbnCombo" 
																					value="#{pc_Kmd00302T05.propSikenKbnCombo.labelName}" 
																					style="#{pc_Kmd00302T05.propSikenKbnCombo.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:selectOneMenu styleClass="selectOneMenu"
																id="htmlSikenKbnCombo"
																onchange="onCangeData();"
																value="#{pc_Kmd00302T05.propSikenKbnCombo.value}"
																tabindex="16"
																disabled="#{pc_Kmd00302T05.propSikenKbnCombo.disabled}"
																style="#{pc_Kmd00302T05.propSikenKbnCombo.style};width:170px"
																readonly="#{pc_Kmd00302T05.propSaitenKbn.readonly}">
																<f:selectItems
																	value="#{pc_Kmd00302T05.propSikenKbnCombo.list}" />
															</h:selectOneMenu>
																			</TD>
																			<TH class="v_b" width="150">
																				<h:outputText styleClass="outputText" id="lblSikenKaisu"
																					value="#{pc_Kmd00302T05.propSikenKaisu.labelName}"
																					style="#{pc_Kmd00302T05.propSikenKaisu.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText styleClass="inputText"
																					id="htmlSikenKaisu"
																					onblur="onCangeData();"
																					value="#{pc_Kmd00302T05.propSikenKaisu.integerValue}"
																					size="2" tabindex="17"
																					disabled="#{pc_Kmd00302T05.propSikenKaisu.disabled}"
																					readonly="#{pc_Kmd00302T05.propSikenKaisu.readonly}"
																					style="#{pc_Kmd00302T05.propSikenKaisu.style}"
																					maxlength="#{pc_Kmd00302T05.propSikenKaisu.maxLength}">
																					<f:convertNumber type="number" pattern="#0"/>
																					<hx:inputHelperAssist errorClass="inputText_Error"
																						promptCharacter="_" />
																				</h:inputText>
																			</TD>
																		</TR>
																		<TR>
																			<TH class="v_c" width="150">
																				<h:outputText styleClass="outputText" id="lblSyusekiRituMin"
																					value="#{pc_Kmd00302T05.propSyusekiRituMin.labelName}" 
																					style="#{pc_Kmd00302T05.propSyusekiRituMin.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText styleClass="inputText"
																					id="htmlSyusekiRituMin"
																					onblur="onCangeData();"
																					value="#{pc_Kmd00302T05.propSyusekiRituMin.floatValue}"
																					size="3" tabindex="18"
																					disabled="#{pc_Kmd00302T05.propSyusekiRituMin.disabled}"
																					readonly="#{pc_Kmd00302T05.propSyusekiRituMin.readonly}"
																					style="#{pc_Kmd00302T05.propSyusekiRituMin.style}"
																					maxlength="#{pc_Kmd00302T05.propSyusekiRituMin.maxLength}">
																					<f:convertNumber type="number" pattern="#0.#"/>
																					<hx:inputHelperAssist errorClass="inputText_Error"
																						promptCharacter="_" />
																				</h:inputText>
																			</TD>
																			<TH class="v_c" width="150">
																				<h:outputText styleClass="outputText" id="lblSyusekiMin"
																					value="#{pc_Kmd00302T05.propSyusekiMin.labelName}" 
																					style="#{pc_Kmd00302T05.propSyusekiMin.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText styleClass="inputText"
																					id="htmlSyusekiMin"
																					onblur="onCangeData();"
																					value="#{pc_Kmd00302T05.propSyusekiMin.integerValue}"
																					size="3" tabindex="19"
																					disabled="#{pc_Kmd00302T05.propSyusekiMin.disabled}"
																					readonly="#{pc_Kmd00302T05.propSyusekiMin.readonly}"
																					style="#{pc_Kmd00302T05.propSyusekiMin.style}"
																					maxlength="#{pc_Kmd00302T05.propSyusekiMin.maxLength}">
																					<f:convertNumber type="number" pattern="##0"/>
																					<hx:inputHelperAssist errorClass="inputText_Error"
																						promptCharacter="_" />
																				</h:inputText>
																			</TD>
																		</TR>
																		<TR>
																			<TH class="v_e" width="150">
																				<h:outputText styleClass="outputText" id="lblSaitenbo" 
																					value="#{pc_Kmd00302T05.propSaitenbo.labelName}" 
																					style="#{pc_Kmd00302T05.propSaitenbo.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD colspan="3">
																				<h:selectBooleanCheckbox
																					styleClass="selectBooleanCheckbox" id="htmlSaitenbo"
																					onchange="onCangeData();"
																					value="#{pc_Kmd00302T05.propSaitenbo.checked}"
																					tabindex="20"
																					disabled="#{pc_Kmd00302T05.propSaitenbo.disabled}"
																					readonly="#{pc_Kmd00302T05.propSaitenbo.readonly}"
																					style="#{pc_Kmd00302T05.propSaitenbo.style}"
																					rendered="#{pc_Kmd00302T05.propSaitenbo.rendered}">
																				</h:selectBooleanCheckbox>
																				採点簿を出力する

																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
																<TABLE border="0" cellspacing="0" class="button_bar" width="720">
																	<TBODY>
																		<TR>
																			<TD nowrap>
																				<hx:commandExButton type="submit" value="確定"
																					styleClass="commandExButton_dat" id="determinateSiken"
																					onclick="onCangeData();"
																					tabindex="21"
																					disabled="#{pc_Kmd00302T05.propDeterminateSiken.disabled}"
																					style="#{pc_Kmd00302T05.propDeterminateSiken.style}"
																					action="#{pc_Kmd00302T05.doDeterminateSikenAction}"
																					rendered="#{pc_Kmd00302T05.propDeterminateSiken.rendered}">
																				</hx:commandExButton>
																				<hx:commandExButton type="submit" value="削除"
																					styleClass="commandExButton_dat" id="deleteSiken"
																					onclick="onCangeData();"
																					tabindex="22"
																					disabled="#{pc_Kmd00302T05.propDeleteSiken.disabled}"
																					style="#{pc_Kmd00302T05.propDeleteSiken.style}"
																					action="#{pc_Kmd00302T05.doDeleteSikenAction}"
																					rendered="#{pc_Kmd00302T05.propDeleteSiken.rendered}">
																				</hx:commandExButton>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</CENTER>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<!--↑タブ用テーブル↑-->
						</TD>
					</TR>
				</TBODY>
			</TABLE>
<!--							<BR>-->
			<TABLE border="0" cellspacing="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD nowrap>
							<hx:commandExButton type="submit" value="登録"
								styleClass="commandExButton_dat" id="register"
								confirm="#{msg.SY_MSG_0002W}" tabindex="23"
								action="#{pc_Kmd00302T05.doRegisterAction}"
								disabled="#{pc_Kmd00302T01.kmd00302.propRegister.disabled}"
								style="#{pc_Kmd00302T01.kmd00302.propRegister.style}"
								rendered="#{pc_Kmd00302T01.kmd00302.propRegister.rendered}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="更新"
								styleClass="commandExButton_dat" id="update"
								confirm="#{msg.SY_MSG_0003W}" tabindex="24"
								action="#{pc_Kmd00302T05.doUpdateAction}"
								style="#{pc_Kmd00302T01.kmd00302.propUpdate.style}"
								disabled="#{pc_Kmd00302T01.kmd00302.propUpdate.disabled}"
								rendered="#{pc_Kmd00302T01.kmd00302.propUpdate.rendered}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="削除"
								styleClass="commandExButton_dat" id="delete"
								confirm="#{msg.SY_MSG_0004W}" tabindex="25"
								action="#{pc_Kmd00302T05.doDeleteAction}"
								disabled="#{pc_Kmd00302T01.kmd00302.propDelete.disabled}"
								style="#{pc_Kmd00302T01.kmd00302.propDelete.style}"
								rendered="#{pc_Kmd00302T01.kmd00302.propDelete.rendered}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<h:inputHidden id="executable"
				value="#{pc_Kmd00302.propExecutable.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Kmd00302T01.kmd00302.changeDataFlg}" ></h:inputHidden>
		</h:form></div>
		<!-- フッターインクルード -->
		<jsp:include page ="../inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
	
</f:view>

</HTML>
