<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_NSB_NYS_SKK" name="入試選考科目" prod_id="NS" description="入試の選考科目情報を持ちます。|『入試要項登録』にて作成され、科目毎に加算配点や必要最低点を持ちます。">
<STATMENT><![CDATA[
NSB_NYS_SKK
]]></STATMENT>
<COLUMN id="NYUSHI_NENDO" name="入試年度" type="number" length="4" lengthDP="0" byteLength="0" description="入試年度です。"/><COLUMN id="NYUSHI_GAKKI_NO" name="入試学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="入試が実施される学期の番号です。"/><COLUMN id="NYS_SBT_CD" name="入試種別コード" type="string" length="6" lengthDP="0" byteLength="6" description="学生の入試形態を表します。入学年度と合わせて「入試種別」の参照キーとなります。"/><COLUMN id="GAKKA_CD" name="学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="入試が実施される学科組織コードが設定されます。"/><COLUMN id="SENKO_HOHO_NO" name="選考方法番号" type="number" length="2" lengthDP="0" byteLength="0" description="選考方法を識別する番号が設定されます。(教科番号)"/><COLUMN id="KAMOK_NO" name="科目番号" type="number" length="2" lengthDP="0" byteLength="0" description="受験教科で選択可能な科目に振られる１からの連番です。科目ＮＯの値が科目指定の番号とリンケージします。"/><COLUMN id="KAMOK_CD" name="科目コード" type="string" length="3" lengthDP="0" byteLength="3" description="入試科目のコードです。"/><COLUMN id="KASAN_HAITEN" name="加算配点" type="number" length="4" lengthDP="1" byteLength="0" description="受験科目の集計時の加算配点です。得点入力の教科について設定されます。評価入力の教科は０となります。|『成績集計』にて集計点へ加算されます。"/><COLUMN id="MIN_SCORE" name="必要最低点" type="number" length="3" lengthDP="0" byteLength="0" description="科目毎の合格必要最低点です。合否判定時での最優先選考判断材料となります。"/>
</TABLE>
