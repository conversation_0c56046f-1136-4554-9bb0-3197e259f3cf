<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/PSsa0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>PSsa0101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css" />
<SCRIPT type="text/javascript">
function confirmOk() {

 document.getElementById('form1:htmlExecutableSearch').value = "1";
 indirectClick('search');
}

function confirmCancel() {
 alert('実行を中断しました。');
}   



function func_1(thisObj, thisEvent) {
	setPage('htmlPage', 'htmlGakuseiList');
}
// 選択中の行
var selectedIdx = null;
//親画面に学籍番号を返却
function doSelect(thisObj, thisEvent) {

	// 学籍番号を学籍番号返却フィールドへセット
	var buttonid = thisObj.id;
	// 正規表現にて選択行のindex値を取得
	var point_start = buttonid.search(":[0-9]*:");
	var point_end = buttonid.search(":htmlListSelected");
	selectedIdx = buttonid.substring(point_start+1,point_end);
	var retValue = document.getElementById("form1:htmlGakuseiList:"+ selectedIdx +":lblGakusekiCd_list").innerHTML;
	var retFieldName = document.getElementById("form1:htmlRetFieldName").value;
	if (window.opener) {
		window.opener.document.getElementById(retFieldName).value = retValue;
	}

	// 選択状態を親画面にセット
	var pSelectedFlgItemId = document.getElementById("form1:pSelectedFlgItemId").value;
	if(pSelectedFlgItemId!="" && window.opener.document.getElementById(pSelectedFlgItemId)){
		window.opener.document.getElementById(pSelectedFlgItemId).value = "1";
	}
	// 親画面の選択ボタン押下
	var selectBtnId = document.getElementById("form1:selectBtnId").value;
	if(selectBtnId!="" && window.opener.document.getElementById(selectBtnId)){
		window.opener.document.getElementById(selectBtnId).click();
		return true;
	} else {
		self.close();
	}
}

function openKigyoModalWindow(field1) {
  var url="${pageContext.request.contextPath}/faces/rev/ss/pSsc0101.jsp"
    + "?retFieldName=" + field1 + "&getNameFlg=1";

  openModalWindow(url, "PSsc0101", "<%=com.jast.gakuen.rev.ss.PSsc0101.getWindowOpenOption() %>");
  return false;
}


// 企業名称を取得する
function doSscKgyAJAX(thisObj, thisEven, targetLabel) {
	var servlet = "rev/ss/SscKgyAJAX";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}
// 画面ロード時の企業名称の再取得
function loadAction(event){
	doSscKgyAJAX(document.getElementById('form1:htmlKigyoCd'), event, 'form1:htmlKigyoName');
}

  window.attachEvent('onload', endload);
  function endload() {
    changeScrollPosition('htmlHidScroll', 'listScroll');
  }
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PSsa0101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_PSsa0101.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PSsa0101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PSsa0101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
		<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" styleClass="outputText" escape="false"></h:outputText>
	</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			

<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
<TABLE width="800" border="0" cellpadding="0" cellspacing="0"align="left">
<TR><TD>

<TABLE width="800" >
	<TBODY>
		<TR>
			<TD colspan="2">
				<TABLE width="800" border="0" cellpadding="3" cellspacing="0" class="table">
					<TBODY>
						<TR>
							<TH class="v_a" width="150"><h:outputText styleClass="outputText"
										id="lblGakusekiCd"
										value="#{pc_PSsa0101.propGakusekiCd.labelName}"
										style="#{pc_PSsa0101.propGakusekiCd.labelStyle}"></h:outputText></TH>
							<TD width="250"><h:inputText styleClass="inputText" id="htmlGakusekiCd"
										value="#{pc_PSsa0101.propGakusekiCd.stringValue}"
										maxlength="#{pc_PSsa0101.propGakusekiCd.maxLength}"
										readonly="#{pc_PSsa0101.propGakusekiCd.readonly}"
										style="#{pc_PSsa0101.propGakusekiCd.style}"></h:inputText></TD>
						</TR>
						<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblGakuseiName"
										value="#{pc_PSsa0101.propGakuseiName.labelName}"
										style="#{pc_PSsa0101.propGakuseiName.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:inputText styleClass="inputText"
										id="htmlGakuseiName"
										value="#{pc_PSsa0101.propGakuseiName.stringValue}"
										maxlength="#{pc_PSsa0101.propGakuseiName.maxLength}"
										readonly="#{pc_PSsa0101.propGakuseiName.readonly}"
										style="#{pc_PSsa0101.propGakuseiName.style}"
										size="32"></h:inputText></TD>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblGakuseiNameKana"
										value="#{pc_PSsa0101.propGakuseiNameKana.labelName}"
										style="#{pc_PSsa0101.propGakuseiNameKana.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:inputText styleClass="inputText" id="htmlGakuseiNameKana"
										value="#{pc_PSsa0101.propGakuseiNameKana.stringValue}"
										style="#{pc_PSsa0101.propGakuseiNameKana.style}"
										maxlength="#{pc_PSsa0101.propGakuseiNameKana.maxLength}"
										readonly="#{pc_PSsa0101.propGakuseiNameKana.readonly}"
										size="32"></h:inputText></TD>
						</TR>
						<TR>
									<TH class="v_d" width="150"><h:outputText
										styleClass="outputText" id="lblSzkGakkaCd"
										value="#{pc_PSsa0101.propSzkGakkaCd.labelName}"
										style="#{pc_PSsa0101.propSzkGakkaCd.labelStyle}"></h:outputText></TH>
									<TD width="650" colspan="3"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlSzkGakkaCd"
										value="#{pc_PSsa0101.propSzkGakkaCd.value}"
										disabled="#{pc_PSsa0101.propSzkGakkaCd.disabled}"
										readonly="#{pc_PSsa0101.propSzkGakkaCd.readonly}"
										style="#{pc_PSsa0101.propSzkGakkaCd.style}"
										style="width:630px">
										<f:selectItems value="#{pc_PSsa0101.propSzkGakkaCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
						<TR>
									<TH class="v_e" width="150"><h:outputText
										styleClass="outputText" id="lblGakunen"
										value="#{pc_PSsa0101.propGakunen.labelName}"
										style="#{pc_PSsa0101.propGakunen.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakunen" value="#{pc_PSsa0101.propGakunen.value}"
										readonly="#{pc_PSsa0101.propGakunen.readonly}"
										style="#{pc_PSsa0101.propGakunen.style}">
										<f:selectItems value="#{pc_PSsa0101.propGakunen.list}" />
									</h:selectOneMenu></TD>
									<TH class="v_f" width="150" rowspan="3"><h:outputText styleClass="outputText" id="lblClubCd" 
															value="#{pc_PSsa0101.propClubCd.labelName}"
															style="#{pc_PSsa0101.propClubCd.labelStyle}"></h:outputText></TH>
									<TD width="250"  rowspan="3"><h:selectManyListbox styleClass="selectManyListbox"
															id="htmlClubCd"
															disabled="#{pc_PSsa0101.propClubCd.disabled}"
															value="#{pc_PSsa0101.propClubCd.value}"
															size="4"
															style="width: 255px">
															<f:selectItems value="#{pc_PSsa0101.propClubCd.list}" /></h:selectManyListbox></TD>
								</TR>
								<TR>
									<TH class="v_g" width="150"><h:outputText
										styleClass="outputText" id="lblSemester"
										value="#{pc_PSsa0101.propSemester.labelName}"
										style="#{pc_PSsa0101.propSemester.labelStyle}"></h:outputText></TH>
									<TD width="250" nowrap><h:selectOneMenu styleClass="selectOneMenu" id="htmlSemester"
										value="#{pc_PSsa0101.propSemester.value}"
										style="#{pc_PSsa0101.propSemester.style}"
										readonly="#{pc_PSsa0101.propSemester.readonly}"
										disabled="#{pc_PSsa0101.propSemester.disabled}">
										<f:selectItems value="#{pc_PSsa0101.propSemester.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_g" width="150"><h:outputText
										styleClass="outputText" id="lblRyugaksei"
										value="留学者"></h:outputText></TH>
									<TD width="250" nowrap>
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" 
																id="htmlCheckboxRyugaku" 
																value="#{pc_PSsa0101.propCheckboxRyugaku.checked}" 
																disabled="#{pc_PSsa0101.propCheckboxRyugaku.disabled}" 
																style="#{pc_PSsa0101.propCheckboxRyugaku.style}">
															</h:selectBooleanCheckbox>
															のみ</TD>
								</TR>
								<TR>
									<TH class="v_g" width="150"><h:outputText
										styleClass="outputText" id="lblKanriTsy"
										value="#{pc_PSsa0101.propKanriTsy.labelName}"
										style="#{pc_PSsa0101.propKanriTsy.labelStyle}"></h:outputText></TH>
									<TD width="250" nowrap><h:selectOneRadio
										styleClass="selectOneRadio" id="htmlKanriTsy"
										value="#{pc_PSsa0101.propKanriTsy.stringValue}"
										readonly="#{pc_PSsa0101.propKanriTsy.readonly}"
										style="#{pc_PSsa0101.propKanriTsy.style}"
										disabled="#{pc_PSsa0101.propKanriTsy.disabled}">
										<f:selectItem itemValue="1" itemLabel="含む" />
										<f:selectItem itemValue="2" itemLabel="含まない" />
										<f:selectItem itemValue="3" itemLabel="のみ" />
									</h:selectOneRadio></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblSsen"
										value="推薦対象者"></h:outputText></TH>
									<TD width="250"><h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" 
																id="htmlCheckboxSsen" 
																value="#{pc_PSsa0101.propCheckboxSsen.checked}" 
																disabled="#{pc_PSsa0101.propCheckboxSsen.disabled}" 
																style="#{pc_PSsa0101.propCheckboxSsen.style}">
															</h:selectBooleanCheckbox>
															のみ</TD>
								</TR>
								<TR>
									<TH class="v_g" width="150" rowspan="3"><h:outputText
										styleClass="outputText" id="lblKtdJyokyo"
										value="#{pc_PSsa0101.propKtdJyokyo.labelName}"
										style="#{pc_PSsa0101.propKtdJyokyo.labelStyle}"></h:outputText></TH>
									<TD width="250" nowrap rowspan="3"><h:selectManyListbox styleClass="selectManyListbox"
										id="htmlKtdJyokyo" value="#{pc_PSsa0101.propKtdJyokyo.value}"
										disabled="#{pc_PSsa0101.propKtdJyokyo.disabled}"
										readonly="#{pc_PSsa0101.propKtdJyokyo.readonly}"
										style="#{pc_PSsa0101.propKtdJyokyo.style}"
										size="4" style="width:226px"
										required="#{pc_PSsa0101.propKtdJyokyo.required}">
										<f:selectItems value="#{pc_PSsa0101.propKtdJyokyo.list}" />
									</h:selectManyListbox></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblItern"
										value="ｲﾝﾀｰﾝｼｯﾌﾟ参加者"></h:outputText></TH>
									<TD width="250"><h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" 
																id="htmlCheckboxItern" 
																value="#{pc_PSsa0101.propCheckboxItern.checked}" 
																disabled="#{pc_PSsa0101.propCheckboxItern.disabled}" 
																style="#{pc_PSsa0101.propCheckboxItern.style}">
															</h:selectBooleanCheckbox>
															のみ</TD>
								</TR>
								<TR>
									<TH width="150" rowspan="2"><h:outputText
													styleClass="outputText" id="lblKigyoCd"
													value="#{pc_PSsa0101.propKigyoCd.labelName}"
													style="#{pc_PSsa0101.propKigyoCd.labelStyle}">
													</h:outputText></TH>
									<TD width="250"><h:inputText styleClass="inputText"
													id="htmlKigyoCd"
													value="#{pc_PSsa0101.propKigyoCd.stringValue}"
													style="#{pc_PSsa0101.propKigyoCd.style}"  size="12"
													disabled="#{pc_PSsa0101.propKigyoCd.disabled}"
													maxlength="#{pc_PSsa0101.propKigyoCd.maxLength}"
													onblur="return doSscKgyAJAX(this, event, 'form1:htmlKigyoName');">
													</h:inputText><hx:commandExButton type="button" value="検"
							                styleClass="commandExButton_search" id="btnKigyoF"
							                disabled="#{pc_PSsa0101.propKigyoCd.disabled}"
							                onclick="openKigyoModalWindow('form1:htmlKigyoCd');"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD width="250"><h:outputText styleClass="outputText"
									id="htmlKigyoName" style="#{pc_PSsa0101.propKigyoName.style}"
									value="#{pc_PSsa0101.propKigyoName.stringValue}"
									title="#{pc_PSsa0101.propKigyoName.stringValue}"></h:outputText></TD>
								</TR>
							</TBODY>
				</TABLE>
			</TD>
		</TR>
		<TR>
			<TD colspan="2">
				<TABLE width="800" border="0" cellpadding="3" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
							<TD width="39%"></TD>
							<TD width="11%"><hx:commandExButton type="submit" value="検索" id="search"
										styleClass="commandExButton_dat" action="#{pc_PSsa0101.doSearchAction}"></hx:commandExButton>
							</TD>
							<TD width="11%">
								<hx:commandExButton type="submit" value="クリア" id="clear" styleClass="commandExButton_dat" action="#{pc_PSsa0101.doClearAction}"></hx:commandExButton>
							</TD>
							<TD width="39%" align="right">
								<FONT color="red"><h:outputText styleClass="note"
												id="lblSearchGakuseiCmt" value="※学籍番号、学生氏名、学生氏名カナは部分一致">
											</h:outputText></FONT>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>
</TD></TR>
<TR><TD>
<TABLE width="800" border="0" cellspacing="0" style="margin-top:0px">
	<TBODY>
		<TR>
			<TD style="text-align:right;"><h:outputText styleClass="outputText"
							id="lblCountGakuseiList"
							value="#{pc_PSsa0101.propGakuseiList.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="lblCountCmt3" value="件">
						</h:outputText></TD></TR>
		<TR>
			<TD>
				<div class="listScroll" id="listScroll" style="height: 220px"
				onscroll="setScrollPosition('htmlHidScroll', this);">
				<h:dataTable border="0" cellpadding="3" cellspacing="0"
							columnClasses="columnCenter,columnCenter,,,columnCenter,,columnRight,"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_PSsa0101.propGakuseiList.rowClasses}"
							styleClass="meisai_scroll" id="htmlGakuseiList" width="800"
							value="#{pc_PSsa0101.propGakuseiList.list}" var="varlist">
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="20" name="width" />
								<h:outputText styleClass="outputText" id="htmlListRegister"
									value="#{varlist.register}" rendered="#{varlist.rendered}"></h:outputText>
							</h:column>
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblListGakusekiCd" styleClass="outputText"
										value="学籍番号"></h:outputText>
								</f:facet>
								<f:attribute value="text-align:left" name="style" />
								<f:attribute value="80" name="width" />
								<h:outputText styleClass="outputText" id="lblGakusekiCd_list"
									value="#{varlist.gakusekiCd}" rendered="#{varlist.rendered}"></h:outputText>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblListGakuseiName" styleClass="outputText"
										value="氏名"></h:outputText>
								</f:facet>
								<f:attribute value="175" name="width" />
								<h:outputText styleClass="outputText" id="htmlListGakuseiName"
									value="#{varlist.gakuseiName.displayValue}"
									rendered="#{varlist.rendered}"
									title="#{varlist.gakuseiName.stringValue}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblListGakuseiNameKana"
										styleClass="outputText" value="氏名カナ"></h:outputText>
								</f:facet>
								<f:attribute value="180" name="width" />
								<h:outputText styleClass="outputText"
									id="htmlListGakuseiNameKana"
									value="#{varlist.gakuseiNameKana.displayValue}"
									rendered="#{varlist.rendered}"
									title="#{varlist.gakuseiNameKana.stringValue}"></h:outputText>
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="所属学科組織コード"
										id="lblListSzkGakkaCd"></h:outputText>
								</f:facet>
								<f:attribute value="text-align:left" name="style" />
								<f:attribute value="65" name="width" />
								<h:outputText styleClass="outputText" id="htmlListSzkGakkaCd"
									value="#{varlist.szkGakkaCd}" rendered="#{varlist.rendered}"></h:outputText>
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText id="lblListSzkGakkaName" styleClass="outputText"
										value="所属学科組織"></h:outputText>
								</f:facet>
								<f:attribute value="200" name="width" />
								<h:outputText styleClass="outputText" id="htmlListSzkGakkaName"
									value="#{varlist.szkGakkaName.displayValue}"
									rendered="#{varlist.rendered}"
									title="#{varlist.szkGakkaName.stringValue}"></h:outputText>
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
									<h:outputText id="lblListGakunen" styleClass="outputText"
										value="学年"></h:outputText>
								</f:facet>
								<f:attribute value="40" name="width" />
								<h:outputText styleClass="outputText" id="htmlListGakunen"
									value="#{varlist.gakunen}" rendered="#{varlist.rendered}"></h:outputText>
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="40" name="width" />
								<hx:commandExButton id="htmlListSelected" type="submit"
									value="選択" styleClass="commandExButton"
									rendered="#{varlist.rendered}" 
									action="#{pc_PSsa0101.doSelectAction}"
									onclick="return doSelect(this, event);"></hx:commandExButton>
							</h:column>
						</h:dataTable>
					</div>
			</TD>
		</TR>
		<TR align="left">
			<TD>
				<h:outputText styleClass="note"
				id="lblRegisterCmt" value="※先頭にアスタリスク（*）のある学生は未登録です">
				</h:outputText>
		</TR>
	</TBODY>
</TABLE>
</TD></TR>
</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/childFooter.jsp" />
			<h:inputHidden
				value="#{pc_PSsa0101.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_PSsa0101.propGakuseiList.currentPage}"
				id="htmlPage"></h:inputHidden>
			<h:inputHidden id="htmlRetFieldName" value="#{pc_PSsa0101.propRetFieldName.value}"></h:inputHidden>
			<h:inputHidden id="selectBtnId" value="#{pc_PSsa0101.propSelectBtnId.value}"></h:inputHidden>
			<h:inputHidden id="motoFuncId" value="#{pc_PSsa0101.propMotoFuncId.value}"></h:inputHidden>
			<h:inputHidden id="pSelectedFlgItemId" value="#{pc_PSsa0101.propPSelectedFlgItemId.value}"></h:inputHidden>
			<h:inputHidden id="htmlHidScroll" value="#{pc_PSsa0101.propGakuseiList.scrollPosition}"></h:inputHidden>			
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

