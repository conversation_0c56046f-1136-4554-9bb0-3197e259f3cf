package com.jast.gakuen.rev.xrm.action;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.jast.gakuen.framework.PageCodeBaseEx;
import com.jast.gakuen.framework.batch.BatchConst;
import com.jast.gakuen.framework.batch.BatchLogic;
import com.jast.gakuen.framework.constant.ActionConst;
import com.jast.gakuen.framework.constant.SyMsgConst;
import com.jast.gakuen.framework.db.DbException;
import com.jast.gakuen.framework.property.Checkable;
import com.jast.gakuen.framework.util.CheckComponent;
import com.jast.gakuen.framework.util.MessageList;
import com.jast.gakuen.framework.util.UtilDate;
import com.jast.gakuen.framework.util.UtilIni;
import com.jast.gakuen.framework.util.UtilProperty;
import com.jast.gakuen.framework.util.UtilStr;
import com.jast.gakuen.framework.util.UtilSystem;
import com.jast.gakuen.rev.RevActionBase;
import com.jast.gakuen.rev.co.Cos00401;
import com.jast.gakuen.rev.co.util.UtilCombBoxMaker;
import com.jast.gakuen.rev.co.util.UtilCosOpt;
import com.jast.gakuen.rev.gh.constant.GhConstant;
import com.jast.gakuen.rev.gh.constant.GhMsgConst;
import com.jast.gakuen.rev.gh.db.dao.GhgPayhBunDAO;
import com.jast.gakuen.rev.gh.db.dao.GhgPaywDAO;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhARComparator;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhBunAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPaywAR;
import com.jast.gakuen.rev.gh.util.UtilGhCombBoxMaker;
import com.jast.gakuen.rev.gh.util.UtilGhFormat;
import com.jast.gakuen.rev.gh.util.UtilGhGyomuNendo;
import com.jast.gakuen.rev.gh.util.UtilGhPdf;
import com.jast.gakuen.rev.xrm.Xrm00101T01;
import com.jast.gakuen.rev.xrm.Xrm00101T02;
import com.jast.gakuen.rev.xrm.Xrm00101T03;
import com.jast.gakuen.rev.xrm.action.bean.Xrm00101L01Bean;
import com.jast.gakuen.rev.xrm.constant.XrmConst;
import com.jast.gakuen.rev.xrm.constant.XrmIniConst;
import com.jast.gakuen.rev.xrm.db.dao.XrmGhgPayhDAO;
import com.jast.gakuen.rev.xrm.util.XrmUtilCombBoxMaker;
import com.jast.gakuen.rev.xrm.util.XrmUtilConvert;
import com.jast.gakuen.rev.xrx.db.dao.XrxMeiKanriKmkDAO;
import com.jast.gakuen.rev.xrx.db.entity.XrxMeiKanriKmkAR;

/**
 * 請求書出力アクションクラス
 * <br>
 * <AUTHOR>
 */
public class Xrm00101T01Act extends RevActionBase {
	
	/** フォーマット */
	static public final String DATE_PATTERN ="yyyy";
	
	
	//エラーメッセージ用文字列
	private final String NOCHECK_ERR_MSG = "納付金";
	private final String ERR_MSG_TSUSHIN_TEXT_1 = "備考欄は1行に32byte以内で";
	private final String ERR_MSG_TSUSHIN_TEXT_2 = "備考欄は6行以内で";	
	private final String ERR_MSG_KIGEN_TEXT_3 = "納入期限を直接入力時は、納入期限/有効期限";	
	//納入期限のエラーメッセージ追加
	private final String ERR_MSG_PAYDATE_1 = "納入期限";
	private final String ERR_MSG_YUKOULIMIT_1 = "有効期限";
	
	/**
	 * 画面初期化処理<br>
	 * 画面の初期化を行う。<br>
	 * @param  pagecode 	(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 */
	protected String init(PageCodeBaseEx pagecode) {
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		
		//画面初期化
		try {
			//初期値設定
			setInitItemValue(pc);
			
			if(pc.getPropgyoumList().getList() == null || pc.getPropgyoumList().getList().size()==0){
				pc.getPropgyoumList().addListItemAutoIndent("", "" , "", 1);
				UtilSystem.getDisplayInfo().setDisplayMessage(					
				"名称管理マスタに業務コードが存在しません。<br>システム管理者にお問い合わせください。");
				
				return  ActionConst.RET_FALSE;
			}
			
		} catch (Exception e) {
			
			throw new RuntimeException(e);
		}
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * 初期値セット<br>
	 * 各項目に初期値を設定する。
	 * <br>
	 * @param  pc 		(Xrm00101T01)ページコード
	 * @throws DbException
	 */
	private void setInitItemValue(Xrm00101T01 pc) throws DbException {
		
		UtilCombBoxMaker utilCombBoxMaker = new UtilCombBoxMaker();
		UtilGhCombBoxMaker ghUtilCombBoxMaker = new UtilGhCombBoxMaker();
		
		//学費年度設定
		pc.getPropGhNendo().setDateValue(
				UtilDate.editDateYYYYMMDD(this.getGhNendo(), 1, 1));
		
		//配当済納付金一覧
		pc.getPropPayhList().setListbean(new Xrm00101L01Bean());
		pc.getPropPayhList().setRows(0);
		pc.getPropPayhList().setList(new ArrayList());
		
		//学年コンボボックス取得
		utilCombBoxMaker.getGakunenCombBox(getDbs(),pc.getPropGakunen());
		
		
		// 画面．通信欄(ぺイジー)を設定
		if(pc.getPropTsushinText().getStringValue() == null){
			pc.getPropTsushinText().setStringValue("");
			
			//通信欄
			pc.getPropTsushinText().setDisabled(true);
		}
		
		//発行日付（他タブからの遷移時には設定しない）
		Date now = nowDate();
		if(pc.getpropHakkouDate().getDateValue() == null){
			pc.getpropHakkouDate().setDateValue(now);
		}
		
		//納入期限
		pc.getPropNonyuDate().setDisabled(true);
		//有効期限
		pc.getPropYukoDate().setDisabled(true);
		
		//所属学科組織コンボボックス取得
		ghUtilCombBoxMaker.getSzksCombBox(getDbs(),pc.getPropSzkGakka());
		
		//業務コード
		pc.getPropgyoumList().setList(new ArrayList());
		pc.getPropgyoumList().setReadonly(true);
		XrmUtilCombBoxMaker box = new XrmUtilCombBoxMaker();		
		box.getGyoumListCombBox(getDbs(),pc.getPropgyoumList(),0);
		pc.getPropgyoumList().setValue("");
		
		
		//納付金種別
		pc.getPropChkListSeika().setChecked(true);
		pc.getPropChkListToitu().setChecked(true);
		pc.getPropChkListKamokutori().setChecked(true);
		
		//就学種別
		utilCombBoxMaker.getSyugakSbtCombBox(getDbs(),pc.getPropSyugakSbt());
		
		//異動種別  
		UtilCombBoxMaker comb = new UtilCombBoxMaker();
		comb.getIdoSyutgakSbt2CombBox(getDbs(), pc.getPropIdoSbtList());
		
		//オプションテーブルより前回入力値を取得
		//(データが存在しない場合は初期値を設定)
		loadDefaultItemValueOption(pc);
		//オプションテーブルにデータが存在しない場合
		if(pc.getPropOutPutKbn().getValue()==null){
			//出力区分
			pc.getPropOutPutKbn().setValue("1");		
		}
		
		//フォーカスセット
		UtilSystem.getDisplayInfo().setTargetFocusId(
				pc.getPropGhNendo().getId());
		
	}
	
	/**
	 * アクション(選択)<br>
	 * 入力された学費年度に配当されている納付金の一覧を表示する。
	 * <br>
	 * @param   pagecode 	(PageCodeBaseEx)ページコード
	 * @return  String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws  DbException
	 */
	protected String search(PageCodeBaseEx pagecode) throws DbException {
		
		try {
			
			Xrm00101T01 pc = (Xrm00101T01)pagecode;
			
			//入力チェック
			MessageList message = checkNendoErr(pc);
			
			if (message.size() != 0) {
				//入力チェックでエラーが有る場合
				UtilSystem.getDisplayInfo()
				.setDisplayMessage(message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			//配当済納付金一覧の設定
			if (!setPayhList(pc)) {
				return ActionConst.RET_FALSE;
			}
			
			
			//ロック項目設定
			//学費年度
			pc.getPropGhNendo().setDisabled(true);
			pc.getPropChkListSeika().setDisabled(true);
			pc.getPropChkListKamokutori().setDisabled(true);
			pc.getPropChkListToitu().setDisabled(true);
			pc.getPropgyoumList().setDisabled(true);
			
			//各ボタン
			pc.getPropActiveControlSearch().setIntegerValue(
					new Integer(GhConstant.GH_CTR_DISABLED));
			
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * 入力チェック(学費年度)<br>
	 * 学費年度の入力チェックを行う。
	 * <br>
	 * @param  pc		 (Xrm00101T01)ページコード
	 * @return chkMsg	 (MessageList)メッセージリスト
	 * @throws DbException
	 */
	private MessageList checkNendoErr(Xrm00101T01 pc) throws DbException {
		
		CheckComponent cc = new CheckComponent();
		//学費年度
		cc.addComponent(pc.getPropGhNendo());
		MessageList chkMsg =  cc.check();
		//納付金種別
		if(!pc.getPropChkListSeika().isChecked()&&
				!pc.getPropChkListKamokutori().isChecked()&&
				!pc.getPropChkListToitu().isChecked()){
			chkMsg.add(UtilProperty.getMsgString(
					SyMsgConst.SY_MSG_0006E,
					pc.getPropChkListLbl().getName()));
			
		}
		return chkMsg;
	}
	
	/**
	 * 配当済納付金一覧情報取得処理<br>
	 * 入力された期間に配当されている納付金配当情報を取得する。
	 * <br>
	 * @param  pc         (ghe00702T01)ページコード 
	 * @return            (boolean) true：正常 false：異常 
	 * @throws Exception
	 */
	private boolean setPayhList(Xrm00101T01 pc) throws DbException {
		
		try {
			
			SimpleDateFormat sdf = new SimpleDateFormat(GhConstant.GH_FORMAT_YYYY);
			//割当年度
			int intGhYear = 0;
			if (pc.getPropGhNendo().getDateValue() != null) {
				intGhYear = Integer.parseInt(sdf.format(pc.getPropGhNendo().getDateValue()));
			}
			//納付金種別取得
			ArrayList paysyu = payKubunget(pc);
			//学費年度
			int ghNend =Integer.parseInt(sdf.format(pc.getPropGhNendo().getDateValue()));
			
			//配当済納付金一覧取得
			//納付金配当一覧DAO作成
			XrmGhgPayhDAO xrmGhgPayhDAO = (XrmGhgPayhDAO)getDbs().getDao(XrmGhgPayhDAO.class);
			List payhList = xrmGhgPayhDAO.findByGyomuCdPayCdJoinGhgPayh(ghNend,pc.getPropgyoumList().getStringValue(),paysyu);	
			
			//納付金コード順にソート
			GhgPayhARComparator sort = new  GhgPayhARComparator();
			sort.asc(GhgPayhARComparator.PAY_CD);
			Collections.sort(payhList, sort);
			
			if (payhList.size() == 0) {
				//該当データが存在しない場合エラー
				MessageList message = new MessageList();
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0032E));
				//学費年度にフォーカスをセット
				UtilSystem.getDisplayInfo().setTargetFocusId(
						pc.getPropGhNendo().getId());
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return false;
			}
			
			if (payhList.size() > 0) {
				//データ件数のエラーチェック
				//iniファイルよりMax件数取得
				int WarningCount =
					Integer.parseInt(UtilIni.getParameter(
							GhConstant.GH_SECTION_MAX, 
							GhConstant.GH_KEY_MAXSEARCH1));
				//iniファイルよりMax件数取得
				int ErrorCount =
					Integer.parseInt(UtilIni.getParameter(
							GhConstant.GH_SECTION_MAX, 
							GhConstant.GH_KEY_MAXSEARCH2));
				
				if (payhList.size() > ErrorCount) {
					//検索結果がエラー件数以上の場合
					UtilSystem.getDisplayInfo().setDisplayMessage(
							UtilProperty.getMsgString(
									SyMsgConst.SY_MSG_0034E,String.valueOf(
											ErrorCount)));
					return false;
					
				} else if (payhList.size() > WarningCount  &&
						pc.getPropExecutableSearch()
						.getIntegerValue().intValue() == 0) {
					//検索結果がエラー件数未満かつ警告件数以上の場合
					UtilSystem.getDisplayInfo().setConfirmMessage(
							UtilProperty.getMsgString(
									SyMsgConst.SY_MSG_0016W,
									String.valueOf(payhList.size())));
					//二度押しチェックの為のボタンの初期化を行なう
					pc.getPropExecutableSearch()
					.setIntegerValue(new Integer(1));
					return false;
					
				} else {
					//二度押しチェックの為のボタンの初期化を行なう
					pc.getPropExecutableSearch()
					.setIntegerValue(new Integer(1));
				}
				
				//リストにデータをセット
				ArrayList payArrList = createPayhList(payhList);
				pc.getPropPayhList().setList(payArrList);
				UtilGhFormat.setRows(
						pc.getPropPayhList(),
						payArrList);
			}
			
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		return true;
	}
	
	/**
	 * 配当済納付金一覧リスト作成<br>
	 * 配当情報を画面表示用に編集する。
	 * <br>
	 * @param  payhList			(List)配当済納付金一覧
	 * @return payhArrayList	(ArrayList)配当済納付金情報
	 * @throws DbException
	 */
	private ArrayList createPayhList(List payhList) throws DbException {
		
		//配当済納付金一覧リスト
		ArrayList payhArrayList = new ArrayList();
		
		//画面表示用Beanに値を設定する
		for (int i = 0; i < payhList.size(); i++) {
			GhgPayhAR ghgPayhAR =(GhgPayhAR)payhList.get(i);
			//納付金配当分納を取得
			GhgPayhBunDAO ghgPayhDAO = (GhgPayhBunDAO)getDbs().getDao(GhgPayhBunDAO.class);
			List ghgPayhBunARList = ghgPayhDAO.findByPayh(ghgPayhAR.getNendo(),
					ghgPayhAR.getPayCd(),
					ghgPayhAR.getPatternCd(),
					ghgPayhAR.getBunnoKbnCd());
			
			
			//分納回数繰り返し
			for(int f=0;f<ghgPayhBunARList.size();f++){
				GhgPayhBunAR ghgPayhBunAR =(GhgPayhBunAR)ghgPayhBunARList.get(f);
				
				String payCd = "";
				String patternCd = "";
				int bunnoKbnCd = 0;
				int bunkatsuNo = 0;
				String payLimit = "";
				String payName = "";
				if (ghgPayhAR != null) {
					payCd = ghgPayhAR.getPayCd();
					patternCd = ghgPayhAR.getPatternCd();
					bunnoKbnCd = ghgPayhAR.getBunnoKbnCd();
					bunkatsuNo = ghgPayhBunAR.getBunkatsuNo();
					Date payLimitDate = ghgPayhBunAR.getPayLimit();
					if (payLimitDate != null) {
						payLimit = UtilDate.editDate(payLimitDate, 2);
					}
					payName = ghgPayhAR.getPayName();
					
				}
				
				Xrm00101L01Bean bean = new Xrm00101L01Bean();
				
				//年度
				bean.setNendo("");
				//納付金コード
				bean.setPayCd(payCd);
				//パターンコード
				bean.setPatternCd(patternCd);
				//分納区分コード
				bean.setBunnoKbnCd(String.valueOf(bunnoKbnCd));
				//納付金名称
				bean.setPayName(payName);
				//分割NO
				bean.setBunkatsuNo(String.valueOf(bunkatsuNo));
				//納付期限
				bean.setPayLimit(payLimit);
				//出力日
				bean.setOutputDate("");
				
				payhArrayList.add(bean);
			}
		}
		
		return payhArrayList;
	}
	
	/**
	 * アクション(解除)<br>
	 * ロック項目の解除を行う。
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws DbException
	 */
	protected String unselect(PageCodeBaseEx pagecode) throws DbException {
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		
		//リスト初期化
		pc.getPropPayhList().setListbean(new Xrm00101L01Bean());
		pc.getPropPayhList().setList(new ArrayList());
		pc.getPropPayhList().setRows(0);
		
		//二度押しチェック
		pc.getPropExecutableSearch().setIntegerValue(new Integer(0));
		
		
		//コントロール活性
		pc.getPropGhNendo().setDisabled(false);
		pc.getPropChkListSeika().setDisabled(false);
		pc.getPropChkListKamokutori().setDisabled(false);
		pc.getPropChkListToitu().setDisabled(false);
		pc.getPropgyoumList().setDisabled(false);
		//フォーカスセット
		UtilSystem.getDisplayInfo().setTargetFocusId(
				pc.getPropGhNendo().getId());
		
		//各ボタン
		pc.getPropActiveControlSearch().setIntegerValue(
				new Integer(GhConstant.GH_CTR_ENABLED));
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * アクション(実行)<br>
	 * 請求書を作成する。
	 * <br>
	 * @param  pagecode		(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 */
	protected String exec(PageCodeBaseEx pagecode) {
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		return execXrm00101BAT01(pc, ActionConst.ACTION_EXEC);
	}
	
	
	/**
	 * バッチ実行<br>
	 * バッチ処理を実行する。
	 * <br>
	 * @param  pc	  		(Xrm00101T01)ページコード
	 * @param  action 		(String)アクション区分
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 */
	private String execXrm00101BAT01(Xrm00101T01 pc, String action) {
		
		try {
			
			ArrayList outPayhList = new ArrayList();
			MessageList message = new MessageList();
			Xrm00101BAT01 bat;
			CheckComponent cc = new CheckComponent();
			
			//発行日付
			cc.addComponent(pc.getpropHakkouDate());
			message =  cc.check();
			
			//通信欄(ぺイジー)を改行コードをキーに分割
			String[] aryTsushinTextChk =getpayeasyTexSubString( pc.getPropTsushinText().getStringValue());
			
			int lineByte = 0; // 1行のバイト数
			
			for (int j = 0; j < aryTsushinTextChk.length; j++) {
				
				//1行のバイト数を取得
				lineByte = aryTsushinTextChk[j].getBytes().length;
				
				//通信欄(ぺイジー) 1行の最大文字が32byteを超えている場合エラー				
				if (lineByte > 32) {
					
					//エラーメッセージに追加
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0015E, ERR_MSG_TSUSHIN_TEXT_1));
					
				}
				
			}
			
			//通信欄(ぺイジー) 最大行数が6行を超えている場合エラー
			if (aryTsushinTextChk.length > 6) {
				
				//エラーメッセージに追加
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0015E, ERR_MSG_TSUSHIN_TEXT_2));
				
			}
			//11/13追加：期限直接入力時、指定してない場合エラー
			if(pc.getPropKigenkbn().isChecked()){
				//有効期限、納入期限が存在していない場合
				if(pc.getPropNonyuDate().getDateValue()==null||pc.getPropYukoDate().getDateValue()==null){
					//エラーメッセージに追加
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0008E, ERR_MSG_KIGEN_TEXT_3));
				}else{
					//日付相関チェック
					String addMonth = "";
					String[] errMsg = new String[2];
					//EUC名称種別項目から学費の納入期限範囲を取得
					XrxMeiKanriKmkDAO xrxMeiKanriKmkDAO = (XrxMeiKanriKmkDAO)getDbs().getDao(XrxMeiKanriKmkDAO.class);
					List list = xrxMeiKanriKmkDAO.findBySbtGyomu(XrmConst.M_SBT_PAY_LIMIT, XrmConst.M_GYOMU_CD);
					if (list.size() != 0) {
						XrxMeiKanriKmkAR xrxMeiKanriKmkAR = (XrxMeiKanriKmkAR) list.get(0);
						addMonth = xrxMeiKanriKmkAR.getKmkName1();
					}			
					//エラーメッセージに追加
					errMsg[0] = ERR_MSG_PAYDATE_1 + "+" + addMonth + "ヶ月";
					errMsg[1] = ERR_MSG_YUKOULIMIT_1;
					
					Calendar cal = Calendar.getInstance();
					
					cal.setTime(pc.getPropNonyuDate().getDateValue());
					
					cal.add(Calendar.MONTH, Integer.parseInt(addMonth));
					
					cal.getTime();
					
					Date cmpDate1 = cal.getTime();
					Date cmpDate2 = pc.getPropYukoDate().getDateValue();
					
					//納入期限 + 学費の納入期限範囲 < 有効期限以外の場合エラー
					if (cmpDate1.compareTo(cmpDate2) <= 0) {
						
						message.add(UtilProperty.getMsgString(
								GhMsgConst.GH_MSG_0002E, errMsg));
					}
					//納入期限＞有効期限の場合エラー
					Date cmpDatenonyu= pc.getPropNonyuDate().getDateValue();
					Date cmpDateyuko = pc.getPropYukoDate().getDateValue();
					if(cmpDateyuko.compareTo(cmpDatenonyu) < 0){
						//エラーメッセージに追加
						errMsg = new String[2];
						errMsg[0] =ERR_MSG_YUKOULIMIT_1;
						errMsg[1] =  ERR_MSG_PAYDATE_1;
						message.add(UtilProperty.getMsgString(
								GhMsgConst.GH_MSG_0002E, errMsg));

					}

				}
			}
			
			
			
			//異動種別が未選択で、異動日付に入力がある場合はエラー
			if (Checkable.NO_SELECT_VALUE.equals(pc.getPropIdoSbtList().getStringValue())) {
				if(pc.getPropIdoDateStart().getDateValue() != null 
						|| pc.getPropIdoDateEnd().getDateValue() != null){
					//フォーカスを異動種別にセット
					UtilSystem.getDisplayInfo().setTargetFocusId(
							pc.getPropIdoSbtList().getId());
					//メッセージ追加
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0006E
							, "異動日付を指定する場合は、異動出学種別"));
				}
			}
			
			//日付が空白で無い場合は大小チェック
			if ((pc.getPropIdoDateStart().getDateValue() != null)
					&& pc.getPropIdoDateEnd().getDateValue() != null) {
				//異動開始日<=異動終了日チェック
				int chkDate = UtilDate.compDateYYYYMMDD(
						UtilDate.editDate(pc.getPropIdoDateStart().getDateValue(), 1), 
						UtilDate.editDate(pc.getPropIdoDateEnd().getDateValue(), 1));
				
				if (chkDate == 2) {
					//異動開始日＞異動終了日場合はエラー
					String[] msgDate = new String[2];
					msgDate[0] = pc.getPropIdoDateStart().getName();
					msgDate[1] = pc.getPropIdoDateEnd().getName();
					//フォーカスを異動開始日にセット
					UtilSystem.getDisplayInfo().setTargetFocusId(
							pc.getPropIdoDateStart().getId());
					//メッセージ追加
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0023E
							, msgDate));
				}
			}
			if (message.size() != 0) {
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			
			//納付金選択チェック
			List payList = createPayList(pc.getPropPayhList().getList());
			if ((payList == null || payList.size() == 0) && pc.getPropPayOutType().getValue().toString().equals("0")) {
				//リストが空で、納付金出力条件指定が「一覧より選択」の場合
				//1件も選択されていない場合エラー
				message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0006E,NOCHECK_ERR_MSG));
				UtilSystem.getDisplayInfo().setDisplayMessage(message.getErrMsg());
				return ActionConst.RET_FALSE;
			}else{
				int cnt = 0;
				if (payList != null){
					//納付金の選択数チェック(1000件以上の選択はエラーとする)
					for (Iterator ite = payList.iterator(); ite.hasNext();) {
						Xrm00101L01Bean paywBean = (Xrm00101L01Bean) ite.next();
						if(paywBean.isPayChecked()){
							cnt++;
						}
						if(cnt > 999){
							//1000件以上なのでエラー
							message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0013E, "納付金は1000件以上"));
							break;
						}
					}
				}
			}
			
			//異動種別が未選択で、異動日付に入力がある場合はエラー
			if (Checkable.NO_SELECT_VALUE.equals(pc.getPropIdoSbtList().getStringValue())) {
				if(pc.getPropIdoDateStart().getDateValue() != null 
						|| pc.getPropIdoDateEnd().getDateValue() != null){
					//フォーカスを異動種別にセット
					UtilSystem.getDisplayInfo().setTargetFocusId(
							pc.getPropIdoSbtList().getId());
					//メッセージ追加
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0006E
							, "異動日付を指定する場合は、異動出学種別"));
				}
			}
			
			if (message.size() > 0) {
				//エラーが存在する場合
				//エラーメッセージ出力
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			
			//バッチのコンストを呼び出し
			bat = new Xrm00101BAT01(BatchConst.BATKBN_P, true);
			
			//選択納付金対象の管理NO取得
			Map kanriList = getGakuseiInfo(pc,payList);
			
			//区分
			String kbn = "PAY";
			
			//帳票タイトル
			//帳票タイトルテーブルよりデータ取得
			UtilGhPdf ghUtilPdf = new UtilGhPdf();
			String title = ghUtilPdf.loadDefaultItemValuePdfTitle(
					this.getDbs(), Xrm00101BAT01.PDF_FILEID_CHKLIST);
			
			//出力条件を取得
			//発行日付(java.sql.date)
			java.sql.Date hakkoDate = UtilDate.cnvSqlDate(pc.getpropHakkouDate().getDateValue());
			
			//通信欄
			//通信区分チェック
			boolean tsusinKbn = pc.getPropTushinKbn().isChecked();
			String tsusinTxt = "";
			
			
			//通信欄直接入力時
			String[] aryTsushinText=null;
			if(pc.getPropTushinKbn().isChecked()){
				//通信欄(ぺイジー)を取得
				aryTsushinText =getpayeasyTexSubString(pc.getPropTsushinText().getStringValue());
			}else{
				//iniファイルの既定文言を使用する場合
				//OS依存の改行コードを取得
				String crlf = System.getProperty("line.separator");
				String strTsushinText = XrmUtilConvert.toNoNullStr(UtilIni.getProductParameter(XrmIniConst.XRM
						, XrmIniConst.SEIKYUUOUT[0]
												 , XrmIniConst.SEIKYUUOUT[1]));
				
				// INIファイルの改行コード(\n)をOS依存の改行コードにて置換
				strTsushinText = strTsushinText.replaceAll("\\\\n", crlf);
				
				aryTsushinText = strTsushinText.split(crlf);
			}
			//出力区分
			String syutKbn = pc.getPropOutPutKbn().getStringValue();
			
			
			//学費年度
			SimpleDateFormat sdf = new SimpleDateFormat(GhConstant.GH_FORMAT_YYYY);
			int ghNendo = Integer.parseInt(sdf.format(pc.getPropGhNendo().getDateValue()));
			
			//学年
			String strGakunen =	UtilStr.cnvNull(pc.getPropGakunen().getStringValue());
			
			if ((strGakunen.equals("")) ||
					(strGakunen.equals(Checkable.NO_SELECT_VALUE))) {
				strGakunen = "0";
			} 
			
			//就学種別
			String syugaksyubetu = 
				UtilStr.cnvNull(pc.getPropSyugakSbt().getStringValue());
			if (syugaksyubetu.equals(Checkable.NO_SELECT_VALUE)) {
				syugaksyubetu = "";
			}
			
			//所属学科組織
			String szkGakka =
				UtilStr.cnvNull((pc.getPropSzkGakka().getStringValue()));
			if (szkGakka.equals(Checkable.NO_SELECT_VALUE)) {
				szkGakka = "";
			}
			
			//業務コード
			String gyomcd = pc.getPropgyoumList().getStringValue();
			if(gyomcd.equals(Checkable.NO_SELECT_VALUE)){
				gyomcd="";
			}
			
			//異動種別
			String idoSbt = pc.getPropIdoSbtList().getStringValue();
			if (idoSbt.equals(Checkable.NO_SELECT_VALUE)) {
				idoSbt = "";
			}
			//異動開始日の入力値を取得
			java.sql.Date idoDateFrom = UtilDate.cnvSqlDate(pc.getPropIdoDateStart().getDateValue());
			java.sql.Date idoDateTo =UtilDate.cnvSqlDate(pc.getPropIdoDateEnd().getDateValue());
			
			//11/13追加：直接入力選択時、納入期限と有効期限を取得(非選択じはnullでバッチへ)
			java.sql.Date nonyuKigen =null;
			java.sql.Date yukoKigen = null;
			if(pc.getPropKigenkbn().isChecked()){
				nonyuKigen= UtilDate.cnvSqlDate(pc.getPropNonyuDate().getDateValue());
				yukoKigen= UtilDate.cnvSqlDate(pc.getPropYukoDate().getDateValue());
			}
			//バッチに出力条件を設定
			bat.setOutputConditions(kbn,
					title,
					hakkoDate,
					tsusinKbn,
					aryTsushinText,
					ghNendo,
					syutKbn,
					strGakunen,
					syugaksyubetu,
					szkGakka,
					idoSbt,
					idoDateFrom,
					idoDateTo,
					kanriList,
					payList,
					gyomcd,
					nonyuKigen,
					yukoKigen);
			
			//オプションテーブルにデータ追加
			saveDefaultItemValue(pc);
			
			if (BatchLogic.startBatch(bat)) {
				//バッチ実行確認画面呼び出し (画面オンロード時に新規ウィンドウでオープン)
				Cos00401.open(bat);
				
			} else {
				//「処理実行中です。詳細はバッチ詳細情報を参照して下さい。」
				UtilSystem.getDisplayInfo().setPopupMessage(
						UtilProperty.getMsgString(SyMsgConst.SY_MSG_0010I));
			}
			
			return ActionConst.RET_TRUE;
			
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
	
	/**
	 * バッチ処理対象配当リスト作成<br>
	 * 処理対象チェックがついている配当情報をバッチ引数のリストに設定する。
	 * <br>
	 * @param  payList			(ArrayList)一覧に表示されいている配当情報
	 * @return list			(ArrayList)処理対象の配当情報
	 * @throws DbException
	 */
	private ArrayList createPayList(ArrayList payList) {
		ArrayList list = new ArrayList();
		
		for (Iterator it = payList.iterator(); it.hasNext();) {
			Xrm00101L01Bean payBean = (Xrm00101L01Bean) it.next();
			if (payBean.isPayChecked()) {
				list.add(payBean);
			}
		}
		
		if (list.size() == 0) {
			list = null;
		}
		
		return list;
	}
	
	
	/**
	 * 学費年度取得処理<br>
	 * 学費業務年度をint型で取得する。
	 * <br>
	 * @return gyomunendo		(int)業務年度
	 * @throws DbException
	 */
	protected int getGhNendo() throws DbException {
		
		UtilGhGyomuNendo ghUtilGyomuNendo = new UtilGhGyomuNendo();
		int gyomunendo = ghUtilGyomuNendo.getGyomuNendoInt(this.getDbs());
		
		return gyomunendo;
	}
	
	
	/**
	 * 前回入力値をロード<br>
	 * 前回入力した値をオプションテーブルから取得し画面に反映する。<br>
	 * オプションテーブルにデータが存在しない場合は、初期値を設定する。<br>
	 * (帳票タイトルについては帳票タイトルより名称を取得し設定する)
	 * <br>
	 * @param  pc 	(Xrm00101T01)ページコード
	 * @throws DbException
	 */
	private void loadDefaultItemValueOption(Xrm00101T01 pc)
	throws DbException {
		
		UtilCosOpt utilOpt = new UtilCosOpt(
				getDbs(),
				UtilSystem.getMySystemData().getLoginID(),
				UtilSystem.getFuncIdFromFormId(pc.getFormId()),
				Integer.parseInt(
						pc.getFormId().substring(pc.getFormId().length() - 1))
		);
		//データを先読みしてDAOのレコードキャッシュに格納
		utilOpt.preLoad();
		
		
		//出力区分
		pc.getPropOutPutKbn().setValue(utilOpt.getValue(pc.getPropOutPutKbn().getId()));
		
		
	}
	
	/**
	 * 今回入力値をセーブ<br>
	 * 今回入力値をオプションテーブルに保存する。
	 * <br>
	 * @param  pc 		(Xrm00101T01)ページコード
	 * @throws DbException
	 */
	private void saveDefaultItemValue(Xrm00101T01 pc)
	throws DbException {
		
		UtilCosOpt utilOpt = new UtilCosOpt(
				getDbs(),
				UtilSystem.getMySystemData().getLoginID(),
				UtilSystem.getFuncIdFromFormId(pc.getFormId()),
				Integer.parseInt(
						pc.getFormId().substring(pc.getFormId().length() - 1))
		);
		//データを先読みしてDAOのレコードキャッシュに格納
		utilOpt.preLoad();
		
		//@@@@@ (F-UK-345-00) JAST s.hase 2006/09/13 START
		
		//学年
		utilOpt.setValue(pc.getPropGakunen().getId(),
				pc.getPropGakunen().getStringValue());
		//所属学科組織
		utilOpt.setValue(pc.getPropSzkGakka().getId(),
				pc.getPropSzkGakka().getStringValue());
		//発行日付
		String hakko ="";
		Date hakkoDate = pc.getpropHakkouDate().getDateValue();
		if(hakkoDate!=null){
			hakko=UtilDate.editDate(hakkoDate,1);
		}
		utilOpt.setValue(pc.getpropHakkouDate().getId(),hakko);
		//出力区分
		utilOpt.setValue(pc.getPropOutPutKbn().getId(),pc.getPropOutPutKbn().getStringValue());
		
		//異動出学種別 
		utilOpt.setValue(pc.getPropIdoSbtList().getId(),
				pc.getPropIdoSbtList().getStringValue());
		//異動日付・開始
		String idoDateFrom = "";
		Date dateIdoDateSta = pc.getPropIdoDateStart().getDateValue();
		if (dateIdoDateSta != null) {
			idoDateFrom = UtilDate.editDate(dateIdoDateSta,1);
		}
		utilOpt.setValue(pc.getPropIdoDateStart().getId(), idoDateFrom);
		//異動日付・終了
		String idoDateTo = "";
		Date dateIdoDateEnd = pc.getPropIdoDateEnd().getDateValue();
		if (dateIdoDateEnd != null) {
			idoDateTo = UtilDate.editDate(dateIdoDateEnd,1);
		}
		utilOpt.setValue(pc.getPropIdoDateEnd().getId(), idoDateTo);
		
		//納付金出力条件指定
		utilOpt.setValue(pc.getPropPayOutType().getId(),
				pc.getPropPayOutType().getStringValue());
	}
	/**
	 * 請求書出力を行う学生の管理NOリスト取得
	 * @param pc
	 * @return
	 * @throws DbException
	 */
	protected Map getGakuseiInfo(Xrm00101T01 pc,List payList) throws DbException{
		long[] kanriNo =  new long[]{};
		ArrayList kanList = new ArrayList();
		
		List list=new ArrayList();
		//ハッシュマップ作成
		Map kanriMap = new HashMap();
		
		for(int i=0;i<payList.size();i++){
			Xrm00101L01Bean bean = new Xrm00101L01Bean();
			//チェックを行った配当情報を取得
			bean = (Xrm00101L01Bean)payList.get(i);
			java.sql.Date gnen = UtilDate.cnvSqlDate(pc.getPropGhNendo().getDateValue());
			String ne =convertDatetoString(gnen);
			int nend = Integer.parseInt(ne);
			
			
			//配当情報から割当分納を取得
			GhgPaywDAO ghgPaywDAO = (GhgPaywDAO)getDbs().getDao(GhgPaywDAO.class);
			List ghgPaywARList = ghgPaywDAO.findByGhgPayh(nend,
					bean.getPayCd(),
					bean.getPatternCd(),
					Integer.parseInt(bean.getBunnoKbnCd()));
			
			for(int f=0;f<ghgPaywARList.size();f++){
				GhgPaywAR ghgPaywAR = (GhgPaywAR)ghgPaywARList.get(f);
				//完納済みは除外
				if(ghgPaywAR.isPayEnd()){
					continue;
				}
				
				//管理NOを取得
				long kanri= ghgPaywAR.getKanriNo();
				String kanriN=Long.toString(kanri);
				
				//同一管理NOは取得しない
				if(kanriMap.containsKey(kanriN)){
					continue;
				}
				//ハッシュマップに管理NOをキーとして格納
				kanriMap.put(kanriN,kanriN);
				
				list.add(kanriN);
				
			}
		}			
		return kanriMap;
	}
	
	
	/**
	 * タブ遷移(納付金学生指定画面) <br>
	 * 納付金学生指定タブ画面へ遷移する
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws DbException
	 * @throws Exception
	 */
	protected String payGakTab(PageCodeBaseEx pagecode) throws DbException,Exception {
		
		//発行日付、通信区分、出力区分を３画面で共有する。
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		
		Xrm00101T02 nextPage = (Xrm00101T02)UtilSystem.getManagedBean(Xrm00101T02.class);
		
		nextPage.setPropHakkouDate(pc.getpropHakkouDate());
		nextPage.setPropTsushinText(pc.getPropTsushinText());
		nextPage.setPropTushinKbn(pc.getPropTushinKbn());
		nextPage.setPropOutPutKbn(pc.getPropOutPutKbn());
		nextPage.setPropNonyuDate(pc.getPropNonyuDate());
		nextPage.setPropYukoDate(pc.getPropYukoDate());
		nextPage.setPropKigenkbn(pc.getPropKigenkbn());
		
		return Xrm00101T02.FORMID;
	}
	
	/**
	 * タブ遷移(学生指定画面) <br>
	 * 学生指定タブ画面へ遷移する
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)ページコード
	 * @return String		処理が成功した場合"true"を、以外の場合は"false"を返す
	 * @throws DbException
	 * @throws Exception
	 */
	protected String gakTab(PageCodeBaseEx pagecode) throws DbException,Exception {
		
		//発行日付、通信区分、出力区分を３画面で共有する。
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		
		Xrm00101T03 nextPage = (Xrm00101T03)UtilSystem.getManagedBean(Xrm00101T03.class);
		
		nextPage.setPropHakkouDate(pc.getpropHakkouDate());
		nextPage.setPropTsushinText(pc.getPropTsushinText());
		nextPage.setPropTushinKbn(pc.getPropTushinKbn());
		nextPage.setPropOutPutKbn(pc.getPropOutPutKbn());
		nextPage.setPropNonyuDate(pc.getPropNonyuDate());
		nextPage.setPropYukoDate(pc.getPropYukoDate());
		nextPage.setPropKigenkbn(pc.getPropKigenkbn());
		
		return Xrm00101T03.FORMID;
	}
	
	/**
	 *現在日付の取得
	 *@return java.sql.Date 
	 */
	public java.sql.Date nowDate(){
		//	現在日時をsqlDateで取得
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		// java.sql.Dateに変換
		java.sql.Date now = new java.sql.Date(cal.getTimeInMillis());	
		
		return now;
	}
	
	/**
	 *通信欄の活性、非活性を操作する<br>
	 *@param pagecode
	 */
	protected String clickcheakBox(PageCodeBaseEx pagecode){
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		if(pc.getPropTushinKbn().isChecked()){
			pc.getPropTsushinText().setDisabled(false);
			return ActionConst.RET_TRUE;
		}else{
			pc.getPropTsushinText().setDisabled(true);
			return ActionConst.RET_TRUE;
		}
	}
	/**
	 *期限の活性、非活性を操作する<br>
	 *@param pagecode
	 */
	protected String clickKigencheakBox(PageCodeBaseEx pagecode){
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		if(pc.getPropKigenkbn().isChecked()){
			pc.getPropNonyuDate().setDisabled(false);
			//			pc.getPropNonyuDate().setReadonly(false);
			pc.getPropYukoDate().setDisabled(false);
			//			pc.getPropYukoDate().setReadonly(false);
			return ActionConst.RET_TRUE;
		}else{
			pc.getPropNonyuDate().setDisabled(true);
			//			pc.getPropNonyuDate().setReadonly(true);
			pc.getPropYukoDate().setDisabled(true);
			//			pc.getPropYukoDate().setReadonly(true);
			return ActionConst.RET_TRUE;
		}
	}
	
	/**
	 * チェック済みの納付金区分を取得<br>
	 * @param pagecode
	 * @return ArrayList
	 */
	protected ArrayList payKubunget(PageCodeBaseEx pagecode){
		ArrayList list = new ArrayList();
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		if(pc.getPropChkListSeika().isChecked()){
			list.add("A");
		}
		if(pc.getPropChkListKamokutori().isChecked()){
			list.add("B");
		}
		if(pc.getPropChkListToitu().isChecked()){
			list.add("C");
		}
		return list;
		
	}
	/**
	 * Date型のオブジェクトをString型に変換します.
	 */
	public String convertDatetoString(java.sql.Date date) {
		return (new SimpleDateFormat(DATE_PATTERN)).format(date);
	}
	/**
	 * ペイジー通信欄を、行で分割する。<br>
	 * @param strTsushinTextchk
	 * @return String[] nullの場合はカラ文字
	 */
	public String[] getpayeasyTexSubString(String strTsushinTextchk){
		//nullチェック
		if(strTsushinTextchk==null){
			strTsushinTextchk="";
		}	
		//OS依存の改行コードを取得
		String crlf = System.getProperty("line.separator");
		//通信欄(ぺイジー)を改行コードをキーに分割
		String[] aryTsushinTextChk = strTsushinTextchk.split(crlf);
		
		return aryTsushinTextChk;
	}
	
}
