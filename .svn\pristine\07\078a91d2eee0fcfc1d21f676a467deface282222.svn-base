<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb10104T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>明細情報入力</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_YosanTaniAJAX(thisObj, thisEvent) {
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlShikkoYsnTName";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	code['code2'] = thisObj.value;
	code['code3'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function func_YosanMokuAJAX(thisObj, thisEvent) {
	var servlet = 'rev/ke/MokuNameGensenKbnAJAX';
	var target  = '';	// ←空文字だけど必要
	var args    = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	args['code2'] = thisObj.value;
	var methodName = 'callBackMethod1';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, methodName);
}

function callBackMethod1(value) {
	document.getElementById('form1:htmlShikkoMokuName').innerHTML = value['key1'];
	document.getElementById('form1:htmlShikkoMokuName').title = value['key1'];
	if (document.getElementById('form1:htmlSknGensenKbn').value == '|no select|'  
		&& value['key2'] != '') {
		var comboBox = document.getElementById('form1:htmlSknGensenKbn');
		for (i=0; i<comboBox.length; i++) {
			if (comboBox.options[i].value == value['key2']) {
				comboBox.selectedIndex = i;
			}
		}
	}
}

function func_KeiriKamokAJAX(thisObj, thisEvent) {
	var servlet = 'rev/ke/KmkNameShohizeiKbnAJAX';
	var target  = '';	// ←空文字だけど必要
	var args    = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	args['code2'] = thisObj.value;
	var methodName = 'callBackMethod2';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, methodName);
}

function callBackMethod2(value) {
	document.getElementById('form1:htmlShikkoKmkName').innerHTML = value['key1'];
	document.getElementById('form1:htmlShikkoKmkName').title = value['key1'];
	var kmkCd = document.getElementById('form1:htmlShikkoKmkCd').value;
	var beforeShikkoKmk = document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value;
	if (kmkCd != beforeShikkoKmk) {
		if (value['key2'] != null && value['key2'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key2']) {
					comboBox.selectedIndex = i;
				}
			}
		} else if (value['key3'] != null && value['key3'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key3']) {
					comboBox.selectedIndex = i;
				}
			}
		}
	}
	document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value = kmkCd;
}

function func_KeiriKamokAJAX2(thisObj, thisEvent) {
	var servlet = 'rev/ke/KmkNameShohizeiKbnAJAX';
	var target  = '';	// ←空文字だけど必要
	var args    = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	args['code2'] = thisObj.value;
	var methodName = 'callBackMethod22';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, methodName);
}

function callBackMethod22(value) {
	var kmkCd = document.getElementById('form1:htmlShikkoKmkCd').value;
	var beforeShikkoKmk = document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value;
	if (kmkCd != beforeShikkoKmk) {
		if (value['key2'] != null && value['key2'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key2']) {
					comboBox.selectedIndex = i;
				}
			}
		} else if (value['key3'] != null && value['key3'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key3']) {
					comboBox.selectedIndex = i;
				}
			}
		}
	}
	document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value = kmkCd;
}

function func_ChuKubunAJAX(thisObj, thisEvent) {
	var servlet = "rev/co/CogChuKubunAJAX";
	var target = "form1:htmlChushutsuKbnName";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	code['code2'] = thisObj.value;
	code['code3'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function func_KobetKubunAJAX(thisObj, thisEvent) {
	var servlet = "rev/co/CogKobetKubunAJAX";
	var target = "form1:htmlKobetsuKbnName";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	code['code2'] = document.getElementById('form1:htmlShikkoYsnTCd').value;
	code['code3'] = thisObj.value;
	code['code4'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

var beforeTekiNaiyo = '';
function func_CogKeTekiNaiyoAJAX(thisObj, thisEvent) {
	if (beforeTekiNaiyo != thisObj.value) {
		beforeTekiNaiyo = thisObj.value;
		// 摘要名称（内容）を取得する
		var servlet = "rev/co/CogKeTekiAJAX";
		var target = "form1:htmlTekiyoNameNaiyo";
		var code = new Array();
		code['code1'] = '1';
		code['code2'] = thisObj.value;
		code['code3'] = '0';
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, code);
	}
}
function func_TekiyoNaiyoFocus(thisObj, thisEvent) {
	beforeTekiNaiyo = thisObj.value;
}

var beforeTekiYoto = '';
function func_CogKeTekiYotoAJAX(thisObj, thisEvent) {
	if (beforeTekiYoto != thisObj.value) {
		beforeTekiYoto = thisObj.value;
		// 摘要名称（用途）を取得する
		var servlet = "rev/co/CogKeTekiAJAX";
		var target = "form1:htmlTekiyoNameYoto";
		var code = new Array();
		code['code1'] = '2';
		code['code2'] = thisObj.value;
		code['code3'] = '0';
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, code);
	}
}
function func_TekiyoYotoFocus(thisObj, thisEvent) {
	beforeTekiYoto = thisObj.value;
}

function func_ShikkoGakuLostFocus(thisObj, thisEvent) {
	var shikkoGaku = parseInt('0');
	if (thisObj.value != null && thisObj.value != '') {
		shikkoGaku = parseInt(trimCanmma(thisObj.value));
	}
	var nyukinGakuGokei = parseInt(document.getElementById('form1:htmlNyukinGokeiHidden').value);
	var sonotaShikkoGakuGokei = parseInt(document.getElementById('form1:htmlSonotaShikkoGakuHidden').value);
	var shikkoGakuGokei = sonotaShikkoGakuGokei + shikkoGaku;
	var sagaku = nyukinGakuGokei - shikkoGakuGokei;
	document.getElementById('form1:htmlShikkoGakuGokeiHidden').value = shikkoGakuGokei;
	document.getElementById('form1:htmlShikkoGakuGokei').innerHTML = addCanmma(shikkoGakuGokei);
	document.getElementById('form1:htmlSagakuHidden').value = sagaku;
	document.getElementById('form1:htmlSagaku').innerHTML = addCanmma(sagaku);
}

/**
 * カンマ除去関数
 * @param value 対象文字列
 * @return カンマ除去された文字列
 */
function trimCanmma(value) {
	if (value == null || value == '') {
		return 0;
	}
	var afterValue = '';
	for (i = 0; i < value.length; i++) {
		if (value.substring(i,i+1) != ',' && value.substring(i,i+1) != '_') {
			afterValue = afterValue + value.substring(i,i+1);
		}
	}
	return afterValue;
}

/**
 * カンマ付与関数
 * @param x 対象文字列
 @ @return カンマ付けされた文字列
 */
function addCanmma(x) {
	var s = "" + x;
	var p = s.indexOf(".");
	if (p < 0) {
		p = s.length;
	}
	var r = s.substring(p, s.length);
	for (var i = 0; i < p; i++) {
		var c = s.substring(p - 1 - i, p - 1 - i + 1);
		if (c < "0" || c > "9") {
			r = s.substring(0, p - i) + r;
			break;
		}
		if (i > 0 && i % 3 == 0) {
			r = "," + r;
		}
		r = c + r;
	}
	return r;
}

function loadFunc() {
	func_YosanTaniAJAX(document.getElementById('form1:htmlShikkoYsnTCd'), '');
	func_YosanMokuAJAX(document.getElementById('form1:htmlShikkoMokuCd'), '');
	func_KeiriKamokAJAX(document.getElementById('form1:htmlShikkoKmkCd'), '');
	func_ChuKubunAJAX(document.getElementById('form1:htmlChushutsuKbn'), '');
	func_KobetKubunAJAX(document.getElementById('form1:htmlKobetsuKbn'), '');
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale="#{SYSTEM_DATA.locale"}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY onLoad="return loadFunc();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb10104T01.onPageLoadBegin}">

<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../../rev/inc/header.jsp" flush="" />

<!-- ヘッダーへのデータセット領域 -->
<DIV style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Keb10104T01.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" 
			  id="htmlFuncId" 
			  value="#{pc_Keb10104T01.funcId}">
</h:outputText>
<h:outputText styleClass="outputText" 
			  id="htmlLoginId" 
			  value="#{SYSTEM_DATA.loginID}">
</h:outputText>
<h:outputText styleClass="outputText" 
			  id="htmlScrnName" 
			  value="#{pc_Keb10104T01.screenName}">
</h:outputText>
</DIV>

<!--↓outer↓-->
<DIV class="outer">
<FIELDSET class="fieldset_err">
<LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" 
					value="戻る" 
					styleClass="commandExButton" 
					id="returnDisp" 
					action="#{pc_Keb10104T01.doReturnDispAction}">
</hx:commandExButton>
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   class="table" 
		   width="900">
		<TBODY>
			<TR>
				<TH class="v_a" width="150">
					<h:outputText styleClass="outputText" 
								  id="lblNyukinGokei" 
								  style="#{pc_Keb10104T01.keb10104.propNyukinGokei.labelStyle}" 
								  value="#{pc_Keb10104T01.keb10104.propNyukinGokei.name}" 
								  rendered="#{pc_Keb10104T01.keb10104.propNyukinGokei.rendered}">
					</h:outputText>
		   		</TH>
				<TD width="150">
					<h:inputHidden value="#{pc_Keb10104T01.keb10104.propNyukinGokei.longValue}"
								   id="htmlNyukinGokeiHidden">
					</h:inputHidden>
					<h:outputText styleClass="outputText" 
								  id="htmlNyukinGokei" 
								  style="#{pc_Keb10104T01.keb10104.propNyukinGokei.style}" 
								  value="#{pc_Keb10104T01.keb10104.propNyukinGokei.longValue}" 
								  rendered="#{pc_Keb10104T01.keb10104.propNyukinGokei.rendered}">
						<f:convertNumber pattern="#,###,###,###,##0;#,###,###,###,##0" />
					</h:outputText>
		   		</TD>
				<TH class="v_b" width="150">
					<h:outputText styleClass="outputText" 
								  id="lblShikkoGakuGokei" 
								  style="#{pc_Keb10104T01.keb10104.propShikkoGakuGokei.labelStyle}" 
								  value="#{pc_Keb10104T01.keb10104.propShikkoGakuGokei.name}" 
								  rendered="#{pc_Keb10104T01.keb10104.propShikkoGakuGokei.rendered}">
					</h:outputText>
		   		</TH>
				<TD width="150">
					<h:inputHidden value="#{pc_Keb10104T01.keb10104.propShikkoGakuGokei.longValue}"
								   id="htmlShikkoGakuGokeiHidden">
					</h:inputHidden>
					<h:outputText styleClass="outputText" 
								  id="htmlShikkoGakuGokei" 
								  style="#{pc_Keb10104T01.keb10104.propShikkoGakuGokei.style}" 
								  value="#{pc_Keb10104T01.keb10104.propShikkoGakuGokei.longValue}" 
								  rendered="#{pc_Keb10104T01.keb10104.propShikkoGakuGokei.rendered}">
						<f:convertNumber pattern="#,###,###,###,##0;#,###,###,###,##0" />
					</h:outputText>
		   		</TD>
				<TH class="v_c" width="150">
					<h:outputText styleClass="outputText" 
								  id="lblSagaku" 
								  style="#{pc_Keb10104T01.keb10104.propSagaku.labelStyle}" 
								  value="#{pc_Keb10104T01.keb10104.propSagaku.name}" 
								  rendered="#{pc_Keb10104T01.keb10104.propSagaku.rendered}">
					</h:outputText>
		   		</TH>
				<TD width="150">
					<h:inputHidden value="#{pc_Keb10104T01.keb10104.propSagaku.longValue}"
								   id="htmlSagakuHidden">
					</h:inputHidden>
					<h:outputText styleClass="outputText" 
								  id="htmlSagaku" 
								  style="#{pc_Keb10104T01.keb10104.propSagaku.style}" 
								  value="#{pc_Keb10104T01.keb10104.propSagaku.longValue}" 
								  rendered="#{pc_Keb10104T01.keb10104.propSagaku.rendered}">
						<f:convertNumber pattern="#,###,###,###,##0;#,###,###,###,##0" />
					</h:outputText>
		   		</TD>
		   </TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="900" 
		   style="margin-top: 7px;">
		<TBODY>
			<TR>
				<TD width="900" align="left">
					<hx:commandExButton type="button" 
										value="執行" 
										id="shikkoButton" 
										style="width: 180px" 
										styleClass="tab_head_on"></hx:commandExButton><hx:commandExButton 
										type="submit" 
										value="研究費" 
										id="kenkyuButton" 
										style="width: 180px" 
										styleClass="tab_head_off" 
										action="#{pc_Keb10104T01.doKenkyuAction}">
					</hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD>
					<TABLE border="0" 
						   cellpadding="0" 
						   cellspacing="0" 
						   width="900" 
						   height="445" 
						   class="tab_body">
						<TBODY>
							<TR>
								<TD align="center" valign="top">
									<TABLE border="0" 
										   cellpadding="0" 
										   cellspacing="0" 
										   width="883" 
										   class="table" 
										   style="margin-top: 7px;">
										<TBODY>
											<TR>
												<TD class="group_label_top" width="" colspan="2">
													<h:outputText styleClass="outputText" 
																  id="lblYsnHenseiInfo" 
																  value="予算編成情報">
													</h:outputText>
												</TD>
												<TD class="v_a" colspan="3" align="left">
													<hx:commandExButton type="submit" 
																		styleClass="commandExButton_search" 
																		id="ysnHenseiInfoSearch" 
																		value="検" 
																		disabled="#{pc_Keb10104T01.propYsnHenseiInfoSearch.disabled}" 
																		action="#{pc_Keb10104T01.doYsnHenseiInfoSearchAction}">
													</hx:commandExButton>
													<hx:commandExButton type="submit" 
																		styleClass="commandExButton_listclear" 
																		id="ysnHenseiInfoClear" 
																		value="ク" 
																		disabled="#{pc_Keb10104T01.propYsnHenseiInfoClear.disabled}" 
																		action="#{pc_Keb10104T01.doYsnHenseiInfoClearAction}">
													</hx:commandExButton>
													<!-- 画面項目自動表示処理用隠しボタン -->
													<DIV style="display:none;">
														<hx:commandExButton type="submit" 
																			styleClass="commandExButton" 
																			id="henseiAutoDisp" 
																			value="自動表示" 
																			action="#{pc_Keb10104T01.doHenseiAutoDispAction}">
														</hx:commandExButton>
													</DIV>
												</TD>
											</TR>
											<TR>
												<TD class="group_label" width="24" rowspan="3">
												</TD>
												<TH class="v_d" width="106">
													<h:outputText styleClass="outputText" 
																  id="lblHenseiYsnTCd" 
																  style="#{pc_Keb10104T01.propHenseiYsnTCd.labelStyle}" 
																  value="#{pc_Keb10104T01.propHenseiYsnTCd.name}">
													</h:outputText>
												</TH>
												<TD width="754" colspan="3">
													<h:inputHidden value="#{pc_Keb10104T01.propHenseiYsnTCdHidden.stringValue}"
																   id="htmlHenseiYsnTCdHidden">
													</h:inputHidden>
													<h:outputText styleClass="outputText" 
																  id="htmlHenseiYsnTCd" 
																  style="#{pc_Keb10104T01.propHenseiYsnTCd.style}" 
																  value="#{pc_Keb10104T01.propHenseiYsnTCd.stringValue}">
													</h:outputText>　
													<h:outputText styleClass="outputText" 
																  id="htmlHenseiYsnTName" 
																  style="#{pc_Keb10104T01.propHenseiYsnTName.style}" 
																  value="#{pc_Keb10104T01.propHenseiYsnTName.stringValue}">
													</h:outputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="">
													<h:outputText styleClass="outputText" 
																  id="lblHenseiMokuCd" 
																  style="#{pc_Keb10104T01.propHenseiMokuCd.labelStyle}" 
																  value="#{pc_Keb10104T01.propHenseiMokuCd.name}">
													</h:outputText>
												</TH>
												<TD width="">
													<h:inputHidden value="#{pc_Keb10104T01.propHenseiMokuCdHidden.stringValue}"
																   id="htmlHenseiMokuCdHidden">
													</h:inputHidden>
													<h:outputText styleClass="outputText" 
																  id="htmlHenseiMokuCd" 
																  style="#{pc_Keb10104T01.propHenseiMokuCd.style}" 
																  value="#{pc_Keb10104T01.propHenseiMokuCd.stringValue}">
													</h:outputText>　
													<h:outputText styleClass="outputText" 
																  id="htmlHenseiMokuName" 
																  style="#{pc_Keb10104T01.propHenseiMokuName.style}" 
																  value="#{pc_Keb10104T01.propHenseiMokuName.stringValue}">
													</h:outputText>
												</TD>
												<TH class="v_f" width="80">
													<h:outputText styleClass="outputText" 
																  id="lblMokuZandaka" 
																  style="#{pc_Keb10104T01.propMokuZandaka.labelStyle}" 
																  value="#{pc_Keb10104T01.propMokuZandaka.name}">
													</h:outputText>
												</TH>
												<TD width="120" style="text-align: right">
													<h:outputText styleClass="outputText" 
																  id="htmlMokuZandaka" 
																  style="#{pc_Keb10104T01.propMokuZandaka.style}" 
																  value="#{pc_Keb10104T01.propMokuZandaka.longValue}" 
																  rendered="#{pc_Keb10104T01.propMokuZandaka.rendered}">
														<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
													</h:outputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_g" width="">
													<h:outputText styleClass="outputText" 
																  id="lblHenseiKmkCd" 
																  style="#{pc_Keb10104T01.propHenseiKmkCd.labelStyle}" 
																  value="#{pc_Keb10104T01.propHenseiKmkCd.name}">
													</h:outputText>
												</TH>
												<TD width="">
													<h:inputHidden value="#{pc_Keb10104T01.propHenseiKmkCdHidden.stringValue}"
																   id="htmlHenseiKmkCdHidden">
													</h:inputHidden>
													<h:outputText styleClass="outputText" 
																  id="htmlHenseiKmkCd" 
																  style="#{pc_Keb10104T01.propHenseiKmkCd.style}" 
																  value="#{pc_Keb10104T01.propHenseiKmkCd.stringValue}">
													</h:outputText>　
													<h:outputText styleClass="outputText" 
																  id="htmlHenseiKmkName" 
																  style="#{pc_Keb10104T01.propHenseiKmkName.style}" 
																  value="#{pc_Keb10104T01.propHenseiKmkName.stringValue}">
													</h:outputText>
												</TD>
												<TH class="v_a" width="">
													<h:outputText styleClass="outputText" 
																  id="lblKmkZandaka" 
																  style="#{pc_Keb10104T01.propKmkZandaka.labelStyle}" 
																  value="#{pc_Keb10104T01.propKmkZandaka.name}">
													</h:outputText>
												</TH>
												<TD width="" style="text-align: right">
													<h:outputText styleClass="outputText" 
																  id="htmlKmkZandaka" 
																  style="#{pc_Keb10104T01.propKmkZandaka.style}" 
																  value="#{pc_Keb10104T01.propKmkZandaka.longValue}" 
																  rendered="#{pc_Keb10104T01.propKmkZandaka.rendered}">
														<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
													</h:outputText>
												</TD>
											</TR>
											<TR>
												<TD class="group_label_bottom" width="">
												</TD>
												<TH class="v_b" width="">
													<h:outputText styleClass="outputText" 
																  id="lblHenseiKomokuNo" 
																  style="#{pc_Keb10104T01.propHenseiKomokuNo.labelStyle}" 
																  value="#{pc_Keb10104T01.propHenseiKomokuNo.name}">
													</h:outputText>
												</TH>
												<TD width="554px" nowrap>
													<DIV style="width:554px;white-space:nowrap;overflow:hidden;display:block;">
														<h:inputHidden value="#{pc_Keb10104T01.propHenseiKomokuNoHidden.integerValue}"
																	   id="htmlHenseiKomokuNoHidden">
														</h:inputHidden>
														<h:outputText styleClass="outputText" 
																	  id="htmlHenseiKomokuNo" 
																	  style="#{pc_Keb10104T01.propHenseiKomokuNo.style}" 
																	  value="#{pc_Keb10104T01.propHenseiKomokuNo.integerValue}">
														</h:outputText>　
														<h:outputText styleClass="outputText" 
																	  id="htmlHenseiKomokuNaiyo" 
																	  style="#{pc_Keb10104T01.propHenseiKomokuNaiyo.style}" 
																	  value="#{pc_Keb10104T01.propHenseiKomokuNaiyo.stringValue}" 
																	  title="#{pc_Keb10104T01.propHenseiKomokuNaiyo.stringValue}">
														</h:outputText>
													</DIV>
												</TD>
												<TH class="v_c" width="">
													<h:outputText styleClass="outputText" 
																  id="lblKomokuZandaka" 
																  style="#{pc_Keb10104T01.propKomokuZandaka.labelStyle}" 
																  value="#{pc_Keb10104T01.propKomokuZandaka.name}">
													</h:outputText>
												</TH>
												<TD width="" style="text-align: right">
													<h:outputText styleClass="outputText" 
																  id="htmlKomokuZandaka" 
																  style="#{pc_Keb10104T01.propKomokuZandaka.style}" 
																  value="#{pc_Keb10104T01.propKomokuZandaka.longValue}" 
																  rendered="#{pc_Keb10104T01.propKomokuZandaka.rendered}">
														<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
													</h:outputText>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE border="0" 
										   cellpadding="0" 
										   cellspacing="0" 
										   width="880" 
										   class="table" 
										   style="margin-top: 5px;">
										<TBODY>
											<TR>
												<TH class="v_d" width="128" nowrap>
													<h:outputText styleClass="outputText" 
																  id="lblShikkoYsnTCd" 
																  style="#{pc_Keb10104T01.propShikkoYsnTCd.labelStyle}" 
																  value="#{pc_Keb10104T01.propShikkoYsnTCd.labelName}">
													</h:outputText>
												</TH>
												<TD width="752" nowrap colspan="3">
													<DIV style="width:752px;white-space:nowrap;overflow:hidden;display:block;">
														<h:inputText styleClass="inputText" 
																	 id="htmlShikkoYsnTCd" 
																	 size="10" 
																	 style="#{pc_Keb10104T01.propShikkoYsnTCd.style}" 
																	 maxlength="#{pc_Keb10104T01.propShikkoYsnTCd.maxLength}" 
																	 disabled="#{pc_Keb10104T01.propShikkoYsnTCd.disabled}" 
																	 readonly="#{pc_Keb10104T01.propShikkoYsnTCd.readonly}" 
																	 rendered="#{pc_Keb10104T01.propShikkoYsnTCd.rendered}" 
																	 value="#{pc_Keb10104T01.propShikkoYsnTCd.stringValue}" 
																	 onblur="return func_YosanTaniAJAX(this, event);">
														</h:inputText><hx:commandExButton type="submit" 
																			styleClass="commandExButton_search" 
																			id="shikkoYsnTaniSearch" 
																			action="#{pc_Keb10104T01.doShikkoYsnTaniSearchAction}">
														</hx:commandExButton>
														<h:outputText styleClass="outputText" 
																	  id="htmlShikkoYsnTName" 
																	  style="#{pc_Keb10104T01.propShikkoYsnTName.style}" 
																	  value="#{pc_Keb10104T01.propShikkoYsnTName.stringValue}" 
																	  title="#{pc_Keb10104T01.propShikkoYsnTName.stringValue}" 
																	  rendered="#{pc_Keb10104T01.propShikkoYsnTName.rendered}">
														</h:outputText>
													</DIV>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="">
													<h:outputText styleClass="outputText" 
																  id="lblShikkoMokuCd" 
																  style="#{pc_Keb10104T01.propShikkoMokuCd.labelStyle}" 
																  value="#{pc_Keb10104T01.propShikkoMokuCd.labelName}">
													</h:outputText>
												</TH>
												<TD width="752px" nowrap colspan="3">
													<DIV style="width:752px;white-space:nowrap;overflow:hidden;display:block;">
														<h:inputText styleClass="inputText" 
																	 id="htmlShikkoMokuCd" 
																	 size="10" 
																	 style="#{pc_Keb10104T01.propShikkoMokuCd.style}" 
																	 maxlength="#{pc_Keb10104T01.propShikkoMokuCd.maxLength}" 
																	 disabled="#{pc_Keb10104T01.propShikkoMokuCd.disabled}" 
																	 readonly="#{pc_Keb10104T01.propShikkoMokuCd.readonly}" 
																	 rendered="#{pc_Keb10104T01.propShikkoMokuCd.rendered}" 
																	 value="#{pc_Keb10104T01.propShikkoMokuCd.stringValue}" 
																	 onblur="return func_YosanMokuAJAX(this, event);">
														</h:inputText><hx:commandExButton type="submit" 
																			styleClass="commandExButton_search" 
																			id="shikkoMokuSearch" 
																			action="#{pc_Keb10104T01.doShikkoMokuSearchAction}">
														</hx:commandExButton>
														<h:outputText styleClass="outputText" 
																	  id="htmlShikkoMokuName" 
																	  style="#{pc_Keb10104T01.propShikkoMokuName.style}" 
																	  value="#{pc_Keb10104T01.propShikkoMokuName.stringValue}" 
																	  title="#{pc_Keb10104T01.propShikkoMokuName.stringValue}" 
																	  rendered="#{pc_Keb10104T01.propShikkoMokuName.rendered}">
														</h:outputText>
													</DIV>
												</TD>
											</TR>
											<TR>
												<TH class="v_f" width="">
													<h:outputText styleClass="outputText" 
																  id="lblShikkoKmkCd" 
																  style="#{pc_Keb10104T01.propShikkoKmkCd.labelStyle}" 
																  value="#{pc_Keb10104T01.propShikkoKmkCd.labelName}">
													</h:outputText>
												</TH>
												<TD width="752px" nowrap colspan="3">
													<DIV style="width:752px;white-space:nowrap;overflow:hidden;display:block;">
														<h:inputText styleClass="inputText" 
																	 id="htmlShikkoKmkCd" 
																	 size="12" 
																	 style="#{pc_Keb10104T01.propShikkoKmkCd.style}" 
																	 maxlength="#{pc_Keb10104T01.propShikkoKmkCd.maxLength}" 
																	 disabled="#{pc_Keb10104T01.propShikkoKmkCd.disabled}" 
																	 readonly="#{pc_Keb10104T01.propShikkoKmkCd.readonly}" 
																	 rendered="#{pc_Keb10104T01.propShikkoKmkCd.rendered}" 
																	 value="#{pc_Keb10104T01.propShikkoKmkCd.stringValue}" 
																	 onblur="return func_KeiriKamokAJAX(this, event);">
														</h:inputText>
														<hx:commandExButton type="submit" 
																			styleClass="commandExButton_search" 
																			id="shikkoKmkSearch" 
																			action="#{pc_Keb10104T01.doShikkoKmkSearchAction}">
														</hx:commandExButton>
														<!-- 画面項目自動表示処理用隠しボタン -->
														<DIV style="display:none;">
															<hx:commandExButton type="button"
																				styleClass="commandExButton"
																				id="shikkoKmkAutoDisp"
																				value="自動表示"
																				onclick="return func_KeiriKamokAJAX2(this, event);">
															</hx:commandExButton>
														</DIV>
														<h:outputText styleClass="outputText" 
																	  id="htmlShikkoKmkName" 
																	  style="#{pc_Keb10104T01.propShikkoKmkName.style}" 
																	  value="#{pc_Keb10104T01.propShikkoKmkName.stringValue}" 
																	  title="#{pc_Keb10104T01.propShikkoKmkName.stringValue}" 
																	  rendered="#{pc_Keb10104T01.propShikkoKmkName.rendered}">
														</h:outputText>
													</DIV>
												</TD>
											</TR>
											<TR>
												<TH class="v_g" width="">
													<h:outputText styleClass="outputText" 
																  id="lblShikkoGaku" 
																  style="#{pc_Keb10104T01.propShikkoGaku.labelStyle}" 
																  value="#{pc_Keb10104T01.propShikkoGaku.labelName}">
													</h:outputText>
												</TH>
												<TD width="" colspan="3">
													<h:inputText styleClass="inputText" id="htmlShikkoGaku"
													size="12"
													style="padding-right: 3px; text-align: right; #{pc_Keb10104T01.propShikkoGaku.style}"
													disabled="#{pc_Keb10104T01.propShikkoGaku.disabled}"
													readonly="#{pc_Keb10104T01.propShikkoGaku.readonly}"
													rendered="#{pc_Keb10104T01.propShikkoGaku.rendered}"
													value="#{pc_Keb10104T01.propShikkoGaku.stringValue}"
													onblur="return func_ShikkoGakuLostFocus(this, event);">
													<hx:inputHelperAssist errorClass="inputText_Error" />
												</h:inputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_a" width="">
													<h:outputText styleClass="outputText" 
																  id="lblShohizeiKbn" 
																  style="#{pc_Keb10104T01.propShohizeiKbn.labelStyle}" 
																  value="#{pc_Keb10104T01.propShohizeiKbn.labelName}">
													</h:outputText>
												</TH>
												<TD width="" colspan="3">
													<h:selectOneMenu styleClass="selectOneMenu" 
																	 id="htmlShohizeiKbn" 
																	 value="#{pc_Keb10104T01.propShohizeiKbn.value}" 
																	 style="#{pc_Keb10104T01.propShohizeiKbn.style}" 
																	 disabled="#{pc_Keb10104T01.propShohizeiKbn.disabled}" 
																	 readonly="#{pc_Keb10104T01.propShohizeiKbn.readonly}" 
																	 rendered="#{pc_Keb10104T01.propShohizeiKbn.rendered}">
														<f:selectItems value="#{pc_Keb10104T01.propShohizeiKbn.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
											<TR>
												<TH class="v_b" width="">
													<h:outputText styleClass="outputText" 
																  id="lblKobetsuKbn" 
																  style="#{pc_Keb10104T01.propKobetsuKbn.labelStyle}" 
																  value="#{pc_Keb10104T01.propKobetsuKbn.labelName}">
													</h:outputText>
												</TH>
												<TD width="752px" nowrap colspan="3">
													<DIV style="width:752px;white-space:nowrap;overflow:hidden;display:block;">
														<h:inputText styleClass="inputText" 
																	 id="htmlKobetsuKbn" 
																	 size="5" 
																	 style="#{pc_Keb10104T01.propKobetsuKbn.style}" 
																	 maxlength="#{pc_Keb10104T01.propKobetsuKbn.maxLength}" 
																	 disabled="#{pc_Keb10104T01.propKobetsuKbn.disabled}" 
																	 readonly="#{pc_Keb10104T01.propKobetsuKbn.readonly}" 
																	 rendered="#{pc_Keb10104T01.propKobetsuKbn.rendered}" 
																	 value="#{pc_Keb10104T01.propKobetsuKbn.stringValue}" 
																	 onblur="return func_KobetKubunAJAX(this, event);">
														</h:inputText><hx:commandExButton type="submit" 
																			styleClass="commandExButton_search" 
																			id="kobetsuKbnSearch" 
																			action="#{pc_Keb10104T01.doKobetsuKbnSearchAction}">
														</hx:commandExButton>
														<h:outputText styleClass="outputText" 
																	  id="htmlKobetsuKbnName" 
																	  style="#{pc_Keb10104T01.propKobetsuKbnName.style}" 
																	  value="#{pc_Keb10104T01.propKobetsuKbnName.stringValue}" 
																	  title="#{pc_Keb10104T01.propKobetsuKbnName.stringValue}" 
																	  rendered="#{pc_Keb10104T01.propKobetsuKbnName.rendered}">
														</h:outputText>
													</DIV>
												</TD>
											</TR>
											<TR>
												<TH class="v_c" width="">
													<h:outputText styleClass="outputText" 
																  id="lblChushutsuKbn" 
																  style="#{pc_Keb10104T01.propChushutsuKbn.labelStyle}" 
																  value="#{pc_Keb10104T01.propChushutsuKbn.labelName}">
													</h:outputText>
												</TH>
												<TD width="752px" nowrap colspan="3">
													<DIV style="width:752px;white-space:nowrap;overflow:hidden;display:block;">
														<h:inputText styleClass="inputText" 
																	 id="htmlChushutsuKbn" 
																	 size="5" 
																	 style="#{pc_Keb10104T01.propChushutsuKbn.style}" 
																	 maxlength="#{pc_Keb10104T01.propChushutsuKbn.maxLength}" 
																	 disabled="#{pc_Keb10104T01.propChushutsuKbn.disabled}" 
																	 readonly="#{pc_Keb10104T01.propChushutsuKbn.readonly}" 
																	 rendered="#{pc_Keb10104T01.propChushutsuKbn.rendered}" 
																	 value="#{pc_Keb10104T01.propChushutsuKbn.stringValue}" 
																	 onblur="return func_ChuKubunAJAX(this, event);">
														</h:inputText><hx:commandExButton type="submit" 
																			styleClass="commandExButton_search" 
																			id="chushutsuKbnSearch" 
																			action="#{pc_Keb10104T01.doChushutsuKbnSearchAction}">
														</hx:commandExButton>
														<h:outputText styleClass="outputText" 
																	  id="htmlChushutsuKbnName" 
																	  style="#{pc_Keb10104T01.propChushutsuKbnName.style}" 
																	  value="#{pc_Keb10104T01.propChushutsuKbnName.stringValue}" 
																	  title="#{pc_Keb10104T01.propChushutsuKbnName.stringValue}" 
																	  rendered="#{pc_Keb10104T01.propChushutsuKbnName.rendered}">
														</h:outputText>
													</DIV>
												</TD>
											</TR>
											<TR>
												<TH class="v_d" width="">
													<h:outputText styleClass="outputText" 
																  id="lblSknGensenKbn" 
																  style="#{pc_Keb10104T01.propSknGensenKbn.labelStyle}" 
																  value="#{pc_Keb10104T01.propSknGensenKbn.labelName}">
													</h:outputText>
												</TH>
												<TD width="" colspan="3">
													<h:selectOneMenu styleClass="selectOneMenu" 
																	 id="htmlSknGensenKbn" 
																	 value="#{pc_Keb10104T01.propSknGensenKbn.value}" 
																	 style="#{pc_Keb10104T01.propSknGensenKbn.style}" 
																	 disabled="#{pc_Keb10104T01.propSknGensenKbn.disabled}" 
																	 readonly="#{pc_Keb10104T01.propSknGensenKbn.readonly}" 
																	 rendered="#{pc_Keb10104T01.propSknGensenKbn.rendered}">
														<f:selectItems value="#{pc_Keb10104T01.propSknGensenKbn.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="">
													<h:outputText styleClass="outputText" 
																  id="lblTekiyoNameHizuke" 
																  style="#{pc_Keb10104T01.propTekiyoNameHizuke.labelStyle}" 
																  value="#{pc_Keb10104T01.propTekiyoNameHizuke.labelName}">
													</h:outputText>
												</TH>
												<TD width="" colspan="3">
													<h:inputText styleClass="inputText" 
																 id="htmlTekiyoNameHizuke" 
																 size="30" 
																 style="#{pc_Keb10104T01.propTekiyoNameHizuke.style}" 
																 maxlength="#{pc_Keb10104T01.propTekiyoNameHizuke.maxLength}" 
																 disabled="#{pc_Keb10104T01.propTekiyoNameHizuke.disabled}" 
																 readonly="#{pc_Keb10104T01.propTekiyoNameHizuke.readonly}" 
																 rendered="#{pc_Keb10104T01.propTekiyoNameHizuke.rendered}" 
																 value="#{pc_Keb10104T01.propTekiyoNameHizuke.stringValue}">
													</h:inputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_f" width="">
													<h:outputText styleClass="outputText" 
																  id="lblTekiyoCdNaiyo" 
																  style="#{pc_Keb10104T01.propTekiyoCdNaiyo.labelStyle}" 
																  value="#{pc_Keb10104T01.propTekiyoCdNaiyo.labelName}">
													</h:outputText>
												</TH>
												<TD width="100">
													<h:inputText styleClass="inputText" 
																 id="htmlTekiyoCdNaiyo" 
																 size="6" 
																 style="#{pc_Keb10104T01.propTekiyoCdNaiyo.style}" 
																 maxlength="#{pc_Keb10104T01.propTekiyoCdNaiyo.maxLength}" 
																 disabled="#{pc_Keb10104T01.propTekiyoCdNaiyo.disabled}" 
																 readonly="#{pc_Keb10104T01.propTekiyoCdNaiyo.readonly}" 
																 rendered="#{pc_Keb10104T01.propTekiyoCdNaiyo.rendered}" 
																 value="#{pc_Keb10104T01.propTekiyoCdNaiyo.stringValue}" 
																 onblur="return func_CogKeTekiNaiyoAJAX(this, event);" 
																 onfocus="return func_TekiyoNaiyoFocus(this, event);">
													</h:inputText><hx:commandExButton type="submit" 
																		styleClass="commandExButton_search" 
																		id="tekiyoNaiyoSearch" 
																		action="#{pc_Keb10104T01.doTekiyoNaiyoSearchAction}">
													</hx:commandExButton>
												</TD>
												<TH class="v_g" width="120">
													<h:outputText styleClass="outputText" 
																  id="lblTekiyoNameNaiyo" 
																  style="#{pc_Keb10104T01.propTekiyoNameNaiyo.labelStyle}" 
																  value="#{pc_Keb10104T01.propTekiyoNameNaiyo.labelName}">
													</h:outputText>
												</TH>
												<TD width="" nowrap>
													<h:inputText styleClass="inputText" 
																 id="htmlTekiyoNameNaiyo" 
																 size="68" 
																 style="#{pc_Keb10104T01.propTekiyoNameNaiyo.style}" 
																 maxlength="#{pc_Keb10104T01.propTekiyoNameNaiyo.maxLength}" 
																 disabled="#{pc_Keb10104T01.propTekiyoNameNaiyo.disabled}" 
																 readonly="#{pc_Keb10104T01.propTekiyoNameNaiyo.readonly}" 
																 rendered="#{pc_Keb10104T01.propTekiyoNameNaiyo.rendered}" 
																 value="#{pc_Keb10104T01.propTekiyoNameNaiyo.stringValue}">
													</h:inputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_a" width="">
													<h:outputText styleClass="outputText" 
																  id="lblTekiyoCdYoto" 
																  style="#{pc_Keb10104T01.propTekiyoCdYoto.labelStyle}" 
																  value="#{pc_Keb10104T01.propTekiyoCdYoto.labelName}">
													</h:outputText>
												</TH>
												<TD width="">
													<h:inputText styleClass="inputText" 
																 id="htmlTekiyoCdYoto" 
																 size="6" 
																 style="#{pc_Keb10104T01.propTekiyoCdYoto.style}" 
																 maxlength="#{pc_Keb10104T01.propTekiyoCdYoto.maxLength}" 
																 disabled="#{pc_Keb10104T01.propTekiyoCdYoto.disabled}" 
																 readonly="#{pc_Keb10104T01.propTekiyoCdYoto.readonly}" 
																 rendered="#{pc_Keb10104T01.propTekiyoCdYoto.rendered}" 
																 value="#{pc_Keb10104T01.propTekiyoCdYoto.stringValue}" 
																 onblur="return func_CogKeTekiYotoAJAX(this, event);" 
																 onfocus="return func_TekiyoYotoFocus(this, event);">
													</h:inputText><hx:commandExButton type="submit" 
																		styleClass="commandExButton_search" 
																		id="tekiyoYotoSearch" 
																		action="#{pc_Keb10104T01.doTekiyoYotoSearchAction}">
													</hx:commandExButton>
												</TD>
												<TH class="v_b" width="" nowrap>
													<h:outputText styleClass="outputText" 
																  id="lblTekiyoNameYoto" 
																  style="#{pc_Keb10104T01.propTekiyoNameYoto.labelStyle}" 
																  value="#{pc_Keb10104T01.propTekiyoNameYoto.labelName}">
													</h:outputText>
												</TH>
												<TD width="">
													<h:inputText styleClass="inputText" 
																 id="htmlTekiyoNameYoto" 
																 size="68" 
																 style="#{pc_Keb10104T01.propTekiyoNameYoto.style}" 
																 maxlength="#{pc_Keb10104T01.propTekiyoNameYoto.maxLength}" 
																 disabled="#{pc_Keb10104T01.propTekiyoNameYoto.disabled}" 
																 readonly="#{pc_Keb10104T01.propTekiyoNameYoto.readonly}" 
																 rendered="#{pc_Keb10104T01.propTekiyoNameYoto.rendered}" 
																 value="#{pc_Keb10104T01.propTekiyoNameYoto.stringValue}">
													</h:inputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_c" width="">
													<h:outputText styleClass="outputText" 
																  id="lblBiko" 
																  style="#{pc_Keb10104T01.propBiko.labelStyle}" 
																  value="#{pc_Keb10104T01.propBiko.labelName}">
													</h:outputText>
												</TH>
												<TD width="" colspan="3">
													<h:inputTextarea styleClass="inputTextarea" 
																	 id="htmlBiko" 
																	 cols="81" 
																	 rows="3" 
																	 style="#{pc_Keb10104T01.propBiko.style}" 
																	 readonly="#{pc_Keb10104T01.propBiko.readonly}" 
																	 disabled="#{pc_Keb10104T01.propBiko.disabled}" 
																	 value="#{pc_Keb10104T01.propBiko.stringValue}">
													</h:inputTextarea>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									<BR>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style="margin-top: 0px;" 
		   class="button_bar" 
		   width="900">
		<TBODY>
			<TR>
				<TD align="center" style="margin-top:0px;">
					<hx:commandExButton type="submit" 
										value="確定" 
										styleClass="commandExButton_dat" 
										id="kakutei" 
										action="#{pc_Keb10104T01.doKakuteiAction}" 
										disabled="#{pc_Keb10104T01.propKakutei.disabled}" 
										rendered="#{pc_Keb10104T01.propKakutei.rendered}" 
										style="#{pc_Keb10104T01.propKakutei.style}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="クリア" 
										styleClass="commandExButton_etc" 
										id="clear" 
										action="#{pc_Keb10104T01.doClearAction}" 
										disabled="#{pc_Keb10104T01.propClear.disabled}" 
										rendered="#{pc_Keb10104T01.propClear.rendered}" 
										style="#{pc_Keb10104T01.propClear.style}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../../rev/inc/footer.jsp" flush="" />
	<h:inputHidden id="htmlKaikeiNendoHidden" 
				   value="#{pc_Keb10104T01.propKaikeiNendoHidden.integerValue}">
	</h:inputHidden>
	<h:inputHidden id="htmlSonotaShikkoGakuHidden" 
				   value="#{pc_Keb10104T01.keb10104.propSonotaShikkoGakuHidden.longValue}">
	</h:inputHidden>
	<h:inputHidden value="#{pc_Keb10104T01.propBeforeShikkoKmkCdHidden.stringValue}" 
				   id="htmlBeforeShikkoKmkCdHidden">
	</h:inputHidden>
	<h:inputHidden
		value="htmlShikkoGaku=###,###,###,##0;###,###,###,##0"
		id="htmlFormatNumberOption"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../../rev/inc/common.jsp" flush="" />
</f:view>

</HTML>
