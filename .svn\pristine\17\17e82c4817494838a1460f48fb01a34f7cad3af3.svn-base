<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea01701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea01701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../../rev/ke/inc/gakuenKE.css"  >	

<style type="text/css">
<!--
 .setWidth TD {width: 110px; white-space: nowrap;}
 .setWidth2 TD {width: 100px; white-space: nowrap;}
 -->
</style>

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea01701.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea01701.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea01701.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea01701.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトのため全角１文字 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Kea01701.propKaikeiNendo.name}"
										style="#{pc_Kea01701.propKaikeiNendo.labelStyle}"></h:outputText></TH>
									<TD width="660">
									<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border">
									<TR>
									<TD width="60" nowrap><h:outputText
										styleClass="outputText" id="htmlKaikeiNendo"
										value="#{pc_Kea01701.propKaikeiNendo.dateValue}"
										style="#{pc_Kea01701.propKaikeiNendo.style}">
										<f:convertDateTime pattern="yyyy" />
										</h:outputText>
										<h:outputText styleClass="outputText"
										id="htmlKaikeiNendoDsp"
										value="#{pc_Kea01701.propKaikeiNendoDsp.stringValue}"
										style="#{pc_Kea01701.propKaikeiNendoDsp.style}"></h:outputText>
									</TD>
									<TD width=""   align="left" nowrap><h:outputText styleClass="outputText"
										id="htmlHoseiKaiji"
										value="#{pc_Kea01701.propHoseiKaiji.integerValue}"
										style="#{pc_Kea01701.propHoseiKaiji.style}">
										<f:convertNumber pattern="##" /></h:outputText><h:outputText styleClass="outputText"
										id="htmlHoseiKaijiDsp"
										value="#{pc_Kea01701.propHoseiKaijiDsp.stringValue}"
										style="#{pc_Kea01701.propHoseiKaijiDsp.style}"></h:outputText></TD>
								</TR>
								</TABLE>
								</TD>
								<TR>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblShikinShohiKbn"
										value="#{pc_Kea01701.propShikinShohiKbn.name}"
										style="#{pc_Kea01701.propShikinShohiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectManyCheckbox
										styleClass="selectManyCheckbox setWidth"
										disabledClass="selectManyCheckbox_Disabled"
										id="htmlShikinShohiKbn" tabindex="2"
										value="#{pc_Kea01701.propShikinShohiKbn.stringValue}"
										disabled="#{pc_Kea01701.propShikinShohiKbn.disabled}"
										readonly="#{pc_Kea01701.propShikinShohiKbn.readonly}"
										rendered="#{pc_Kea01701.propShikinShohiKbn.rendered}"
										style="#{pc_Kea01701.propShikinShohiKbn.style}">
										<f:selectItems value="#{pc_Kea01701.propShikinShohiKbn.list}" />
										</h:selectManyCheckbox></TD>
								</TR>
								<TR>
									<TH class="v_d"><h:outputText
										styleClass="outputText" id="lblYsnIkkatsuKbn"
										value="#{pc_Kea01701.propYsnIkkatsuKbn.name}"
										style="#{pc_Kea01701.propYsnIkkatsuKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlYsnIkkatsuKbn" tabindex="3" style="width:300px;"
										value="#{pc_Kea01701.propYsnIkkatsuKbn.stringValue}"
										disabled="#{pc_Kea01701.propYsnIkkatsuKbn.disabled}"
										readonly="#{pc_Kea01701.propYsnIkkatsuKbn.readonly}">
										<f:selectItems value="#{pc_Kea01701.propYsnIkkatsuKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_e">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFile"
													value="#{pc_Kea01701.propInputFile.name}"
													style="#{pc_Kea01701.propInputFile.labelStyle}"></h:outputText></TH>
											</TR>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFilePre"
													value="#{pc_Kea01701.propInputFilePre.name}"
													style="#{pc_Kea01701.propInputFilePre.labelStyle}"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									<TD class="">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border"><hx:fileupload
													styleClass="fileupload" id="htmlInputFile" size="50"
													value="#{pc_Kea01701.propInputFile.value}" tabindex="4"
													style="width:530px">
													<hx:fileProp name="fileName"
														value="#{pc_Kea01701.propInputFile.fileName}" />
													<hx:fileProp name="contentType"
														value="#{pc_Kea01701.propInputFile.contentType}" />
												</hx:fileupload></TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:outputText
													styleClass="outputText" id="htmlInputFilePre"
													value="#{pc_Kea01701.propInputFilePre.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_f"><h:outputText
										styleClass="outputText" id="lblRegKbn"
										value="#{pc_Kea01701.propRegKbn.name}"
										style="#{pc_Kea01701.propRegKbn.labelStyle}"></h:outputText></TH>
									<TD class=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRegKbn" tabindex="5"
										layout="pageDirection"
										value="#{pc_Kea01701.propRegKbn.stringValue}"
										readonly="#{pc_Kea01701.propRegKbn.readonly}"
										disabled="#{pc_Kea01701.propRegKbn.disabled}">
										<f:selectItems value="#{pc_Kea01701.propRegKbn.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_g"><h:outputText
										styleClass="outputText" id="lblSyoriKbnL"
										value="#{pc_Kea01701.propSyoriKbn.name}"
										style="#{pc_Kea01701.propSyoriKbn.labelStyle}"></h:outputText></TH>
									<TD class="">
									<TABLE border="0" class="">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlSyoriKbn" tabindex="6"
													value="#{pc_Kea01701.propSyoriKbn.checked}"
													disabled="#{pc_Kea01701.propSyoriKbn.disabled}"
													readonly="#{pc_Kea01701.propSyoriKbn.readonly}"></h:selectBooleanCheckbox>
													<h:outputText
													styleClass="outputText" id="lblSyoriKbn"
													value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_a"><h:outputText
										styleClass="outputText" id="lblDeleteListL"
										value="#{pc_Kea01701.propDeleteList.name}"
										style="#{pc_Kea01701.propDeleteList.labelStyle}"></h:outputText></TH>
									<TD class="">
									<TABLE border="0" class="">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlDeleteList" tabindex="7"
													value="#{pc_Kea01701.propDeleteList.checked}"
													disabled="#{pc_Kea01701.propDeleteList.disabled}"
													readonly="#{pc_Kea01701.propDeleteList.readonly}"></h:selectBooleanCheckbox>
													<h:outputText
													styleClass="outputText" id="lblDeleteList"
													value="出力"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText
										styleClass="outputText" id="lblChkList"
										value="#{pc_Kea01701.propChkListNormal.name}"
										style="#{pc_Kea01701.propChkListNormal.labelStyle}"></h:outputText></TH>
									<TD>
									<TABLE border="0" class="">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlChkListNormal" tabindex="8"
													value="#{pc_Kea01701.propChkListNormal.checked}"
													disabled="#{pc_Kea01701.propChkListNormal.disabled}"
													readonly="#{pc_Kea01701.propChkListNormal.readonly}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListNormal" 
													value="正常データ"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlChkListError" tabindex="9"
													value="#{pc_Kea01701.propChkListError.checked}"
													readonly="#{pc_Kea01701.propChkListError.readonly}"
													disabled="#{pc_Kea01701.propChkListError.disabled}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListError"
													value="エラーデータ"></h:outputText></TD>
											</TR>
											<TR>
												<TD class="clear_border">
													<h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlChkListWarning" tabindex="10"
													value="#{pc_Kea01701.propChkListWarning.checked}"
													disabled="#{pc_Kea01701.propChkListWarning.disabled}"
													readonly="#{pc_Kea01701.propChkListWarning.readonly}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblChkListWarning"
													value="ワーニングデータ"></h:outputText>
                                                </TD>
											</TR>
										</TBODY>
									</TABLE>
                                  </TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="700" border="0" class="button_bar" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="入力項目指定"
										styleClass="commandExButton_etc" id="setinput" tabindex="19"
										action="#{pc_Kea01701.doSetinputAction}"
										disabled="#{pc_Kea01701.propSetinput.disabled}"
										rendered="#{pc_Kea01701.propSetinput.rendered}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_dat" id="exec" tabindex="20"
										action="#{pc_Kea01701.doExecAction}"
										disabled="#{pc_Kea01701.propExec.disabled}"
										rendered="#{pc_Kea01701.propExec.rendered}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

