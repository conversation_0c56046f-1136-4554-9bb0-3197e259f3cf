<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00401T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsc00401T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1";
	if (document.getElementById('form1:htmlExecutableFlg').value == "0") {
		indirectClick('fix');
	} else {
		indirectClick('delete');
	}
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutable').value = "2";
	if (document.getElementById('form1:htmlExecutableFlg').value == "0") {
		indirectClick('fix');
	} else {
		indirectClick('delete');
	}
}

function func_1(thisObj, thisEvent) {
	var zipNo = document.getElementById("form1:htmlSgnAddrNo").value;
	zipNo = encodeURIComponent(zipNo);
	var add = document.getElementById("form1:htmlSgnAddr1").value;
	add = encodeURIComponent(add);
	var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp"
		   +"?"
  		   +"zipNo=form1:htmlSgnAddrNo"
  		   +"&"
  		   +"zipNoValue="+zipNo
  		   +"&"
  		   +"jyusyoKanji=form1:htmlSgnAddr1"
  		   +"&"
  		   +"jyusyoValue="+add
  		   +"&"
  		   +"jyusyoKana=form1:htmlSgnAddrKana1";

	openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");
	return true;
}

function func_2(thisObj, thisEvent) {
	//ローカル用
	openModalWindow("", "PNsc0101", "<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
	setTarget("PNsc0101");
	return true;
}
function func_3(thisObj, thisEvent) {
//
}
function func_4(thisObj, thisEvent) {
	//ローカル用
	openModalWindow("", "PNsc0101", "<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
	setTarget("PNsc0101");
	return true;

}
function func_5(thisObj, thisEvent) {
//
}
window.onload = function (){
	setZipMenu('form1:htmlSgnAddrNo','form1:htmlSgnAddr1','form1:htmlSgnAddrKana1');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc00401T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc00401T02.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc00401T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc00401T02.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD style="" rowspan="7">

									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%">
										<TBODY>
											<TR>
												<TH style="" class="v_a" width="130" colspan="2"><h:outputText
													styleClass="outputText" id="lblNyushiNendoH"
													value="#{pc_Nsc00401T02.nsc00401.propNyushiNendo.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propNyushiNendo.labelStyle}"></h:outputText></TH><TD width="130"><h:inputText
													styleClass="inputText" id="htmlNyushiNendoH" size="4"
													value="#{pc_Nsc00401T02.nsc00401.propNyushiNendo.dateValue}"
													disabled="#{pc_Nsc00401T02.nsc00401.propNyushiNendo.disabled}"
													readonly="#{pc_Nsc00401T02.nsc00401.propNyushiNendo.readonly}"
													style="#{pc_Nsc00401T02.nsc00401.propNyushiNendo.style}" tabindex="1">
													<f:convertDateTime pattern="yyyy" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TH width="130" class="v_b"><h:outputText
													styleClass="outputText" id="lblNyushiGakkiNoH"
													value="#{pc_Nsc00401T02.nsc00401.propNyushiGakkiNo.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propNyushiGakkiNo.labelStyle}">
												</h:outputText></TH>
												<TD><h:inputText styleClass="inputText"
													id="htmlNyushiGakkiNoH" size="2"
													value="#{pc_Nsc00401T02.nsc00401.propNyushiGakkiNo.integerValue}"
													disabled="#{pc_Nsc00401T02.nsc00401.propNyushiGakkiNo.disabled}"
													readonly="#{pc_Nsc00401T02.nsc00401.propNyushiGakkiNo.readonly}"
													style="#{pc_Nsc00401T02.nsc00401.propNyushiGakkiNo.style}" tabindex="2">
													<f:convertNumber pattern="#0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
											<TR>
												<TH style="" class="v_c" colspan="2"><h:outputText
													styleClass="outputText" id="lblSgnCdH"
													value="#{pc_Nsc00401T02.nsc00401.propSgnCd.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propSgnCd.labelStyle}"></h:outputText></TH><TD><h:inputText
													styleClass="inputText" id="htmlSgnCdH" size="17"
													value="#{pc_Nsc00401T02.nsc00401.propSgnCd.stringValue}"
													disabled="#{pc_Nsc00401T02.nsc00401.propSgnCd.disabled}"
													readonly="#{pc_Nsc00401T02.nsc00401.propSgnCd.readonly}"
													style="#{pc_Nsc00401T02.nsc00401.propSgnCd.style}"
													maxlength="#{pc_Nsc00401T02.nsc00401.propSgnCd.maxLength}" tabindex="5" onblur="return func_3(this, event);"></h:inputText><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchSgn" tabindex="6"
													onclick="return func_2(this, event);"
													action="#{pc_Nsc00401T02.doSearchSgnAction}"
													disabled="#{pc_Nsc00401T02.nsc00401.propSearchSgn.disabled}"></hx:commandExButton></TD>
												<TH class="v_d"><h:outputText styleClass="outputText"
													id="lblJukenCdH"
													value="#{pc_Nsc00401T02.nsc00401.propJukenCd.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propJukenCd.labelStyle}"></h:outputText></TH>
												<TD><h:inputText styleClass="inputText" id="htmlJukenCdH"
													size="17"
													value="#{pc_Nsc00401T02.nsc00401.propJukenCd.stringValue}"
													disabled="#{pc_Nsc00401T02.nsc00401.propJukenCd.disabled}"
													readonly="#{pc_Nsc00401T02.nsc00401.propJukenCd.readonly}"
													style="#{pc_Nsc00401T02.nsc00401.propJukenCd.style}"
													maxlength="#{pc_Nsc00401T02.nsc00401.propJukenCd.maxLength}" tabindex="7" onblur="return func_5(this, event);"></h:inputText>
													<hx:commandExButton type="submit"
													styleClass="commandExButton_search" id="searchJkn"
													tabindex="8" onclick="return func_4(this, event);"
													action="#{pc_Nsc00401T02.doSearchJknAction}"
													disabled="#{pc_Nsc00401T02.nsc00401.propSearchJkn.disabled}"></hx:commandExButton>
												</TD>
											</TR>
											<TR>
												<TH style="" class="group_label_top" colspan="2"><h:outputText
													styleClass="outputText" id="lblKaiPageJun"
													value="#{pc_Nsc00401T02.nsc00401.propKaiPageJun.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propKaiPageJun.labelStyle}"></h:outputText></TH><TD colspan="3"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlKaiPageJun" value="#{pc_Nsc00401T02.nsc00401.propKaiPageJun.value}" tabindex="11">
													<f:selectItem itemValue="0" itemLabel="志願者番号順" />
													<f:selectItem itemValue="1" itemLabel="受験番号順" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH style="" class="group_label_bottom" width="30"></TH>
												<TH style="" class="v_e" width="120"><h:outputText
													styleClass="outputText" id="lblKaiPageHani"
													value="#{pc_Nsc00401T02.nsc00401.propKaiPageHani.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propKaiPageHani.labelStyle}"></h:outputText></TH>
												<TD colspan="3"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlKaiPageHani" value="#{pc_Nsc00401T02.nsc00401.propKaiPageHani.value}" tabindex="12">
													<f:selectItem itemValue="0" itemLabel="同一入試種別" />
													<f:selectItem itemValue="1" itemLabel="全入試種別" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH style="" class="v_f" colspan="2"><h:outputText
													styleClass="outputText" id="lblNysSbtH"
													value="#{pc_Nsc00401T02.nsc00401.propNysSbt.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propNysSbt.labelStyle}"></h:outputText></TH><TD colspan="3"><h:outputText
													styleClass="outputText" id="htmlNysSbtH" value="#{pc_Nsc00401T02.nsc00401.propNysSbt.stringValue}"></h:outputText></TD>
											</TR>
											<TR>
												<TH style="" class="v_g" colspan="2"><h:outputText
													styleClass="outputText" id="lblKiboGakka1H"
													value="#{pc_Nsc00401T02.nsc00401.propKiboGakka.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propKiboGakka.labelStyle}"></h:outputText></TH>
												<TD colspan="3" width="390"><h:outputText
													styleClass="outputText" id="htmlKiboGakka1H" value="#{pc_Nsc00401T02.nsc00401.propKiboGakka.stringValue}"></h:outputText></TD>
											</TR>
											<TR>
												<TH style="" class="v_a" colspan="2"><h:outputText
													styleClass="outputText" id="lblSgnNameH"
													value="#{pc_Nsc00401T02.nsc00401.propSgnName.labelName}"
													style="#{pc_Nsc00401T02.nsc00401.propSgnName.labelStyle}"></h:outputText></TH>
												<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlSgnNameH" size="40"
													value="#{pc_Nsc00401T02.nsc00401.propSgnName.stringValue}"
													style="#{pc_Nsc00401T02.nsc00401.propSgnName.style}"
													maxlength="#{pc_Nsc00401T02.nsc00401.propSgnName.maxLength}" tabindex="13"></h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD style="" width="20" rowspan="7"></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="選　択" styleClass="commandExButton" id="select1"
										disabled="#{pc_Nsc00401T02.nsc00401.propSelect1.disabled}" action="#{pc_Nsc00401T02.doSelect1Action}" tabindex="3"></hx:commandExButton></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="解　除" styleClass="commandExButton" id="reset1"
										disabled="#{pc_Nsc00401T02.nsc00401.propReset1.disabled}" action="#{pc_Nsc00401T02.doReset1Action}" tabindex="4"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="選　択" styleClass="commandExButton" id="select2"
										disabled="#{pc_Nsc00401T02.nsc00401.propSelect2.disabled}" action="#{pc_Nsc00401T02.doSelect2Action}" tabindex="9"></hx:commandExButton></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="解　除" styleClass="commandExButton" id="reset2"
										disabled="#{pc_Nsc00401T02.nsc00401.propReset2.disabled}" action="#{pc_Nsc00401T02.doReset2Action}" tabindex="10"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
								<TR>
									<TD align="right"><hx:commandExButton type="submit" value="|＜"
										styleClass="commandExButton" id="first"
										disabled="#{pc_Nsc00401T02.nsc00401.propFirst.disabled}" action="#{pc_Nsc00401T02.doFirstAction}" tabindex="14"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton type="submit" value="＜"
										styleClass="commandExButton" id="previous"
										disabled="#{pc_Nsc00401T02.nsc00401.propPrevious.disabled}" action="#{pc_Nsc00401T02.doPreviousAction}" tabindex="15"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton type="submit" value="＞"
										styleClass="commandExButton" id="next"
										disabled="#{pc_Nsc00401T02.nsc00401.propNext.disabled}" action="#{pc_Nsc00401T02.doNextAction}" tabindex="16"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton type="submit" value="＞|"
										styleClass="commandExButton" id="end"
										disabled="#{pc_Nsc00401T02.nsc00401.propLast.disabled}" action="#{pc_Nsc00401T02.doEndAction}" tabindex="17"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD>
<%-- 2011-07-27 y.nishigaki GAKEX-1959対応 mod start --%>
<%--
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" style="border-bottom-style: none; margin-top: 10px; ">
										<TBODY>
											<TR>
												<TD class="tab_head_off" width="12%"><hx:commandExButton
													type="submit" value="志願者情報①" styleClass="tab_head_off"
													id="selectTab1" style="width: 100%" action="#{pc_Nsc00401T02.doSelectTab1Action}" tabindex="34"></hx:commandExButton></TD>
												<TD class="tab_head_on" width="11%"><hx:commandExButton
													type="submit" value="志願者情報②" styleClass="tab_head_on"
													id="selectTab2" style="width:100%" tabindex="18"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="試験情報" styleClass="tab_head_off"
													id="selectTab3" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab3Action}" tabindex="35"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="教科情報" styleClass="tab_head_off"
													id="selectTab4" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab4Action}" tabindex="36"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="出身校情報" styleClass="tab_head_off"
													id="selectTab5" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab5Action}" tabindex="37"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="保証人情報" styleClass="tab_head_off"
													id="selectTab6" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab6Action}" tabindex="38"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="問合せ情報" styleClass="tab_head_off"
													id="selectTab7" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab7Action}" tabindex="39"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="自由設定" styleClass="tab_head_off"
													id="selectTab8" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab8Action}" tabindex="40"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="留学生情報" styleClass="tab_head_off"
													id="selectTab9" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab9Action}" tabindex="41"></hx:commandExButton></TD>
--%>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="744px" style="border-bottom-style: none; margin-top: 10px; ">
										<TBODY>
											<TR>
												<TD class="tab_head_off" width="84px"><hx:commandExButton
													type="submit" value="志願者情報①" styleClass="tab_head_off"
													id="selectTab1" style="width: 100%" action="#{pc_Nsc00401T02.doSelectTab1Action}" tabindex="34"></hx:commandExButton></TD>
												<TD class="tab_head_on" width="84px"><hx:commandExButton
													type="submit" value="志願者情報②" styleClass="tab_head_on"
													id="selectTab2" style="width:100%" tabindex="18"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="試験情報" styleClass="tab_head_off"
													id="selectTab3" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab3Action}" tabindex="35"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="教科情報" styleClass="tab_head_off"
													id="selectTab4" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab4Action}" tabindex="36"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="出身校情報" styleClass="tab_head_off"
													id="selectTab5" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab5Action}" tabindex="37"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="保証人情報" styleClass="tab_head_off"
													id="selectTab6" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab6Action}" tabindex="38"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="問合せ情報" styleClass="tab_head_off"
													id="selectTab7" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab7Action}" tabindex="39"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="自由設定" styleClass="tab_head_off"
													id="selectTab8" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab8Action}" tabindex="40"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="84px"><hx:commandExButton
													type="submit" value="留学生情報" styleClass="tab_head_off"
													id="selectTab9" style="width:100%" action="#{pc_Nsc00401T02.doSelectTab9Action}" tabindex="41"></hx:commandExButton></TD>
<%-- 2011-07-27 y.nishigaki GAKEX-1959対応 mod end --%>

											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="100%" height="290" >
										<TBODY>
											<TR>
												<TD>
												<TABLE width="98%" border="0" cellpadding="0"
													cellspacing="0" style="" class="table">
													<TBODY>
														<TR>
															<TH style="" class="v_b" width="150"><h:outputText
																styleClass="outputText" id="lblSgnAddrNo"
																value="#{pc_Nsc00401T02.propSgnAddrNo.labelName}"
																style="#{pc_Nsc00401T02.propSgnAddrNo.labelStyle}"></h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSgnAddrNo" size="8"
																value="#{pc_Nsc00401T02.propSgnAddrNo.stringValue}"
																style="#{pc_Nsc00401T02.propSgnAddrNo.style}"
																maxlength="8" tabindex="19"></h:inputText><hx:commandExButton
																type="button" styleClass="commandExButton_search"
																id="search" onclick="return func_1(this, event);" tabindex="21"></hx:commandExButton></TD>
														</TR>
														<TR>
															<TH style="" class="v_c" width="150" rowspan="3"><h:outputText
																styleClass="outputText" id="lblSgnAddr" value="住所(全75)"
																style="#{pc_Nsc00401T02.propSgnAddr1.labelStyle}"></h:outputText></TH>
															<TD colspan="3" style="border-bottom-style:none;"><h:inputText
																styleClass="inputText" id="htmlSgnAddr1" size="50"
																value="#{pc_Nsc00401T02.propSgnAddr1.stringValue}"
																style="#{pc_Nsc00401T02.propSgnAddr1.style}"
																maxlength="#{pc_Nsc00401T02.propSgnAddr1.maxLength}" tabindex="20"></h:inputText><h:outputText
																styleClass="outputText" id="lblSgnAddr1"
																value="(都道府県市区町村大字)"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="3"
																style="border-top-style:none;border-bottom-style:none;"><h:inputText
																styleClass="inputText" id="htmlSgnAddr2" size="50"
																style="#{pc_Nsc00401T02.propSgnAddr2.style}"
																value="#{pc_Nsc00401T02.propSgnAddr2.stringValue}"
																maxlength="#{pc_Nsc00401T02.propSgnAddr2.maxLength}" tabindex="22"></h:inputText><h:outputText
																styleClass="outputText" id="lblSgnAddr2"
																value="(丁目・字以下)"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="3" style="border-top-style:none;"><h:inputText
																styleClass="inputText" id="htmlSgnAddr3" size="50"
																style="#{pc_Nsc00401T02.propSgnAddr3.style}"
																value="#{pc_Nsc00401T02.propSgnAddr3.stringValue}"
																maxlength="#{pc_Nsc00401T02.propSgnAddr3.maxLength}" tabindex="23"></h:inputText><h:outputText
																styleClass="outputText" id="lblSgnAddr3"
																value="(マンション/ビル名　号室)"></h:outputText></TD>
														</TR>
														<TR>
															<TH style="" class="v_d" width="150" rowspan="3"><h:outputText
																styleClass="outputText" id="lblSgnAddrKana"
																value="住所カナ(全150)"
																style="#{pc_Nsc00401T02.propSgnAddrKana1.labelStyle}"></h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSgnAddrKana1" size="90"
																value="#{pc_Nsc00401T02.propSgnAddrKana1.stringValue}"
																style="#{pc_Nsc00401T02.propSgnAddrKana1.style}"
																maxlength="#{pc_Nsc00401T02.propSgnAddrKana1.maxLength}" tabindex="24"></h:inputText></TD>
														</TR>
														<TR>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSgnAddrKana2" size="90"
																style="#{pc_Nsc00401T02.propSgnAddrKana2.style}"
																value="#{pc_Nsc00401T02.propSgnAddrKana2.stringValue}"
																maxlength="#{pc_Nsc00401T02.propSgnAddrKana2.maxLength}" tabindex="25"></h:inputText></TD>
														</TR>
														<TR>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSgnAddrKana3" size="90"
																value="#{pc_Nsc00401T02.propSgnAddrKana3.stringValue}"
																style="#{pc_Nsc00401T02.propSgnAddrKana3.style}"
																maxlength="#{pc_Nsc00401T02.propSgnAddrKana3.maxLength}" tabindex="26"></h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_e" width="150"><h:outputText
																styleClass="outputText" id="lblSgnTel1"
																value="#{pc_Nsc00401T02.propSgnTel1.labelName}"
																style="#{pc_Nsc00401T02.propSgnTel1.labelStyle}"></h:outputText></TH>
															<TD width="197"><h:inputText styleClass="inputText"
																id="htmlSgnTel1" size="25"
																value="#{pc_Nsc00401T02.propSgnTel1.stringValue}"
																style="#{pc_Nsc00401T02.propSgnTel1.style}"
																maxlength="#{pc_Nsc00401T02.propSgnTel1.maxLength}" tabindex="27"></h:inputText></TD>
															<TH class="v_f" width="189"><h:outputText
																styleClass="outputText" id="lblSgnTel2"
																value="#{pc_Nsc00401T02.propSgnTel2.labelName}"
																style="#{pc_Nsc00401T02.propSgnTel2.labelStyle}"></h:outputText></TH>
															<TD width="192"><h:inputText styleClass="inputText" id="htmlSgnTel2"
																size="25"
																value="#{pc_Nsc00401T02.propSgnTel2.stringValue}"
																style="#{pc_Nsc00401T02.propSgnTel2.style}"
																maxlength="#{pc_Nsc00401T02.propSgnTel2.maxLength}"
																tabindex="28"></h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_g" width="150"><h:outputText
																styleClass="outputText" id="lblSgnKeitaiTel"
																value="#{pc_Nsc00401T02.propSgnKeitaiTel.labelName}"
																style="#{pc_Nsc00401T02.propSgnKeitaiTel.labelStyle}"></h:outputText></TH>
															<TD width="197"><h:inputText styleClass="inputText"
																id="htmlSgnKeitaiTel" size="25"
																value="#{pc_Nsc00401T02.propSgnKeitaiTel.stringValue}"
																style="#{pc_Nsc00401T02.propSgnKeitaiTel.style}"
																maxlength="#{pc_Nsc00401T02.propSgnKeitaiTel.maxLength}" tabindex="29"></h:inputText></TD>
															<TH class="v_a" width="189"><h:outputText
																styleClass="outputText" id="lblSgnFax"
																value="#{pc_Nsc00401T02.propSgnFax.labelName}"
																style="#{pc_Nsc00401T02.propSgnFax.labelStyle}"></h:outputText></TH>
															<TD width="192"><h:inputText styleClass="inputText" id="htmlSgnFax"
																size="25" value="#{pc_Nsc00401T02.propSgnFax.stringValue}"
																style="#{pc_Nsc00401T02.propSgnFax.style}"
																maxlength="#{pc_Nsc00401T02.propSgnFax.maxLength}" tabindex="30"></h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_b" width="150"><h:outputText
																styleClass="outputText" id="lblSgnEMail"
																value="#{pc_Nsc00401T02.propSgnEMail.labelName}"
																style="#{pc_Nsc00401T02.propSgnEMail.labelStyle}"></h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSgnEMail" size="90"
																value="#{pc_Nsc00401T02.propSgnEMail.stringValue}"
																style="#{pc_Nsc00401T02.propSgnEMail.style}"
																maxlength="#{pc_Nsc00401T02.propSgnEMail.maxLength}" tabindex="31"></h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_c" width="150"><h:outputText
																styleClass="outputText" id="lblSgnRenraku"
																value="#{pc_Nsc00401T02.propSgnRenraku.labelName}"
																style="#{pc_Nsc00401T02.propSgnRenraku.labelStyle}"></h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSgnRenraku" size="90"
																value="#{pc_Nsc00401T02.propSgnRenraku.stringValue}"
																style="#{pc_Nsc00401T02.propSgnRenraku.style}"
																maxlength="#{pc_Nsc00401T02.propSgnRenraku.maxLength}" tabindex="32"></h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_d" width="150"><h:outputText
																styleClass="outputText" id="lblSgnRenrakuTel"
																value="#{pc_Nsc00401T02.propSgnRenrakuTel.labelName}"
																style="#{pc_Nsc00401T02.propSgnRenrakuTel.labelStyle}"></h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSgnRenrakuTel" size="25"
																value="#{pc_Nsc00401T02.propSgnRenrakuTel.stringValue}"
																style="#{pc_Nsc00401T02.propSgnRenrakuTel.style}"
																maxlength="#{pc_Nsc00401T02.propSgnRenrakuTel.maxLength}" tabindex="33"></h:inputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%"
							class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="fix"
										action="#{pc_Nsc00401T02.doFixAction}"
										disabled="#{pc_Nsc00401T02.nsc00401.propFix.disabled}" confirm="#{msg.SY_MSG_0003W}" tabindex="42"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Nsc00401T02.doDeleteAction}"
										disabled="#{pc_Nsc00401T02.nsc00401.propDelete.disabled}" confirm="#{msg.SY_MSG_0004W}" tabindex="43"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Nsc00401T02.nsc00401.propExecutable.integerValue}"
				id="htmlExecutable">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsc00401T02.nsc00401.propExecutableFlg.integerValue}"
				id="htmlExecutableFlg">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

