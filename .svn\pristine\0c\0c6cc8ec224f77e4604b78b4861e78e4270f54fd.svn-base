<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsb00302.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsb00302.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
function func_3(thisObj, thisEvent) {
changeScrollPosition('scroll','listscroll');
}
function func_4(thisObj, thisEvent) {
uncheck('htmlYosoList','htmlCheckList');
}
function func_5(thisObj, thisEvent) {
check('htmlYosoList','htmlCheckList');
}
function confirmOk() {
	document.getElementById('form1:htmlExecutableSelectJoken').value = "1";	
	indirectClick('selectJoken');	
}			
function confirmCancel() {
 	document.getElementById('form1:htmlExecutableSelectJoken').value = "0";	
}			
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY onload="return func_3(this, event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Nsb00302.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Nsb00302.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Nsb00302.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Nsb00302.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Nsb00302.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>

						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="80%">
							<TBODY>
								<TR>
									<TH class="v_a" width="20%"><h:outputText
										styleClass="outputText" id="lblNyushiNendo"
										value="#{pc_Nsb00302.propNyushiNendo.name}"
										style="#{pc_Nsb00302.propNyushiNendo.labelStyle}"></h:outputText></TH>
									<TD width="15%"><h:outputText styleClass="outputText"
										id="htmlNyushiNendo"
										value="#{pc_Nsb00302.propNyushiNendo.stringValue}"
										style="#{pc_Nsb00302.propNyushiNendo.labelStyle}"></h:outputText></TD>
									<TH width="20%" class="v_b"><h:outputText
										styleClass="outputText" id="lblNyushiGakkiName"
										style="#{pc_Nsb00302.propNyushiGakkiName.labelStyle}"
										value="#{pc_Nsb00302.propNyushiGakkiName.name}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="htmlNyushiGakkiName"
										value="#{pc_Nsb00302.propNyushiGakkiName.stringValue}"
										style="#{pc_Nsb00302.propNyushiGakkiName.labelStyle}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblNysSbtCd"
										style="#{pc_Nsb00302.propNysSbtCd.labelStyle}"
										value="#{pc_Nsb00302.propNysSbtCd.name}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlNysSbtCd"
										value="#{pc_Nsb00302.propNysSbtCd.stringValue}"
										style="#{pc_Nsb00302.propNysSbtCd.labelStyle}"></h:outputText></TD>
									<TH width="150" class="v_d"><h:outputText
										styleClass="outputText" id="lblNysSbtName"
										value="#{pc_Nsb00302.propNysSbtName.name}"
										style="#{pc_Nsb00302.propNysSbtName.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlNysSbtName"
										style="#{pc_Nsb00302.propNysSbtName.labelStyle}"
										value="#{pc_Nsb00302.propNysSbtName.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblGakkaCd" value="#{pc_Nsb00302.propGakkaCd.name}"
										style="#{pc_Nsb00302.propGakkaCd.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlGakkaCd"
										style="#{pc_Nsb00302.propGakkaCd.labelStyle}"
										value="#{pc_Nsb00302.propGakkaCd.stringValue}"></h:outputText></TD>
									<TH width="150" class="v_f"><h:outputText
										styleClass="outputText" id="lblGakkaName"
										value="#{pc_Nsb00302.propGakkaName.name}"
										style="#{pc_Nsb00302.propGakkaName.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlGakkaName"
										style="#{pc_Nsb00302.propGakkaName.labelStyle}"
										value="#{pc_Nsb00302.propGakkaName.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g"><h:outputText styleClass="outputText"
										id="lblSikenDate" value="#{pc_Nsb00302.propSikenDate.name}"
										style="#{pc_Nsb00302.propSikenDate.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlSikenDate"
										style="#{pc_Nsb00302.propSikenDate.labelStyle}"
										value="#{pc_Nsb00302.propSikenDate.stringValue}"></h:outputText></TD>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblJisDankai"
										value="#{pc_Nsb00302.propJisDankai.name}"
										style="#{pc_Nsb00302.propJisDankai.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlJisDankai"
										style="#{pc_Nsb00302.propJisDankai.labelStyle}"
										value="#{pc_Nsb00302.propJisDankai.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblSikenPlaceCd"
										value="#{pc_Nsb00302.propSikenPlaceCd.name}"
										style="#{pc_Nsb00302.propSikenPlaceCd.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlSikenPlaceCd"
										style="#{pc_Nsb00302.propSikenPlaceCd.labelStyle}"
										value="#{pc_Nsb00302.propSikenPlaceCd.stringValue}"></h:outputText></TD>
									<TH width="150" class="v_c"><h:outputText
										styleClass="outputText" id="lblSikenPlaceName"
										value="#{pc_Nsb00302.propSikenPlaceName.name}"
										style="#{pc_Nsb00302.propSikenPlaceName.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="htmlSikenPlaceName"
										style="#{pc_Nsb00302.propSikenPlaceName.labelStyle}"
										value="#{pc_Nsb00302.propSikenPlaceName.stringValue}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class=""
							style="margin-top: 18px;" width="80%">
							<TBODY>
								<TR>


									<TD width="300">
									<TABLE border="0" cellpadding="0" cellspacing="0" style=""
										align="left" class="table" width="100%">
										<TBODY>
											<TR align="left">


												<TH class="v_d" width="150"><h:outputText
													styleClass="outputText" id="lblJokenCd"
													value="#{pc_Nsb00302.propJokenCd.labelName}"
													style="#{pc_Nsb00302.propJokenCd.labelStyle}"></h:outputText></TH>
												<TD width="150"><h:inputText styleClass="inputText"
													id="htmlJokenCd" size="5" tabindex="1"
													value="#{pc_Nsb00302.propJokenCd.stringValue}"
													style="#{pc_Nsb00302.propJokenCd.style}" maxlength="5"
													disabled="#{pc_Nsb00302.propJokenCd.disabled}"
													readonly="#{pc_Nsb00302.propJokenCd.readonly}"></h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD align="left"><hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="selectJoken"
										disabled="#{pc_Nsb00302.propSelectJoken.disabled}"
										tabindex="2" action="#{pc_Nsb00302.doSelectJokenAction}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="解除"
										styleClass="commandExButton" id="resetJoken" tabindex="3"
										action="#{pc_Nsb00302.doResetJokenAction}"
										disabled="#{pc_Nsb00302.propResetJoken.disabled}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="80%">
							<TBODY>
								<TR>
									<TH class="v_e" width="146"><h:outputText
										styleClass="outputText" id="lblJokenName"
										value="#{pc_Nsb00302.propJokenName.labelName}"
										style="#{pc_Nsb00302.propJokenName.labelStyle}"></h:outputText></TH>
									<TD width=""><h:inputText styleClass="inputText"
										id="htmlJokenName" size="90"
										value="#{pc_Nsb00302.propJokenName.stringValue}"
										style="#{pc_Nsb00302.propJokenName.style}" maxlength="100"
										tabindex="4"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%"
							style="list_table">
							<TBODY>
								<TR>
									<TD align="right" width="100%"><h:outputText
										styleClass="outputText" id="htmlCount"
										value="#{pc_Nsb00302.propYosoList.listCount}">

									</h:outputText><h:outputText styleClass="outputText"
										id="lblKensu" value="件"></h:outputText></TD>
								</TR>
								<TR>
									<TD>
									<DIV class="listScroll" id="listScroll"
										style="height:150px;width:100%"
										onscroll="setScrollPosition('scroll',this);"><h:dataTable
										border="0" cellpadding="2" cellspacing="0"
										headerClass="headerClass" footerClass="footerClass"
										rowClasses="#{pc_Nsb00302.propYosoList.rowClasses}"
										styleClass="meisai_scroll" id="htmlYosoList"
										value="#{pc_Nsb00302.propYosoList.list}" var="varlist">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="　" id="text1"></h:outputText>
											</f:facet>
											<f:attribute value="30" name="width" />
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
												id="htmlCheckList" value="#{varlist.selected}" tabindex="5"></h:selectBooleanCheckbox>
											<f:attribute value="text-align:center" name="style" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="順位"
													id="lblJuniHead"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlJuniList"
												value="#{varlist.kyostWariJni}"></h:outputText>
											<f:attribute value="35" name="width" />
											<f:attribute value="text-align:right" name="style" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="時限"
													id="lblJigenHead"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlJigenList"
												value="#{varlist.jisJigen}"></h:outputText>
											<f:attribute value="35" name="width" />
											<f:attribute value="text-align:right" name="style" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="必須"
													id="lblHissuHead"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlHissuList"
												value="#{varlist.kykStFlg}"></h:outputText>
											<f:attribute value="35" name="width" />
											<f:attribute value="text-align:center" name="style" />
										</h:column>
										<h:column id="column5">
											<hx:jspPanel id="jspPanel1">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													width="100%">
													<TBODY>
														<TR>
															<TD width="40"
																style="border-left-style: none; border-top-style: none; border-bottom-style: none; text-align: right;"><h:outputText
																styleClass="outputText" id="htmlKyokaNoList"
																value="#{varlist.senkoHohoNo}"></h:outputText></TD>
															<TD width="60"
																style="border-right-style: none; border-left-style: none; border-top-style: none; border-bottom-style: none;"><h:outputText
																styleClass="outputText" id="htmlKyokaNameList"
																value="#{varlist.kykNameRyak}"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
											</hx:jspPanel>

											<f:facet name="header">
												<hx:jspPanel id="jspPanel2">
													<TABLE border="0" cellpadding="0" cellspacing="0"
														width="100%" class="" height="100%">
														<TBODY>
															<TR>
																<TH colspan="2"
																	style="border-bottom-style: solid;border-top-style: none; border-left-style: none;"
																	width="100"><h:outputText styleClass="outputText"
																	value="教科" id="lblKyokaHead"></h:outputText></TH>
															</TR>
															<TR>
																<TH
																	style="border-right-style: none; border-bottom-style: none; border-left-style: none; border-top-style: none;"
																	width="40"><h:outputText styleClass="outputText"
																	value="番号" id="lblKyokaNoHead"></h:outputText></TH>
																<TH
																	style="border-right-style: none; border-bottom-style: none; border-top-style: none;"
																	width="60"><h:outputText styleClass="outputText"
																	value="略称" id="lblKyokaNameHead"></h:outputText></TH>
															</TR>
														</TBODY>
													</TABLE>
												</hx:jspPanel>
											</f:facet>
											<f:attribute
												value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
												name="style" />
											<f:attribute value="100" name="width" />
										</h:column>
										<h:column id="column6">
											<hx:jspPanel id="jspPanel3">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													width="100%">
													<TBODY>
														<TR>
															<TD width="40"
																style="border-left-style: none; border-top-style: none; border-bottom-style: none; text-align: right;"><h:outputText
																styleClass="outputText" id="htmlKamokNoList"
																value="#{varlist.kamokNo}"></h:outputText></TD>
															<TD width="60"
																style="border-right-style: none; border-left-style: none; border-top-style: none; border-bottom-style: none;"><h:outputText
																styleClass="outputText" id="htmlKamokNameList"
																value="#{varlist.kamokNameRyak}"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
											</hx:jspPanel>

											<f:facet name="header">
												<hx:jspPanel id="jspPanel4">
													<TABLE border="0" cellpadding="0" cellspacing="0"
														width="100%" class="" height="100%">
														<TBODY>
															<TR>
																<TH colspan="2"
																	style="border-left-style: none; border-bottom-style: solid; border-top-style: none;"
																	width="100"><h:outputText styleClass="outputText"
																	value="科目" id="lblKamokHead"></h:outputText></TH>
															</TR>
															<TR>
																<TH
																	style="border-right-style: none; border-bottom-style: none; border-left-style: none; border-top-style: none;"
																	width="40"><h:outputText styleClass="outputText"
																	value="番号" id="lblKamokNoHead"></h:outputText></TH>
																<TH
																	style="border-right-style: none; border-bottom-style: none; border-top-style: none;"
																	width="60"><h:outputText styleClass="outputText"
																	value="略称" id="lblKamokNameHead"></h:outputText></TH>
															</TR>
														</TBODY>
													</TABLE>
												</hx:jspPanel>
											</f:facet>
											<f:attribute
												value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
												name="style" />

											<f:attribute value="100" name="width" />
										</h:column>
										<h:column id="column8">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="教室"
													id="lblKyostHead"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlKyostList"
												value="#{varlist.kyostName.displayValue}"
												title="#{varlist.kyostName.value}"></h:outputText>
											<f:attribute value="200" name="width" />
										</h:column>
										<h:column id="column9">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="受験者数"
													id="lblJukensuHead"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlJukensuList"
												value="#{varlist.jukenCnt}"></h:outputText>
											<f:attribute value="40" name="width" />
											<f:attribute value="text-align:right" name="style" />
										</h:column>
										<h:column id="column10">
											<h:outputText styleClass="outputText" id="htmlTeiinList"
												value="#{varlist.teiin}"></h:outputText>
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="定員"
													id="lblTeiinHead"></h:outputText>
											</f:facet>
											<f:attribute value="40" name="width" />
											<f:attribute value="text-align:right" name="style" />
										</h:column>
										<h:column id="column11">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="同一試験日内同一座席"
													id="lblDoitsuHead"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlDoitsuList"
												value="#{varlist.doitsuHiSekiFlg}"></h:outputText>
											<f:attribute value="80" name="width" />
											<f:attribute value="text-align:center" name="style" />
										</h:column>
										<h:column id="column12">
											<hx:commandExButton type="submit" value="選択"
												styleClass="commandExButton" id="select" tabindex="6"
												action="#{pc_Nsb00302.doSelectAction}"></hx:commandExButton>
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="　" id="text2"></h:outputText>
											</f:facet>
											<f:attribute value="40" name="width" />
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="2" cellspacing="0"
										class="meisai_scroll" width="100%">
										<TR>
											<TD class="footerClass">
											<TABLE class="panelBox">
												<TBODY>
													<TR>
														<TD><hx:commandExButton type="button" styleClass="check"
															id="check" tabindex="7"
															disabled="#{pc_Nsb00302.propCheck.disabled}"
															onclick="return func_5(this, event);"></hx:commandExButton><hx:commandExButton
															type="button" styleClass="uncheck" id="uncheck"
															tabindex="8"
															disabled="#{pc_Nsb00302.propUncheck.disabled}"
															onclick="return func_4(this, event);"></hx:commandExButton></TD>
														<TD width="5"></TD>
														<TD><hx:commandExButton type="submit" value="削除"
															styleClass="commandExButton" id="delete" tabindex="9"
															action="#{pc_Nsb00302.doDeleteAction}"
															disabled="#{pc_Nsb00302.propDelete.disabled}"
															confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
											</TD>
										</TR>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%"
							style="margin-top: 10px;">
							<TBODY>
								<TR>
									<TD width="100%">
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%">
										<TBODY>
											<TR>
												<TH class="v_f" width="25%"><h:outputText
													styleClass="outputText" id="lblJuni"
													value="#{pc_Nsb00302.propJuni.labelName}"
													style="#{pc_Nsb00302.propJuni.labelStyle}"></h:outputText></TH>
												<TD width="72%"><h:inputText styleClass="inputText"
													id="htmlJuni" size="3" tabindex="10"
													value="#{pc_Nsb00302.propJuni.integerValue}" maxlength="3"
													style="#{pc_Nsb00302.propJuni.style}">
													<f:convertNumber pattern="###" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
											<TR>
												<TH class="v_g"><h:outputText styleClass="outputText"
													id="lblKyokaKmk"
													value="#{pc_Nsb00302.propKyokaKmk.labelName}"
													style="#{pc_Nsb00302.propKyokaKmk.labelStyle}"></h:outputText></TH>
												<TD><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlKyokaKmk" style='width: 250px' tabindex="11"
													value="#{pc_Nsb00302.propKyokaKmk.stringValue}">
													<f:selectItems value="#{pc_Nsb00302.propKyokaKmk.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_a"><h:outputText styleClass="outputText"
													id="lblKyostApp" value="#{pc_Nsb00302.propKyost.labelName}"
													style="#{pc_Nsb00302.propKyost.labelStyle}"></h:outputText></TH>
												<TD><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlKyost" style="width:250px;" tabindex="12"
													value="#{pc_Nsb00302.propKyost.stringValue}">
													<f:selectItems value="#{pc_Nsb00302.propKyost.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_b"><h:outputText styleClass="outputText"
													id="lblDoitsu" value="#{pc_Nsb00302.propDoitsu.labelName}"
													style="#{pc_Nsb00302.propDoitsu.labelStyle}"></h:outputText></TH>
												<TD><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlDoutituApp"
													tabindex="13" value="#{pc_Nsb00302.propDoitsu.checked}"></h:selectBooleanCheckbox><h:outputText
													styleClass="outputText" id="lblDoutituApp"
													value="同一試験日内同一座席"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="20%"></TD>
								</TR>
								<TR>
									<TD align="right"><h:outputText
										id="lblSetumei" styleClass="note"
										value="※割当順位は指定された順位に挿入されます。確定・削除の度、自動的に連番が振られます。"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="673" class="button_bar" style="margin-top:5px">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="exec" tabindex="14"
										action="#{pc_Nsb00302.doExecAction}"
										confirm="#{msg.SY_MSG_0002W}"
										disabled="#{pc_Nsb00302.propExec.disabled}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" tabindex="15" action="#{pc_Nsb00302.doClearAction}"
										disabled="#{pc_Nsb00302.propClear.disabled}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden id="scroll"
				value="#{pc_Nsb00302.propYosoList.scrollPosition}"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsb00302.propExecutableSelectJoken.integerValue}"
				id="htmlExecutableSelectJoken">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

