<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00401T06.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsc00401T06.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
check('htmlTableList','htmlHsyCheck');
}
function func_2(thisObj, thisEvent) {
uncheck('htmlTableList','htmlHsyCheck');
}
function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1";
	if (document.getElementById('form1:htmlExecutableFlg').value == "0") {
		indirectClick('fix');
	} else {
		indirectClick('delete');
	}
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutable').value = "2";
	if (document.getElementById('form1:htmlExecutableFlg').value == "0") {
		indirectClick('fix');
	} else {
		indirectClick('delete');
	}
}
function func(thisObj, thisEvent) {
	changeScrollPosition('scroll','listScroll');
}

function func_3(thisObj, thisEvent) {
	//ローカル用
	openModalWindow("", "PNsc0101", "<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
	setTarget("PNsc0101");
	return true;
}
function func_4(thisObj, thisEvent) {
//
}
function func_5(thisObj, thisEvent) {
	//ローカル用
	openModalWindow("", "PNsc0101", "<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
	setTarget("PNsc0101");
	return true;
}
function func_6(thisObj, thisEvent) {
//
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY onload="return func(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc00401T06.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc00401T06.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc00401T06.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc00401T06.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD style="" rowspan="7">

									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%">
										<TBODY>
											<TR>
												<TH style="" class="v_a" width="130" colspan="2"><h:outputText
													styleClass="outputText" id="lblNyushiNendoH"
													value="#{pc_Nsc00401T06.nsc00401.propNyushiNendo.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propNyushiNendo.labelStyle}"></h:outputText></TH><TD width="130"><h:inputText
													styleClass="inputText" id="htmlNyushiNendoH" size="4"
													value="#{pc_Nsc00401T06.nsc00401.propNyushiNendo.dateValue}"
													disabled="#{pc_Nsc00401T06.nsc00401.propNyushiNendo.disabled}"
													readonly="#{pc_Nsc00401T06.nsc00401.propNyushiNendo.readonly}"
													style="#{pc_Nsc00401T06.nsc00401.propNyushiNendo.style}" tabindex="1">
													<f:convertDateTime pattern="yyyy" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TH width="130" class="v_b"><h:outputText
													styleClass="outputText" id="lblNyushiGakkiNoH"
													value="#{pc_Nsc00401T06.nsc00401.propNyushiGakkiNo.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
												<TD><h:inputText styleClass="inputText"
													id="htmlNyushiGakkiNoH" size="2"
													value="#{pc_Nsc00401T06.nsc00401.propNyushiGakkiNo.integerValue}"
													disabled="#{pc_Nsc00401T06.nsc00401.propNyushiGakkiNo.disabled}"
													readonly="#{pc_Nsc00401T06.nsc00401.propNyushiGakkiNo.readonly}"
													style="#{pc_Nsc00401T06.nsc00401.propNyushiGakkiNo.style}" tabindex="2">
													<f:convertNumber pattern="#0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
											<TR>
												<TH style="" class="v_c" colspan="2"><h:outputText
													styleClass="outputText" id="lblSgnCdH"
													value="#{pc_Nsc00401T06.nsc00401.propSgnCd.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propSgnCd.labelStyle}"></h:outputText></TH><TD><h:inputText
													styleClass="inputText" id="htmlSgnCdH" size="17"
													value="#{pc_Nsc00401T06.nsc00401.propSgnCd.stringValue}"
													disabled="#{pc_Nsc00401T06.nsc00401.propSgnCd.disabled}"
													readonly="#{pc_Nsc00401T06.nsc00401.propSgnCd.readonly}"
													style="#{pc_Nsc00401T06.nsc00401.propSgnCd.style}"
													maxlength="#{pc_Nsc00401T06.nsc00401.propSgnCd.maxLength}" tabindex="5" onblur="return func_4(this, event);"></h:inputText><hx:commandExButton
													type="submit" styleClass="commandExButton_search"
													id="searchSgn" tabindex="6"
													onclick="return func_3(this, event);"
													action="#{pc_Nsc00401T06.doSearchSgnAction}"
													disabled="#{pc_Nsc00401T06.nsc00401.propSearchSgn.disabled}"></hx:commandExButton></TD>
												<TH class="v_d"><h:outputText styleClass="outputText"
													id="lblJukenCdH"
													value="#{pc_Nsc00401T06.nsc00401.propJukenCd.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propJukenCd.labelStyle}"></h:outputText></TH>
												<TD><h:inputText styleClass="inputText" id="htmlJukenCdH"
													size="17"
													disabled="#{pc_Nsc00401T06.nsc00401.propJukenCd.disabled}"
													readonly="#{pc_Nsc00401T06.nsc00401.propJukenCd.readonly}"
													style="#{pc_Nsc00401T06.nsc00401.propJukenCd.style}"
													value="#{pc_Nsc00401T06.nsc00401.propJukenCd.stringValue}"
													maxlength="#{pc_Nsc00401T06.nsc00401.propJukenCd.maxLength}" tabindex="7" onblur="return func_6(this, event);"></h:inputText>
													<hx:commandExButton type="submit"
													styleClass="commandExButton_search" id="searchJkn"
													tabindex="8" onclick="return func_5(this, event);"
													action="#{pc_Nsc00401T06.doSearchJknAction}"
													disabled="#{pc_Nsc00401T06.nsc00401.propSearchJkn.disabled}"></hx:commandExButton>
												</TD>
											</TR>
											<TR>
												<TH style="" class="group_label_top" colspan="2"><h:outputText
													styleClass="outputText" id="lblKaiPageJun"
													value="#{pc_Nsc00401T06.nsc00401.propKaiPageJun.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propKaiPageJun.labelStyle}"></h:outputText></TH><TD colspan="3"><h:selectOneRadio
													styleClass="selectOneRadio" id="htmlKaiPageJun"
													value="#{pc_Nsc00401T06.nsc00401.propKaiPageJun.value}" tabindex="11">
													<f:selectItem itemValue="0" itemLabel="志願者番号順" />
													<f:selectItem itemValue="1" itemLabel="受験番号順" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH style="" class="group_label_bottom" width="30"></TH>
												<TH style="" class="v_e" width="120"><h:outputText
													styleClass="outputText" id="lblKaiPageHani"
													value="#{pc_Nsc00401T06.nsc00401.propKaiPageHani.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propKaiPageHani.labelStyle}"></h:outputText></TH>
												<TD colspan="3"><h:selectOneRadio
													styleClass="selectOneRadio" id="htmlKaiPageHani"
													value="#{pc_Nsc00401T06.nsc00401.propKaiPageHani.value}" tabindex="12">
													<f:selectItem itemValue="0" itemLabel="同一入試種別" />
													<f:selectItem itemValue="1" itemLabel="全入試種別" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH style="" class="v_f" colspan="2"><h:outputText
													styleClass="outputText" id="lblNysSbtH"
													value="#{pc_Nsc00401T06.nsc00401.propNysSbt.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propNysSbt.labelStyle}"></h:outputText></TH><TD colspan="3"><h:outputText
													styleClass="outputText" id="htmlNysSbtH"
													value="#{pc_Nsc00401T06.nsc00401.propNysSbt.stringValue}"></h:outputText></TD>
											</TR>
											<TR>
												<TH style="" class="v_g" colspan="2"><h:outputText
													styleClass="outputText" id="lblKiboGakka1H"
													value="#{pc_Nsc00401T06.nsc00401.propKiboGakka.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propKiboGakka.labelStyle}"></h:outputText></TH>
												<TD colspan="3" width="390"><h:outputText
													styleClass="outputText" id="htmlKiboGakka1H" value="#{pc_Nsc00401T06.nsc00401.propKiboGakka.stringValue}"></h:outputText></TD>
											</TR>
											<TR>
												<TH style="" class="v_a" colspan="2"><h:outputText
													styleClass="outputText" id="lblSgnNameH"
													value="#{pc_Nsc00401T06.nsc00401.propSgnName.labelName}"
													style="#{pc_Nsc00401T06.nsc00401.propSgnName.labelStyle}"></h:outputText></TH>
												<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlSgnNameH" size="40"
													value="#{pc_Nsc00401T06.nsc00401.propSgnName.stringValue}"
													style="#{pc_Nsc00401T06.nsc00401.propSgnName.style}"
													maxlength="#{pc_Nsc00401T06.nsc00401.propSgnName.maxLength}" tabindex="13"></h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD style="" width="20" rowspan="7"></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="選　択" styleClass="commandExButton" id="select1"
										disabled="#{pc_Nsc00401T06.nsc00401.propSelect1.disabled}" action="#{pc_Nsc00401T06.doSelect1Action}" tabindex="3"></hx:commandExButton></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="解　除" styleClass="commandExButton" id="reset1"
										action="#{pc_Nsc00401T06.doReset1Action}"
										disabled="#{pc_Nsc00401T06.nsc00401.propReset1.disabled}" tabindex="4"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="選　択" styleClass="commandExButton" id="select2"
										action="#{pc_Nsc00401T06.doSelect2Action}"
										disabled="#{pc_Nsc00401T06.nsc00401.propSelect2.disabled}" tabindex="9"></hx:commandExButton></TD>
									<TD align="right" colspan="2"><hx:commandExButton type="submit"
										value="解　除" styleClass="commandExButton" id="reset2"
										action="#{pc_Nsc00401T06.doReset2Action}"
										disabled="#{pc_Nsc00401T06.nsc00401.propReset2.disabled}" tabindex="10"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
								<TR>
									<TD align="right"><hx:commandExButton type="submit" value="|＜"
										styleClass="commandExButton" id="first"
										disabled="#{pc_Nsc00401T06.nsc00401.propFirst.disabled}" action="#{pc_Nsc00401T06.doFirstAction}" tabindex="14"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton type="submit" value="＜"
										styleClass="commandExButton" id="previous"
										disabled="#{pc_Nsc00401T06.nsc00401.propPrevious.disabled}"
										action="#{pc_Nsc00401T06.doPreviousAction}" tabindex="15"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton type="submit" value="＞"
										styleClass="commandExButton" id="next"
										disabled="#{pc_Nsc00401T06.nsc00401.propNext.disabled}" action="#{pc_Nsc00401T06.doNextAction}" tabindex="16"></hx:commandExButton></TD>
									<TD align="right"><hx:commandExButton type="submit" value="＞|"
										styleClass="commandExButton" id="last"
										disabled="#{pc_Nsc00401T06.nsc00401.propLast.disabled}"
										action="#{pc_Nsc00401T06.doLastAction}" tabindex="17"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
								<TR>
									<TD colspan="4"><BR>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD>

<%-- 2011-07-27 y.nishigaki GAKEX-1959対応 mod start --%>
<%--
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" style="border-bottom-style: none; margin-top: 10px; ">
										<TBODY>
											<TR>
												<TD class="tab_head_off" width="12%"><hx:commandExButton
													type="submit" value="志願者情報①" styleClass="tab_head_off"
													id="selectTab1" style="width: 100%"
													action="#{pc_Nsc00401T06.doSelectTab1Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab1.disabled}" tabindex="25"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="志願者情報②" styleClass="tab_head_off"
													id="selectTab2" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab2Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab2.disabled}" tabindex="26"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="試験情報" styleClass="tab_head_off"
													id="selectTab3" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab3Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab3.disabled}" tabindex="27"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="教科情報" styleClass="tab_head_off"
													id="selectTab4" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab4Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab4.disabled}" tabindex="28"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="出身校情報" styleClass="tab_head_off"
													id="selectTab5" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab5Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab5.disabled}" tabindex="29"></hx:commandExButton></TD>
												<TD class="tab_head_on" width="11%"><hx:commandExButton
													type="submit" value="保証人情報" styleClass="tab_head_on"
													id="selectTab6" style="width:100%"
													disabled="#{pc_Nsc00401T06.propSelectTab6.disabled}" tabindex="18"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="問合せ情報" styleClass="tab_head_off"
													id="selectTab7" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab7Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab7.disabled}" tabindex="30"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="自由設定" styleClass="tab_head_off"
													id="selectTab8" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab8Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab8.disabled}" tabindex="31"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="11%"><hx:commandExButton
													type="submit" value="留学生情報" styleClass="tab_head_off"
													id="selectTab9" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab9Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab9.disabled}" tabindex="32"></hx:commandExButton></TD>
--%>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="744px" style="border-bottom-style: none; margin-top: 10px; ">
										<TBODY>
											<TR>
												<TD class="tab_head_off" width="84px"><hx:commandExButton
													type="submit" value="志願者情報①" styleClass="tab_head_off"
													id="selectTab1" style="width: 100%"
													action="#{pc_Nsc00401T06.doSelectTab1Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab1.disabled}" tabindex="25"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="84px"><hx:commandExButton
													type="submit" value="志願者情報②" styleClass="tab_head_off"
													id="selectTab2" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab2Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab2.disabled}" tabindex="26"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="試験情報" styleClass="tab_head_off"
													id="selectTab3" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab3Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab3.disabled}" tabindex="27"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="教科情報" styleClass="tab_head_off"
													id="selectTab4" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab4Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab4.disabled}" tabindex="28"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="出身校情報" styleClass="tab_head_off"
													id="selectTab5" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab5Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab5.disabled}" tabindex="29"></hx:commandExButton></TD>
												<TD class="tab_head_on" width="82px"><hx:commandExButton
													type="submit" value="保証人情報" styleClass="tab_head_on"
													id="selectTab6" style="width:100%"
													disabled="#{pc_Nsc00401T06.propSelectTab6.disabled}" tabindex="18"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="問合せ情報" styleClass="tab_head_off"
													id="selectTab7" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab7Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab7.disabled}" tabindex="30"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="82px"><hx:commandExButton
													type="submit" value="自由設定" styleClass="tab_head_off"
													id="selectTab8" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab8Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab8.disabled}" tabindex="31"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="84px"><hx:commandExButton
													type="submit" value="留学生情報" styleClass="tab_head_off"
													id="selectTab9" style="width:100%"
													action="#{pc_Nsc00401T06.doSelectTab9Action}"
													disabled="#{pc_Nsc00401T06.propSelectTab9.disabled}" tabindex="32"></hx:commandExButton></TD>
<%-- 2011-07-27 y.nishigaki GAKEX-1959対応 mod end --%>


											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="100%" height="290">
										<TBODY>
											<TR>
												<TD>
												<TABLE width="98%" border="0" cellpadding="0"
													cellspacing="0">
													<TBODY>
														<TR>
															<TD align="right" width="100%"><hx:commandExButton
																type="submit" value="新規登録" styleClass="commandExButton"
																id="register" action="#{pc_Nsc00401T06.doRegisterAction}"
																disabled="#{pc_Nsc00401T06.propRegister.disabled}" tabindex="19"></hx:commandExButton></TD>
														</TR>
														<TR><TD height="4"></TD></TR>
														<TR>
															<TD align="right" width="100%"><h:outputText
																styleClass="outputText" id="htmlHsyCnt" value="#{pc_Nsc00401T06.propHsyList.listCount}">
																
															</h:outputText><h:outputText styleClass="outputText"
																id="lblCount" value="件"></h:outputText></TD>
														</TR>
														<TR><TD height="4"></TD></TR>
														<TR>
															<TD>
															<DIV class="listScroll" style="height:168px;width:730px" id="listScroll" 
																onscroll="setScrollPosition('scroll',this);"><h:dataTable
																border="0" cellpadding="2" cellspacing="0"
																headerClass="headerClass" footerClass="footerClass"
																rowClasses="#{pc_Nsc00401T06.propHsyList.rowClasses}"
																styleClass="meisai_scroll" id="htmlTableList"
																frame="void" value="#{pc_Nsc00401T06.propHsyList.list}" var="varlist">
																<h:column id="columnHsy1">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" id="lblColCheck"></h:outputText>
																	</f:facet>
																	<h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox" id="htmlHsyCheck"
																		value="#{varlist.selected}" tabindex="20"></h:selectBooleanCheckbox>
																	<f:attribute value="50" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>
																<h:column id="columnHsy2">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" id="lblColHsyNo"
																			value="保証人NO"></h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText" id="htmlHsyNo"
																		value="#{varlist.hsyNo}"></h:outputText>
																	<f:attribute value="100" name="width" />
																	<f:attribute value="text-align: left" name="style" />
																</h:column>
																<h:column id="columnHsy3">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="保証人種別"
																			id="lblColHsySbt"></h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText" id="htmlHsySbt"
																		value="#{varlist.hsySbtName}"></h:outputText>
																	<f:attribute value="150" name="width" />
																</h:column>
																<h:column id="columnHsy4">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="氏名"
																			id="lblColHsyName"></h:outputText>
																	</f:facet>
																	<f:attribute value="390" name="width" />
																	<h:outputText styleClass="outputText" id="htmlHsyName"
																		value="#{varlist.hsyName}"></h:outputText>
																</h:column>
																<h:column id="columnHsy5">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" id="lblColEdit"></h:outputText>
																	</f:facet>
																	<f:attribute value="30" name="width" />
																	<hx:commandExButton type="submit" value="編集"
																		styleClass="commandExButton" id="edit"
																		action="#{pc_Nsc00401T06.doEditAction}"
																		disabled="#{pc_Nsc00401T06.propEdit.disabled}" tabindex="21"></hx:commandExButton>
																</h:column>
															</h:dataTable></DIV>
															</TD>
														</TR>
														<TR>
														<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="meisai_scroll" width="100%">
																<TR>
																	<TD class="footerClass">
																	<TABLE class="panelBox">
																		<TBODY>
																			<TR>

																				<TD align="left"><hx:commandExButton type="button"
																					styleClass="check" id="multiCheck"
																					onclick="return func_1(this, event);"
																					disabled="#{pc_Nsc00401T06.propCheck.disabled}" tabindex="22"></hx:commandExButton><hx:commandExButton
																					type="button" styleClass="uncheck" id="multiCancel"
																					onclick="return func_2(this, event);"
																					disabled="#{pc_Nsc00401T06.propUncheck.disabled}" tabindex="23"></hx:commandExButton></TD>
																					<TD width="5"></TD>
																					<TD align="left">
																					<hx:commandExButton
																					type="submit" value="削除"
																					styleClass="commandExButton" id="deleteHsy"
																					action="#{pc_Nsc00401T06.doDeleteHsyAction}"
																					disabled="#{pc_Nsc00401T06.propDeleteHsy.disabled}"
																					confirm="#{msg.SY_MSG_0004W}" tabindex="24"></hx:commandExButton></TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																</TR>
															</TABLE>
															</TD>

														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%"
							class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="fix"
										action="#{pc_Nsc00401T06.doFixAction}"
										disabled="#{pc_Nsc00401T06.nsc00401.propFix.disabled}" confirm="#{msg.SY_MSG_0003W}" tabindex="33"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Nsc00401T06.doDeleteAction}"
										disabled="#{pc_Nsc00401T06.nsc00401.propDelete.disabled}" confirm="#{msg.SY_MSG_0004W}" tabindex="34"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Nsc00401T06.propHsyList.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsc00401T06.nsc00401.propExecutable.integerValue}"
				id="htmlExecutable">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsc00401T06.nsc00401.propExecutableFlg.integerValue}"
				id="htmlExecutableFlg">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

