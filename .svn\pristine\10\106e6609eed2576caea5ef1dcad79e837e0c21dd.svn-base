<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_NSE_NYS_HAK" name="入試通知書発行管理台帳" prod_id="NS" description="通知書の発行情報を持ちます。|通知書発行業務を行った時に作成します。">
<STATMENT><![CDATA[
NSE_NYS_HAK
]]></STATMENT>
<COLUMN id="HAKKO_NENDO" name="発行年度" type="number" length="4" lengthDP="0" byteLength="0" description="入試年度です。"/><COLUMN id="HAKKO_GAKKI_NO" name="発行学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="入試が実施される学期の番号です。"/><COLUMN id="BUNMEN_KBN" name="文面区分" type="string" length="5" lengthDP="0" byteLength="5" description="各種通知書を識別する為のコードが設定されます。"/><COLUMN id="HAKKO_NO" name="発行ＮＯ" type="number" length="6" lengthDP="0" byteLength="0" description="証明書に出力した発行番号が設定されます。"/><COLUMN id="JUKEN_CD" name="受験番号" type="string" length="10" lengthDP="0" byteLength="10" description="受験番号です。"/><COLUMN id="NAME" name="氏名" type="string" length="40" lengthDP="0" byteLength="120" description="氏名です。"/><COLUMN id="HAKKO_DATE" name="発行日付" type="date" length="0" lengthDP="0" byteLength="0" description="証明書の発行日付が設定されます。"/><COLUMN id="OUTPUT_DATE" name="出力日付" type="date" length="0" lengthDP="0" byteLength="0" description="証明書を実際に発行した日付が設定されます。"/><COLUMN id="HAK_FLG" name="発行一覧作成済フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="証明書発行管理台帳を出力したか否かを示します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN>
</TABLE>
