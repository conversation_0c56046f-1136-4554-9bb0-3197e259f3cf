<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghg00501T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>Ghg00601.jsp</TITLE>
	<SCRIPT type="text/javascript">
	
	function func_check_on(thisObj, thisEvent) {
		check('htmlPayhList','htmlPayChecked');
	}
	
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPayhList','htmlPayChecked');
	}
	
	function fncButtonActive(){
	    var codeRegSearch = null;
	    var codeRegNsSbtSearch = null;

		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch').value;
		if(codeRegSearch == 1){
			//選択ボタン(割当年度横)
			document.getElementById('form1:search').disabled = true;
		} else {
			//解除ボタン(割当年度横)
			document.getElementById('form1:unselect').disabled = true;
			//PDF作成・EXCEL作成・CSV作成・出力項目指定ボタン
			document.getElementById('form1:pdfout').disabled = true;
			document.getElementById('form1:excelout').disabled = true;
			document.getElementById('form1:csvout').disabled = true;
			document.getElementById('form1:setoutput').disabled = true;	
		}
		
		codeRegNsSbtSearch = document.getElementById('form1:htmlActiveControlNsSbtSearch').value;
		if(codeRegNsSbtSearch == 1){
			//選択ボタン(入試種別横)
			document.getElementById('form1:nsSbtSearch').disabled = true;
		} else {
			//解除ボタン(入試種別横)
			document.getElementById('form1:nsSbtUnselect').disabled = true;
		}
		
		//スクロール位置保持
		changeScrollPosition('scroll', 'listScroll');
	}
	
	//帳票タイトル変更
	function changePdfTitle(thisObj) {
		var code = thisObj.value;
		var befCode = document.getElementById('form1:htmlBeforeValue').value;
		var title = document.getElementById('form1:htmlIktPdfTitle').value;
		var hid1 = document.getElementById('form1:htmlPdfId01').value;
		var hid2 = document.getElementById('form1:htmlPdfId02').value;
		
		if(befCode == 2){
			document.getElementById('form1:htmlPdfId02').value = title;
		} else {
			document.getElementById('form1:htmlPdfId01').value = title;
		}
		
		if(code == 2){
			document.getElementById('form1:htmlIktPdfTitle').value = hid2;
		} else {
			document.getElementById('form1:htmlIktPdfTitle').value = hid1;
		}
		
		document.getElementById('form1:htmlBeforeValue').value = code;
	}
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		document.getElementById('form1:htmlExecutableSearch').value = 1;
		indirectClick('search');
	}
	
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}

	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}
	
	</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY onload="fncButtonActive();">
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghg00501T01.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp"
                    action="#{pc_Ghg00501T01.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghg00501T01.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghg00501T01.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
											<TBODY>
												<TR align="left">
													<TD>
														<hx:commandExButton type="submit" 
															value="一括指定"
															styleClass="tab_head_on" 
															id="htmlTabIkt" tabindex="1"></hx:commandExButton><hx:commandExButton
															type="submit" value="志願者指定" styleClass="tab_head_off"
															id="htmlTabSgn"
															tabindex="2" action="#{pc_Ghg00501T01.doHtmlTabSgnAction}">
														</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="1" cellpadding="20" cellspacing="0" width="100%" class="tab_body">
															<TBODY>
																<TR align="center">
																	<TD>
																		<TABLE width="850px">
																			<TR>
																				<TD height="20"></TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH width="150px" nowrap class="v_a">
																									<!-- 入試年度 -->
																									<h:outputText styleClass="outputText" 
																										id="lblNsNendo"
																										value="#{pc_Ghg00501T01.propNsNendo.labelName}"
																										style="#{pc_Ghg00501T01.propNsNendo.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="*" nowrap valign="middle">
																									<h:outputText styleClass="outputText" 
																										id="htmlNsNendo"
																										value="#{pc_Ghg00501T01.propNsNendo.value}"
																										style="#{pc_Ghg00501T01.propNsNendo.style}">
																									</h:outputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH width="150px" nowrap class="v_b">
																									<!-- 入試学期NO -->
																									<h:outputText styleClass="outputText" 
																										id="lblNsGakkiNo"
																										value="#{pc_Ghg00501T01.propNsGakkiNo.labelName}"
																										style="#{pc_Ghg00501T01.propNsGakkiNo.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:outputText styleClass="outputText" 
																										id="htmlNsGakkiNo" 
																										value="#{pc_Ghg00501T01.propNsGakkiNo.value}"
																										style="#{pc_Ghg00501T01.propNsGakkiNo.style};">
																									</h:outputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20"></TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH width="150px" class="v_a">
																									<h:outputText
																										styleClass="outputText" id="lblPayOutType"
																										value="#{pc_Ghg00501T01.propPayOutType.labelName}"
																										style="#{pc_Ghg00501T01.propPayOutType.style}">
																									</h:outputText>
																								</TH>
																								<TD nowrap class="v_a" colspan=2>
																									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" 
																										disabled="#{pc_Ghg00501T01.propPayOutType.disabled}"
																										id="htmlPayOutType"
																										 tabindex="3"
																										value="#{pc_Ghg00501T01.propPayOutType.value}">
																										<f:selectItem itemValue="0" itemLabel="一覧より選択" />
																										<f:selectItem itemValue="1" itemLabel="入力条件で出力" />
																									</h:selectOneRadio>
																								</TD>
																								<TD nowrap class="v_a" width="*" rowspan=4 >
																									<hx:commandExButton type="submit" value="選択"
																											styleClass="cmdBtn_dat_s" id="search" 
																											tabindex="9" action="#{pc_Ghg00501T01.doSearchAction}">
																										</hx:commandExButton>
																										<hx:commandExButton type="submit" value="解除"
																											styleClass="cmdBtn_etc_s" id="unselect" 
																											tabindex="10" action="#{pc_Ghg00501T01.doUnselectAction}">
																									</hx:commandExButton>
																								</TD>
																							</TR>
																							<TR align="center">
																								<TH width="150px" nowrap class="v_c">
																									<!-- 割当年度 -->
																									<h:outputText styleClass="outputText"
																										id="lblsNendo"
																										value="#{pc_Ghg00501T01.propNendo.labelName}"
																										style="#{pc_Ghg00501T01.propNendo.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="*" colspan=2>
																									<h:inputText styleClass="inputText"
																										id="htmlNendo"
																										value="#{pc_Ghg00501T01.propNendo.dateValue}"
																										size="4"
																										disabled="#{pc_Ghg00501T01.propNendo.disabled}"
																										maxlength="#{pc_Ghg00501T01.propNendo.maxLength}"
																										style="#{pc_Ghg00501T01.propNendo.style}"
																										tabindex="4">
																										<hx:inputHelperAssist imeMode="inactive"
																											errorClass="inputText_Error" promptCharacter="_" />
																										<f:convertDateTime pattern="yyyy" />
																									</h:inputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_a" nowrap width="150px">
																									<h:outputText styleClass="outputText"
																										id="lblPayCode" 
																										value="#{pc_Ghg00501T01.propPayCode.labelName}"
																										style="#{pc_Ghg00501T01.propPayCode.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD nowrap width="*">
																									<h:inputText styleClass="inputText"
																										id="htmlPayCode" 
																										value="#{pc_Ghg00501T01.propPayCode.stringValue}"
																										style="#{pc_Ghg00501T01.propPayCode.style}"
																										tabindex="5" 
																										disabled="#{pc_Ghg00501T01.propPayCode.disabled}"
																										size="6"
																										maxlength="#{pc_Ghg00501T01.propPayCode.maxLength}">
																									</h:inputText>
																								</TD>
																								<TD nowrap width="150px">
																									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" 
																										disabled="#{pc_Ghg00501T01.propPayCdFindType.disabled}"
																										id="htmlPayCdFindType"
																										 tabindex="6"
																										value="#{pc_Ghg00501T01.propPayCdFindType.value}">
																										<f:selectItem itemValue="0" itemLabel="前方一致" />
																										<f:selectItem itemValue="1" itemLabel="部分一致" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_b" nowrap nowrap width="150px">
																									<h:outputText styleClass="outputText" 
																										id="lblPayNm"
																										value="#{pc_Ghg00501T01.propPayNm.labelName}"
																										style="#{pc_Ghg00501T01.propPayNm.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD nowrap width="*">
																									<h:inputText styleClass="inputText"
																										id="htmlPayNm" 
																										value="#{pc_Ghg00501T01.propPayNm.value}"
																										style="#{pc_Ghg00501T01.propPayNm.style}"
																										tabindex="7" 
																										disabled="#{pc_Ghg00501T01.propPayNm.disabled}"
																										size="40"
																										maxlength="#{pc_Ghg00501T01.propPayNm.maxLength}">
																									</h:inputText>
																								</TD>
																								<TD nowrap width="150px">
																									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" 
																										disabled="#{pc_Ghg00501T01.propPayNmFindType.disabled}"
																										id="htmlPayNmFindType"
																										 tabindex="8"
																										value="#{pc_Ghg00501T01.propPayNmFindType.value}">
																										<f:selectItem itemValue="0" itemLabel="前方一致" />
																										<f:selectItem itemValue="1" itemLabel="部分一致" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>	
																			</TR>
																			<TR>												
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="layoutTable">
																						<TBODY>
																							<TR>
																								<TD align="right">
																									<h:outputText 
																										styleClass="outputText" 
																										id="htmlCount" 
																										value="#{pc_Ghg00501T01.propPayhList.listCount}">
																									</h:outputText>
																									<h:outputText 
																										styleClass="outputText" 
																										id="lblCount" value="件">
																									</h:outputText>
																								</TD>
																							</TR>
																							<TR>
																								<TD>
																									<DIV style="height: 233px; width=100%;" id="listScroll" onscroll="setScrollPosition('scroll',this);" class="listScroll">
																										<h:dataTable border="0" 
																										rowClasses="#{pc_Ghg00501T01.propPayhList.rowClasses}"
																										headerClass="headerClass"
																										footerClass="footerClass" styleClass="meisai_scroll"
																										id="htmlPayhList"
																										value="#{pc_Ghg00501T01.propPayhList.list}"
																										var="varlist" width="827px">
																										<h:column id="column8">
																											<f:facet name="header">
										
																											</f:facet>								
																											<h:selectBooleanCheckbox
																												styleClass="selectBooleanCheckbox"
																												id="htmlPayChecked" value="#{varlist.payChecked}"
																												rendered="#{varlist.rendered}" tabindex="11">
																											</h:selectBooleanCheckbox>
																											<f:attribute value="30px" name="width" />
																											<f:attribute value="true" name="nowrap" />
																										</h:column>
																										<h:column id="column5">
																											<f:facet name="header">
																												<h:outputText 
																													styleClass="outputText"
																													value="#{pc_Ghg00501T01.propPayCd.labelName}"
																													id="lblPayCd">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText id="htmpPayCd" 
																												value="#{varlist.payCd}"
																												styleClass="outputText">
																											</h:outputText>
																											<f:attribute value="120px" name="width" />
																											<f:attribute value="true" name="nowrap" />
																											<f:attribute value="text-align: left" name="style" />
																										</h:column>
																										<h:column id="column2">
																											<f:facet name="header">
																												<h:outputText 
																													styleClass="outputText"
																													value="#{pc_Ghg00501T01.propPatternCd.labelName}"
																													id="lblPatternCd">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText id="htmlPatternCd"
																											value="#{varlist.patternCd}" styleClass="outputText">
																											</h:outputText>
																											<f:attribute value="120px" name="width" />
																											<f:attribute value="true" name="nowrap" />
																											<f:attribute value="text-align: left" name="style" />
																										</h:column>
																										<h:column id="column7">
																											<f:facet name="header">
																												<h:outputText 
																													styleClass="outputText"
																													value="#{pc_Ghg00501T01.propBunnoKbnCd.labelName}"
																													id="lblBunnoKbnCd">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText id="htmlBunnoKbnCd"
																												value="#{varlist.bunnoKbnCd}"
																												styleClass="outputText">
																											</h:outputText>
																											<f:attribute value="120px" name="width" />
																											<f:attribute value="true" name="nowrap" />
																											<f:attribute value="text-align: center" name="style" />
																										</h:column>
																										<h:column id="column6">
																											<f:facet name="header">
																												<h:outputText 
																													styleClass="outputText"
																													value="#{pc_Ghg00501T01.propPayName.labelName}"
																													id="lblPayName">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText id="htmlPayName"
																												value="#{varlist.payName}" styleClass="outputText">
																											</h:outputText>
																											<f:attribute value="*" name="width" />
																											<f:attribute value="true" name="nowrap" />
																											<f:attribute value="text-align: left" name="style" />
																										</h:column>
																										<h:column id="column4">
																											<f:facet name="header">
																												<h:outputText 
																													styleClass="outputText"
																													value="#{pc_Ghg00501T01.propBunkatsuNo.labelName}"
																													id="lblBunkatsuNo">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText id="htmlBunkatsuNo"
																												value="#{varlist.bunkatsuNo}"
																												styleClass="outputText">
																											</h:outputText>
																											<f:attribute value="100px" name="width" />
																											<f:attribute value="true" name="nowrap" />
																											<f:attribute value="text-align: center" name="style" />
																										</h:column>
																										</h:dataTable>
																									</DIV>
																								</TD>
																							</TR>
																							<TR>
																								<TD>
																									<TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
																										<TBODY>
																											<TR>
																												<TD class="footerClass">
																													<TABLE class="panelBox">
																														<TBODY>
																															<TR>
																																<TD>
																																	<%-- 全選択・全解除 --%>
																																	<hx:jspPanel id="bankJspPanel">
																																	<INPUT type="button" name="check" value="on"
																																		onclick="return func_check_on(this, event, 0);"
																																		class="check" tabindex="12">
																																	<INPUT type="button" name="uncheck" value="off"
																																		onclick="return func_check_off(this, event, 0);"
																																		class="uncheck" tabindex="13">
																																	</hx:jspPanel>
																																</TD>
																															</TR>
																														</TBODY>
																													</TABLE>
																												</TD>
																											</TR>
																										</TBODY>
																									</TABLE>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20"></TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH width="150px" class="v_d">
																									<!-- 入試種別 -->
																									<h:outputText
																										styleClass="outputText" id="lblNsSbt"
																										value="#{pc_Ghg00501T01.propNsSbt.labelName}"
																										style="#{pc_Ghg00501T01.propNsSbt.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:selectOneMenu
																										styleClass="selectOneMenu"
																										id="htmlNsSbt"
																										style="#{pc_Ghg00501T01.propNsSbt.style};width:530px"
																										value="#{pc_Ghg00501T01.propNsSbt.stringValue}" 
																										disabled="#{pc_Ghg00501T01.propNsSbt.disabled}" tabindex="14">
																									<f:selectItems value="#{pc_Ghg00501T01.propNsSbt.list}" />
																									</h:selectOneMenu>
																									<hx:commandExButton type="submit" value="選択"
																										styleClass="cmdBtn_dat_s" id="nsSbtSearch"
																										tabindex="15" action="#{pc_Ghg00501T01.doNsSbtSearchAction}">
																									</hx:commandExButton>
																									<hx:commandExButton type="submit" value="解除"
																										styleClass="cmdBtn_etc_s" id="nsSbtUnselect"
																										tabindex="16" action="#{pc_Ghg00501T01.doNsSbtUnselectAction}">
																									</hx:commandExButton>
																								</TD>
																							</TR>
																							<TR>
																								<TH width="150px" class="v_e">
																									<!-- 学科組織 -->
																									<h:outputText
																										styleClass="outputText" id="lblGakka"
																										value="#{pc_Ghg00501T01.propGakka.labelName}"
																										style="#{pc_Ghg00501T01.propGakka.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:selectOneMenu
																										styleClass="selectOneMenu"
																										id="htmlGakka"
																										style="#{pc_Ghg00501T01.propGakka.style};width:530px"
																										value="#{pc_Ghg00501T01.propGakka.stringValue}"
																										disabled="#{pc_Ghg00501T01.propGakka.disabled}" tabindex="17">
																									<f:selectItems value="#{pc_Ghg00501T01.propGakka.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																							<TR>
																								<TH width="150px" class="v_f">
																									<!-- 免除種別 -->
																									<h:outputText
																										styleClass="outputText" id="lblMenjSbt"
																										value="#{pc_Ghg00501T01.propMenjSbt.labelName}"
																										style="#{pc_Ghg00501T01.propMenjSbt.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:selectOneMenu
																										styleClass="selectOneMenu"
																										id="htmlIktMenjSbt" 
																										style="#{pc_Ghg00501T01.propMenjSbt.style}"
																										value="#{pc_Ghg00501T01.propMenjSbt.stringValue}" tabindex="18">
																									<f:selectItems value="#{pc_Ghg00501T01.propMenjSbt.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR><TD height="20"></TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH width="150px" class="v_b">
																									<h:outputText
																										styleClass="outputText" id="lblOutput"
																										value="#{pc_Ghg00501T01.propOutputType.name}"
																										style="#{pc_Ghg00501T01.propOutputType.style}">
																									</h:outputText>
																								</TH>
																								
																								<TD width="*">
																									<TABLE border="0" class="clear_border" width="100%">
																									<TBODY>
																										<TR>
																										<TD width="75">
																											<h:selectOneRadio
																												disabledClass="selectOneRadio_Disabled"
																												styleClass="selectOneRadio" id="htmlOutputType"
																												value="#{pc_Ghg00501T01.propOutputType.value}"
																												style="#{pc_Ghg00501T01.propOutputType.style}"
																												onclick="changePdfTitle(this);"
																												layout="pageDirection" tabindex="19">
																												<f:selectItem itemValue="1" itemLabel="内訳有" />
																												<f:selectItem itemValue="2" itemLabel="内訳無" />
																											</h:selectOneRadio>
																										</TD>
																										<TD>
																											<TABLE border="0" class="clear_border">
																												<TBODY>
																												<TR>
																													<TD>
																														<h:outputText styleClass="outputText" id="ChokaStart" value="（"></h:outputText>
																														<h:selectBooleanCheckbox
																															styleClass="selectBooleanCheckbox"
																															value="#{pc_Ghg00501T01.propIsChoka.checked}"
																															id="htmlIsChoka" style="#{pc_Ghg00501T01.propIsChoka.style}" tabindex="20">
																														</h:selectBooleanCheckbox>
																														<h:outputText styleClass="selectManyCheckbox" id="lblIsChoka" value="超過金を含む"></h:outputText>
																														<h:outputText styleClass="outputText" id="ChokaEnd" value="）"></h:outputText>
																													</TD>
																													

																													
																												</TR>
																												<TR>
																													<TD>
																													</TD>
																												</TR>
																												</TBODY>
																											</TABLE>
																										</TD>
																										</TR>
																									</TBODY>
																									</TABLE>
																								</TD>
																								
																							</TR>
																							<TR>
																								<TH width="150px" class="v_a">
																									<!-- 小計出力 -->
																									<h:outputText
																										styleClass="outputText" id="lblSum"
																										value="#{pc_Ghg00501T01.propSum.name}"
																										style="#{pc_Ghg00501T01.propSum.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:selectBooleanCheckbox
																										styleClass="selectBooleanCheckbox"
																										value="#{pc_Ghg00501T01.propSum.checked}"
																										id="htmIktlSum" style="#{pc_Ghg00501T01.propSum.style}" tabindex="21">
																									</h:selectBooleanCheckbox>
																								</TD>
																							</TR>
																							<TR>
																								<TH width="150px" class="v_b">
																									<!-- 帳票タイトル -->
																									<h:outputText
																										styleClass="outputText" id="lblPdfTitle"
																										value="#{pc_Ghg00501T01.propPdfTitle.labelName}"
																										style="#{pc_Ghg00501T01.propPdfTitle.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:inputText
																										styleClass="inputText" size="100"
																										id="htmlIktPdfTitle"
																										style="#{pc_Ghg00501T01.propPdfTitle.style}"
																										value="#{pc_Ghg00501T01.propPdfTitle.stringValue}"
																										maxlength="#{pc_Ghg00501T01.propPdfTitle.maxLength}" tabindex="22">
																									</h:inputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20"></TD>
																			</TR>
																		</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD width="100%">
														<hx:commandExButton
															type="submit" value="PDF作成"
															styleClass="commandExButton_out" id="pdfout" 
															tabindex="23" action="#{pc_Ghg00501T01.doPdfoutAction}" 
															confirm="#{msg.SY_MSG_0019W}">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton
															type="submit" value="EXCEL作成"
															styleClass="commandExButton_out" id="excelout" 
															tabindex="24" action="#{pc_Ghg00501T01.doExceloutAction}" 
															confirm="#{msg.SY_MSG_0027W}">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton
															type="submit" value="CSV作成"
															styleClass="commandExButton_out" id="csvout" 
															tabindex="25" confirm="#{msg.SY_MSG_0020W}" 
															action="#{pc_Ghg00501T01.doCsvoutAction}">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton
															type="submit" value="出力項目指定"
															styleClass="commandExButton_out" id="setoutput" 
															tabindex="26" action="#{pc_Ghg00501T01.doSetoutputAction}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>		
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			</DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />
            <h:inputHidden
   				value="#{pc_Ghg00501T01.propExecutableSearch.integerValue}"
   				id="htmlExecutableSearch">
   			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00501T01.propActiveControlSearch.value}"
				id="htmlActiveControlSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00501T01.propActiveControlNsSbtSearch.value}"
				id="htmlActiveControlNsSbtSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00501T01.propPdfId01.stringValue}"
				id="htmlPdfId01">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00501T01.propPdfId02.stringValue}"
				id="htmlPdfId02">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00501T01.propBeforeValue.stringValue}"
				id="htmlBeforeValue">
			</h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghg00501T01.propPayhList.scrollPosition}" id="scroll">
			</h:inputHidden>

        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
