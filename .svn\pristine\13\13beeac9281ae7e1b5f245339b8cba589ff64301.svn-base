<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/kk/Kka00403.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kka00403.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
// 会員一覧全選択
function checkKaiList(thisObj, thisEvent) {
	check('htmlKaiList','htmlChkKai');
}

// 会員一覧全解除
function uncheckKaiList(thisObj, thisEvent) {
	uncheck('htmlKaiList','htmlChkKai');
}


</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kka00403.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Kka00403">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kka00403.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kka00403.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kka00403.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
	styleClass="commandExButton" id="returnDisp"
	action="#{pc_Kka00403.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>

<DIV id="content">			
<DIV class="column">

<!-- ↓ここにコンポーネントを配置 -->

	<TABLE border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD width="900">
				<TABLE border="0" class="table" width="100%">
					<TBODY>
						<TR>
							<TH nowrap class="v_a" width="170"><!-- 出力形式 --><h:outputText
								styleClass="outputText" id="lblFileSelect"
								value="#{pc_Kka00403.propFileSelect.labelName}"
								style="#{pc_Kka00403.propFileSelect.labelStyle}"></h:outputText>
							</TH>
							<TD width="280"><h:outputText
			                    id="htmlFileSelect" styleClass="outputText"
			                    value="#{pc_Kka00403.propFileSelect.stringValue}"></h:outputText>
							</TD>
							<TH nowrap class="v_b" width="170"><!-- 帳票タイトル --><h:outputText
								styleClass="outputText" id="lblPdfTitle"
								value="#{pc_Kka00403.propPdfTitle.labelName}"
								style="#{pc_Kka00403.propPdfTitle.labelStyle}"></h:outputText></TH>
							<TD><h:outputText
			                    id="htmlPdfTitle" styleClass="outputText"
			                    value="#{pc_Kka00403.propPdfTitle.displayValue}"
								title="#{pc_Kka00403.propPdfTitle.value}"></h:outputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>

				<TABLE style="margin-top:10px" width="100%" border="0" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
							<TD align="right" nowrap class="outputText" width="100%"><h:outputText
								styleClass="outputText" id="lblKaiListCnt" value="#{pc_Kka00403.propKaiList.listCount}"></h:outputText>件</TD>
						</TR>
						<TR>
							<TD>
								<div class="listScroll" id="listScroll" style="height: 445px" onscroll="setScrollPosition('htmlHidScroll', this);">
									<h:dataTable
										border="1" cellpadding="2" cellspacing="0"
										headerClass="headerClass" footerClass="footerClass"
										columnClasses="columnClass1"
										rowClasses="#{pc_Kka00403.propKaiList.rowClasses}"
										styleClass="meisai_scroll" id="htmlKaiList" var="varlist"
										value="#{pc_Kka00403.propKaiList.list}">
										<h:column id="column1">
											<f:facet name="header">
											</f:facet>
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
												id="htmlChkKai" value="#{varlist.checkKai}"
												rendered="#{varlist.dummyChkBox.rendered}"></h:selectBooleanCheckbox>
											<f:attribute value="26" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText" id="lblKaiinNo_head"
													value="会員番号"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblKaiinNo_list"
												value="#{varlist.propKaiinNo.stringValue}"></h:outputText>
											<f:attribute value="130" name="width" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText" id="lblName_head"
													value="氏名"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblName_list"
												value="#{varlist.name.displayValue}"
												title="#{varlist.name.value}"></h:outputText>
											<f:attribute value="190" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText" id="lblSotNendo_head"
													value="卒業年度"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblSotNendo_list"
												value="#{varlist.propSotNendo.stringValue}"></h:outputText>
											<f:attribute value="80" name="width" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText styleClass="outputText" id="lblSotGakkiNm_head"
													value="卒業学期"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblSotGakkiNm_list"
												value="#{varlist.propSotGakkiNm.stringValue}"></h:outputText>
											<f:attribute value="100" name="width" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
												<hx:jspPanel id="jspPanel1">
													<TABLE border="0" cellpadding="0" cellspacing="0"
														width="300" height="100%">
														<TBODY>
															<TR>
																<TH
																	style="border-right-style: none; border-style: none; text-align: center;"
																	width="300" colspan="2">卒業時所属学科組織</TH>
															</TR>
															<TR>
																<TH
																	style="border-left-style:none; border-bottom-style:none; text-align: center;"
																	width="70"><h:outputText styleClass="outputText"
																	id="lblSotSgksCd_head" value="コード"></h:outputText></TH>
																<TH
																	style=" border-right-style: none; border-bottom-style: none; text-align: center;"
																	width="230"><h:outputText styleClass="outputText"
																	id="lblSotSgksNm_head" value="卒業時所属学科組織名称"></h:outputText></TH>
															</TR>
														</TBODY>
													</TABLE>
												</hx:jspPanel>
											</f:facet>
											<hx:jspPanel id="jspPanel2">
												<TABLE border="0" cellpadding="0" cellspacing="0" width="300" height="100%"
													style="border-bottom-style:none;border-top-style:none;">
													<TBODY>
														<TR>
															<TD
																style="border-top-style:none; border-bottom-style:none;
																border-right-style:none; border-left-style:none;"
																width="70"><h:outputText styleClass="outputText"
																id="lblSotSgksCd_list" value="#{varlist.propSotSgksCd.stringValue}"></h:outputText>
															</TD>
															<TD
																style="border-top-style:none; border-bottom-style: none; border-right-style: none;"
																width="230"><h:outputText styleClass="outputText"
																id="lblSotSgksNm_list"
																value="#{varlist.propSotSgksNm.displayValue}"
																title="#{varlist.propSotSgksNm.value}"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
											</hx:jspPanel>
										</h:column>
										<h:column id="column7">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="選択"
												styleClass="commandExButton" id="listSelect"
												action="#{pc_Kka00403.doListSelectAction}"></hx:commandExButton>
											<f:attribute value="45" name="width" />
											<f:attribute value="text-align:center" name="style" />
										</h:column>
									</h:dataTable>
								</div>
							</TD>
						</TR>
						<TR>
							<TD>
								<TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
									<TBODY>											
										<TR>
											<TD class="footerClass">
												<TABLE class="panelBox">
													<TBODY>											
														<TR>
															<TD align="left"><hx:commandExButton type="button"
																styleClass="check" id="check"
																onclick="return checkKaiList(this, event);"></hx:commandExButton>
																<hx:commandExButton	type="button" styleClass="uncheck" id="uncheck"
																onclick="return uncheckKaiList(this, event);"></hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>

				<TABLE cellspacing="0" cellpadding="0" class="button_bar" width="100%" style="margin-top:5px">
					<TR>
						<!-- PDF作成ボタン--><!-- CSV作成ボタン --><!-- 出力項目指定ボタン -->
						<TD align="center">
							<hx:commandExButton type="submit"
								value="PDF作成" styleClass="commandExButton_out" id="pdfout"
								action="#{pc_Kka00403.doPdfOutAction}"
								confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" styleClass="commandExButton_out"
								id="excelout" value="EXCEL作成"
								action="#{pc_Kka00403.doExcelOutAction}"
								confirm="#{msg.SY_MSG_0027W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" styleClass="commandExButton_out"
								id="csvout" value="CSV作成"
								action="#{pc_Kka00403.doCsvOutAction}"
								confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" styleClass="commandExButton_out"
								id="setoutput" value="出力項目指定"
								action="#{pc_Kka00403.doSetoutputAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
  <h:inputHidden id="htmlHidScroll" value="#{pc_Kka00403.propKaiList.scrollPosition}"></h:inputHidden>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>
</HTML>
