<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00402.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsc00402.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
function onReturnClick(id) {
// 「戻る」ボタンクリック処理

	var args = new Array();
	args[0] = "";
	return confirm(messageCreate(id, args));
}
window.onload = init;

function init(){
	// iframeを生成する
	createHeadShim("form1:htmlTableListKyoka");
	// スクロールを戻す処理を実装
	changeScrollPosition('scroll', 'listScroll');
}

function createHeadShim(tableId){
	children = document.getElementById(tableId).getElementsByTagName("thead");
	var headerObject;
	var top;
	var left;
	for (i = 0;i < children.length;i++) {
		headerObject = children[i];
		thObject = headerObject.getElementsByTagName("th");
		for (j = 0;j < thObject.length;j++) {
			thObject[j].style.zIndex = 2;
			if (j == 0){
				top = thObject[j].offsetTop;
				left = thObject[j].offsetLeft;
			}
		}
	} 
	var shim = document.createElement("<iframe scrolling='no' frameborder='0'"+
	"style='position:absolute; top:0px;"+
	"left:0px; display:none' id='headerObject'></iframe>");
	window.document.body.appendChild(shim);
	shim.style.width = headerObject.offsetWidth;
	shim.style.height = headerObject.offsetHeight;
	shim.style.top = top;
	shim.style.left = left;
	shim.style.zIndex = 1;
	shim.style.position = "absolute";
	shim.style.display = "block";
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc00402.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc00402.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc00402.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc00402.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp"
				onclick="return onReturnClick('#{msgCO.CO_MSG_0013W}');"
				action="#{pc_Nsc00402.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD><TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="760">
							<TBODY>
								<TR>
                                  <TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblSgnCd"
										value="#{pc_Nsc00402.propSgnCd.labelName}"></h:outputText></TH>
                                  <TD width="150"><h:outputText
										styleClass="outputText" id="htmlSgnCd"
										value="#{pc_Nsc00402.propSgnCd.stringValue}"></h:outputText></TD>
                                  <TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblJukenCd"
										value="#{pc_Nsc00402.propJukenCd.labelName}"></h:outputText></TH>
                                  <TD><h:outputText
										styleClass="outputText" id="htmlJukenCd"
										value="#{pc_Nsc00402.propJukenCd.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
                                  <TH width="150" class="v_c"><h:outputText styleClass="outputText" id="lblSgnName" value="#{pc_Nsc00402.propSgnName.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:outputText styleClass="outputText"
										id="htmlSgnName"
										value="#{pc_Nsc00402.propSgnName.stringValue}"></h:outputText></TD>
								</TR>
							</TBODY>
                        </TABLE>
                      </TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText styleClass="outputText"
										id="htmlListCount" value="#{pc_Nsc00402.propKyokaList.listCount}">
										
									</h:outputText><h:outputText styleClass="outputText"
										id="lblCount" value="件"></h:outputText></TD>
								</TR>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
											<TBODY>
												<TR>
													<TD>
														<DIV class="listScroll" style="height:350px" id="listScroll"
														align="center" onscroll="setScrollPosition('scroll',this);"><h:dataTable border="0" cellpadding="0"
														cellspacing="0" rowClasses="#{pc_Nsc00402.propKyokaList.rowClasses}"
														styleClass="meisai_scroll" id="htmlTableListKyoka"
														value="#{pc_Nsc00402.propKyokaList.list}" var="varlist" footerClass="footerClass" headerClass="headerClass" width="745">
														<h:column id="columnKyoka1">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="教科番号"
																	id="lblColSenkoHohoNo"
																	style="text-align: center; vertical-align: super"></h:outputText>
															</f:facet>
															<f:attribute value="100" name="width" />
														<f:attribute
															value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: left"
															name="style" />
														<h:outputText styleClass="outputText" id="htmlSenkoHohoNo"
																value="#{varlist.senkoHohoNo}"></h:outputText>
														</h:column>
														<h:column id="columnKyoka2">
															<f:facet name="header">
																<hx:jspPanel id="jspPanel1">
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		width="100%" class="table" height="100%">
																		<TBODY>
																			<TR>
																				<TH colspan="2"
																					style="border-left-style: none; border-right-style: none; border-top-style: none;"
																					width="100"><h:outputText styleClass="outputText"
																					value="選択教科条件" id="lblSentakuKyoka"
																					style="text-align: center"></h:outputText></TH>
																			</TR>
																			<TR>
																				<TH
																					style="border-left-style: none; border-bottom-style: none; border-top-style: none;"
																					width="50"><h:outputText styleClass="outputText"
																					value="NO" id="lblKyokaJokenCd"
																					style="text-align: center"></h:outputText></TH>
																				<TH
																					style="border-right-style: none; border-bottom-style: none; border-top-style: none;"
																					width="50"><h:outputText styleClass="outputText"
																					value="選択" id="lblSentaku"
																					style="text-align: center"></h:outputText></TH>
																			</TR>
																		</TBODY>
																	</TABLE>
																</hx:jspPanel>
															</f:facet>
															<hx:jspPanel id="jspPanel2">
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	width="100%">
																	<TBODY>
																		<TR>
																		<TD width="50"
																			style="border-left-style: none; border-top-style: none; border-bottom-style: none;text-align: left"><h:outputText
																			styleClass="outputText" id="htmlKyokaJokenCd"
																			value="#{varlist.kykStJkn}"
																			style="text-align: center"></h:outputText></TD>
																		<TD width="50"
																				style="border-right-style: none; border-left-style: none; border-top-style: none; border-bottom-style: none;text-align: center"><h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox" id="htmlSentaku"
																				value="#{varlist.selected}"
																				disabled="#{varlist.propSelect.disabled}" tabindex="1"></h:selectBooleanCheckbox></TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</hx:jspPanel>
				
															<f:attribute
																value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
																name="style" />
															<f:attribute value="100" name="width" />
														</h:column>
				
														<h:column id="columnKyoka3">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="必須"
																	id="lblColHissu"></h:outputText>
															</f:facet>
															<f:attribute value="40" name="width" />
															<f:attribute
																value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
																name="style" />
															<h:outputText styleClass="outputText" id="htmlHissu"
																value="#{varlist.hissu}"></h:outputText>
														</h:column>
														<h:column id="columnKyoka4">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="教科"
																	id="lblColKyokaName" style="text-align: center"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText" id="htmlKyokaName"
																value="#{varlist.kykName}"></h:outputText>
															<f:attribute value="315" name="width" />
														</h:column>
														<h:column id="columnKyoka5">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="科目"
																	id="lblColKamok" style="text-align: center"></h:outputText>
															</f:facet>
														<h:selectOneMenu styleClass="selectOneMenu" id="htmlKamok"
															style="width:170px;" value="#{varlist.propKamok.value}"
															disabled="#{varlist.propKamok.disabled}"
															readonly="#{varlist.propKamok.readonly}" tabindex="2">

															<f:selectItems value="#{varlist.propKamok.list}" />
														</h:selectOneMenu>
														<f:attribute value="190" name="width" />
															<f:attribute
																value="margin: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; margin-top: 0px; padding: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px; text-align: center"
																name="style" />
														</h:column>
													</h:dataTable></DIV>
													</TD>
												</TR>

											</TBODY>
										</TABLE>
									</TD>
								</TR>

								<TR>
									<TD align="left"></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="fix"
										disabled="#{pc_Nsc00402.propExec.disabled}"
										confirm="#{msg.SY_MSG_0003W}" action="#{pc_Nsc00402.doFixAction}" tabindex="3"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
                     </TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Nsc00402.propKyokaList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

