<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmb02006.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.framework.util.UtilSystem" %>
<%@ page import="com.jast.gakuen.rev.km.action.bean.Kmb02000Bean" %>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Kmb02006.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--    title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >  
<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
check('htmlHaitouKamokuList', 'htmlHaitouKamokuCheck');
}
function func_2(thisObj, thisEvent) {
uncheck('htmlHaitouKamokuList', 'htmlHaitouKamokuCheck');
}
function func_3(thisObj, thisEvent) {
check('htmlYosoKamokuList', 'htmlYosoKamokuCheck');
}
function func_4(thisObj, thisEvent) {
uncheck('htmlYosoKamokuList', 'htmlYosoKamokuCheck');

}

<% 
	Kmb02000Bean pageInfo 
	= (Kmb02000Bean)UtilSystem.getManagedBean(Kmb02000Bean.class); 
%>

// 科目明細配当を検索する
function openKmkHaiSearchWindow() {

	var url = "${pageContext.request.contextPath}/faces/rev/km/pKmb0101.jsp"
			+ "?retFieldNameKamokuCd=form1:htmlKamokuCode"
			+ "&retFieldNameMeisaiNo=form1:htmlMeisaiNo"
			+ "&nendo=<%= pageInfo.getNyugakNendo() %>"
			+ "&gakkiNo=<%= pageInfo.getGakkiNo() %>"
			+ "&curGakkaCd=<%= pageInfo.getCurGakkaCd() %>"
			+ "&sikakutaisyoFlg=0"
			+ "&nendoFlg=0"
			+ "&gakkiNoFlg=0"
			+ "&curGakkaCdFlg=0";

	openModalWindow(url, "pKmb0101", "<%= com.jast.gakuen.rev.km.PKmb0101.getWindowOpenOption() %>");
	//setTarget("Kmb01802");
	return true;
}

// 科目明細配当名称を取得する
function doKmkHaiAjax(thisObj, thisEvent) {

	var servlet = "rev/km/KmbKmkHaiAJAX";
	var target = "form1:htmlKamokuName";
	var args = new Array();
	args['nendo'] = <%= pageInfo.getNyugakNendo() %>;
	args['gakkiNo'] = <%= pageInfo.getGakkiNo() %>;
	args['curGakkaCd'] = "<%= pageInfo.getCurGakkaCd() %>";
	args['kamokuCd'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function loadAction(event){
  doKmkHaiAjax(document.getElementById('form1:htmlKamokuCode'), event);
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="loadAction(event)"><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmb02006.onPageLoadBegin}">
    <h:form styleClass="form" id="form1">
        <!-- ヘッダーインクルード -->
        <jsp:include page ="../inc/header.jsp" />
        <!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmb02006.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmb02006.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmb02006.screenName}"></h:outputText></div>
    <!--↓outer↓-->
    <DIV class="outer">
    
    <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
        styleClass="outputText" escape="false">
    </h:outputText>
    </FIELDSET>

    <DIV class="head_button_area" >
    <!-- ↓ここに戻るボタンを配置 -->
        <TABLE>
            <TR>
                <TD nowrap align="right"><hx:commandExButton
						type="submit" value="戻　る" styleClass="commandExButton"
						id="returnDisp" action="#{pc_Kmb02006.doReturnDispAction}"
						tabindex="14"></hx:commandExButton></TD>
                </TR>
        </TABLE>
    <!-- ↑ここに戻るボタンを配置 -->
    </DIV>
    
    <!--↓content↓-->
    <DIV id="content">
        <DIV class="column" align="center">
            <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="816">
                <TBODY>
                    <TR>
                        <TH class="v_a" width="150"><h:outputText styleClass="outputText"
                            id="lblNyugakuNendo" value="#{pc_Kmb02006.propNyugakuNendo.labelName}"></h:outputText></TH>
                        <TD width="100"><h:outputText styleClass="outputText"
                            id="htmlNyugakuNendo" value="#{pc_Kmb02006.propNyugakuNendo.stringValue}"></h:outputText></TD>
                        <TH class="v_a" width="150"><h:outputText styleClass="outputText"
                            id="lblNyugakuGakki" value="#{pc_Kmb02006.propNyugakuGakki.labelName}"></h:outputText></TH>
                        <TD width="416"><h:outputText styleClass="outputText"
                            id="htmlNyugakuGakki" value="#{pc_Kmb02006.propNyugakuGakki.stringValue}"></h:outputText></TD>
                    </TR>
                    <TR>
                        <TH class="v_b" width="150"><h:outputText styleClass="outputText"
                            id="lblCurGakka" value="#{pc_Kmb02006.propCurGakka.labelName}"></h:outputText></TH>
                        <TD colspan="3" width="666"><h:outputText
							styleClass="outputText" id="htmlCurGakka"
							value="#{pc_Kmb02006.propCurGakka.displayValue}"
							title="#{pc_Kmb02006.propCurGakka.stringValue}"></h:outputText></TD>
                    </TR>
                    <TR>
                        <TH class="v_c" width="150"><h:outputText styleClass="outputText"
                            id="lblTaisyoGakunen" value="#{pc_Kmb02006.propTaisyoGakunen.labelName}"></h:outputText></TH>
                        <TD width="100"><h:outputText styleClass="outputText"
                            id="htmlTaisyoGakunen" value="#{pc_Kmb02006.propTaisyoGakunen.stringValue}"></h:outputText></TD>
                        <TH class="v_c" width="150"><h:outputText styleClass="outputText"
                            id="lblTaisyoSemester" value="#{pc_Kmb02006.propTaisyoSemester.labelName}"></h:outputText></TH>
                        <TD width="416"><h:outputText styleClass="outputText"
                            id="htmlTaisyoSemester" value="#{pc_Kmb02006.propTaisyoSemester.stringValue}"></h:outputText></TD>
                    </TR>
                    <TR>
                        <TH class="v_d" width="150"><h:outputText styleClass="outputText"
                            id="lblJokenCode" value="#{pc_Kmb02006.propJokenCode.labelName}"></h:outputText></TH>
                        <TD width="100"><h:outputText styleClass="outputText"
                            id="htmlJokenCode" value="#{pc_Kmb02006.propJokenCode.stringValue}"></h:outputText></TD>
                        <TH class="v_d" width="150"><h:outputText styleClass="outputText"
                            id="lblTitle" value="#{pc_Kmb02006.propTitle.labelName}"></h:outputText></TH>
                        <TD width="416"><h:outputText styleClass="outputText"
                            id="htmlTitle" value="#{pc_Kmb02006.propTitle.stringValue}"></h:outputText></TD>
                    </TR>
                    <TR>
                        <TH class="v_e" width="150"><h:outputText styleClass="outputText"
                            id="lblYosoNo" value="#{pc_Kmb02006.propYosoNo.labelName}"></h:outputText></TH>
                        <TD width="100"><h:outputText styleClass="outputText"
                            id="htmlYosoNo" value="#{pc_Kmb02006.propYosoNo.integerValue}"><f:convertNumber /></h:outputText></TD>
                        <TH class="v_e" width="150"><h:outputText styleClass="outputText"
                            id="lblYosoComment"
                            value="#{pc_Kmb02006.propYosoComment.labelName}"></h:outputText></TH>
                        <TD width="416"><h:outputText styleClass="outputText"
                            id="htmlYosoComment" value="#{pc_Kmb02006.propYosoComment.stringValue}"></h:outputText></TD>
                    </TR>
                </TBODY>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE border="0" cellpadding="0" cellspacing="0" width="820">
                <TBODY>
                    <TR>
                        <TD width="600" align="left"><h:outputText styleClass="outputText"
                            id="lblKamkuHaitouDataTableTitle" value="科目配当情報"></h:outputText></TD>
                        <TD width="220" align="right">
                        	<h:outputFormat styleClass="outputFormat" id="lblHaitouKamokuListTotal" value="{0}科目">
                        	<f:param name="haitouKamokuListTotal" value="#{pc_Kmb02006.propHaitouKamokuList.listCount}"></f:param>
							</h:outputFormat>
						</TD>
                    </TR>
				</TBODY>
			</TABLE>
			<TABLE class="meisai_scroll" border="0" cellpadding="0" cellspacing="0" width="820">
				<TBODY>
				<TR>
					<TH class="headerClass" width="30" scope="col">
					</TH>
					<TH class="headerClass" width="200" scope="col">
						<span id="lblHaitouKamokuCodeColumn" class="outputText">コード
						</span>
					</TH>
					<TH class="headerClass" width="470" scope="col">
						<span id="lblHaitouKamokuNameColumn" class="outputText">名称
						</span>
					</TH>
					<TH class="headerClass" width="100" scope="col">
						<span id="lblHaitouKamokuTaniSuColumn" class="outputText">単位
						</span>
					</TH>
					<TH class="headerClass" width="20" scope="col">
					</TH>
				</TR>
				</TBODY>
			</TABLE>
			<TABLE  border="0" cellpadding="0" cellspacing="0" width="800">
				<TBODY>
                    <TR>
                        <TD colspan="2">
						<DIV style="height:106px" class="listScroll"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							columnClasses="columnClass1" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Kmb02006.propHaitouKamokuList.rowClasses}"
							styleClass="meisai_scroll" id="htmlHaitouKamokuList"
							value="#{pc_Kmb02006.propHaitouKamokuList.list}" var="varlist"
							width="800">
							<h:column id="column1">
								<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
									id="htmlHaitouKamokuCheck" value="#{varlist.selected}"
									tabindex="1"></h:selectBooleanCheckbox>
								<f:attribute value="30" name="width" />
							</h:column>
							<h:column id="column2">
								<h:outputText styleClass="outputText" id="htmlHaitouKamokuCode"
									value="#{varlist.kamokuCode}"></h:outputText>
								<f:attribute value="200" name="width" />
							</h:column>
							<h:column id="column3">
								<h:outputText styleClass="outputText" id="htmlHaitouKamokuName"
									value="#{varlist.kamokuName}"></h:outputText>
								<f:attribute value="470" name="width" />
							</h:column>
							<h:column id="column4">
								<h:outputText styleClass="outputText"
									id="htmlHaitouKamokuTaniSu" value="#{varlist.taniSu}">
									<f:convertNumber pattern="0.00" />
								</h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align:right" name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
                    </TR>
                    <TR>
                    	<TD align="left">
                        <TABLE border="0" cellpadding="0" cellspacing="0">
                            <TBODY>
                                <TR>
                                    <TD>
                                    <input type="button" class="check"
										id="checkButton1" onclick="return func_1(this, event);"
										tabindex="2"> <input type="button" class="uncheck"
										id="uncheckButton1" onclick="return func_2(this, event);"
										tabindex="3">
                                    </TD>
                                    <TD><hx:commandExButton
										type="submit" value="追加" styleClass="commandExButton"
										id="addHaitouKamoku" tabindex="4"
										action="#{pc_Kmb02006.doAddHaitouKamokuAction}"></hx:commandExButton>
                                    </TD>
                                </TR>
                            </TBODY>
                        </TABLE>
                    	</TD>
                    </TR>
                </TBODY>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="820">
                <TBODY>
                    <TR>
                        <TH class="v_a" width="150"><h:outputText styleClass="outputText"
                            id="lblKamokuCode" value="#{pc_Kmb02006.propKamokuCode.labelName}"></h:outputText></TH>
                        <TD width="670">
                            <h:inputText id="htmlKamokuCode"
							styleClass="inputText"
							readonly="#{pc_Kmb02006.propKamokuCode.readonly}"
							style="#{pc_Kmb02006.propKamokuCode.style}"
							value="#{pc_Kmb02006.propKamokuCode.stringValue}"
							disabled="#{pc_Kmb02006.propKamokuCode.disabled}" size="10"
							tabindex="5" onblur="return doKmkHaiAjax(this, event);"
							maxlength="#{pc_Kmb02006.propKamokuCode.maxLength}"></h:inputText>
                            <hx:commandExButton type="button"
							styleClass="commandExButton_search" id="button2" tabindex="6"
							onclick="return openKmkHaiSearchWindow();"></hx:commandExButton>
                            <hx:commandExButton type="submit" value="追加"
							styleClass="commandExButton" id="addKamoku" tabindex="7"
							action="#{pc_Kmb02006.doAddKamokuAction}"></hx:commandExButton>
							<h:outputText
							styleClass="outputText" id="htmlKamokuName"
							value="#{pc_Kmb02006.propKamokuName.stringValue}"></h:outputText></TD>
                    </TR>
                </TBODY>
            </TABLE>
            <BR>
            <TABLE border="0" cellpadding="0" cellspacing="0" width="820">
                <TBODY>
                    <TR>
                        <TD width="600" align="left"><h:outputText styleClass="outputText"
                            id="lblYosoKamokuDataTableTitle" value="要素科目"></h:outputText></TD>
                        <TD width="220" align="right">
                            <h:outputText styleClass="outputText"
                            id="htmlYosoKamokuTaniTotal" value="#{pc_Kmb02006.propYosoKamokuTaniTotal.stringValue}"></h:outputText>
                        	<h:outputFormat styleClass="outputFormat" id="lblYosoKamokuListTotal" value="{0}科目">
                        	<f:param name="yosoKamokuListTotal" value="#{pc_Kmb02006.propYosoKamokuList.listCount}"></f:param>
							</h:outputFormat>
                    </TR>
 				</TBODY>
			</TABLE>
			<TABLE class="meisai_scroll" border="0" cellpadding="0" cellspacing="0" width="820">
				<TBODY>
				<TR>
					<TH class="headerClass" width="30" scope="col">
					</TH>
					<TH class="headerClass" width="200" scope="col">
						<span id="lblYosoKamokuCodeColumn" class="outputText">コード
						</span>
					</TH>
					<TH class="headerClass" width="470" scope="col">
						<span id="lblYosoKamokuNameColumn" class="outputText">名称
						</span>
					</TH>
					<TH class="headerClass" width="100" scope="col">
						<span id="lblYosoKamokuTaniSuColumn" class="outputText">単位
						</span>
					</TH>
					<TH class="headerClass" width="20" scope="col">
					</TH>
				</TR>
				</TBODY>
			</TABLE>
			<TABLE  border="0" cellpadding="0" cellspacing="0" width="800">
				<TBODY>
                   <TR>
                        <TD colspan="2">
						<DIV style="height:106px" class="listScroll"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							columnClasses="columnClass1" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Kmb02006.propYosoKamokuList.rowClasses}"
							styleClass="meisai_scroll" id="htmlYosoKamokuList"
							value="#{pc_Kmb02006.propYosoKamokuList.list}" var="varlist"
							width="800">
							<h:column id="column5">
								<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
									id="htmlYosoKamokuCheck" value="#{varlist.selected}"
									tabindex="8"></h:selectBooleanCheckbox>
								<f:attribute value="30" name="width" />
							</h:column>
							<h:column id="column6">
								<h:outputText styleClass="outputText" id="htmlYosoKamokuCode"
									value="#{varlist.kamokuCode}"></h:outputText>
								<f:attribute value="200" name="width" />
							</h:column>
							<h:column id="column7">
								<h:outputText styleClass="outputText" id="htmlYosoKamokuName"
									value="#{varlist.kamokuName}"></h:outputText>
								<f:attribute value="470" name="width" />
							</h:column>
							<h:column id="column8">
								<h:outputText styleClass="outputText" id="htmlYosoKamokuTaniSu"
									value="#{varlist.taniSu}">
									<f:convertNumber pattern="0.00" />
								</h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align:right" name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
                    </TR>
                    <TR>
                    	<TD align="left">
                                <TABLE border="0" cellpadding="0" cellspacing="0">
                                    <TBODY>
                                        <TR>
                                            <TD>
                                                <input type="button"
										class="check" id="checkButton2"
										onclick="return func_3(this, event);" tabindex="9">
                                                <input type="button"
										class="uncheck" id="uncheckButton2"
										onclick="return func_4(this, event);" tabindex="10">
                                            </TD>
                                            <TD>
                                                <hx:commandExButton
										type="submit" value="除外" styleClass="commandExButton"
										id="exceptYosoKamoku" tabindex="11"
										action="#{pc_Kmb02006.doExceptYosoKamokuAction}"></hx:commandExButton>
                                            </TD>
                                        </TR>
                                    </TBODY>
                                </TABLE>
                    	</TD>
                    </TR>
                </TBODY>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
                width="800">
                <TBODY>
                    <TR>
                    	<% 
                    		// 画面情報Beanの取得

    						Kmb02000Bean infoBean 
    						= (Kmb02000Bean)UtilSystem.getManagedBean(Kmb02000Bean.class);
							// 要素追加の場合

                    		if (infoBean.getYosoProcKbn() == Kmb02000Bean.YOSO_PROC_ADD) { 
                   		%>
                        <TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="decision" tabindex="12"
							confirm="#{msg.SY_MSG_0002W}"
							action="#{pc_Kmb02006.doDecisionAction}"></hx:commandExButton>
							<hx:commandExButton type="submit" value="確定"
							style="display:none;" id="decision3"
							action="#{pc_Kmb02006.doDecisionAction}"></hx:commandExButton>
                        </TD>
                        <% } else { %>
                        <TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="decision2" tabindex="13"
							confirm="#{msg.SY_MSG_0003W}"
							action="#{pc_Kmb02006.doDecision2Action}"></hx:commandExButton>
                        </TD>
                        <% } %>
                    </TR>
                </TBODY>
            </TABLE>
            </DIV>
    </DIV>
    <!--↑content↑-->
    </DIV>
    <!--↑outer↑-->
			<h:inputHidden
				value="#{pc_Kmb02006.propExecutableDecision.integerValue}"
				id="propExecutableDecision">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="htmlMeisaiNo"></h:inputHidden>
		</h:form>
    <!-- フッターインクルード -->
    <jsp:include page ="../inc/footer.jsp" />
    </hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
