<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmd01301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmd01301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmd01301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmd01301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmd01301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmd01301.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"></TD>
				</TR>
			</TABLE>
			<!-- ↑ここに戻るボタンを配置 --></DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="700" valign="top">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText 
												styleClass="outputText" id="lblKaikoNendo"
												value="#{pc_Kmd01301.propKaikoNendo.labelName}"
												style="#{pc_Kmd01301.propKaikoNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:inputText 
												id="htmlKaikoNendo" styleClass="inputText" 
												size="4" tabindex="1"  
												value="#{pc_Kmd01301.propKaikoNendo.dateValue}"
												readonly="#{pc_Kmd01301.propKaikoNendo.readonly}"
												disabled="#{pc_Kmd01301.propKaikoNendo.disabled}"
												style="#{pc_Kmd01301.propKaikoNendo.style}"
												maxlength="#{pc_Kmd01301.propKaikoNendo.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_b" width="150">
											<h:outputText
												styleClass="outputText" id="lblKaikoGakkiNo"
												style="#{pc_Kmd01301.propKaikoGakkiNo.labelStyle}"
												value="#{pc_Kmd01301.propKaikoGakkiNo.labelName}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:inputText 
												id="htmlKaikoGakkiNo" styleClass="inputText" 
												size="2" tabindex="2" 
												value="#{pc_Kmd01301.propKaikoGakkiNo.integerValue}"
												readonly="#{pc_Kmd01301.propKaikoGakkiNo.readonly}"
												disabled="#{pc_Kmd01301.propKaikoGakkiNo.disabled}"
												style="#{pc_Kmd01301.propKaikoGakkiNo.style}"
												maxlength="#{pc_Kmd01301.propKaikoGakkiNo.maxLength}">
												<f:convertNumber type="number" pattern="#0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<BR>
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_c" width="150">
											<h:outputText
												styleClass="outputText" id="lblKanriBusyo" 
												value="#{pc_Kmd01301.propKanriBusyo.labelName}" 
												style="#{pc_Kmd01301.propKanriBusyo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlKanriBusyo" tabindex="3"
										value="#{pc_Kmd01301.propKanriBusyo.value}"
										readonly="#{pc_Kmd01301.propKanriBusyo.readonly}"
										disabled="#{pc_Kmd01301.propKanriBusyo.disabled}"
										style="width:410px">
										<f:selectItems value="#{pc_Kmd01301.propKanriBusyo.list}" />
									</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="150">
											<h:outputText 
												styleClass="outputText" id="lblCurriculum"
												value="#{pc_Kmd01301.propCurriculum.labelName}" 
												style="#{pc_Kmd01301.propCurriculum.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlCurriculum" tabindex="4"
										value="#{pc_Kmd01301.propCurriculum.value}"
										readonly="#{pc_Kmd01301.propCurriculum.readonly}"
										disabled="#{pc_Kmd01301.propCurriculum.disabled}"
										style="width:540px">
										<f:selectItems value="#{pc_Kmd01301.propCurriculum.list}" />
									</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="150">
											<h:outputText 
												styleClass="outputText" id="lblSikenKbn"
												value="#{pc_Kmd01301.propSikenKbn.labelName}"
												style="#{pc_Kmd01301.propSikenKbn.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:selectManyCheckbox
												disabledClass="selectManyCheckbox_Disabled"
												styleClass="selectManyCheckbox" 
												id="htmlSikenKbn" layout="pageDirection" tabindex="5" 
												value="#{pc_Kmd01301.propSikenKbn.value}" 
												readonly="#{pc_Kmd01301.propSikenKbn.readonly}" 
												disabled="#{pc_Kmd01301.propSikenKbn.disabled}" 
												style="#{pc_Kmd01301.propSikenKbn.style}">
												<f:selectItems value="#{pc_Kmd01301.propSikenKbn.list}" />
											</h:selectManyCheckbox>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="150">
											<h:outputText styleClass="outputText" 
												id="lblSikenJissibi" value="試験実施日">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:inputText styleClass="inputText" id="htmlSikenJissibiFrom"
										size="12" tabindex="6"
										value="#{pc_Kmd01301.propSikenJissibiFrom.dateValue}"
										readonly="#{pc_Kmd01301.propSikenJissibiFrom.readonly}"
										disabled="#{pc_Kmd01301.propSikenJissibiFrom.disabled}"
										style="#{pc_Kmd01301.propSikenJissibiFrom.style}">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText>
											～

											<h:inputText styleClass="inputText" id="htmlSikenJissibiTo"
										size="12" tabindex="7"
										value="#{pc_Kmd01301.propSikenJissibiTo.dateValue}"
										readonly="#{pc_Kmd01301.propSikenJissibiTo.readonly}"
										disabled="#{pc_Kmd01301.propSikenJissibiTo.disabled}"
										style="#{pc_Kmd01301.propSikenJissibiTo.style}">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<BR>
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_d" width="150">
											<h:outputText
												styleClass="outputText" id="lblLayout" 
												value="#{pc_Kmd01301.propLayout.labelName}" 
												style="#{pc_Kmd01301.propLayout.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlLayout"
												layout="pageDirection" tabindex="8" 
												value="#{pc_Kmd01301.propLayout.value}" 
												readonly="#{pc_Kmd01301.propLayout.readonly}" 
												disabled="#{pc_Kmd01301.propLayout.disabled}" 
												style="#{pc_Kmd01301.propLayout.style}">
												<f:selectItems value="#{pc_Kmd01301.propLayout.list}" />
											</h:selectOneRadio>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<BR>
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_e" width="150">
											<h:outputText
												styleClass="outputText" id="lblPdfTitle"
												value="#{pc_Kmd01301.propPdfTitle.labelName}"
												style="#{pc_Kmd01301.propPdfTitle.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="550">
											<h:inputText 
												id="htmlPdfTitle" styleClass="inputText" 
												size="60" tabindex="9" 
												value="#{pc_Kmd01301.propPdfTitle.stringValue}" 
												readonly="#{pc_Kmd01301.propPdfTitle.readonly}" 
												disabled="#{pc_Kmd01301.propPdfTitle.disabled}" 
												style="#{pc_Kmd01301.propPdfTitle.style}" 
												maxlength="#{pc_Kmd01301.propPdfTitle.maxLength}">
											</h:inputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<BR>
							<TABLE border="0" cellspacing="0" class="button_bar" width="100%">
								<TBODY>
									<TR>
										<TD width="" nowrap>
											<hx:commandExButton type="submit" value="PDF作成"
												styleClass="commandExButton_out" id="pdfOut"
												confirm="#{msg.SY_MSG_0019W}" tabindex="10"
												action="#{pc_Kmd01301.doPdfOutAction}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="EXCEL作成"
												styleClass="commandExButton_out" id="excelOut"
												confirm="#{msg.SY_MSG_0027W}" tabindex="11"
												action="#{pc_Kmd01301.doExcelOutAction}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="CSV作成"
												styleClass="commandExButton_out" id="csvOut"
												confirm="#{msg.SY_MSG_0020W}" tabindex="12"
												action="#{pc_Kmd01301.doCsvOutAction}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="出力項目指定"
												styleClass="commandExButton_out" id="setOutput"
												tabindex="13"
												action="#{pc_Kmd01301.doSetOutputAction}">
											</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			
		</h:form></div>
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
	
</f:view>

</HTML>
