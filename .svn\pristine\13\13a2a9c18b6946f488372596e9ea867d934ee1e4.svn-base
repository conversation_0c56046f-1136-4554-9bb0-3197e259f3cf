<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssd00401T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssd00401T05.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css" />

<SCRIPT type="text/javascript">
function checkOn(thisObj, thisEvent) {
	// 全選択


	check('htmlKgyList','htmlListCheck');
}
function checkOff(thisObj, thisEvent) {
	// 全解除
	uncheck('htmlKgyList','htmlListCheck');
}
function confirmOk() {
    // 確認メッセージでＯＫ押下時、しきい値フラグオンして再検索
	document.getElementById('form1:htmlExecutableSearch').value = "1";	
	indirectClick('search');	
}			
function confirmCancel() {
    // 確認メッセージでキャンセル押下時、しきい値フラグオフ


	document.getElementById('form1:htmlExecutableSearch').value = "0";
	alert('実行を中断しました。');	
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssd00401T05.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">

	<!-- ヘッダーインクルード -->
	<jsp:include page ="../inc/header.jsp" />

	<!-- ヘッダーへのデータセット領域 -->
	<DIV style="display:none;">
		<hx:commandExButton type="submit" value="閉じる"
			styleClass="commandExButton" id="closeDisp"
			action="#{pc_Ssd00401T05.doCloseDispAction}"></hx:commandExButton>
		<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssd00401T05.funcId}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssd00401T05.screenName}"></h:outputText>
	</DIV>

	<!--↓outer↓-->
	<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
		<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			styleClass="outputText" escape="false">
		</h:outputText>
	</FIELDSET>
			
	<!--↓content↓-->
	<DIV class="head_button_area" >　
	</DIV>

	<DIV id="content">
		<DIV class="column">
			<TABLE border="0" class="table" width="850" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblKjnNendo"
							value="#{pc_Ssd00401T01.ssd00401.propKjnNendo.labelName}"
							style="#{pc_Ssd00401T01.ssd00401.propKjnNendo.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:inputText styleClass="inputText" id="htmlKjnNendo"
							value="#{pc_Ssd00401T01.ssd00401.propKjnNendo.value}"
							readonly="#{pc_Ssd00401T01.ssd00401.propKjnNendo.readonly}"
							style="#{pc_Ssd00401T01.ssd00401.propKjnNendo.style}"
							disabled="#{pc_Ssd00401T01.ssd00401.propKjnNendo.disabled}"
							size="4">
							<hx:inputHelperAssist imeMode="inactive"
								errorClass="inputText_Error" promptCharacter="_" />

							<f:convertDateTime pattern="yyyy" />
						</h:inputText><h:outputText
							styleClass="outputText" value="年度"></h:outputText></TD>
					</TR>
					<TR>
						<TH nowrap class="v_c" width="150"><h:outputText styleClass="outputText"
							id="lblAddrType" value="電話番号・所在地"
							style="#{pc_Ssd00401T01.ssd00401.propAddrType.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:selectOneRadio styleClass="selectOneRadio" id="htmlAddrType"
							value="#{pc_Ssd00401T01.ssd00401.propAddrType.value}"
							style="#{pc_Ssd00401T01.ssd00401.propAddrType.style}">
							<f:selectItem itemValue="1" itemLabel="企業所在地" />
							<f:selectItem itemValue="2" itemLabel="書類提出先" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH nowrap class="v_b" width="150"><h:outputText styleClass="outputText"
							id="labelChohyoTitle" 
							value="#{pc_Ssd00401T01.ssd00401.propChohyoTitle.labelName}"
							style="#{pc_Ssd00401T01.ssd00401.propChohyoTitle.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:inputText styleClass="inputText"
							id="htmlChohyoTitle"
							value="#{pc_Ssd00401T01.ssd00401.propChohyoTitle.stringValue}"
							style="#{pc_Ssd00401T01.ssd00401.propChohyoTitle.style}" size="70"
							disabled="#{pc_Ssd00401T01.ssd00401.propChohyoTitle.disabled}"
							maxlength="#{pc_Ssd00401T01.ssd00401.propChohyoTitle.maxLength}"
							readonly="#{pc_Ssd00401T01.ssd00401.propChohyoTitle.readonly}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblGakko"
							value="#{pc_Ssd00401T01.ssd00401.propGakko.labelName}"
							style="#{pc_Ssd00401T01.ssd00401.propGakko.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlGakko"
							disabled="#{pc_Ssd00401T01.ssd00401.propGakko.disabled}"
							style="#{pc_Ssd00401T01.ssd00401.propGakko.style}"
							value="#{pc_Ssd00401T01.ssd00401.propGakko.value}">
							<f:selectItems value="#{pc_Ssd00401T01.ssd00401.propGakko.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE width="850" border="0" cellpadding="0" cellspacing="0" style="margin-top:20px;">
				<TBODY>
					<TR>
						<TD style="text-align:left;">
							<hx:commandExButton 
								type="submit" value="一括指定" styleClass="tab_head_off" 
								id="btnSsd00401T01" action="#{pc_Ssd00401T05.doBtnSsd00401T01Action}" style="width:14%"></hx:commandExButton><hx:commandExButton
								type="submit" value="企業指定" styleClass="tab_head_on"
								id="btnSsd00401T05" style="width:14%">
							</hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TD style="align:left;">
							<TABLE width="100%" border="0" cellspacing="0" cellpadding="0" class = "tab_body">
								<TR>
									<TD style="text-align:left;">
										<DIV style="height:395px">
											<TABLE border="0" cellspacing="0" cellpadding="0">
												<TBODY>
													<TR>
														<TD style="text-align:left;">
															<TABLE width="750" border="0" cellpadding="3" class="table"	cellspacing="0" style="margin-top:20px; margin-left:40px;">
																<TBODY>
																	<TR>
																		<TH class="v_a" width="150"><h:outputText
																			styleClass="outputText" id="lblKgyCd"
																			value="#{pc_Ssd00401T05.propKgyCd.labelName}"
																			style="#{pc_Ssd00401T05.propKgyCd.labelStyle}"></h:outputText></TH>
																		<TD width="15%"><h:inputText styleClass="inputText"
																			id="htmlKgyCd"
																			value="#{pc_Ssd00401T05.propKgyCd.stringValue}" size="18"
																			disabled="#{pc_Ssd00401T05.propKgyCd.disabled}"
																			maxlength="#{pc_Ssd00401T05.propKgyCd.maxLength}"
																			readonly="#{pc_Ssd00401T05.propKgyCd.readonly}"
																			style="#{pc_Ssd00401T05.propKgyCd.style}">
																		</h:inputText></TD>
																	</TR>
																	<TR>
																		<TH class="v_b" width="150"><h:outputText
																			styleClass="outputText" id="lblKgyName"
																			value="#{pc_Ssd00401T05.propKgyName.labelName}"
																			style="#{pc_Ssd00401T05.propKgyName.labelStyle}"></h:outputText></TH>
																		<TD width="30%"><h:inputText styleClass="inputText"
																			id="htmlKgyName"
																			value="#{pc_Ssd00401T05.propKgyName.stringValue}" size="30"
																			disabled="#{pc_Ssd00401T05.propKgyName.disabled}"
																			maxlength="#{pc_Ssd00401T05.propKgyName.maxLength}"
																			readonly="#{pc_Ssd00401T05.propKgyName.readonly}"
																			style="#{pc_Ssd00401T05.propKgyName.style}">
																		</h:inputText></TD>
																		<TH class="v_c" width="150"><h:outputText
																			styleClass="outputText" id="lblKgyNameKana"
																			value="#{pc_Ssd00401T05.propKgyNameKana.labelName}"
																			style="#{pc_Ssd00401T05.propKgyNameKana.labelStyle}"></h:outputText></TH>
																		<TD width="30%"><h:inputText styleClass="inputText"
																			id="htmlKgyNameKana"
																			value="#{pc_Ssd00401T05.propKgyNameKana.stringValue}"
																			disabled="#{pc_Ssd00401T05.propKgyNameKana.disabled}"
																			maxlength="#{pc_Ssd00401T05.propKgyNameKana.maxLength}"
																			readonly="#{pc_Ssd00401T05.propKgyNameKana.readonly}"
																			style="#{pc_Ssd00401T05.propKgyNameKana.style}" size="30">
																		</h:inputText></TD>
																	</TR>
																	<TR>
																		<TH class="v_d" width="150"><h:outputText
																			styleClass="outputText" id="lblKgyNameRyak"
																			value="#{pc_Ssd00401T05.propKgyNameRyak.labelName}"
																			style="#{pc_Ssd00401T05.propKgyNameRyak.labelStyle}"></h:outputText></TH>
																		<TD width="30%"><h:inputText styleClass="inputText"
																			id="htmlKgyNameRyak"
																			value="#{pc_Ssd00401T05.propKgyNameRyak.stringValue}"
																			size="30"
																			disabled="#{pc_Ssd00401T05.propKgyNameRyak.disabled}"
																			maxlength="#{pc_Ssd00401T05.propKgyNameRyak.maxLength}"
																			readonly="#{pc_Ssd00401T05.propKgyNameRyak.readonly}"
																			style="#{pc_Ssd00401T05.propKgyNameRyak.style}">
																		</h:inputText></TD>
																	</TR>
																	<TR>
																		<TH class="v_e" width="150"><h:outputText
																			styleClass="outputText" id="lblGyosyuCd"
																			value="#{pc_Ssd00401T05.propGyosyuCd.labelName}"
																			style="#{pc_Ssd00401T05.propGyosyuCd.labelStyle}"></h:outputText></TH>
																		<TD width="30%"><h:selectOneMenu styleClass="selectOneMenu"
																			id="htmlGyosyuCd"
																			value="#{pc_Ssd00401T05.propGyosyuCd.value}"
																			readonly="#{pc_Ssd00401T05.propGyosyuCd.readonly}"
																			style="#{pc_Ssd00401T05.propGyosyuCd.style}">
																			<f:selectItems value="#{pc_Ssd00401T05.propGyosyuCd.list}" />
																		</h:selectOneMenu></TD>
																		<TH class="v_f" width="20%"><h:outputText
																			styleClass="outputText" id="lblChiikiCd"
																			value="#{pc_Ssd00401T05.propChiikiCd.labelName}"
																			style="#{pc_Ssd00401T05.propChiikiCd.labelStyle}"></h:outputText></TH>
																		<TD width="30%"><h:selectOneMenu styleClass="selectOneMenu"
																			id="htmlChiikiCd"
																			value="#{pc_Ssd00401T05.propChiikiCd.value}"
																			disabled="#{pc_Ssd00401T05.propChiikiCd.disabled}"
																			readonly="#{pc_Ssd00401T05.propChiikiCd.readonly}"
																			style="#{pc_Ssd00401T05.propChiikiCd.style}">
																			<f:selectItems value="#{pc_Ssd00401T05.propChiikiCd.list}" />
																		</h:selectOneMenu></TD>
																	</TR>
																</TBODY>
															</TABLE>
														</TD>
													</TR>
													<TR>
														<TD style="text-align:left;">
														<TABLE width="750" class="button_bar" style="margin-left:40px;">
															<TBODY>
																<TR>
																	<TD width="300"></TD>
																	<TD><hx:commandExButton type="submit" value="検索" id="search"
																		styleClass="commandExButton_dat"
																		action="#{pc_Ssd00401T05.doSearchAction}"></hx:commandExButton>
																	</TD>
																	<TD width="300" style="text-align:right"><h:outputText styleClass="note"
																		id="lblSearchCmt" value="　※部分一致　企業コード除く"></h:outputText></TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
													<TR>
													</TR>
													<TR>
														<TD>
															<TABLE width="750" border="0" cellspacing="3" cellpadding="0" style="margin-left:40px;">
																<TBODY>
																	<TR>
																		<TD style="text-align:right;"><h:outputText
																			styleClass="outputText" id="htmlUserListCnt"
																			value="#{pc_Ssd00401T05.propKgyList.listCount}">
																			<f:convertNumber pattern="#,##0" />
																		</h:outputText>
																		<h:outputText styleClass="outputText" id="lblKensuu"
																			value="件"></h:outputText></TD>
																	</TR>
																	<TR>
																		<TD style="text-align:center;">
																		<DIV id="listScroll" class="listScroll" onscroll="setScrollPosition('scroll',this);"
																			style="height:170px;"><h:dataTable border="0"
																			cellpadding="3" cellspacing="0"
																			headerClass="headerClass"
																			footerClass="footerClass"
																			rowClasses="#{pc_Ssd00401T05.propKgyList.rowClasses}"
																			styleClass="meisai_scroll" id="htmlKgyList" width="750"
																			value="#{pc_Ssd00401T05.propKgyList.list}" var="varlist">
																			<h:column id="column5">
																				<f:facet name="header"></f:facet>
																				<f:attribute value="15" name="width" />
																					<h:selectBooleanCheckbox
																						styleClass="selectBooleanCheckbox" id="htmlListCheck"
																						value="#{varlist.selected}"
																						rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
																					</h:column>
																			<h:column id="column1">
																				<f:facet name="header">
																					<h:outputText id="lblListKgyCd" styleClass="outputText"
																						value="企業コード"></h:outputText>
																				</f:facet>
																				<f:attribute value="100" name="width" />
																				<f:attribute value="text-align:left" name="style" />
																				<h:outputText styleClass="outputText" id="htmlListKgyCd"
																					value="#{varlist.kgyCd}" rendered="#{varlist.rendered}"></h:outputText>
																			</h:column>
																			<h:column id="column2">
																				<f:facet name="header">
																					<h:outputText styleClass="outputText" value="企業名称"
																						id="lblListKgyName"></h:outputText>
																				</f:facet>
																				<f:attribute value="200" name="width" />
																				<h:outputText styleClass="outputText" id="htmlListKgyName"
																					value="#{varlist.kgyName.displayValue}"
																					rendered="#{varlist.rendered}"
																					title="#{varlist.kgyName.value}"></h:outputText>
																			</h:column>
																			<h:column id="column3">
																				<f:facet name="header">
																					<h:outputText styleClass="outputText" value="企業カナ名称"
																						id="lblListKgyNameKana"></h:outputText>
																				</f:facet>
																				<f:attribute value="245" name="width" />
																				<h:outputText styleClass="outputText"
																					id="htmlListKgyNameKana"
																					value="#{varlist.kgyNameKana.displayValue}"
																					title="#{varlist.kgyNameKana.value}"
																					rendered="#{varlist.rendered}"></h:outputText>
																			</h:column>
																			<h:column id="column6">
																				<f:facet name="header">
																					<h:outputText styleClass="outputText" value="業種コード"
																						id="lblListGyosyuCd"></h:outputText>
																				</f:facet>
																				<f:attribute value="100" name="width" />
																				<f:attribute value="text-align:left" name="style" />
																				<h:outputText styleClass="outputText"
																					id="htmlListGyogyuCd"
																					value="#{varlist.gyosyuCd.displayValue}"
																					title="#{varlist.gyosyuCd.value}"
																					rendered="#{varlist.rendered}"></h:outputText>
																			</h:column>
																			<h:column id="column4">
																				<f:facet name="header">
																					<h:outputText styleClass="outputText" value="主業種"
																						id="lblListGyosyuNameRyak"></h:outputText>
																				</f:facet>
																				<f:attribute value="140" name="width" />
																				<h:outputText styleClass="outputText"
																					id="htmlListGyogyuNameRyak"
																					value="#{varlist.gyosyuNameRyak.displayValue}"
																					title="#{varlist.gyosyuNameRyak.value}"
																					rendered="#{varlist.rendered}"></h:outputText>
																			</h:column>
																		</h:dataTable></DIV>
																		<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
																			<TBODY>
																				<TR>
																					<TD style="text-align:left">
																						<hx:jspPanel id="jspPanel1">
																						<hx:commandExButton type="submit" value="全選択"
																							styleClass="check" id="check"
																							onclick="return checkOn(this, event);"></hx:commandExButton>
																						<hx:commandExButton type="submit" value="全解除"
																							styleClass="uncheck" id="uncheck"
																							onclick="return checkOff(this, event);"></hx:commandExButton>
																						</hx:jspPanel>
																					</TD>
																				</TR>
																			</TBODY>
																		</TABLE>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</DIV>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="100%" class="button_bar">
								<TBODY>
									<TR>
										<TD><hx:commandExButton type="submit" value="PDF作成"
											styleClass="commandExButton_out" id="pdfout"
											action="#{pc_Ssd00401T05.doPdfoutAction}"
											confirm="#{msg.SY_MSG_0019W}"
											></hx:commandExButton><hx:commandExButton type="submit" value="CSV作成"
											styleClass="commandExButton_out" id="csvout"
											action="#{pc_Ssd00401T05.doCsvoutAction}"
											confirm="#{msg.SY_MSG_0020W}"
											></hx:commandExButton><hx:commandExButton type="submit" value="出力項目指定"
											styleClass="commandExButton_out" id="setoutput"
											action="#{pc_Ssd00401T05.doSetoutputAction}"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
		</DIV>
	</DIV>
	<!--↑outer↑-->
	<!-- フッダーインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
</DIV>
	<h:inputHidden value="#{pc_Ssd00401T05.propExecutableSearch.integerValue}" id="htmlExecutableSearch">
		<f:convertNumber />
	</h:inputHidden>
	<h:inputHidden value="#{pc_Ssd00401T05.propKgyList.scrollPosition}" id="scroll"></h:inputHidden>
	</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
	</f:view>
	<SCRIPT LANGUAGE="JavaScript">
		window.attachEvent('onload', endload);
		function endload() {
			changeScrollPosition('scroll','listScroll');
		}
	</SCRIPT>
</HTML>
