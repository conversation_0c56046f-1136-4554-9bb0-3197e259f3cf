<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb20101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>光熱費検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css" >

<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function func_ToriKihonAJAX(thisObj, thisEvent) {
	// 取引先略称（Ajax）を取得する
	var servlet = "rev/co/CogToriKihonAJAX";
	var target = "form1:htmlKonetsuhiToriSakiName";
	var code = new Array();
	code['code1'] = thisObj.value;
	code['code2'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function loadFunc() {
	func_ToriKihonAJAX(document.getElementById('form1:htmlKonetsuhiToriSakiCd'), '');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="return loadFunc();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb20101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる"
					styleClass="commandExButton" 
					id="closeDisp"
					action="#{pc_Keb20101.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb20101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb20101.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" 
					value="新規登録" 
					styleClass="commandExButton" 
					id="register" 
					action="#{pc_Keb20101.doRegisterAction}">
</hx:commandExButton>
　　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="900" 
		   class="table">
		<TBODY>
			<TR>
				<TH class="v_a" width="160">
					<h:outputText styleClass="outputText" 
								  id="lblKaikeiNendo" 
								  style="#{pc_Keb20101.propKaikeiNendo.labelStyle}" 
								  value="#{pc_Keb20101.propKaikeiNendo.labelName}">
					</h:outputText>
				</TH>
				<TD width="740">
					<h:inputText styleClass="inputText" 
								 id="htmlKaikeiNendo" 
								 maxlength="#{pc_Keb20101.propKaikeiNendo.maxLength}" 
								 value="#{pc_Keb20101.propKaikeiNendo.dateValue}" 
								 style="#{pc_Keb20101.propKaikeiNendo.style}" 
								 size="4">
						<f:convertDateTime pattern="yyyy" />
						<hx:inputHelperAssist errorClass="inputText_Error" 
											  promptCharacter="_" />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_b">
					<h:outputText styleClass="outputText" 
									  id="lblKonetsuhiKbn" 
								  style="#{pc_Keb20101.propKonetsuhiKbn.labelStyle}" 
								  value="#{pc_Keb20101.propKonetsuhiKbn.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:selectOneMenu styleClass="selectOneMenu" 
									 id="htmlKonetsuhiKbn" 
									 value="#{pc_Keb20101.propKonetsuhiKbn.value}" 
									 style="#{pc_Keb20101.propKonetsuhiKbn.style}">
						<f:selectItems value="#{pc_Keb20101.propKonetsuhiKbn.list}" />
					</h:selectOneMenu>
				</TD>
			</TR>
			<TR>
				<TH class="v_c">
					<h:outputText styleClass="outputText" 
								  id="lblKonetsuhiToriSakiCd" 
								  style="#{pc_Keb20101.propKonetsuhiToriSakiCd.labelStyle}" 
								  value="光熱費取引先(半20)">
					</h:outputText>
				</TH>
				<TD width="740px" nowrap>
					<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
						<h:inputText styleClass="inputText" 
									 id="htmlKonetsuhiToriSakiCd" 
									 maxlength="#{pc_Keb20101.propKonetsuhiToriSakiCd.maxLength}" 
									 value="#{pc_Keb20101.propKonetsuhiToriSakiCd.stringValue}" 
									 style="#{pc_Keb20101.propKonetsuhiToriSakiCd.style}" 
									 size="20" 
									 onblur="return func_ToriKihonAJAX(this, event);">
						</h:inputText><hx:commandExButton type="submit" 
											value="実行" 
											styleClass="commandExButton_search" 
											id="searchKonetsuhiToriSakiCd" 
											action="#{pc_Keb20101.doToriSakiSearchAction}">
						</hx:commandExButton>
						<h:outputText styleClass="outputText" 
									  id="htmlKonetsuhiToriSakiName"
									  style="white-space:nowrap;">
						</h:outputText>
					</DIV>
				</TD>
			</TR>
			<TR>
				<TH class="v_d">
					<h:outputText styleClass="outputText" 
								  id="lblKonetsuhiTName" 
								  value="#{pc_Keb20101.propKonetsuhiTName.labelName}" 
								  style="#{pc_Keb20101.propKonetsuhiTName.labelStyle}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText styleClass="inputText" 
								 id="htmlKonetsuhiTName" 
								 size="70" 
								 value="#{pc_Keb20101.propKonetsuhiTName.stringValue}" 
								 style="#{pc_Keb20101.propKonetsuhiTName.style}" 
								 maxlength="#{pc_Keb20101.propKonetsuhiTName.maxLength}">
					</h:inputText>&#160;&#160;(部分一致)
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<BR>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="900" 
		   style="margin-top:0px" 
		   class="button_bar">
		<TBODY>
			<TR>
				<TD>
					<hx:commandExButton type="submit" 
										value="検索" 
										styleClass="commandExButton_dat" 
										id="search" 
										action="#{pc_Keb20101.doSearchAction}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="CSV作成" 
										styleClass="commandExButton_out" 
										id="csvout" 
										action="#{pc_Keb20101.doCsvoutAction}" 
										confirm="#{msg.SY_MSG_0020W}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="出力項目指定" 
										styleClass="commandExButton_out" 
										id="setoutput" 
										action="#{pc_Keb20101.doSetoutputAction}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="クリア" 
										styleClass="commandExButton_etc" 
										id="clear" 
										action="#{pc_Keb20101.doClearAction}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   width="900" 
		   style="">
		<TBODY>
			<TR>
				<TD align="right" width="900">
					<h:outputText styleClass="outputText" 
								  id="htmlListCount" 
								  value="#{pc_Keb20101.propKebKonetList.listCount}件" 
								  style="font-size: 8pt">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TD width="900">
					<DIV id="listScroll" 
						 onscroll="setScrollPosition('scroll',this);" 
						 class="listScroll" 
						 style="height:379px">
						<h:dataTable border="0" 
									 cellpadding="2" 
									 cellspacing="0" 
									 headerClass="headerClass" 
									 footerClass="footerClass" 
									 rowClasses="#{pc_Keb20101.propKebKonetList.rowClasses}" 
									 id="htmlKebKonetList" 
									 width="880" 
									 value="#{pc_Keb20101.propKebKonetList.list}" 
									 var="varlist" 
									 styleClass="meisai_scroll" 
									 style="#{pc_Keb20101.propKebKonetList.style}; table-layout: fixed;" 
									 first="#{pc_Keb20101.propKebKonetList.first}" 
									 rows="#{pc_Keb20101.propKebKonetList.rows}">
							<h:column id="column1">
								<f:facet name="header">
									<hx:jspPanel>
										<TABLE border="0" cellpadding="0" cellspacing="0" height="40">
											<TBODY>
												<TR>
													<TD style="border-top-style:none;border-bottom-style:none;border-left-style:none;border-right-style:none">
														<div align="center">
															<h:outputText styleClass="outputText" 
																		  id="lblListKonetsuhiKbn" 
																		  value="#{pc_Keb20101.propKonetsuhiKbn.name}">
															</h:outputText>
														</div>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</hx:jspPanel>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  value="#{varlist.konetsuhiKbnName}" 
											  title="#{varlist.konetsuhiKbnName}" 
											  id="htmlListKonetsuhiKbn" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="50" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<hx:jspPanel>
										<TABLE border="0" cellpadding="0" cellspacing="0" height="40">
											<TBODY>
												<TR>
													<TD style="border-top-style:none;border-bottom-style:none;border-left-style:none;border-right-style:none">
														<div align="center">
															<h:outputText styleClass="outputText" 
																		  id="lblListToriSakiName" 
																		  value="光熱費取引先">
															</h:outputText>
														</div>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</hx:jspPanel>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  value="#{varlist.toriSakiName}" 
											  title="#{varlist.toriSakiName}" 
											  id="htmlListToriSakiName" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="150" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<hx:jspPanel>
										<TABLE border="0" cellpadding="0" cellspacing="0" height="40">
											<TBODY>
												<TR>
													<TD style="border-top-style:none;border-bottom-style:none;border-left-style:none;border-right-style:none">
														<div align="center">
															<h:outputText styleClass="outputText" 
																		  id="lblListKonetsuhiTNo" 
																		  value="光熱費単位番号">
															</h:outputText>
														</div>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</hx:jspPanel>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  value="#{varlist.konetsuhiTNo}" 
											  title="#{varlist.konetsuhiTNo}" 
											  id="htmlListKonetsuhiTNo" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="150" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<hx:jspPanel>
										<TABLE border="0" cellpadding="0" cellspacing="0" height="40">
											<TBODY>
												<TR>
													<TD style="border-top-style:none;border-bottom-style:none;border-left-style:none;border-right-style:none">
														<div align="center">
															<h:outputText styleClass="outputText" 
																		  id="lblListKonetsuhiTName" 
																		  value="#{pc_Keb20101.propKonetsuhiTName.name}">
															</h:outputText>
														</div>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</hx:jspPanel>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  value="#{varlist.konetsuhiTName}" 
											  title="#{varlist.konetsuhiTName}" 
											  id="htmlListKonetsuhiTName" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="150" name="width" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<hx:jspPanel>
										<TABLE border="0" cellpadding="0" cellspacing="0" height="40">
											<TBODY>
												<TR>
													<TD style="border-top-style:none;border-bottom-style:none;border-left-style:none;border-right-style:none">
														<div align="center">
															<h:outputText styleClass="outputText" 
																		  id="lblListKmkName" 
																		  value="科目">
															</h:outputText>
														</div>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</hx:jspPanel>
								</f:facet>
								<h:outputText styleClass="outputText" 
											  value="#{varlist.kmkName}" 
											  title="#{varlist.kmkName}" 
											  id="htmlListKmkName" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="" name="width" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" 
													styleClass="commandExButton" 
													id="edit" value="編集" 
													action="#{pc_Keb20101.doEditAction}">
								</hx:commandExButton>
								<f:attribute value="40" name="width" />
							</h:column>
							<h:column id="column7">
								<hx:commandExButton type="submit" 
													value="ｺﾋﾟｰ" 
													styleClass="commandExButton" 
													id="copy" 
													action="#{pc_Keb20101.doCopyAction}">
								</hx:commandExButton>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="40" name="width" />
							</h:column>
						</h:dataTable>
					</DIV>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden value="#{pc_Keb20101.propExecutableSearch.integerValue}" 
			   id="htmlExecutableSearch">
</h:inputHidden>
<h:inputHidden value="#{pc_Keb20101.propKebKonetList.scrollPosition}" 
			   id="scroll">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>
