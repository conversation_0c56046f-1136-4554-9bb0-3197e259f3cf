<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_GHP_PAY_K_SAIW" name="過年度納付金再割当" prod_id="GH" description="過年度の学生個人に対する納付金が再割当された場合の納付金情報を管理します。|割当てられた納付金が完納で出学した学生の情報は、過年度の情報に移されます。">
<STATMENT><![CDATA[
GHP_PAY_K_SAIW
]]></STATMENT>
<COLUMN id="SYUTGAK_NENDO" name="出学年度" type="number" length="4" lengthDP="0" byteLength="0" description="学生の出学した年度（西暦）です。志願者の場合は入試年度となります。"/><COLUMN id="SYUTGAK_GAKKI_NO" name="出学学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="学生の出学した学期ＮＯです。志願者の場合は入試学期ＮＯとなります。"/><COLUMN id="NENDO" name="学費年度" type="number" length="4" lengthDP="0" byteLength="0" description="業務実行時の年度（西暦）です。"/><COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生、志願者等を一意に識別する為の番号です。"/><COLUMN id="RE_MOTO_NENDO" name="再割当元＿学費年度" type="number" length="4" lengthDP="0" byteLength="0" description="再割当元納付金の学費年度です。"/><COLUMN id="RE_MOTO_PAY_CD" name="再割当元＿納付金コード" type="string" length="6" lengthDP="0" byteLength="6" description="再割当元納付金の納付金コードです。"/><COLUMN id="RE_MOTO_PATTERN_CD" name="再割当元＿パターンコード" type="string" length="4" lengthDP="0" byteLength="4" description="再割当元納付金の同一納付金コードで複数の納付金の種類を識別するための番号です。"/><COLUMN id="RE_MOTO_BUNNO_CD" name="再割当元＿分納区分コード" type="number" length="2" lengthDP="0" byteLength="0" description="再割当元納付金の分納方法を管理する為のコードです。"/><COLUMN id="RE_WARIATE_HIST_EDANO" name="再割当履歴枝番" type="number" length="2" lengthDP="0" byteLength="0" description="納付金毎の再割当処理に対しての履歴枝番です。|（納付金コード・パターンコード・|分納区分コード毎の連番）"/><COLUMN id="RE_WARIATE_CANCEL_FLG" name="再割当取消フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="再割当を取消したか否かを表します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="RE_SAKI_NENDO" name="再割当先＿学費年度" type="number" length="4" lengthDP="0" byteLength="0" description="再割当先納付金の学費年度です。"/><COLUMN id="RE_SAKI_PAY_CD" name="再割当先＿納付金コード" type="string" length="6" lengthDP="0" byteLength="6" description="再割当先納付金の納付金コードです。"/><COLUMN id="RE_SAKI_PATTERN_CD" name="再割当先＿パターンコード" type="string" length="4" lengthDP="0" byteLength="4" description="再割当先納付金の同一納付金コードで複数の納付金の種類を識別するための番号です。"/><COLUMN id="RE_SAKI_BUNNO_CD" name="再割当先＿分納区分コード" type="number" length="2" lengthDP="0" byteLength="0" description="再割当先納付金の分納方法を管理する為のコードです。"/><COLUMN id="RE_WARIATE_DATE" name="再割当日付" type="date" length="0" lengthDP="0" byteLength="0" description="再割当が発生した日付が設定されます。"/><COLUMN id="IKO_NENDO" name="移行年度" type="number" length="4" lengthDP="0" byteLength="0" description="このデータを作成した年次更新の処理年度が設定されます。"/>
</TABLE>
