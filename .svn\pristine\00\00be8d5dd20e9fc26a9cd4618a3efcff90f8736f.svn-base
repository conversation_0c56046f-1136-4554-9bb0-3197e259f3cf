<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea01002.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.framework.util.UtilLog" %>
<%@ page import="com.jast.gakuen.rev.ke.Kea01002" %>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea01002.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<% UtilLog.debug(Kea01002.class, "JSP Write Start"); %>

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	var btn = document.getElementById('form1:htmlTableList1Row').value;
	indirectClick(btn);
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function func_1(thisObj, thisEvent) {
	document.getElementById('form1:htmlTableList1Row').value = thisObj.name.substr(6);
}
function func_2(thisObj, thisEvent) {
	countTotal();
}

function alChk(thisObj, thisEvent) {
	check('htmlTableList1','htmlCheck');
}
function alUnchk(thisObj, thisEvent) {
	uncheck('htmlTableList1','htmlCheck');
}
function countTotal() { 
	var listCnt = document.getElementById('form1:htmlTableList1').rows.length;
	var sumKingaku = 0;
	for (i = 0; i < listCnt; i++){
		var value1 = FormatNumber.getValue(document.getElementById('form1:htmlTableList1:'+i+':htmlColShinseiGakuYen').value);
		if (isNaN(value1) || value1=='') {
   			kingakuValue=0;
 		} else {
 			kingakuValue=parseInt(value1);
 		}			
   		sumKingaku = sumKingaku + kingakuValue;
  	}
  	document.getElementById('form1:htmlKingakuTotal').value = sumKingaku;
	sumKingaku = sumKingaku + '';
  	var newVar='';
  	for (var i=sumKingaku.length;i>=3;i-=3) {
  		var tmp = sumKingaku.substring(i-3,i);
  		newVar = ','+tmp+newVar;
  	}
  	if(sumKingaku.length%3 == 0) {
  		newVar = newVar.substring(1,newVar.length);
  	} else {
  		newVar = sumKingaku.substring(0,sumKingaku.length%3) + newVar;
  	}
  	document.getElementById('form1:htmlKingakuTotal').innerHTML=newVar;
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_2(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea01002.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea01002.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea01002.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea01002.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="新規登録"
				styleClass="commandExButton" id="create"
				action="#{pc_Kea01002.doCreateAction}"
				rendered="#{pc_Kea01002.propCreateButton.rendered}"></hx:commandExButton><hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returndisp" action="#{pc_Kea01002.doReturndispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="926" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="table" width="890">
							<TBODY>
								<TR>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Kea01002.propKaikeiNendoSyoriKbn.name}"
										style="#{pc_Kea01002.propKaikeiNendoSyoriKbn.style}"></h:outputText></TH>
									<TD width="280"><h:outputText
										styleClass="outputText" id="htmlKaikeiNendoSyoriKbn"
										value="#{pc_Kea01002.propKaikeiNendoSyoriKbn.stringValue}"
										style="#{pc_Kea01002.propKaikeiNendoSyoriKbn.style}"></h:outputText></TD>
									<TH class="v_b" width="110"><h:outputText
										styleClass="outputText" id="lblSknShohiKbn"
										value="#{pc_Kea01002.propSknShohiKbn.name}"
										style="#{pc_Kea01002.propSknShohiKbn.style}"></h:outputText></TH>
									<TD width="350"><h:outputText styleClass="outputText" id="htmlSknShohiKbn" value="#{pc_Kea01002.propSknShohiKbn.stringValue}" style="#{pc_Kea01002.propSknShohiKbn.style}"></h:outputText></TD>
									
								</TR>
								<TR>
									<TH width="150" class="v_c"><h:outputText
										styleClass="outputText" id="lblYsnT"
										value="#{pc_Kea01002.propYsnTCd.name}"
										style="#{pc_Kea01002.propYsnTCd.style}"></h:outputText></TH>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="280">
										<TBODY>
											<TR>
												<TD class="clear_border" width="70"><h:outputText styleClass="outputText" id="htmlYsnTCd" value="#{pc_Kea01002.propYsnTCd.stringValue}" style="#{pc_Kea01002.propYsnTCd.style}"></h:outputText></TD>
												<TD class="clear_border" nowrap>
												<DIV style="width:208px;white-spce:nowrap;overflow:hidden;display:block;">
													<h:outputText
														styleClass="outputText" id="htmlYsnTName"
														value="#{pc_Kea01002.propYsnTName.stringValue}"
														style="#{pc_Kea01002.propYsnTName.style}"
														title="#{pc_Kea01002.propYsnTName.stringValue}"></h:outputText>
												</DIV></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TH class="v_d" width="110">
										<h:outputText styleClass="outputText"
											id="lblMoku" value="#{pc_Kea01002.propMokuCd.name}"
											style="#{pc_Kea01002.propMokuCd.style}"></h:outputText></TH>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="350">
										<TBODY>
											<TR>
												<TD class="clear_border" width="80">
													<h:outputText 
														styleClass="outputText" id="htmlMokuCd" 
														value="#{pc_Kea01002.propMokuCd.stringValue}" 
														style="#{pc_Kea01002.propMokuCd.style}"></h:outputText></TD>
												<TD class="clear_border" nowrap>
												<DIV style="width:270px;white-spce:nowrap;overflow:hidden;display:block;">
													<h:outputText
														styleClass="outputText" id="htmlMokuName"
														value="#{pc_Kea01002.propMokuName.stringValue}"
														style="#{pc_Kea01002.propMokuName.style}"
														title="#{pc_Kea01002.propMokuName.stringValue}"></h:outputText>
												</DIV></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_e" width="150"><h:outputText styleClass="outputText" id="lblKmk" value="#{pc_Kea01002.propKmkCd.name}" style="#{pc_Kea01002.propKmkCd.style}"></h:outputText></TH><TD colspan="3">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="745">
										<TBODY>
											<TR>
												<TD class="clear_border" width="70">
													<h:outputText 
														styleClass="outputText" id="htmlKmkCd" 
														style="#{pc_Kea01002.propKmkCd.style}" 
														value="#{pc_Kea01002.propKmkCd.stringValue}"></h:outputText></TD>
												<TD class="clear_border" nowrap>
												<DIV style="width:675px;white-spce:nowrap;overflow:hidden;display:block;">
													<h:outputText
														styleClass="outputText"
														id="htmlKmkName"
														value="#{pc_Kea01002.propKmkName.stringValue}"
														style="#{pc_Kea01002.propKmkName.style}"
														title="#{pc_Kea01002.propKmkName.stringValue}"></h:outputText></DIV></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="926">
							<TBODY>
								<TR>
									<TD width="18">　</TD>
									<TD width="790" align="left"></TD>
									<TD width="100" align="right"><h:outputText
										styleClass="outputText" id="htmlListCount" value="#{pc_Kea01002.propTableList1.listCount}">

									</h:outputText><h:outputText
										styleClass="outputText" id="lblKen" value="件"></h:outputText></TD>
									<TD width="18">　</TD>
								</TR>
								<TR>
									<TD width="18">　</TD>
									<TD colspan="3" align="left">
										<TABLE border="1" class="meisai_page" height="20">
											<TR>
												<TH align="center" nowrap width="31"><h:outputText id="lblCheck" styleClass="outputText"
													value="削"></h:outputText></TH>
												<TH align="center" nowrap width="31"><h:outputText id="lblRowNo" styleClass="outputText"
													value="NO"></h:outputText></TH>
												<TH align="center" nowrap width="240"><h:outputText id="lblColMokuName" styleClass="outputText"
													value="目的"></h:outputText></TH>
												<TH align="center" nowrap width="254"><h:outputText id="lblColKmkName" styleClass="outputText"
													value="科目"></h:outputText></TH>
												<TH align="center" nowrap width="80"><h:outputText id="lblColShinseiJotai" styleClass="outputText" 
													value="申請状態"></h:outputText></TH>
												<TH align="center" nowrap width="147">
													<h:outputText
														styleClass="outputText_label"
														value="#{pc_Kea01002.readOnly ? pc_Kea01002.propShinseiGakuLabel.name : pc_Kea01002.propShinseiGakuLabel.labelName}"
														style="#{pc_Kea01002.readOnly ? null : pc_Kea01002.propShinseiGakuLabel.labelStyle}"></h:outputText>
												</TH>
												<TH align="center" width="109" nowrap><h:outputText id="lblColButton" styleClass="outputText"
													value="　"></h:outputText></TH>
											</TR>
										</TABLE>
									</TD></TR>
								<TR>
									<TD width="18">　</TD>
									<TD colspan="3" align="left" width="898">
									<DIV class="listScroll" onscroll="setScrollPosition('scroll', this);" style="height:378px; width:898px"
										id="listScroll">
										<h:dataTable border="0"
											cellpadding="2" cellspacing="0" headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Kea01002.propTableList1.rowClasses}"
											styleClass="meisai_scroll" id="htmlTableList1" var="varlist"
											width="881" value="#{pc_Kea01002.propTableList1.list}">
											
										<h:column id="column10">
											<f:facet name="header"></f:facet>
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
											 id="htmlCheck"
											 value="#{varlist.selected.checked}"
											 disabled="#{varlist.selected.disabled}" >
											</h:selectBooleanCheckbox>
											<f:attribute value="31" name="width" />
										</h:column>
										<h:column id="column0">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlRowNo"
												value="#{varlist.recNo}"></h:outputText>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: right" name="style" />
											<f:attribute value="right" name="align" />
										</h:column>
										<h:column id="column1">
											<f:facet name="header">
											</f:facet>
											<hx:jspPanel id="jspPanel1">
												<DIV style="width:240px;white-space:nowrap;overflow:hidden;display:block;">
													<h:outputText styleClass="outputText" id="htmlColMokuName"
														value="#{varlist.colMokuName.stringValue}"
														title="#{varlist.colMokuName.stringValue}"
														style="white-space:nowrap;"></h:outputText>
												</DIV>
											</hx:jspPanel>	
											<f:attribute value="240" name="width" />
											<f:attribute value="true" name="nowrap" />	
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
											</f:facet>
											<hx:jspPanel id="jspPanel2">
												<DIV style="width:254px;white-space:nowrap;overflow:hidden;display:block;">
													<h:outputText styleClass="outputText" id="htmlColKmkName"
														value="#{varlist.colKmkName.stringValue}"
														title="#{varlist.colKmkName.stringValue}"
														style="white-space:nowrap;"></h:outputText>
												</DIV>
											</hx:jspPanel>	
											<f:attribute value="254" name="width" />
											<f:attribute value="true" name="nowrap" />	
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="80" name="width" />
											<h:outputText styleClass="outputText"
												id="htmlColShinseiJotai"
												value="#{varlist.colShinseiJotai.displayValue}"></h:outputText>
											<f:attribute value="center" name="align" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<h:inputText id="htmlColShinseiGakuYen" size="16"
												disabled="#{varlist.colShinseiGakuYen.disabled}"
												readonly="#{varlist.colShinseiGakuYen.readonly}"
												value="#{varlist.colShinseiGakuYen.stringValue}"
												styleClass="#{varlist.colShinseiGakuYen.style}"
												style="#{varlist.colShinseiGakuYen.style}; padding-right: 3px; text-align: right"
												rendered="#{!pc_Kea01002.readOnly}"
												onblur="return countTotal();">

												<hx:inputHelperAssist errorClass="inputText_Error" />
											</h:inputText>
											<h:outputText styleClass="outputText"
												id="htmlColShinseiGakuYenLbl"
												value="#{varlist.colShinseiGakuYen.longValue}"
												style="padding-right: 3px;"
												rendered="#{pc_Kea01002.readOnly}">
												<f:convertNumber
													pattern="#{pc_Kea01002.propShinseiGakuFormatHidden.stringValue}" />
											</h:outputText>
											<f:attribute value="center" name="align" />
											<f:attribute value="148" name="width" />
											<f:attribute value="#{pc_Kea01002.readOnly ? 'text-align: right' : 'text-align: center'}"
											 name="style" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="明細"
												styleClass="commandExButton" id="meisai"
												action="#{pc_Kea01002.doMeisaiAction}"
												disabled="#{varlist.colMeisaiButton.disabled}"
												onclick="return func_1(this, event);" style="font-size: 9pt; vertical-align: middle; width: 30px"></hx:commandExButton>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="center" name="align" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="詳細"
												styleClass="commandExButton" id="detail"
												action="#{pc_Kea01002.doDetailAction}"
												disabled="#{varlist.colDetailButton.disabled}" style="font-size: 9pt; vertical-align: middle; width: 30px"></hx:commandExButton>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="center" name="align" />
										</h:column>
										<h:column id="column7">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="ｺﾋﾟｰ"
												styleClass="commandExButton" id="copy"
												action="#{pc_Kea01002.doCopyAction}"
												disabled="#{varlist.colCopyButton.disabled}" style="font-size: 9pt; vertical-align: middle; width: 30px"></hx:commandExButton>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="center" name="align" />
										</h:column>
									</h:dataTable></DIV>
									</TD></TR>
									<TR>
										<TD width="18"></TD>
										<TD colspan="2" align="left" nowrap>
											<TABLE border="0" cellpadding="0" cellspacing="0"
													class="button_bar" width="890">
												<TBODY>
													<TR>
														<TD width="250" align="left">
															<hx:panelBox styleClass="panelBox" id="box1">
																<hx:jspPanel id="jspPanel3">
																	<hx:commandExButton type="button" 
																						value="on" 
																						styleClass="check" 
																						id="check1" 
																						onclick="return alChk(this, event);">
																	</hx:commandExButton>
																	<hx:commandExButton type="button" 
																						value="off" 
																						styleClass="uncheck" 
																						id="uncheck1" 
																						onclick="return alUnchk(this, event);">
																	</hx:commandExButton>
																	<hx:commandExButton type="submit" 
																						value="削除" 
																						styleClass="commandExButton" 
																						id="delete" 
																						action="#{pc_Kea01002.doDeleteAction}"
																						confirm="#{msg.SY_MSG_0004W}">
																	</hx:commandExButton>
																</hx:jspPanel>
															</hx:panelBox>
														</TD>
														<TD align="right" width="420" nowrap>
													 		<h:outputText
																styleClass="outputText" id="text1" value="金額計：">
															</h:outputText>
														</TD>
														<TD width="120px" align="right" style="border-bottom:1px solid black;">
															<h:outputText styleClass="outputText"
																	id="htmlKingakuTotal">
															</h:outputText>
														</TD>
														<TD align="right" width="115">
														</TD>
													</TR>
													<TR>
														<TD colspan="3" align="center" nowrap>
															<hx:commandExButton styleClass="commandExButton_dat" type="submit"
															id="exec" tabindex="6"
															value="確定"
															action="#{pc_Kea01002.doExecAction}"
															confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE><BR>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden id="htmlKaikeiNendoAjaxHidden"
				value="#{pc_Kea01002.propKaikeiNendoAjaxHidden.stringValue}">
			</h:inputHidden>
			<h:inputHidden id="htmlExecutableSearch"
				value="#{pc_Kea01002.propExecutableSearch.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="htmlTableList1Row">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kea01002.propTableList1.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea01002.propFormatNumberOption.stringValue}"
				id="htmlFormatNumberOption"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea01002.propShinseiGakuFormatHidden.stringValue}"
				id="htmlFormatWkHidden"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
	changeScrollPosition('scroll', 'listScroll');
</SCRIPT>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
	document.getElementById('form1:htmlKingakuTotal').readOnly=true;
</SCRIPT>
</HTML>
<% UtilLog.debug(Kea01002.class, "JSP Write End"); %>
