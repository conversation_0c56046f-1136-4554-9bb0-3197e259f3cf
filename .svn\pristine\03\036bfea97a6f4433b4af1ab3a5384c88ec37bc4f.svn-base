<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssg00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
	// 確認メッセージでOKが押下された場合の処理


	function confirmOk() {
		var btnId = document.getElementById('form1:linkFlg').value;

		// ボタン押下フラグをセットし、同じ処理が流れないようにする
		var nowBtn = document.getElementById('form1:executable').value;
		document.getElementById('form1:executable').value = eval(nowBtn) + 1;

		if (btnId == 1){
			indirectClick('pdfout');
		}

		if (btnId == 2){
			indirectClick('csvout');
		}

		if (btnId == 3){
			indirectClick('print');
		}
		
		if (btnId == 4){
			indirectClick('excelout');
		}
	}

	// 確認メッセージでキャンセルが押下された場合の処理


	function confirmCancel() {
		// ボタン押下フラグをクリアする
		document.getElementById('form1:executable').value = 0;
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssg00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ページコード定義 -->
			<jsp:useBean id="pc_Ssg00301" scope="session"
				class="com.jast.gakuen.rev.ss.Ssg00301" />

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssg00301.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssg00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssg00301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0">

				<TBODY>
					<TR align="center">
						<TD width="974">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="htmlLblKyujinNendo"
										value="#{pc_Ssg00301.kyujinNendo.labelName}"
										style="#{pc_Ssg00301.kyujinNendo.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:outputText styleClass="outputText"
										id="htmlKyujinNendo"
										value='#{pc_Ssg00301.kyujinNendo.stringValue}'
										style="#{pc_Ssg00301.kyujinNendo.style}">
									</h:outputText><h:outputText styleClass="outputText"
										id="htmlNendoLabel" value='年度'></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="htmlLblSumType"
										value="#{pc_Ssg00301.propSumType.labelName}"
										style="#{pc_Ssg00301.propSumType.labelStyle}"></h:outputText></TH>
									<TD width="400"><input type="radio" id="form1:SumKbn"
										name="SumKbn" value="0"
										<jsp:getProperty name="pc_Ssg00301" property="strNensyo" />>年商規模別
									<DIV style="padding-left:2em;"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSumType"
										value="#{pc_Ssg00301.propSumType.stringValue}"
										disabled="#{pc_Ssg00301.propSumType.disabled}"
										readonly="#{pc_Ssg00301.propSumType.readonly}"
										style="#{pc_Ssg00301.propSumType.style}">
										<f:selectItems value="#{pc_Ssg00301.propSumType.list}" />
									</h:selectOneRadio></DIV>
									<input type="radio" id="form1:SumKbn" name="SumKbn" value="1"
										<jsp:getProperty name="pc_Ssg00301" property="strChiki" />>地域コード・年商規模別

									</TD>
								</TR>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="htmlLblGakkaSosikiLv"
										value="#{pc_Ssg00301.propGakkaSosikiLv.labelName}"
										style="#{pc_Ssg00301.propGakkaSosikiLv.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakkaSosikiLv"
										value="#{pc_Ssg00301.propGakkaSosikiLv.stringValue}"
										disabled="#{pc_Ssg00301.propGakkaSosikiLv.disabled}"
										readonly="#{pc_Ssg00301.propGakkaSosikiLv.readonly}"
										style="#{pc_Ssg00301.propGakkaSosikiLv.style}">
										<f:selectItems value="#{pc_Ssg00301.propGakkaSosikiLv.list}" />
									</h:selectOneMenu><BR>
									<h:outputText styleClass="note" id="htmlLblSumType2"
										value="　（集計単位が学科組織単位の場合のみ選択）">
									</h:outputText></TD>
								</TR>
								<TR>
						         <hx:jspPanel rendered="#{!pc_Ssg00301.propSeikiCheck.rendered}">
									<TH class="v_c" width="150" rowspan="3"><h:outputText
										styleClass="outputText" id="htmlLblSyukeiTaisyo" value="集計対象">
									</h:outputText></TH>
						         </hx:jspPanel>
						         <hx:jspPanel rendered="#{pc_Ssg00301.propSeikiCheck.rendered}">
							         <TH class="v_c" width="150" rowspan="5"><h:outputText
						    	      styleClass="outputText" id="htmlLblSyukeiTaisyo2" value="集計対象">
						        	 </h:outputText></TH>
						         </hx:jspPanel>       
									<TD height="46"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlInternShipCheck"
										value="#{pc_Ssg00301.propInternShipCheck.checked}"
										disabled="#{pc_Ssg00301.propInternShipCheck.disabled}"
										readonly="#{pc_Ssg00301.propInternShipCheck.readonly}"
										style="#{pc_Ssg00301.propInternShipCheck.style}">
									</h:selectBooleanCheckbox><h:outputText styleClass="outputText"
										id="htmlLblInternCheck"
										value="#{pc_Ssg00301.propInternShipCheck.labelName}"
										style="#{pc_Ssg00301.propInternShipCheck.labelStyle}"></h:outputText></TD>
								</TR>
								<TR>
									<TD height="46"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSexCheck"
										value="#{pc_Ssg00301.propSexCheck.checked}"
										disabled="#{pc_Ssg00301.propSexCheck.disabled}"
										readonly="#{pc_Ssg00301.propSexCheck.readonly}"
										style="#{pc_Ssg00301.propSexCheck.style}">
									</h:selectBooleanCheckbox><h:outputText styleClass="outputText"
										id="htmlLblSexCheck"
										value="#{pc_Ssg00301.propSexCheck.labelName}"
										style="#{pc_Ssg00301.propSexCheck.labelStyle}"></h:outputText></TD>
								</TR>
								<TR>
									<TD height="46"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlJitaiCountCheck"
										value="#{pc_Ssg00301.propJitaiCountCheck.checked}"
										disabled="#{pc_Ssg00301.propJitaiCountCheck.disabled}"
										readonly="#{pc_Ssg00301.propJitaiCountCheck.readonly}"
										style="#{pc_Ssg00301.propJitaiCountCheck.style}">
									</h:selectBooleanCheckbox><h:outputText styleClass="outputText"
										id="htmlLblJitaiCountCheck"
										value="#{pc_Ssg00301.propJitaiCountCheck.labelName}"
										style="#{pc_Ssg00301.propJitaiCountCheck.labelStyle}"></h:outputText></TD>
								</TR>
						        <hx:jspPanel rendered="#{pc_Ssg00301.propSeikiCheck.rendered}">
						        <TR>         
						         <TD height="46">
						          <h:selectBooleanCheckbox
						           styleClass="selectBooleanCheckbox" id="htmlSeikiCheck"
						           value="#{pc_Ssg00301.propSeikiCheck.checked}"
						           disabled="#{pc_Ssg00301.propSeikiCheck.disabled}"
						           readonly="#{pc_Ssg00301.propSeikiCheck.readonly}"
						           style="#{pc_Ssg00301.propSeikiCheck.style}">
						          </h:selectBooleanCheckbox>
						          <h:outputText styleClass="outputText"
						           id="htmlLblSeikiCheck"
						           value="#{pc_Ssg00301.propSeikiCheck.labelName}"
						           style="#{pc_Ssg00301.propSeikiCheck.labelStyle}">
						          </h:outputText>
						         </TD>
						        </TR>
						        <TR>         
						         <TD height="46">
						          <h:selectBooleanCheckbox
						           styleClass="selectBooleanCheckbox" id="htmlHiseikiCheck"
						           value="#{pc_Ssg00301.propHiseikiCheck.checked}"
						           disabled="#{pc_Ssg00301.propHiseikiCheck.disabled}"
						           readonly="#{pc_Ssg00301.propHiseikiCheck.readonly}"
						           style="#{pc_Ssg00301.propHiseikiCheck.style}">
						          </h:selectBooleanCheckbox>
						          <h:outputText styleClass="outputText"
						           id="htmlLblHiseikiCheck"
						           value="#{pc_Ssg00301.propHiseikiCheck.labelName}"
						           style="#{pc_Ssg00301.propHiseikiCheck.labelStyle}">
						          </h:outputText>
						         </TD>
						        </TR>
						        </hx:jspPanel>        
							</TBODY>
						</TABLE>
						<BR>
						</TD>
					</TR>
					<TR align="center">
						<TD width="974" height="37">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_d" width="150"><h:outputText
										styleClass="outputText" id="htmlLblRepTitle"
										value="#{pc_Ssg00301.propRepTitle.labelName}"
										style="#{pc_Ssg00301.propRepTitle.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:inputText styleClass="inputText"
										id="htmlRepTitle"
										value="#{pc_Ssg00301.propRepTitle.stringValue}" size="60"
										disabled="#{pc_Ssg00301.propRepTitle.disabled}"
										maxlength="#{pc_Ssg00301.propRepTitle.maxLength}"
										readonly="#{pc_Ssg00301.propRepTitle.readonly}"
										style="#{pc_Ssg00301.propRepTitle.style}">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar">

				<TBODY>
					<TR align="center">
						<TD width="974"><hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfout"
							action="#{pc_Ssg00301.doPdfOutAction}"
							confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="EXCEL作成" styleClass="commandExButton_out"
							id="excelout" action="#{pc_Ssg00301.doExcelOutAction}"
							confirm="#{msg.SY_MSG_0027W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="CSV作成" styleClass="commandExButton_out"
							id="csvout" action="#{pc_Ssg00301.doCsvOutAction}"
							confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="出力項目指定" styleClass="commandExButton_out"
							id="setoutput" action="#{pc_Ssg00301.doSetoutputAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden id="linkFlg"
				value="#{pc_Ssg00301.propLinkFlg.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="executable"
				value="#{pc_Ssg00301.propExecutable.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

