<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00302.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page import="com.jast.gakuen.rev.xra.Xra00302"%>
<%@ page import="com.jast.gakuen.framework.util.UtilStr"%>
<%@ page import="com.jast.gakuen.framework.util.UtilSystem"%>

<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00302.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}

function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xra00302.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xra00302.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xra00302.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xra00302.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
				<hx:commandExButton type="submit" value="戻る"
					styleClass="commandExButton" id="returnDisp"
					action="#{pc_Xra00302.doReturnDispAction}" tabindex="12">
				</hx:commandExButton>
            <!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="900">
				<TR>
					<TD width="900" align="left">
						<TABLE class="table">
<% // PageCodeを取得してhidden処理区分を変数に格納し、出力項目の制御を行う
	Xra00302 pagecode = (Xra00302) UtilSystem.getManagedBean(Xra00302.class);
	String syoriKbn = UtilStr.cnvNull(pagecode.getPropHidSyoriKbn().getStringValue());
%>
<% // 学生証用データ出力、第１回配本シール用データ出力
	if (syoriKbn.equals("1") || syoriKbn.equals("3")) { %>
							<TR>
								<TH class="v_a" width="140">
									<h:outputText styleClass="outputText"
										id="lblNyugakNendo"
										style="#{pc_Xra00302.propNyugakNendo.labelStyle}"
										value="#{pc_Xra00302.propNyugakNendo.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:inputText styleClass="inputText"
										id="htmlNyugakNendo"
										value="#{pc_Xra00302.propNyugakNendo.dateValue}"
										style="#{pc_Xra00302.propNyugakNendo.style}"
										size="10" tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
								</TD>
								<TH class="v_b" width="140">
									<h:outputText styleClass="outputText"
										id="lblNyugakGakkiNo"
										style="#{pc_Xra00302.propNyugakGakkiNo.labelStyle}"
										value="#{pc_Xra00302.propNyugakGakkiNo.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:inputText styleClass="inputText"
										id="htmlNyugakGakkiNo"
										value="#{pc_Xra00302.propNyugakGakkiNo.integerValue}"
										style="#{pc_Xra00302.propNyugakGakkiNo.style}"
										size="5" tabindex="2">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
								</TD>
							</TR>
<% } %>
<% // 玉川通信・ZENJINシール打出し
	if (syoriKbn.equals("2")) { %>
							<TR>
								<TH class="v_a" width="140">
									<h:outputText styleClass="outputText"
										id="lblHassoNen"
										style="#{pc_Xra00302.propHassoNen.labelStyle}"
										value="#{pc_Xra00302.propHassoNen.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:inputText styleClass="inputText"
										id="htmlHassoNen"
										value="#{pc_Xra00302.propHassoNen.dateValue}"
										style="#{pc_Xra00302.propHassoNen.style}"
										size="10" tabindex="3">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
									年
									<h:inputText styleClass="inputText"
										id="htmlHassoTsuki"
										value="#{pc_Xra00302.propHassoTsuki.dateValue}"
										style="#{pc_Xra00302.propHassoTsuki.style}"
										size="5" tabindex="4">
										<f:convertDateTime pattern="MM" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
									月
								</TD>
								<TH class="v_b" width="140">
									<h:outputText styleClass="outputText"
										id="lblOutputTarget"
										style="#{pc_Xra00302.propOutputTarget.labelStyle}"
										value="#{pc_Xra00302.propOutputTarget.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlOutputTarget" tabindex="5"
										value="#{pc_Xra00302.propOutputTarget.value}">
										<f:selectItems value="#{pc_Xra00302.propOutputTarget.list}" />
									</h:selectOneMenu>
								</TD>
							</TR>
<% } %>
<% // 継続用学生証シール送付住所データ作成 以外
	if (!syoriKbn.equals("4")) { %>
							<TR>
								<TH nowrap class="v_c">
									<h:outputText styleClass="outputText"
										id="lblOutput"
										value="#{pc_Xra00302.propOutput.labelName}"
										style="#{pc_Xra00302.propOutput.labelStyle}">
                            		</h:outputText>
                            	</TH>
								<TD colspan=3>
									<h:selectOneRadio
										onchange="onCangeData();"
										id="htmlOutput" disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" tabindex="6"
										value="#{pc_Xra00302.propOutput.stringValue}">
										<f:selectItems value="#{pc_Xra00302.propOutput.list}" />
									</h:selectOneRadio>
								</TD>
							</TR>
<% } %>
<% // 玉川通信・ZENJINシール打出し
	if (syoriKbn.equals("2")) { %>
							<TR>
								<TH nowrap class="v_c">
									<h:outputText styleClass="outputText"
										id="lblZaisekiKigen"
										value="#{pc_Xra00302.propZaisekiKigen.labelName}"
										style="#{pc_Xra00302.propZaisekiKigen.labelStyle}">
                            		</h:outputText>
                            	</TH>
								<TD colspan=3>
									<h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox"
										id="htmlZaisekiKigen" tabindex="7"
										value="#{pc_Xra00302.propZaisekiKigen.checked}">
									</h:selectBooleanCheckbox>
									在籍期限参照の基準を次学期にする
								</TD>
							</TR>
<% } %>
<% // 継続用学生証シール送付住所データ作成
	if (syoriKbn.equals("4")) { %>
							<TR>
								<TH class="v_a" width="140">
									<h:outputText styleClass="outputText"
										id="lblHakkoNendo"
										style="#{pc_Xra00302.propHakkoNendo.labelStyle}"
										value="#{pc_Xra00302.propHakkoNendo.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:inputText styleClass="inputText"
										id="htmlHakkoNendo"
										value="#{pc_Xra00302.propHakkoNendo.dateValue}"
										style="#{pc_Xra00302.propHakkoNendo.style}"
										size="10" tabindex="8">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
								</TD>
								<TH class="v_b" width="140">
									<h:outputText styleClass="outputText"
										id="lblTaisyoGakkiNo"
										style="#{pc_Xra00302.propTaisyoGakkiNo.labelStyle}"
										value="#{pc_Xra00302.propTaisyoGakkiNo.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:inputText styleClass="inputText"
										id="htmlTaisyoGakkiNo"
										value="#{pc_Xra00302.propTaisyoGakkiNo.integerValue}"
										style="#{pc_Xra00302.propTaisyoGakkiNo.style}"
										size="5" tabindex="9">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_c">
									<h:outputText styleClass="outputText"
										id="lblHakkoDate"
										value="#{pc_Xra00302.propHakkoDate.labelName}"
										style="#{pc_Xra00302.propHakkoDate.labelStyle}">
                            		</h:outputText>
                            	</TH>
								<TD colspan=3>
									<h:inputText styleClass="inputText"
										id="htmlHakkoDate"
										value="#{pc_Xra00302.propHakkoDate.dateValue}"
										style="#{pc_Xra00302.propHakkoDate.style}"
										size="12" tabindex="10">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
										<hx:inputHelperDatePicker />
									</h:inputText>
								</TD>
							</TR>
<% } %>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
			<TABLE width="900" class="button_bar" border="0" cellpadding="0"
				cellspacing="0">
				<TR>
					<TD >
						<hx:commandExButton type="submit" value="CSV作成"
							styleClass="commandExButton_dat" id="csvout"
							style="#{pc_Xra00302.propCsvout.style}"
							action="#{pc_Xra00302.doCsvoutAction}"
							confirm="#{msg.SY_MSG_0001W}" tabindex="11">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<h:inputHidden value="#{pc_Xra00302.propHidSyoriKbn.stringValue}" id="htmlHidSyoriKbn">
			</h:inputHidden>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

