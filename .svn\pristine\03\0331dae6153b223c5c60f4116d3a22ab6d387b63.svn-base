<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmg01201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmg01201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT type="text/javascript"></SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmg01201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmg01201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmg01201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmg01201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="600" class="table">
				<TBODY>
					<TR>
						<TH width="150" class="v_a"><h:outputText styleClass="outputText"
							id="lblBunmenKbn"
							value="#{pc_Kmg01201.propBunmenKbn.labelName}"
							style="#{pc_Kmg01201.propBunmenKbn.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlBunmenKbn" value="#{pc_Kmg01201.propBunmenKbn.value}"
							style="width:140px;">
							<f:selectItems value="#{pc_Kmg01201.propBunmenKbn.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH width="150" class="v_a"><h:outputText styleClass="outputText"
							id="lblOutKbn" value="#{pc_Kmg01201.propOutKbn.labelName}"
							style="#{pc_Kmg01201.propOutKbn.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlOutKbn"
							value="#{pc_Kmg01201.propOutKbn.stringValue}">
							<f:selectItem itemValue="0" itemLabel="在学生" />
							<f:selectItem itemValue="1" itemLabel="卒業生" />
						</h:selectOneRadio></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE width="600" class="table">
				<TBODY>
					<TR>
						<TH width="150" class="v_b"><h:outputText styleClass="outputText"
							id="lblOutputNendo"
							value="#{pc_Kmg01201.propOutputNendo.labelName}"
							style="#{pc_Kmg01201.propOutputNendo.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlOutputNendo" size="4"
							value="#{pc_Kmg01201.propOutputNendo.integerValue}"
							maxlength="#{pc_Kmg01201.propOutputNendo.maxLength}"
							style="#{pc_Kmg01201.propOutputNendo.style}">
							<f:convertNumber type="number" pattern="###0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH width="150" class="v_c"><h:outputText styleClass="outputText"
							id="lblOutputGakkiNo"
							value="#{pc_Kmg01201.propOutputGakkiNo.labelName}"
							style="#{pc_Kmg01201.propOutputGakkiNo.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlOutputGakkiNo" size="2"
							value="#{pc_Kmg01201.propOutputGakkiNo.integerValue}"
							maxlength="#{pc_Kmg01201.propOutputGakkiNo.maxLength}"
							style="#{pc_Kmg01201.propOutputGakkiNo.style}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText></TD>
					</TR>
					<TR>
						<TH width="150" class="v_c"><h:outputText styleClass="outputText"
							id="lblSikak" value="#{pc_Kmg01201.propSikak.labelName}"
							style="#{pc_Kmg01201.propSikak.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSikak" value="#{pc_Kmg01201.propSikak.value}" style="width:450px;">
							<f:selectItems value="#{pc_Kmg01201.propSikak.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH width="150" class="v_e"><h:outputText styleClass="outputText"
							id="lblHsySbt" value="#{pc_Kmg01201.propHsySbt.labelName}"
							style="#{pc_Kmg01201.propHsySbt.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlHsySbt" value="#{pc_Kmg01201.propHsySbt.value}"
							style="width:370px;">
							<f:selectItems value="#{pc_Kmg01201.propHsySbt.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH width="150" class="v_f"><h:outputText styleClass="outputText"
							id="lblHakkouDate" value="#{pc_Kmg01201.propHakkouDate.name}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlHakkouDate" value="#{pc_Kmg01201.propHakkouDate.dateValue}">
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH width="150" class="v_d"><h:outputText styleClass="outputText"
							id="lblGakusiName" value="#{pc_Kmg01201.propGakusiName.labelName}"
							style="#{pc_Kmg01201.propGakusiName.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlGakushiName" value="#{pc_Kmg01201.propGakusiName.checked}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="text19" value="発行日時点で「卒業」の学生のみ出力する"></h:outputText></TD>
					</TR>

				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE class="table" width="600">

				<TBODY>
					<TR>
						<TH width="200" class="v_d"><h:outputText styleClass="outputText"
							id="lblSikakSeiseki" value="#{pc_Kmg01201.propSikakuKamokSeiseki.labelName}"
							style="#{pc_Kmg01201.propSikakuKamokSeiseki.labelStyle}"></h:outputText></TH>
						<TD width="400"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlSikakSeiseki" value="#{pc_Kmg01201.propSikakuKamokSeiseki.checked}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="text18" value="資格用科目成績を表示する"></h:outputText></TD>
					</TR>
					<TR>
						<TH width="200" class="v_d"><h:outputText styleClass="outputText"
							id="lblFurikaeKamokTitle"
							value="#{pc_Kmg01201.propFurikaeKamokTitle.labelName}"></h:outputText></TH>
						<TD width="400"><h:inputText styleClass="inputText"
							id="htmlFurikaeKamokTitle" size="30"
							value="#{pc_Kmg01201.propFurikaeKamokTitle.stringValue}"
							maxlength="#{pc_Kmg01201.propFurikaeKamokTitle.maxLength}"
							style="#{pc_Kmg01201.propFurikaeKamokTitle.style}"></h:inputText></TD>
					</TR>
					<TR>
						<TH width="200" class="v_e"><h:outputText styleClass="outputText"
						       id="lblKokaiDate"
						       value="#{pc_Kmg01201.propKokaiDate.labelName}"
						       style="#{pc_Kmg01201.propKokaiDate.labelStyle}"></h:outputText></TH>
						<TD width="400"><h:selectBooleanCheckbox
						       styleClass="selectBooleanCheckbox" id="htmlKokaiDate"
						       value="#{pc_Kmg01201.propKokaiDate.checked}"></h:selectBooleanCheckbox><h:outputText
						       styleClass="outputText" id="lblKokaiDate2" value="公開開始日を経過した成績のみ対象"></h:outputText></TD>
					</TR>
					<TR>
						<TH width="200" class="v_e"><h:outputText styleClass="outputText"
						       id="lblYusenFlg"
						       value="#{pc_Kmg01201.propYusenFlg.labelName}"
						       style="#{pc_Kmg01201.propYusenFlg.labelStyle}"></h:outputText></TH>
						<TD width="400"><h:selectBooleanCheckbox
						       styleClass="selectBooleanCheckbox" id="htmlYusenFlg"
						       value="#{pc_Kmg01201.propYusenFlg.checked}"></h:selectBooleanCheckbox><h:outputText
						       styleClass="outputText" id="lblYusenKbn2" value="科目標準名称を優先して出力する"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" styleClass="commandExButton_dat" id="register" 
						action="#{pc_Kmg01201.doRegisterAction}" value="確定"></hx:commandExButton><hx:commandExButton type="submit" 
						styleClass="commandExButton_etc" id="clear" action="#{pc_Kmg01201.doClearAction}" value="クリア"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>
