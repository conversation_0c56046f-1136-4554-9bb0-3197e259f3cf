<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_COB_STFR" name="卒業生自由設定" prod_id="CO" description="パッケージの標準データとして管理していないが、別途管理したい教職員の情報を保持します。">
<STATMENT><![CDATA[
COB_STFR
]]></STATMENT>
<COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生を一意に識別する為の番号です。"/><COLUMN id="FRE_TSY_CD" name="自由設定対象コード" type="string" length="5" lengthDP="0" byteLength="5" description="自由設定で管理する対象を識別するコードが設定されます。"/><COLUMN id="FRE_DATA_NO" name="自由設定データＮＯ" type="number" length="3" lengthDP="0" byteLength="0" description="自由設定対象コードに対して自由設定項目を時系列または意味合いを分けて管理することができます。
この時系列または意味合いを分けたデータを識別するための数値がシステムで自動的に設定されます。"/><COLUMN id="FRE_KOMOK_NO" name="自由設定項目ＮＯ" type="number" length="3" lengthDP="0" byteLength="0" description="同一管理対象の各項目を識別するための数値が設定されます。"/><COLUMN id="FRE_TSY_NAME" name="自由設定対象名称" type="string" length="30" lengthDP="0" byteLength="90" description="自由設定対象コードを表す名称が設定されます。"/><COLUMN id="FRE_KOMOK_NAME" name="自由設定項目名称" type="string" length="20" lengthDP="0" byteLength="60" description="自由設定対象の中の自由設定項目を表す名称が設定されます。"/><COLUMN id="FRE_VALUE" name="自由設定内容" type="string" length="1024" lengthDP="0" byteLength="3072" description="学生個別の情報が設定されます。"/>
</TABLE>
