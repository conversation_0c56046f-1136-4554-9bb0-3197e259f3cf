<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmi00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmi00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

// 確認ダイアログで「ＯＫ」の場合
function confirmOk() {
	document.getElementById("form1:htmlExecutableSearch").value = 1;

	var csvpdf = document.getElementById("form1:htmlOutputTarget").value;
	if(csvpdf == 'csv') {
		indirectClick('csvOut');
	}
	else {
		indirectClick('pdfOut');
	}
}

// 確認ダイアログで「キャンセル」の場合
function confirmCancel() {	
	document.getElementById("form1:htmlExecutableSearch").value = 0;
}


function func_1(thisObj, thisEvent) {
openWindow("", "PCos0402",
"<%=com.jast.gakuen.rev.co.PCos0402.getWindowOpenOption() %>");
setTarget("PCos0402"); return true;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmi00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmi00301.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmi00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmi00301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 --><BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600" class="table">
							<TBODY>
								<TR>
									<TH width="170" class="v_a"><h:outputText
										styleClass="outputText" id="text1"
										value="#{pc_Kmi00301.propTitle.labelName}"
										style="#{pc_Kmi00301.propTitle.labelStyle}"></h:outputText>
									</TH>
									<TD width="380"><h:inputText styleClass="inputText"
										id="htmlTitle" size="60"
										value="#{pc_Kmi00301.propTitle.stringValue}"
										maxlength="#{pc_Kmi00301.propTitle.maxLength}"
										style="#{pc_Kmi00301.propTitle.style}"></h:inputText>
									</TD>
								</TR>
								<TR>
									<TH width="170" class="v_b"><h:outputText
										styleClass="outputText" id="text2" value="クラブサークル"
										style="#{pc_Kmi00301.propClub.labelStyle}"></h:outputText>
									</TH>
									<TD width="380"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlClub" value="#{pc_Kmi00301.propClub.value}"
										style="width:330px">
										<f:selectItems value="#{pc_Kmi00301.propClub.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH width="170" class="v_c"><h:outputText
										styleClass="outputText" id="text3" value="出力対象"
										style="#{pc_Kmi00301.propTsy.labelStyle}"></h:outputText>
									</TH>
									<TD width="380"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlTsy"
										value="#{pc_Kmi00301.propTsy.stringValue}"
										style="#{pc_Kmi00301.propTitle.style};width:380px"
										layout="pageDirection">
										<f:selectItem itemValue="0" itemLabel="クラブサークル一覧" />
										<f:selectItem itemValue="1" itemLabel="所属学生一覧" />
										<f:selectItem itemValue="2" itemLabel="クラブサークル活動実績一覧" />
										<f:selectItem itemValue="3" itemLabel="所属学生活動実績一覧" />
									</h:selectOneRadio>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton
							type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfOut"
							action="#{pc_Kmi00301.doPdfOutAction}"
							confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
							<hx:commandExButton
							type="submit" value="CSV作成"
							styleClass="commandExButton_out" id="csvOut"
							action="#{pc_Kmi00301.doCsvOutAction}"
							confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
							<hx:commandExButton
							type="submit" value="出力項目指定"
							styleClass="commandExButton_out" id="setOutput"
							action="#{pc_Kmi00301.doSetOutputAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Kmi00301.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kmi00301.propOutputTarget.stringValue}"
				id="htmlOutputTarget">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

