<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb20302.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>光熱費配分実績登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT type="text/javascript">
function alChk(thisObj, thisEvent) {
	check('htmlKonetsuhiList','checkbox1');
}
function alUnchk(thisObj, thisEvent) {
	uncheck('htmlKonetsuhiList','checkbox1');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb20302.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Keb20302.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb20302.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb20302.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" 
					value="新規登録" 
					styleClass="commandExButton" 
					id="register" 
					action="#{pc_Keb20302.doRegisterAction}" 
					disabled="#{pc_Keb20302.propRegister.disabled}" 
					rendered="#{pc_Keb20302.propRegister.rendered}" 
					style="#{pc_Keb20302.propRegister.style}">
</hx:commandExButton>
<hx:commandExButton type="submit" 
					value="戻る" 
					styleClass="commandExButton" 
					id="returnDisp" 
					action="#{pc_Keb20302.doReturnDispAction}">
</hx:commandExButton>
　<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<!-- ↑ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style="" 
		   class="table" 
		   width="900">
		<TBODY>
			<TR>
				<TH style="" class="v_a" width="125">
					<h:outputText styleClass="outputText" 
								  id="lblKaikeiNendo" 
								  style="#{pc_Keb20302.propKaikeiNendo.style}" 
								  value="#{pc_Keb20302.propKaikeiNendo.name}" 
								  rendered="#{pc_Keb20302.propKaikeiNendo.rendered}">
					</h:outputText>
				</TH>
				<TD>
					<h:outputText styleClass="outputText" 
								  id="htmlKaikeiNendo" 
								  style="#{pc_Keb20302.propKaikeiNendo.style}" 
								  value="#{pc_Keb20302.propKaikeiNendo.dateValue}" 
								  rendered="#{pc_Keb20302.propKaikeiNendo.rendered}">
						<f:convertDateTime pattern="yyyy" />
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_b" width="">
					<h:outputText styleClass="outputText" 
								  id="lblKonetsuhiKbn" 
								  style="#{pc_Keb20302.propKonetsuhiKbn.style}" 
								  value="#{pc_Keb20302.propKonetsuhiKbn.name}" 
								  rendered="#{pc_Keb20302.propKonetsuhiKbn.rendered}">
					</h:outputText>
				</TH>
				<TD>
					<h:outputText styleClass="outputText" 
								  id="htmlKonetsuhiKbn" 
								  style="#{pc_Keb20302.propKonetsuhiKbn.style}"
								  value="#{pc_Keb20302.propKonetsuhiKbn.stringValue}" 
								  rendered="#{pc_Keb20302.propKonetsuhiKbn.rendered}">
					</h:outputText>　
					<h:outputText styleClass="outputText" 
								  id="htmlKonetsuhiKbnName" 
								  title="#{pc_Keb20302.propKonetsuhiKbnName.stringValue}" 
								  value="#{pc_Keb20302.propKonetsuhiKbnName.stringValue}" 
								  style="#{pc_Keb20302.propKonetsuhiKbnName.style}" 
								  rendered="#{pc_Keb20302.propKonetsuhiKbn.rendered}">
					</h:outputText></TD>
			</TR>
			<TR>
				<TH style="" class="v_c" width="">
					<h:outputText styleClass="outputText" 
								  id="lblKonetsuhiToriSaki" 
								  value="#{pc_Keb20302.propKonetsuhiToriSaki.name}" 
								  style="#{pc_Keb20302.propKonetsuhiToriSaki.style}" 
								  rendered="#{pc_Keb20302.propKonetsuhiToriSaki.rendered}">
					</h:outputText>
				</TH>
				<TD>
					<h:outputText styleClass="outputText" 
								  id="htmlKonetsuhiToriSaki" 
								  value="#{pc_Keb20302.propKonetsuhiToriSaki.stringValue}" 
								  rendered="#{pc_Keb20302.propKonetsuhiToriSaki.rendered}" 
								  style="#{pc_Keb20302.propKonetsuhiToriSaki.style}">
					</h:outputText>　
					<h:outputText styleClass="outputText" 
								  id="htmlKonetsuhiToriSakiName" 
								  title="#{pc_Keb20302.propKonetsuhiToriSakiName.stringValue}" 
								  value="#{pc_Keb20302.propKonetsuhiToriSakiName.stringValue}" 
								  rendered="#{pc_Keb20302.propKonetsuhiToriSakiName.rendered}" 
								  style="#{pc_Keb20302.propKonetsuhiToriSakiName.style}">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_d" width="">
					<h:outputText styleClass="outputText" 
								  id="lblKonetsuhiTani" 
								  value="#{pc_Keb20302.propKonetsuhiTani.name}" 
								  style="#{pc_Keb20302.propKonetsuhiTani.style}">
					</h:outputText>
				</TH>
				<TD>
					<h:outputText styleClass="outputText" 
								  id="htmlKonetsuhiTani" 
								  value="#{pc_Keb20302.propKonetsuhiTani.stringValue}" 
								  style="#{pc_Keb20302.propKonetsuhiTani.style}" 
								  rendered="#{pc_Keb20302.propKonetsuhiTani.rendered}">
					</h:outputText>　
					<h:outputText styleClass="outputText" 
								  id="htmlKonetsuhiTaniName" 
								  title="#{pc_Keb20302.propKonetsuhiTaniName.stringValue}" 
								  value="#{pc_Keb20302.propKonetsuhiTaniName.stringValue}" 
								  style="#{pc_Keb20302.propKonetsuhiTaniName.style}" 
								  rendered="#{pc_Keb20302.propKonetsuhiTaniName.rendered}">
					</h:outputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="900" 
		   style="margin-top:10px;" 
		   height="370">
		<TBODY>
			<TR>
				<TD align="right">
					<h:outputText styleClass="outputText" 
								  id="htmlListCount" 
								  value="#{pc_Keb20302.propKonetsuhiList.listCount}件" 
								  style="font-size: 8pt">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TD valign="top" height="370">
					<DIV style="height: 370px; overflow-y:scroll" 
						 id="listScroll" 
						 onscroll="setScrollPosition('scroll',this);" 
						 class="listScroll">
						<h:dataTable border="0" 
									 cellpadding="2" 
									 cellspacing="0" 
									 columnClasses="columnClass1" 
									 headerClass="headerClass" 
									 footerClass="footerClass" 
									 rowClasses="#{pc_Keb20302.propKonetsuhiList.rowClasses}" 
									 styleClass="meisai_scroll" 
									 style="#{pc_Keb20302.propKonetsuhiList.style};table-layout: fixed;"
									 id="htmlKonetsuhiList" 
									 value="#{pc_Keb20302.propKonetsuhiList.list}" 
									 var="varlist" 
									 width="880" 
									 first="#{pcKeb20302.propKonetsuhiList.first}" 
									 rows="#{pc_Keb20302.propKonetsuhiList.rows}">
							<h:column id="column1">
								<f:facet name="header">
								</f:facet>
								<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
														 id="checkbox1" 
														 value="#{varlist.selected}" 
														 rendered="#{varlist.rendered}">
								</h:selectBooleanCheckbox>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="25" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="対象年月" 
												  id="lblColumn2">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn2" 
											  value="#{varlist.taishoNengetsu}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="60" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="金額" 
												  id="lblColumn3">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn3" 
											  value="#{varlist.kingaku}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: right" name="style" />
								<f:attribute value="90" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="数量" 
												  id="lblColumn4">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn4" 
											  value="#{varlist.suryo}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: right" name="style" />
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="単価" 
												  id="lblColumn5">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn5" 
											  value="#{varlist.tanka}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: right" name="style" />
								<f:attribute value="95" name="width" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="出納日付" 
												  id="lblColumn6">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn6" 
											  value="#{varlist.suitoDate}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="60" name="width" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="銀行引落日付" 
												  id="lblColumn7">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn7" 
											  value="#{varlist.bnkHikiotoshiDate}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="60" name="width" />
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="摘要（内容）" 
												  id="lblColumn8">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn8" 
											  value="#{varlist.tekiyoNameNaiyo.stringValue}" 
											  title="#{varlist.tekiyoNameNaiyo.stringValue}" 
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="169" name="width" />
							</h:column>
							<h:column id="column11">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="伝票番号"
										id="lblColumn11"></h:outputText>
								</f:facet>
								<f:attribute value="175" name="width" />
								<h:outputText id="htmlDenpyoKanriNo"
												styleClass="outputText" value="#{varlist.denpyoKanriNo}"
												style="white-space:nowrap;">
								</h:outputText>
							</h:column>
							<h:column id="column9">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" 
													value="編集" 
													styleClass="commandExButton" 
													id="edit" 
													action="#{pc_Keb20302.doEditAction}" 
													disabled="#{pc_Keb20302.propEdit.disabled}" 
													rendered="#{pc_Keb20302.propEdit.rendered}">
								</hx:commandExButton>
								<f:attribute value="38" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column10">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" 
													value="ｺﾋﾟｰ" 
													styleClass="commandExButton" 
													id="copy" 
													action="#{pc_Keb20302.doCopyAction}" 
													disabled="#{pc_Keb20302.propCopy.disabled}" 
													rendered="#{pc_Keb20302.propCopy.rendered}">
								</hx:commandExButton>
								<f:attribute value="38" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
						</h:dataTable>
					</DIV>
				</TD>
			</TR>
			<TR>
				<TD align="left">
					<hx:panelBox styleClass="panelBox" id="box1">
						<hx:jspPanel id="jspPanel1">
							<hx:commandExButton type="button" 
												value="on" 
												styleClass="check" 
												id="check" 
												onclick="return alChk(this, event);">
							</hx:commandExButton>
							<hx:commandExButton type="button" 
												value="off" 
												styleClass="uncheck" 
												id="uncheck" 
												onclick="return alUnchk(this, event);">
							</hx:commandExButton>
						</hx:jspPanel>
					</hx:panelBox>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE width="900" 
		   border="0" 
		   class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton type="submit" 
										value="CSV作成" 
										styleClass="commandExButton_out" 
										id="csvout" 
										action="#{pc_Keb20302.doCsvoutAction}" 
										confirm="#{msg.SY_MSG_0020W}" 
										rendered="#{pc_Keb20302.propCsvout.rendered}" 
										disabled="#{pc_Keb20302.propCsvout.disabled}">
					</hx:commandExButton>
					<hx:commandExButton type="submit" 
										value="出力項目指定" 
										styleClass="commandExButton_out" 
										id="setoutput" 
										action="#{pc_Keb20302.doSetoutputAction}" 
										disabled="#{pc_Keb20302.propSetoutput.disabled}" 
										rendered="#{pc_Keb20302.propSetoutput.rendered}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden value="#{pc_Keb20302.propKonetsuhiList.scrollPosition}" 
			   id="scroll">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>

