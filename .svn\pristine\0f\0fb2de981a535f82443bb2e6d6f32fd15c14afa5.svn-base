<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob04301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob04301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob04301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob04301.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob04301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob04301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" width="430">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText
							styleClass="outputText" id="lblCount"
							value="#{pc_Cob04301.propCount.stringValue}"
							style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="text15" style="font-size: 8pt"
							value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="430">
				<TBODY>
					<TR>
						<TD>
						<div class="listScroll" style="height:313px;width: 100%;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Cob04301.propZairskk.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Cob04301.propZairskk.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text1" styleClass="outputText" value="在留資格コード"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text4"
									value="#{varlist.zairskkCode}"></h:outputText>
								<f:attribute value="100" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="在留資格名称" id="text2"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text5"
									value="#{varlist.zairskkName}"></h:outputText>
								<f:attribute value="300" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Cob04301.doSelectAction}"></hx:commandExButton>
								<f:attribute value="30" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable></div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR class="hr" noshade>
			<BR>
			<TABLE border="0" width="430" class="table">
				<TBODY>
								<TR>
									<TH bgcolor="white" width="130" class="v_a" align="left"><h:outputText
										styleClass="outputText" id="lblcode"
										value="#{pc_Cob04301.propZairskkCode.labelName}"
										style="#{pc_Cob04301.propZairskkCode.labelStyle}"></h:outputText></TH>
									<TD align="left" width="300"><h:inputText
										styleClass="inputText" id="htmlcode"
										value="#{pc_Cob04301.propZairskkCode.stringValue}"
										maxlength="#{pc_Cob04301.propZairskkCode.max}"
										style="#{pc_Cob04301.propZairskkCode.style}" size="2">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH bgcolor="white" width="130" class="v_b" align="left"><h:outputText
										styleClass="outputText" id="lblname"
										value="#{pc_Cob04301.propZairskkName.labelName}"
										style="#{pc_Cob04301.propZairskkName.labelStyle}"></h:outputText></TH>
									<TD align="left" width="300"><h:inputText
										styleClass="inputText" id="htmlname"
										value="#{pc_Cob04301.propZairskkName.stringValue}"
										maxlength="#{pc_Cob04301.propZairskkName.maxLength}"
										style="#{pc_Cob04301.propZairskkName.style}" size="36">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center" width="100%"><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Cob04301.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton> <hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Cob04301.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton> <hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Cob04301.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Cob04301.propZairskk.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>

