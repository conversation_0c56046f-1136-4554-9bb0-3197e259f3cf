<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab01904T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>関連情報登録（財産目録）</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kab01904T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kab01904T01.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kab01904T01.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kab01904T01.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton 
	type="submit" 
	value="戻る"
	styleClass="commandExButton" 
	id="rtnDisp"
	action="#{pc_Kab01904T01.doRtnDispAction}" 
	rendered="#{pc_Kab01904T01.propRtnDisp.rendered}">
</hx:commandExButton>
　
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="820">
		<TBODY>
			<TR>
				<TH style="" class="v_a" width="110">
					<B><h:outputText styleClass="outputText" 
									 id="lblBihinNo" 
									 value="#{pc_Kab01904T01.propBihnNo.labelName}">
					</h:outputText></B>
				</TH>
				<TD colspan="3" width="163">
				<h:outputText styleClass="outputText" id="htmlBihnNo" value="#{pc_Kab01904T01.propBihnNo.stringValue}"></h:outputText></TD>
				<TH style="" class="v_b" width="110">
					<h:outputText styleClass="outputText" 
								  id="lblEdaNo"
								  value="#{pc_Kab01904T01.propEdaNo.labelName}">
					</h:outputText>
				</TH>
				<TD colspan="3" width="163">
				<h:outputText styleClass="outputText" id="htmlEdaNo" value="#{pc_Kab01904T01.propEdaNo.stringValue}"></h:outputText></TD>
				<TH style="" class="v_c" width="110">
					<h:outputText styleClass="outputText" 
								  id="lblSyutokuKagaku"
								  value="#{pc_Kab01904T01.propSyutokuKagaku.labelName}">
					</h:outputText>
				</TH>
				<TD colspan="5" width="163">
				<h:outputText styleClass="outputText" id="htmlSyutokuKagaku" value="#{pc_Kab01904T01.propSyutokuKagaku.longValue}"><f:convertNumber pattern="###,###,###,##0;###,###,###,##0"/></h:outputText></TD>
			</TR>
		</TBODY>
	</TABLE>
	<BR>
	<TABLE border="0" cellpadding="0" cellspacing="0" width="820" height="300">
		<TBODY>
			<TR>
				<TD align="left" height="27">
					<hx:commandExButton
						type="submit"
						style="width: 164px"
						value="財産目録" 
						styleClass="tab_head_on" 
						id="lblZaisanMokuroku"></hx:commandExButton><hx:commandExButton
						type="submit"
						style="width: 164px"
						value="付属品" 
						styleClass="tab_head_off" 
						id="lblFuzokuHin"
						action="#{pc_Kab01904T01.doLblFuzokuHinAction}">
					</hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD height="100%" align="left" valign="top">
					<TABLE border="1" cellpadding="20" cellspacing="0" height="100%" width="820" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center" valign="top">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="800" style="margin-top: 10px;">
								<TBODY>
									<TR>
										<TD align="right" class="border_style:none;">
											<h:outputText styleClass="outputText" 
														  id="lblCodeListCnt" 
														  value="#{pc_Kab01904T01.propCodeList.listCount}">
											</h:outputText>
											<h:outputText styleClass="outputText" 
														  id="lblKen" 
														  value="件">
											</h:outputText>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
								<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
								<TBODY>
									<TR>
										<TD valign="top" height="250">
										<DIV id="listScroll0" 
											 class="listScroll" 
											 style="height: 250px; overflow-y:scroll" 
											 onscroll="setScrollPosition('htmlScrollPosition',this);">
											<h:dataTable 
												style=""
												var="varlist"
												value="#{pc_Kab01904T01.propCodeList.list}" cellpadding="2"
												width="780"
												rowClasses="#{pc_Kab01904T01.propCodeList.rowClasses}"
												columnClasses="columnClass1" cellspacing="0"
												headerClass="headerClass" footerClass="footerClass"
												id="htmlZaisanMokurokuList" styleClass="meisai_scroll"
												first="#{pc_Kab01904T01.propCodeList.first}"
												rows="#{pc_Kab01904T01.propCodeList.rows}">
												<h:column id="column1">
													<h:outputText styleClass="outputText" id="htmlColumn1"
														value="#{varlist.zaisanMokurokuCd}"
														style="white-space:nowrap;">
													</h:outputText>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="財産目録コード"
															id="lblColumn1">
														</h:outputText>
													</f:facet>
														<f:attribute value="160" name="width" />
														<f:attribute value="text-align: left" name="style" />
												</h:column>
												<h:column id="column2">
													<hx:jspPanel id="jspPanel1">
														<DIV style="width:270px;white-space:nowrap;overflow:hidden;display:block;">
															<h:outputText styleClass="outputText" id="htmlColumn2"
																value="#{varlist.zaisanMokurokuName.stringValue}"
																title="#{varlist.zaisanMokurokuName.stringValue}"
																style="white-space:nowrap;">
															</h:outputText>
														</DIV>
													</hx:jspPanel>	
													<f:facet name="header">
														<h:outputText styleClass="outputText" id="lblColumn2"
															value="財産目録名称">
														</h:outputText>
													</f:facet>
														<f:attribute value="270" name="width" />
														<f:attribute value="text-align: left" name="style" />
													<f:attribute value="true" name="nowrap" />
												</h:column>
												<h:column id="column3">
													<h:outputText styleClass="outputText" id="htmlColumn3"
														value="#{varlist.zaisanMokurokuAnbJissu}"
														style="white-space:nowrap;">
															<f:convertNumber
																pattern="###,###,###,##0.00;###,###,###,##0.00" />
														</h:outputText>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="財産目録按分実数"
															id="lblColumn3">
														</h:outputText>
													</f:facet>
														<f:attribute value="160" name="width" />
														<f:attribute value="text-align: right" name="style" />
												</h:column>
												<h:column id="column4">
													<h:outputText styleClass="outputText" id="htmlColumn4"
														value="#{varlist.percent}"
														style="white-space:nowrap;">
															<f:convertNumber pattern="##0.00;##0.00"/>
														</h:outputText>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="％"
															id="lblColumn4">
														</h:outputText>
													</f:facet>
														<f:attribute value="70" name="width" />
														<f:attribute value="text-align: right" name="style" />
												</h:column>
												<h:column id="column5">
													<h:outputText 
														styleClass="outputText" 
														id="htmlColumn5"
														value="#{varlist.tyoboKagaku}" 
														style="text-align: right">
															<f:convertNumber
																pattern="###,###,###,##0;###,###,###,##0" />
														</h:outputText>
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="帳簿価額"
															id="lblColumn5">
														</h:outputText>
													</f:facet>
														<f:attribute value="120" name="width" />
														<f:attribute value="text-align: right" name="style" />
												</h:column>
											</h:dataTable>
										</DIV>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="800">
								<TBODY>
									<TR>
										<TD width="330" class="clear_border" style="background-color:transparent"></TD>
										<TH class="v_d" width="100">
											<h:outputText styleClass="outputText" 
														  id="lblTotal" 
														  value="合計値">
											</h:outputText>
										</TH>
										<TD style="text-align: right" width="160">
											<h:outputText styleClass="outputText"
														  id="htmlTotalZsnMkrkAnbJissu"
														  value="#{pc_Kab01904T01.propTotalZsnMkrkAnbJissu.doubleValue}"
														  style="padding-right: 3px; text-align: right">
												<f:convertNumber pattern="#,###,###,###,##0.00" />
											</h:outputText>
										</TD>
										<TD style="text-align: right" width="70">
											<h:outputText styleClass="outputText"
														  id="htmlTotalPercent"
														  value="#{pc_Kab01904T01.propTotalPercent.doubleValue}"
														  style="padding-right: 3px; text-align: right">
												<f:convertNumber pattern="##0.00" />
											</h:outputText>
										</TD>
										<TD style="text-align: right" width="120">
											<h:outputText styleClass="outputText"
														  id="htmlTotalTyoboKagaku"
														  value="#{pc_Kab01904T01.propTotalTyoboKagaku.longValue}"
														  style="padding-right: 3px; text-align: right">
												<f:convertNumber pattern="#,###,###,###,##0" />
											</h:outputText>
										</TD>
										<TD class="clear_border" style="background-color:transparent" width="20"></TD>
									</TR>
								</TBODY>
								</TABLE>
								<BR>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
	<h:inputHidden id="htmlHiddenSsnKbn"
		value="#{pc_Kab01904T01.propHiddenSsnKbn.value}"></h:inputHidden>
	<h:inputHidden value="htmlSyutokuKagaku=#,###,###,###,##0;#,###,###,###,##0 | htmlZaisanMokurokuAnbunJissu=#,###,###,###,##0.00;#,###,###,###,##0.00"
		id="htmlFormatNumberOption">
	</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

