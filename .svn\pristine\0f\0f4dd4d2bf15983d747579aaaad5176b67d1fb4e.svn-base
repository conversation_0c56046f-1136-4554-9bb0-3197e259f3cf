<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmb03101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmb03101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	
<SCRIPT type="text/javascript">
        function confirmOk() {  

			if (document.getElementById('form1:htmlExecutableFlg').value == "0") {
				//登録処理
				if (document.getElementById('form1:SotuTaniChk').value == "0") {
					//卒業生単位振替条件チェック
					document.getElementById('form1:SotuTaniChk').value = "1";
					indirectClick('register');
				}else{
			    //修得上限単位数チェック
				   document.getElementById('form1:taniChk').value = "1";
		            indirectClick('register');
				}
			}else{
				//削除処理
					document.getElementById('form1:SotuTaniChk').value = "1";
					indirectClick('delete');
			}			
        }
        function confirmCancel() {
             //alert('実行を中断しました。');   
             //初期化
             document.getElementById('form1:SotuTaniChk').value = "0";
             document.getElementById('form1:taniChk').value = "0";
        }

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}

function loadAction(event){

    changeScrollPosition('scroll', 'listScroll');
}
</SCRIPT>

</HEAD>



<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Kmb03101.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Kmb03101.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Kmb03101.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Kmb03101.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
            <TABLE width="900">
                <TR>
                    <TD width="810" align="left">
                    <TABLE class="table">
                        <TR>
                            <TH class="v_a" width="140"><h:outputText styleClass="outputText"
                                id="lblNyugakNendo"
                                value="#{pc_Kmb03101.propNyugakNendo.labelName}"
                                style="#{pc_Kmb03101.propNyugakNendo.labelStyle}"></h:outputText></TH>
                            <TD width="260"><h:inputText styleClass="inputText"
                                id="htmlNyugakNendo"
                                value="#{pc_Kmb03101.propNyugakNendo.dateValue}"
                                style="#{pc_Kmb03101.propNyugakNendo.style}"
                                disabled="#{pc_Kmb03101.propNyugakNendo.disabled}"
                                size="10">
                                <hx:inputHelperAssist errorClass="inputText_Error"
                                imeMode="inactive" promptCharacter="_" />
                                <f:convertDateTime pattern="yyyy" />
                            </h:inputText></TD>
                           <TH class="v_b" width="150"><h:outputText styleClass="outputText"
                                id="lblNyugakGakkiNo"
                                value="#{pc_Kmb03101.propNyugakGakkiNo.labelName}"
                                style="#{pc_Kmb03101.propNyugakGakkiNo.labelStyle}"></h:outputText></TH>
                            <TD width="260"><h:inputText styleClass="inputText"
                                id="htmlNyugakGakkiNo"
                                value="#{pc_Kmb03101.propNyugakGakkiNo.integerValue}"
                                style="#{pc_Kmb03101.propNyugakGakkiNo.style}"
                                maxlength="#{pc_Kmb03101.propNyugakGakkiNo.maxLength}"
                                disabled="#{pc_Kmb03101.propNyugakGakkiNo.disabled}" size="10">
                                <f:convertNumber type="number" pattern="#0" />
                                <hx:inputHelperAssist errorClass="inputText_Error"
                                    promptCharacter="_" />
                            </h:inputText></TD>
                        </TR>
                    </TABLE>
                    </TD>
                    <TD align="left"><hx:commandExButton type="submit" value="選択"
                        styleClass="commandExButton" id="search1"
                        action="#{pc_Kmb03101.doSearch1Action}"
                        disabled="#{pc_Kmb03101.propSearch1.disabled}">
                    </hx:commandExButton><hx:commandExButton type="submit" value="解除"
                        styleClass="commandExButton" id="cancel1"
                        action="#{pc_Kmb03101.doCancel1Action}"
                        disabled="#{pc_Kmb03101.propCancel1.disabled}"></hx:commandExButton></TD>
                </TR>
            </TABLE>
            <table width="900">
                <tr>
                    <td width="810" align="left">
                    <table class="table">
                        <tr>
                            <TH class="v_c" width="140" nowrap><h:outputText styleClass="outputText"
                                id="lblCurriculumSubjectOrganize"
                                style="#{pc_Kmb03101.propCurriculumSubjectOrganize.labelStyle}"
                                value="#{pc_Kmb03101.propCurriculumSubjectOrganize.labelName}"></h:outputText></TH>
                            <TD width="670" nowrap><h:selectOneMenu styleClass="selectOneMenu"
                                id="htmlCurriculumSubjectOrganize"
                                value="#{pc_Kmb03101.propCurriculumSubjectOrganize.stringValue}"
                                disabled="#{pc_Kmb03101.propCurriculumSubjectOrganize.disabled}"
                                style="width:667px">
                                <f:selectItems
                                    value="#{pc_Kmb03101.propCurriculumSubjectOrganize.list}" />
                            </h:selectOneMenu></td>
                        </tr>
                    </table>
                    </td>
                    <td align="left"><hx:commandExButton type="submit" value="選択"
                        styleClass="commandExButton" id="search2"
                        disabled="#{pc_Kmb03101.propSearch2.disabled}"
                        action="#{pc_Kmb03101.doSearch2Action}">
                    </hx:commandExButton><hx:commandExButton type="submit" value="解除"
                        styleClass="commandExButton" id="cancel2"
                        disabled="#{pc_Kmb03101.propCancel2.disabled}"
                        action="#{pc_Kmb03101.doCancel2Action}"></hx:commandExButton></td>
                </tr>
            </table>
            <HR class="hr" noshade>
            <TABLE width="900">
                <TR>
                    <TD width="460" align="left"><h:outputText styleClass="outputText"
                        id="text1" value=""></h:outputText></TD>
                    <TD align="right"><h:outputFormat styleClass="outputFormat"
                        id="htmlListCount" value="{0}件">
                        <f:param name="listCount"
                            value="#{pc_Kmb03101.propListCount.stringValue}"></f:param>
                    </h:outputFormat></TD>
                </TR>
            </TABLE>
            
            
            
            <TABLE width="900" border="0" cellpadding="0">
                <tr>
                    <td>

                    <DIV onscroll="setScrollPosition('scroll', this)"
                        style="height:310px;" id="listScroll" class="listScroll">
                        
                    <h:dataTable
                        border="0" cellpadding="0"
                        rowClasses="#{pc_Kmb03101.propKamokBunruiList.rowClasses}"
                        rendered="#{pc_Kmb03101.propKamokBunruiList.rendered}"
                        cellspacing="0" columnClasses="columnClass1"
                        headerClass="headerClass" footerClass="footerClass"
                        rowClasses="#{pc_Kmb03101.propKamokBunruiList.rowClasses}"
                        styleClass="meisai_scroll"
                        id="htmlKamokBunruiList"
                        value="#{pc_Kmb03101.propKamokBunruiList.list}" var="varlist">
                        
                        
                        
                        
                        
                        <h:column id="column1">
                        	<f:facet name="header">
                        
                        	<hx:jspPanel id="jspPanel1">
	                            <TABLE border="0" cellpadding="0" cellspacing="0" width="480" height="55">
	                            <TR height="20">
									<TH style="border-left-style: none; border-top-style:none;" 
									colspan="4" align="center">
										<h:outputText
											styleClass="outputText" 
											value="振替元"
											id="lblFurimoto_head">
										</h:outputText>
									</TH>
	                            </TR>
								<TR  height=35">
									<TH style="border-left-style: none; border-bottom-style: none;"
										class="headerClass" align="center" width="70">
										<h:outputText
											styleClass="outputText" 
											value="分類コード"
											id="lblFuriMotoBunruiCd_head">
										</h:outputText>
									</TH>
									<TH style="border-bottom-style: none;"
										class="headerClass" align="center" width="290">
										<h:outputText
											styleClass="outputText" 
											value="分類名称"
											id="lblFuriMotoBunruiName_head">
										</h:outputText>
									</TH>
									<TH style="border-bottom-style: none;"
										class="headerClass" align="center" width="65">
										<h:outputText
											styleClass="outputText" 
											value="卒業要件単位数"
											id="lblSotugyoYokenTani_head">
										</h:outputText>
									</TH>
									<TH style="border-bottom-style: none;"
										class="headerClass" align="center" width="65">
										<h:outputText
											styleClass="outputText" 
											value="修得上限単位数"
											id="lblSyutokJogenTani_head">
										</h:outputText>
									</TH>
								</TR>
								</TABLE>
							</hx:jspPanel>
							</f:facet>
						
							<hx:jspPanel id="jspPanel2">
								<TABLE border="0" cellpadding="0" cellspacing="0">
									<TR>
										<TD style="border-style:none;"
											width="70">
											<!-- 分類コード -->
											<h:outputText
												styleClass="outputText"
												id="lblmotoBunruiCd_list"
												value="#{varlist.motoBunruiCd}">
											</h:outputText>
										</TD>
										<TD style="border-top-style:none; border-bottom-style: none; border-right-style: none;"
											width="290">
											<!-- 元分類名称 -->
	                            			<h:outputText styleClass="outputText" id="text2"
	                                			value="#{varlist.propMotoKamokBunruiName.displayValue}"
	                                			title="#{varlist.propMotoKamokBunruiName.stringValue}">
	                                		</h:outputText>
										</TD>
										<TD style="border-top-style:none; border-bottom-style: none; border-right-style: none; text-align:right"
											width="65">
											<!-- 卒業要件単位数 -->
	                            			<h:outputText styleClass="outputText" id="text3"
	                                			value="#{varlist.motoSotugyoYokenTani}">
	                                		</h:outputText>
										</TD>
										<TD style="border-top-style:none; border-bottom-style: none; border-right-style: none; text-align:right"
											width="65">
											<!-- 修得上限単位数 -->
	                            			<h:outputText styleClass="outputText" id="text4"
	                                			value="#{varlist.motoSyutokJogenTani}">
	                                		</h:outputText>
										</TD>
									</TR>
								</TABLE>
							</hx:jspPanel>
						</h:column>
								
							
							
							
							
							
							
					<h:column id="column2">
					<f:facet name="header">
                        
                        <hx:jspPanel id="jspPanel3">
                            <TABLE border="0" cellpadding="0" cellspacing="0" width="360" height="55">
                            <TR  height="20">
								<TH style="border-left-style: none; border-top-style:none;" 
								colspan="2" align="center">
									<h:outputText
										styleClass="outputText" 
										value="振替先"
										id="lblFurisaki_head">
									</h:outputText>
								</TH>
                            </TR>
                        
							<TR  height="35">
								<TH style="border-left-style: none; border-bottom-style: none;"
									class="headerClass" align="center" width="70" nowrap>
									<h:outputText
										styleClass="outputText" 
										value="分類コード   　　　"
										id="lblFuriSakiBunruiCd_head" >
									</h:outputText>
								</TH>
								<TH style="border-bottom-style: none;"
									class="headerClass" align="center" width="290">
									<h:outputText
										styleClass="outputText" 
										value="分類名称"
										id="lblFuriSakiBunruiName_head">
									</h:outputText>
								</TH>
							</TR>
							</TABLE>
						</hx:jspPanel>
					</f:facet>
						
						
						<hx:jspPanel id="jspPanel4">
							<TABLE border="0" cellpadding="0" cellspacing="0">
								<TR>
									<TD style="border-style:none;"
										width="70">
										<!-- 先分類コード -->
										<h:outputText
											styleClass="outputText"
											id="lblsakiBunruiCd_list"
											value="#{varlist.sakiBunruiCd}">
										</h:outputText>
									</TD>
									<TD style="border-top-style:none; border-bottom-style: none; border-right-style: none;"
										width="290">
										<!-- 先分類名称 -->
                            			<h:outputText styleClass="outputText" id="text6"
                                			value="#{varlist.propSakiKamokBunruiName.displayValue}"
                                			title="#{varlist.propSakiKamokBunruiName.stringValue}">
                                		</h:outputText>
									</TD>

								</TR>
							</TABLE>
						</hx:jspPanel>
					</h:column>
					
					

                        
					<h:column id="column3">
					    <hx:commandExButton type="submit" value="選択"
					        styleClass="commandExButton" id="edit"
					        onblur="return func_1(this, event);"
					        action="#{pc_Kmb03101.doEditAction}"></hx:commandExButton>
					    <f:facet name="header">
					    </f:facet>
					    <f:attribute value="140" name="width" />
					</h:column>

                    </h:dataTable>
                    <BR>
                    </DIV>
                    </td>
                </tr>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE width="900" class="table">
                <TBODY>
                    <TR>
                        <TH class="v_a"><h:outputText styleClass="outputText"
                            id="lblMotoKamokBunruiCd"
                            value="#{pc_Kmb03101.propMotoKamokBunruiCd.labelName}"
                            style="#{pc_Kmb03101.propMotoKamokBunruiCd.labelStyle}"></h:outputText>
                        </TH>
                        <TD colspan="3" ><h:outputText styleClass="outputText"
                            id="htmlMotoKamokBunruiCd"
                            value="#{pc_Kmb03101.propMotoKamokBunruiCd.stringValue}"
                            style="#{pc_Kmb03101.propMotoKamokBunruiCd.style}"></h:outputText>
                        </TD>
                    </TR>
                    

                    <TR>
                        <TH class="v_g" width="170" nowrap><h:outputText styleClass="outputText"
                            id="lblSotugyoYokenTani"
                            value="#{pc_Kmb03101.propSotugyoYokenTani.labelName}"
                            style="#{pc_Kmb03101.propSotugyoYokenTani.labelStyle}"></h:outputText></TH>
                        <TD width="100"><h:outputText styleClass="outputText"
                            id="htmlSotugyoYokenTani"
                            value="#{pc_Kmb03101.propSotugyoYokenTani.stringValue}"
                            style="#{pc_Kmb03101.propSotugyoYokenTani.style}"></h:outputText></TD>
                            
                        <TH class="v_a" width="170"><h:outputText styleClass="outputText"
                            id="lblSyutokJogenTani"
                            value="#{pc_Kmb03101.propSyutokJogenTani.labelName}"
                            style="#{pc_Kmb03101.propSyutokJogenTani.labelStyle}"></h:outputText></TH>
                        <TD><h:inputText styleClass="inputText" 
                            id="htmlSyutokJogenTani"
                            value="#{pc_Kmb03101.propSyutokJogenTani.doubleValue}"
                            disabled="#{pc_Kmb03101.propSyutokJogenTani.disabled}"
                            maxlength="#{pc_Kmb03101.propSyutokJogenTani.maxLength}"
                            style="#{pc_Kmb03101.propSyutokJogenTani.style}" size="10">
                            <f:convertNumber type="number" pattern="##0.00" />
                            <hx:inputHelperAssist errorClass="inputText_Error"
                                promptCharacter="_" />
                            </h:inputText>
                        </TD>
                    </TR>
                    <TR>
                        <TH class="v_b"><h:outputText styleClass="outputText"
                            id="lblSakiKamokBunruiCd"
                            value="#{pc_Kmb03101.propSakiKamokBunruiList.labelName}"
                            style="#{pc_Kmb03101.propSakiKamokBunruiList.labelStyle}"></h:outputText></TH>
                        <TD colspan="3" ><h:selectOneMenu styleClass="selectOneMenu"
                            id="htmlSakiKamokBunruiList"
                            disabled="#{pc_Kmb03101.propSakiKamokBunruiList.disabled}"
                            value="#{pc_Kmb03101.propSakiKamokBunruiList.stringValue}"
                            style="#{pc_Kmb03101.propSakiKamokBunruiList.style}">
                            <f:selectItems value="#{pc_Kmb03101.propSakiKamokBunruiList.list}" />
                        </h:selectOneMenu>
                        </TD>
                    </TR>

                </TBODY>
            </TABLE>
            
            
            <TABLE width="900" class="button_bar" border="0" cellpadding="0"
                cellspacing="0">
                <TR>
                    <TD ><hx:commandExButton type="submit" value="確定"
                            styleClass="commandExButton_dat" id="register"
                            confirm="#{msg.SY_MSG_0002W}"
                            disabled="#{pc_Kmb03101.propRegister.disabled}"
                            style="#{pc_Kmb03101.propRegister.style}" action="#{pc_Kmb03101.doRegisterAction}"></hx:commandExButton>
                            
                            <hx:commandExButton type="submit" value="削除" 
                            styleClass="commandExButton_dat" id="delete" 
                            disabled="#{pc_Kmb03101.propDelete.disabled}"
                            style="#{pc_Kmb03101.propDelete.style}" 
                            action="#{pc_Kmb03101.doDeleteAction1}" confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton>
                            
                            <hx:commandExButton
                            type="submit" value="クリア" styleClass="commandExButton_etc"
                            disabled="#{pc_Kmb03101.propClear.disabled}"
                            style="#{pc_Kmb03101.propClear.style}" 
                            id="clear" action="#{pc_Kmb03101.doClearAction}"></hx:commandExButton>
                    </TD>
                </TR>
            </TABLE>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            
            <h:inputHidden value="#{pc_Kmb03101.propTaniChk.integerValue}"
                id="taniChk">

                <f:convertNumber type="number" />
            </h:inputHidden>
            
            <h:inputHidden value="#{pc_Kmb03101.propSotuTaniChk.integerValue}"
                id="SotuTaniChk">

                <f:convertNumber type="number" />
            </h:inputHidden>
            
            <h:inputHidden value="#{pc_Kmb03101.prophtmlExecutableFlg.integerValue}"
                id="htmlExecutableFlg">

                <f:convertNumber type="number" />
            </h:inputHidden>
            
            <h:inputHidden
                value="#{pc_Kmb03101.propKamokBunruiList.scrollPosition}"
                id="scroll"></h:inputHidden>
            <!--<h:inputHidden value="#{pc_Kmb03101.propIsMax.stringValue}" id="max"></h:inputHidden>-->
            <h:inputHidden value="#{pc_Kmb03101.propIsReturn.stringValue}" id="isReturn"></h:inputHidden>
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

