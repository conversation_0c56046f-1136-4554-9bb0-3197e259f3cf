<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea02001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea02001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<style type="text/css">
<!--
.setWidth TD {width: 60px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript">

// ＝＝＝ Ajax ＝＝＝
function func_1(thisObj, thisEvent) {
	// (変更元)予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlMotoYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlMotoYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_2(thisObj, thisEvent) {
	// (変更先)予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlSakiYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlSakiYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_3(thisObj, thisEvent) {
	// (変更元)目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMotoMokuName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlMotoMokuCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_4(thisObj, thisEvent) {
	// (変更先)目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlSakiMokuName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlSakiMokuCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_5(thisObj, thisEvent) {
	// (変更元)科目名称を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlMotoKmkName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlMotoKmkCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_6(thisObj, thisEvent) {
	// (変更先)科目名称を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlSakiKmkName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlSakiKmkCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// LOAD時
function func_onload(thisObj, thisEvent){
	// (変更元)予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlMotoYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlMotoYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

	// (変更先)予算単位名称を取得する
	servlet = "rev/co/CogYosanTaniAJAX";
	target = "form1:htmlSakiYsnTName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlSakiYsnTCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

	// (変更元)目的名称を取得する
	servlet = "rev/co/CogYosanMokuAJAX";
	target = "form1:htmlMotoMokuName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlMotoMokuCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
	
	// (変更先)目的名称を取得する
	servlet = "rev/co/CogYosanMokuAJAX";
	target = "form1:htmlSakiMokuName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlSakiMokuCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

	// (変更元)科目名称を取得する
	servlet = "rev/co/CogKeiriKamokAJAX";
	target = "form1:htmlMotoKmkName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlMotoKmkCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
	
	// (変更先)科目名称を取得する
	servlet = "rev/co/CogKeiriKamokAJAX";
	target = "form1:htmlSakiKmkName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	args['code2'] = document.getElementById("form1:htmlSakiKmkCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function confirmOk() {
	document.getElementById('form1:htmlConfirmFlg').value = "1";
	indirectClick('search');
}
function confirmCancel() {
	document.getElementById('form1:htmlConfirmFlg').value = "2";
	indirectClick('search');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_onload(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea02001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea02001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea02001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea02001.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="新規登録"
				styleClass="commandExButton" id="register" action="#{pc_Kea02001.doRegisterAction}" tabindex="25"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblKaikeiNendoHenseiJotai"
										value="#{pc_Kea02001.propKaikeiNendoHenseiJotai.name}"
										style="#{pc_Kea02001.propKaikeiNendoHenseiJotai.labelStyle}"></h:outputText></TH>
									<TD width="450"><h:outputText styleClass="outputText"
										id="htmlKaikeiNendoHenseiJotai"
										value="#{pc_Kea02001.propKaikeiNendoHenseiJotai.stringValue}"
										style="#{pc_Kea02001.propKaikeiNendoHenseiJotai.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2"><h:outputText
										styleClass="outputText" id="lblhenkoMoto" value="変更元"></h:outputText></TH>
									<TD class="group_item"></TD>
								</TR>
								<TR>
									<TH class="group_label" width="20"></TH>
									<TH class="v_b" width="130"><h:outputText
										styleClass="outputText" id="lblMotoYsnTCd"
										value="#{pc_Kea02001.propMotoYsnTCd.labelName}"
										style="#{pc_Kea02001.propMotoYsnTCd.labelStyle}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlMotoYsnTCd"
												size="14" value="#{pc_Kea02001.propMotoYsnTCd.stringValue}"
												style="#{pc_Kea02001.propMotoYsnTCd.style}" tabindex="1"
												maxlength="#{pc_Kea02001.propMotoYsnTCd.maxLength}"
												disabled="#{pc_Kea02001.propMotoYsnTCd.disabled}"
												readonly="#{pc_Kea02001.propMotoYsnTCd.readonly}" onblur="return func_1(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchMotoYsnTCd"
												tabindex="2" action="#{pc_Kea02001.doSearchMotoYsnTCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMotoYsnTName" 
												value="#{pc_Kea02001.propMotoYsnTName.stringValue}" 
												style="#{pc_Kea02001.propMotoYsnTName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblMotoMokuCd"
										value="#{pc_Kea02001.propMotoMokuCd.labelName}"
										style="#{pc_Kea02001.propMotoMokuCd.labelStyle}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlMotoMokuCd"
												size="14" tabindex="3"
												value="#{pc_Kea02001.propMotoMokuCd.stringValue}"
												style="#{pc_Kea02001.propMotoMokuCd.style}"
												maxlength="#{pc_Kea02001.propMotoMokuCd.maxLength}"
												disabled="#{pc_Kea02001.propMotoMokuCd.disabled}"
												readonly="#{pc_Kea02001.propMotoMokuCd.readonly}" onblur="return func_3(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchMotoMokuCd"
												tabindex="4" action="#{pc_Kea02001.doSearchMotoMokuCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMotoMokuName" 
												value="#{pc_Kea02001.propMotoMokuName.stringValue}" 
												style="#{pc_Kea02001.propMotoMokuName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_bottom"></TH>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblMotoKmkCd"
										value="#{pc_Kea02001.propMotoKmkCd.labelName}"
										style="#{pc_Kea02001.propMotoKmkCd.labelStyle}"></h:outputText></TH>
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlMotoKmkCd" 
												size="14" tabindex="5" value="#{pc_Kea02001.propMotoKmkCd.stringValue}" 
												style="#{pc_Kea02001.propMotoKmkCd.style}" 
												maxlength="#{pc_Kea02001.propMotoKmkCd.maxLength}"
												disabled="#{pc_Kea02001.propMotoKmkCd.disabled}"
												readonly="#{pc_Kea02001.propMotoKmkCd.readonly}" onblur="return func_5(this, event);"></h:inputText><hx:commandExButton type="submit" styleClass="commandExButton_search" 
												id="searchMotoKmkCd" tabindex="6" action="#{pc_Kea02001.doSearchMotoKmkCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMotoKmkName" 
												value="#{pc_Kea02001.propMotoKmkName.stringValue}" 
												style="#{pc_Kea02001.propMotoKmkName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2"><h:outputText
										styleClass="outputText" id="lblhenkoSaki" value="変更先"></h:outputText></TH>
									<TD class="group_item"></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_e"><h:outputText styleClass="outputText" id="lblSakiYsnTCd" 
									value="#{pc_Kea02001.propSakiYsnTCd.labelName}" 
									style="#{pc_Kea02001.propSakiYsnTCd.labelStyle}"></h:outputText></TH>
									
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlSakiYsnTCd" 
												size="14" value="#{pc_Kea02001.propSakiYsnTCd.stringValue}" 
												style="#{pc_Kea02001.propSakiYsnTCd.style}" tabindex="7" 
												maxlength="#{pc_Kea02001.propSakiYsnTCd.maxLength}"
												disabled="#{pc_Kea02001.propSakiYsnTCd.disabled}"
												readonly="#{pc_Kea02001.propSakiYsnTCd.readonly}" onblur="return func_2(this, event);"></h:inputText><hx:commandExButton type="submit" styleClass="commandExButton_search" 
												id="searchSakiYsnTCd" tabindex="8" action="#{pc_Kea02001.doSearchSakiYsnTCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlSakiYsnTName" 
												value="#{pc_Kea02001.propSakiYsnTName.stringValue}" 
												style="#{pc_Kea02001.propSakiYsnTName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_f"><h:outputText styleClass="outputText" id="lblSakiMokuCd" 
									value="#{pc_Kea02001.propSakiMokuCd.labelName}" 
									style="#{pc_Kea02001.propSakiMokuCd.labelStyle}"></h:outputText></TH>
									
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlSakiMokuCd" 
												size="14" value="#{pc_Kea02001.propSakiMokuCd.stringValue}" 
												style="#{pc_Kea02001.propSakiMokuCd.style}" tabindex="9"
												maxlength="#{pc_Kea02001.propSakiMokuCd.maxLength}"
												disabled="#{pc_Kea02001.propSakiMokuCd.disabled}"
												readonly="#{pc_Kea02001.propSakiMokuCd.readonly}" onblur="return func_4(this, event);"></h:inputText><hx:commandExButton type="submit" styleClass="commandExButton_search" 
												id="searchSakiMokuCd" tabindex="10" action="#{pc_Kea02001.doSearchSakiMokuCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlSakiMokuName" 
												value="#{pc_Kea02001.propSakiMokuName.stringValue}" 
												style="#{pc_Kea02001.propSakiMokuName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_bottom"></TH>
									<TH class="v_g"><h:outputText styleClass="outputText" id="lblSakiKmkCd" 
									value="#{pc_Kea02001.propSakiKmkCd.labelName}" 
									style="#{pc_Kea02001.propSakiKmkCd.labelStyle}"></h:outputText></TH>
									
									<TD width="650" nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlSakiKmkCd" 
												size="14" tabindex="11" value="#{pc_Kea02001.propSakiKmkCd.stringValue}" 
												style="#{pc_Kea02001.propSakiKmkCd.style}" 
												maxlength="#{pc_Kea02001.propSakiKmkCd.maxLength}"
												disabled="#{pc_Kea02001.propSakiKmkCd.disabled}"
												readonly="#{pc_Kea02001.propSakiKmkCd.readonly}" onblur="return func_6(this, event);"></h:inputText><hx:commandExButton type="submit" styleClass="commandExButton_search" 
												id="searchSakiKmkCd" tabindex="12" action="#{pc_Kea02001.doSearchSakiKmkCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlSakiKmkName" 
												value="#{pc_Kea02001.propSakiKmkName.stringValue}" 
												style="#{pc_Kea02001.propSakiKmkName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_a" colspan="2"><h:outputText
										styleClass="outputText" id="lblShinseiNo"
										value="#{pc_Kea02001.propShinseiNoFrom.labelName}"
										style="#{pc_Kea02001.propShinseiNoFrom.labelStyle}"></h:outputText></TH>
									<TD colspan="">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD width="130" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiNoFrom" size="14"
													disabled="#{pc_Kea02001.propShinseiNoFrom.disabled}"
													readonly="#{pc_Kea02001.propShinseiNoFrom.readonly}"
													style="#{pc_Kea02001.propShinseiNoFrom.style}"
													value="#{pc_Kea02001.propShinseiNoFrom.integerValue}"
													tabindex="13">
													<f:convertNumber pattern="####0;####0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TD width="25" class="clear_border"><h:outputText
													styleClass="outputText" id="lblFromTo1" value="～"></h:outputText></TD>
												<TD width="150" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiNoTo" size="14"
													disabled="#{pc_Kea02001.propShinseiNoTo.disabled}"
													readonly="#{pc_Kea02001.propShinseiNoTo.readonly}"
													style="#{pc_Kea02001.propShinseiNoTo.style}"
													value="#{pc_Kea02001.propShinseiNoTo.integerValue}"
													tabindex="14">
													<f:convertNumber pattern="####0;####0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblShinseiDate"
										value="#{pc_Kea02001.propShinseiDateFrom.name}"
										style="#{pc_Kea02001.propShinseiDateFrom.labelStyle}"></h:outputText></TH>
									<TD colspan="">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD width="130" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiDateFrom" size="14"
													disabled="#{pc_Kea02001.propShinseiDateFrom.disabled}"
													readonly="#{pc_Kea02001.propShinseiDateFrom.readonly}"
													style="#{pc_Kea02001.propShinseiDateFrom.style}"
													value="#{pc_Kea02001.propShinseiDateFrom.dateValue}"
													tabindex="15">
													<f:convertDateTime pattern="yyyy/MM/dd" />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TD width="25" class="clear_border"><h:outputText
													styleClass="outputText" id="lblFromTo2" value="～"></h:outputText></TD>
												<TD width="180" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiDateTo" size="14"
													disabled="#{pc_Kea02001.propShinseiDateTo.disabled}"
													readonly="#{pc_Kea02001.propShinseiDateTo.readonly}"
													style="#{pc_Kea02001.propShinseiDateTo.style}"
													value="#{pc_Kea02001.propShinseiDateTo.dateValue}"
													tabindex="16">
													<f:convertDateTime pattern="yyyy/MM/dd" />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblShinseiUser"
										value="#{pc_Kea02001.propShinseiUser1.labelName}"
										style="#{pc_Kea02001.propShinseiUser1.labelStyle}"></h:outputText></TH>
									<TD colspan="">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser1" size="14"
													disabled="#{pc_Kea02001.propShinseiUser1.disabled}"
													readonly="#{pc_Kea02001.propShinseiUser1.readonly}"
													style="#{pc_Kea02001.propShinseiUser1.style}"
													value="#{pc_Kea02001.propShinseiUser1.stringValue}"
													tabindex="17"
													maxlength="#{pc_Kea02001.propShinseiUser1.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser2" size="14"
													disabled="#{pc_Kea02001.propShinseiUser2.disabled}"
													readonly="#{pc_Kea02001.propShinseiUser2.readonly}"
													style="#{pc_Kea02001.propShinseiUser2.style}"
													value="#{pc_Kea02001.propShinseiUser2.stringValue}"
													tabindex="18"
													maxlength="#{pc_Kea02001.propShinseiUser2.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser3" size="14"
													disabled="#{pc_Kea02001.propShinseiUser3.disabled}"
													readonly="#{pc_Kea02001.propShinseiUser3.readonly}"
													style="#{pc_Kea02001.propShinseiUser3.style}"
													value="#{pc_Kea02001.propShinseiUser3.stringValue}"
													tabindex="19"
													maxlength="#{pc_Kea02001.propShinseiUser3.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser4" size="14"
													disabled="#{pc_Kea02001.propShinseiUser4.disabled}"
													readonly="#{pc_Kea02001.propShinseiUser4.readonly}"
													style="#{pc_Kea02001.propShinseiUser4.style}"
													value="#{pc_Kea02001.propShinseiUser4.stringValue}"
													tabindex="20"
													maxlength="#{pc_Kea02001.propShinseiUser4.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser5" size="14"
													disabled="#{pc_Kea02001.propShinseiUser5.disabled}"
													readonly="#{pc_Kea02001.propShinseiUser5.readonly}"
													style="#{pc_Kea02001.propShinseiUser5.style}"
													value="#{pc_Kea02001.propShinseiUser5.stringValue}"
													tabindex="21"
													maxlength="#{pc_Kea02001.propShinseiUser5.maxLength}">
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblHyojiKbn"
										value="#{pc_Kea02001.propHyojiKbn.name}"
										style="#{pc_Kea02001.propHyojiKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox setWidth" 
										id="htmlHyojiKbn" tabindex="22"
										value="#{pc_Kea02001.propHyojiKbn.value}"
										disabled="#{pc_Kea02001.propHyojiKbn.disabled}"
										readonly="#{pc_Kea02001.propHyojiKbn.readonly}">
										<f:selectItems value="#{pc_Kea02001.propHyojiKbn.list}" />
									</h:selectManyCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="検索" 
									styleClass="commandExButton_dat" id="search" tabindex="23" 
									action="#{pc_Kea02001.doSearchAction}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="クリア" 
									styleClass="commandExButton_etc" id="clear" 
									action="#{pc_Kea02001.doClearAction}" tabindex="24"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Kea02001.propKaikeiNendoHidden.stringValue}"
				id="htmlKaikeiNendoHidden"></h:inputHidden>
			<h:inputHidden value="#{pc_Kea02001.propConfirmFlg.integerValue}"
				id="htmlConfirmFlg">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

