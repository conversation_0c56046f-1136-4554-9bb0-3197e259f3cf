<%-- 
	学生自由設定内容一括登録
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xra00801.onPageLoadBegin}">
<h:form styleClass="form" id="form1">
	
<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cob00201.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xra00801.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xra00801.screenName}"></h:outputText>
</div>	
	
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">

<!-- ↓ここにコンポーネントを配置 -->
<TABLE width="650" border="0" cellpadding="3" cellspacing="0">
	<TBODY>
		<TR>
			<TD align="center">
			<TABLE class="table" width="650">
				<TBODY>
					<TR>
						<TH class="v_a" width="150">
						<h:outputText styleClass="outputText" id="lblFile"
							value="入力ファイル"
							style="#{pc_Xra00801.propInputFile.labelStyle}"></h:outputText><BR>
						<h:outputText styleClass="outputText" id="lblOldFile" value="（前回ファイル）"></h:outputText>
						</TH>
						<TD nowrap width="500">
						<hx:fileupload styleClass="fileupload" id="htmlInputFile"
										value="#{pc_Xra00801.propInputFile.value}"
										style="width: 495px">
										<hx:fileProp name="fileName"
											value="#{pc_Xra00801.propInputFile.fileName}" />
										<hx:fileProp name="contentType"
											value="#{pc_Xra00801.propInputFile.contentType}" />
									</hx:fileupload><BR>
						<h:outputText styleClass="outputText" id="htmlInputFileOld"
							value="#{pc_Xra00801.propInputFileOld.stringValue}"></h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="150">
						<h:outputText styleClass="outputText" id="lblDataInsertKbn" value="データ登録区分指定"></h:outputText>
						</TH>
						<TD nowrap width="500"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRegKbn"
										layout="pageDirection"
										value="#{pc_Xra00801.propRegKbn.stringValue}">
										<f:selectItem itemValue="1"
											itemLabel="データを登録します。同一データが存在すれば、エラーとなります。" />
										<f:selectItem itemValue="2"
											itemLabel="データを登録または更新します。同一データが存在すれば、上書き更新します。" />
										<f:selectItem itemValue="3"
											itemLabel="データを更新します。入力ファイルに指定された項目のみ更新します。" />
									</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH class="v_c" width="150">
						<h:outputText styleClass="outputText" id="lblProcessKbn" value="処理区分指定"></h:outputText>
						</TH>
						<TD nowrap width="500">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
						 						 value="#{pc_Xra00801.propSyoriKbn.checked}">
						</h:selectBooleanCheckbox>チェックのみ（データの登録/更新は行いません）</TD>
					</TR>
					<TR>
						<TH rowspan="3" class="v_d" width="150">
						<h:outputText styleClass="outputText" id="lblChekListOutput" value="チェックリスト出力指定"></h:outputText>
						</TH>
						<TD style="border-top-style:none;border-bottom-style:none" width="500">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
						    id="htmlChkListNormal" value="#{pc_Xra00801.propChkListNormal.checked}"></h:selectBooleanCheckbox>
						正常データ</TD>
					</TR>
					<TR>
						<TD style="border-top-style:none;border-bottom-style:none" width="500">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlChkListError" value="#{pc_Xra00801.propChkListError.checked}"></h:selectBooleanCheckbox>
						エラーデータ</TD>
					</TR>
					<TR>
						<TD style="border-top-style:none" width="500"><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlChkListWarning" value="#{pc_Xra00801.propChkListWarning.checked}"></h:selectBooleanCheckbox>
						ワーニングデータ</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE class="button_bar" align="center" cellspacing="1" cellpadding="1" width="700">
				<TR>
					<TD align="center">
						<hx:commandExButton	type="submit" value="実　行"
						styleClass="commandExButton_dat" id="exec"
						action="#{pc_Xra00801.doExecAction}" confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>

</DIV>
</DIV>
</DIV>

<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />		
			
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
