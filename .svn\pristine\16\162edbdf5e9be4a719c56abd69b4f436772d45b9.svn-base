<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsz01901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pc_Nsz01901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlEntry').value = "1";
	indirectClick('register');
}
function confirmCancel() {
	document.getElementById('form1:htmlEntry').value = "0";
}			
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsz01901.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsz01901.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsz01901.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsz01901.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<TABLE border="0" cellpadding="0" cellspacing="0" width="620px">
	<TBODY>
		<TR>
			<TD>
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="500px">
					<TBODY>
						<TR>
							<TH class="v_a" width="150" height="14"><h:outputText
								styleClass="outputText" id="lblNyushiNendo"
								value="#{pc_Nsz01901.propNyushiNendo.labelName}"
								style="#{pc_Nsz01901.propNyushiNendo.labelStyle}"></h:outputText></TH>
							<TD width="100" height="14"><h:inputText
								styleClass="inputText" id="htmlNyushiNendo" size="4"
								disabled="#{pc_Nsz01901.propNyushiNendo.disabled}"
								readonly="#{pc_Nsz01901.propNyushiNendo.readonly}"
								value="#{pc_Nsz01901.propNyushiNendo.dateValue}"
								style="#{pc_Nsz01901.propNyushiNendo.style}" tabindex="1">
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<f:convertDateTime pattern="yyyy" />
							</h:inputText></TD>
							<TH width="150" class="v_b" height="14"><h:outputText
								styleClass="outputText" id="lblNyushiGakkiNo"
								value="#{pc_Nsz01901.propNyushiGakkiNo.labelName}"
								style="#{pc_Nsz01901.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
							<TD width="100" height="14"><h:inputText
								styleClass="inputText" id="htmlNyushiGakkiNo" size="2"
								disabled="#{pc_Nsz01901.propNyushiGakkiNo.disabled}"
								readonly="#{pc_Nsz01901.propNyushiGakkiNo.readonly}"
								tabindex="2"
								maxlength="#{pc_Nsz01901.propNyushiGakkiNo.maxLength}"
								style="#{pc_Nsz01901.propNyushiGakkiNo.style}"
								value="#{pc_Nsz01901.propNyushiGakkiNo.integerValue}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<f:convertNumber pattern="#0" />
							</h:inputText></TD>
						</TR>
					</TBODY>
				</TABLE>
			</TD>
			<TD>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="120px">
					<TBODY>
						<TR>
							<TD width="20" class="clear_border" height="14"></TD>
							<TD width="40" class="clear_border" height="14"><hx:commandExButton
								type="submit" value="選択" styleClass="commandExButton"
								id="select" action="#{pc_Nsz01901.doSelectAction}"
								disabled="#{pc_Nsz01901.propSelect.disabled}" tabindex="3"></hx:commandExButton></TD>
							<TD width="60" class="clear_border" height="14"><hx:commandExButton
								type="submit" value="解除" styleClass="commandExButton"
								id="reset" disabled="#{pc_Nsz01901.propReset.disabled}" tabindex="4" action="#{pc_Nsz01901.doResetAction}"></hx:commandExButton></TD>
						</TR>
					</TBODY>
				</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>
<TABLE border="0" width="620px">
	<TBODY>
		<TR>
			<TD></TD>
			<TD align="right" width="620px"><h:outputText
				styleClass="outputText"
				value="#{pc_Nsz01901.propCount.stringValue}"
				style="font-size: 8pt"></h:outputText><h:outputText
				styleClass="outputText" id="htmlCountlbl" value="件"
				style="font-size: 8pt"></h:outputText></TD>
		</TR>
	</TBODY>
</TABLE>
<TABLE border="0" width="665px">
	<TBODY>
		<TR>
			<TD width="45"></TD>
			<TD>
			<DIV class="listScroll" id="listScroll" onscroll="setScrollPosition('scroll',this);" style="height:148px;width:637px;">
			<h:dataTable border="0" cellpadding="2" cellspacing="0"
				headerClass="headerClass" footerClass="footerClass"
				rowClasses="#{pc_Nsz01901.propSenKmk.rowClasses}"
				styleClass="meisai_scroll" id="table1"
				value="#{pc_Nsz01901.propSenKmk.list}" var="varlist"
				width="620px">
				<h:column id="column1">
					<f:facet name="header">
						<h:outputText id="text11" styleClass="outputText" value="コード"></h:outputText>
					</f:facet>
					<f:attribute value="70" name="width" />
					<h:outputText styleClass="likeOutput" id="htmlCd"
						value="#{varlist.kamokCd}"></h:outputText>
				</h:column>
				<h:column id="column2">
					<f:facet name="header">
						<h:outputText styleClass="likeOutput" value="名称" id="text1"></h:outputText>
					</f:facet>
					<f:attribute value="210" name="width" />
					<h:outputText styleClass="likeOutput" id="htmlNA"
						value="#{varlist.kamokName}"></h:outputText>
				</h:column>

				<h:column id="column3">
					<f:facet name="header">
						<h:outputText styleClass="outputText" value="略称" id="text2"></h:outputText>
					</f:facet>
					<h:outputText styleClass="likeOutput" id="htmlKNR"
						value="#{varlist.kamokNameRyak}"></h:outputText>
					<f:attribute value="75" name="width" />
				</h:column>
				<h:column id="column4">
					<f:facet name="header">
						<h:outputText styleClass="outputText" value="番号" id="text3"></h:outputText>
					</f:facet>
					<h:outputText styleClass="likeOutput" id="htmlCKN"
						value="#{varlist.ctrKamokNo}"></h:outputText>
					<f:attribute value="70" name="width" />
					<f:attribute value="text-align: right" name="style" />
				</h:column>

				<h:column id="column5">
					<f:facet name="header">
						<h:outputText styleClass="outputText" value="請求" id="text4"></h:outputText>
					</f:facet>
					<f:attribute value="70" name="width" />
					<f:attribute value="text-align: center; vertical-align: middle"
						name="style" />
					<h:outputText styleClass="outputText" id="text6"
						value="#{varlist.ctrDaibetSeiKbn}"></h:outputText>
				</h:column>
				<h:column id="column6">
					<f:facet name="header">
						<h:outputText styleClass="outputText" value="リスニング" id="text5"></h:outputText>
					</f:facet>
					<f:attribute value="95" name="width" />
					<f:attribute value="text-align: center; vertical-align: middle"
						name="style" />
					<h:outputText styleClass="outputText" id="text7"
						value="#{varlist.lsnKmkFlg}"></h:outputText>
				</h:column>

				<h:column id="column7">
					<f:facet name="header">
					</f:facet>
					<hx:commandExButton type="submit" value="選択"
						styleClass="commandExButton" id="selectKmk"
						action="#{pc_Nsz01901.doSelectKmkAction}" tabindex="5"></hx:commandExButton>
					<f:attribute value="30" name="width" />
				</h:column>
			</h:dataTable></DIV></TD>
			<TD></TD>
		</TR>
	</TBODY>
</TABLE>
<TABLE border="0" class="table" cellspacing="0" cellpadding="0" width="620" style="margin-top:10px;">
	<TBODY>
		<TR>
			<TH class="v_a" width="200"><h:outputText
				styleClass="outputText" id="lblKamokCd"
				value="#{pc_Nsz01901.propKamokCd.labelName}"
				style="#{pc_Nsz01901.propKamokCd.labelStyle}"></h:outputText></TH>

			<TD width="410"><h:inputText styleClass="inputText"
				id="htmlKamokCd"
				value="#{pc_Nsz01901.propKamokCd.stringValue}" size="3"
				style="#{pc_Nsz01901.propKamokCd.style}"
				maxlength="#{pc_Nsz01901.propKamokCd.max}" tabindex="6"
				disabled="#{pc_Nsz01901.propKamokName.disabled}"
				readonly="#{pc_Nsz01901.propKamokName.readonly}">
				<hx:inputHelperAssist errorClass="inputText_Error" />
			</h:inputText></TD>
		</TR>
		<TR>

			<TH class="v_b" width="200"><h:outputText
				styleClass="outputText" id="lblKamokName"
				value="#{pc_Nsz01901.propKamokName.labelName}"
				style="#{pc_Nsz01901.propKamokName.labelStyle}"></h:outputText></TH>

			<TD width="410">
				<h:inputText styleClass="inputText"	id="htmlKamokName"
				value="#{pc_Nsz01901.propKamokName.stringValue}" size="20"
				style="#{pc_Nsz01901.propKamokName.style}"
				maxle	ngth="#{pc_Nsz01901.propKamokName.maxLength}" tabindex="7">		
					<hx:inputHelperAssist errorClass="inputText_Error" />	
				</h:inputText>
			</TD>
		</TR>
		<TR>
			<TH class="v_c" width="200"><h:outputText
				styleClass="outputText" id="lblKamokNameRyak"
				value="#{pc_Nsz01901.propKamokNameRyak.labelName}"></h:outputText></TH>

			<TD width="410"><h:inputText styleClass="inputText"
				id="htmlKamokNameRyak"
				value="#{pc_Nsz01901.propKamokNameRyak.stringValue}" size="6"
				style="#{pc_Nsz01901.propKamokNameRyak.style}"
				maxlength="#{pc_Nsz01901.propKamokNameRyak.maxLength}"
				tabindex="8"
				disabled="#{pc_Nsz01901.propKamokNameRyak.disabled}"
				readonly="#{pc_Nsz01901.propKamokNameRyak.readonly}">
			</h:inputText></TD>
		</TR>
		<TR>
			<TH class="v_d" width="200"><h:outputText
				styleClass="outputText" id="lblCtrKamokNo"
				value="#{pc_Nsz01901.propCtrKamokNo.labelName}"></h:outputText></TH>

			<TD width="410"><h:inputText styleClass="inputText"
				id="htmlCtrKamokNo"
				value="#{pc_Nsz01901.propCtrKamokNo.integerValue}" size="2"
				style="#{pc_Nsz01901.propCtrKamokNo.style}"
				maxlength="#{pc_Nsz01901.propCtrKamokNo.max}" tabindex="9"
				disabled="#{pc_Nsz01901.propCtrKamokNo.disabled}"
				readonly="#{pc_Nsz01901.propCtrKamokNo.readonly}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertNumber pattern="#0"/></h:inputText></TD>
		</TR>
		<TR>
			<TH class="v_e" width="200"><h:outputText
				styleClass="outputText" id="lblCtrDaibetSeiKbn"
				value="#{pc_Nsz01901.propCtrDaibetSeiKbn.labelName}"></h:outputText></TH>

			<TD width="410"><h:selectBooleanCheckbox
				styleClass="selectBooleanCheckbox" id="htmlCtrDaibetSeiKbn"
				value="#{pc_Nsz01901.propCtrDaibetSeiKbn.checked}"
				tabindex="10"
				disabled="#{pc_Nsz01901.propCtrDaibetSeiKbn.disabled}"
				readonly="#{pc_Nsz01901.propCtrDaibetSeiKbn.readonly}"></h:selectBooleanCheckbox></TD>
		</TR>
		<TR>
			<TH class="v_f" width="200"><h:outputText
				styleClass="outputText" id="lblLsnKmkFlg"
				value="#{pc_Nsz01901.propLsnKmkFlg.labelName}"></h:outputText></TH>

			<TD width="410"><h:selectBooleanCheckbox
				styleClass="selectBooleanCheckbox" id="htmlLsnKmkFlg"
				value="#{pc_Nsz01901.propLsnKmkFlg.checked}" tabindex="11"
				readonly="#{pc_Nsz01901.propLsnKmkFlg.readonly}"
				disabled="#{pc_Nsz01901.propLsnKmkFlg.disabled}"></h:selectBooleanCheckbox></TD>
		</TR>
	</TBODY>
</TABLE>
<TABLE align="right" class="button_bar" width="100%">
	<TBODY>
		<TR>
			<TD nowrap align="center"><hx:commandExButton type="submit"
				value="コピー" styleClass="commandExButton_etc" id="copy"
				action="#{pc_Nsz01901.doCopyAction}" tabindex="12"
				disabled="#{pc_Nsz01901.propCopy.disabled}"></hx:commandExButton><hx:commandExButton
				type="submit" styleClass="commandExButton_dat" id="register"
				value="確定" action="#{pc_Nsz01901.doRegisterAction}"
				confirm="#{msg.SY_MSG_0003W}" tabindex="13"
				disabled="#{pc_Nsz01901.propRegister.disabled}"></hx:commandExButton><hx:commandExButton
				type="submit" styleClass="commandExButton_dat" id="delete"
				value="削除" action="#{pc_Nsz01901.doDeleteAction}"
				confirm="#{msg.SY_MSG_0004W}" tabindex="14"
				disabled="#{pc_Nsz01901.propDelete.disabled}"></hx:commandExButton><hx:commandExButton
				type="submit" value="クリア" styleClass="commandExButton_etc"
				id="clear" action="#{pc_Nsz01901.doClearAction}" tabindex="15"
				disabled="#{pc_Nsz01901.propClear.disabled}"></hx:commandExButton></TD>
		</TR>
	</TBODY>
</TABLE>
<BR><!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Nsz01901.propSenKmk.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Nsz01901.propEntry.integerValue}"
				id="htmlEntry">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

