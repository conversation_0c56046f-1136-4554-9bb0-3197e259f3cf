<%-- 
	クラス編成（編成処理）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob01002.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01002.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableUpdateHidden').value = "1";
	indirectClick('kakutei');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableUpdateHidden').value = "0";
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cob01002.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cob01002.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Cob01002.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Cob01002.screenName}"></h:outputText>
</div>			

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
	styleClass="commandExButton" id="returnDisp"
	action="#{pc_Cob01002.doReturnAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR align="center">
									<TD>
										<!-- 対象学生一覧 -->
										<TABLE border="0" cellpadding="0" cellspacing="0" width="300">
											<TBODY>
												<TR>
													<TH align="left" nowrap width="100%">
														<h:outputText styleClass="outputText" id="lblGakseiList"
													value="対象学生"></h:outputText></TH>
													<TH align="right" nowrap width="100%"><h:outputText
														styleClass="outputText" id="lblGakseiListCnt" 
														value="#{pc_Cob01002.propGakseiList.listCount}件"></h:outputText>
													</TH>
												</TR>
												<TR>
													<TD colspan="2"><h:selectManyListbox
														styleClass="selectManyListbox" id="htmlGakseiList" size="23"
														style="width: 100%" disabled="#{pc_Cob01002.propGakseiList.disabled}"
														value="#{pc_Cob01002.propGakseiList.integerValue}">
														<f:selectItems value="#{pc_Cob01002.propGakseiList.list}" />
													</h:selectManyListbox></TD>
												</TR>
											</TBODY>
										</TABLE>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="300">
											<TBODY>
												<TR align="left">
													<TD class="clear_border"><h:outputText
														styleClass="outputText" id="text1" value="（複数選択可）"></h:outputText></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="100">
											<TBODY>
												<TR align="center">
													<TD align="center" width="536"><hx:commandExButton
													type="submit" value="全て選択 →"
													styleClass="commandExButton" style="width: 100px"
													id="selectAll" action="#{pc_Cob01002.doSelectAllAction}"></hx:commandExButton><BR><BR>
														<hx:commandExButton	type="submit" value="選択 →" styleClass="commandExButton" style="width: 100px"
														id="select" action="#{pc_Cob01002.doSelectAction}"></hx:commandExButton><BR><BR>
														<hx:commandExButton	type="submit" value="← 解除" styleClass="commandExButton" style="width: 100px"
														id="remove" action="#{pc_Cob01002.doRemoveAction}"></hx:commandExButton><BR><BR>
														<hx:commandExButton type="submit" value="← 全て解除"
														styleClass="commandExButton" style="width: 100px"
														id="removeAll" action="#{pc_Cob01002.doRemoveAllAction}"></hx:commandExButton></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
									<TD>
										<!-- 割当学生一覧 -->
										<TABLE border="0" cellpadding="0" cellspacing="0" width="300">
											<TBODY>
												<TR>
													<TH align="left" nowrap width="100%">
														<h:outputText styleClass="outputText"
															id="lblWariGakseiList" value="割当学生"></h:outputText>
													</TH>
													<TH align="right" nowrap width="100%">
														<h:outputText styleClass="outputText" id="lblWariGakseiListCnt" 
														value="#{pc_Cob01002.propWariGakseiList.listCount}件"></h:outputText>
													</TH>
												</TR>
												<TR>
													<TD colspan="2"><h:selectManyListbox
														styleClass="selectManyListbox" id="htmlWariGakseiList" size="23"
														style="width: 100%" disabled="#{pc_Cob01002.propWariGakseiList.disabled}"
														value="#{pc_Cob01002.propWariGakseiList.integerValue}">
														<f:selectItems value="#{pc_Cob01002.propWariGakseiList.list}" /></h:selectManyListbox>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="300">
											<TBODY>
												<TR align="left">
													<TD class="clear_border"><h:outputText
														styleClass="outputText" id="text2" value="（複数選択可）"></h:outputText></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR><BR>
						<TABLE width="755" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD>
									<TABLE class="table" width="100%">
										<TBODY>
											<TR>
												<TH nowrap class="v_a"  width="130">
												<!-- 処理対象 -->
													<h:outputText styleClass="outputText" id="lblShoriKbn" 
													value="#{pc_Cob01002.propShoriKbn.labelName}"
													style="#{pc_Cob01002.propShoriKbn.labelStyle}"></h:outputText></TH>
												<TD width="500"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlShoriKbn"
													value="#{pc_Cob01002.propShoriKbn.value}"
													disabled="#{pc_Cob01002.propShoriKbn.disabled}">
													<f:selectItem itemValue="0" itemLabel="割当" />
													<f:selectItem itemValue="1" itemLabel="割当取消" />
												</h:selectOneRadio>
												</TD>									
											</TR>
											<TR>
												<TH nowrap class="v_b">
												<!-- クラス種別 -->
													<h:outputText styleClass="outputText" id="lblClsSbt" 
													value="#{pc_Cob01002.propClsSbtCd.labelName}"
													style="#{pc_Cob01002.propClsSbtCd.labelStyle}"></h:outputText></TH>
												<TD><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlClsSbtCd"
													disabled="#{pc_Cob01002.propClsSbtCd.disabled}"
													value="#{pc_Cob01002.propClsSbtCd.value}"
													style="width:155px;"
													required="#{pc_Cob01002.propClsSbtCd.required}">
													<f:selectItems value="#{pc_Cob01002.propClsSbtCd.list}" />
												</h:selectOneMenu>
													<hx:commandExButton type="submit" styleClass="commandExButton"
													id="clsSbtSelect" value="選択"
													disabled="#{pc_Cob01002.propClsSbtCd.disabled}"
													action="#{pc_Cob01002.doClsSbtSelectAction}"></hx:commandExButton>
													<hx:commandExButton	type="submit" styleClass="commandExButton"
													id="clsSbtUnselect" value="解除"
													disabled="#{pc_Cob01002.propClsCd.disabled}"
													action="#{pc_Cob01002.doClsSbtUnselectAction}"></hx:commandExButton>
											</TR>
											<TR>
												<TH nowrap class="v_c">
												<!-- クラス -->
													<h:outputText styleClass="outputText" id="lblCls" 
													value="#{pc_Cob01002.propClsCd.labelName}"
													style="#{pc_Cob01002.propClsCd.labelStyle}"></h:outputText></TH>
												<TD><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlClsCd" disabled="#{pc_Cob01002.propClsCd.disabled}"
													value="#{pc_Cob01002.propClsCd.value}" style="width:175px;"
													required="#{pc_Cob01002.propClsCd.required}">
													<f:selectItems value="#{pc_Cob01002.propClsCd.list}" />
												</h:selectOneMenu></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE width="755" cellspacing="1" cellpadding="1" class="button_bar">
							<TBODY>
								<TR align="right">
									<TD align="center"><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="kakutei"
										action="#{pc_Cob01002.doKakuteiAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Cob01002.propExecutableUpdateHidden.integerValue}" id="htmlExecutableUpdateHidden"><f:convertNumber /></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
