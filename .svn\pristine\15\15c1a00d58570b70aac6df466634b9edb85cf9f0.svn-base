<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="COB_CGKS" name="カリキュラム学科組織" prod_id="CO" description="カリキュラム学科組織の情報です。">
<STATMENT><![CDATA[
(SELECT CGKS.CUR_GAKKA_CD CUR_GAKKA_CD, CASE CGKS.CUR_GAKKA_LVL WHEN 1 THEN CGKS1.CUR_GAKKA_CD WHEN 2 THEN CGKS2.CUR_GAKKA_CD WHEN 3 THEN CGKS3.CUR_GAKKA_CD WHEN 4 THEN CGKS4.CUR_GAKKA_CD WHEN 5 THEN CGKS5.CUR_GAKKA_CD WHEN 6 THEN CGKS6.CUR_GAKKA_CD END CUR_GAKKA_CD1, CASE CGKS.CUR_GAKKA_LVL WHEN 2 THEN CGKS1.CUR_GAKKA_CD WHEN 3 THEN CGKS2.CUR_GAKKA_CD WHEN 4 THEN CGKS3.CUR_GAKKA_CD WHEN 5 THEN CGKS4.CUR_GAKKA_CD WHEN 6 THEN CGKS5.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD2, CASE CGKS.CUR_GAKKA_LVL WHEN 3 THEN CGKS1.CUR_GAKKA_CD WHEN 4 THEN CGKS2.CUR_GAKKA_CD WHEN 5 THEN CGKS3.CUR_GAKKA_CD WHEN 6 THEN CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD3, CASE CGKS.CUR_GAKKA_LVL WHEN 4 THEN CGKS2.CUR_GAKKA_CD WHEN 5 THEN CGKS3.CUR_GAKKA_CD WHEN 6 THEN CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD4, CASE CGKS.CUR_GAKKA_LVL WHEN 5 THEN CGKS3.CUR_GAKKA_CD WHEN 6 THEN CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD5, CASE CGKS.CUR_GAKKA_LVL WHEN 6 THEN CGKS4.CUR_GAKKA_CD ELSE '' END CUR_GAKKA_CD6, LTRIM(COALESCE(CGKS6.CUR_GAKKA_NAME,'') || ' ' || COALESCE(CGKS5.CUR_GAKKA_NAME,'') || ' ' || COALESCE(CGKS4.CUR_GAKKA_NAME,'') || ' ' || COALESCE(CGKS3.CUR_GAKKA_NAME,'') || ' ' || COALESCE(CGKS2.CUR_GAKKA_NAME,'') || ' ' || COALESCE(CGKS1.CUR_GAKKA_NAME,'')) FULL_NAME, CASE WHEN CGKS1.CUR_GAKKA_LVL = 1 THEN CGKS1.CUR_GAKKA_NAME ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 1 THEN CGKS2.CUR_GAKKA_NAME ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 1 THEN CGKS3.CUR_GAKKA_NAME ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 1 THEN CGKS4.CUR_GAKKA_NAME ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 1 THEN CGKS5.CUR_GAKKA_NAME ELSE CGKS6.CUR_GAKKA_NAME END END END END END CUR_GAKKA_NAME1, CASE WHEN CGKS1.CUR_GAKKA_LVL = 2 THEN CGKS1.CUR_GAKKA_NAME ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 2 THEN CGKS2.CUR_GAKKA_NAME ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 2 THEN CGKS3.CUR_GAKKA_NAME ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 2 THEN CGKS4.CUR_GAKKA_NAME ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 2 THEN CGKS5.CUR_GAKKA_NAME ELSE CGKS6.CUR_GAKKA_NAME END END END END END CUR_GAKKA_NAME2, CASE WHEN CGKS1.CUR_GAKKA_LVL = 3 THEN CGKS1.CUR_GAKKA_NAME ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 3 THEN CGKS2.CUR_GAKKA_NAME ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 3 THEN CGKS3.CUR_GAKKA_NAME ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 3 THEN CGKS4.CUR_GAKKA_NAME ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 3 THEN CGKS5.CUR_GAKKA_NAME ELSE CGKS6.CUR_GAKKA_NAME END END END END END CUR_GAKKA_NAME3, CASE WHEN CGKS1.CUR_GAKKA_LVL = 4 THEN CGKS1.CUR_GAKKA_NAME ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 4 THEN CGKS2.CUR_GAKKA_NAME ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 4 THEN CGKS3.CUR_GAKKA_NAME ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 4 THEN CGKS4.CUR_GAKKA_NAME ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 4 THEN CGKS5.CUR_GAKKA_NAME ELSE CGKS6.CUR_GAKKA_NAME END END END END END CUR_GAKKA_NAME4, CASE WHEN CGKS1.CUR_GAKKA_LVL = 5 THEN CGKS1.CUR_GAKKA_NAME ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 5 THEN CGKS2.CUR_GAKKA_NAME ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 5 THEN CGKS3.CUR_GAKKA_NAME ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 5 THEN CGKS4.CUR_GAKKA_NAME ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 5 THEN CGKS5.CUR_GAKKA_NAME ELSE CGKS6.CUR_GAKKA_NAME END END END END END CUR_GAKKA_NAME5, CASE WHEN CGKS1.CUR_GAKKA_LVL = 6 THEN CGKS1.CUR_GAKKA_NAME ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 6 THEN CGKS2.CUR_GAKKA_NAME ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 6 THEN CGKS3.CUR_GAKKA_NAME ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 6 THEN CGKS4.CUR_GAKKA_NAME ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 6 THEN CGKS5.CUR_GAKKA_NAME ELSE CGKS6.CUR_GAKKA_NAME END END END END END CUR_GAKKA_NAME6, LTRIM(COALESCE(CGKS6.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(CGKS5.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(CGKS4.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(CGKS3.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(CGKS2.CUR_GAKKA_NAME_RYAK,'') || ' ' || COALESCE(CGKS1.CUR_GAKKA_NAME_RYAK,'')) FULL_NAME_RYAK, CASE WHEN CGKS1.CUR_GAKKA_LVL = 1 THEN CGKS1.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 1 THEN CGKS2.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 1 THEN CGKS3.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 1 THEN CGKS4.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 1 THEN CGKS5.CUR_GAKKA_NAME_RYAK ELSE CGKS6.CUR_GAKKA_NAME_RYAK END END END END END CUR_GAKKA_NAME_RYAK1, CASE WHEN CGKS1.CUR_GAKKA_LVL = 2 THEN CGKS1.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 2 THEN CGKS2.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 2 THEN CGKS3.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 2 THEN CGKS4.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 2 THEN CGKS5.CUR_GAKKA_NAME_RYAK ELSE CGKS6.CUR_GAKKA_NAME_RYAK END END END END END CUR_GAKKA_NAME_RYAK2, CASE WHEN CGKS1.CUR_GAKKA_LVL = 3 THEN CGKS1.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 3 THEN CGKS2.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 3 THEN CGKS3.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 3 THEN CGKS4.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 3 THEN CGKS5.CUR_GAKKA_NAME_RYAK ELSE CGKS6.CUR_GAKKA_NAME_RYAK END END END END END CUR_GAKKA_NAME_RYAK3, CASE WHEN CGKS1.CUR_GAKKA_LVL = 4 THEN CGKS1.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 4 THEN CGKS2.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 4 THEN CGKS3.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 4 THEN CGKS4.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 4 THEN CGKS5.CUR_GAKKA_NAME_RYAK ELSE CGKS6.CUR_GAKKA_NAME_RYAK END END END END END CUR_GAKKA_NAME_RYAK4, CASE WHEN CGKS1.CUR_GAKKA_LVL = 5 THEN CGKS1.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 5 THEN CGKS2.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 5 THEN CGKS3.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 5 THEN CGKS4.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 5 THEN CGKS5.CUR_GAKKA_NAME_RYAK ELSE CGKS6.CUR_GAKKA_NAME_RYAK END END END END END CUR_GAKKA_NAME_RYAK5, CASE WHEN CGKS1.CUR_GAKKA_LVL = 6 THEN CGKS1.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 6 THEN CGKS2.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 6 THEN CGKS3.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 6 THEN CGKS4.CUR_GAKKA_NAME_RYAK ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 6 THEN CGKS5.CUR_GAKKA_NAME_RYAK ELSE CGKS6.CUR_GAKKA_NAME_RYAK END END END END END CUR_GAKKA_NAME_RYAK6, LTRIM(COALESCE(CGKS6.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(CGKS5.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(CGKS4.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(CGKS3.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(CGKS2.CUR_GAKKA_NAME_ENG,'') || ' ' || COALESCE(CGKS1.CUR_GAKKA_NAME_ENG,'')) FULL_NAME_ENG, CASE WHEN CGKS1.CUR_GAKKA_LVL = 1 THEN CGKS1.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 1 THEN CGKS2.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 1 THEN CGKS3.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 1 THEN CGKS4.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 1 THEN CGKS5.CUR_GAKKA_NAME_ENG ELSE CGKS6.CUR_GAKKA_NAME_ENG END END END END END CUR_GAKKA_NAME_ENG1, CASE WHEN CGKS1.CUR_GAKKA_LVL = 2 THEN CGKS1.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 2 THEN CGKS2.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 2 THEN CGKS3.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 2 THEN CGKS4.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 2 THEN CGKS5.CUR_GAKKA_NAME_ENG ELSE CGKS6.CUR_GAKKA_NAME_ENG END END END END END CUR_GAKKA_NAME_ENG2, CASE WHEN CGKS1.CUR_GAKKA_LVL = 3 THEN CGKS1.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 3 THEN CGKS2.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 3 THEN CGKS3.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 3 THEN CGKS4.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 3 THEN CGKS5.CUR_GAKKA_NAME_ENG ELSE CGKS6.CUR_GAKKA_NAME_ENG END END END END END CUR_GAKKA_NAME_ENG3, CASE WHEN CGKS1.CUR_GAKKA_LVL = 4 THEN CGKS1.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 4 THEN CGKS2.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 4 THEN CGKS3.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 4 THEN CGKS4.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 4 THEN CGKS5.CUR_GAKKA_NAME_ENG ELSE CGKS6.CUR_GAKKA_NAME_ENG END END END END END CUR_GAKKA_NAME_ENG4, CASE WHEN CGKS1.CUR_GAKKA_LVL = 5 THEN CGKS1.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 5 THEN CGKS2.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 5 THEN CGKS3.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 5 THEN CGKS4.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 5 THEN CGKS5.CUR_GAKKA_NAME_ENG ELSE CGKS6.CUR_GAKKA_NAME_ENG END END END END END CUR_GAKKA_NAME_ENG5, CASE WHEN CGKS1.CUR_GAKKA_LVL = 6 THEN CGKS1.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS2.CUR_GAKKA_LVL = 6 THEN CGKS2.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS3.CUR_GAKKA_LVL = 6 THEN CGKS3.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS4.CUR_GAKKA_LVL = 6 THEN CGKS4.CUR_GAKKA_NAME_ENG ELSE CASE WHEN CGKS5.CUR_GAKKA_LVL = 6 THEN CGKS5.CUR_GAKKA_NAME_ENG ELSE CGKS6.CUR_GAKKA_NAME_ENG END END END END END CUR_GAKKA_NAME_ENG6, CGKS1.CUR_GAKKA_LVL, CGKS1.HIGH_CUR_GAKKA_CD, CGKS1.ROW_NO FROM COB_CGKS CGKS LEFT OUTER JOIN (SELECT A.CUR_GAKKA_CD TMP_CGKS_CD1, B.CUR_GAKKA_CD TMP_CGKS_CD2, C.CUR_GAKKA_CD TMP_CGKS_CD3, D.CUR_GAKKA_CD TMP_CGKS_CD4, E.CUR_GAKKA_CD TMP_CGKS_CD5, F.CUR_GAKKA_CD TMP_CGKS_CD6 FROM COB_CGKS A LEFT OUTER JOIN COB_CGKS B ON A.HIGH_CUR_GAKKA_CD = B.CUR_GAKKA_CD LEFT OUTER JOIN COB_CGKS C ON B.HIGH_CUR_GAKKA_CD = C.CUR_GAKKA_CD LEFT OUTER JOIN COB_CGKS D ON C.HIGH_CUR_GAKKA_CD = D.CUR_GAKKA_CD LEFT OUTER JOIN COB_CGKS E ON D.HIGH_CUR_GAKKA_CD = E.CUR_GAKKA_CD LEFT OUTER JOIN COB_CGKS F ON E.HIGH_CUR_GAKKA_CD = F.CUR_GAKKA_CD) TMP_CGKS ON CGKS.CUR_GAKKA_CD = TMP_CGKS.TMP_CGKS_CD1 LEFT OUTER JOIN COB_CGKS CGKS1 ON CGKS1.CUR_GAKKA_CD = TMP_CGKS.TMP_CGKS_CD1 LEFT OUTER JOIN COB_CGKS CGKS2 ON CGKS2.CUR_GAKKA_CD = TMP_CGKS.TMP_CGKS_CD2 LEFT OUTER JOIN COB_CGKS CGKS3 ON CGKS3.CUR_GAKKA_CD = TMP_CGKS.TMP_CGKS_CD3 LEFT OUTER JOIN COB_CGKS CGKS4 ON CGKS4.CUR_GAKKA_CD = TMP_CGKS.TMP_CGKS_CD4 LEFT OUTER JOIN COB_CGKS CGKS5 ON CGKS5.CUR_GAKKA_CD = TMP_CGKS.TMP_CGKS_CD5 LEFT OUTER JOIN COB_CGKS CGKS6 ON CGKS6.CUR_GAKKA_CD = TMP_CGKS.TMP_CGKS_CD6)
]]></STATMENT>
<COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="配当対象となるカリキュラム学科組織コードが設定されます。"/><COLUMN id="CUR_GAKKA_CD1" name="第１レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="最上位の学科組織コード"/><COLUMN id="CUR_GAKKA_CD2" name="第２レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第２レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD3" name="第３レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第３レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD4" name="第４レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第４レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD5" name="第５レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第５レベルの学科組織コード"/><COLUMN id="CUR_GAKKA_CD6" name="第６レベル学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="第６レベルの学科組織コード"/><COLUMN id="FULL_NAME" name="カリキュラム学科組織名称" type="string" length="310" lengthDP="0" byteLength="930" description="カリキュラム学科組織の上位連結名称です。"/><COLUMN id="CUR_GAKKA_NAME1" name="第１レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル１の名称です。"/><COLUMN id="CUR_GAKKA_NAME2" name="第２レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル２の名称です。"/><COLUMN id="CUR_GAKKA_NAME3" name="第３レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル３の名称です。"/><COLUMN id="CUR_GAKKA_NAME4" name="第４レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル４の名称です。"/><COLUMN id="CUR_GAKKA_NAME5" name="第５レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="150" description="カリキュラム学科組織レベル５の名称です。"/><COLUMN id="CUR_GAKKA_NAME6" name="第６レベルカリキュラム学科組織名称" type="string" length="50" lengthDP="0" byteLength="50" description="カリキュラム学科組織レベル６の名称です。"/><COLUMN id="FULL_NAME_RYAK" name="カリキュラム学科組織略称" type="string" length="110" lengthDP="0" byteLength="330" description="カリキュラム学科組織の上位連結略称名称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK1" name="第１レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル１の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK2" name="第２レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル２の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK3" name="第３レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル３の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK4" name="第４レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル４の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK5" name="第５レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル５の略称です。"/><COLUMN id="CUR_GAKKA_NAME_RYAK6" name="第６レベルカリキュラム学科組織略称" type="string" length="16" lengthDP="0" byteLength="48" description="カリキュラム学科組織レベル６の略称です。"/><COLUMN id="FULL_NAME_ENG" name="カリキュラム学科組織英語名称" type="string" length="430" lengthDP="0" byteLength="430" description="カリキュラム学科組織の上位連結名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG1" name="第１レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル１の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG2" name="第２レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル２の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG3" name="第３レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル３の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG4" name="第４レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル４の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG5" name="第５レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル５の名称＿英語です。"/><COLUMN id="CUR_GAKKA_NAME_ENG6" name="第６レベルカリキュラム学科組織英語名称" type="string" length="70" lengthDP="0" byteLength="70" description="カリキュラム学科組織レベル６の名称＿英語です。"/><COLUMN id="CUR_GAKKA_LVL" name="カリキュラム学科組織レベル" type="number" length="1" lengthDP="0" byteLength="0" description="カリキュラム学科組織のレベルが設定されます。数字が小さい程、上位学科組織となります。"/><COLUMN id="HIGH_CUR_GAKKA_CD" name="上位カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="上位のカリキュラム学科組織コードが設定されます。"/><COLUMN id="ROW_NO" name="並び順ＮＯ" type="number" length="3" lengthDP="0" byteLength="0" description="カリキュラム学科組織の並び順が設定されます。"/>
</TABLE>
