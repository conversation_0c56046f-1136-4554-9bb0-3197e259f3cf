<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmd00101T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmd00101T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >  
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
    rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
    href="${pageContext.request.contextPath}/theme/stylesheet.css"
    title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

    <BODY>
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Kmd00101T01.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">
        <!-- ヘッダーインクルード -->
        <jsp:include page ="../inc/header.jsp" />
            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Kmd00101T01.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Kmd00101T01.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Kmd00101T01.screenName}"></h:outputText></div>
    <!--↓outer↓-->
    <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
            <TABLE>
                <TR>
                    <TD nowrap align="right"></TD>
                </TR>
            </TABLE>
            <!-- ↑ここに戻るボタンを配置 --></DIV>

            <!--↓content↓-->
            <DIV id="content">
            <DIV class="column" align="center">
            <TABLE border="0" cellpadding="5">
                <TBODY>
                    <TR>
                        <TD width="600" valign="top">
                            <!-- ↓タブ間共有テーブル↓ -->
                            <TABLE border="0" cellpadding="0" cellspacing="0" class="table">
                                <TBODY>
                                    <TR>
                                        <TH class="v_a" width="150">
                                            <h:outputText 
                                                styleClass="outputText" id="lblHyokaNendo"
                                                value="#{pc_Kmd00101T01.kmd00101.propHyokaNendo.labelName}"
                                                style="#{pc_Kmd00101T01.kmd00101.propHyokaNendo.labelStyle}">
                                            </h:outputText>
                                        </TH>
                                        <TD width="450">
                                            <h:inputText 
                                                id="htmlHyokaNendo" styleClass="inputText" 
                                                size="4" tabindex="1" 
                                                value="#{pc_Kmd00101T01.kmd00101.propHyokaNendo.dateValue}"
                                                readonly="#{pc_Kmd00101T01.kmd00101.propHyokaNendo.readonly}"
                                                disabled="#{pc_Kmd00101T01.kmd00101.propHyokaNendo.disabled}"
                                                style="#{pc_Kmd00101T01.kmd00101.propHyokaNendo.style}"
                                                maxlength="#{pc_Kmd00101T01.kmd00101.propHyokaNendo.maxLength}">
                                                <hx:inputHelperAssist errorClass="inputText_Error"
                                                imeMode="inactive" promptCharacter="_" />
                                                <f:convertDateTime pattern="yyyy" />
                                            </h:inputText>
                                        </TD>
                                    </TR>
                                    <TR>
                                        <TH class="v_b" width="150">
                                            <h:outputText 
                                                styleClass="outputText" id="lblHyokaGakkiNo"
                                                style="#{pc_Kmd00101T01.kmd00101.propHyokaGakkiNo.labelStyle}"
                                                value="#{pc_Kmd00101T01.kmd00101.propHyokaGakkiNo.labelName}">
                                            </h:outputText>
                                        </TH>
                                        <TD width="450">
                                            <h:inputText 
                                                id="htmlHyokaGakkiNo" styleClass="inputText" 
                                                size="4" tabindex="2" 
                                                value="#{pc_Kmd00101T01.kmd00101.propHyokaGakkiNo.integerValue}"
                                                readonly="#{pc_Kmd00101T01.kmd00101.propHyokaGakkiNo.readonly}"
                                                disabled="#{pc_Kmd00101T01.kmd00101.propHyokaGakkiNo.disabled}"
                                                style="#{pc_Kmd00101T01.kmd00101.propHyokaGakkiNo.style}"
                                                maxlength="#{pc_Kmd00101T01.kmd00101.propHyokaGakkiNo.maxLength}">
                                                <f:convertNumber type="number" pattern="#0"/>
                                                <hx:inputHelperAssist errorClass="inputText_Error"
                                                    promptCharacter="_" />
                                            </h:inputText>
                                        </TD>
                                    </TR>
                                    <TR>
                                        <TH class="v_c">
                                            <h:outputText 
                                                styleClass="outputText" id="lblOutputContents" 
                                                value="#{pc_Kmd00101T01.kmd00101.propOutputContents.labelName}" 
                                                style="#{pc_Kmd00101T01.kmd00101.propOutputContents.labelStyle}">
                                            </h:outputText>
                                        </TH>
                                        <TD>
                                            <h:selectOneRadio 
                                                disabledClass="selectOneRadio_Disabled"
                                                styleClass="selectOneRadio" id="htmlOutputContents" 
                                                layout="pageDirection" tabindex="3" 
                                                value="#{pc_Kmd00101T01.kmd00101.propOutputContents.value}"
                                                readonly="#{pc_Kmd00101T01.kmd00101.propOutputContents.readonly}"
                                                disabled="#{pc_Kmd00101T01.kmd00101.propOutputContents.disabled}"
                                                style="#{pc_Kmd00101T01.kmd00101.propOutputContents.style}">
                                                <f:selectItems
                                                    value="#{pc_Kmd00101T01.kmd00101.propOutputContents.list}" />
                                            </h:selectOneRadio>
                                        </TD>
                                    </TR>
                                    <TR>
                                        <TH class="v_d">
                                            <h:outputText 
                                                styleClass="outputText" id="lblPdfTitle" 
                                                value="#{pc_Kmd00101T01.kmd00101.propPdfTitle.labelName}" 
                                                style="#{pc_Kmd00101T01.kmd00101.propPdfTitle.labelStyle}">
                                            </h:outputText>
                                        </TH>
                                        <TD>
                                            <h:inputText 
                                                id="htmlPdfTitle" styleClass="inputText" 
                                                size="62" tabindex="4" 
                                                value="#{pc_Kmd00101T01.kmd00101.propPdfTitle.stringValue}"
                                                readonly="#{pc_Kmd00101T01.kmd00101.propPdfTitle.readonly}"
                                                disabled="#{pc_Kmd00101T01.kmd00101.propPdfTitle.disabled}" 
                                                style="#{pc_Kmd00101T01.kmd00101.propPdfTitle.style}"
                                                maxlength="#{pc_Kmd00101T01.kmd00101.propPdfTitle.maxLength}">
                                            </h:inputText>
                                        </TD>
                                    </TR>
                                </TBODY>
                            </TABLE>
                            <!-- ↑タブ間共有テーブル↑ -->
                            <BR>
                            <!-- ↓タブ用テーブル↓ -->
                            <TABLE border="0" cellpadding="20" cellspacing="0">
                                <TBODY>
                                    <TR>
                                        <TD width="600" align="left">
                                            <TABLE border="0" cellpadding="0" cellspacing="0">
                                                <TBODY>
                                                    <TR>
                                                        <TD class="tab_head_on">
                                                            <hx:commandExButton type="button"
                                                                value="一括指定" id="moveIkkatsuSiteiTab"
                                                                styleClass="tab_head_on"
                                                                tabindex="5">
                                                            </hx:commandExButton>
                                                        </TD>
                                                        <TD class="tab_head_off">
                                                            <hx:commandExButton type="submit" value="科目指定"
                                                                styleClass="tab_head_off" id="moveKamokuSiteiTab"
                                                                tabindex="6"
                                                                action="#{pc_Kmd00101T01.doMoveKamokuSiteiTabAction}">
                                                            </hx:commandExButton>
                                                        </TD>
                                                    </TR>
                                                </TBODY>
                                            </TABLE>
                                        </TD>
                                    </TR>
                                    <TR>
                                        <TD valign="top">
                                            <TABLE border="0" cellpadding="0" cellspacing="0"
                                                class="tab_body" width="100%">
                                                <TBODY>
                                                    <TR>
                                                        <TD valign="top" height="300">
                                                            <CENTER>
                                                                <BR>
                                                                <TABLE border="0" cellpadding="0" cellspacing="0"
                                                                    class="table">
                                                                    <TBODY>
                                                                        <TR>
                                                                            <TH class="v_a" width="150">
                                                                                <h:outputText 
                                                                                    styleClass="outputText" id="lblKanriBusyo" 
                                                                                    value="#{pc_Kmd00101T01.propKanriBusyo.labelName}" 
                                                                                    style="#{pc_Kmd00101T01.propKanriBusyo.labelStyle}">
                                                                                </h:outputText>
                                                                            </TH>
                                                                            <TD>
                                                                                <h:selectOneMenu styleClass="selectOneMenu"
                                                                id="htmlKanriBusyoCombo" tabindex="7"
                                                                value="#{pc_Kmd00101T01.propKanriBusyo.value}"
                                                                readonly="#{pc_Kmd00101T01.propKanriBusyo.readonly}"
                                                                disabled="#{pc_Kmd00101T01.propKanriBusyo.disabled}"
                                                                style="#{pc_Kmd00101T01.propKanriBusyo.style};width:410px">
                                                                <f:selectItems
                                                                    value="#{pc_Kmd00101T01.propKanriBusyo.list}" />
                                                            </h:selectOneMenu>
                                                                            </TD>
                                                                        </TR>
                                                                    </TBODY>
                                                                </TABLE>
                                                            </CENTER>
                                                        </TD>
                                                    </TR>
                                                    <TR>
                                                        <TD>
                                                            <TABLE border="0" cellspacing="0" class="button_bar" width="100%">
                                                                <TBODY>
                                                                    <TR>
                                                                        <TD width="" nowrap>
                                                                            <hx:commandExButton type="submit" value="PDF作成"
                                                                                styleClass="commandExButton_out" id="pdfOut"
                                                                                confirm="#{msg.SY_MSG_0019W}" tabindex="8"
                                                                                action="#{pc_Kmd00101T01.doPdfOutAction}">
                                                                            </hx:commandExButton>
                                                                            <hx:commandExButton type="submit" value="EXCEL作成"
                                                                                styleClass="commandExButton_out" id="excelOut"
                                                                                confirm="#{msg.SY_MSG_0027W}" tabindex="9"
                                                                                action="#{pc_Kmd00101T01.doExcelOutAction}">
                                                                            </hx:commandExButton>
                                                                            <hx:commandExButton type="submit" value="CSV作成"
                                                                                styleClass="commandExButton_out" id="csvOut"
                                                                                confirm="#{msg.SY_MSG_0020W}" tabindex="10"
                                                                                action="#{pc_Kmd00101T01.doCsvOutAction}">
                                                                            </hx:commandExButton>
                                                                            <hx:commandExButton type="submit" value="出力項目指定"
                                                                                styleClass="commandExButton_out" id="setOutput"
                                                                                tabindex="11" action="#{pc_Kmd00101T01.doSetOutputAction}">
                                                                            </hx:commandExButton>
                                                                        </TD>
                                                                    </TR>
                                                                </TBODY>
                                                            </TABLE>
                                                        </TD>
                                                    </TR>
                                                </TBODY>
                                            </TABLE>
                                        </TD>
                                    </TR>
                                </TBODY>
                            </TABLE>
                            <!-- ↑タブ用テーブル↑ -->
                        </TD>
                    </TR>
                </TBODY>
            </TABLE>
            </DIV>
            </DIV>
            
        </h:form></div>
        <!-- フッターインクルード -->
        <jsp:include page ="../inc/footer.jsp" />
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
    
</f:view>

</HTML>
