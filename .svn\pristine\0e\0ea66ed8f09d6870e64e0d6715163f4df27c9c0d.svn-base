<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsg00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsg00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>
<style type="text/css">
<!--
.setWidth TD {width: 430px; white-space: nowrap;}
-->
</style>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsg00401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsg00401.doCloseDispAction}"
></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsg00401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsg00401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="700">
								<TBODY>
									<TR>
										<TH class="v_a" width="152"><h:outputText
										styleClass="outputText" id="lblNyushiNendoHeader"
										value="#{pc_Nsg00401.propNyushiNendo.name}"></h:outputText></TH>
										<TD width="198"><h:outputText styleClass="outputText"
										id="lblNyushiNendo"
										value="#{pc_Nsg00401.propNyushiNendo.dateValue}">
										<f:convertDateTime pattern="yyyy" />
									</h:outputText></TD>
										<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblNyushiGakkiNoHeader"
										value="#{pc_Nsg00401.propNyushiGakkiNo.name}"></h:outputText></TH>
										<TD width="200"><h:outputText styleClass="outputText"
										id="lblNyushiGakkiNo"
										value="#{pc_Nsg00401.propNyushiGakkiNo.stringValue}"></h:outputText></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="700" style="margin-top:10px">
								<TBODY>
									<TR>
									<TH nowrap width="150" class="v_c"><h:outputText
										styleClass="outputText" id="lblInputFile" value="入力ファイル"
										style="#{pc_Nsg00401.propInputFile.labelStyle}"></h:outputText><BR>
									&nbsp;<h:outputText styleClass="outputText" id="lblInputFilePreL"
										value="(前回ファイル)"></h:outputText></TH>
									<TD  width="550"><hx:fileupload styleClass="fileupload"
											id="htmlInputFile" size="50" style="width:500px" value="#{pc_Nsg00401.propInputFile.value}">
											<hx:fileProp name="fileName" value="#{pc_Nsg00401.propInputFile.fileName}"/>
											<hx:fileProp name="contentType" value="#{pc_Nsg00401.propInputFile.contentType}"/>
										</hx:fileupload>
											<BR>
										&nbsp;&nbsp;<h:outputText styleClass="outputText" id="lblInputFilePre" value="#{pc_Nsg00401.propInputFilePre.stringValue}"></h:outputText><BR>
										</TD>
									</TR>
									<TR>
									<TH nowrap width="150" class="v_d"><h:outputText
										styleClass="outputText" id="lblDataKbnSitei" value="データ登録区分指定"></h:outputText></TH>
									<TD width="550" class="">
										<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio setWidth" id="htmlDataKbnSitei"
										layout="pageDirection"
										value="#{pc_Nsg00401.propDataKbnSitei.stringValue}"
										disabled="#{pc_Nsg00401.propDataKbnSitei.disabled}"
										readonly="#{pc_Nsg00401.propDataKbnSitei.readonly}"
										style="#{pc_Nsg00401.propDataKbnSitei.style}">
										<f:selectItem itemValue="1"
											itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
										<f:selectItem itemValue="4" itemLabel="入力ファイルに指定されたデータを削除します。" />
									</h:selectOneRadio>
									</TD>
									</TR>
									<TR>
									<TH nowrap width="150" class="v_e"><h:outputText
										styleClass="outputText" id="lblSyoryKbnSitei" value="処理区分指定"></h:outputText></TH>
									<TD width="550" class="">
										<h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox setWidth"
										id="htmlSyoiKbnSitei"
										disabled="#{pc_Nsg00401.propSyoriKbnSitei.disabled}"
										readonly="#{pc_Nsg00401.propSyoriKbnSitei.readonly}"
										style="#{pc_Nsg00401.propSyoriKbnSitei.style}"
										value="#{pc_Nsg00401.propSyoriKbnSitei.checked}">
									</h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblCheck"
										value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
									</TR>
									<TR>
									<TH class="v_f" nowrap width="150"><h:outputText
										styleClass="outputText" id="lblCheckList" value="チェックリスト出力指定"></h:outputText></TH>
									<TD height="300" width="550" class=""><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox setWidth" id="htmlCheckList"
										layout="pageDirection"
										value="#{pc_Nsg00401.propCheckList.stringValue}"
										disabled="#{pc_Nsg00401.propCheckList.disabled}"
										readonly="#{pc_Nsg00401.propCheckList.readonly}"
										style="#{pc_Nsg00401.propCheckList.style}">
										<f:selectItem itemValue="NORMAL" itemLabel="正常データ" />
										<f:selectItem itemValue="ERROR" itemLabel="エラーデータ" />
										<f:selectItem itemValue="WARNING" itemLabel="ワーニングデータ" />
									</h:selectManyCheckbox></TD>
										
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE border="0" width="700" class="button_bar">
								<TBODY>
									<TR>
										<TD>											
											<hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_dat" id="exec"
										disabled="#{pc_Nsg00401.propExec.disabled}" action="#{pc_Nsg00401.doExecAction}" confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

