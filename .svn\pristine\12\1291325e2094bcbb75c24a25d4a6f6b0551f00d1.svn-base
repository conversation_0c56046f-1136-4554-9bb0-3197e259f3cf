<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb12501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>日計表</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb12501.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Keb12501.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb12501.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb12501.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<CENTER>
		<TABLE border="0" 
			   class="table" 
			   width="700" 
			   cellspacing="0" 
			   cellpadding="0">
			<TBODY>
				<TR>
					<TH class="v_a" width="130px">
						<h:outputText id="lblKaikeiTani" 
									  styleClass="outputText" 
									  style="#{pc_Keb12501.propKaikeiTani.labelStyle}" 
									  value="#{pc_Keb12501.propKaikeiTani.labelName}">
						</h:outputText>
					</TH>
					<TD width="570px">
						<h:selectOneMenu styleClass="selectOneMenu" 
										 id="htmlKaikeiTani" 
										 value="#{pc_Keb12501.propKaikeiTani.value}" 
										 style="#{pc_Keb12501.propKaikeiTani.style}" 
										 disabled="#{pc_Keb12501.propKaikeiTani.disabled}" 
										 readonly="#{pc_Keb12501.propKaikeiTani.readonly}" 
										 rendered="#{pc_Keb12501.propKaikeiTani.rendered}">
							<f:selectItems value="#{pc_Keb12501.propKaikeiTani.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_b" width="">
						<h:outputText styleClass="outputText" 
									  id="lblSuitoDate" 
									  style="#{pc_Keb12501.propSuitoDate.labelStyle}" 
									  value="#{pc_Keb12501.propSuitoDate.name}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText styleClass="inputText" 
									 id="htmlSuitoDate" 
									 size="10" 
									 value="#{pc_Keb12501.propSuitoDate.dateValue}" 
									 style="#{pc_Keb12501.propSuitoDate.style}" 
									 disabled="#{pc_Keb12501.propSuitoDate.disabled}" 
									 readonly="#{pc_Keb12501.propSuitoDate.readonly}" 
									 rendered="#{pc_Keb12501.propSuitoDate.rendered}">
							<f:convertDateTime />
							<hx:inputHelperAssist errorClass="inputText_Error"
												  promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText>
					<h:outputText styleClass="outputText" id="htmlSetumei"
							value="　（入力日から５日間が出力対象）"></h:outputText></TD>
				</TR>
				<TR>
					<TH class="v_c" width="">
						<h:outputText styleClass="outputText" id="lblOutputKbn"
							value="伝票状態">
						</h:outputText>
					</TH>
					<TD>
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlSyonin" value="#{pc_Keb12501.propSyonin.checked}">
						</h:selectBooleanCheckbox>承認済
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlMisyonin" value="#{pc_Keb12501.propMisyonin.checked}">
						</h:selectBooleanCheckbox>未承認
					</TD>
				</TR>
			</TBODY>
		</TABLE><BR>
		<TABLE border="0" 
			   class="table" 
			   width="700" 
			   cellspacing="0" 
			   cellpadding="0">
			<TBODY>
				<TR>
					<TH class="v_d" width="130px">
						<h:outputText styleClass="outputText" 
									  id="lblTitle" 
									  style="#{pc_Keb12501.propTitle.labelStyle}" 
									  value="#{pc_Keb12501.propTitle.labelName}">
						</h:outputText>
					</TH>
					<TD width="570px">
						<h:inputText id="htmlTitle" 
									 styleClass="inputText" 
									 size="70" 
									 maxlength="#{pc_Keb12501.propTitle.maxLength}" 
									 value="#{pc_Keb12501.propTitle.stringValue}" 
									 style="#{pc_Keb12501.propTitle.style}" 
									 readonly="#{pc_Keb12501.propTitle.readonly}" 
									 disabled="#{pc_Keb12501.propTitle.disabled}" 
									 rendered="#{pc_Keb12501.propTitle.rendered}">
						</h:inputText>
					</TD>
				</TR>
			</TBODY>
		</TABLE><BR>
	</CENTER>
	<CENTER>
		<TABLE border="0" 
			   cellpadding="0" 
			   cellspacing="0" 
			   class="button_bar" 
			   width="700">
			<TBODY>
				<TR>
					<TD align="center" style="margin-top:10px;">
						<hx:commandExButton type="submit" 
											value="PDF作成" 
											styleClass="commandExButton_out" 
											id="pdfout" 
											rendered="#{pc_Keb12501.propPdfout.rendered}" 
											disabled="#{pc_Keb12501.propPdfout.disabled}" 
											style="#{pc_Keb12501.propPdfout.style}" 
											action="#{pc_Keb12501.doPdfoutAction}" 
											confirm="#{msg.SY_MSG_0019W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="EXCEL作成" 
											styleClass="commandExButton_out" 
											id="excelout" 
											rendered="#{pc_Keb12501.propExcelout.rendered}" 
											disabled="#{pc_Keb12501.propExcelout.disabled}" 
											style="#{pc_Keb12501.propExcelout.style}" 
											action="#{pc_Keb12501.doExceloutAction}" 
											confirm="#{msg.SY_MSG_0027W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="CSV作成" 
											styleClass="commandExButton_out" 
											id="csvout" 
											rendered="#{pc_Keb12501.propCsvout.rendered}" 
											disabled="#{pc_Keb12501.propCsvout.disabled}" 
											style="#{pc_Keb12501.propCsvout.style}" 
											action="#{pc_Keb12501.doCsvoutAction}" 
											confirm="#{msg.SY_MSG_0020W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="出力項目指定" 
											styleClass="commandExButton_out" 
											id="setoutput" 
											action="#{pc_Keb12501.doSetoutputAction}" 
											disabled="#{pc_Keb12501.propSetoutput.disabled}" 
											rendered="#{pc_Keb12501.propSetoutput.rendered}" 
											style="#{pc_Keb12501.propSetoutput.style}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="印刷" 
											styleClass="commandExButton_out" 
											id="print" 
											rendered="#{pc_Keb12501.propPrint.rendered}" 
											disabled="#{pc_Keb12501.propPrint.disabled}" 
											style="#{pc_Keb12501.propPrint.style}" 
											action="#{pc_Keb12501.doPrintAction}" 
											confirm="#{msg.SY_MSG_0022W}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</CENTER>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
