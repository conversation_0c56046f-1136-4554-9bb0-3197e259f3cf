<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghf00301.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<TITLE>Ghf00301.jsp</TITLE>
<SCRIPT type="text/javascript">

	window.attachEvent("onload", attachFormatNumber);

	function fncInit() {

//	▽▽ 2007-05-25 Tsutsumi 障害No 7200 対応　削除　START ▽▽
//		changeScrollPosition('scrollbun', 'listbunScroll');
//		changeScrollPosition('scrollhist', 'listhistScroll');
//	△△ 2007-05-25 Tsutsumi 障害No 7200 対応　削除 END △△　
		
		//読込専用項目
		document.getElementById('form1:htmlBankName').readOnly = true;
		document.getElementById('form1:htmlSitenName').readOnly = true;
		
		document.getElementById('form1:htmlName').readOnly = true;
		document.getElementById('form1:htmlGakunen').readOnly = true;
		document.getElementById('form1:htmlSemester').readOnly = true;
		document.getElementById('form1:htmlSzks').readOnly = true;
		document.getElementById('form1:htmlIdoInfo').readOnly = true;
		
		//ボタン使用可不可設定
    	activeKbn = document.getElementById('form1:htmlActiveControl').value;
    	
		if (activeKbn == "0") {
			document.getElementById('form1:regist').disabled = true;		//確定
			document.getElementById('form1:revoke').disabled = true;		//取消

		} else if (activeKbn == "1") {
			document.getElementById('form1:regist').disabled = false;		//確定
			document.getElementById('form1:revoke').disabled = true;		//取消
			
		} else if (activeKbn == "2") {
			document.getElementById('form1:regist').disabled = true;		//確定
			document.getElementById('form1:revoke').disabled = false;		//取消
			
		} else if (activeKbn == "3") {
			document.getElementById('form1:regist').disabled = false;		//確定
			document.getElementById('form1:revoke').disabled = false;		//取消
		}

//	▽▽ 2007-05-25 Tsutsumi 障害No 7200 対応　追加　START ▽▽
		window.attachEvent('onload', endload);		
//	△△ 2007-05-25 Tsutsumi 障害No 7200 対応　追加 END △△　
		
	}

//	▽▽ 2007-05-25 Tsutsumi 障害No 7200 対応　追加 START ▽▽
	function endload() {
		changeScrollPosition('scrollbun', 'listbunScroll');
		changeScrollPosition('scrollhist', 'listhistScroll');
	}
//	△△ 2007-05-25 Tsutsumi 障害No 7200 対応　追加 END △△　

	//学費学生検索画面へ遷移
	function openPGhz0301Window() {
		openPGhz0301("<%=com.jast.gakuen.rev.gh.PGhz0301.getWindowOpenOption() %>");
		 return true;
	}
	
	//学籍情報取得Ajax呼び出し
	function ajaxGakusekiCd(thisObj) {
		//学籍コード
		var gakusekiCd = document.getElementById('form1:htmlGakusekiCd');
		
		//学生氏名項目id
		var nameId = "form1:htmlName";
		//学年項目id
		var gakunenId = "form1:htmlGakunen";
		//セメスタ項目id
		var semesterId = "form1:htmlSemester";
		//所属学科組織項目id
		var szkGakkaId = "form1:htmlSzks";
		//異動情報項目id
		var idoInfoId = "form1:htmlIdoInfo";

		//学籍情報取得Ajax呼び出し
		funcAjaxGakusekiCd(thisObj, "1", nameId);
		//学年取得Ajax呼び出し
		funcAjaxGakunenAjax(gakusekiCd, "1", gakunenId);
		//セメスタ取得Ajax呼び出し
		funcAjaxSemesterAjax(gakusekiCd, "1", semesterId);		
		//所属学科組織名称取得Ajax呼び出し
		funcAjaxSgksName(gakusekiCd, "1", szkGakkaId);
		//異動情報取得Ajax呼び出し
		funcAjaxIdoInfoAjax(gakusekiCd, "1", idoInfoId);

		//学籍番号は初期化
		document.getElementById('form1:htmlGakusekiCd2').value = "";

		return true;
	}

	
	//金融機関取引口座検索画面へ遷移
	function openPCog1101Window() {
		openPCog1101("<%=com.jast.gakuen.rev.co.PCog1101.getWindowOpenOption() %>");
		return true;
	}
	
	//銀行名称取得・支店名称取得Ajax呼び出し
	function ajaxBankSitenCd() {
		//銀行コード
		var bankCd = document.getElementById('form1:htmlBankCd').value;
		//支店コード
		var sitenCd = document.getElementById('form1:htmlSitenCd').value;
		//銀行名称項目id
		var bankNameId = "form1:htmlBankName";
		//支店名称項目id
		var sitenNameId = "form1:htmlSitenName";
		
		//銀行名称取得Ajax呼び出し
		funcAjaxSetBankCd(bankCd, bankNameId)
		//支店名称取得Ajax呼び出し
		funcAjaxSetSitenCd(bankCd, sitenCd, sitenNameId)
		return true;
	}
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
	
		var procBunkatsuFind = document.getElementById('form1:htmlBunkatsuListFind').value;
		var procRegistFind = document.getElementById('form1:htmlRegistFind').value;

		//選択(上部)処理実行
		if(procBunkatsuFind == "1"){
			indirectClick('selectGakusei');
		}
		//確定処理実行
		if(procRegistFind == "1"){
			indirectClick('regist');
		}

	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlBunkatsuListFind').value = 0;
		document.getElementById('form1:htmlRegistFind').value = 0;
	}


</SCRIPT></HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY onload="fncInit();"><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghf00301.onPageLoadBegin}">

		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<DIV style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ghf00301.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Ghf00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ghf00301.screenName}"></h:outputText>
			           </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE width="900px">
							<TBODY>
								<TR align="center" valign="middle">
									<TD>
										<TABLE border="0" width="900px" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
													<TH nowrap class="v_a" align="center" width="150px">
														<h:outputText
															styleClass="outputText" id="lblGhNendo"
															value="#{pc_Ghf00301.propGhNendo.labelName}"
															style="#{pc_Ghf00301.propGhNendo.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="350px">
														<h:inputText styleClass="inputText"
															id="htmlGhNendo" value="#{pc_Ghf00301.propGhNendo.dateValue}"
															style="#{pc_Ghf00301.propGhNendo.style}"
															maxlength="#{pc_Ghf00301.propGhNendo.maxLength}" size="4"
															disabled="#{pc_Ghf00301.propGhNendo.disabled}" tabindex="1">
														<hx:inputHelperAssist imeMode="inactive"
															errorClass="inputText_Error" promptCharacter="_" />
														<f:convertDateTime pattern="yyyy" />
														</h:inputText>
													</TD>
													<TD rowspan="3" valign="middle" width="400px">
														<hx:commandExButton
															type="submit" value="選択" styleClass="cmdBtn_dat_s"
															id="selectGakusei" 
															disabled="#{pc_Ghf00301.propGhNendo.disabled}" 
															action="#{pc_Ghf00301.doSelectGakuseiAction}" tabindex="6">
														</hx:commandExButton>
														<hx:commandExButton type="submit" value="解除"
															styleClass="cmdBtn_etc_s" id="unselectGakusei" 
															disabled="#{!pc_Ghf00301.propGhNendo.disabled}" 
															action="#{pc_Ghf00301.doUnselectGakuseiAction}" tabindex="7">
														</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_b" align="left">
														<h:outputText
															styleClass="outputText" id="lblFurikomiIraiCd"
															value="#{pc_Ghf00301.propFurikomiIraiCd.labelName}"
															style="#{pc_Ghf00301.propFurikomiIraiCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left">
														<h:inputText styleClass="inputText"
															id="htmlFurikomiIraiCd"
															value="#{pc_Ghf00301.propFurikomiIraiCd.stringValue}"
															style="#{pc_Ghf00301.propFurikomiIraiCd.style}"
															maxlength="#{pc_Ghf00301.propFurikomiIraiCd.maxLength}" size="10"
															disabled="#{pc_Ghf00301.propFurikomiIraiCd.disabled}" tabindex="2">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH align="left" nowrap class="v_c">
														<h:outputText
															styleClass="outputText" id="lblGakusekiCd"
															value="#{pc_Ghf00301.propGakusekiCd.labelName}"
															style="#{pc_Ghf00301.propGakusekiCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left">
														<h:inputText styleClass="inputText"
															id="htmlGakusekiCd"
															value="#{pc_Ghf00301.propGakusekiCd.stringValue}"
															style="#{pc_Ghf00301.propGakusekiCd.style}"
															maxlength="#{pc_Ghf00301.propGakusekiCd.maxLength}" size="10" 
															onblur="return ajaxGakusekiCd(this);" 
															disabled="#{pc_Ghf00301.propGakusekiCd.disabled}" tabindex="3">
														</h:inputText>
														<hx:commandExButton
															type="submit" styleClass="commandExButton_search"
															disabled="#{pc_Ghf00301.propGhNendo.disabled}" 
															onclick="return openPGhz0301Window();" 
															id="paySearchGakusei" 
															action="#{pc_Ghf00301.doPaySearchGakuseiAction}" tabindex="4">
														</hx:commandExButton>
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlSgnContain"
															value="#{pc_Ghf00301.propSgnContain.checked}"
															disabled="#{pc_Ghf00301.propSgnContain.disabled}" tabindex="5">
														</h:selectBooleanCheckbox> <h:outputText
															styleClass="outputText" id="lblSgnContain" value="#{pc_Ghf00301.propSgnContain.labelName}"
															 >
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="10"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE border="0" width="900px" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
												<!-- 学生氏名 -->
													<TH align="center" width="150px" nowrap class="v_d">
														<h:outputText
															styleClass="outputText" id="lblNameLabel"
															value="#{pc_Ghf00301.propName.name}"
															style="#{pc_Ghf00301.propName.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" colspan="3" width="350px">
														<h:inputText styleClass="likeOutput"
															id="htmlName" style="#{pc_Ghf00301.propName.style}"
															size="40"
															value="#{pc_Ghf00301.propName.value}"
															tabindex="-1">
														</h:inputText>
													</TD>
													<!-- 学籍番号(表示用) -->
													<TH align="center" width="100px" nowrap class="v_d">
														<h:outputText
															styleClass="outputText" id="lblGakusekiCd2Label"
															value="#{pc_Ghf00301.propGakusekiCd2.name}"
															style="#{pc_Ghf00301.propGakusekiCd2.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="300px">
														<h:inputText styleClass="likeOutput"
															id="htmlGakusekiCd2" style="#{pc_Ghf00301.propGakusekiCd2.style}"
															size="10"
															value="#{pc_Ghf00301.propGakusekiCd2.value}"
															tabindex="-1">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<!-- 学年 -->
													<TH align="center" width="150px" nowrap class="v_e">
														<h:outputText
															styleClass="outputText" id="lblGakunen"
															value="#{pc_Ghf00301.propGakunen.name}"
															style="#{pc_Ghf00301.propGakunen.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="150px" >
														<h:inputText styleClass="likeOutput"
															id="htmlGakunen" style="#{pc_Ghf00301.propGakunen.style}"
															size="1"
															value="#{pc_Ghf00301.propGakunen.value}"
															tabindex="-1">
														</h:inputText>
													</TD>
													<!-- セメスタ -->
													<TH align="center" width="150px" nowrap class="v_f">
														<h:outputText
															styleClass="outputText" id="lblSemester"
															value="#{pc_Ghf00301.propSemester.name}"
															style="#{pc_Ghf00301.propSemester.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="200px" >
														<h:inputText styleClass="likeOutput"
															id="htmlSemester" style="#{pc_Ghf00301.propSemester.style}"
															size="1"
															value="#{pc_Ghf00301.propSemester.value}"
															tabindex="-1">
														</h:inputText>
													</TD>
													<!-- 異動情報 -->
													<TH align="center" width="100px" nowrap class="v_a">
														<h:outputText
															styleClass="outputText" id="lblIdoInfo"
															value="#{pc_Ghf00301.propIdoInfo.name}"
															style="#{pc_Ghf00301.propIdoInfo.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="150px" >
														<h:inputText styleClass="likeOutput"
															id="htmlIdoInfo" style="#{pc_Ghf00301.propIdoInfo.style}"
															size="50"
															value="#{pc_Ghf00301.propIdoInfo.value}"
															tabindex="-1">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<!-- 所属学科組織 -->
													<TH align="center" nowrap class="v_g" width="150px">
														<h:outputText
															styleClass="outputText" id="lblSgksNameRyakLabel"
															value="#{pc_Ghf00301.propSgksNameRyak.name}"
															style="#{pc_Ghf00301.propSgksNameRyak.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" colspan="5" width="750px">
														<h:inputText styleClass="likeOutput"
															id="htmlSzks" style="#{pc_Ghf00301.propSgksNameRyak.style}"
															size="120"
															value="#{pc_Ghf00301.propSgksNameRyak.value}"
															tabindex="-1">
														</h:inputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="900px" class="layoutTable">
											<TBODY>
												<TR>
													<TD align="right">
														<h:outputText styleClass="outputText"
															id="textCountBunkatsuGaku"
															value="#{pc_Ghf00301.propBunkatsuGakuList.listCount}">
														</h:outputText>
														<h:outputText
															styleClass="outputText" id="textKenBunkatsuGaku" value="件">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TD>
													<DIV style="height: 128px; width=100%;" id="listbunScroll" 
														onscroll="setScrollPosition('scrollbun',this);"
														class="listScroll"><h:dataTable
														headerClass="headerClass" footerClass="footerClass"
														styleClass="meisai_scroll" id="htmlBunkatsuGakuList"
														value="#{pc_Ghf00301.propBunkatsuGakuList.list}" var="varlist"
														width="885px" rows="#{pc_Ghf00301.propBunkatsuGakuList.rows}"
														rowClasses="#{pc_Ghf00301.propBunkatsuGakuList.rowClasses}">
														<h:column id="column1">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="#{pc_Ghf00301.propNendo.name}"
																	id="text1">
																</h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text41"
																value="#{varlist.nendo}"></h:outputText>
															<f:attribute value="60" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
														<h:column id="column2">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="納付金コード"
																	id="text2">
																</h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text42"
																value="#{varlist.payCd}"></h:outputText>
															<f:attribute value="80px" name="width" />
															<f:attribute value="text-align: left" name="style" />
														</h:column>
														<h:column id="column3">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="パターン"
																	id="text3">
																</h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text43"
																value="#{varlist.patternCd}"></h:outputText>
															<f:attribute value="60px" name="width" />
															<f:attribute value="text-align: left" name="style" />
														</h:column>
														<h:column id="column4">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="#{pc_Ghf00301.propBunnoKbnCd.name}"
																	id="text4">
																</h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text44"
																value="#{varlist.bunnoKbnCd}"></h:outputText>
															<f:attribute value="60px" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
														<h:column id="column5">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="#{pc_Ghf00301.propTblPayName.name}" 
																	id="text5">
																</h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text45"
																value="#{varlist.propPayName.displayValue}"
																title="#{varlist.propPayName.value}"></h:outputText>
															<f:attribute value="220px" name="width" />
															<f:attribute value="text-align: left" name="style" />
														</h:column>
														<h:column id="column6">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="#{pc_Ghf00301.propBunkatsuNo.name}"
																	id="text6">
																</h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text46"
																value="#{varlist.bunkatsuNo}"></h:outputText>
															<f:attribute value="55px" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
														<h:column id="column7">
															<f:facet name="header">
																<h:outputText styleClass="outputText" value="振込依頼人"
																	id="text7">
																</h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text47"
																value="#{varlist.furikomiIraiCd}"></h:outputText>
															<f:attribute value="90px" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
														<h:column id="column8">
															<f:facet name="header">
																<h:outputText id="text8" styleClass="outputText"
																	value="#{pc_Ghf00301.propTblSumYotyosyuGaku.name}"></h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text48"
																value="#{varlist.sumYotyosyuGaku}"></h:outputText>
															<f:attribute value="80px" name="width" />
															<f:attribute value="text-align: right" name="style" />
														</h:column>
														<h:column id="column9">
															<f:facet name="header">
																<h:outputText id="text9" styleClass="outputText"
																	value="#{pc_Ghf00301.propTblSumNyukinGaku.name}"></h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text49"
																value="#{varlist.sumNyukinGaku}"></h:outputText>
															<f:attribute value="70px" name="width" />
															<f:attribute value="text-align: right" name="style" />
														</h:column>
														<h:column id="column10">
															<f:facet name="header">
																<h:outputText id="text10" styleClass="outputText"
																	value="#{pc_Ghf00301.propTblSumHenkinGaku.name}"></h:outputText>
															</f:facet>								
															<h:outputText styleClass="outputText" id="text50"
																value="#{varlist.sumHenkinGaku}"></h:outputText>
															<f:attribute value="70px" name="width" />
															<f:attribute value="text-align: right" name="style" />
														</h:column>
														<h:column id="column11">
															<f:facet name="header">
															</f:facet>								
															<hx:commandExButton type="submit" value="選択"
																styleClass="cmdBtn_dat_s" id="listGakuSelect"
																rendered="#{varlist.rendered}"
																action="#{pc_Ghf00301.doListGakuSelectAction}" tabindex="8">
															</hx:commandExButton>
															<f:facet name="header"></f:facet>
															<f:attribute value="40px" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
													</h:dataTable></DIV>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="10"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="900px" border="0" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
													<TH align="left" width="80" nowrap class="v_b">
														<h:outputText
															styleClass="outputText" id="lblNendoLabel"
															value="#{pc_Ghf00301.propNendo.labelName}"
															style="#{pc_Ghf00301.propNendo.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="70">
														<h:outputText styleClass="outputText"
															id="lblNendo" value="#{pc_Ghf00301.propNendo.stringValue}"
															style="#{pc_Ghf00301.propNendo.labelStyle}">
														</h:outputText>
													</TD>
													<TH align="left" width="90" nowrap class="v_c">
														<h:outputText
															styleClass="outputText" id="lblPayCdLabel"
															value="#{pc_Ghf00301.propPayCd.labelName}"
															style="#{pc_Ghf00301.propPayCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="150">
														<h:outputText styleClass="outputText"
															id="lblPayCd" value="#{pc_Ghf00301.propPayCd.stringValue}"
															style="#{pc_Ghf00301.propPayCd.labelStyle}">
														</h:outputText>
													</TD>
													<TH align="left" width="90" nowrap class="v_d">
														<h:outputText
															styleClass="outputText" id="lblPatternCdLabel"
															value="#{pc_Ghf00301.propPatternCd.labelName}"
															style="#{pc_Ghf00301.propPatternCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="85">
														<h:outputText styleClass="outputText"
															id="lblPatternCd"
															value="#{pc_Ghf00301.propPatternCd.stringValue}"
															style="#{pc_Ghf00301.propPatternCd.labelStyle}">
														</h:outputText>
													</TD>
													<TH align="left" width="70" nowrap class="v_f">
														<h:outputText
															styleClass="outputText" id="lblBunkatsuNoLabel"
															value="#{pc_Ghf00301.propBunkatsuNo.labelName}"
															style="#{pc_Ghf00301.propBunkatsuNo.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="90">
														<h:outputText styleClass="outputText"
															id="lblBunkatsuNo"
															value="#{pc_Ghf00301.propBunkatsuNo.stringValue}"
															style="#{pc_Ghf00301.propBunkatsuNo.labelStyle}">
														</h:outputText>
													</TD>
													<TH align="left" width="105" nowrap class="v_f">
														<h:outputText
															styleClass="outputText" id="lblFurikomiIraiCdLabel"
															value="#{pc_Ghf00301.propTblFurikomiIraiCd.labelName}"
															style="#{pc_Ghf00301.propTblFurikomiIraiCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="100">
														<h:outputText styleClass="outputText"
															id="lblFurikomiIraiCdOut"
															value="#{pc_Ghf00301.propOutFurikomiIraiCd.stringValue}"
															style="#{pc_Ghf00301.propOutFurikomiIraiCd.labelStyle}">
														</h:outputText>
													</TD>

												</TR>
												<TR>
													<TH align="left" nowrap class="v_g">
														<h:outputText
															styleClass="outputText" id="lblPayNameLabel"
															value="#{pc_Ghf00301.propPayName.labelName}"
															style="#{pc_Ghf00301.propPayName.labelStyle}">
														</h:outputText>
													</TH>
													<TD colspan="3" align="left">
														<h:outputText styleClass="outputText"
															id="lblPayName" value="#{pc_Ghf00301.propPayName.stringValue}"
															style="#{pc_Ghf00301.propPayName.labelStyle}">
														</h:outputText>
													</TD>
													<TH align="left" nowrap class="v_a">
														<h:outputText
															styleClass="outputText" id="lblBunnoKbnNameLabel"
															value="#{pc_Ghf00301.propBunnoKbnName.labelName}"
															style="#{pc_Ghf00301.propBunnoKbnName.labelStyle}">
														</h:outputText>
													</TH>
													<TD colspan="3" align="left">
														<h:outputText styleClass="outputText"
															id="lblBunnoKbnName"
															value="#{pc_Ghf00301.propBunnoKbnName.stringValue}"
															style="#{pc_Ghf00301.propBunnoKbnName.labelStyle}">
														</h:outputText>
													</TD>
													<TH align="left" nowrap class="v_b">
														<h:outputText
															styleClass="outputText" id="lblZenkaiTyokaGakuLabel"
															value="#{pc_Ghf00301.propTyokaGaku.labelName}"
															style="#{pc_Ghf00301.propTyokaGaku.labelStyle}">
														</h:outputText>
													</TH>
													<TD  align="left">
														<h:outputText styleClass="outputText"
															id="lblTyokaGaku"
															value="#{pc_Ghf00301.propTyokaGaku.stringValue}"
															style="#{pc_Ghf00301.propTyokaGaku.labelStyle}">
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="900px" class="layoutTable">
											<TBODY>
												<TR>
													<TD align="right">
														<h:outputText styleClass="outputText"
															id="textCountNyukinHist"
															value="#{pc_Ghf00301.propNyukinHistList.listCount}">
														</h:outputText>
														<h:outputText
															styleClass="outputText" id="textKenNyukinHIst" value="件">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TD>
														<DIV style="height: 86px; width=100%;" id="listhistScroll" 
														onscroll="setScrollPosition('scrollhist',this);" 
														class="listScroll">
														<h:dataTable headerClass="headerClass" footerClass="footerClass"
														styleClass="meisai_scroll" id="htmlNyukinHistList"
														value="#{pc_Ghf00301.propNyukinHistList.list}" var="varlist"
														width="884px" rows="#{pc_Ghf00301.propNyukinHistList.rows}"
														rowClasses="#{pc_Ghf00301.propBunkatsuGakuList.rowClasses}">
														<h:column id="column21">
															<f:facet name="header">
																<h:outputText id="text51" styleClass="outputText"
																	value="#{pc_Ghf00301.propTblNyukinDate.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText" id="text61"
																value="#{varlist.nyukinDate}"></h:outputText>
															<f:attribute value="*" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
														<h:column id="column22">
															<f:facet name="header">
																<h:outputText id="text52" styleClass="outputText"
																	value="#{pc_Ghf00301.propTblNyukinGaku.name}"></h:outputText>				
															</f:facet>
															<h:outputText styleClass="outputText" id="text62"
																value="#{varlist.nyukinGaku}"></h:outputText>
															<f:attribute value="300px" name="width" />
															<f:attribute value="text-align: right" name="style" />
														</h:column>
														<h:column id="column23">
															<f:facet name="header">
																<h:outputText id="text53" styleClass="outputText"
																	value="#{pc_Ghf00301.propTblSelectKahi.name}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText" id="text63"
																value="#{varlist.okNg}"></h:outputText>
															<f:attribute value="300px" name="width" />
															<f:attribute value="text-align: left" name="style" />
														</h:column>
														<h:column id="column24">
															<f:facet name="header">
															
															</f:facet>
															<hx:commandExButton type="submit" value="選択"
																styleClass="cmdBtn_dat_s" id="listHistSelect"
																rendered="#{varlist.rendered}"
																action="#{pc_Ghf00301.doListHistSelectAction}" tabindex="9">
															</hx:commandExButton>
															<f:facet name="header"></f:facet>
															<f:attribute value="40px" name="width" />
															<f:attribute value="text-align: center" name="style" />
														</h:column>
													</h:dataTable></DIV></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="10"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="900px" border="0" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
													<TH width="100px" align="left" nowrap class="v_c">
														<h:outputText
															styleClass="outputText" id="lblNyukinDate"
															value="#{pc_Ghf00301.propNyukinDate.labelName}"
															style="#{pc_Ghf00301.propNyukinDate.labelStyle}">
														</h:outputText>
													</TH>
													<TD width="200px" align="left">
														<h:inputText styleClass="inputText"
															id="htmlNyukinDate"
															value="#{pc_Ghf00301.propNyukinDate.dateValue}"
															style="#{pc_Ghf00301.propNyukinDate.style}"
															disabled="#{pc_Ghf00301.propNyukinDate.disabled}" size="10"
															tabindex="10">
															<f:convertDateTime />
															<hx:inputHelperDatePicker />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
														</h:inputText>
													</TD>
													<TH width="110px" align="left" nowrap class="v_f">
														<h:outputText
															styleClass="outputText" id="lblBankCd"
															value="#{pc_Ghf00301.propBankCd.labelName}"
															style="#{pc_Ghf00301.propBankCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD width="430px" align="left" colspan="3">
														<h:inputText styleClass="inputText"
															id="htmlBankCd" value="#{pc_Ghf00301.propBankCd.stringValue}"
															style="#{pc_Ghf00301.propBankCd.style}"
															maxlength="#{pc_Ghf00301.propBankCd.maxLength}" 
															disabled="#{pc_Ghf00301.propBankCd.disabled}" 
															onblur="return ajaxBankSitenCd();" 
															size="4" tabindex="13">
														</h:inputText>
														<h:inputText styleClass="likeOutput" size="40"
															id="htmlBankName" tabindex="-1" 
															value="#{pc_Ghf00301.propBnkName.stringValue}"
															style="#{pc_Ghf00301.propBnkName.style}">
														</h:inputText>
													</TD>
													<TD width="60px" rowspan="6" align="left">
														<hx:commandExButton type="submit"
															styleClass="commandExButton_search" 
															disabled="#{pc_Ghf00301.propBankCd.disabled}" 
															id="paySearchBank" 
															onclick="return openPCog1101Window();" 
															action="#{pc_Ghf00301.doPopSearchBankAction}" tabindex="17">
														</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TH align="left" nowrap class="v_d">
														<h:outputText
															styleClass="outputText" id="lblNyukinGaku"
															value="#{pc_Ghf00301.propNyukinGaku.labelName}"
															style="#{pc_Ghf00301.propNyukinGaku.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left">
														<h:inputText styleClass="inputText"
															id="htmlNyukinGaku"
															value="#{pc_Ghf00301.propNyukinGaku.stringValue}"
															style="#{pc_Ghf00301.propNyukinGaku.style}" 
															disabled="#{pc_Ghf00301.propNyukinGaku.disabled}" 
															size="9" style="padding-right: 3px; text-align: right" tabindex="11">
														</h:inputText>
													</TD>
													<TH align="left" nowrap class="v_g">
														<h:outputText 
															styleClass="outputText" id="lblSitenCd"
															value="#{pc_Ghf00301.propSitenCd.labelName}"
															style="#{pc_Ghf00301.propSitenCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" colspan="3">
														<h:inputText styleClass="inputText"
															id="htmlSitenCd" value="#{pc_Ghf00301.propSitenCd.stringValue}"
															style="#{pc_Ghf00301.propSitenCd.style}"
															maxlength="#{pc_Ghf00301.propSitenCd.maxLength}" 
															disabled="#{pc_Ghf00301.propSitenCd.disabled}" 
															onblur="return ajaxBankSitenCd();" 
															size="3" tabindex="14">
														</h:inputText>
														<h:outputText
															value=" ">
														</h:outputText>
														<h:inputText styleClass="likeOutput" size="40"
															id="htmlSitenName" tabindex="-1" 
															value="#{pc_Ghf00301.propStnName.stringValue}"
															style="#{pc_Ghf00301.propStnName.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH rowspan="1" align="left" nowrap class="v_e">
														<h:outputText
															styleClass="outputText" id="lblNyukinHoho"
															value="#{pc_Ghf00301.propNyukinHoho.labelName}"
															style="#{pc_Ghf00301.propNyukinHoho.labelStyle}">
														</h:outputText>
													</TH>
													<TD rowspan="2" align="left">
														<h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlNyukinHoho"
															value="#{pc_Ghf00301.propNyukinHoho.stringValue}" 
															disabled="#{pc_Ghf00301.propNyukinHoho.disabled}" 
															style="#{pc_Ghf00301.propNyukinHoho.style}" tabindex="12">
															<f:selectItem itemValue="1" itemLabel="預金入金" />
															<f:selectItem itemValue="2" itemLabel="現金入金" />
														</h:selectOneRadio>
													</TD>
													<TH align="left" nowrap class="v_c">
														<h:outputText
															styleClass="outputText" id="lblYokinItemCd"
															value="#{pc_Ghf00301.propYokinItemCd.labelName}"
															style="#{pc_Ghf00301.propYokinItemCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left">
														<h:selectOneMenu styleClass="selectOneMenu"
															id="htmlYokinItem"
															style="#{pc_Ghf00301.propYokinItemCd.style}"
															disabled="#{pc_Ghf00301.propYokinItemCd.disabled}"
															value="#{pc_Ghf00301.propYokinItemCd.stringValue}" tabindex="15">
															<f:selectItems value="#{pc_Ghf00301.propYokinItemCd.list}" />
														</h:selectOneMenu>
													</TD>
													<TH align="left" nowrap class="v_d">
														<h:outputText
															styleClass="outputText" id="lblKouzaNo"
															value="#{pc_Ghf00301.propKouzaNo.labelName}"
															style="#{pc_Ghf00301.propKouzaNo.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left">
														<h:inputText styleClass="inputText"
															id="htmlKouzaNo" value="#{pc_Ghf00301.propKouzaNo.stringValue}"
															style="#{pc_Ghf00301.propKouzaNo.style}"
															maxlength="#{pc_Ghf00301.propKouzaNo.maxLength}" 
															disabled="#{pc_Ghf00301.propKouzaNo.disabled}" 
															size="10" tabindex="16">
														</h:inputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="900px" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton type="submit" value="確定"
															styleClass="commandExButton_dat" id="regist" 
															confirm="#{msg.SY_MSG_0001W}" 
															action="#{pc_Ghf00301.doRegistAction}" tabindex="18">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton type="submit" value="取消"
															styleClass="commandExButton_dat" id="revoke"
															confirm="#{msg.SY_MSG_0004W}" 
															action="#{pc_Ghf00301.doRevokeAction}" tabindex="19">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton type="submit"
															value="クリア" styleClass="commandExButton_etc" id="clear" 
															action="#{pc_Ghf00301.doClearAction}" tabindex="20">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
                </DIV>
                <!--↑CONTENT↑-->
            </DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden
			value="htmlNyukinGaku=###,###,###;"
			id="htmlFormatNumberOption"></h:inputHidden>

			<h:inputHidden
				value="#{pc_Ghf00301.propBunkatsuGakuList.scrollPosition}"
				id="scrollbun"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghf00301.propNyukinHistList.scrollPosition}"
				id="scrollhist"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghf00301.propBunkatsuListFind.integerValue}"
				id="htmlBunkatsuListFind"></h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghf00301.propRegistFind.integerValue}"
				id="htmlRegistFind"></h:inputHidden>
			<h:inputHidden value="#{pc_Ghf00301.propActiveControl.integerValue}" 
				id="htmlActiveControl">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Ghf00301.propScrollPos.stringValue}" 
				id="htmlScrollPos"></h:inputHidden>
				
			<h:inputHidden value="#{pc_Ghf00301.propSotuGakkiHidden.stringValue}" 
				id="htmlSotuGakkiHidden">
			</h:inputHidden>
		</h:form>
    </hx:scriptCollector>
    </BODY>
   	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
