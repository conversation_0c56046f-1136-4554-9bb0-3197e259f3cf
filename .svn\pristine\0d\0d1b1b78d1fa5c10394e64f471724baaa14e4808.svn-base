<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/PCoi0401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>部門検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<SCRIPT type="text/javascript">

function func_4(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
	document.getElementById("form1:htmlBmnCdListBox").selectedIndex=-1;
	// ｺﾝﾎﾞﾎﾞｯｸｽ用
	var cmbBox1 = document.getElementById("form1:htmlCampus");
	document.getElementById("form1:htmlHiddenCmbIndex1").value = 
		cmbBox1.options[cmbBox1.selectedIndex].value;
	
	return true;
}
function func_2(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
	
	// フォーカスを設定
	var target = document.getElementsByName("form1:htmlBmnTaishoDate");
	for (var i = 0;target.length > i;i++) {
		if (target[i].nodeName.toUpperCase() == 'INPUT') {
			target[i].focus();
			break;
		}
	}
	
	var retIdBmnCd = document.getElementById("form1:htmlHiddenBackIdBmnCd").value;
	var retIdBmnName = document.getElementById("form1:htmlHiddenBackIdBmnName").value;
	var listBox = document.getElementById("form1:htmlBmnCdListBox");
		
	if (window.opener) {
		if (listBox.selectedIndex != -1) {
			var sel = listBox.options[listBox.selectedIndex].value;
			if (sel != "|no select|") {
				
				// 部門コード　+　"|"　+　部門名称を"|"で区切る HEX-2023
				var ssn = sel.split("|");

				// 親ウィンドウのInputTextにコードをセット
				window.opener.document.getElementById(retIdBmnCd).value = ssn[0];
				window.opener.document.getElementById(retIdBmnCd).focus();
				window.opener.document.getElementById(retIdBmnCd).onblur();
				
				// キャンセルアクション呼び出しで、セッションクリアする
				indirectClick('cancel');
				
				return false;
			} else {
				var lst = new Array();
				lst[0]=('部門');
				setErrMsg( messageCreate( '<%=com.jast.gakuen.framework.util.UtilProperty.getMsgString("SY_MSG_0006E")%>', lst ) );
			
			}
		}
	}

	return false;

}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PCoi0401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page = "../../rev/inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_PCoi0401.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PCoi0401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PCoi0401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- レイアウト対応、全角スペース -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
			<DIV class="column"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" style=""
				class="table" width="732">
				<TBODY>
					<TR>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblBmnTaishoDate"
							value="#{pc_PCoi0401.propBmnTaishoDate.name}"
							style="#{pc_PCoi0401.propBmnTaishoDate.labelStyle}"></h:outputText></TH>
						<TD width="529"><h:inputText styleClass="inputText"
							id="htmlBmnTaishoDate"
							value="#{pc_PCoi0401.propBmnTaishoDate.dateValue}" size="10"
							disabled="#{pc_PCoi0401.propBmnTaishoDate.disabled}"
							rendered="#{pc_PCoi0401.propBmnTaishoDate.rendered}"
							readonly="#{pc_PCoi0401.propBmnTaishoDate.readonly}">
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText styleClass="outputText"
							id="lblBmnCd" value="#{pc_PCoi0401.propBmnCd.labelName}"
							style="#{pc_PCoi0401.propBmnCdListBox.style}">
						</h:outputText></TH>
						<TD width="529"><h:inputText styleClass="inputText" id="htmlBmnCd"
							value="#{pc_PCoi0401.propBmnCd.stringValue}" size="10"
							disabled="#{pc_PCoi0401.propBmnCd.disabled}"
							readonly="#{pc_PCoi0401.propBmnCd.readonly}"
							rendered="#{pc_PCoi0401.propBmnCd.rendered}"
							style="#{pc_PCoi0401.propBmnCd.style}"
							maxlength="#{pc_PCoi0401.propBmnCd.maxLength}">
						</h:inputText><h:outputText styleClass="outputText" value="（前方一致）"
							id="lblBmnCdFindType">
						</h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblBmnName" value="#{pc_PCoi0401.propBmnName.labelName}"
							style="#{pc_PCoi0401.propBmnName.style}">
						</h:outputText></TH>
						<TD width="529"><h:inputText styleClass="inputText"
							id="htmlBmnName" size="50"
							value="#{pc_PCoi0401.propBmnName.stringValue}"
							rendered="#{pc_PCoi0401.propBmnName.rendered}"
							disabled="#{pc_PCoi0401.propBmnName.disabled}"
							readonly="#{pc_PCoi0401.propBmnName.readonly}"
							style="#{pc_PCoi0401.propBmnName.style}"
							maxlength="#{pc_PCoi0401.propBmnName.maxLength}"></h:inputText><h:outputText
							styleClass="outputText" id="lblBmnNameFindType" value="（部分一致）"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblJoiBmnCd" value="#{pc_PCoi0401.propJoiBmnCd.labelName}"
							style="#{pc_PCoi0401.propJoiBmnCd.style}">
						</h:outputText></TH>
						<TD width="529"><h:inputText styleClass="inputText"
							id="htmlJoiBmnCd" size="10"
							value="#{pc_PCoi0401.propJoiBmnCd.stringValue}"
							style="#{pc_PCoi0401.propJoiBmnCd.style}"
							disabled="#{pc_PCoi0401.propJoiBmnCd.disabled}"
							readonly="#{pc_PCoi0401.propJoiBmnCd.readonly}"
							rendered="#{pc_PCoi0401.propJoiBmnCd.rendered}"
							maxlength="#{pc_PCoi0401.propJoiBmnCd.maxLength}"></h:inputText><h:outputText
							styleClass="outputText" id="lblJoiBmnCdFindType" value="（前方一致）"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_e"><h:outputText styleClass="outputText"
							id="lblBmnLevel" value="#{pc_PCoi0401.propBmnLevel.labelName}"
							style="#{pc_PCoi0401.propBmnName.style}"></h:outputText></TH>
						<TD width="529"><h:inputText styleClass="inputText"
							id="htmlBmnLevel"
							value="#{pc_PCoi0401.propBmnLevel.integerValue}" size="1"
							disabled="#{pc_PCoi0401.propBmnLevel.disabled}"
							maxlength="#{pc_PCoi0401.propBmnLevel.maxLength}"
							readonly="#{pc_PCoi0401.propBmnLevel.readonly}"
							rendered="#{pc_PCoi0401.propBmnLevel.rendered}"
							style="text-align: right">
							<f:convertNumber pattern="0;0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_f"><h:outputText styleClass="outputText"
							id="lblCampus" value="#{pc_PCoi0401.propCampus.name}"
							style="#{pc_PCoi0401.propCampus.style}"></h:outputText></TH>
						<TD width="529"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlCampus" value="#{pc_PCoi0401.propCampus.value}"
							style="#{pc_PCoi0401.propCampus.style}"
							disabled="#{pc_PCoi0401.propCampus.disabled}"
							readonly="#{pc_PCoi0401.propCampus.readonly}"
							rendered="#{pc_PCoi0401.propCampus.rendered}">
							<f:selectItems value="#{pc_PCoi0401.propCampus.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			<DIV align="right"></DIV>

			
			<TABLE border="0" cellpadding="0" cellspacing="0" width="732"
				style="margin-top:10px;" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search"
							style="#{pc_PCoi0401.propSeach.style}"
							action="#{pc_PCoi0401.doSearchAction}"
							onclick="return func_4(this, event);"
							disabled="#{pc_PCoi0401.propSeach.disabled}"
							rendered="#{pc_PCoi0401.propSeach.rendered}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_PCoi0401.doClearAction}"
							style="#{pc_PCoi0401.propClear.style}"
							rendered="#{pc_PCoi0401.propClear.rendered}"
							disabled="#{pc_PCoi0401.propClear.disabled}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE><TABLE border="0" width="732">
				<TBODY>
					<TR>
						<TD><TABLE border="0" width="100%"
							style="margin-top: 10px; text-align: left">
							<TBODY>
								<TR>
									<TH width="621"><h:outputText styleClass="outputText"
										id="lblBmnCdList" value="部門コード" style="margin-left:5px">
									</h:outputText><h:outputText styleClass="outputText"
										id="lblBmnNameList" value="部門名称" style="margin-left:45px">
									</h:outputText></TH>
									<TD width="96"><h:outputText styleClass="outputText"
										id="htmlListCnt"
										value="#{pc_PCoi0401.propListCount.stringValue}">
									</h:outputText><h:outputText styleClass="outputText"
										id="lblKen" value="件">
									</h:outputText></TD>
								</TR>
								<TR>
									<TD colspan="2"><h:selectOneListbox
										styleClass="selectOneListbox" id="htmlBmnCdListBox"
										style="height: 185px; width: 732px"
										value="#{pc_PCoi0401.propBmnCdListBox.stringValue}" size="7"
										disabled="#{pc_PCoi0401.propBmnCdListBox.disabled}"
										readonly="#{pc_PCoi0401.propBmnCdListBox.readonly}"
										rendered="#{pc_PCoi0401.propBmnCdListBox.rendered}"
										ondblclick="return func_2(this, event);">
										<f:selectItems value="#{pc_PCoi0401.propBmnCdListBox.list}" />
									</h:selectOneListbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0"
							style="margin-top:10px;" width="100%" class="button_bar">
							<TBODY>
								<TR>
									<TD align="center" style="" class="" nowrap><hx:commandExButton
										type="submit" styleClass="commandExButton_etc" id="select"
										value="選択" rendered="#{pc_PCoi0401.propSelect.rendered}"
										style="#{pc_PCoi0401.propSelect.style}"
										disabled="#{pc_PCoi0401.propSelect.disabled}"
										onclick="return func_2(this, event);">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="キャンセル" styleClass="commandExButton_etc" id="cancel"
										disabled="#{pc_PCoi0401.propCancel.disabled}"
										rendered="#{pc_PCoi0401.propCancel.rendered}"
										style="#{pc_PCoi0401.propCancel.style}" action="#{pc_PCoi0401.doCancelAction}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --> 
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/childFooter.jsp" />
<hx:inputHelperSetFocus target="#{pc_PCoi0401.propSetFocus.stringValue}"></hx:inputHelperSetFocus>
			<h:inputHidden value="#{pc_PCoi0401.propHiddenBmnCd.value}" id="htmlHiddenBmnCd"></h:inputHidden>
			<h:inputHidden value="#{pc_PCoi0401.propExecutableSearch.value}" id="htmlExecutableSearch"></h:inputHidden>
			<h:inputHidden value="#{pc_PCoi0401.propHiddenCmbIndex1.value}" id="htmlHiddenCmbIndex1"></h:inputHidden>
			<h:inputHidden value="#{pc_PCoi0401.propHiddenBackIdBmnCd.value}" id="htmlHiddenBackIdBmnCd"></h:inputHidden>
			<h:inputHidden value="#{pc_PCoi0401.propHiddenBackIdBmnName.value}" id="htmlHiddenBackIdBmnName"></h:inputHidden>
			<h:inputHidden value="#{pc_PCoi0401.propHiddenSentakChkErrMsg.stringValue}" id="htmlHiddenSentakChkErrMsg"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

