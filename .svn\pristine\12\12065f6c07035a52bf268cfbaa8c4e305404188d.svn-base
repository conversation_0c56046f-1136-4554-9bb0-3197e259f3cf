<%--
 * 進級見込判定（学生指定出力）

 * Kme00801T02.jsp
 * liuJB
 * 2005-11-22
 * version 1.0
 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kme00801T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<HTML>
<HEAD>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kme00801T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT language="javascript"> 
		function confirmOk() {	
			document.getElementById('form1:removeAllButtonCount').value = "1";
			indirectClick('removeAllGakusei');
		}
		function confirmCancel() {
			// alert('実行を中断しました。');	
		}
</SCRIPT>

<SCRIPT type="text/javascript">
		function openSubWindow() {
			// 学生検索画面
			var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakkiBangou";
			openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
			return true;
		}
		
		function loadAction(event){
			doGakuseiAjax(document.getElementById('form1:htmlGakkiBangou'), event);
		}
</SCRIPT>

<SCRIPT type="text/javascript">
		function doGakuseiAjax(thisObj, thisEvent) {
			// 学生氏名を取得する
			var servlet = "rev/co/CobGakseiAJAX";
			var args = new Array();
			args['code1'] = thisObj.value;
			var target = "form1:lblGakuseName";

			var ajaxUtil = new AjaxUtil();
			ajaxUtil.getCodeName(servlet, target, args);
		}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kme00801T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kme00801T02.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kme00801T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kme00801T02.screenName}"></h:outputText>
</div>
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" width="850">
				<TBODY>
				<%--EUC DEL 取得見込資格（出力する）,エラー内容 2014/06/23 k-sou 
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
							class="table">
							<TBODY>
								<TR>
									<TH bgcolor="" width="170" class="v_a"><h:outputText
										styleClass="outputText" id="lblStkMkmSikaku"
										value="#{pc_Kme00801T01.kme00801.propStkMkmSikaku.labelName}"
										style="#{pc_Kme00801T01.kme00801.propOutputTani.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlStkMkmSikaku"
										value="#{pc_Kme00801T01.kme00801.propStkMkmSikaku.checked}" style="#{pc_Kme00801T01.kme00801.propStkMkmSikaku.style}"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblStkMkmSikaku2" value="出力する"></h:outputText></TD>
								</TR>
								<TR>
									<TH bgcolor="" width="170" class="v_b"><h:outputText
										styleClass="outputText" id="lblErrorContent"
										value="#{pc_Kme00801T01.kme00801.propErrorContent.labelName}"
										style="#{pc_Kme00801T01.kme00801.propErrorContent.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlErrorContent"
										value="#{pc_Kme00801T01.kme00801.propErrorContent.stringValue}"
										style="#{pc_Kme00801T01.kme00801.propErrorContent.style}">
										<f:selectItem itemValue="0" itemLabel="エラー内容・科目を出力" />
										<f:selectItem itemValue="1" itemLabel="エラー内容のみ出力" />
										<f:selectItem itemValue="2" itemLabel="出力しない" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					EUC DEL 取得見込資格（出力する）,エラー内容 2014/06/23 k-sou  --%>
					<TR>
						<TD align="center" style="height:10px"></TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
							class="table">
							<TBODY>
								<%--EUC DEL 単位集計出力方法 2014/06/23 k-sou 
								<TR>
									<TH bgcolor="" width="170" class="v_c"><h:outputText
										styleClass="outputText" id="lblOutputTani"
										value="#{pc_Kme00801T01.kme00801.propOutputTani.labelName}"
										style="#{pc_Kme00801T01.kme00801.propOutputTani.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlOutputTani"
										layout="pageDirection"
										value="#{pc_Kme00801T01.kme00801.propOutputTani.stringValue}"
										style="#{pc_Kme00801T01.kme00801.propOutputTani.style}">
										<f:selectItem itemValue="0" itemLabel="全て出力" />
										<f:selectItem itemValue="1" itemLabel="成績用科目分類の単位表示フラグにより出力" />
									</h:selectOneRadio></TD>
								</TR>
								EUC DEL 単位集計出力方法 2014/06/23 k-sou  --%>
								<TR>
									<TH bgcolor="" width="170" class="v_d"><h:outputText
										styleClass="outputText" id="lblPdfTitle"
										value="#{pc_Kme00801T01.kme00801.propPdfTitle.labelName}"
										style="#{pc_Kme00801T01.kme00801.propPdfTitle.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:inputText styleClass="inputText"
										id="htmlPdfTitle"
										value="#{pc_Kme00801T01.kme00801.propPdfTitle.stringValue}"
										style="#{pc_Kme00801T01.kme00801.propPdfTitle.style}"
										size="70"
										maxlength="#{pc_Kme00801T01.kme00801.propPdfTitle.maxLength}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center" style="height:10px"></TD>
					</TR>
					<TR>
						<TD width="850" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="140">
							<TBODY>
								<TR height="25">
									<TD width="70" nowrap align="center" valign="middle"
										class="tab_head_off"><hx:commandExButton type="submit"
										value="一括指定" styleClass="tab_head_off" id="button1"
										action="#{pc_Kme00801T02.doChangeTab1Action}"></hx:commandExButton></TD>
									<TD width="70" nowrap align="center" valign="middle">
										<hx:commandExButton type="button" value="学生指定" styleClass="tab_head_on" id="gakuseiSitei">
										</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="850" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
							class="tab_body" height="360">
							<TBODY>
								<TR>
									<TD align="center">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
										<TBODY>
											<TR>
												<TD width="500" align="left"><h:outputText styleClass="outputText"
													id="text8" value="学籍番号"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE width="800" class="table">
										<TBODY>
											<TR>
												<TH class="v_a" width="170"><h:outputText
													styleClass="outputText" id="lblCSVFile"
													value="#{pc_Kme00801T02.propCSVFile.labelName}"></h:outputText><BR>
												<h:outputText styleClass="outputText" id="lblCSVFileName"
													value="#{pc_Kme00801T02.propCSVFileName.labelName}"
													style="#{pc_Kme00801T02.propCSVFileName.labelStyle}"></h:outputText></TH>
												<TD colspan="2"><hx:fileupload styleClass="fileupload"
													id="ｈｔｍｌＣＳＶFile"
													value="#{pc_Kme00801T02.propCSVFile.value}"
													style="#{pc_Kme00801T02.propCSVFile.style};width:485px"
													size="50">
													<hx:fileProp name="fileName"
														value="#{pc_Kme00801T02.propCSVFile.fileName}" />
													<hx:fileProp name="contentType"
														value="#{pc_Kme00801T02.propCSVFile.contentType}" />
												</hx:fileupload> <hx:commandExButton type="submit"
													value="取込" styleClass="commandExButton"
													id="addGakuseiFormCSV"
													action="#{pc_Kme00801T02.doLoadCSVAction}"></hx:commandExButton><BR>
													<h:outputText styleClass="outputText"
													id="htmlCSVFileName"
													value="#{pc_Kme00801T02.propCSVFileName.stringValue}"></h:outputText><BR></TD>

											</TR>
											<TR>
												<TH class="v_b" width="170"><h:outputText
													styleClass="outputText" id="lblGakkiBangou"
													value="#{pc_Kme00801T02.propGakkiBangou.labelName}"
													style="#{pc_Kme00801T02.propGakkiBangou.labelStyle}"></h:outputText></TH>
												<TD width="180"><h:inputText styleClass="inputText"
													id="htmlGakkiBangou"
													onblur="return doGakuseiAjax(this, event);"
													value="#{pc_Kme00801T02.propGakkiBangou.stringValue}"
													maxlength="#{pc_Kme00801T02.propGakkiBangou.maxLength}"
													style="#{pc_Kme00801T02.propGakkiBangou.style}" size="10"></h:inputText><hx:commandExButton
													type="button" styleClass="commandExButton_search"
													id="doSearchAction"
													onclick="return openSubWindow(this, event);"></hx:commandExButton>
												<hx:commandExButton type="submit" value="追加"
													styleClass="commandExButton" id="addSingleGakusei"
													action="#{pc_Kme00801T02.doAddGakusei}"></hx:commandExButton></TD>
												<TD width="450"><h:outputText styleClass="outputText"
													id="lblGakuseName"
													value="#{pc_Kme00801T02.propGakuseName.stringValue}"
													style="#{pc_Kme00801T02.propGakuseName.style}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									<HR class="hr" noshade>
									<CENTER>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="600">
										<TBODY>
											<TR>
												<TD width="500" align="left"><h:outputText styleClass="outputText"
													id="lblTaisyouGakuse"
													value="#{pc_Kme00801T02.propTaisyouGakuseList.labelName}"></h:outputText></TD>
												<TD width="100" colspan="2"></TD>
											</TR>
											<TR>
												<TD ><h:selectManyListbox styleClass="selectManyListbox"
													style="width:100%" id="htmlTaisyouGakuse" size="10"
													value="#{pc_Kme00801T02.propTaisyouGakuseList.value}">
													<f:selectItems
														value="#{pc_Kme00801T02.propTaisyouGakuseList.list}" />
												</h:selectManyListbox></TD>
												<TD align="center" valign="top"><hx:commandExButton
													type="submit" value="除外" styleClass="commandExButton"
													id="removeGakusei"
													action="#{pc_Kme00801T02.doRemoveGakuseiAction}" style="width:60px"></hx:commandExButton><BR>
												<h:outputText styleClass="outputText" id="text6"
													value="(複数選択可)"></h:outputText> <BR>
												<hx:commandExButton type="submit" value="全て除外"
													styleClass="commandExButton" id="removeAllGakusei"
													action="#{pc_Kme00801T02.doRemoveAllGakuseiAction}"
													style="width:60px">
												</hx:commandExButton></TD>
											</TR>
											<TR>
												<TD align="right"><h:outputFormat
													styleClass="outputFormat" id="format1"
													value="合計件数：{0}件　正常件数：{1}件　エラー件数：{2}件">
													<f:param name="maxCount"
														value="#{pc_Kme00801T02.propTaisyouGakuseList.listCount}"></f:param>
													<f:param name="nomalCount"
														value="#{pc_Kme00801T02.propTaisyouGakuseList.listCount - pc_Kme00801T02.propErrorCount.integerValue}"></f:param>
													<f:param name="errCount"
														value="#{pc_Kme00801T02.propErrorCount.integerValue}"></f:param>
												</h:outputFormat></TD>
												<TD align="center" colspan="2"></TD>
											</TR>
										</TBODY>
									</TABLE>
									</CENTER>

									<BR>
									<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
										class="button_bar">
										<TBODY>
											<TR>
												<TD>
													<hx:commandExButton type="submit" value="PDF作成"
													styleClass="commandExButton_out" id="pdfout" confirm="#{msg.SY_MSG_0019W}"
													action="#{pc_Kme00801T02.doPdfoutAction}">
													</hx:commandExButton>

													<hx:commandExButton type="submit" value="EXCEL作成"
													styleClass="commandExButton_out" id="excelout" confirm="#{msg.SY_MSG_0027W}"
													action="#{pc_Kme00801T02.doExceloutAction}">
													</hx:commandExButton>
													
													<hx:commandExButton
													type="submit" value="CSV作成" confirm="#{msg.SY_MSG_0020W}"
													styleClass="commandExButton_out" id="csvout"
													action="#{pc_Kme00801T02.doCsvoutAction}"></hx:commandExButton><hx:commandExButton
													type="submit" value="出力項目指定"
													styleClass="commandExButton_out" id="setoutput"
													action="#{pc_Kme00801T02.doSetoutputAction}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>

						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑--></DIV>

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Kme00801T02.propRemoveAllButton.integerValue}"
				id="removeAllButtonCount">
				<f:convertNumber type="number" />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
