<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssg01201T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

	// 確認メッセージでOKが押下された場合の処理

	function confirmOk() {
		var btnId = document.getElementById('form1:linkFlg').value;

		// ボタン押下フラグをセットし、同じ処理が流れないようにする
		var nowBtn = document.getElementById('form1:executable').value;
		document.getElementById('form1:executable').value = eval(nowBtn) + 1;

		if (btnId == 1){
			indirectClick('pdfout');
		}

		if (btnId == 2){
			indirectClick('csvout');
		}

		if (btnId == 3){
			indirectClick('print');
		}
		
		if (btnId == 4){
			indirectClick('excelout');
		}
	}

	// 確認メッセージでキャンセルが押下された場合の処理

	function confirmCancel() {
		// ボタン押下フラグをクリアする
		document.getElementById('form1:executable').value = 0;
	}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssg01201T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssg01201T02.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssg01201T02.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssg01201T02.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%"
				align="center">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="620">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="htmlLblKyujinNendo"
										value="#{pc_Ssg01201T01.ssg01201.kyujinNendo.labelName}"
										style="#{pc_Ssg01201T01.ssg01201.kyujinNendo.labelStyle}"></h:outputText></TH>
									<TD width="470"><h:outputText styleClass="outputText"
										id="htmlKyujinNendo"
										value='#{pc_Ssg01201T01.ssg01201.kyujinNendo.stringValue}'
										style="#{pc_Ssg01201T01.ssg01201.kyujinNendo.style}">
									</h:outputText><h:outputText styleClass="outputText"
										id="htmlNendoLabel" value='年度'></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="htmlLblSumType"
										value="#{pc_Ssg01201T01.ssg01201.propSumType.labelName}"
										style="#{pc_Ssg01201T01.ssg01201.propSumType.labelStyle}"></h:outputText></TH>
									<TD width="470"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSumType"
										value="#{pc_Ssg01201T01.ssg01201.propSumType.stringValue}"
										disabled="#{pc_Ssg01201T01.ssg01201.propSumType.disabled}"
										readonly="#{pc_Ssg01201T01.ssg01201.propSumType.readonly}"
										style="#{pc_Ssg01201T01.ssg01201.propSumType.style}">
										<f:selectItems
											value="#{pc_Ssg01201T01.ssg01201.propSumType.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="htmlLblGakkaSosikiLv"
										value="#{pc_Ssg01201T01.ssg01201.propGakkaSosikiLv.labelName}"
										style="#{pc_Ssg01201T01.ssg01201.propGakkaSosikiLv.labelStyle}"></h:outputText></TH>
									<TD width="470"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakkaSosikiLv"
										value="#{pc_Ssg01201T01.ssg01201.propGakkaSosikiLv.stringValue}"
										disabled="#{pc_Ssg01201T01.ssg01201.propGakkaSosikiLv.disabled}"
										readonly="#{pc_Ssg01201T01.ssg01201.propGakkaSosikiLv.readonly}"
										style="#{pc_Ssg01201T01.ssg01201.propGakkaSosikiLv.style}">
										<f:selectItems
											value="#{pc_Ssg01201T01.ssg01201.propGakkaSosikiLv.list}" />
									</h:selectOneMenu><BR>
									<h:outputText styleClass="note" id="htmlLblSumType2"
										value="　（集計単位が学科組織単位の場合のみ選択）">
									</h:outputText></TD>
								</TR>
								<TR>
									<hx:jspPanel rendered="#{!pc_Ssg01201T01.ssg01201.hiseiki}">
									<TH class="v_c" width="150" rowspan="2"><h:outputText
										styleClass="outputText" id="htmlLblSyukeiTaisyo" value="集計対象">
									</h:outputText></TH>
									</hx:jspPanel>
									<hx:jspPanel rendered="#{pc_Ssg01201T01.ssg01201.hiseiki}">
									<TH class="v_c" width="150" rowspan="4"><h:outputText
										styleClass="outputText" id="htmlLblSyukeiTaisyo2" value="集計対象">
									</h:outputText></TH>
									</hx:jspPanel>
									<TD width="470"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSexCheck"
										value="#{pc_Ssg01201T01.ssg01201.propSexCheck.checked}"
										disabled="#{pc_Ssg01201T01.ssg01201.propSexCheck.disabled}"
										readonly="#{pc_Ssg01201T01.ssg01201.propSexCheck.readonly}"
										style="#{pc_Ssg01201T01.ssg01201.propSexCheck.style}">
									</h:selectBooleanCheckbox><h:outputText styleClass="outputText"
										id="htmlLblSexCheck"
										value="#{pc_Ssg01201T01.ssg01201.propSexCheck.labelName}"
										style="#{pc_Ssg01201T01.ssg01201.propSexCheck.labelStyle}"></h:outputText></TD>
								</TR>
								<hx:jspPanel rendered="#{pc_Ssg01201T01.ssg01201.hiseiki}">
								<TR>
									<TD width="470"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSeikiCheck"
										value="#{pc_Ssg01201T01.ssg01201.propSeikiCheck.checked}"
										disabled="#{pc_Ssg01201T01.ssg01201.propSeikiCheck.disabled}"
										readonly="#{pc_Ssg01201T01.ssg01201.propSeikiCheck.readonly}"
										style="#{pc_Ssg01201T01.ssg01201.propSeikiCheck.style}">
									</h:selectBooleanCheckbox><h:outputText styleClass="outputText"
										id="htmlLblSeikiCheck"
										value="#{pc_Ssg01201T01.ssg01201.propSeikiCheck.labelName}"
										style="#{pc_Ssg01201T01.ssg01201.propSeikiCheck.labelStyle}"></h:outputText></TD>
								</TR>
								<TR>
									<TD width="470"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlHiseikiCheck"
										value="#{pc_Ssg01201T01.ssg01201.propHiseikiCheck.checked}"
										disabled="#{pc_Ssg01201T01.ssg01201.propHiseikiCheck.disabled}"
										readonly="#{pc_Ssg01201T01.ssg01201.propHiseikiCheck.readonly}"
										style="#{pc_Ssg01201T01.ssg01201.propHiseikiCheck.style}">
									</h:selectBooleanCheckbox><h:outputText styleClass="outputText"
										id="htmlLblHiseikiCheck"
										value="#{pc_Ssg01201T01.ssg01201.propHiseikiCheck.labelName}"
										style="#{pc_Ssg01201T01.ssg01201.propHiseikiCheck.labelStyle}"></h:outputText></TD>
								</TR>
								</hx:jspPanel>
							</TBODY>
						</TABLE>
						<BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="620">
							<TBODY>
								<TR>
									<TH class="v_c" width="147"><h:outputText
										styleClass="outputText" id="htmlLblRepTitle"
										value="#{pc_Ssg01201T01.ssg01201.propRepTitle.labelName}"
										style="#{pc_Ssg01201T01.ssg01201.propRepTitle.labelStyle}"></h:outputText></TH>
									<TD widht="473"><h:inputText styleClass="inputText"
										id="htmlRepTitle"
										value="#{pc_Ssg01201T01.ssg01201.propRepTitle.stringValue}"
										size="70"
										disabled="#{pc_Ssg01201T01.ssg01201.propRepTitle.disabled}"
										maxlength="#{pc_Ssg01201T01.ssg01201.propRepTitle.maxLength}"
										readonly="#{pc_Ssg01201T01.ssg01201.propRepTitle.readonly}"
										style="#{pc_Ssg01201T01.ssg01201.propRepTitle.style}">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="620">
							<TBODY>
								<TR>
									<TD width="620" align="left"><hx:commandExButton type="submit"
										value="全件指定" styleClass="tab_head_off" id="tab1" style="width:21%"
										action="#{pc_Ssg01201T01.doTab1Action}"></hx:commandExButton><hx:commandExButton
										type="button" value="出身校指定" style="width:21%" styleClass="tab_head_on"
										id="tab2"></hx:commandExButton><hx:commandExButton
										type="submit" value="出身校所在地指定" styleClass="tab_head_off"
										id="tab3" action="#{pc_Ssg01201T01.doTab3Action}" style="width:21%"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD class="tab_body" height="270" width="620"><BR>
												<TABLE border="0" cellpadding="0" cellspacing="0">
													<TBODY>
														<TR>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="table" width="480">
																<TBODY>
																	<TR>
																		<TH class="v_a" width="150"><h:outputText
																			styleClass="outputText" id="htmlSearchLabel"
																			value="#{pc_Ssg01201T02.propSsnFrom.labelName}"></h:outputText></TH>
																		<TD width="330"><h:inputText styleClass="inputText"
																			id="htmlSsnFrom"
																			value="#{pc_Ssg01201T02.propSsnFrom.stringValue}"
																			size="10" style="#{pc_Ssg01201T02.propSsnFrom.style}"
																			onblur="return true"
																			disabled="#{pc_Ssg01201T02.propSsnFrom.disabled}"
																			maxlength="#{pc_Ssg01201T02.propSsnFrom.maxLength}"
																			readonly="#{pc_Ssg01201T02.propSsnFrom.readonly}"></h:inputText><hx:commandExButton
																			type="submit" value="検"
																			styleClass="commandExButton_search" id="searchFrom"
																			action="#{pc_Ssg01201T02.doSsnBtnFrom}"></hx:commandExButton>
																		～ <h:inputText styleClass="inputText" id="htmlSsnTo"
																			value="#{pc_Ssg01201T02.propSsnTo.stringValue}"
																			size="10" style="#{pc_Ssg01201T02.propSsnTo.style}"
																			onblur="return true"
																			disabled="#{pc_Ssg01201T02.propSsnTo.disabled}"
																			maxlength="#{pc_Ssg01201T02.propSsnTo.maxLength}"
																			readonly="#{pc_Ssg01201T02.propSsnTo.readonly}"></h:inputText><hx:commandExButton
																			type="submit" value="検"
																			styleClass="commandExButton_search" id="searchTo"
																			action="#{pc_Ssg01201T02.doSsnBtnTo}"></hx:commandExButton>
																		<hx:commandExButton type="submit" value="選択"
																			styleClass="commandExButton" id="select"
																			action="#{pc_Ssg01201T02.doSelectAction}"
																			style="white-space: nowrap;">
																		</hx:commandExButton></TD>
																	</TR>
																</TBODY>
															</TABLE>
															<BR>
															</TD>
														</TR>
														<TR>
															<TD width="480">
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="">
																<TBODY>
																	<TR>
																		<TH style="text-align: left" width="316"><h:outputText
																			styleClass="outputText" id="listTitle"
																			style="margin-left:5px;" value="出身校">
																		</h:outputText></TH>
																		<TD style="text-align: right" width="60"><h:outputText
																			styleClass="outputText" id="htmlListCount"
																			value="#{pc_Ssg01201T02.propSsn.listCount}">
																		</h:outputText> <h:outputText styleClass="outputText"
																			id="htmlKenLabel" value="件">
																		</h:outputText></TD>
																		<TD></TD>
																	</TR>
																	<TR>
																		<TD colspan="2">
																		<TABLE border="0" cellpadding="0" cellspacing="0"
																			class="list_table">
																			<TBODY>
																				<TR>
																					<TD><h:selectManyListbox
																						styleClass="selectManyListbox" id="htmlssn"
																						style="width: 476px" size="7"
																						value="#{pc_Ssg01201T02.propSsn.stringValue}"
																						disabled="#{pc_Ssg01201T02.propSsn.disabled}"
																						readonly="#{pc_Ssg01201T02.propSsn.readonly}">
																						<f:selectItems
																							value="#{pc_Ssg01201T02.propSsn.list}" />
																					</h:selectManyListbox></TD>
																				</TR>
																			</TBODY>
																		</TABLE>

																		</TD>

																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															<TD width="100">
															<TABLE border="0" cellpadding="0" cellspacing="0">
																<TR>
																	<TD><hx:commandExButton type="submit" value="除外"
																		styleClass="commandExButton" id="remove"
																		action="#{pc_Ssg01201T02.doRemoveAction}"
																		style="width:65px"></hx:commandExButton><BR>
																	<h:outputText styleClass="note" id="lblExclusionCmt"
																		value="（複数選択可）"></h:outputText><BR>
																	<BR>
																	</TD>
																</TR>
																<TR>
																	<TD><hx:commandExButton type="submit" value="全て除外"
																		styleClass="commandExButton" id="removeAll"
																		action="#{pc_Ssg01201T02.doRemoveAllAction}"
																		style="width:65px">
																	</hx:commandExButton></TD>
																</TR>
															</TABLE>
															</TD>
														</TR>
														<TR>
															<TD><BR>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="table">
																<TBODY>
																	<TR>
																		<TH class="v_b" width="150"><h:outputText
																			styleClass="outputText" id="htmlLblSortType"
																			value="#{pc_Ssg01201T02.propSortType.labelName}"
																			style="#{pc_Ssg01201T02.propSortType.labelStyle}"></h:outputText></TH>
																		<TD width="324"><h:selectOneRadio
																			disabledClass="selectOneRadio_Disabled"
																			styleClass="selectOneRadio" id="htmlSortType"
																			layout="pageDirection"
																			value="#{pc_Ssg01201T02.propSortType.stringValue}"
																			disabled="#{pc_Ssg01201T02.propSortType.disabled}"
																			readonly="#{pc_Ssg01201T02.propSortType.readonly}"
																			style="#{pc_Ssg01201T02.propSortType.style}">
																			<f:selectItem itemValue="0" itemLabel="出身校コード順" />
																			<f:selectItem itemValue="1" itemLabel="出身校カナ名称順" />
																		</h:selectOneRadio></TD>
																	</TR>
																</TBODY>
															</TABLE>
															<BR>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="button_bar" width="100%">
										<TBODY>
											<TR>
												<TD><hx:commandExButton type="submit" value="PDF作成"
													styleClass="commandExButton_out" id="pdfout"
													confirm="#{msg.SY_MSG_0019W}"
													action="#{pc_Ssg01201T02.doPdfOutAction}"></hx:commandExButton><hx:commandExButton
													type="submit" value="EXCEL作成"
													styleClass="commandExButton_out" id="excelout"
													confirm="#{msg.SY_MSG_0027W}"
													action="#{pc_Ssg01201T02.doExcelOutAction}"></hx:commandExButton><hx:commandExButton
													type="submit" value="CSV作成"
													styleClass="commandExButton_out" id="csvout"
													confirm="#{msg.SY_MSG_0020W}"
													action="#{pc_Ssg01201T02.doCsvOutAction}"></hx:commandExButton><hx:commandExButton
													type="submit" value="出力項目指定"
													styleClass="commandExButton_out" id="setoutput"
													action="#{pc_Ssg01201T02.doSetoutputAction}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden id="linkFlg"
				value="#{pc_Ssg01201T02.propLinkFlg.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="executable"
				value="#{pc_Ssg01201T02.propExecutable.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

