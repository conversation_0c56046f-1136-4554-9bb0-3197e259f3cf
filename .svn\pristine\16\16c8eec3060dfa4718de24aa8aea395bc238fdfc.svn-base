<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghf00101.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<TITLE>Ghf00101.jsp</TITLE>
<SCRIPT type="text/javascript">

	function fncInit() {
	
		changeScrollPosition('scrollheader', 'listInputFileHeader');
		
		//フォーカスの設定
		setFocus();
		
		//ラジオボタンの設定
		fncRadioActive();
		
	}
	
	//フォーカスの設定
	function setFocus(){
		
		var id = null;

		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;

		//リスト内選択ボタンフォーカス時
		if ((id != null) && (id != "")) {
			document.getElementById(id).focus();
		}
		
		//初期化
		document.getElementById('form1:htmlScrollPos').value = "";

	}

    //ラジオボタンの制御
    function fncRadioActive(){
    	//取込ボタンの制御 htmlHidErrorKbn
    	if(document.getElementById('form1:htmlHidErrorKbn').value == '1'){
    		document.getElementById('form1:takein').disabled = true;
    	}
    
    	//選択ボタン取得
    	val = document.getElementById('form1:htmlHidFileKbn').value;
		objForm = document.getElementById("form1");
		
//20140519 shigihara add start
			objForm.htmlFileKbn.checked = true;
//20140519 shigihara add end
//20140519 shigihara コメント化 start
		//if(val == "1") {
		//	objForm.htmlFileKbn[0].checked = true;
		//	objForm.htmlFileKbn[1].checked = false;
		//	objForm.htmlFileKbn[2].checked = false;
		//} else if (val == "2"){
		//	objForm.htmlFileKbn[0].checked = false;
		//	objForm.htmlFileKbn[1].checked = true;
		//	objForm.htmlFileKbn[2].checked = false;
		//}else{
		//	objForm.htmlFileKbn[0].checked = false;
		//	objForm.htmlFileKbn[1].checked = false;
		//	objForm.htmlFileKbn[2].checked = true;
		//}

		// ヘッダなしＣＳＶを選択時は取込・解除ボタンは選択不可
		//if(val == "3") {
		//	document.getElementById('form1:takein').disabled = true;
		//	document.getElementById('form1:unselect').disabled = true;
		//}	
//20140519 shigihara コメント化 end	
		
		// 取り込み状態の場合は入力ファイル形式項目は選択不可とする
	   	if(document.getElementById('form1:takein').disabled == true &&
	   		document.getElementById('form1:htmlHidErrorKbn').value != '1'){
//20140519 shigihara コメント化 start
			//if(val != "3"){
				//objForm.htmlFileKbn[0].disabled = true;
				//objForm.htmlFileKbn[1].disabled = true;
				//objForm.htmlFileKbn[2].disabled = true;
				document.getElementById('form1:htmlInputFileFormat').disabled = true;
				//document.getElementById('form1:setinput1').disabled = true;
				//document.getElementById('form1:setinput2').disabled = true;
				//document.getElementById('form1:setinput3').disabled = true;
			//}
//20140519 shigihara コメント化 end
			document.getElementById('form1:exec').disabled = false;
	   	}else{
//20140519 shigihara コメント化 start
			//objForm.htmlFileKbn[0].disabled = false;
			//objForm.htmlFileKbn[1].disabled = false;
			//objForm.htmlFileKbn[2].disabled = false;
			document.getElementById('form1:htmlInputFileFormat').disabled = false;
			//document.getElementById('form1:setinput1').disabled = false;
			//document.getElementById('form1:setinput2').disabled = false;
			//document.getElementById('form1:setinput3').disabled = false;
//20140519 shigihara コメント化 start
			document.getElementById('form1:exec').disabled = true;
	   	}
		
	}
	
//20140519 shigihara コメント化 start
//	//ラジオボタンの値をhiddenにセット
//	function setFileKbn(val){
//		document.getElementById('form1:htmlHidFileKbn').value = val;
//		// ヘッダなしＣＳＶを選択時は取込・解除ボタンは選択不可
//		if(val == "3") {
//			document.getElementById('form1:takein').disabled = true;
//			document.getElementById('form1:unselect').disabled = true;
//			document.getElementById('form1:exec').disabled = false;
//		}else 
//		if(document.getElementById('form1:takein').disabled == true){
//			document.getElementById('form1:takein').disabled = false;
//			document.getElementById('form1:exec').disabled = true;
//		}
//	}
//20140519 shigihara コメント化 end


</SCRIPT></HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY onload="fncInit();">
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghf00101.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp"
                    action="#{pc_Ghf00101.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghf00101.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghf00101.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
                    
                <TABLE border="0" cellpadding="0" cellspacing="0" width="900"><TBODY><TR><TD>

				<TABLE border="0" cellpadding="0" cellspacing="0" width="900" class="table">
					<TBODY>
						<TR>
							<TH rowspan="2" align="left" nowrap class="v_a" width="150px"><h:outputText
								styleClass="outputText" id="lblInputFileFormat"
								value="#{pc_Ghf00101.propInputFileFormat.labelName}"
								style="#{pc_Ghf00101.propInputFileFormat.labelStyle}"></h:outputText>
							</TH>
								
							<TD align="left" width="150px" style="border-right-style:none;">
								<input type="radio" name="htmlFileKbn" value="1"><!--  onclick="setFileKbn(this.value);"> -->
								<h:outputText styleClass="outputText" id="lblZengin" value="#{pc_Ghf00101.propZengin.name}"></h:outputText>
							</TD>
							<TD align="left" width="500px" colspan="2" style="border-left-style:none;">
								<h:selectOneMenu styleClass="selectOneMenu"
								id="htmlInputFileFormat"
								style="#{pc_Ghf00101.propInputFileFormat.style}" 
								disabled="#{pc_Ghf00101.propInputFileFormat.disabled}" 
								value="#{pc_Ghf00101.propInputFileFormat.stringValue}" tabindex="1">
								<f:selectItems value="#{pc_Ghf00101.propInputFileFormat.list}" />
								</h:selectOneMenu>
							</TD>
							<TD rowspan="2"></TD>
						</TR>
						<TR>
							<TD width="150px" align="right" style="text-align:right; border-right-style:none;">
								<h:outputText styleClass="outputText" id="lblNyukinDateKbn" value="入金日付指定： "></h:outputText>
							</TD>
							<TD align="left" width="500px" style="border-left-style:none;border-right-style:none;" colspan="2">
								<h:selectOneRadio
									disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio outputText" id="htmlNyukinDateKbn"
									value="#{pc_Ghf00101.propNyukinDateKbn.stringValue}"
									disabled="#{pc_Ghf00101.propNyukinDateKbn.disabled}"
									style="#{pc_Ghf00101.propNyukinDateKbn.style}">
									<f:selectItem itemValue="1" itemLabel="勘定日を使用する" />
									<f:selectItem itemValue="2" itemLabel="起算日（預入･払出日）を使用する" />
								</h:selectOneRadio>
							</TD>

						<!-- 20140501 shigihara start	
						<TR>
							<TD align="left" width="150px" style="border-right-style:none;">
								<input type="radio" name="htmlFileKbn" value="2" onclick="setFileKbn(this.value);">
								<h:outputText
									styleClass="outputText" 
									id="lblHedAri" 
									value="#{pc_Ghf00101.propHedAri.name}"> 
								</h:outputText>
							</TD>
							<TD align="left" width="150px" style="border-left-style:none;border-right-style:none;">
								<h:outputText styleClass="outputText" id="lblHed" value="#{pc_Ghf00101.propHed.name}"></h:outputText>
								<hx:commandExButton type="submit"
									value="入力項目指定" styleClass="commandExButton_out" id="setinput1"
									action="#{pc_Ghf00101.doSetinput1Action}">
								</hx:commandExButton>
							</TD>
							
							<TD align="left" width="350px" style="border-left-style:none;">
								<h:outputText styleClass="outputText" id="lblMeisai" value="#{pc_Ghf00101.propMeisai.name}"></h:outputText>
								<hx:commandExButton type="submit"
									value="入力項目指定" styleClass="commandExButton_out" id="setinput2"
									action="#{pc_Ghf00101.doSetinput2Action}">
								</hx:commandExButton>
							</TD>
						</TR>

						<TR>
							<TD align="left" width="150px" style="border-right-style:none;">
								<input type="radio" name="htmlFileKbn" value="3" onclick="setFileKbn(this.value);">
								<h:outputText
									styleClass="outputText" 
									id="lblHedNasi" 
									value="#{pc_Ghf00101.propHedNasi.name}"> 
								</h:outputText>
							</TD>
							<TD align="left" width="150px" style="border-left-style:none;border-right-style:none;">
							</TD>
							<TD align="left" width="350px" style="border-left-style:none;">
								<h:outputText
									styleClass="outputText" 
									id="lblMeisai2" 
									value="#{pc_Ghf00101.propMeisai.name}">
								</h:outputText>
								<hx:commandExButton type="submit"
									value="入力項目指定" styleClass="commandExButton_out" id="setinput3"
									action="#{pc_Ghf00101.doSetinput3Action}">
								</hx:commandExButton>
							</TD>
						</TR>
						20140501 shigihara end -->
						<!-- 入力ファイル -->
						<TR>
							<TH align="left" nowrap class="v_b" width="150px">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
									<TBODY>
										<TR>
											<TH class="clear_border">
												<!-- 入力ファイル -->
												<h:outputText styleClass="outputText"
													id="lblInputFile"
													style="#{pc_Ghf00101.propInputFile.labelStyle}"
													value="#{pc_Ghf00101.propInputFile.labelName}">
												</h:outputText>
											</TH>
										</TR>
										<TR>
											<TH class="clear_border">
												<!-- 前回ファイル -->
												<h:outputText styleClass="outputText"
													id="lblZenkaiFileNameLabel" 
													style="#{pc_Ghf00101.propZenkaiFileName.labelStyle}" 
													value="#{pc_Ghf00101.propZenkaiFileName.labelName}">
												</h:outputText>
											</TH>
										</TR>
									</TBODY>
								</TABLE>
							</TH>
							<TD width="*" nowrap colspan="3">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
									<TBODY>
										<TR>
											<TD class="clear_border">
												<hx:fileupload styleClass="fileupload" 
													id="htmlInputFile" 
													value="#{pc_Ghf00101.propInputFile.value}"
													style="#{pc_Ghf00101.propInputFile.style} width:600px" 
													disabled="#{pc_Ghf00101.propInputFile.disabled}" 
													>
												<hx:fileProp name="fileName" 
													value="#{pc_Ghf00101.propInputFile.fileName}"/>
												<hx:fileProp name="contentType"
													value="#{pc_Ghf00101.propInputFile.contentType}" />
												</hx:fileupload>
											</TD>
										</TR>
										<TR>
											<TD class="clear_border">
												<h:outputText 
													styleClass="outputText" 
													id="lblZenkaiFileName" 
													value="#{pc_Ghf00101.propZenkaiFileName.stringValue}" 
													style="#{pc_Ghf00101.propZenkaiFileName.labelStyle}">
												</h:outputText></TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
							<TD align="left">
								<hx:commandExButton type="submit" 
									value="取込" styleClass="cmdBtn_dat_s" id="takein" 
									disabled="#{pc_Ghf00101.propInputFileFormat.disabled}" 
									action="#{pc_Ghf00101.doTakeInAction}" >
								</hx:commandExButton><div>
								<hx:commandExButton type="submit"
									value="解除" styleClass="cmdBtn_etc_s" id="unselect"
									disabled="#{!pc_Ghf00101.propInputFileFormat.disabled}" 
									action="#{pc_Ghf00101.doUnselectAction}" >
								</hx:commandExButton>
							</TD>
						</TR>
						
					</TBODY>
				</TABLE>
				
				</TD></TR><TR><TD>
				
				<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
							<TD align="right"><h:outputText styleClass="outputText"
								id="textCountInputFileHeader"
								value="#{pc_Ghf00101.propInputFileHeaderList.listCount}"></h:outputText>
								<h:outputText styleClass="outputText" 
								id="textKenInputFileHeader" value="件"></h:outputText>
							</TD>
						</TR>
						<TR><TD>
						</TD></TR>
						<TR>
							<TD>
							<DIV style="height: 156px; width=100%;" id="listInputFileHeader" onscroll="setScrollPosition('scrollheader',this);" class="listScroll">
								<h:dataTable
									id="htmlInputFileHeaderList"
									headerClass="headerClass" 
									footerClass="footerClass"
									styleClass="meisai_scroll" 
									value="#{pc_Ghf00101.propInputFileHeaderList.list}" 
									var="varlist"
									width="881px"
									rowClasses="#{pc_Ghf00101.propInputFileHeaderList.rowClasses}" 
									rows="#{pc_Ghf00101.propInputFileHeaderList.rows}">
									<h:column id="column1">
										<f:facet name="header">
											<h:outputText 
												styleClass="outputText" 
												value="#{pc_Ghf00101.propTblKanjoDate.name}"
												id="text1">
											</h:outputText>								
										</f:facet>								
										<h:outputText styleClass="outputText" 
											id="text11"
											value="#{varlist.propKanjoDate.displayValue}" 
											title="#{varlist.propKanjoDate.value}">
										</h:outputText>
										<f:attribute value="*" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText styleClass="outputText" 
												value="#{pc_Ghf00101.propTblBankCd.name}"
												id="text2">
											</h:outputText>
										</f:facet>								
										<h:outputText styleClass="outputText" 
											id="text12"
											value="#{varlist.propBankCd.displayValue}" 
											title="#{varlist.propBankCd.value}">
										</h:outputText>
										<f:attribute value="80px" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText styleClass="outputText" 
												value="#{pc_Ghf00101.propTblBankName.name}"
												id="text3">
											</h:outputText>
										</f:facet>								
										<h:outputText styleClass="outputText" i
											d="text13"
											value="#{varlist.propBankName.displayValue}" 
											title="#{varlist.propBankName.value}">
										</h:outputText>
										<f:attribute value="220px" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
									<h:column id="column4">
										<f:facet name="header">
											<h:outputText styleClass="outputText" 
												value="#{pc_Ghf00101.propTblSitenCd.name}"
												id="text4">
											</h:outputText>
										</f:facet>								
										<h:outputText styleClass="outputText" 
											id="text14"
											value="#{varlist.propSitenCd.displayValue}" 
											title="#{varlist.propSitenCd.value}">
										</h:outputText>
										<f:attribute value="80px" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
									<h:column id="column5">
										<f:facet name="header">
											<h:outputText styleClass="outputText" 
												value="#{pc_Ghf00101.propTblSitenName.name}" 
												id="text5">
											</h:outputText>
										</f:facet>								
										<h:outputText styleClass="outputText" 
											id="text15"
											value="#{varlist.propSitenName.displayValue}" 
											title="#{varlist.propSitenName.value}">
										</h:outputText>
										<f:attribute value="220px" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
									<h:column id="column6">
										<f:facet name="header">
											<h:outputText styleClass="outputText" 
												value="#{pc_Ghf00101.propTblYokinItemCd.name}"
												id="text6">
											</h:outputText>
										</f:facet>								
										<h:outputText styleClass="outputText" 
											id="text16"
											value="#{varlist.propYokinItemCd.displayValue}" 
											title="#{varlist.propYokinItemCd.value}">
										</h:outputText>
										<f:attribute value="80px" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									<h:column id="column7">
										<f:facet name="header">
											<h:outputText styleClass="outputText" v
												alue="#{pc_Ghf00101.propTblKouzaNo.name}" 
												id="text7">
											</h:outputText>
										</f:facet>								
										<h:outputText styleClass="outputText" 
											id="text17"
											value="#{varlist.propKouzaNo.displayValue}" 
											title="#{varlist.propKouzaNo.value}">
										</h:outputText>
										<f:attribute value="90px" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
								</h:dataTable>
							</DIV>
							</TD>
						</TR>
					</TBODY>
				</TABLE>	
							
				
				</TD></TR><TR><TD height="15"></TD></TR><TR><TD>
				
				<TABLE class="table" width="100%">
					<TBODY>
						<TR align="center">
							<TH nowrap class="v_c" width="150px" rowspan="5"><h:outputText styleClass="outputText"
								id="lblChkKomoku" value="#{pc_Ghf00101.propChkKomoku.name}" style="#{pc_Ghf00101.propChkKomoku.style}"></h:outputText></TH>
							<TD align="left" style="border-bottom-style:none">
								<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlSmaller"
								value="#{pc_Ghf00101.propSmaller.checked}" 
								disabled="#{pc_Ghf00101.propSmaller.disabled}" 
								style="#{pc_Ghf00101.propSmaller.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblSmaller"
								value="#{pc_Ghf00101.propSmaller.name}"
								style="#{pc_Ghf00101.propSmaller.style}"></h:outputText></TD>
						</TR>
						<TR align="center">
							<TD align="left" style="border-bottom-style:none;border-top-style:none">
								<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlBigger"
								value="#{pc_Ghf00101.propBigger.checked}" 
								disabled="#{pc_Ghf00101.propBigger.disabled}" 
								style="#{pc_Ghf00101.propBigger.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblBigger"
								value="#{pc_Ghf00101.propBigger.name}"
								style="#{pc_Ghf00101.propBigger.style}"></h:outputText>
						</TR>
						<TR align="center">
							<TD align="left" style="border-bottom-style:none;border-top-style:none"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlKanaName"
								value="#{pc_Ghf00101.propKanaName.checked}" 
								disabled="#{pc_Ghf00101.propKanaName.disabled}" 
								style="#{pc_Ghf00101.propKanaName.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblKanaName"
								value="#{pc_Ghf00101.propKanaName.name}"
								style="#{pc_Ghf00101.propKanaName.style}"></h:outputText></TD>
						</TR>
						<TR align="center">
							<TD align="left" style="border-bottom-style:none;border-top-style:none"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlPayLimit"
								value="#{pc_Ghf00101.propPayLimit.checked}" 
								disabled="#{pc_Ghf00101.propPayLimit.disabled}" 
								style="#{pc_Ghf00101.propPayLimit.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblPayLimit"
								value="#{pc_Ghf00101.propPayLimit.name}"
								style="#{pc_Ghf00101.propPayLimit.style}"></h:outputText></TD>
						</TR>
						<TR align="center">
							<TD align="left" style="border-top-style:none"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlTorihikiKouza"
								value="#{pc_Ghf00101.propTorihikiKouza.checked}" 
								disabled="#{pc_Ghf00101.propTorihikiKouza.disabled}" 
								style="#{pc_Ghf00101.propTorihikiKouza.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblTorihikiKouza"
								value="#{pc_Ghf00101.propTorihikiKouza.name}"
								style="#{pc_Ghf00101.propTorihikiKouza.style}"></h:outputText></TD>
						</TR>
					</TBODY>
				</TABLE>
				
				</TD></TR><TR><TD height="15"></TD></TR><TR><TD>
				
				<TABLE class="table" width="100%">
					<TBODY>
						<TR align="center">
							<TH nowrap class="v_d" width="150px"><h:outputText styleClass="outputText" 
								id="lblSyoriKbn" value="#{pc_Ghf00101.propSyoriKbn.name}" style="#{pc_Ghf00101.propSyoriKbn.style}"></h:outputText></TH>
							<TD align="left"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlCheckOnly" 
								value="#{pc_Ghf00101.propCheckOnly.checked}" 
								disabled="#{pc_Ghf00101.propCheckOnly.disabled}" 
								style="#{pc_Ghf00101.propCheckOnly.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblCheckOnly"
								value="#{pc_Ghf00101.propCheckOnly.name}"
								style="#{pc_Ghf00101.propCheckOnly.style}"></h:outputText></TD>
						</TR>
						<TR align="center">
							<TH nowrap class="v_e" rowspan="3"><h:outputText styleClass="outputText"
								id="lblChkList" value="#{pc_Ghf00101.propChkList.name}" style="#{pc_Ghf00101.propChkList.style}"></h:outputText></TH>
							<TD align="left" style="border-bottom-style:none"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlChkListNormal" 
								value="#{pc_Ghf00101.propChkListNormal.checked}" 
								disabled="#{pc_Ghf00101.propChkListNormal.disabled}" 
								style="#{pc_Ghf00101.propChkListNormal.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblChkListNormal" 
								value="#{pc_Ghf00101.propChkListNormal.name}" 
								style="#{pc_Ghf00101.propChkListNormal.style}"></h:outputText></TD>
						</TR>
						<TR align="center">
							<TD align="left" style="border-bottom-style:none;border-top-style:none"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlChkListError"
								value="#{pc_Ghf00101.propChkListError.checked}" 
								disabled="#{pc_Ghf00101.propChkListError.disabled}" 
								style="#{pc_Ghf00101.propChkListError.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblChkListError"
								value="#{pc_Ghf00101.propChkListError.name}"
								style="#{pc_Ghf00101.propChkListError.style}"></h:outputText></TD>
						</TR>
						<TR align="center">
							<TD align="left" style="border-top-style:none"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlChkListWarning"
								value="#{pc_Ghf00101.propChkListWarning.checked}" 
								disabled="#{pc_Ghf00101.propChkListWarning.disabled}" 
								style="#{pc_Ghf00101.propChkListWarning.style}" ></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lblChkListWarning"
								value="#{pc_Ghf00101.propChkListWarning.name}"
								style="#{pc_Ghf00101.propChkListWarning.style}"></h:outputText></TD>
						</TR>
					</TBODY>
				</TABLE>
				
				</TD></TR><TR><TD></TD></TR><TR><TD>
				
				<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit" value="実行"
								styleClass="commandExButton_dat" id="exec" 
								confirm="#{msg.SY_MSG_0001W}" 
								action="#{pc_Ghf00101.doExecAction}" >
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>


    			</TD></TR></TBODY></TABLE>
                </DIV>
                </DIV>
                <!--↑CONTENT↑-->
            </DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />
            
            <h:inputHidden
				value="#{pc_Ghf00101.propInputFileHeaderList.scrollPosition}"
				id="scrollheader"></h:inputHidden>
				
			<h:inputHidden value="#{pc_Ghf00101.propScrollPos.stringValue}" 
				id="htmlScrollPos"></h:inputHidden>

			<h:inputHidden value="#{pc_Ghf00101.propFileKbn.stringValue}"
				id="htmlHidFileKbn"></h:inputHidden>

			<h:inputHidden value="#{pc_Ghf00101.propErrorKbn.stringValue}"
				id="htmlHidErrorKbn"></h:inputHidden>


        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
