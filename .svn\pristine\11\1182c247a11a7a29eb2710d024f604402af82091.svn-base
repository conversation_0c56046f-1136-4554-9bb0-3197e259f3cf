<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kae00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>建設仮勘定振替検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">


function func_KanriBumonAjax(thisObj, thisEvent) {
	// 管理部門名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlKanriBumonName";
	var code = new Array();
	
	code['code1'] = document.getElementById("form1:htmlKanzaiNendoHidden").value;
	code['code2'] = document.getElementById("form1:htmlKanriBumonCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function func_SetchiBashoAjax(thisObj, thisEvent) {
	//設置場所名称を取得する
	var servlet = "rev/ka/KazSetiAJAX";	
	var target = "form1:htmlSetchiBashoName";
	var code = thisObj.value;
	getCodeName(servlet, target, code );
}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function func_1(thisObj, thisEvent) {

	func_KanriBumonAjax(document.getElementById('form1:htmlKanriBumonCd'), '');
	func_SetchiBashoAjax(document.getElementById('form1:htmlSetchiBashoCd'), '');

}

function endload(){
　　changeScrollPosition('scroll', 'listScroll');
}

window.attachEvent('onload', endload);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="return func_1(this, event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kae00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kae00201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kae00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kae00201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<!-- ↓ここに戻る／閉じるボタンを配置 --> 
			<hx:commandExButton type="submit" value="新規登録"
				styleClass="commandExButton" id="register" action="#{pc_Kae00201.doRegisterAction}" tabindex="1">
			</hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			
			<TABLE border="0" width="100%" class="" style="margin-top:0px">
				<TBODY>
					<TR>
						<TD width="3%"></TD>
						<TD width="880">
						<DIV>
			<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="880">
				<TBODY>
					<TR>
								<TH class="v_a" colspan="2" width="200"><h:outputText
									styleClass="outputText" id="lblKenkariNo"
									style="#{pc_Kae00201.propKenkariNo.labelStyle}"
									value="#{pc_Kae00201.propKenkariNo.labelName}">
								</h:outputText></TH>
								<TD width="680">
							<h:inputText id="htmlKenkariNo" styleClass="inputText"
										style="#{pc_Kae00201.propKenkariNo.style}"
										value="#{pc_Kae00201.propKenkariNo.stringValue}"
										maxlength="#{pc_Kae00201.propKenkariNo.maxLength}"
										tabindex="2" size="20">
									</h:inputText>
								<h:outputText
									styleClass="outputText" 
									value="（前方一致）"
									id="lblKenkariNoFindType">
								</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" colspan="2"><h:outputText styleClass="outputText"
							id="lblFurikaeDate"
							value="#{pc_Kae00201.propFurikaeDateFrom.labelName}"
							style="#{pc_Kae00201.propFurikaeDateFrom.labelStyle}">
						</h:outputText></TH>
						<TD nowrap>
							<h:inputText id="htmlFurikaeDateFrom" styleClass="inputText"
										style="#{pc_Kae00201.propFurikaeDateFrom.style}"
										value="#{pc_Kae00201.propFurikaeDateFrom.dateValue}" size="12"
										tabindex="3">
										<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText>
							<h:outputText 
								styleClass="outputText" 
								value="　～"
								id="lblKara2">
							</h:outputText>
							<h:inputText id="htmlFurikaeDateTo" styleClass="inputText"
										style="#{pc_Kae00201.propFurikaeDateTo.style}"
										value="#{pc_Kae00201.propFurikaeDateTo.dateValue}" size="12"
										tabindex="4">
										<f:convertDateTime type="date" pattern="yyyy/MM/dd" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText>
						</TD>
					</TR>			
					<TR>
						<TH class="v_c" colspan="2"><h:outputText styleClass="outputText"
							id="lblAnkenName" value="#{pc_Kae00201.propAnkenName.labelName}"
							style="#{pc_Kae00201.propAnkenName.labelStyle}">
						</h:outputText></TH>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
								<TR>
									<TD width="10">
										<h:inputText id="htmlAnkenName" styleClass="inputText"
												style="#{pc_Kae00201.propAnkenName.style}"
												value="#{pc_Kae00201.propAnkenName.stringValue}"
												maxlength="#{pc_Kae00201.propAnkenName.maxLength}" size="80"
												tabindex="5">
											</h:inputText>
									</TD>
									<TD>
										<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlAnkenNameFindType"
												value="#{pc_Kae00201.propAnkenNameFindType.stringValue}"
												tabindex="6">
												<f:selectItems
													value="#{pc_Kae00201.propAnkenNameFindType.list}" />
											</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_d" colspan="2"><h:outputText styleClass="outputText"
							id="lblKanriBumonCd"
							style="#{pc_Kae00201.propKanriBumonCd.labelStyle}"
							value="#{pc_Kae00201.propKanriBumonCd.labelName}">
						</h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText id="htmlKanriBumonCd" styleClass="inputText"
										style="#{pc_Kae00201.propKanriBumonCd.style}"
										value="#{pc_Kae00201.propKanriBumonCd.stringValue}" size="10"
										maxlength="#{pc_Kae00201.propKanriBumonCd.maxLength}"
										onblur="return func_KanriBumonAjax(this, event);" tabindex="7">
									</h:inputText>
								<h:outputText
									styleClass="outputText" 
									value="（前方一致）"
									id="lblKanriBumonCdFindType">
								</h:outputText>
								<hx:commandExButton type="submit"
										styleClass="commandExButton_search" id="searchKanriBumon"
										action="#{pc_Kae00201.doSearchKanriBumonAction}" tabindex="8">
									</hx:commandExButton>
								<h:outputText styleClass="outputText" id="htmlKanriBumonName">
								</h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TH class="v_e" colspan="2"><h:outputText styleClass="outputText"
							id="lblSetchiBashoCd"
							value="#{pc_Kae00201.propSetchiBashoCd.labelName}"
							style="#{pc_Kae00201.propSetchiBashoCd.labelStyle}">
						</h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText id="htmlSetchiBashoCd" styleClass="inputText"
										style="#{pc_Kae00201.propSetchiBashoCd.style}"
										value="#{pc_Kae00201.propSetchiBashoCd.stringValue}" size="10"
										maxlength="#{pc_Kae00201.propSetchiBashoCd.maxLength}"
										onblur="return func_SetchiBashoAjax(this, event);"
										tabindex="9">
									</h:inputText>
								<h:outputText
									styleClass="outputText" 
									value="（前方一致）"
									id="lblSetchiBashoCdFindType">
								</h:outputText>
								<hx:commandExButton type="submit"
										styleClass="commandExButton_search" id="searchSetchiBasho"
										action="#{pc_Kae00201.doSearchSetchiBashoAction}"
										tabindex="10">
									</hx:commandExButton>
								<h:outputText styleClass="outputText" id="htmlSetchiBashoName">
								</h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TH class="v_f" colspan="2"><h:outputText styleClass="outputText"
										id="lblKeiriSiwakeSakuseiFlg"
										value="#{pc_Kae00201.propKeiriSiwakeSakuseiFlg.name}"
										style="#{pc_Kae00201.propKeiriSiwakeSakuseiFlg.labelStyle}">
									</h:outputText></TH>
						<TD>
							<h:selectManyCheckbox styleClass="selectManyCheckbox setWidth"
										id="htmlKeiriSiwakeSakuseiFlg"
										value="#{pc_Kae00201.propKeiriSiwakeSakuseiFlg.value}"
										disabledClass="selectManyCheckbox_Disabled" tabindex="11"
										readonly="#{pc_Kae00201.propKeiriSiwakeSakuseiFlg.readonly}"
										disabled="#{pc_Kae00201.propKeiriSiwakeSakuseiFlg.disabled}">
										<f:selectItems
											value="#{pc_Kae00201.propKeiriSiwakeSakuseiFlg.list}" />
									</h:selectManyCheckbox>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
							</DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD style="margin-top:10px;">
							<hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search"
							action="#{pc_Kae00201.doSearchAction}" tabindex="12"></hx:commandExButton>
							<hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clear"
							action="#{pc_Kae00201.doClearAction}" tabindex="13"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%" class="" style="margin-top:0px">
				<TBODY>
					<TR>
						<TD width="3%"></TD>
						<TD width="880" align="right"><h:outputText
							styleClass="outputText" id="htmlListCount"
							value="#{pc_Kae00201.propKenfuriList.listCount}"
							style="font-size: 8pt"></h:outputText><SPAN
							style="font-size: 8pt">件</SPAN></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="3%"></TD>
						<TD width="880">
						<DIV class="listScroll"
							style="height:300px; OVERFLOW:scroll;overflow-x: hidden;" 
							id="listScroll" align="center" 
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Kae00201.propKenfuriList.rowClasses}"
							styleClass="meisai_scroll" id="htmlKenfuriList" width="860"
							value="#{pc_Kae00201.propKenfuriList.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblListFurikaeNo" styleClass="outputText"
										value="振替番号"></h:outputText>
								</f:facet>
								<f:attribute value="60" name="width" />
								<h:outputText styleClass="outputText" id="htmlListFurikaeNo"
									value="#{varlist.furikaeNo}"></h:outputText>
								<f:attribute value="padding-right: 13px; text-align: right" name="style" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblListKenkariNo" styleClass="outputText"
										value="建設仮勘定番号"></h:outputText>
								</f:facet>
								<f:attribute value="125" name="width" />
								<h:outputText styleClass="outputText" id="htmlListKenkariNo"
									value="#{varlist.kenkariNo}" 
									title="#{varlist.kenkariNo}">
								</h:outputText>
							</h:column>
<!-- 2009-10-27 追記 振替日付 -->
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblHuriKaeDate" styleClass="outputText" value="振替日付"></h:outputText>
								</f:facet>
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center" name="style" />
								<h:outputText styleClass="outputText" id="htmlHuriKaeDate"
									value="#{varlist.huriKaeDate}"></h:outputText>
							</h:column>							
							
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblListAnkenName" styleClass="outputText"
										value="案件名称"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel1">
									<DIV
										style="width:160px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListAnkenName"
										value="#{varlist.ankenName.stringValue}"
										styleClass="outputText"
										title="#{varlist.ankenName.stringValue}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="170" name="width" />
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblListSsnKbnName" styleClass="outputText"
										value="資産区分"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel2">
									<DIV
										style="width:80px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListSsnKbnName"
										value="#{varlist.ssnKbnName.stringValue}"
										styleClass="outputText"
										title="#{varlist.ssnKbnName.stringValue}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="60" name="width" />
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText id="lblListSsnNo" styleClass="outputText"
										value="資産番号"></h:outputText>
								</f:facet>
								<f:attribute value="135" name="width" />
								<h:outputText styleClass="outputText" id="htmlListSsnNo"
									value="#{varlist.ssnNo}" 
									title="#{varlist.ssnNo}">
								</h:outputText>
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
									<h:outputText id="lblListEdaNo" styleClass="outputText"
										value="枝番"></h:outputText>
								</f:facet>
								<f:attribute value="50" name="width" />
								<h:outputText styleClass="outputText" id="htmlListEdaNo"
									value="#{varlist.edaNoDisp}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
									<h:outputText id="lblListSsnName" styleClass="outputText"
										value="資産名称"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel3">
									<DIV
										style="width:140px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListSsnName"
										value="#{varlist.ssnName.stringValue}" styleClass="outputText"
										title="#{varlist.ssnName.stringValue}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="130" name="width" />
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column9">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="38" name="width" />
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="edit" value="編集" style="width:38"
									action="#{pc_Kae00201.doEditAction}" tabindex="14"></hx:commandExButton>
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kae00201.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kae00201.propKenfuriList.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kae00201.propKanzaiNendoHidden.stringValue}"
				id="htmlKanzaiNendoHidden"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

