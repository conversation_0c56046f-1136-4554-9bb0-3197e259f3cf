<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab01603.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>備品分割登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	openModalWindow("", "Kab01604T01", "<%=com.jast.gakuen.rev.ka.Kab01604T01.getWindowOpenOption() %>");
	setTarget("Kab01604T01");
	return true;
}
	
function confirmOk() {

	var status = document.getElementById('form1:htmlRegisterStatus').value;

	if (status == "REGISTER_READY") {
		document.getElementById('form1:htmlRegisterStatus').value = "CHECK_CHOTATSU";
		indirectClick('register');
	} else if (status == "CHECK_CHOTATSU") {
		document.getElementById('form1:htmlRegisterStatus').value = "CHECK_SHOKYAKU";
		indirectClick('register');
	} else if (status == "CHECK_SHOKYAKU") {
		document.getElementById('form1:htmlRegisterStatus').value = "CHECK_HYOKAGAKU";
		indirectClick('register');
	} else if (status == "CHECK_HYOKAGAKU") {
		document.getElementById('form1:htmlRegisterStatus').value = "CHECK_KIHONKIN";
		indirectClick('register');
	} else if (status == "CHECK_KIHONKIN") {
		document.getElementById('form1:htmlRegisterStatus').value = "REGISTER_DATA";
		indirectClick('register');
	}
}

function confirmCancel() {
	document.getElementById('form1:htmlRegisterStatus').value = "READY";
}

function loadFunc() {
	changeScrollPosition('scroll','listScroll');
}

/**
 * 帳簿価額計算処理（取得価額ロストフォーカスまたは償却累計額ロストフォーカス時）
 */
function getTyoboKagaku1(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku1";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku1').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku1').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku2(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku2";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku2').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku2').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku3(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku3";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku3').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku3').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku4(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku4";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku4').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku4').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku5(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku5";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku5').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku5').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku6(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku6";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku6').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku6').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku7(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku7";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku7').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku7').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku8(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku8";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku8').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku8').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku9(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku9";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku9').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku9').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function getTyoboKagaku10(thisObj, thisEvent) {
	var servlet = "rev/ka/KabTyoboKagakuAJAX";
	var target = "form1:htmlTyoboKagaku10";
	var args = new Array();
	args['code1'] = FormatNumber.getValue(document.getElementById('form1:htmlShutokuKagaku10').value);
	args['code2'] = FormatNumber.getValue(document.getElementById('form1:htmlShokyakuRuikeiGaku10').value);
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

window.attachEvent("onload", attachFormatNumber);
window.attachEvent("onload", loadFunc);

</SCRIPT>

<!-- tableを二重時のstyle対応 -->
<style type="text/css">
table.table_outer_frame {
border-collapse :collapse;
font-size: 8pt;
color: #333333;
border-width: 1px;
}
table.table_outer_frame td {
text-align:left;
vertical-align:middle;
border: 1px solid #CCCCCC;
background-color:#FFFFFF;
padding: 0px;
empty-cells: hide;
height:20px;
padding-left:2px;
}
</style>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kab01603.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<DIV style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kab01603.doCloseDispAction}"
				></hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kab01603.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kab01603.screenName}"></h:outputText>
			</DIV>
			
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
			<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText>
			</FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<!-- 戻るボタンを配置 --> 
			<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp"
				action="#{pc_Kab01603.doReturnDispAction}"
				disabled="#{pc_Kab01603.propReturnDisp.disabled}"
				rendered="#{pc_Kab01603.propReturnDisp.rendered}"
				style="#{pc_Kab01603.propReturnDisp.style}"></hx:commandExButton>
			<!-- 戻るボタンを配置 -->	
			</DIV>
			<DIV id="content">
			<DIV class="column"><!-- ↓ここにコンポーネントを配置 -->
			<DIV align="left">
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="311" style="margin-left: 20px;">
				<TBODY>
					<TR>
						<TH class="v_a" width="150" align="left"><h:outputText
							styleClass="outputText" id="lblBunkatuDay"
							value="#{pc_Kab01603.propBunkatuDay.labelName}"
							style="#{pc_Kab01603.propBunkatuDay.labelStyle}"></h:outputText></TH>
						<TD width="159" align="left"><h:inputText styleClass="inputText"
							id="htmlBunkatuDay"
							value="#{pc_Kab01603.propBunkatuDay.dateValue}" size="10"
							readonly="#{pc_Kab01603.propBunkatuDay.readonly}"
							disabled="#{pc_Kab01603.propBunkatuDay.disabled}"
							style="#{pc_Kab01603.propBunkatuDay.style}"
							rendered="#{pc_Kab01603.propBunkatuDay.rendered}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
												  promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV><DIV id="center">
			
			<DIV class="listScroll" style="height:460px;width:900px;margin-top:10px;" id="listScroll" align="center"
			onscroll="setScrollPosition('scroll',this);">
			
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
				style="margin-top: 0px;">
				<TBODY>
					<TR align="center">
						<TD width="900">分割前
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="850" style="margin-left: 10px;">
							<TBODY>
								<TR>
									<TH class="v_a" width="171"><h:outputText
										styleClass="outputText" id="lblBhnNo"
										value="#{pc_Kab01603.propBhnNo.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlBhnNo" value="#{pc_Kab01603.propBhnNo.stringValue}"></h:outputText></TD>
									<TH class="v_b" width="151">
										<h:outputText styleClass="outputText" id="lblEdaNo"
										value="#{pc_Kab01603.propEdaNo.name}"
										style="#{pc_Kab01603.propEdaNo.labelStyle}">
									</h:outputText>
									</TH>
									<TD width="305">
										<h:inputText id="htmlEdaNo" size="5"
										value="#{pc_Kab01603.propEdaNo.stringValue}"
										disabled="#{pc_Kab01603.propEdaNo.disabled}"
										maxlength="#{pc_Kab01603.propEdaNo.maxLength}"
										readonly="#{pc_Kab01603.propEdaNo.readonly}"
										rendered="#{pc_Kab01603.propEdaNo.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo.style}"
										styleClass="inputText">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText>
									<h:outputText styleClass="outputText" id="htmlLabelEdaNo"
										rendered="#{pc_Kab01603.propLabelEdaNo.rendered}"
										style="#{pc_Kab01603.propLabelEdaNo.style}"
										value="#{pc_Kab01603.propLabelEdaNo.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="171"><h:outputText
										styleClass="outputText" id="lblShutokuDate"
										style="#{pc_Kab01603.propShutokuDate.labelStyle}"
										value="#{pc_Kab01603.propShutokuDate.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlShutokuDate"
										style="#{pc_Kab01603.propShutokuDate.style}"
										value="#{pc_Kab01603.propShutokuDate.dateValue}">
										<f:convertDateTime pattern="yyyy/MM/dd" />
									</h:outputText></TD>
									<TH class="v_e" width="151"><h:outputText
										styleClass="outputText" id="lblSsnBnrRyak"
										value="#{pc_Kab01603.propSsnBnrRyak.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlSsnBnrRyak"
										value="#{pc_Kab01603.propSsnBnrRyak.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="171"><h:outputText
										styleClass="outputText" id="lblSsnName"
										value="#{pc_Kab01603.propSsnName.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:outputText styleClass="outputText"
										id="htmlSsnName"
										value="#{pc_Kab01603.propSsnName.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="171"><h:outputText
										styleClass="outputText" id="lblSsnNameRyak"
										value="#{pc_Kab01603.propSsnNameRyak.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlSsnNameRyak"
										value="#{pc_Kab01603.propSsnNameRyak.stringValue}"></h:outputText></TD>
									<TH class="v_f" width="151"><h:outputText
										styleClass="outputText" id="lblChotatsuSu"
										value="#{pc_Kab01603.propChotatsuSu.labelName}"></h:outputText>／<h:outputText
										styleClass="outputText" id="lblTani"
										value="#{pc_Kab01603.propTani.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlChotatsuSu"
										value="#{pc_Kab01603.propChotatsuSu.integerValue}">
										<f:convertNumber pattern="##,###" />
									</h:outputText> <h:outputText styleClass="outputText"
										id="htmlTani" value="#{pc_Kab01603.propTani.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g" width="171"><h:outputText
										styleClass="outputText" id="lblShutokuKagaku"
										value="#{pc_Kab01603.propShutokuKagaku.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlShutokuKagaku"
										value="#{pc_Kab01603.propShutokuKagaku.longValue}">
										<f:convertNumber pattern="#,###,###,###,###" />
									</h:outputText></TD>
									<TH class="v_d" width="151"><h:outputText
										styleClass="outputText" id="lblTyoboKagaku"
										value="#{pc_Kab01603.propTyoboKagaku.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlTyoboKagaku"
										value="#{pc_Kab01603.propTyoboKagaku.longValue}">
										<f:convertNumber pattern="#,###,###,###,###" />
									</h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="171"><h:outputText
										styleClass="outputText" id="lblShokyakuGaku"
										value="#{pc_Kab01603.propShokyakuGaku.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlShokyakuGaku"
										value="#{pc_Kab01603.propShokyakuGaku.longValue}">
										<f:convertNumber pattern="#,###,###,###,###" />
									</h:outputText></TD>
									<TH class="v_c" width="151"><h:outputText
										styleClass="outputText" id="lblShokyakuRuikeiGaku"
										value="#{pc_Kab01603.propShokyakuRuikeiGaku.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmllShokyakuRuikeiGaku"
										value="#{pc_Kab01603.propShokyakuRuikeiGaku.longValue}">
										<f:convertNumber pattern="#,###,###,###,###" />
									</h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_f" width="171"><h:outputText
										styleClass="outputText" id="lblKihonkinKumiireGaku"
										value="#{pc_Kab01603.propKihonkinKumiireGaku.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlKihonkinKumiireGaku"
										value="#{pc_Kab01603.propKihonkinKumiireGaku.longValue}">
										<f:convertNumber pattern="#,###,###,###,###" />
									</h:outputText></TD>
									<TH class="v_e" width="151"><h:outputText
										styleClass="outputText" id="lblHyokaGaku"
										value="#{pc_Kab01603.propHyokaGaku.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlHyokaGaku"
										value="#{pc_Kab01603.propHyokaGaku.longValue}">
										<f:convertNumber pattern="#,###,###,###,###" />
									</h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="171"><h:outputText
										styleClass="outputText" id="lblHoteiTaiyoNensuShurui"
										style="#{pc_Kab01603.propHoteiTaiyoNensuShurui.labelStyle}"
										value="#{pc_Kab01603.propHoteiTaiyoNensuShurui.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlHoteiTaiyoNensuShurui"
										style="#{pc_Kab01603.propHoteiTaiyoNensuShurui.style}"
										value="#{pc_Kab01603.propHoteiTaiyoNensuShurui.stringValue}"></h:outputText></TD>
									<TH class="v_a" width="151"><h:outputText
										styleClass="outputText" id="lblTaiyoNensu"
										value="#{pc_Kab01603.propTaiyoNensu.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlTaiyoNensu"
										value="#{pc_Kab01603.propTaiyoNensu.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g" width="171"><h:outputText
										styleClass="outputText" id="lblShokyakuStartNendo"
										value="#{pc_Kab01603.propShokyakuStartNendo.labelName}"></h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlShokyakuStartNendo"
										value="#{pc_Kab01603.propShokyakuStartNendo.dateValue}">
										<f:convertDateTime pattern="yyyy" />
									</h:outputText></TD>
									<TH class="v_a" width="151"><h:outputText
										styleClass="outputText" id="lblShokyakuKaisu"
										value="#{pc_Kab01603.propShokyakuKaisu.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlShokyakuKaisu"
										value="#{pc_Kab01603.propShokyakuKaisu.stringValue}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
							style="margin-top:0px;margin-left: 10px;" class="button_bar">
							<TBODY>
								<TR>
									<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
										value="関連情報" styleClass="commandExButton_etc" id="kanren0"
										action="#{pc_Kab01603.doKanren0Action}"
										onclick="return func_1(this, event);"
										disabled="#{pc_Kab01603.propKanren0.disabled}"
										rendered="#{pc_Kab01603.propKanren0.rendered}"
										style="#{pc_Kab01603.propKanren0.style}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
				style="margin-top:10px;">
				<TBODY>
					<TR>
						<TD width="900">分割先１
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="850" style="margin-left: 10px;">
							<TBODY>
								<TR>
									<TH width="171" class="v_a"><h:outputText
										styleClass="outputText" id="lblBhnNo1"
										value="#{pc_Kab01603.propBhnNo1.labelName}">
									</h:outputText></TH>
									<TD width="220"><h:outputText styleClass="outputText"
										id="htmlBhnNo1" value="#{pc_Kab01603.propBhnNo1.stringValue}"></h:outputText></TD>
									<TH width="151" class="v_b">
										<h:outputText styleClass="outputText" id="lblEdaNo1"
										value="#{pc_Kab01603.propEdaNo1.name}"
										style="#{pc_Kab01603.propEdaNo1.labelStyle}">
									</h:outputText>
									</TH>
									<TD width="305">
										<h:inputText id="htmlEdaNo1" size="5"
										value="#{pc_Kab01603.propEdaNo1.stringValue}"
										disabled="#{pc_Kab01603.propEdaNo1.disabled}"
										maxlength="#{pc_Kab01603.propEdaNo1.maxLength}"
										readonly="#{pc_Kab01603.propEdaNo1.readonly}"
										rendered="#{pc_Kab01603.propEdaNo1.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo1.style}"
										styleClass="inputText">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText>
									<h:outputText styleClass="outputText" id="htmlLabelEdaNo1"
										rendered="#{pc_Kab01603.propLabelEdaNo1.rendered}"
										style="#{pc_Kab01603.propLabelEdaNo1.style}"
										value="#{pc_Kab01603.propLabelEdaNo1.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_c"><h:outputText
										styleClass="outputText" id="lblSsnName1"
										value="#{pc_Kab01603.propSsnName1.labelName}"
										style="#{pc_Kab01603.propSsnName1.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlSsnName1" size="100"
										value="#{pc_Kab01603.propSsnName1.stringValue}"
										maxlength="#{pc_Kab01603.propSsnName1.maxLength}"
										readonly="#{pc_Kab01603.propSsnName1.readonly}"
										rendered="#{pc_Kab01603.propSsnName1.rendered}"
										disabled="#{pc_Kab01603.propSsnName1.disabled}"
										style="#{pc_Kab01603.propSsnName1.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_d"><h:outputText
										styleClass="outputText" id="lblSsnRyak1"
										value="#{pc_Kab01603.propSsnNameRyak1.labelName}"
										style="#{pc_Kab01603.propSsnNameRyak1.labelStyle}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlSsnRyak1" size="20"
										value="#{pc_Kab01603.propSsnNameRyak1.stringValue}"
										style="#{pc_Kab01603.propSsnNameRyak1.style}"
										maxlength="#{pc_Kab01603.propSsnNameRyak1.maxLength}"
										readonly="#{pc_Kab01603.propSsnNameRyak1.readonly}"
										rendered="#{pc_Kab01603.propSsnNameRyak1.rendered}"
										disabled="#{pc_Kab01603.propSsnNameRyak1.disabled}"></h:inputText></TD>
									<TH width="151" class="v_e"><h:outputText
										styleClass="outputText" id="lblChotatuSu1"
										value="#{pc_Kab01603.propChotatsuSu1.labelName}"
										style="#{pc_Kab01603.propChotatsuSu1.labelStyle}"></h:outputText>／<h:outputText
										styleClass="outputText" id="lblTani1"
										value="#{pc_Kab01603.propTani1.labelName}"></h:outputText></TH>
									<TD width="305">
									<TABLE border="1" class="clear_border">
										<TBODY>
											<TR>
												<TD><h:inputText styleClass="inputText" id="htmlChotatsuSu1"
													size="6" value="#{pc_Kab01603.propChotatsuSu1.stringValue}"
													disabled="#{pc_Kab01603.propChotatsuSu1.disabled}"
													maxlength="#{pc_Kab01603.propChotatsuSu1.maxLength}"
													readonly="#{pc_Kab01603.propChotatsuSu1.readonly}"
													rendered="#{pc_Kab01603.propChotatsuSu1.rendered}"
													style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu1.style}">
													<hx:inputHelperAssist errorClass="inputText_Error" />
												</h:inputText></TD>
												<TD><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlTani1" disabled="#{pc_Kab01603.propTani1.disabled}"
													readonly="#{pc_Kab01603.propTani1.readonly}"
													rendered="#{pc_Kab01603.propTani1.rendered}"
													style="#{pc_Kab01603.propTani1.style}"
													value="#{pc_Kab01603.propTani1.value}">
													<f:selectItems value="#{pc_Kab01603.propTani1.list}" />
												</h:selectOneMenu></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH width="171" class="v_f"><h:outputText
										styleClass="outputText" id="lblShutokuKagaku1"
										value="#{pc_Kab01603.propShutokuKagaku1.labelName}"
										style="#{pc_Kab01603.propShutokuKagaku1.labelStyle}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlShutokuKagaku1" size="17"
										value="#{pc_Kab01603.propShutokuKagaku1.stringValue}"
										disabled="#{pc_Kab01603.propShutokuKagaku1.disabled}"
										maxlength="#{pc_Kab01603.propShutokuKagaku1.maxLength}"
										readonly="#{pc_Kab01603.propShutokuKagaku1.readonly}"
										rendered="#{pc_Kab01603.propShutokuKagaku1.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku1.style}"
										onblur="return getTyoboKagaku1(this, event);">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
									<TH width="151" class="v_b"><h:outputText
										styleClass="outputText" id="lblTyoboKagaku1"
										value="#{pc_Kab01603.propTyoboKagaku1.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlTyoboKagaku1"
										value="#{pc_Kab01603.propTyoboKagaku1.longValue}">
										<f:convertNumber pattern="#,###,###,###,##0" />
									</h:outputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_g"><h:outputText
										styleClass="outputText" id="lblShokyakuGaku1"
										value="#{pc_Kab01603.propShokyakuGaku1.labelName}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlShokyakuGaku1" size="17"
										value="#{pc_Kab01603.propShokyakuGaku1.stringValue}"
										disabled="#{pc_Kab01603.propShokyakuGaku1.disabled}"
										maxlength="#{pc_Kab01603.propShokyakuGaku1.maxLength}"
										readonly="#{pc_Kab01603.propShokyakuGaku1.readonly}"
										rendered="#{pc_Kab01603.propShokyakuGaku1.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku1.style}">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
									<TH width="151" class="v_a"><h:outputText
										styleClass="outputText" id="lblShokyakuRuikeiGaku1"
										value="#{pc_Kab01603.propShokyakuRuikeiGaku1.labelName}"></h:outputText></TH>
									<TD width="305"><h:inputText styleClass="inputText"
										id="htmlShokyakuRuikeiGaku1" size="17"
										value="#{pc_Kab01603.propShokyakuRuikeiGaku1.stringValue}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku1.style}"
										disabled="#{pc_Kab01603.propShokyakuRuikeiGaku1.disabled}"
										maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku1.maxLength}"
										readonly="#{pc_Kab01603.propShokyakuRuikeiGaku1.readonly}"
										rendered="#{pc_Kab01603.propShokyakuRuikeiGaku1.rendered}"
										onblur="return getTyoboKagaku1(this, event);">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_d"><h:outputText
										styleClass="outputText" id="lblKihonkinKumiireGaku1"
										value="#{pc_Kab01603.propKihonkinKumiireGaku1.labelName}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlKihonkinKumiireGaku1" size="17"
										value="#{pc_Kab01603.propKihonkinKumiireGaku1.stringValue}"
										disabled="#{pc_Kab01603.propKihonkinKumiireGaku1.disabled}"
										maxlength="#{pc_Kab01603.propKihonkinKumiireGaku1.maxLength}"
										readonly="#{pc_Kab01603.propKihonkinKumiireGaku1.readonly}"
										rendered="#{pc_Kab01603.propKihonkinKumiireGaku1.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku1.style}">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
									<TH width="151" class="v_c"><h:outputText
										styleClass="outputText" id="lblHyokaGaku1"
										value="#{pc_Kab01603.propHyokaGaku1.labelName}"
										style="#{pc_Kab01603.propSsnName1.style}"></h:outputText></TH>
									<TD width="305"><h:inputText styleClass="inputText"
										id="htmlHyokaGaku1" size="17"
										value="#{pc_Kab01603.propHyokaGaku1.stringValue}"
										disabled="#{pc_Kab01603.propHyokaGaku1.disabled}"
										maxlength="#{pc_Kab01603.propHyokaGaku1.maxLength}"
										readonly="#{pc_Kab01603.propHyokaGaku1.readonly}"
										rendered="#{pc_Kab01603.propHyokaGaku1.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku1.style}">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
							style="margin-top:0px;margin-left: 10px;" class="button_bar">
							<TBODY>
								<TR>
									<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
										value="関連情報" styleClass="commandExButton_etc" id="kanren1"
										action="#{pc_Kab01603.doKanren1Action}"
										onclick="return func_1(this, event);"
										disabled="#{pc_Kab01603.propKanren1.disabled}"
										rendered="#{pc_Kab01603.propKanren1.rendered}"
										style="#{pc_Kab01603.propKanren1.style}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
				style="margin-top: 10px;">
				<TBODY>
					<TR>
						<TD width="900">分割先２
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="850" style="margin-left: 10px;">
							<TBODY>
								<TR>
									<TH width="171" class="v_a"><h:outputText
										styleClass="outputText" id="lblBhnNo2"
										value="#{pc_Kab01603.propBhnNo2.labelName}"
										style="#{pc_Kab01603.propBhnNo2.labelStyle}"></h:outputText></TH>
									<TD width="220"><h:inputText id="htmlBhnNo2" size="20"
										value="#{pc_Kab01603.propBhnNo2.stringValue}"
										disabled="#{pc_Kab01603.propBhnNo2.disabled}"
										maxlength="#{pc_Kab01603.propBhnNo2.maxLength}"
										readonly="#{pc_Kab01603.propBhnNo2.readonly}"
										rendered="#{pc_Kab01603.propBhnNo2.rendered}"
										style="#{pc_Kab01603.propBhnNo2.style}" styleClass="inputText">
									</h:inputText><h:outputText styleClass="outputText"
										id="htmlLabelBhnNo2"
										rendered="#{pc_Kab01603.propLabelBhnNo2.rendered}"
										style="#{pc_Kab01603.propLabelBhnNo2.style}"
										value="#{pc_Kab01603.propLabelBhnNo2.stringValue}"></h:outputText></TD>
									<TH width="151" class="v_b"><h:outputText
										styleClass="outputText" id="lblEdaNo2"
										value="#{pc_Kab01603.propEdaNo2.labelName}"
										style="#{pc_Kab01603.propBhnNo2.labelStyle}"></h:outputText></TH>
									<TD width="305"><h:inputText id="htmlEdaNo2" size="5"
										value="#{pc_Kab01603.propEdaNo2.stringValue}"
										disabled="#{pc_Kab01603.propEdaNo2.disabled}"
										maxlength="#{pc_Kab01603.propEdaNo2.maxLength}"
										readonly="#{pc_Kab01603.propEdaNo2.readonly}"
										rendered="#{pc_Kab01603.propEdaNo2.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo2.style}"
										styleClass="inputText">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText><h:outputText styleClass="outputText"
										id="htmlLabelEdaNo2"
										rendered="#{pc_Kab01603.propLabelEdaNo2.rendered}"
										style="#{pc_Kab01603.propLabelEdaNo2.style}"
										value="#{pc_Kab01603.propLabelEdaNo2.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="171" class='v_c'><h:outputText
										styleClass="outputText" id="lblSsnName2"
										value="#{pc_Kab01603.propSsnName2.labelName}"
										style="#{pc_Kab01603.propSsnName2.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlSsnName2" size="100"
										value="#{pc_Kab01603.propSsnName2.stringValue}"
										style="#{pc_Kab01603.propSsnName2.style}"
										disabled="#{pc_Kab01603.propSsnName2.disabled}"
										maxlength="#{pc_Kab01603.propSsnName2.maxLength}"
										readonly="#{pc_Kab01603.propSsnName2.readonly}"
										rendered="#{pc_Kab01603.propSsnName2.rendered}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_d"><h:outputText
										styleClass="outputText" id="lblSsnRyak2"
										value="#{pc_Kab01603.propSsnNameRyak2.labelName}"
										style="#{pc_Kab01603.propSsnNameRyak2.labelStyle}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlSsnRyak2" size="20"
										value="#{pc_Kab01603.propSsnNameRyak2.stringValue}"
										disabled="#{pc_Kab01603.propSsnNameRyak2.disabled}"
										maxlength="#{pc_Kab01603.propSsnNameRyak2.maxLength}"
										readonly="#{pc_Kab01603.propSsnNameRyak2.readonly}"
										rendered="#{pc_Kab01603.propSsnNameRyak2.rendered}"
										style="#{pc_Kab01603.propSsnNameRyak2.style}"></h:inputText></TD>
									<TH width="151" class="v_e"><h:outputText
										styleClass="outputText" id="lblChotatuSu2"
										value="#{pc_Kab01603.propChotatsuSu2.labelName}"
										style="#{pc_Kab01603.propChotatsuSu2.labelStyle}"></h:outputText>／<h:outputText
										styleClass="outputText" id="lblTani2"
										value="#{pc_Kab01603.propTani2.labelName}"></h:outputText></TH>
									<TD width="305">
									<TABLE border="1" class="clear_border">
										<TBODY>
											<TR>
												<TD><h:inputText styleClass="inputText" id="htmlChotatsuSu2"
													size="6" value="#{pc_Kab01603.propChotatsuSu2.stringValue}"
													disabled="#{pc_Kab01603.propChotatsuSu2.disabled}"
													maxlength="#{pc_Kab01603.propChotatsuSu2.maxLength}"
													readonly="#{pc_Kab01603.propChotatsuSu2.readonly}"
													rendered="#{pc_Kab01603.propChotatsuSu2.rendered}"
													style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu2.style}">
													<hx:inputHelperAssist errorClass="inputText_Error" />
												</h:inputText></TD>
												<TD><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlTani2" disabled="#{pc_Kab01603.propTani2.disabled}"
													readonly="#{pc_Kab01603.propTani2.readonly}"
													rendered="#{pc_Kab01603.propTani2.rendered}"
													value="#{pc_Kab01603.propTani2.value}"
													style="#{pc_Kab01603.propTani2.style}">
													<f:selectItems value="#{pc_Kab01603.propTani2.list}" />
												</h:selectOneMenu></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH width="171" class="v_f"><h:outputText
										styleClass="outputText" id="lblShutokuKagaku2"
										value="#{pc_Kab01603.propShutokuKagaku2.labelName}"
										style="#{pc_Kab01603.propShutokuKagaku2.labelStyle}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlShutokuKagaku2" size="17"
										value="#{pc_Kab01603.propShutokuKagaku2.stringValue}"
										disabled="#{pc_Kab01603.propShutokuKagaku2.disabled}"
										maxlength="#{pc_Kab01603.propShutokuKagaku2.maxLength}"
										readonly="#{pc_Kab01603.propShutokuKagaku2.readonly}"
										rendered="#{pc_Kab01603.propShutokuKagaku2.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku2.style}"
										onblur="return getTyoboKagaku2(this, event);">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
									<TH width="151" class="v_b"><h:outputText
										styleClass="outputText" id="lblTyoboKagaku2"
										value="#{pc_Kab01603.propTyoboKagaku2.labelName}"></h:outputText></TH>
									<TD width="305"><h:outputText styleClass="outputText"
										id="htmlTyoboKagaku2"
										value="#{pc_Kab01603.propTyoboKagaku2.longValue}">
										<f:convertNumber pattern="#,###,###,###,##0" />
									</h:outputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_g"><h:outputText
										styleClass="outputText" id="lblShokyakuGaku2"
										value="#{pc_Kab01603.propShokyakuGaku2.labelName}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlShokyakuGaku2" size="17"
										value="#{pc_Kab01603.propShokyakuGaku2.stringValue}"
										disabled="#{pc_Kab01603.propShokyakuGaku2.disabled}"
										maxlength="#{pc_Kab01603.propShokyakuGaku2.maxLength}"
										readonly="#{pc_Kab01603.propShokyakuGaku2.readonly}"
										rendered="#{pc_Kab01603.propShokyakuGaku2.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku2.style}">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
									<TH width="151" class="v_a"><h:outputText
										styleClass="outputText" id="lblShokyakuRuikeiGaku2"
										value="#{pc_Kab01603.propShokyakuRuikeiGaku2.labelName}"></h:outputText></TH>
									<TD width="305"><h:inputText styleClass="inputText"
										id="htmlShokyakuRuikeiGaku2" size="17"
										value="#{pc_Kab01603.propShokyakuRuikeiGaku2.stringValue}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku2.style}"
										disabled="#{pc_Kab01603.propShokyakuRuikeiGaku2.disabled}"
										maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku2.maxLength}"
										readonly="#{pc_Kab01603.propShokyakuRuikeiGaku2.readonly}"
										rendered="#{pc_Kab01603.propShokyakuRuikeiGaku2.rendered}"
										onblur="return getTyoboKagaku2(this, event);">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_d"><h:outputText
										styleClass="outputText" id="lblKihonkinKumiireGaku2"
										value="#{pc_Kab01603.propKihonkinKumiireGaku2.labelName}"></h:outputText></TH>
									<TD width="220"><h:inputText styleClass="inputText"
										id="htmlKihonkinKumiireGaku2" size="17"
										value="#{pc_Kab01603.propKihonkinKumiireGaku2.stringValue}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku2.style}"
										disabled="#{pc_Kab01603.propKihonkinKumiireGaku2.disabled}"
										maxlength="#{pc_Kab01603.propKihonkinKumiireGaku2.maxLength}"
										readonly="#{pc_Kab01603.propKihonkinKumiireGaku2.readonly}"
										rendered="#{pc_Kab01603.propKihonkinKumiireGaku2.rendered}">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
									<TH width="151" class="v_c"><h:outputText
										styleClass="outputText" id="lblHyokaGaku2"
										value="#{pc_Kab01603.propHyokaGaku2.labelName}"></h:outputText></TH>
									<TD width="305"><h:inputText styleClass="inputText"
										id="htmlHyokaGaku2" size="17"
										value="#{pc_Kab01603.propHyokaGaku2.stringValue}"
										disabled="#{pc_Kab01603.propHyokaGaku2.disabled}"
										maxlength="#{pc_Kab01603.propHyokaGaku2.maxLength}"
										readonly="#{pc_Kab01603.propHyokaGaku2.readonly}"
										rendered="#{pc_Kab01603.propHyokaGaku2.rendered}"
										style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku2.style}">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
							style="margin-top:0px;margin-left: 10px;" class="button_bar">
							<TBODY>
								<TR>
									<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
										value="関連情報" styleClass="commandExButton_etc" id="kanren2"
										action="#{pc_Kab01603.doKanren2Action}"
										onclick="return func_1(this, event);"
										disabled="#{pc_Kab01603.propKanren2.disabled}"
										rendered="#{pc_Kab01603.propKanren2.rendered}"
										style="#{pc_Kab01603.propKanren2.style}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<hx:jspPanel id="panel1" rendered="#{pc_Kab01603.propPanel1.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900">分割先３
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo3"
											value="#{pc_Kab01603.propBhnNo3.labelName}"
											style="#{pc_Kab01603.propBhnNo3.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo3" size="20"
											value="#{pc_Kab01603.propBhnNo3.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo3.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo3.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo3.readonly}"
											rendered="#{pc_Kab01603.propBhnNo3.rendered}"
											style="#{pc_Kab01603.propBhnNo3.style}">
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelBhnNo3"
											rendered="#{pc_Kab01603.propLabelBhnNo3.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo3.style}"
											value="#{pc_Kab01603.propLabelBhnNo3.stringValue}"></h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo3"
											value="#{pc_Kab01603.propEdaNo3.labelName}"
											style="#{pc_Kab01603.propEdaNo3.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo3" size="5"
											value="#{pc_Kab01603.propEdaNo3.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo3.style}"
											disabled="#{pc_Kab01603.propEdaNo3.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo3.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo3.readonly}"
											rendered="#{pc_Kab01603.propEdaNo3.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo3"
											rendered="#{pc_Kab01603.propLabelEdaNo3.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo3.style}"
											value="#{pc_Kab01603.propLabelEdaNo3.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName3"
											value="#{pc_Kab01603.propSsnName3.labelName}"
											style="#{pc_Kab01603.propSsnName3.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName3" size="100"
											value="#{pc_Kab01603.propSsnName3.stringValue}"
											style="#{pc_Kab01603.propSsnName3.style}"
											disabled="#{pc_Kab01603.propSsnName3.disabled}"
											maxlength="#{pc_Kab01603.propSsnName3.maxLength}"
											readonly="#{pc_Kab01603.propSsnName3.readonly}"
											rendered="#{pc_Kab01603.propSsnName3.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak3"
											value="#{pc_Kab01603.propSsnNameRyak3.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak3.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak3"
											size="20" value="#{pc_Kab01603.propSsnNameRyak3.stringValue}"
											style="#{pc_Kab01603.propSsnNameRyak3.style}"
											disabled="#{pc_Kab01603.propSsnNameRyak3.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak3.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak3.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak3.rendered}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu3"
											value="#{pc_Kab01603.propChotatsuSu3.labelName}"
											style="#{pc_Kab01603.propChotatsuSu3.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani3"
											value="#{pc_Kab01603.propTani3.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu3" size="6"
														value="#{pc_Kab01603.propChotatsuSu3.stringValue}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu3.style}"
														disabled="#{pc_Kab01603.propChotatsuSu3.disabled}"
														maxlength="#{pc_Kab01603.propChotatsuSu3.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu3.readonly}"
														rendered="#{pc_Kab01603.propChotatsuSu3.rendered}">
														<hx:inputHelperAssist errorClass="inputText_Error" />
													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani3"
														disabled="#{pc_Kab01603.propTani3.disabled}"
														readonly="#{pc_Kab01603.propTani3.readonly}"
														rendered="#{pc_Kab01603.propTani3.rendered}"
														style="#{pc_Kab01603.propTani3.style}"
														value="#{pc_Kab01603.propTani3.value}">
														<f:selectItems value="#{pc_Kab01603.propTani3.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku3"
											value="#{pc_Kab01603.propShutokuKagaku3.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku3.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku3" size="17"
											value="#{pc_Kab01603.propShutokuKagaku3.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku3.style}"
											disabled="#{pc_Kab01603.propShutokuKagaku3.disabled}"
											maxlength="#{pc_Kab01603.propShutokuKagaku3.maxLength}"
											readonly="#{pc_Kab01603.propShutokuKagaku3.readonly}"
											rendered="#{pc_Kab01603.propShutokuKagaku3.rendered}"
											onblur="return getTyoboKagaku3(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku3"
											value="#{pc_Kab01603.propTyoboKagaku3.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku3"
											value="#{pc_Kab01603.propTyoboKagaku3.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku3"
											value="#{pc_Kab01603.propShokyakuGaku3.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku3" size="17"
											value="#{pc_Kab01603.propShokyakuGaku3.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku3.style}"
											disabled="#{pc_Kab01603.propShokyakuGaku3.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku3.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku3.readonly}"
											rendered="#{pc_Kab01603.propShokyakuGaku3.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku3"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku3.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku3" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku3.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku3.style}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku3.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku3.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku3.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku3.rendered}"
											onblur="return getTyoboKagaku3(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku3"
											value="#{pc_Kab01603.propKihonkinKumiireGaku3.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku3" size="17"
											value="#{pc_Kab01603.propKihonkinKumiireGaku3.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku3.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku3.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku3.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku3.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku3.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku3"
											value="#{pc_Kab01603.propHyokaGaku3.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku3"
											size="17" value="#{pc_Kab01603.propHyokaGaku3.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku3.style}"
											disabled="#{pc_Kab01603.propHyokaGaku3.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku3.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku3.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku3.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">
								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren3"
											action="#{pc_Kab01603.doKanren3Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren3.disabled}"
											rendered="#{pc_Kab01603.propKanren3.rendered}"
											style="#{pc_Kab01603.propKanren3.style}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			<hx:jspPanel id="panel2" rendered="#{pc_Kab01603.propPanel2.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900">分割先４
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo4"
											value="#{pc_Kab01603.propBhnNo4.labelName}"
											style="#{pc_Kab01603.propBhnNo4.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo4" size="20"
											value="#{pc_Kab01603.propBhnNo4.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo4.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo4.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo4.readonly}"
											rendered="#{pc_Kab01603.propBhnNo4.rendered}"
											style="#{pc_Kab01603.propBhnNo4.style}"></h:inputText><h:outputText
											styleClass="outputText" id="htmlLabelBhnNo4"
											rendered="#{pc_Kab01603.propLabelBhnNo4.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo4.style}"
											value="#{pc_Kab01603.propLabelBhnNo4.stringValue}">
										</h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo4"
											value="#{pc_Kab01603.propEdaNo4.labelName}"
											style="#{pc_Kab01603.propEdaNo4.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo4" size="5"
											value="#{pc_Kab01603.propEdaNo4.stringValue}"
											disabled="#{pc_Kab01603.propEdaNo4.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo4.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo4.readonly}"
											rendered="#{pc_Kab01603.propEdaNo4.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo4.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo4"
											rendered="#{pc_Kab01603.propLabelEdaNo4.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo4.style}"
											value="#{pc_Kab01603.propLabelEdaNo4.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName4"
											value="#{pc_Kab01603.propSsnName4.labelName}"
											style="#{pc_Kab01603.propSsnName4.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName4" size="100"
											value="#{pc_Kab01603.propSsnName4.stringValue}"
											style="#{pc_Kab01603.propSsnName4.style}"
											disabled="#{pc_Kab01603.propSsnName4.disabled}"
											maxlength="#{pc_Kab01603.propSsnName4.maxLength}"
											readonly="#{pc_Kab01603.propSsnName4.readonly}"
											rendered="#{pc_Kab01603.propSsnName4.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak4"
											value="#{pc_Kab01603.propSsnNameRyak4.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak4.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak4"
											size="20" value="#{pc_Kab01603.propSsnNameRyak4.stringValue}"
											disabled="#{pc_Kab01603.propSsnNameRyak4.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak4.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak4.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak4.rendered}"
											style="#{pc_Kab01603.propSsnNameRyak4.style}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu4"
											value="#{pc_Kab01603.propChotatsuSu4.labelName}"
											style="#{pc_Kab01603.propChotatsuSu4.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani4"
											value="#{pc_Kab01603.propTani4.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu4" size="6"
														value="#{pc_Kab01603.propChotatsuSu4.stringValue}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu4.style}"
														disabled="#{pc_Kab01603.propChotatsuSu4.disabled}"
														rendered="#{pc_Kab01603.propChotatsuSu4.rendered}"
														maxlength="#{pc_Kab01603.propChotatsuSu4.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu4.readonly}">
														<hx:inputHelperAssist errorClass="inputText_Error" />
													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani4"
														disabled="#{pc_Kab01603.propTani4.disabled}"
														readonly="#{pc_Kab01603.propTani4.readonly}"
														rendered="#{pc_Kab01603.propTani4.rendered}"
														style="#{pc_Kab01603.propTani4.style}"
														value="#{pc_Kab01603.propTani4.value}">
														<f:selectItems value="#{pc_Kab01603.propTani4.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku4"
											value="#{pc_Kab01603.propShutokuKagaku4.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku4.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku4" size="17"
											value="#{pc_Kab01603.propShutokuKagaku4.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku4.style}"
											disabled="#{pc_Kab01603.propShutokuKagaku4.disabled}"
											maxlength="#{pc_Kab01603.propShutokuKagaku4.maxLength}"
											readonly="#{pc_Kab01603.propShutokuKagaku4.readonly}"
											rendered="#{pc_Kab01603.propShutokuKagaku4.rendered}"
											onblur="return getTyoboKagaku4(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku4"
											value="#{pc_Kab01603.propTyoboKagaku4.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku4"
											value="#{pc_Kab01603.propTyoboKagaku4.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku4"
											value="#{pc_Kab01603.propShokyakuGaku4.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku4" size="17"
											value="#{pc_Kab01603.propShokyakuGaku4.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuGaku4.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku4.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku4.readonly}"
											rendered="#{pc_Kab01603.propShokyakuGaku4.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku4.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku4"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku4.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku4" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku4.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku4.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku4.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku4.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku4.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku4.style}"
											onblur="return getTyoboKagaku4(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku4"
											value="#{pc_Kab01603.propKihonkinKumiireGaku4.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku4" size="17"
											value="#{pc_Kab01603.propKihonkinKumiireGaku4.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku4.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku4.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku4.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku4.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku4.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku4"
											value="#{pc_Kab01603.propHyokaGaku4.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku4"
											size="17" value="#{pc_Kab01603.propHyokaGaku4.stringValue}"
											disabled="#{pc_Kab01603.propHyokaGaku4.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku4.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku4.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku4.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku4.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">
								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren4"
											action="#{pc_Kab01603.doKanren4Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren4.disabled}"
											rendered="#{pc_Kab01603.propKanren4.rendered}"
											style="#{pc_Kab01603.propKanren4.style}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			<hx:jspPanel id="panel3" rendered="#{pc_Kab01603.propPanel3.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900">分割先５
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo5"
											value="#{pc_Kab01603.propBhnNo5.labelName}"
											style="#{pc_Kab01603.propBhnNo5.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo5" size="20"
											value="#{pc_Kab01603.propBhnNo5.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo5.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo5.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo5.readonly}"
											rendered="#{pc_Kab01603.propBhnNo5.rendered}"
											style="#{pc_Kab01603.propBhnNo5.style}">
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelBhnNo5"
											rendered="#{pc_Kab01603.propLabelBhnNo5.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo5.style}"
											value="#{pc_Kab01603.propLabelBhnNo5.stringValue}"></h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo5"
											value="#{pc_Kab01603.propEdaNo5.labelName}"
											style="#{pc_Kab01603.propEdaNo5.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo5" size="5"
											value="#{pc_Kab01603.propEdaNo5.stringValue}"
											disabled="#{pc_Kab01603.propEdaNo5.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo5.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo5.readonly}"
											rendered="#{pc_Kab01603.propEdaNo5.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo5.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo5"
											rendered="#{pc_Kab01603.propLabelEdaNo5.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo5.style}"
											value="#{pc_Kab01603.propLabelEdaNo5.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName5"
											value="#{pc_Kab01603.propSsnName5.labelName}"
											style="#{pc_Kab01603.propSsnName5.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName5" size="100"
											value="#{pc_Kab01603.propSsnName5.stringValue}"
											style="#{pc_Kab01603.propSsnName5.style}"
											disabled="#{pc_Kab01603.propSsnName5.disabled}"
											maxlength="#{pc_Kab01603.propSsnName5.maxLength}"
											readonly="#{pc_Kab01603.propSsnName5.readonly}"
											rendered="#{pc_Kab01603.propSsnName5.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak5"
											value="#{pc_Kab01603.propSsnNameRyak5.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak5.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak5"
											size="20" value="#{pc_Kab01603.propSsnNameRyak5.stringValue}"
											disabled="#{pc_Kab01603.propSsnNameRyak5.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak5.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak5.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak5.rendered}"
											style="#{pc_Kab01603.propSsnNameRyak5.style}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu5"
											value="#{pc_Kab01603.propChotatsuSu5.labelName}"
											style="#{pc_Kab01603.propChotatsuSu5.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani5"
											value="#{pc_Kab01603.propTani5.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu5" size="6"
														value="#{pc_Kab01603.propChotatsuSu5.stringValue}"
														disabled="#{pc_Kab01603.propChotatsuSu5.disabled}"
														maxlength="#{pc_Kab01603.propChotatsuSu5.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu5.readonly}"
														rendered="#{pc_Kab01603.propChotatsuSu5.rendered}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu5.style}">
														<hx:inputHelperAssist errorClass="inputText_Error" />
													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani5"
														disabled="#{pc_Kab01603.propTani5.disabled}"
														readonly="#{pc_Kab01603.propTani5.readonly}"
														rendered="#{pc_Kab01603.propTani5.rendered}"
														style="#{pc_Kab01603.propTani5.style}"
														value="#{pc_Kab01603.propTani5.value}">
														<f:selectItems value="#{pc_Kab01603.propTani5.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku5"
											value="#{pc_Kab01603.propShutokuKagaku5.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku5.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku5" size="17"
											value="#{pc_Kab01603.propShutokuKagaku5.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku5.style}"
											disabled="#{pc_Kab01603.propShutokuKagaku5.disabled}"
											maxlength="#{pc_Kab01603.propShutokuKagaku5.maxLength}"
											readonly="#{pc_Kab01603.propShutokuKagaku5.readonly}"
											rendered="#{pc_Kab01603.propShutokuKagaku5.rendered}"
											onblur="return getTyoboKagaku5(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku5"
											value="#{pc_Kab01603.propTyoboKagaku5.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku5"
											value="#{pc_Kab01603.propTyoboKagaku5.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku5"
											value="#{pc_Kab01603.propShokyakuGaku5.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku5" size="17"
											value="#{pc_Kab01603.propShokyakuGaku5.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuGaku5.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku5.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku5.readonly}"
											rendered="#{pc_Kab01603.propShokyakuGaku5.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku5.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku5"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku5.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku5" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku5.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku5.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku5.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku5.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku5.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku5.style}"
											onblur="return getTyoboKagaku5(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku5"
											value="#{pc_Kab01603.propKihonkinKumiireGaku5.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku5" size="17"
											value="#{pc_Kab01603.propKihonkinKumiireGaku5.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku5.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku5.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku5.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku5.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku5.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku5"
											value="#{pc_Kab01603.propHyokaGaku5.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku5"
											size="17" value="#{pc_Kab01603.propHyokaGaku5.stringValue}"
											disabled="#{pc_Kab01603.propHyokaGaku5.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku5.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku5.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku5.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku5.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">
								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren5"
											action="#{pc_Kab01603.doKanren5Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren5.disabled}"
											rendered="#{pc_Kab01603.propKanren5.rendered}"
											style="#{pc_Kab01603.propKanren5.style}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			<hx:jspPanel id="panel4" rendered="#{pc_Kab01603.propPanel4.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900">分割先６
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo6"
											value="#{pc_Kab01603.propBhnNo6.labelName}"
											style="#{pc_Kab01603.propBhnNo6.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo6" size="20"
											value="#{pc_Kab01603.propBhnNo6.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo6.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo6.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo6.readonly}"
											rendered="#{pc_Kab01603.propBhnNo6.rendered}"
											style="#{pc_Kab01603.propBhnNo6.style}">
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelBhnNo6"
											rendered="#{pc_Kab01603.propLabelBhnNo6.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo6.style}"
											value="#{pc_Kab01603.propLabelBhnNo6.stringValue}"></h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo6"
											value="#{pc_Kab01603.propEdaNo6.labelName}"
											style="#{pc_Kab01603.propEdaNo6.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo6" size="5"
											value="#{pc_Kab01603.propEdaNo6.stringValue}"
											disabled="#{pc_Kab01603.propEdaNo6.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo6.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo6.readonly}"
											rendered="#{pc_Kab01603.propEdaNo6.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo6.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo6"
											rendered="#{pc_Kab01603.propLabelEdaNo6.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo6.style}"
											value="#{pc_Kab01603.propLabelEdaNo6.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName6"
											value="#{pc_Kab01603.propSsnName6.labelName}"
											style="#{pc_Kab01603.propSsnName6.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName6" size="100"
											value="#{pc_Kab01603.propSsnName6.stringValue}"
											style="#{pc_Kab01603.propSsnName6.style}"
											disabled="#{pc_Kab01603.propSsnName6.disabled}"
											maxlength="#{pc_Kab01603.propSsnName6.maxLength}"
											readonly="#{pc_Kab01603.propSsnName6.readonly}"
											rendered="#{pc_Kab01603.propSsnName6.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak6"
											value="#{pc_Kab01603.propSsnNameRyak6.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak6.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak6"
											size="20" value="#{pc_Kab01603.propSsnNameRyak6.stringValue}"
											disabled="#{pc_Kab01603.propSsnNameRyak6.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak6.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak6.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak6.rendered}"
											style="#{pc_Kab01603.propSsnNameRyak6.style}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu6"
											value="#{pc_Kab01603.propChotatsuSu6.labelName}"
											style="#{pc_Kab01603.propChotatsuSu6.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani6"
											value="#{pc_Kab01603.propTani6.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu6" size="6"
														value="#{pc_Kab01603.propChotatsuSu6.stringValue}"
														maxlength="#{pc_Kab01603.propChotatsuSu6.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu6.readonly}"
														rendered="#{pc_Kab01603.propChotatsuSu6.rendered}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu6.style}"
														disabled="#{pc_Kab01603.propChotatsuSu6.disabled}">
														<hx:inputHelperAssist errorClass="inputText_Error" />
													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani6"
														disabled="#{pc_Kab01603.propTani6.disabled}"
														readonly="#{pc_Kab01603.propTani6.readonly}"
														rendered="#{pc_Kab01603.propTani6.rendered}"
														style="#{pc_Kab01603.propTani6.style}"
														value="#{pc_Kab01603.propTani6.value}">
														<f:selectItems value="#{pc_Kab01603.propTani6.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku6"
											value="#{pc_Kab01603.propShutokuKagaku6.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku6.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku6" size="17"
											value="#{pc_Kab01603.propShutokuKagaku6.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku6.style}"
											disabled="#{pc_Kab01603.propShutokuKagaku6.disabled}"
											maxlength="#{pc_Kab01603.propShutokuKagaku6.maxLength}"
											readonly="#{pc_Kab01603.propShutokuKagaku6.readonly}"
											rendered="#{pc_Kab01603.propShutokuKagaku6.rendered}"
											onblur="return getTyoboKagaku6(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku6"
											value="#{pc_Kab01603.propTyoboKagaku6.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku6"
											value="#{pc_Kab01603.propTyoboKagaku6.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku6"
											value="#{pc_Kab01603.propShokyakuGaku6.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku6" size="17"
											value="#{pc_Kab01603.propShokyakuGaku6.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuGaku6.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku6.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku6.readonly}"
											rendered="#{pc_Kab01603.propShokyakuGaku6.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku6.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku6"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku6.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku6" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku6.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku6.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku6.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku6.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku6.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku6.style}"
											onblur="return getTyoboKagaku6(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku6"
											value="#{pc_Kab01603.propKihonkinKumiireGaku6.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku6" size="17"
											value="#{pc_Kab01603.propKihonkinKumiireGaku6.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku6.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku6.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku6.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku6.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku6.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku6"
											value="#{pc_Kab01603.propHyokaGaku6.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku6"
											size="17" value="#{pc_Kab01603.propHyokaGaku6.stringValue}"
											disabled="#{pc_Kab01603.propHyokaGaku6.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku6.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku6.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku6.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku6.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">
								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren6"
											action="#{pc_Kab01603.doKanren6Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren6.disabled}"
											rendered="#{pc_Kab01603.propKanren6.rendered}"
											style="#{pc_Kab01603.propKanren6.style}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			<hx:jspPanel id="panel5" rendered="#{pc_Kab01603.propPanel5.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900" class="">分割先７
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo7"
											value="#{pc_Kab01603.propBhnNo7.labelName}"
											style="#{pc_Kab01603.propBhnNo7.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo7" size="20"
											value="#{pc_Kab01603.propBhnNo7.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo7.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo7.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo7.readonly}"
											rendered="#{pc_Kab01603.propBhnNo7.rendered}"
											style="#{pc_Kab01603.propBhnNo7.style}">
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelBhnNo7"
											rendered="#{pc_Kab01603.propLabelBhnNo7.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo7.style}"
											value="#{pc_Kab01603.propLabelBhnNo7.stringValue}"></h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo7"
											value="#{pc_Kab01603.propEdaNo7.labelName}"
											style="#{pc_Kab01603.propEdaNo7.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo7" size="5"
											value="#{pc_Kab01603.propEdaNo7.stringValue}"
											disabled="#{pc_Kab01603.propEdaNo7.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo7.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo7.readonly}"
											rendered="#{pc_Kab01603.propEdaNo7.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo7.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo7"
											rendered="#{pc_Kab01603.propLabelEdaNo7.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo7.style}"
											value="#{pc_Kab01603.propLabelEdaNo7.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName7"
											value="#{pc_Kab01603.propSsnName7.labelName}"
											style="#{pc_Kab01603.propSsnName7.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName7" size="100"
											value="#{pc_Kab01603.propSsnName7.stringValue}"
											style="#{pc_Kab01603.propSsnName7.style}"
											disabled="#{pc_Kab01603.propSsnName7.disabled}"
											maxlength="#{pc_Kab01603.propSsnName7.maxLength}"
											readonly="#{pc_Kab01603.propSsnName7.readonly}"
											rendered="#{pc_Kab01603.propSsnName7.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak7"
											value="#{pc_Kab01603.propSsnNameRyak7.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak7.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak7"
											size="20" value="#{pc_Kab01603.propSsnNameRyak7.stringValue}"
											disabled="#{pc_Kab01603.propSsnNameRyak7.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak7.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak7.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak7.rendered}"
											style="#{pc_Kab01603.propSsnNameRyak7.style}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu7"
											value="#{pc_Kab01603.propChotatsuSu7.labelName}"
											style="#{pc_Kab01603.propChotatsuSu7.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani7"
											value="#{pc_Kab01603.propTani7.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu7" size="6"
														value="#{pc_Kab01603.propChotatsuSu7.stringValue}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu7.style}"
														disabled="#{pc_Kab01603.propChotatsuSu7.disabled}"
														maxlength="#{pc_Kab01603.propChotatsuSu7.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu7.readonly}"
														rendered="#{pc_Kab01603.propChotatsuSu7.rendered}">
														<hx:inputHelperAssist errorClass="inputText_Error" />
													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani7"
														disabled="#{pc_Kab01603.propTani7.disabled}"
														readonly="#{pc_Kab01603.propTani7.readonly}"
														rendered="#{pc_Kab01603.propTani7.rendered}"
														style="#{pc_Kab01603.propTani7.style}"
														value="#{pc_Kab01603.propTani7.value}">
														<f:selectItems value="#{pc_Kab01603.propTani7.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku7"
											value="#{pc_Kab01603.propShutokuKagaku7.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku7.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku7" size="17"
											value="#{pc_Kab01603.propShutokuKagaku7.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku7.style}"
											disabled="#{pc_Kab01603.propShutokuKagaku7.disabled}"
											maxlength="#{pc_Kab01603.propShutokuKagaku7.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku7.readonly}"
											rendered="#{pc_Kab01603.propShutokuKagaku7.rendered}"
											onblur="return getTyoboKagaku7(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku7"
											value="#{pc_Kab01603.propTyoboKagaku7.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku7"
											value="#{pc_Kab01603.propTyoboKagaku7.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku7"
											value="#{pc_Kab01603.propShokyakuGaku7.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku7" size="17"
											value="#{pc_Kab01603.propShokyakuGaku7.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuGaku7.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku7.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku7.readonly}"
											rendered="#{pc_Kab01603.propShokyakuGaku7.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku7.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku7"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku7.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku7" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku7.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku7.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku7.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku7.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku7.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku7.style}"
											onblur="return getTyoboKagaku7(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku7"
											value="#{pc_Kab01603.propKihonkinKumiireGaku7.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku7" size="17"
											value="#{pc_Kab01603.propKihonkinKumiireGaku7.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku7.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku7.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku7.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku7.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku7.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku7"
											value="#{pc_Kab01603.propHyokaGaku7.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku7"
											size="17" value="#{pc_Kab01603.propHyokaGaku7.stringValue}"
											disabled="#{pc_Kab01603.propHyokaGaku7.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku7.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku7.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku7.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku7.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">
								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren7"
											action="#{pc_Kab01603.doKanren7Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren7.disabled}"
											rendered="#{pc_Kab01603.propKanren7.rendered}"
											style="#{pc_Kab01603.propKanren7.style}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			<hx:jspPanel id="panel6" rendered="#{pc_Kab01603.propPanel6.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900">分割先８
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo8"
											value="#{pc_Kab01603.propBhnNo8.labelName}"
											style="#{pc_Kab01603.propBhnNo8.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo8" size="20"
											value="#{pc_Kab01603.propBhnNo8.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo8.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo8.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo8.readonly}"
											rendered="#{pc_Kab01603.propBhnNo8.rendered}"
											style="#{pc_Kab01603.propBhnNo8.style}">
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelBhnNo8"
											rendered="#{pc_Kab01603.propLabelBhnNo8.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo8.style}"
											value="#{pc_Kab01603.propLabelBhnNo8.stringValue}"></h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo8"
											value="#{pc_Kab01603.propEdaNo8.labelName}"
											style="#{pc_Kab01603.propEdaNo8.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo8" size="5"
											value="#{pc_Kab01603.propEdaNo8.stringValue}"
											disabled="#{pc_Kab01603.propEdaNo8.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo8.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo8.readonly}"
											rendered="#{pc_Kab01603.propEdaNo8.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo8.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo8"
											rendered="#{pc_Kab01603.propLabelEdaNo8.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo8.style}"
											value="#{pc_Kab01603.propLabelEdaNo8.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName8"
											value="#{pc_Kab01603.propSsnName8.labelName}"
											style="#{pc_Kab01603.propSsnName8.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName8" size="100"
											value="#{pc_Kab01603.propSsnName8.stringValue}"
											style="#{pc_Kab01603.propSsnName8.style}"
											disabled="#{pc_Kab01603.propSsnName8.disabled}"
											maxlength="#{pc_Kab01603.propSsnName8.maxLength}"
											readonly="#{pc_Kab01603.propSsnName8.readonly}"
											rendered="#{pc_Kab01603.propSsnName8.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak8"
											value="#{pc_Kab01603.propSsnNameRyak8.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak8.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak8"
											size="20" value="#{pc_Kab01603.propSsnNameRyak8.stringValue}"
											disabled="#{pc_Kab01603.propSsnNameRyak8.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak8.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak8.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak8.rendered}"
											style="#{pc_Kab01603.propSsnNameRyak8.style}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu8"
											value="#{pc_Kab01603.propChotatsuSu8.labelName}"
											style="#{pc_Kab01603.propChotatsuSu8.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani8"
											value="#{pc_Kab01603.propTani8.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu8" size="6"
														value="#{pc_Kab01603.propChotatsuSu8.stringValue}"
														disabled="#{pc_Kab01603.propChotatsuSu8.disabled}"
														maxlength="#{pc_Kab01603.propChotatsuSu8.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu8.readonly}"
														rendered="#{pc_Kab01603.propChotatsuSu8.rendered}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu8.style}">
														<hx:inputHelperAssist errorClass="inputText_Error" />
													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani8"
														disabled="#{pc_Kab01603.propTani8.disabled}"
														readonly="#{pc_Kab01603.propTani8.readonly}"
														rendered="#{pc_Kab01603.propTani8.rendered}"
														style="#{pc_Kab01603.propTani8.style}"
														value="#{pc_Kab01603.propTani8.value}">
														<f:selectItems value="#{pc_Kab01603.propTani8.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku8"
											value="#{pc_Kab01603.propShutokuKagaku8.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku8.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku8" size="17"
											value="#{pc_Kab01603.propShutokuKagaku8.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku8.style}"
											disabled="#{pc_Kab01603.propShutokuKagaku8.disabled}"
											maxlength="#{pc_Kab01603.propShutokuKagaku8.maxLength}"
											readonly="#{pc_Kab01603.propShutokuKagaku8.readonly}"
											rendered="#{pc_Kab01603.propShutokuKagaku8.rendered}"
											onblur="return getTyoboKagaku8(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku8"
											value="#{pc_Kab01603.propTyoboKagaku8.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku8"
											value="#{pc_Kab01603.propTyoboKagaku8.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku8"
											value="#{pc_Kab01603.propShokyakuGaku8.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku8" size="17"
											value="#{pc_Kab01603.propShokyakuGaku8.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuGaku8.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku8.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku8.readonly}"
											rendered="#{pc_Kab01603.propShokyakuGaku8.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku8.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku8"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku8.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku8" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku8.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku8.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku8.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku8.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku8.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku8.style}"
											onblur="return getTyoboKagaku8(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku8"
											value="#{pc_Kab01603.propKihonkinKumiireGaku8.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku8" size="17"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku8.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku8.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku8.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku8.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku8.rendered}"
											value="#{pc_Kab01603.propKihonkinKumiireGaku8.stringValue}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku8"
											value="#{pc_Kab01603.propHyokaGaku8.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku8"
											size="17" value="#{pc_Kab01603.propHyokaGaku8.stringValue}"
											disabled="#{pc_Kab01603.propHyokaGaku8.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku8.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku8.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku8.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku8.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">
								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren8"
											action="#{pc_Kab01603.doKanren8Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren8.disabled}"
											rendered="#{pc_Kab01603.propKanren8.rendered}"
											style="#{pc_Kab01603.propKanren8.style}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			<hx:jspPanel id="panel7"rendered="#{pc_Kab01603.propPanel7.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900">分割先９
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo9"
											value="#{pc_Kab01603.propBhnNo9.labelName}"
											style="#{pc_Kab01603.propBhnNo9.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo9" size="20"
											value="#{pc_Kab01603.propBhnNo9.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo9.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo9.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo9.readonly}"
											rendered="#{pc_Kab01603.propBhnNo9.rendered}"
											style="#{pc_Kab01603.propBhnNo9.style}">
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelBhnNo9"
											rendered="#{pc_Kab01603.propLabelBhnNo9.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo9.style}"
											value="#{pc_Kab01603.propLabelBhnNo9.stringValue}"></h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo9"
											value="#{pc_Kab01603.propEdaNo9.labelName}"
											style="#{pc_Kab01603.propEdaNo9.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo9" size="5"
											value="#{pc_Kab01603.propEdaNo9.stringValue}"
											disabled="#{pc_Kab01603.propEdaNo9.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo9.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo9.readonly}"
											rendered="#{pc_Kab01603.propEdaNo9.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo9.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo9"
											rendered="#{pc_Kab01603.propLabelEdaNo9.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo9.style}"
											value="#{pc_Kab01603.propLabelEdaNo9.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName9"
											value="#{pc_Kab01603.propSsnName9.labelName}"
											style="#{pc_Kab01603.propSsnName9.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName9" size="100"
											value="#{pc_Kab01603.propSsnName9.stringValue}"
											style="#{pc_Kab01603.propSsnName9.style}"
											disabled="#{pc_Kab01603.propSsnName9.disabled}"
											maxlength="#{pc_Kab01603.propSsnName9.maxLength}"
											readonly="#{pc_Kab01603.propSsnName9.readonly}"
											rendered="#{pc_Kab01603.propSsnName9.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak9"
											value="#{pc_Kab01603.propSsnNameRyak9.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak9.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak9"
											size="20" value="#{pc_Kab01603.propSsnNameRyak9.stringValue}"
											disabled="#{pc_Kab01603.propSsnNameRyak9.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak9.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak9.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak9.rendered}"
											style="#{pc_Kab01603.propSsnNameRyak9.style}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu9"
											value="#{pc_Kab01603.propChotatsuSu9.labelName}"
											style="#{pc_Kab01603.propChotatsuSu9.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani9"
											value="#{pc_Kab01603.propTani9.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu9" size="6"
														value="#{pc_Kab01603.propChotatsuSu9.stringValue}"
														disabled="#{pc_Kab01603.propChotatsuSu9.disabled}"
														maxlength="#{pc_Kab01603.propChotatsuSu9.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu9.readonly}"
														rendered="#{pc_Kab01603.propChotatsuSu9.rendered}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu9.style}">
														<hx:inputHelperAssist errorClass="inputText_Error" />
													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani9"
														disabled="#{pc_Kab01603.propTani9.disabled}"
														readonly="#{pc_Kab01603.propTani9.readonly}"
														rendered="#{pc_Kab01603.propTani9.rendered}"
														style="#{pc_Kab01603.propTani9.style}"
														value="#{pc_Kab01603.propTani9.value}">
														<f:selectItems value="#{pc_Kab01603.propTani9.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku9"
											value="#{pc_Kab01603.propShutokuKagaku9.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku9.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku9" size="17"
											value="#{pc_Kab01603.propShutokuKagaku9.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku9.style}"
											rendered="#{pc_Kab01603.propShutokuKagaku9.rendered}"
											disabled="#{pc_Kab01603.propShutokuKagaku9.disabled}"
											maxlength="#{pc_Kab01603.propShutokuKagaku9.maxLength}"
											readonly="#{pc_Kab01603.propShutokuKagaku9.readonly}"
											onblur="return getTyoboKagaku9(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku9"
											value="#{pc_Kab01603.propTyoboKagaku9.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku9"
											value="#{pc_Kab01603.propTyoboKagaku9.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku9"
											value="#{pc_Kab01603.propShokyakuGaku9.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku9" size="17"
											value="#{pc_Kab01603.propShokyakuGaku9.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuGaku9.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku9.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku9.readonly}"
											rendered="#{pc_Kab01603.propShokyakuGaku9.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuGaku9.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku9"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku9.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku9" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku9.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku9.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku9.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku9.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku9.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku9.style}"
											onblur="return getTyoboKagaku9(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku9"
											value="#{pc_Kab01603.propKihonkinKumiireGaku9.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku9" size="17"
											value="#{pc_Kab01603.propKihonkinKumiireGaku9.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku9.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku9.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku9.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku9.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku9.rendered}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku9"
											value="#{pc_Kab01603.propHyokaGaku9.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku9"
											size="17" value="#{pc_Kab01603.propHyokaGaku9.stringValue}"
											disabled="#{pc_Kab01603.propHyokaGaku9.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku9.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku9.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku9.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku9.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">

								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren9"
											action="#{pc_Kab01603.doKanren9Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren9.disabled}"
											rendered="#{pc_Kab01603.propKanren9.rendered}"
											style="#{pc_Kab01603.propKanren9.style}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			<hx:jspPanel id="panel8"rendered="#{pc_Kab01603.propPanel8.rendered}">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table_outer_frame"
					style="margin-top: 10px;">
					<TBODY>
						<TR>
							<TD width="900">分割先１０
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
								width="850" style="margin-left: 10px;">
								<TBODY>
									<TR>
										<TH width="171" class="v_a"><h:outputText
											styleClass="outputText" id="lblBhnNo10"
											value="#{pc_Kab01603.propBhnNo10.labelName}"
											style="#{pc_Kab01603.propBhnNo10.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlBhnNo10" size="20"
											value="#{pc_Kab01603.propBhnNo10.stringValue}"
											disabled="#{pc_Kab01603.propBhnNo10.disabled}"
											maxlength="#{pc_Kab01603.propBhnNo10.maxLength}"
											readonly="#{pc_Kab01603.propBhnNo10.readonly}"
											rendered="#{pc_Kab01603.propBhnNo10.rendered}"
											style="#{pc_Kab01603.propBhnNo10.style}">
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelBhnNo10"
											rendered="#{pc_Kab01603.propLabelBhnNo10.rendered}"
											style="#{pc_Kab01603.propLabelBhnNo10.style}"
											value="#{pc_Kab01603.propLabelBhnNo10.stringValue}"></h:outputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblEdaNo10"
											value="#{pc_Kab01603.propEdaNo10.labelName}"
											style="#{pc_Kab01603.propEdaNo10.labelStyle}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlEdaNo10" size="5"
											value="#{pc_Kab01603.propEdaNo10.stringValue}"
											disabled="#{pc_Kab01603.propEdaNo10.disabled}"
											maxlength="#{pc_Kab01603.propEdaNo10.maxLength}"
											readonly="#{pc_Kab01603.propEdaNo10.readonly}"
											rendered="#{pc_Kab01603.propEdaNo10.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propEdaNo10.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText><h:outputText styleClass="outputText"
											id="htmlLabelEdaNo10"
											rendered="#{pc_Kab01603.propLabelEdaNo10.rendered}"
											style="#{pc_Kab01603.propLabelEdaNo10.style}"
											value="#{pc_Kab01603.propLabelEdaNo10.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText styleClass="outputText"
											id="lblSsnName10"
											value="#{pc_Kab01603.propSsnName10.labelName}"
											style="#{pc_Kab01603.propSsnName10.labelStyle}"></h:outputText></TH>
										<TD colspan="3"><h:inputText styleClass="inputText"
											id="htmlSsnName10" size="100"
											value="#{pc_Kab01603.propSsnName10.stringValue}"
											style="#{pc_Kab01603.propSsnName10.style}"
											disabled="#{pc_Kab01603.propSsnName10.disabled}"
											maxlength="#{pc_Kab01603.propSsnName10.maxLength}"
											readonly="#{pc_Kab01603.propSsnName10.readonly}"
											rendered="#{pc_Kab01603.propSsnName10.rendered}"></h:inputText></TD>
									</TR>
									<TR>
										<TH class="v_d"><h:outputText styleClass="outputText"
											id="lblSsnRyak10"
											value="#{pc_Kab01603.propSsnNameRyak10.labelName}"
											style="#{pc_Kab01603.propSsnNameRyak10.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSsnRyak10"
											size="20"
											value="#{pc_Kab01603.propSsnNameRyak10.stringValue}"
											disabled="#{pc_Kab01603.propSsnNameRyak10.disabled}"
											maxlength="#{pc_Kab01603.propSsnNameRyak10.maxLength}"
											readonly="#{pc_Kab01603.propSsnNameRyak10.readonly}"
											rendered="#{pc_Kab01603.propSsnNameRyak10.rendered}"
											style="#{pc_Kab01603.propSsnNameRyak10.style}"></h:inputText></TD>
										<TH width="151" class="v_e"><h:outputText
											styleClass="outputText" id="lblChotatuSu10"
											value="#{pc_Kab01603.propChotatsuSu10.labelName}"
											style="#{pc_Kab01603.propChotatsuSu10.labelStyle}"></h:outputText>／<h:outputText
											styleClass="outputText" id="lblTani10"
											value="#{pc_Kab01603.propTani10.labelName}"></h:outputText></TH>
										<TD width="305">
										<TABLE border="1" class="clear_border">
											<TBODY>
												<TR>
													<TD><h:inputText styleClass="inputText"
														id="htmlChotatsuSu10" size="6"
														value="#{pc_Kab01603.propChotatsuSu10.stringValue}"
														disabled="#{pc_Kab01603.propChotatsuSu10.disabled}"
														maxlength="#{pc_Kab01603.propChotatsuSu10.maxLength}"
														readonly="#{pc_Kab01603.propChotatsuSu10.readonly}"
														rendered="#{pc_Kab01603.propChotatsuSu10.required}"
														style="padding-right: 3px; text-align: right;#{pc_Kab01603.propChotatsuSu10.style}">
														<hx:inputHelperAssist errorClass="inputText_Error" />

													</h:inputText></TD>
													<TD><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTani10"
														disabled="#{pc_Kab01603.propTani10.disabled}"
														readonly="#{pc_Kab01603.propTani10.readonly}"
														rendered="#{pc_Kab01603.propTani10.rendered}"
														style="#{pc_Kab01603.propTani10.style}"
														value="#{pc_Kab01603.propTani10.value}">
														<f:selectItems value="#{pc_Kab01603.propTani10.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<TR>
										<TH class="v_f"><h:outputText styleClass="outputText"
											id="lblShutokuKagaku10"
											value="#{pc_Kab01603.propShutokuKagaku10.labelName}"
											style="#{pc_Kab01603.propShutokuKagaku10.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText"
											id="htmlShutokuKagaku10" size="17"
											value="#{pc_Kab01603.propShutokuKagaku10.stringValue}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShutokuKagaku10.style}"
											rendered="#{pc_Kab01603.propShutokuKagaku10.rendered}"
											maxlength="#{pc_Kab01603.propShutokuKagaku10.maxLength}"
											readonly="#{pc_Kab01603.propShutokuKagaku10.readonly}"
											disabled="#{pc_Kab01603.propChotatsuSu10.disabled}"
											onblur="return getTyoboKagaku10(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_b"><h:outputText
											styleClass="outputText" id="lblTyoboKagaku10"
											value="#{pc_Kab01603.propTyoboKagaku10.labelName}"></h:outputText></TH>
										<TD width="305"><h:outputText styleClass="outputText"
											id="htmlTyoboKagaku10"
											value="#{pc_Kab01603.propTyoboKagaku10.longValue}">
											<f:convertNumber pattern="#,###,###,###,##0" />
										</h:outputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_g"><h:outputText
											styleClass="outputText" id="lblShokyakuGaku10"
											value="#{pc_Kab01603.propShokyakuGaku10.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlShokyakuGaku10" size="17"
											value="#{pc_Kab01603.propShokyakuGaku10.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuGaku10.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuGaku10.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuGaku10.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku10.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku10.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_a"><h:outputText styleClass="outputText"
											id="lblShokyakuRuikeiGaku10"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku10.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText"
											id="htmlShokyakuRuikeiGaku10" size="17"
											value="#{pc_Kab01603.propShokyakuRuikeiGaku10.stringValue}"
											disabled="#{pc_Kab01603.propShokyakuRuikeiGaku10.disabled}"
											maxlength="#{pc_Kab01603.propShokyakuRuikeiGaku10.maxLength}"
											readonly="#{pc_Kab01603.propShokyakuRuikeiGaku10.readonly}"
											rendered="#{pc_Kab01603.propShokyakuRuikeiGaku10.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propShokyakuRuikeiGaku10.style}"
											onblur="return getTyoboKagaku10(this, event);">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
									<TR>
										<TH width="171" class="v_d"><h:outputText
											styleClass="outputText" id="lblKihonkinKumiireGaku10"
											value="#{pc_Kab01603.propKihonkinKumiireGaku10.labelName}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKihonkinKumiireGaku10" size="17"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propKihonkinKumiireGaku10.style}"
											disabled="#{pc_Kab01603.propKihonkinKumiireGaku10.disabled}"
											maxlength="#{pc_Kab01603.propKihonkinKumiireGaku10.maxLength}"
											readonly="#{pc_Kab01603.propKihonkinKumiireGaku10.readonly}"
											rendered="#{pc_Kab01603.propKihonkinKumiireGaku10.rendered}"
											value="#{pc_Kab01603.propKihonkinKumiireGaku10.stringValue}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
										<TH width="151" class="v_c"><h:outputText styleClass="outputText"
											id="lblHyokaGaku10"
											value="#{pc_Kab01603.propHyokaGaku10.labelName}"></h:outputText></TH>
										<TD width="305"><h:inputText styleClass="inputText" id="htmlHyokaGaku10"
											size="17" value="#{pc_Kab01603.propHyokaGaku10.stringValue}"
											disabled="#{pc_Kab01603.propHyokaGaku10.disabled}"
											maxlength="#{pc_Kab01603.propHyokaGaku10.maxLength}"
											readonly="#{pc_Kab01603.propHyokaGaku10.readonly}"
											rendered="#{pc_Kab01603.propHyokaGaku10.rendered}"
											style="padding-right: 3px; text-align: right;#{pc_Kab01603.propHyokaGaku10.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
								style="margin-top:0px;margin-left: 10px;" class="button_bar">

								<TBODY>
									<TR>
										<TD style="text-align:right; border-style:none"><hx:commandExButton type="submit"
											value="関連情報" styleClass="commandExButton_etc" id="kanren10"
											action="#{pc_Kab01603.doKanren10Action}"
											onclick="return func_1(this, event);"
											disabled="#{pc_Kab01603.propKanren10.disabled}"
											rendered="#{pc_Kab01603.propKanren10.rendered}">
										</hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>	
			
			</DIV>
			
			</DIV>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="900"
				style="margin-top: 20px;" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="計算"
							styleClass="commandExButton_etc" id="calculation"
							action="#{pc_Kab01603.doCalculationAction}"
							disabled="#{pc_Kab01603.propCalculation.disabled}"
							rendered="#{pc_Kab01603.propCalculation.rendered}"
							style="#{pc_Kab01603.propCalculation.style}">
						</hx:commandExButton>
							<hx:commandExButton 
								type="submit"
								value="合計値チェック" 
								styleClass="commandExButton_large1"
								id="checktotal"
								action="#{pc_Kab01603.doChecktotalAction}"
								disabled="#{pc_Kab01603.propCheckTotal.disabled}"
								rendered="#{pc_Kab01603.propCheckTotal.rendered}"
								style="width: 100px;">
							</hx:commandExButton>
							<hx:commandExButton
								type="submit" value="登録" styleClass="commandExButton_dat"
								id="register" action="#{pc_Kab01603.doRegisterAction}"
								disabled="#{pc_Kab01603.propRegister.disabled}"
								style="#{pc_Kab01603.propRegister.style}"
								rendered="#{pc_Kab01603.propRegister.rendered}" confirm="#{msg.SY_MSG_0002W}">
							</hx:commandExButton>
							<hx:commandExButton
								type="submit" value="クリア" styleClass="commandExButton_etc"
								id="clear" action="#{pc_Kab01603.doClearAction}"
								disabled="#{pc_Kab01603.propClear.disabled}"
								style="#{pc_Kab01603.propClear.style}"
								rendered="#{pc_Kab01603.propClear.rendered}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				id="htmlRegisterStatus"
				value="#{pc_Kab01603.propRegisterStatus.stringValue}">
			</h:inputHidden>
			<h:inputHidden
				id="htmlKintouBunkatsu"
				value="#{pc_Kab01603.propKintouBunkatsu.stringValue}">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kab01603.propFormatNumberOption.stringValue}"
				id="htmlFormatNumberOption">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab01603.propHiddenScrollPos.integerValue}" id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

