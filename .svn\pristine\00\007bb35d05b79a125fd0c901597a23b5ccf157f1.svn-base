<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cog05501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>科目按分パターン登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<style type="text/css">
<!--
.setWidth TD {width: 62px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";	
	indirectClick('search');	
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";	
}

function getKamok(thisObj, thisEvent) {																			
	//執行科目名称を取得する																			
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	args['code2'] = thisObj.value;
	args['code3'] = '0';
	var target = "form1:htmlChoseiKmkame";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function func_1(thisObj, thisEvent) {
	openModalWindow("", "PCog0301", "<%=com.jast.gakuen.rev.co.PCog0301.getWindowOpenOption() %>");
	setTarget("PCog0301");
	return true;
}



function loadFunc() {
	getKamok(document.getElementById('form1:htmlChoseiKmkCd'), '');
}
function endload(){
	changeScrollPosition('scroll', 'listScroll');
}

window.attachEvent('onload', endload);
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="return loadFunc();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cog05501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cog05501.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cog05501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cog05501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="800" style="margin-top:30px">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblKaikeiNendo"
							value="#{pc_Cog05501.propKaikeiNendo.labelName}"
							style="#{pc_Cog05501.propKaikeiNendo.labelStyle}"></h:outputText></TH>
						<TD width="100"><h:inputText styleClass="inputText"
							id="htmlKaikeiNendo"
							value="#{pc_Cog05501.propKaikeiNendo.dateValue}" size="4"
							style="#{pc_Cog05501.propKaikeiNendo.style}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="" />
						</h:inputText></TD>
						<TH class="v_b" width="150"><h:outputText styleClass="outputText"
							id="lblAvailable" value="有効プロダクト"></h:outputText></TH>
						<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlKeFlgSearch"
							value="#{pc_Cog05501.propKeFlgSearch.checked}"></h:selectBooleanCheckbox>経理&#160;&#160;&#160;&#160;&#160;
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlKaFlgSearch"
							value="#{pc_Cog05501.propKaFlgSearch.checked}">
						</h:selectBooleanCheckbox>管財&#160;&#160;&#160;&#160;&#160; <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlJiFlgSearch"
							value="#{pc_Cog05501.propJiFlgSearch.checked}"></h:selectBooleanCheckbox>人事</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
				width="800" style="margin-top:10px">
				<TBODY>
					<TR>
						<TD width="40%"></TD>
						<TD><hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search"
							action="#{pc_Cog05501.doSearchAction}"></hx:commandExButton></TD>
						<TD><hx:commandExButton type="submit" value="CSV作成"
							styleClass="commandExButton_out" id="csvout"
							confirm="#{msg.SY_MSG_0020W}"
							action="#{pc_Cog05501.doCsvoutAction}"></hx:commandExButton></TD>
						<TD><hx:commandExButton type="submit" value="出力項目指定"
							styleClass="commandExButton_out" id="setoutput"
							action="#{pc_Cog05501.doSetoutputAction}"></hx:commandExButton></TD>
						<TD width="40%"></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" cellpadding="0" cellspacing="0" width="800"
				style="margin-top:10px">
				<TBODY>
					<TR>
						<TD width="100" align="left"><h:outputText styleClass="outputText"
							id="lblNendo" value="#{pc_Cog05501.propNendo.dateValue}">
							<f:convertDateTime pattern="yyyy" />
						</h:outputText><h:outputText
							styleClass="outputText" id="lblNendoMoji" value="年度"></h:outputText></TD>
						<TD width="500" align="left"><h:outputText styleClass="outputText"
							id="lblYuko" value="有効プロダクト："></h:outputText> <h:outputText
							styleClass="outputText" id="htmlYuko" value="#{pc_Cog05501.propYuko.stringValue}"></h:outputText></TD>
						<TD width="200" align="right"><h:outputText
							styleClass="outputText" id="htmlListCount" style="font-size: 8pt"
							value="#{pc_Cog05501.propKmanPatanList.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="lblListCount" value="件"></h:outputText></TD>
					</TR>

					<TR>

						<TD colspan="3">
						<DIV class="listScroll"
							style="height:252px;OVERFLOW:scroll;overflow-x: hidden"
							id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable border="0" cellpadding="2"
							cellspacing="0" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Cog05501.propKmanPatanList.rowClasses}"
							styleClass="meisai_scroll" id="htmlKmanRitsuList" width="780"
							value="#{pc_Cog05501.propKmanPatanList.list}" var="varlist"
							first="#{pc_Cog05501.propKmanPatanList.first}"
							rows="#{pc_Cog05501.propKmanPatanList.rows}"
							style="table-layout: fixed;">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText"
										value="#{pc_Cog05501.propKmkAnbPtn.name}"
										id="lblListKmkAnbPtn"></h:outputText>
								</f:facet>
								<f:attribute value="122" name="width" />
								<f:attribute value="text-align: left; vertical-align: middle"
									name="style" />
								<h:outputText styleClass="outputText" id="htmlListkmkAnbPtn"
									value="#{varlist.kmkAnbPtn}"></h:outputText>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText"
										value="#{pc_Cog05501.propKmkAnbPtnName.name}"
										id="lblListKmkAnbPtnName"></h:outputText>
								</f:facet>
								<f:attribute value="450" name="width" />
								<f:attribute value="text-align: left; vertical-align: middle"
									name="style" />
								<h:outputText styleClass="outputText" id="htmlListKmkAnbPtnName"
									value="#{varlist.kmkAnbPtnName}" 
									style="white-space:nowrap;"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListStrKeYuko"
										value="経理"></h:outputText>
								</f:facet>
								<f:attribute value="40" name="width" />
								<h:outputText styleClass="outputText"
									value="#{varlist.strKeYuko}" id="htmlListStrKeYuko"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListStrKaYuko"
										value="管財"></h:outputText>
								</f:facet>
								<f:attribute value="40" name="width" />
								<h:outputText styleClass="outputText"
									value="#{varlist.strKaYuko}" id="htmlListStrKaYuko"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListStrJiYuko"
										value="人事"></h:outputText>
								</f:facet>
								<f:attribute value="40" name="width" />
								<h:outputText styleClass="outputText"
									value="#{varlist.strJiYuko}" id="htmlListStrJiYuko"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListStrYukoMuko"
										value="状況"></h:outputText>
								</f:facet>
								<f:attribute value="40" name="width" />
								<h:outputText styleClass="outputText"
									value="#{varlist.strYukoMuko}" id="htmlListStrYukoMuko"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="40" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="select" value="選択" action="#{pc_Cog05501.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE><BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="800" style="margin-top:10px">
				<TBODY>
					<TR>
						<TH class="v_a" width="240"><h:outputText styleClass="outputText"
							id="text5" value="#{pc_Cog05501.propKmkAnbPtn.labelName}"
							style="#{pc_Cog05501.propKmkAnbPtn.labelStyle}"></h:outputText></TH>
						<TD width="560"><h:inputText styleClass="inputText" id="htmlKmkAnbPtn"
							size="8" value="#{pc_Cog05501.propKmkAnbPtn.stringValue}"
							style="#{pc_Cog05501.propKmkAnbPtn.style}"
							maxlength="#{pc_Cog05501.propKmkAnbPtn.maxLength}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText styleClass="outputText" id="text6"
							value="#{pc_Cog05501.propKmkAnbPtnName.labelName}"
							style="#{pc_Cog05501.propKmkAnbPtnName.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText" id="htmlKmkAnbPtnName"
							size="80" value="#{pc_Cog05501.propKmkAnbPtnName.stringValue}"
							maxlength="#{pc_Cog05501.propKmkAnbPtnName.maxLength}"
							style="#{pc_Cog05501.propKmkAnbPtnName.style}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_c"><h:outputText styleClass="outputText" id="text7"
							value="#{pc_Cog05501.propChoseiKmkCd.labelName}"
							style="#{pc_Cog05501.propChoseiKmkCd.labelStyle}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:560px;white-space:nowrap;overflow:hidden;display:block;">
								<h:inputText styleClass="inputText" id="htmlChoseiKmkCd"
									size="12" value="#{pc_Cog05501.propChoseiKmkCd.stringValue}"
									maxlength="#{pc_Cog05501.propChoseiKmkCd.maxLength}"
									style="#{pc_Cog05501.propChoseiKmkCd.style}"
									onblur="return getKamok(this, event);"></h:inputText><hx:commandExButton
									type="submit" styleClass="commandExButton_search"
									onclick="return func_1(this, event);" id="searchKmkCd"
									action="#{pc_Cog05501.doSearchKmkCdAction}">
								</hx:commandExButton>
								<h:outputText styleClass="outputText"
									id="htmlChoseiKmkame"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TH class="v_d"><h:outputText styleClass="outputText"
							style="#{pc_Cog05501.propKeYukoFlg.labelStyle}"
							value="#{pc_Cog05501.propKeYukoFlg.labelName}"></h:outputText></TH>
						<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlKeYukoFlg" value="#{pc_Cog05501.propKeYukoFlg.checked}"></h:selectBooleanCheckbox>経理&#160;&#160;&#160;&#160;&#160;
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlKaYukoFlg" value="#{pc_Cog05501.propKaYukoFlg.checked}">
						</h:selectBooleanCheckbox>管財&#160;&#160;&#160;&#160;&#160; <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlJiYukoFlg"
							value="#{pc_Cog05501.propJiYukoFlg.checked}"></h:selectBooleanCheckbox>人事</TD>
					</TR>
					<TR>
						<TH class="v_e"><h:outputText styleClass="outputText" id="text9"
							value="#{pc_Cog05501.propYukoMuko.labelName}"
							style="#{pc_Cog05501.propYukoMuko.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio setWidth" id="radio2"
							value="#{pc_Cog05501.propYukoMuko.value}">
							<f:selectItem itemValue="1" itemLabel="有効" />
							<f:selectItem itemValue="0" itemLabel="無効" />
						</h:selectOneRadio></TD>
					</TR>
				</TBODY>
			</TABLE><BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" width="800"
				class="button_bar" style="margin-top:10px">
				<TBODY>
					<TR>
						<TD width="35%"></TD>
						<TD><hx:commandExButton type="submit" value="登録"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0002W}"
							action="#{pc_Cog05501.doRegisterAction}"></hx:commandExButton></TD>
						<TD><hx:commandExButton type="submit" value="更新"
							styleClass="commandExButton_dat" id="update"
							confirm="#{msg.SY_MSG_0003W}"
							action="#{pc_Cog05501.doUpdateAction}"></hx:commandExButton></TD>
						<TD><hx:commandExButton type="submit" value="削除"
							styleClass="commandExButton_dat" id="delete"
							confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Cog05501.doDeleteAction}"></hx:commandExButton></TD>
						<TD><hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clear"
							action="#{pc_Cog05501.doClearAction}"></hx:commandExButton></TD>
						<TD width="35%"></TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Cog05501.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Cog05501.propKmanPatanList.scrollPosition}" id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
</SCRIPT>
</HTML>

