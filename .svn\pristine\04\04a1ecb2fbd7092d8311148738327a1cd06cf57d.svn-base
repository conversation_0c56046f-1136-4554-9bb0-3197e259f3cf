<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/hk/Hkd00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Hkd00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

//出力区分指定による帳票タイトル変更
function titleChange(thisObj) {

	var code = thisObj.value;

	var hid01 = document.getElementById('form1:htmlPdfId01').value;
	var hid02 = document.getElementById('form1:htmlPdfId02').value;

	if(code == 0){
		document.getElementById('form1:htmlSlitTitle').value = hid01;
	} else if (code == 1){
		document.getElementById('form1:htmlSlitTitle').value = hid02;
	}
	
}
</SCRIPT>


</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Hkd00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Hkd00301.doCloseDispAction}"></hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Hkd00301.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName"
				value="#{pc_Hkd00301.screenName}"></h:outputText>
				
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
					styleClass="outputText" escape="false"></h:outputText>
				</FIELDSET>

				<!--↓content↓-->
				<DIV class="head_button_area"></DIV>
				<DIV id="content">
					<DIV class="column" align="center">

<TABLE border="0" cellpadding="5">
	<TBODY>
		<TR>
		<TD width="650">
			<TABLE class="table" width="100%" style="margin-top:7px;">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="155">
						<!-- 取引日付 -->
						<h:outputText styleClass="outputText"
							value="取引期間"
							style="#{pc_Hkd00301.propStartDate.labelStyle}"></h:outputText>
						</TH>
						<TD>
							<h:inputText styleClass="inputText" id="htmlStartDate"
								value="#{pc_Hkd00301.propStartDate.dateValue}"
								style="#{pc_Hkd00301.propStartDate.style}" size="10">
								<f:convertDateTime />
								<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
								<hx:inputHelperDatePicker /></h:inputText>
							<h:inputText styleClass="inputText" id="htmlDealingsTimeFrom"
								value="#{pc_Hkd00301.propDealingsTimeFrom.dateValue}"
								style="#{pc_Hkd00301.propDealingsTimeFrom.style}"
								size="2">
								<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
								<f:convertDateTime type="time" timeStyle="short"/>
							</h:inputText>
	
							<h:outputText styleClass="outputText" value="～"></h:outputText>

							<h:inputText styleClass="inputText" id="htmlEndDate"
								value="#{pc_Hkd00301.propEndDate.dateValue}"
								style="#{pc_Hkd00301.propEndDate.style}" size="10">
								<f:convertDateTime />
								<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
								<hx:inputHelperDatePicker /></h:inputText>
							<h:inputText styleClass="inputText" id="htmlDealingsTimeTo"
								value="#{pc_Hkd00301.propDealingsTimeTo.dateValue}"
								style="#{pc_Hkd00301.propDealingsTimeTo.style}"
								size="2">
								<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
								<f:convertDateTime type="time" timeStyle="short"/>
							</h:inputText>
						</TD>
					</TR>

					<TR>
						<TH nowrap class="v_a" width="155">
						<!-- 発行機 -->
							<h:outputText styleClass="outputText"
								value="#{pc_Hkd00301.propHakoki.labelName}"></h:outputText>
						</TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlHakoki"
							value="#{pc_Hkd00301.propHakoki.value}">
							<f:selectItems value="#{pc_Hkd00301.propHakoki.list}" /></h:selectOneMenu>
						</TD>
					</TR>
					
					<TR>
						<TH nowrap class="v_a" width="155">
						<!-- 支払方法 -->
						<h:outputText 
							styleClass="outputText" id="lblPayHoho"
							value="#{pc_Hkd00301.propPayHoho.name}"
							style="#{pc_Hkd00301.propPayHoho.labelStyle}">
						</h:outputText>
						</TH>
						<TD>
						
						
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlPayZero"
							value="#{pc_Hkd00301.propPayZero.checked}" >
						</h:selectBooleanCheckbox> <h:outputText
							styleClass="outputText" id="text1" value="領収金額0円">
						</h:outputText>

						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlGenkin"
							value="#{pc_Hkd00301.propGenkin.checked}" >
						</h:selectBooleanCheckbox> <h:outputText
							styleClass="outputText" id="text2" value="現金">
						</h:outputText>

						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlEdy"
							value="#{pc_Hkd00301.propEdy.checked}" >
						</h:selectBooleanCheckbox> <h:outputText
							styleClass="outputText" id="text3" value="Ｅｄｙ">
						</h:outputText>

						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlDairiHakko"
							value="#{pc_Hkd00301.propDairihakko.checked}" >
						</h:selectBooleanCheckbox> 
						<h:outputText
							styleClass="outputText" id="text4" value="代理発行">
						</h:outputText>
							
							
						</TD>
					</TR>
					
					<TR>
						<TH nowrap class="v_d" width="155">
						<!-- 帳票レイアウト -->
						<h:outputText styleClass="outputText" 
							value="#{pc_Hkd00301.propFrmLayout.name}"></h:outputText>
						</TH>
						<TD>
							<h:selectOneRadio styleClass="selectOneRadio" id="htmlFrmLayout"
								value="#{pc_Hkd00301.propFrmLayout.stringValue}"
								onclick="return titleChange(this);">
							<f:selectItem itemValue="0" itemLabel="発行機帳票集計表" />
							<f:selectItem itemValue="1" itemLabel="発行機利用実績集計表"/>
							</h:selectOneRadio>
						</TD>
					</TR>
					
					<TR>
						<TH nowrap class="v_a" width="135">
						<!-- 帳票タイトル -->
						<h:outputText styleClass="outputText"
							value="#{pc_Hkd00301.propSlitTitle.labelName}"></h:outputText>
						</TH>
						<TD><h:inputText styleClass="inputText" id="htmlSlitTitle" size="71"
							value="#{pc_Hkd00301.propSlitTitle.stringValue}"
							style="#{pc_Hkd00301.propSlitTitle.style}"
							maxlength="#{pc_Hkd00301.propSlitTitle.maxLength}"></h:inputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
		</TD>
		</TR>
	</TBODY>
</TABLE>

<BR>

<TABLE border="0" cellpadding="5">
	<TBODY>
		<TR>
		<TD width="650">

<TABLE class="table" width="100%" style="margin-top:7px;">
	<TBODY>
		<TR>
			<!-- 発行機帳票集計表・出力条件 -->
			<TH colspan=3 class="group_label_top">
			<h:outputText styleClass="outputText" 
				value="発行機帳票集計表・出力条件"></h:outputText>
		</TR>

		<TR>
			<TH width="10" class="group_label"></TH>
			<TH nowrap class="v_a" width="145">
			<!-- 明細集計レベル指定 -->
				<h:outputText styleClass="outputText"
					value="#{pc_Hkd00301.propDtlLevel.labelName}"
					style="#{pc_Hkd00301.propDtlLevel.labelStyle}"></h:outputText>
			</TH>
			<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlDtlLevel"
						value="#{pc_Hkd00301.propDtlLevel.stringValue}"
						disabled="#{pc_Hkd00301.propDtlLevel.disabled}"
						style="#{pc_Hkd00301.propDtlLevel.style}">
					<f:selectItems value="#{pc_Hkd00301.propDtlLevel.list}" /></h:selectOneMenu>
					<hx:commandExButton type="submit" value="選択" styleClass="commandExButton"
						action="#{pc_Hkd00301.doSelectDtlLevelAction}"
						disabled="#{pc_Hkd00301.propSelectButton.disabled}"></hx:commandExButton>
					<hx:commandExButton type="submit" value="解除" styleClass="commandExButton"
						action="#{pc_Hkd00301.doUnSelectDtlLevelAction}"
						disabled="#{pc_Hkd00301.propUnSelectButton.disabled}"></hx:commandExButton>
			</TD>
		</TR>

		<TR>
			<TH width="10" class="group_label"></TH>
			<TH nowrap class="v_d" width="145">
			<!-- 所属学科組織 -->
			<h:outputText styleClass="outputText"
				value="#{pc_Hkd00301.propSgksList.name}"></h:outputText><BR>
				&nbsp;&nbsp;（複数選択可）</TH>
			<TD><h:selectManyListbox styleClass="selectManyListbox" 
						id="htmlSgksList" size="5" style="width:95%"
						value="#{pc_Hkd00301.propSgksList.stringValue}"
						disabled="#{pc_Hkd00301.propSgksList.disabled}">
						<f:selectItems value="#{pc_Hkd00301.propSgksList.list}" />
					</h:selectManyListbox>
			</TD>
		</TR>

		<TR>
			<TD width="10" class="group_label"></TD>
			<TH class="v_d" width="145">
			<!-- Ｅｄｙ最終締め日時 -->
				<h:outputText styleClass="outputText" id="lblEdyLastSimeChk"
					value="#{pc_Hkd00301.propEdyLastSimeChk.labelName}"
					style="#{pc_Hkd00301.propEdyLastSimeChk.labelStyle}"></h:outputText>
			</TH>
			<TD>
				<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
					id="htmlEdyLastSimeChk"
					value="#{pc_Hkd00301.propEdyLastSimeChk.checked}" style="#{pc_Hkd00301.propEdyLastSimeChk.style}">
				</h:selectBooleanCheckbox>
				<h:outputText styleClass="outputText" value="最終締め日時以降の実績は表示しない"></h:outputText>
			</TD>
		</TR>

		<TR>
			<TD width="10" class="group_label"></TD>
			<TH class="v_d" width="145">
			<!-- 集計期間 -->
			<h:outputText styleClass="outputText" 
				value="#{pc_Hkd00301.propFrmTotalTerm.name}"></h:outputText>
			</TH>
			<TD><h:selectOneRadio styleClass="selectOneRadio" id="htmlFrmTotalTerm"
					value="#{pc_Hkd00301.propFrmTotalTerm.stringValue}">
				<f:selectItem itemValue="0" itemLabel="取引期間集計" />
				<f:selectItem itemValue="1" itemLabel="日別"/>
				<f:selectItem itemValue="2" itemLabel="Ｅｄｙ締め日時別"/>
				</h:selectOneRadio>
			</TD>
		</TR>
	</TBODY>
</TABLE>
		</TD>
		</TR>
	</TBODY>
</TABLE>

<BR>

<TABLE border="0" cellpadding="5">
	<TBODY>
		<TR>
		<TD width="650">

<TABLE class="table" width="100%" style="margin-top:7px;">
	<TBODY>
		<TR>
			<TH colspan=3 class="group_label_top">
			<!-- 発行機利用実績集計表・出力条件 -->
			<h:outputText styleClass="outputText" value="発行機利用実績集計表・出力条件"></h:outputText>
		</TR>

		<TR>
			<TH width="10" class="group_label"></TH>
			<TH width="145" nowrap class="v_d">
			<!-- 集計単位 -->
			<h:outputText styleClass="outputText" 
				value="#{pc_Hkd00301.propTotalTani.name}"></h:outputText>
			</TH>
			<TD>
				<h:selectOneRadio styleClass="selectOneRadio" id="htmlTotalTani"
					value="#{pc_Hkd00301.propTotalTani.stringValue}">
				<f:selectItem itemValue="0" itemLabel="人数別" />
				<f:selectItem itemValue="1" itemLabel="部数別"/>
				</h:selectOneRadio>
			</TD>
		</TR>
		
		<TR>
			<TH width="10" class="group_label"></TH>
			<TH nowrap class="v_d" width="145">
			<!-- 集計期間 -->
			<h:outputText styleClass="outputText" 
				value="#{pc_Hkd00301.propSiyoTotalTerm.name}"></h:outputText>
			</TH>
			<TD>
				<h:selectOneRadio styleClass="selectOneRadio" id="htmlSiyoTotalTerm"
					value="#{pc_Hkd00301.propSiyoTotalTerm.stringValue}">
				<f:selectItem itemValue="0" itemLabel="月別" />
				<f:selectItem itemValue="1" itemLabel="曜日別"/>
				<f:selectItem itemValue="2" itemLabel="日別"/>
				</h:selectOneRadio>
			</TD>
		</TR>
	</TBODY>
</TABLE>
		</TD>
		</TR>
	</TBODY>
</TABLE>

<BR>

<TABLE border="0" class="button_bar" height="100%" width="100%">
	<TBODY>
		<TR>
			<TD align="center" valign="middle" height="100%">
				<hx:commandExButton type="submit" value="PDF作成" styleClass="commandExButton_out"
				id="pdfOut" action="#{pc_Hkd00301.doPdfOutAction}" confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
				<hx:commandExButton type="submit" value="EXCEL作成" styleClass="commandExButton_out"
				id="excelOut" action="#{pc_Hkd00301.doExcelOutAction}" confirm="#{msg.SY_MSG_0027W}"></hx:commandExButton>
				<hx:commandExButton type="submit" value="CSV作成" styleClass="commandExButton_out"
				id="csvOut" action="#{pc_Hkd00301.doCvsOutAction}" confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
				<hx:commandExButton type="submit" value="出力項目指定" styleClass="commandExButton_out"
				id="setoutput" action="#{pc_Hkd00301.doSetoutputAction}"></hx:commandExButton>
			</TD>
		</TR>
	</TBODY>
</TABLE>

					</DIV>
				</DIV>
				<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Hkd00301.propPdfId01.stringValue}" id="htmlPdfId01"></h:inputHidden>
			<h:inputHidden value="#{pc_Hkd00301.propPdfId02.stringValue}" id="htmlPdfId02"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
</HTML>