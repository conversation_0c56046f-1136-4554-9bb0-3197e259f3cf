<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssg01801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssg01801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
	// 確認メッセージでOKが押下された場合の処理

	function confirmOk() {
		var btnId = document.getElementById('form1:linkFlg').value;

		// ボタン押下フラグをセットし、同じ処理が流れないようにする
		var nowBtn = document.getElementById('form1:executable').value;
		document.getElementById('form1:executable').value = eval(nowBtn) + 1;

		if (btnId == 1){
			indirectClick('pdfout');
		}

		if (btnId == 2){
			indirectClick('csvout');
		}

		if (btnId == 3){
			indirectClick('print');
		}
		
		if (btnId == 4){
			indirectClick('excelout');
		}
	}

	// 確認メッセージでキャンセルが押下された場合の処理
	function confirmCancel() {
		// ボタン押下フラグをクリアする
		document.getElementById('form1:executable').value = 0;
	}
	
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssg01801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssg01801.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Ssg01801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssg01801.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<DIV align="right"></DIV>
			<TABLE border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR align="center">
						<TD width="974">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="90"><h:outputText
										styleClass="outputText" id="htmlLblNendo"
										value="#{pc_Ssg01801.propNendo.labelName}"
										style="#{pc_Ssg01801.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD width="399"><h:inputText styleClass="inputText"
										id="htmlNendo" value="#{pc_Ssg01801.propNendo.dateValue}"
										size="4" disabled="#{pc_Ssg01801.propNendo.disabled}"
										readonly="#{pc_Ssg01801.propNendo.readonly}"
										style="#{pc_Ssg01801.propNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText> <h:outputText styleClass="outputText"
										id="htmlNendoLabel" value="年度">
									</h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="110"><h:outputText
										styleClass="outputText" id="htmlLblGakko"
										value="#{pc_Ssg01801.propGakko.labelName}"
										style="#{pc_Ssg01801.propGakko.labelStyle}">
									</h:outputText></TH>
									<TD width="399"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakko" value="#{pc_Ssg01801.propGakko.stringValue}"
										disabled="#{pc_Ssg01801.propGakko.disabled}"
										style="width:300px;"
										readonly="#{pc_Ssg01801.propGakko.readonly}">
										<f:selectItems value="#{pc_Ssg01801.propGakko.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select"
										action="#{pc_Ssg01801.doSelectAction}"
										style="white-space: nowrap;"
										disabled="#{pc_Ssg01801.propBtnSelect.disabled}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="解除" styleClass="commandExButton" id="cancel"
										action="#{pc_Ssg01801.doCancelAction}"
										style="white-space: nowrap;"
										disabled="#{pc_Ssg01801.propBtnUnSelect.disabled}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_c" width="110"><h:outputText
										styleClass="outputText" id="htmlLblGakkaSosikiLv"
										value="#{pc_Ssg01801.propGakkaSosikiLv.labelName}"
										style="#{pc_Ssg01801.propGakkaSosikiLv.labelStyle}">
									</h:outputText></TH>
									<TD width="399"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakkaSosikiLv"
										value="#{pc_Ssg01801.propGakkaSosikiLv.stringValue}"
										disabled="#{pc_Ssg01801.propGakkaSosikiLv.disabled}"
										style="width:384px;"
										readonly="#{pc_Ssg01801.propGakkaSosikiLv.readonly}">
										<f:selectItems value="#{pc_Ssg01801.propGakkaSosikiLv.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_d" width="110"><h:outputText
										styleClass="outputText" id="htmlLblPageBreak"
										value="#{pc_Ssg01801.propPageBreak.labelName}"
										style="#{pc_Ssg01801.propPageBreak.labelStyle}">
									</h:outputText></TH>
									<TD width="399"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlPageBreak"
										value="#{pc_Ssg01801.propPageBreak.stringValue}"
										disabled="#{pc_Ssg01801.propPageBreak.disabled}"
										readonly="#{pc_Ssg01801.propPageBreak.readonly}"
										style="#{pc_Ssg01801.propPageBreak.style}">
										<f:selectItems value="#{pc_Ssg01801.propPageBreak.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_e" width="110"><h:outputText
										styleClass="outputText" id="htmlLblSyukeiTani"
										value="#{pc_Ssg01801.propSyukeiTani.labelName}"
										style="#{pc_Ssg01801.propSyukeiTani.labelStyle}">
									</h:outputText></TH>
									<TD width="399"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSyukeiTani"
										value="#{pc_Ssg01801.propSyukeiTani.stringValue}"
										readonly="#{pc_Ssg01801.propSyukeiTani.readonly}"
										disabled="#{pc_Ssg01801.propSyukeiTani.disabled}"
										style="#{pc_Ssg01801.propSyukeiTani.style}">
										<f:selectItems value="#{pc_Ssg01801.propSyukeiTani.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH rowspan="2"><h:outputText styleClass="outputText"
										id="htmlSyuturyokusitei" value="出力指定">
									</h:outputText></TH>
									<TD colspan=""><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlHirituCheck"
										value="#{pc_Ssg01801.propHirituCheck.checked}"
										disabled="#{pc_Ssg01801.propHirituCheck.disabled}"
										readonly="#{pc_Ssg01801.propHirituCheck.readonly}"
										style="#{pc_Ssg01801.propHirituCheck.style}">
									</h:selectBooleanCheckbox> <h:outputText
										styleClass="outputText" id="htmlLblHirituCheck"
										value="#{pc_Ssg01801.propHirituCheck.labelName}"
										style="#{pc_Ssg01801.propHirituCheck.labelStyle}"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TD colspan=""><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSyukeiCheck"
										value="#{pc_Ssg01801.propSyukeiCheck.checked}"
										disabled="#{pc_Ssg01801.propSyukeiCheck.disabled}"
										readonly="#{pc_Ssg01801.propSyukeiCheck.readonly}"
										style="#{pc_Ssg01801.propSyukeiCheck.style}">
									</h:selectBooleanCheckbox> <h:outputText
										styleClass="outputText" id="htmlLblSyukeiCheck"
										value="#{pc_Ssg01801.propSyukeiCheck.labelName}"
										style="#{pc_Ssg01801.propSyukeiCheck.labelStyle}"></h:outputText>
									</TD>
								</TR>
								<TR>
									<hx:jspPanel rendered="#{!pc_Ssg01801.hiseiki}">
									<TH class="v_d" width="110"><h:outputText styleClass="outputText"
										id="htmlSyukeiTaisyo" value="集計対象">
									</h:outputText></TH>
									</hx:jspPanel>
									<hx:jspPanel rendered="#{pc_Ssg01801.hiseiki}">
									<TH class="v_d" width="110" rowspan="3"><h:outputText styleClass="outputText"
										id="htmlSyukeiTaisyo2" value="集計対象">
									</h:outputText></TH>
									</hx:jspPanel>
									<TD colspan=""><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlJitaiCountCheck"
										value="#{pc_Ssg01801.propJitaiCountCheck.checked}"
										disabled="#{pc_Ssg01801.propJitaiCountCheck.disabled}"
										readonly="#{pc_Ssg01801.propJitaiCountCheck.readonly}"
										style="#{pc_Ssg01801.propJitaiCountCheck.style}">
									</h:selectBooleanCheckbox> <h:outputText
										styleClass="outputText" id="htmlLblJitaiCountCheck"
										value="#{pc_Ssg01801.propJitaiCountCheck.labelName}"
										style="#{pc_Ssg01801.propJitaiCountCheck.labelStyle}"></h:outputText>
									</TD>
								</TR>
						        <hx:jspPanel rendered="#{pc_Ssg01801.hiseiki}">
								<TR>
									<TD colspan="">
										<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox"
											id="htmlSeiki" value="#{pc_Ssg01801.propSeiki.checked}"
											disabled="#{pc_Ssg01801.propSeiki.disabled}"
											readonly="#{pc_Ssg01801.propSeiki.readonly}"
											style="#{pc_Ssg01801.propSeiki.style}">
                							</h:selectBooleanCheckbox>
                							<h:outputText styleClass="outputText" id="htmlLblSeiki"
											value="#{pc_Ssg01801.propSeiki.name}"></h:outputText>        
									</TD>
								</TR>
								<TR>
									<TD colspan="">
										<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox"
                 							id="htmlHiseiki" value="#{pc_Ssg01801.propHiseiki.checked}"
                 							disabled="#{pc_Ssg01801.propHiseiki.disabled}"
                 							readonly="#{pc_Ssg01801.propHiseiki.readonly}"
                 							style="#{pc_Ssg01801.propHiseiki.style}">
										</h:selectBooleanCheckbox>
										<h:outputText styleClass="outputText" id="htmlLblHiseiki"
                 							value="#{pc_Ssg01801.propHiseiki.name}">
                 						</h:outputText>
									</TD>
								</TR>
						        </hx:jspPanel>
							</TBODY>
						</TABLE>
						<BR>
						</TD>
					</TR>
					<TR align="center">
						<TD width="974">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_f" width="110"><h:outputText
										styleClass="outputText" id="htmlLblRepTitle"
										value="#{pc_Ssg01801.propRepTitle.labelName}"
										style="#{pc_Ssg01801.propRepTitle.labelStyle}"></h:outputText></TH>
									<TD width="399"><h:inputText styleClass="inputText"
										id="htmlRepTitle"
										value="#{pc_Ssg01801.propRepTitle.stringValue}" size="60"
										maxlength="#{pc_Ssg01801.propRepTitle.maxLength}"
										disabled="#{pc_Ssg01801.propRepTitle.disabled}"
										readonly="#{pc_Ssg01801.propRepTitle.readonly}"
										style="#{pc_Ssg01801.propRepTitle.style}">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
				width="516">
				<TBODY>
					<TR align="right">
						<TD><hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfout"
							confirm="#{msg.SY_MSG_0019W}"
							action="#{pc_Ssg01801.doPdfOutAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="EXCEL作成" styleClass="commandExButton_out"
							id="excelout" confirm="#{msg.SY_MSG_0027W}"
							action="#{pc_Ssg01801.doExcelOutAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="CSV作成" styleClass="commandExButton_out"
							id="csvout" confirm="#{msg.SY_MSG_0020W}"
							action="#{pc_Ssg01801.doCsvOutAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="出力項目指定" styleClass="commandExButton_out"
							id="setoutput" action="#{pc_Ssg01801.doSetoutputAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden id="linkFlg"
				value="#{pc_Ssg01801.propLinkFlg.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="executable"
				value="#{pc_Ssg01801.propExecutable.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="gakkoFlg"
				value="#{pc_Ssg01801.propGakkoFlg.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

