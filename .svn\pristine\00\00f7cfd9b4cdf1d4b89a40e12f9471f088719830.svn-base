<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssd01301T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssd01301T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />

<SCRIPT type="text/javascript">
function autochange(thisObj, thisEvent){
	// 内定者決定企業選択時
	if(thisObj.value == "3"){
		// 内定辞退者含むチェックボックス活性
		document.getElementById("form1:htmlNaiteiJitai").disabled = false;
	// 上記以外

	}else{
		// 内定辞退者含むチェックボックス非活性
		document.getElementById("form1:htmlNaiteiJitai").disabled = true;
		document.getElementById("form1:htmlNaiteiJitai").checked = false;
		document.getElementById("form1:htmlNaiteiJitaiValue").value = "0";
	}
}
function keepvalue(thisObj, thisEvent){
	if (thisObj.checked){
		document.getElementById("form1:htmlNaiteiJitaiValue").value = "1";
	}else{
		document.getElementById("form1:htmlNaiteiJitaiValue").value = "0";
	}
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssd01301T02.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">

	<!-- ヘッダーインクルード -->
	<jsp:include page ="../inc/header.jsp" />

	<!-- ヘッダーへのデータセット領域 -->
	<DIV style="display:none;">
		<hx:commandExButton type="submit" value="閉じる"
			styleClass="commandExButton" id="closeDisp"
			action="#{pc_Ssd01301T02.doCloseDispAction}"></hx:commandExButton>
		<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssd01301T01.ssd01301.funcId}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssd01301T02.screenName}"></h:outputText>
	</DIV>

	<!--↓outer↓-->
	<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
		<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			styleClass="outputText" escape="false">
		</h:outputText>
	</FIELDSET>
			
	<!--↓content↓-->
	<DIV class="head_button_area" >
　<!-- ←レイアウトの問題の為に、全角スペースを配置 -->
	</DIV>

	<DIV id="content">
		<DIV class="column">
			<TABLE border="0" class="table" width="800" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblKjnNendo"
							value="#{pc_Ssd01301T01.ssd01301.propKjnNendo.labelName}"
							style="#{pc_Ssd01301T01.ssd01301.propKjnNendo.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:inputText styleClass="inputText" id="htmlKjnNendo"
							value="#{pc_Ssd01301T01.ssd01301.propKjnNendo.value}"
							readonly="#{pc_Ssd01301T01.ssd01301.propKjnNendo.readonly}"
							style="#{pc_Ssd01301T01.ssd01301.propKjnNendo.style}"
							disabled="#{pc_Ssd01301T01.ssd01301.propKjnNendo.disabled}"
							size="6">
							<hx:inputHelperAssist imeMode="inactive"
								errorClass="inputText_Error" promptCharacter="_" />

							<f:convertDateTime pattern="yyyy" />
						</h:inputText><h:outputText
							styleClass="outputText" value="年度"></h:outputText></TD>
					</TR>
					<TR>
						<TH nowrap class="v_b" width="150">
							<h:outputText styleClass="outputText" id="lblOutputCondition"
							value="#{pc_Ssd01301T01.ssd01301.propOutputCondition.labelName}"
							style="#{pc_Ssd01301T01.ssd01301.propOutputCondition.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="650">
							<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlOutputCondition"
							layout="pageDirection"
							value="#{pc_Ssd01301T01.ssd01301.propOutputCondition.value}"
							style="#{pc_Ssd01301T01.ssd01301.propOutputCondition.style}"
							readonly="#{pc_Ssd01301T01.ssd01301.propOutputCondition.readonly}"
							disabled="#{pc_Ssd01301T01.ssd01301.propOutputCondition.disabled}"
							onclick="return autochange(this, event);">
							<f:selectItem itemValue="1" itemLabel="全企業" />
							<f:selectItem itemValue="2" itemLabel="就職先決定企業" />
							<f:selectItem itemValue="3" itemLabel="内定者決定企業" />
							</h:selectOneRadio>
							&nbsp;&nbsp;&nbsp;&nbsp;
							<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlNaiteiJitai" value="#{pc_Ssd01301T01.ssd01301.propNaiteiJitai.checked}"
							style="#{pc_Ssd01301T01.ssd01301.propNaiteiJitai.style}"
							readonly="#{pc_Ssd01301T01.ssd01301.propNaiteiJitai.readonly}"
							disabled="#{pc_Ssd01301T01.ssd01301.propNaiteiJitai.disabled}"
							onclick="return keepvalue(this, event);">
						</h:selectBooleanCheckbox>
							<h:outputText styleClass="outputText" id="textNaiteiJitai"
							value="内定辞退者含む">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_c" width="150"><h:outputText styleClass="outputText"
							id="labelChohyoTitle" 
							value="#{pc_Ssd01301T01.ssd01301.propChohyoTitle.labelName}"
							style="#{pc_Ssd01301T01.ssd01301.propChohyoTitle.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:inputText styleClass="inputText"
							id="htmlChohyoTitle"
							value="#{pc_Ssd01301T01.ssd01301.propChohyoTitle.stringValue}"
							style="#{pc_Ssd01301T01.ssd01301.propChohyoTitle.style}" size="70"
							disabled="#{pc_Ssd01301T01.ssd01301.propChohyoTitle.disabled}"
							maxlength="#{pc_Ssd01301T01.ssd01301.propChohyoTitle.maxLength}"
							readonly="#{pc_Ssd01301T01.ssd01301.propChohyoTitle.readonly}">
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="800" border="0" cellpadding="0" cellspacing="0" style="margin-top:20px;">
				<TBODY>
					<TR>
						<TD style="text-align:left;" width="100%"><hx:commandExButton
							type="submit" value="　全件指定　" styleClass="tab_head_off" 
							id="btnSsd01301T01" action="#{pc_Ssd01301T02.doBtnSsd01301T01Action}"></hx:commandExButton><hx:commandExButton
							type="submit" value="　地域指定　" styleClass="tab_head_on"
							id="btnSsd01301T02"></hx:commandExButton><hx:commandExButton
							type="submit" value="　業種指定　" styleClass="tab_head_off"
							id="btnSsd01301T03" action="#{pc_Ssd01301T02.doBtnSsd01301T03Action}"></hx:commandExButton><hx:commandExButton
							type="submit" value="　企業指定　" styleClass="tab_head_off"
							id="btnSsd01301T04" action="#{pc_Ssd01301T02.doBtnSsd01301T04Action}"></hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TD style="align:left;">
						<TABLE width="100%" border="0" cellspacing="0" cellpadding="0" class="tab_body">
							<TBODY>
								<TR>
									<TD align="center" style="height:320px">
										<TABLE width="600" border="0" cellspacing="0" class="">
											<TBODY>
												<TR>
													<TH width="80%" align="left"><h:outputText styleClass="outputText"
														id="lblGyosyuHeaderCmt" value="地域一覧"></h:outputText></TH>
													<TD width="20%" style="text-align: right">
														<h:outputText styleClass="outputText" id="htmlListChiikiCnt" value="#{pc_Ssd01301T02.propListChiiki.listCount == 0 ? 0 : pc_Ssd01301T02.propListChiiki.listCount - 1}"></h:outputText>
														<h:outputText styleClass="outputText" id="lblListChiikiCnt" value="件"></h:outputText>
													</TD>
												</TR>
												
												<TR>
												<TD colspan="2">
													<TABLE width="600" border="0" cellspacing="0" class="list_table">
													<TBODY>
														<TR>
															<TD><h:selectManyListbox
																	styleClass="selectManyListbox" id="htmlListChiiki"
																	size="10" style="width:100%;"
																	disabled="#{pc_Ssd01301T02.propListChiiki.disabled}"
																	readonly="#{pc_Ssd01301T02.propListChiiki.readonly}"
																	value="#{pc_Ssd01301T02.propListChiiki.value}">
																	<f:selectItems
																		value="#{pc_Ssd01301T02.propListChiiki.list}" />
															</h:selectManyListbox></TD>
														</TR>
												
													</TBODY>
													</TABLE>
												</TD>
												</TR>
												
												<TR>
													<TD colspan="2" style="text-align:right;" width=""><h:outputText
														styleClass="note" id="lblSelectMany"
														value="（複数選択可）"></h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										<TABLE border="0" width="600">
											<TBODY>
												<TR>
													<TD>
														<TABLE width="100%" border="0" cellspacing="0" class="table" style="margin-top:20px;">
															<TBODY>
																<TR>
																	<TH nowrap class="v_a" width="20%" height="10">
																		<h:outputText styleClass="outputText" id="lblSort"
																	value="並び順">
																</h:outputText>
																	</TH>
																	<TD colspan="3" width="80%" height="10">
																		<h:selectOneRadio
																	disabledClass="selectOneRadio_Disabled"
																	styleClass="selectOneRadio" id="htmlSort"
																	layout="lineDirection"
																	value="#{pc_Ssd01301T02.propSort.value}"
																	style="#{pc_Ssd01301T02.propSort.style}">
																	<f:selectItem itemValue="1" itemLabel="企業コード順" />
																	<f:selectItem itemValue="2" itemLabel="企業名称カナ順" />
																</h:selectOneRadio>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="100%" class="button_bar">
								<TBODY>
									<TR>
										<TD><hx:commandExButton type="submit" value="PDF作成"
											styleClass="commandExButton_out" id="pdfout"
											action="#{pc_Ssd01301T02.doPdfoutAction}"
											confirm="#{msg.SY_MSG_0019W}"
											></hx:commandExButton><hx:commandExButton type="submit" value="CSV作成"
											styleClass="commandExButton_out" id="csvout"
											action="#{pc_Ssd01301T02.doCsvoutAction}"
											confirm="#{msg.SY_MSG_0020W}"
											></hx:commandExButton><hx:commandExButton type="submit" value="出力項目指定"
											styleClass="commandExButton_out" id="setoutput"
											action="#{pc_Ssd01301T02.doSetoutputAction}"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
		</DIV>
	</DIV>
	<!--↑outer↑-->
	<!-- フッダーインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
	<h:inputHidden value="#{pc_Ssd01301T01.ssd01301.propNaiteiJitaiValue.integerValue}" id="htmlNaiteiJitaiValue">
	<f:convertNumber />
	</h:inputHidden>
	</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
	</f:view>
</HTML>
