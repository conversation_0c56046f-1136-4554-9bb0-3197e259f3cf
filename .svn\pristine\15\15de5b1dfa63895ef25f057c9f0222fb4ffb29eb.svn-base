<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg00102.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
}

function init() {
	//開催地項目・抽選区分の活性区分を制御する変数を取得
	var gakunaigaiKbn = document.getElementById('form1:htmlHidGakunaigaiKbn').value;
	var selectKaisaiti = document.getElementById('form1:htmlHidKaisaitiFixFlg').value;
	var tyusenKbn = document.getElementById('form1:htmlHidTyusenKbn').value;
	var yusenKbn01 = document.getElementById('form1:htmlHidYusenKbn01FixFlg').value;
	var yusenKbn02 = document.getElementById('form1:htmlHidYusenKbn02FixFlg').value;
	var yusenKbn03 = document.getElementById('form1:htmlHidYusenKbn03FixFlg').value;
	var yusenKbn04 = document.getElementById('form1:htmlHidYusenKbn04FixFlg').value;
	var yusenKbn05 = document.getElementById('form1:htmlHidYusenKbn05FixFlg').value;

	var currentTab = document.getElementById('form1:htmlHidTab').value;

	//日程・時限タブの場合
	if(currentTab == "NteiTab"){
		if (gakunaigaiKbn == "1") {
			//学内の場合、開催地・会場を非活性に変える
			document.getElementById('form1:htmlHidKaisaitiFixFlg').value = "0";
			document.getElementById('form1:htmlKaisaiti').value = "";
			document.getElementById('form1:htmlKaisaiti').disabled = true;
			document.getElementById('form1:searchKaisaiti').disabled = true;
			document.getElementById('form1:selectKaisaiti').disabled = true;
			document.getElementById('form1:releasKaisaiti').disabled = true;
			document.getElementById('form1:htmlKaijo').value = "|no select|";
			document.getElementById('form1:htmlKaijo').disabled = true;
		} else if(gakunaigaiKbn == "2") {
			//学外の場合
			if(selectKaisaiti == "1"){
				document.getElementById('form1:htmlKaisaiti').disabled = true;
				document.getElementById('form1:searchKaisaiti').disabled = true;
				document.getElementById('form1:selectKaisaiti').disabled = true;
				document.getElementById('form1:releasKaisaiti').disabled = false;
				document.getElementById('form1:htmlKaijo').disabled = false;
			} else if(selectKaisaiti == "0"){
				document.getElementById('form1:htmlKaisaiti').disabled = false;
				document.getElementById('form1:searchKaisaiti').disabled = false;
				document.getElementById('form1:selectKaisaiti').disabled = false;
				document.getElementById('form1:releasKaisaiti').disabled = true;
				document.getElementById('form1:htmlKaijo').disabled = true;
			}
		}
	} else if(currentTab == "TyusenTab") {
	//抽選情報タブの場合
		if(tyusenKbn == "3"){
			//抽選なしの場合
			document.getElementById('form1:htmlHidTyusenKbn').value = tyusenKbn;
			document.getElementById('form1:htmlHidYusenKbn01FixFlg').value = "0";
			document.getElementById('form1:htmlHidYusenKbn02FixFlg').value = "0";
			document.getElementById('form1:htmlHidYusenKbn03FixFlg').value = "0";
			document.getElementById('form1:htmlHidYusenKbn04FixFlg').value = "0";
			document.getElementById('form1:htmlHidYusenKbn05FixFlg').value = "0";

			document.getElementById('form1:htmlYusenKbn01').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn01Detail').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbn01').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbnDtl01').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn02').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn02Detail').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbn02').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbnDtl02').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn03').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn03Detail').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbn03').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbnDtl03').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn04').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn04Detail').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbn04').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbnDtl04').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn05').value = "|no select|";
			document.getElementById('form1:htmlYusenKbn05Detail').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbn05').value = "|no select|";
			document.getElementById('form1:htmlHidYusenKbnDtl05').value = "|no select|";

			document.getElementById('form1:htmlYusenKbn01').disabled = true;
			document.getElementById('form1:selectYusen01').disabled = true;
			document.getElementById('form1:releaseYusen01').disabled = true;
			document.getElementById('form1:htmlYusenKbn01Detail').disabled = true;
			document.getElementById('form1:htmlYusenKbn02').disabled = true;
			document.getElementById('form1:selectYusen02').disabled = true;
			document.getElementById('form1:releaseYusen02').disabled = true;
			document.getElementById('form1:htmlYusenKbn02Detail').disabled = true;
			document.getElementById('form1:htmlYusenKbn03').disabled = true;
			document.getElementById('form1:selectYusen03').disabled = true;
			document.getElementById('form1:releaseYusen03').disabled = true;
			document.getElementById('form1:htmlYusenKbn03Detail').disabled = true;
			document.getElementById('form1:htmlYusenKbn04').disabled = true;
			document.getElementById('form1:selectYusen04').disabled = true;
			document.getElementById('form1:releaseYusen04').disabled = true;
			document.getElementById('form1:htmlYusenKbn04Detail').disabled = true;
			document.getElementById('form1:htmlYusenKbn05').disabled = true;
			document.getElementById('form1:selectYusen05').disabled = true;
			document.getElementById('form1:releaseYusen05').disabled = true;
			document.getElementById('form1:htmlYusenKbn05Detail').disabled = true;

		} else if(tyusenKbn == "1" || tyusenKbn == "2"){
			//先着、抽選の場合
			if(yusenKbn01 == "1"){
				document.getElementById('form1:htmlYusenKbn01').disabled = true;
				document.getElementById('form1:selectYusen01').disabled = true;
				document.getElementById('form1:releaseYusen01').disabled = false;
				document.getElementById('form1:htmlYusenKbn01Detail').disabled = false;
			}else{
				document.getElementById('form1:htmlYusenKbn01').disabled = false;
				document.getElementById('form1:selectYusen01').disabled = false;
				document.getElementById('form1:releaseYusen01').disabled = true;
				document.getElementById('form1:htmlYusenKbn01Detail').disabled = true;
			}
			if(yusenKbn02 == "1"){
				document.getElementById('form1:htmlYusenKbn02').disabled = true;
				document.getElementById('form1:selectYusen02').disabled = true;
				document.getElementById('form1:releaseYusen02').disabled = false;
				document.getElementById('form1:htmlYusenKbn02Detail').disabled = false;
			}else{
				document.getElementById('form1:htmlYusenKbn02').disabled = false;
				document.getElementById('form1:selectYusen02').disabled = false;
				document.getElementById('form1:releaseYusen02').disabled = true;
				document.getElementById('form1:htmlYusenKbn02Detail').disabled = true;
			}
			if(yusenKbn03 == "1"){
				document.getElementById('form1:htmlYusenKbn03').disabled = true;
				document.getElementById('form1:selectYusen03').disabled = true;
				document.getElementById('form1:releaseYusen03').disabled = false;
				document.getElementById('form1:htmlYusenKbn03Detail').disabled = false;
			}else{
				document.getElementById('form1:htmlYusenKbn03').disabled = false;
				document.getElementById('form1:selectYusen03').disabled = false;
				document.getElementById('form1:releaseYusen03').disabled = true;
				document.getElementById('form1:htmlYusenKbn03Detail').disabled = true;
			}
			if(yusenKbn04 == "1"){
				document.getElementById('form1:htmlYusenKbn04').disabled = true;
				document.getElementById('form1:selectYusen04').disabled = true;
				document.getElementById('form1:releaseYusen04').disabled = false;
				document.getElementById('form1:htmlYusenKbn04Detail').disabled = false;
			}else{
				document.getElementById('form1:htmlYusenKbn04').disabled = false;
				document.getElementById('form1:selectYusen04').disabled = false;
				document.getElementById('form1:releaseYusen04').disabled = true;
				document.getElementById('form1:htmlYusenKbn04Detail').disabled = true;
			}
			if(yusenKbn05 == "1"){
				document.getElementById('form1:htmlYusenKbn05').disabled = true;
				document.getElementById('form1:selectYusen05').disabled = true;
				document.getElementById('form1:releaseYusen05').disabled = false;
				document.getElementById('form1:htmlYusenKbn05Detail').disabled = false;
			}else{
				document.getElementById('form1:htmlYusenKbn05').disabled = false;
				document.getElementById('form1:selectYusen05').disabled = false;
				document.getElementById('form1:releaseYusen05').disabled = true;
				document.getElementById('form1:htmlYusenKbn05Detail').disabled = true;
			}
		}
	}
}

function confirmOk() {
	if (document.getElementById("form1:delete")) {
		//削除処理の実行

		indirectClick('delete');					
	}
}
function confirmCancel() {
	if (document.getElementById("form1:delete")) {
		//更新処理パラメタの初期化

		document.getElementById("form1:htmlExecutableDeleteFlg").value = "0";
	}
}

function openKaisaitiSearchWindow(thisObj, thisEvent) {
	var url="${pageContext.request.contextPath}/faces/rev/xrh/pXrh0101.jsp?retFieldName=form1:htmlKaisaiti";
	openModalWindow(url, "pXrh0101", "<%= com.jast.gakuen.rev.xrh.PXrh0101.getWindowOpenOption() %>");
	return true;

}

function doKaisaitiAjax(thisObj, thisEvent) {
	var servlet = "rev/xrg/XrgMeiKaisaitiNmAJAX";
	var target = "form1:htmlKaisaitiNm";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function loadAction(event){
 doKaisaitiAjax(document.getElementById('form1:htmlKaisaiti'), event);
}

function onCangeGakunaigaiKbn(thisObj, thisEvent) {
	var gakunaigaiKbn = thisObj.value;
	if (gakunaigaiKbn == "1") {
		//学内の場合、開催地・会場を非活性に変える
		document.getElementById('form1:htmlHidGakunaigaiKbn').value = gakunaigaiKbn;
		document.getElementById('form1:htmlHidKaisaitiFixFlg').value = "0";

		document.getElementById('form1:htmlKaisaiti').value = "";
		document.getElementById('form1:htmlKaisaiti').disabled = true;
		doKaisaitiAjax(document.getElementById('form1:htmlKaisaiti'), thisEvent);
		document.getElementById('form1:searchKaisaiti').disabled = true;
		document.getElementById('form1:selectKaisaiti').disabled = true;
		document.getElementById('form1:releasKaisaiti').disabled = true;
		document.getElementById('form1:htmlKaijo').value = "|no select|";
		document.getElementById('form1:htmlKaijo').disabled = true;
	} else if(gakunaigaiKbn == "2") {
		//学外の場合
		document.getElementById('form1:htmlHidGakunaigaiKbn').value = gakunaigaiKbn;
		document.getElementById('form1:htmlHidKaisaitiFixFlg').value = "0";

		document.getElementById('form1:htmlKaisaiti').disabled = false;
		document.getElementById('form1:searchKaisaiti').disabled = false;
		document.getElementById('form1:selectKaisaiti').disabled = false;
		document.getElementById('form1:releasKaisaiti').disabled = true;
		document.getElementById('form1:htmlKaijo').disabled = true;
	}
}

function onCangeTyusenKbn(thisObj, thisEvent) {
	var tyusenKbn = thisObj.value;
	if (tyusenKbn == "3") {
		//抽選なしの場合優先順位を非活性に変える
		document.getElementById('form1:htmlHidTyusenKbn').value = tyusenKbn;
		document.getElementById('form1:htmlHidYusenKbn01FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn02FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn03FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn04FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn05FixFlg').value = "0";

		document.getElementById('form1:htmlYusenKbn01').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn01Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn01').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl01').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn02').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn02Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn02').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl02').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn03').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn03Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn03').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl03').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn04').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn04Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn04').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl04').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn05').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn05Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn05').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl05').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn01').disabled = true;
		document.getElementById('form1:selectYusen01').disabled = true;
		document.getElementById('form1:releaseYusen01').disabled = true;
		document.getElementById('form1:htmlYusenKbn01Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn02').disabled = true;
		document.getElementById('form1:selectYusen02').disabled = true;
		document.getElementById('form1:releaseYusen02').disabled = true;
		document.getElementById('form1:htmlYusenKbn02Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn03').disabled = true;
		document.getElementById('form1:selectYusen03').disabled = true;
		document.getElementById('form1:releaseYusen03').disabled = true;
		document.getElementById('form1:htmlYusenKbn03Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn04').disabled = true;
		document.getElementById('form1:selectYusen04').disabled = true;
		document.getElementById('form1:releaseYusen04').disabled = true;
		document.getElementById('form1:htmlYusenKbn04Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn05').disabled = true;
		document.getElementById('form1:selectYusen05').disabled = true;
		document.getElementById('form1:releaseYusen05').disabled = true;
		document.getElementById('form1:htmlYusenKbn05Detail').disabled = true;

	} else {
		//先着・抽選の場合
		document.getElementById('form1:htmlHidTyusenKbn').value = tyusenKbn;
		document.getElementById('form1:htmlHidYusenKbn01FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn02FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn03FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn04FixFlg').value = "0";
		document.getElementById('form1:htmlHidYusenKbn05FixFlg').value = "0";
		
		document.getElementById('form1:htmlYusenKbn01').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn01Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn01').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl01').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn02').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn02Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn02').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl02').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn03').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn03Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn03').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl03').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn04').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn04Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn04').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl04').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn05').value = "|no select|";
		document.getElementById('form1:htmlYusenKbn05Detail').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbn05').value = "|no select|";
		document.getElementById('form1:htmlHidYusenKbnDtl05').value = "|no select|";

		document.getElementById('form1:htmlYusenKbn01').disabled = false;
		document.getElementById('form1:selectYusen01').disabled = false;
		document.getElementById('form1:releaseYusen01').disabled = true;
		document.getElementById('form1:htmlYusenKbn01Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn02').disabled = false;
		document.getElementById('form1:selectYusen02').disabled = false;
		document.getElementById('form1:releaseYusen02').disabled = true;
		document.getElementById('form1:htmlYusenKbn02Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn03').disabled = false;
		document.getElementById('form1:selectYusen03').disabled = false;
		document.getElementById('form1:releaseYusen03').disabled = true;
		document.getElementById('form1:htmlYusenKbn03Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn04').disabled = false;
		document.getElementById('form1:selectYusen04').disabled = false;
		document.getElementById('form1:releaseYusen04').disabled = true;
		document.getElementById('form1:htmlYusenKbn04Detail').disabled = true;
		document.getElementById('form1:htmlYusenKbn05').disabled = false;
		document.getElementById('form1:selectYusen05').disabled = false;
		document.getElementById('form1:releaseYusen05').disabled = true;
		document.getElementById('form1:htmlYusenKbn05Detail').disabled = true;
	}
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
	if(changeDataFlg == "1"){
		return doPopupMsg(id);
	}else{
		return true;
	}
	return true;
}

// データチェンジ時
function onCangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

function onCangeYusenKbn01(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbn01').value = thisObj.value;
}

function onCangeYusenKbn02(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbn02').value = thisObj.value;
}

function onCangeYusenKbn03(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbn03').value = thisObj.value;
}

function onCangeYusenKbn04(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbn04').value = thisObj.value;
}

function onCangeYusenKbn05(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbn05').value = thisObj.value;
}

function onCangeYusenKbnDtl01(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbnDtl01').value = thisObj.value;
}

function onCangeYusenKbnDtl02(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbnDtl02').value = thisObj.value;
}

function onCangeYusenKbnDtl03(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbnDtl03').value = thisObj.value;
}

function onCangeYusenKbnDtl04(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbnDtl04').value = thisObj.value;
}

function onCangeYusenKbnDtl05(thisObj, thisEvent) {
	document.getElementById('form1:htmlHidYusenKbnDtl05').value = thisObj.value;
}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	
	<body onload="init()">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrg00102.onPageLoadBegin}">
		
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00102.doCloseDispAction}"></hx:commandExButton><h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00102.funcId}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00102.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND><h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right">
						<hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="53" action="#{pc_Xrg00102.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
					<TABLE border="0" cellpadding="5">
						<TBODY>
							<TR>
								<TD width="800" valign="top">
									<!--↓タブ間共有テーブル↓-->
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
										<TBODY>
											<TR>
												<TH class="v_a" width="175">
													<h:outputText styleClass="outputText" id="lblBunruiNm"
													value="#{pc_Xrg00102.propBunruiNm.labelName}"
													style="#{pc_Xrg00102.propBunruiNm.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="550">
													<h:outputText styleClass="outputText" id="htmlBunruiNm"
														value="#{pc_Xrg00102.propBunruiNm.stringValue}"
														style="#{pc_Xrg00102.propBunruiNm.style}">
													</h:outputText>
												</TD>
												<TD width="100" rowspan="3"
													style="background-color: transparent; text-align: right"
													class="clear_border">
													<hx:commandExButton type="submit"
														value="確定" styleClass="commandExButton" id="determinateSchooling"
														action="#{pc_Xrg00102.doDeterminateSchoolingAction}"
														tabindex="2"
														disabled="#{pc_Xrg00102.propDeterminateSchooling.disabled}"
														style="#{pc_Xrg00102.propDeterminateSchooling.style}"
														rendered="#{pc_Xrg00102.propDeterminateSchooling.rendered}">
													</hx:commandExButton>
													<hx:commandExButton type="submit"
														value="解除" styleClass="commandExButton" id="releaseSchooling"
														action="#{pc_Xrg00102.doReleaseSchoolingAction}" tabindex="3"
														style="#{pc_Xrg00102.propReleaseSchooling.style}"
														disabled="#{pc_Xrg00102.propReleaseSchooling.disabled}"
														rendered="#{pc_Xrg00102.propReleaseSchooling.rendered}"
														confirm="#{msg.SY_MSG_0014W}">
													</hx:commandExButton>
												</TD>
											</TR>
											<TR>
												<TH class="v_b" width="175">
													<h:outputText styleClass="outputText" id="lblTargetNendo" 
														value="#{pc_Xrg00102.propTargetNendo.labelName}" 
														style="#{pc_Xrg00102.propTargetNendo.labelStyle}">
													</h:outputText>
												</TH>
												<TD>
													<h:outputText styleClass="outputText" id="htmlTargetNendo"
														value="#{pc_Xrg00102.propTargetNendo.integerValue}"
														style="#{pc_Xrg00102.propTargetNendo.style}">
													</h:outputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_c" width="175">
													<h:outputText styleClass="outputText" id="lblSchoolingSbtCd"
														value="#{pc_Xrg00102.propSchoolingSbtCd.labelName}"
														style="#{pc_Xrg00102.propSchoolingSbtCd.labelStyle}">
													</h:outputText>
												</TH>
												<TD>
													<h:inputText styleClass="inputText" id="htmlSchoolingSbtCd"
														onchange="onCangeData();"
														value="#{pc_Xrg00102.propSchoolingSbtCd.stringValue}"
														size="10" tabindex="1"
														disabled="#{pc_Xrg00102.propSchoolingSbtCd.disabled}"
														maxlength="#{pc_Xrg00102.propSchoolingSbtCd.max}"
														readonly="#{pc_Xrg00102.propSchoolingSbtCd.readonly}"
														style="#{pc_Xrg00102.propSchoolingSbtCd.style}">
													</h:inputText>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									<!--↑タブ間共有テーブル↑-->
									<BR>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					<TABLE>
						<TR>
							<TD height="5"></TD>
						</TR>
					</TABLE>
					<TABLE width="800" border="0" cellpadding="0" cellspacing="0" height=430>
						<TBODY>
							<TR>
								<TD width="800" align="left">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD class="tab_head_off">
													<hx:commandExButton type="submit" id="MoveSbtTab"
														value="スクーリング種別" styleClass="#{pc_Xrg00102.propMoveSbtTab.style}"
														tabindex="4"
														action="#{pc_Xrg00102.doMoveSbtTabAction}"
														disabled="#{pc_Xrg00102.propMoveSbtTab.disabled}"
														style="#{pc_Xrg00102.propMoveSbtTab.style}">
													</hx:commandExButton>
												</TD>
												<TD class="tab_head_off">
													<hx:commandExButton type="submit" id="moveNteiTab"
														value="日程・時限" styleClass="#{pc_Xrg00102.propMoveNteiTab.style}"
														tabindex="5"
														action="#{pc_Xrg00102.doMoveNteiTabAction}"
														disabled="#{pc_Xrg00102.propMoveNteiTab.disabled}"
														style="#{pc_Xrg00102.propMoveNteiTab.style}">
													</hx:commandExButton>
												</TD>
												<TD class="tab_head_off">
													<hx:commandExButton type="submit" id="moveTyusenTab"
														value="抽選情報" styleClass="#{pc_Xrg00102.propMoveTyusenTab.style}"
														tabindex="6"
														action="#{pc_Xrg00102.doMoveTyusenTabAction}"
														disabled="#{pc_Xrg00102.propMoveTyusenTab.disabled}"
														style="#{pc_Xrg00102.propMoveTyusenTab.style}">
													</hx:commandExButton>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</TD>
							</TR>
							<TR>
								<!--↓スクーリング種別タブ↓-->
								<hx:jspPanel id="panel1"
									rendered="#{pc_Xrg00102.propSchJspPanel.rendered}">
									<TD valign="top">
										<TABLE border="0" cellpadding="0" cellspacing="0"
											class="tab_body" width="100%">
											<TBODY>
												<TR>
													<TD valign="top" height="370">
														<CENTER>
															<BR>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="table" width="720">
																<TBODY>
																	<TR>
																		<TH class="v_b" width="200">
																			<h:outputText styleClass="outputText"
																				id="lblKanriBusyo"
																				value="#{pc_Xrg00102.propKanriBusyo.labelName}"
																				style="#{pc_Xrg00102.propKanriBusyo.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:selectOneMenu styleClass="selectOneMenu"
																				id="htmlKanriBusyo"
																				onchange="onCangeData();"
																				value="#{pc_Xrg00102.propKanriBusyo.value}"
																				tabindex="7"
																				disabled="#{pc_Xrg00102.propKanriBusyo.disabled}">
																				<f:selectItems
																					value="#{pc_Xrg00102.propKanriBusyo.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="200">
																			<h:outputText styleClass="outputText" 
																				id="lblSchoolingName" 
																				value="#{pc_Xrg00102.propSchoolingSbtName.labelName}" 
																				style="#{pc_Xrg00102.propSchoolingSbtName.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:inputText styleClass="inputText"
																				id="htmlSchoolingName" size="75"
																				onchange="onCangeData();"
																				value="#{pc_Xrg00102.propSchoolingSbtName.stringValue}"
																				tabindex="8"
																				disabled="#{pc_Xrg00102.propSchoolingSbtName.disabled}"
																				maxlength="#{pc_Xrg00102.propSchoolingSbtName.maxLength}"
																				style="#{pc_Xrg00102.propSchoolingSbtName.style}">
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_b" width="200">
																			<h:outputText styleClass="outputText"
																				id="lblNofukinCd"
																				value="#{pc_Xrg00102.propNofukinCd.labelName}"
																				style="#{pc_Xrg00102.propNofukinCd.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:selectOneMenu styleClass="selectOneMenu"
																				id="htmlNofukinCd" onchange="onCangeData();"
																				value="#{pc_Xrg00102.propNofukinCd.value}"
																				tabindex="9"
																				disabled="#{pc_Xrg00102.propNofukinCd.disabled}">
																				<f:selectItems
																					value="#{pc_Xrg00102.propNofukinCd.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="200">
																			<h:outputText styleClass="outputText"
																				id="lblSchoolingCalcPtn"
																				value="#{pc_Xrg00102.propSchoolingCalcPtn.labelName}" 
																				style="#{pc_Xrg00102.propSchoolingCalcPtn.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:selectOneMenu styleClass="selectOneMenu"
																				id="htmlSchoolingCalcPtn" onchange="onCangeData();"
																				value="#{pc_Xrg00102.propSchoolingCalcPtn.value}"
																				tabindex="10"
																				disabled="#{pc_Xrg00102.propSchoolingCalcPtn.disabled}">
																				<f:selectItems
																					value="#{pc_Xrg00102.propSchoolingCalcPtn.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_d" width="200">
																			<h:outputText styleClass="outputText"
																				id="lblGakuseiMibun" 
																				value="#{pc_Xrg00102.propGakuseiMibun.labelName}" 
																				style="#{pc_Xrg00102.propGakuseiMibun.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlGakuseiMibun"
																				onchange="onCangeData();"
																				value="#{pc_Xrg00102.propGakuseiMibun.value}"
																				tabindex="11"
																				disabled="#{pc_Xrg00102.propGakuseiMibun.disabled}">
																				<f:selectItems
																					value="#{pc_Xrg00102.propGakuseiMibun.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_a" width="200">
																			<h:outputText styleClass="outputText"
																				id="lblChikokusu"
																				value="#{pc_Xrg00102.propChikokusu.labelName}"
																				style="#{pc_Xrg00102.propChikokusu.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:inputText styleClass="inputText"
																				id="htmlChikokusu"
																				onchange="onCangeData();"
																				value="#{pc_Xrg00102.propChikokusu.integerValue}" size="3"
																				tabindex="12"
																				disabled="#{pc_Xrg00102.propChikokusu.disabled}"
																				maxlength="#{pc_Xrg00102.propChikokusu.max}"
																				style="#{pc_Xrg00102.propChikokusu.style}">
																				<f:convertNumber type="number" pattern="0"/>
																				<hx:inputHelperAssist errorClass="inputText_Error"
																				promptCharacter="_" />
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_b" width="200">
																			<h:outputText styleClass="outputText" 
																				id="lblShuchuKogiFlg" 
																				value="#{pc_Xrg00102.propShuchuKogiFlg.labelName}" 
																				style="#{pc_Xrg00102.propShuchuKogiFlg.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox"
																				id="htmlShuchuKogiFlg"
																				onchange="onCangeData();"
																				value="#{pc_Xrg00102.propShuchuKogiFlg.checked}"
																				tabindex="13"
																				disabled="#{pc_Xrg00102.propShuchuKogiFlg.disabled}"
																				style="#{pc_Xrg00102.propShuchuKogiFlg.style}">
																			</h:selectBooleanCheckbox>
																			集中・出張講義
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="200">
																			<h:outputText styleClass="outputText" 
																				id="lblKogiFlg" 
																				value="#{pc_Xrg00102.propJukoSekyuFlg.labelName}" 
																				style="#{pc_Xrg00102.propJukoSekyuFlg.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="520">
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox"
																				id="htmlJukoSekyuFlg"
																				onchange="onCangeData();"
																				value="#{pc_Xrg00102.propJukoSekyuFlg.checked}"
																				tabindex="14"
																				disabled="#{pc_Xrg00102.propJukoSekyuFlg.disabled}"
																				style="#{pc_Xrg00102.propJukoSekyuFlg.style}">
																			</h:selectBooleanCheckbox>
																			受講料請求対象
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<BR>
															<TABLE border="0" cellspacing="0" class="button_bar" width="720">
																<TBODY>
																	<TR>
																		<TD nowrap>
																			<hx:commandExButton type="submit" value="確定"
																				styleClass="commandExButton_dat" id="sbtRegister"
																				onclick="return doPopupMsg('#{msg.SY_MSG_0002W}');"
																				tabindex="15"
																				action="#{pc_Xrg00102.doSbtRegisterAction}"
																				disabled="#{pc_Xrg00102.propSbtRegister.disabled}"
																				style="#{pc_Xrg00102.propSbtRegister.style}"
																				rendered="#{pc_Xrg00102.propSbtRegister.rendered}">
																			</hx:commandExButton>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</hx:jspPanel>
								<!--↑スクーリング種別タブ↑-->
								<!--↓日程・時限タブ↓-->
								<hx:jspPanel id="panel2"
									rendered="#{pc_Xrg00102.propNteiJspPanel.rendered}">
									<TD valign="top">
										<TABLE border="0" cellpadding="0" cellspacing="0"
											class="tab_body" width="100%">
											<TBODY>
												<TR>
													<TD valign="top" height="370">
														<CENTER>
															<BR>
															<TABLE border="0" cellpadding="0" width="780">
																<TBODY>
																	<TR>
																		<TD align="center">
																			<DIV style="height:120px" class="listScroll"
																				id="listScroll" onscroll="setScrollPosition('scroll',this);">
																				<h:dataTable border="0" cellpadding="0"
																				cellspacing="0" columnClasses="columnClass"
																				headerClass="headerClass" footerClass="footerClass"
																				rowClasses="#{pc_Xrg00102.propNteiList.rowClasses}"
																				styleClass="meisai_scroll" id="htmlNteiList" width="760"
																				value="#{pc_Xrg00102.propNteiList.list}"
																				var="varlist" style="#{pc_Xrg00102.propNteiList.style}">
																					<h:column id="column1">
																						<f:facet name="header">
																							<h:outputText styleClass="outputText" value="開催期"
																								id="lblKaisaikiColumn">
																							</h:outputText>
																						</f:facet>
																						<h:outputText id="htmlKaisaikiColumn"
																							styleClass="outputText"
																							value="#{varlist.propKiName.displayValue}"
																							style="width: 55px">
																						</h:outputText>
																						<f:attribute value="55" name="width" />
																						<f:attribute value="text-align: center" name="style" />
																					</h:column>
																					<h:column id="column2">
																						<f:facet name="header">
																							<h:outputText styleClass="outputText"
																								id="lblGakunaigaiKbnColumn" value="学内外">
																							</h:outputText>
																						</f:facet>
																						<h:outputText id="htmlGakunaigaiKbnColumn"
																							styleClass="outputText"
																							value="#{varlist.naigaiKbnName}"
																							style="width: 55px">
																						</h:outputText>
																						<f:attribute value="55" name="width" />
																						<f:attribute value="text-align: center" name="style" />
																					</h:column>
																					<h:column id="column3">
																						<f:facet name="header">
																							<h:outputText styleClass="outputText"
																								id="lblKaisaitiColumn" value="開催地">
																							</h:outputText>
																						</f:facet>
																						<h:outputText id="htmlKaisaitiColumn"
																							styleClass="outputText"
																							value="#{varlist.propKaisaitiName.displayValue}"
																							title="#{varlist.propKaisaitiName.stringValue}"
																							style="width: 110px">
																						</h:outputText>
																						<f:attribute value="110" name="width" />
																					</h:column>
																					<h:column id="column4">
																						<f:facet name="header">
																							<h:outputText styleClass="outputText"
																								id="lblKaijoColumn" value="会場">
																							</h:outputText>
																						</f:facet>
																						<h:outputText id="htmlKaijoColumn"
																							styleClass="outputText" style="width: 160px"
																							title="#{varlist.propKaijyoName.stringValue}"
																							value="#{varlist.propKaijyoName.displayValue}">
																						</h:outputText>
																						<f:attribute value="160" name="width" />
																					</h:column>
																					<h:column id="column5">
																						<f:facet name="header">
																							<h:outputText styleClass="outputText"
																								id="lblStartDateColumn" value="開始日">
																							</h:outputText>
																						</f:facet>
																						<h:outputText id="htmlStartDateColumn"
																							styleClass="outputText" style="width: 75px"
																							value="#{varlist.staDate}">
																						</h:outputText>
																						<f:attribute value="75" name="width" />
																						<f:attribute value="text-align: center" name="style" />
																					</h:column>
																					<h:column id="column6">
																						<f:facet name="header">
																							<h:outputText styleClass="outputText"
																								id="lblEndDateColumn" value="終了日">
																							</h:outputText>
																						</f:facet>
																						<h:outputText id="htmlEndDateColumn"
																							styleClass="outputText" style="width: 75px"
																							value="#{varlist.endDate}">
																						</h:outputText>
																						<f:attribute value="75" name="width" />
																						<f:attribute value="text-align: center" name="style" />
																					</h:column>
																					<h:column id="column7">
																						<f:facet name="header">
																							<h:outputText styleClass="outputText"
																								id="lblJknPtnColumn" value="時間割パターン">
																							</h:outputText>
																						</f:facet>
																						<h:outputText styleClass="outputText"
																							id="htmlJknPtnColumn"
																							value="#{varlist.propJknPtnName.displayValue}"
																							title="#{varlist.propJknPtnName.stringValue}"
																							style="width: 160px">
																						</h:outputText>
																						<f:attribute value="160" name="width" />
																						<f:attribute value="text-align: left" name="style" />
																					</h:column>
																					<h:column id="column8">
																						<f:facet name="header">
																						</f:facet>
																						<hx:commandExButton type="submit" value="選択"
																							onclick="onCangeData();"
																							styleClass="commandExButton" id="select" tabindex="16"
																							action="#{pc_Xrg00102.doSelectNteiAction}"
																							style="width: 40px">
																						</hx:commandExButton>
																						<f:attribute value="40" name="width" />
																						<f:attribute value="text-align: center" name="style" />
																					</h:column>
																				</h:dataTable>
																			</DIV>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<BR>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="tab_body;clear_border" width="780">
																<TBODY>
																	<TR>
																		<TD valign="top" align="center">
																			<TABLE border="0" cellpadding="0" cellspacing="0"
																				class="table" width="400">
																				<TBODY>
																					<TR>
																						<TH class="v_a" width="120">
																							<h:outputText styleClass="outputText"
																								id="lblKi" value="#{pc_Xrg00102.propKi.labelName}"
																								style="#{pc_Xrg00102.propKi.labelStyle}">
																							</h:outputText>
																						</TH>
																						<TD>
																							<h:selectOneMenu styleClass="selectOneMenu" id="htmlKi"
																								value="#{pc_Xrg00102.propKi.value}"
																								tabindex="17"
																								disabled="#{pc_Xrg00102.propKi.disabled}"
																								style="width:100px">
																								<f:selectItems value="#{pc_Xrg00102.propKi.list}" />
																							</h:selectOneMenu>
																						</TD>
																					</TR>
																					<TR>
																						<TH class="v_b" width="120">
																							<h:outputText styleClass="outputText"
																								id="lblGakunaigaiKbn"
																								value="#{pc_Xrg00102.propGakunaigaiKbn.labelName}" 
																								style="#{pc_Xrg00102.propGakunaigaiKbn.labelStyle}">
																							</h:outputText>
																						</TH>
																						<TD>
																							<h:selectOneRadio
																								disabledClass="selectOneRadio_Disabled"
																								styleClass="selectOneRadio" id="htmlGakunaigaiKbn"
																								onclick="onCangeData();return onCangeGakunaigaiKbn(this, event);"
																								layout="lineDirection"
																								value="#{pc_Xrg00102.propGakunaigaiKbn.value}"
																								tabindex="18"
																								disabled="#{pc_Xrg00102.propGakunaigaiKbn.disabled}">
																								<f:selectItem itemValue="1" itemLabel="学内" />
																								<f:selectItem itemValue="2" itemLabel="学外" />
																							</h:selectOneRadio>
																						</TD>
																					</TR>
																					<TR>
																						<TH class="v_c" width="120">
																							<h:outputText styleClass="outputText" 
																								id="lblKaisaiti" 
																								value="#{pc_Xrg00102.propKaisaiti.labelName}" 
																								style="#{pc_Xrg00102.propKaisaiti.labelStyle}">
																							</h:outputText>
																						</TH>
																						<TD>
																							<h:inputText styleClass="inputText"
																								id="htmlKaisaiti" size="3"
																								value="#{pc_Xrg00102.propKaisaiti.value}"
																								tabindex="19"
																								maxlength="#{pc_Xrg00102.propKaisaiti.max}"
																								style="#{pc_Xrg00102.propKaisaiti.style}"
																								onblur="onCangeData();return doKaisaitiAjax(this, event);">
																							</h:inputText>
																							<hx:commandExButton type="button"
																								styleClass="commandExButton_search" id="searchKaisaiti"
																								style="#{pc_Xrg00102.propSearchKaisaiti.style}" 
																								tabindex="20" 
																								onclick="return openKaisaitiSearchWindow(this, event);">
																							</hx:commandExButton>
																							<hx:commandExButton type="submit" value="選択"
																								styleClass="cmdBtn_dat_s"
																								id="selectKaisaiti"  
																								action="#{pc_Xrg00102.doSelectKaisaitiAction}"
																								tabindex="21">
																							</hx:commandExButton>
																							<hx:commandExButton type="submit" value="解除"
																								styleClass="cmdBtn_etc_s" 
																								id="releasKaisaiti"
																								action="#{pc_Xrg00102.doReleasKaisaitiAction}"
																								tabindex="22">
																							</hx:commandExButton>
																							<h:outputText styleClass="outputText" 
																								id="htmlKaisaitiNm" 
																								value="#{pc_Xrg00102.propKaisaitiNm.stringValue}" 
																								style="#{pc_Xrg00102.propKaisaitiNm.style}">
																							</h:outputText>
																						</TD>
																					</TR>
																					<TR>
																						<TH class="v_d" width="120">
																							<h:outputText styleClass="outputText" 
																								id="lblKaijo" 
																								value="#{pc_Xrg00102.propKaijo.labelName}" 
																								style="#{pc_Xrg00102.propKaijo.labelStyle}">
																							</h:outputText>
																						</TH>
																						<TD>
																							<h:selectOneMenu styleClass="selectOneMenu" id="htmlKaijo"
																								onchange="onCangeData();"
																								value="#{pc_Xrg00102.propKaijo.value}"
																								tabindex="23"
																								style="width:230px">
																								<f:selectItems
																									value="#{pc_Xrg00102.propKaijo.list}" />
																							</h:selectOneMenu>
																						</TD>
																					</TR>
																					<TR>
																						<TH class="v_e" width="120">
																							<h:outputText
																								styleClass="outputText" id="lblKaishibi"
																								style="#{pc_Xrg00102.propKaishibi.labelStyle}"
																								value="#{pc_Xrg00102.propKaishibi.labelName}">
																							</h:outputText>
																						</TH>
																						<TD>
																							<h:inputText id="htmlKaishibi"
																								styleClass="inputText"
																								style="#{pc_Xrg00102.propKaishibi.style};width:105px"
																								value="#{pc_Xrg00102.propKaishibi.dateValue}"
																								disabled="#{pc_Xrg00102.propKaishibi.disabled}"
																								tabindex="24"
																								onkeydown="onCangeData();">
																								<f:convertDateTime />
																								<hx:inputHelperDatePicker />
																								<hx:inputHelperAssist errorClass="inputText_Error"
																								promptCharacter="_" />
																							</h:inputText>
																						</TD>
																					</TR>
																					<TR>
																						<TH class="v_f" width="120">
																							<h:outputText
																								styleClass="outputText" id="lblShuryobi"
																								style="#{pc_Xrg00102.propShuryobi.labelStyle}"
																								value="#{pc_Xrg00102.propShuryobi.labelName}">
																							</h:outputText>
																						</TH>
																						<TD>
																							<h:inputText id="htmlShuryobi"
																								styleClass="inputText"
																								style="#{pc_Xrg00102.propShuryobi.style};width:105px"
																								value="#{pc_Xrg00102.propShuryobi.dateValue}"
																								tabindex="25"
																								disabled="#{pc_Xrg00102.propShuryobi.disabled}"
																								onkeydown="onCangeData();">
																								<f:convertDateTime />
																								<hx:inputHelperDatePicker />
																								<hx:inputHelperAssist errorClass="inputText_Error"
																								promptCharacter="_" />
																							</h:inputText>
																						</TD>
																					</TR>
																				</TBODY>
																			</TABLE>
																		</TD>
																		<TD valign="top" align="right">
																			<TABLE border="0" cellpadding="0" width="370">
																				<TBODY>
																					<TR>
																						<TD colspan="2">
																							<TABLE border="0" cellpadding="0" cellspacing="0"
																								class="table" width="365">
																								<TBODY>
																									<TR>
																										<TH class="v_a" width="115">
																											<h:outputText
																												styleClass="outputText" id="lblJknwrPtn"
																												value="#{pc_Xrg00102.propJknwrPtn.labelName}"
																												style="#{pc_Xrg00102.propJknwrPtn.labelStyle}">
																											</h:outputText>
																										</TH>
																										<TD width="265">
																											<h:selectOneMenu
																												styleClass="selectOneMenu" id="htmlJknwrPtn"
																												onchange="onCangeData();"
																												value="#{pc_Xrg00102.propJknwrPtn.value}"
																												tabindex="26"
																												disabled="#{pc_Xrg00102.propJknwrPtn.disabled}"
																												style="width:180px">
																												<f:selectItems
																													value="#{pc_Xrg00102.propJknwrPtn.list}" />
																											</h:selectOneMenu>
																											<hx:commandExButton
																												type="submit" value="選択"
																												styleClass="cmdBtn_dat_s" id="selectJknwrPtn"
																												disabled="#{pc_Xrg00102.propSelectJknwrPtn.disabled}"
																												action="#{pc_Xrg00102.doSelectJknwrPtnAction}"
																												tabindex="27">
																											</hx:commandExButton>
																											<hx:commandExButton
																												type="submit" value="解除"
																												styleClass="cmdBtn_etc_s" id="unselectJknwrPtn"
																												action="#{pc_Xrg00102.doClearJknwrPtnAction}"
																												disabled="#{pc_Xrg00102.propClearJknwrPtn.disabled}"
																												tabindex="28">
																											</hx:commandExButton>
																										</TD>
																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>
																					<TR>
																						<TD width="290" align="center">
																							<DIV style="height:100px" class="listScroll" style="overflow-x:scroll:hidden;">
																								<h:dataTable border="0" cellpadding="0"
																								cellspacing="0" columnClasses="columnClass"
																								headerClass="headerClass" footerClass="footerClass"
																								rowClasses="#{pc_Xrg00102.propJknwrList.rowClasses}"
																								styleClass="meisai_scroll" id="htmlJknwrList" width="272"
																								value="#{pc_Xrg00102.propJknwrList.list}"
																								var="varlist"
																								style="#{pc_Xrg00102.propJknwrList.style}">
																									<h:column id="jknwrcolumn1">
																										<f:facet name="header">
																											<h:outputText styleClass="outputText" value="時限"
																												id="lblJigenColumn">
																											</h:outputText>
																										</f:facet>
																										<h:outputText id="htmlJigenColumn"
																											styleClass="outputText"
																											value="#{varlist.propJigenColumn.integerValue}"
																											style="width: 55px">
																										</h:outputText>
																										<f:attribute value="55" name="width" />
																										<f:attribute value="text-align: center" name="style" />
																									</h:column>
																									<h:column id="jknwrcolumn2">
																										<f:facet name="header">
																											<h:outputText styleClass="outputText"
																												id="lblStartTimeColumn" value="開始時間">
																											</h:outputText>
																										</f:facet>
																										<h:outputText id="htmlStartTimeColumn"
																											styleClass="outputText"
																											value="#{varlist.propStartTimeColumn.stringValue}"
																											style="width: 90px">
																										</h:outputText>
																										<f:attribute value="90" name="width" />
																										<f:attribute value="text-align: center" name="style" />
																									</h:column>
																									<h:column id="jknwrcolumn03">
																										<f:facet name="header">
																											<h:outputText styleClass="outputText"
																												id="lblEndTimeColumn" value="終了時間">
																											</h:outputText>
																										</f:facet>
																										<h:outputText id="htmlEndTimeColumn"
																											styleClass="outputText"
																											value="#{varlist.propEndTimeColumn.stringValue}"
																											style="width: 90px">
																										</h:outputText>
																										<f:attribute value="90" name="width" />
																										<f:attribute value="text-align: center" name="style" />
																									</h:column>
																								</h:dataTable>
																							</DIV>
																						</TD>
																						<TD>
																						</TD>
																					</TR>
																				</TBODY>
																			</TABLE>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<BR>
															<TABLE border="0" cellspacing="0" class="button_bar" width="780">
																<TBODY>
																	<TR>
																		<TD nowrap>
																			<hx:commandExButton type="submit" value="確定"
																				styleClass="commandExButton_dat" id="nteiRegister"
																				confirm="#{msg.SY_MSG_0002W}" tabindex="29"
																				action="#{pc_Xrg00102.doNteiRegisterAction}"
																				disabled="#{pc_Xrg00102.propNteiRegister.disabled}"
																				style="#{pc_Xrg00102.propNteiRegister.style}"
																				rendered="#{pc_Xrg00102.propNteiRegister.rendered}">
																			</hx:commandExButton>
																			<hx:commandExButton type="submit" value="削除"
																				styleClass="commandExButton_dat" id="delete"
																				confirm="#{msg.SY_MSG_0004W}" tabindex="30"
																				action="#{pc_Xrg00102.doNteiDeleteAction}"
																				disabled="#{pc_Xrg00102.propDelete.disabled}"
																				style="#{pc_Xrg00102.propDelete.style}"
																				rendered="#{pc_Xrg00102.propDelete.rendered}">
																			</hx:commandExButton>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</hx:jspPanel>
								<!--↑日程・時限タブ↑-->
								<!--↓抽選情報タブ↓-->
								<hx:jspPanel id="panel3"
									rendered="#{pc_Xrg00102.propTyusenJspPanel.rendered}">
									<TD valign="top">
										<TABLE border="0" cellpadding="0" cellspacing="0"
											class="tab_body" width="100%">
											<TBODY>
												<TR>
													<TD valign="top" height="370">
														<CENTER>
															<BR>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																class="table" width="90%">
																<TBODY>
																	<TR>
																		<TH class="v_a" width="120">
																			<h:outputText styleClass="outputText" id="lblTyusenKbn"
																				value="#{pc_Xrg00102.propTyusenKbn.labelName}"
																				style="#{pc_Xrg00102.propTyusenKbn.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:selectOneRadio
																				disabledClass="selectOneRadio_Disabled"
																				styleClass="selectOneRadio" id="htmlTyusenKbn"
																				onclick="onCangeData();return onCangeTyusenKbn(this, event);"
																				layout="lineDirection"
																				value="#{pc_Xrg00102.propTyusenKbn.value}"
																				tabindex="31"
																				disabled="#{pc_Xrg00102.propTyusenKbn.disabled}">
																				<f:selectItems
																					value="#{pc_Xrg00102.propTyusenKbn.list}" />
																			</h:selectOneRadio>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="120">
																			<h:outputText styleClass="outputText" id="lblYusenKbn01"
																				value="#{pc_Xrg00102.propYusenKbn01.labelName}"
																				style="#{pc_Xrg00102.propYusenKbn01.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn01"
																				onchange="onCangeData();return onCangeYusenKbn01(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn01.value}"
																				tabindex="32" style="width: 150px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn01.list}" />
																			</h:selectOneMenu> <hx:commandExButton type="submit"
																				value="選択"
																				styleClass="cmdBtn_dat_s" id="selectYusen01"
																				tabindex="33"
																				style="#{pc_Xrg00102.propSelectYusen01.style}"
																				action="#{pc_Xrg00102.doSelectYusen01Action}">
																			</hx:commandExButton><hx:commandExButton type="submit"
																				value="解除"
																				styleClass="cmdBtn_dat_s" id="releaseYusen01"
																				tabindex="34"
																				style="#{pc_Xrg00102.propReleaseYusen01.style}"
																				action="#{pc_Xrg00102.doReleaseYusen01Action}">
																			</hx:commandExButton> <h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn01Detail"
																				onchange="onCangeData();return onCangeYusenKbnDtl01(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn01Detail.value}"
																				tabindex="35" style="width: 300px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn01Detail.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="120">
																			<h:outputText styleClass="outputText" id="lblYusenKbn02"
																				value="#{pc_Xrg00102.propYusenKbn02.labelName}"
																				style="#{pc_Xrg00102.propYusenKbn02.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn02"
																				onchange="onCangeData();return onCangeYusenKbn02(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn02.value}"
																				tabindex="36" style="width: 150px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn02.list}" />
																			</h:selectOneMenu> <hx:commandExButton type="submit"
																				value="選択"
																				styleClass="cmdBtn_dat_s" id="selectYusen02"
																				tabindex="37"
																				style="#{pc_Xrg00102.propSelectYusen02.style}"
																				action="#{pc_Xrg00102.doSelectYusen02Action}">
																			</hx:commandExButton><hx:commandExButton type="submit"
																				value="解除"
																				styleClass="cmdBtn_dat_s" id="releaseYusen02"
																				tabindex="38"
																				style="#{pc_Xrg00102.propReleaseYusen02.style}"
																				action="#{pc_Xrg00102.doReleaseYusen02Action}">
																			</hx:commandExButton> <h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn02Detail"
																				onchange="onCangeData();return onCangeYusenKbnDtl02(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn02Detail.value}"
																				tabindex="39" style="width: 300px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn02Detail.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="120">
																			<h:outputText styleClass="outputText" id="lblYusenKbn03"
																				value="#{pc_Xrg00102.propYusenKbn03.labelName}"
																				style="#{pc_Xrg00102.propYusenKbn03.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn03"
																				onchange="onCangeData();return onCangeYusenKbn03(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn03.value}"
																				tabindex="40" style="width: 150px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn03.list}" />
																			</h:selectOneMenu> <hx:commandExButton type="submit"
																				value="選択"
																				styleClass="cmdBtn_dat_s" id="selectYusen03"
																				tabindex="41"
																				style="#{pc_Xrg00102.propSelectYusen03.style}"
																				action="#{pc_Xrg00102.doSelectYusen03Action}">
																			</hx:commandExButton><hx:commandExButton type="submit"
																				value="解除"
																				styleClass="cmdBtn_dat_s" id="releaseYusen03"
																				tabindex="42"
																				style="#{pc_Xrg00102.propReleaseYusen03.style}"
																				action="#{pc_Xrg00102.doReleaseYusen03Action}">
																			</hx:commandExButton> <h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn03Detail"
																				onchange="onCangeData();return onCangeYusenKbnDtl03(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn03Detail.value}"
																				tabindex="43" style="width: 300px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn03Detail.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="120">
																			<h:outputText styleClass="outputText" id="lblYusenKbn04"
																				value="#{pc_Xrg00102.propYusenKbn04.labelName}"
																				style="#{pc_Xrg00102.propYusenKbn04.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:selectOneMenu
																			styleClass="selectOneMenu" id="htmlYusenKbn04"
																			onchange="onCangeData();return onCangeYusenKbn04(this, event);"
																			value="#{pc_Xrg00102.propYusenKbn04.value}"
																			tabindex="44" style="width: 150px">
																			<f:selectItems
																				value="#{pc_Xrg00102.propYusenKbn04.list}" />
																		</h:selectOneMenu> <hx:commandExButton type="submit"
																			value="選択"
																			styleClass="cmdBtn_dat_s" id="selectYusen04"
																			tabindex="45"
																			style="#{pc_Xrg00102.propSelectYusen04.style}"
																			action="#{pc_Xrg00102.doSelectYusen04Action}">
																		</hx:commandExButton><hx:commandExButton type="submit"
																			value="解除"
																			styleClass="cmdBtn_dat_s" id="releaseYusen04"
																			tabindex="46"
																			style="#{pc_Xrg00102.propReleaseYusen04.style}"
																			action="#{pc_Xrg00102.doReleaseYusen04Action}">
																		</hx:commandExButton> <h:selectOneMenu
																			styleClass="selectOneMenu" id="htmlYusenKbn04Detail"
																			onchange="onCangeData();return onCangeYusenKbnDtl04(this, event);"
																			value="#{pc_Xrg00102.propYusenKbn04Detail.value}"
																			tabindex="47" style="width: 300px">
																			<f:selectItems
																				value="#{pc_Xrg00102.propYusenKbn04Detail.list}" />
																		</h:selectOneMenu></TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="120">
																			<h:outputText styleClass="outputText" id="lblYusenKbn05"
																				value="#{pc_Xrg00102.propYusenKbn05.labelName}"
																				style="#{pc_Xrg00102.propYusenKbn05.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD colspan="3">
																			<h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn05"
																				onchange="onCangeData();return onCangeYusenKbn05(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn05.value}"
																				tabindex="48" style="width: 150px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn05.list}" />
																			</h:selectOneMenu> <hx:commandExButton type="submit"
																				value="選択"
																				styleClass="cmdBtn_dat_s" id="selectYusen05"
																				tabindex="49"
																				style="#{pc_Xrg00102.propSelectYusen05.style}"
																				action="#{pc_Xrg00102.doSelectYusen05Action}">
																			</hx:commandExButton>
																			<hx:commandExButton type="submit" value="解除"
																				styleClass="cmdBtn_dat_s" id="releaseYusen05"
																				tabindex="50"
																				style="#{pc_Xrg00102.propReleaseYusen05.style}"
																				action="#{pc_Xrg00102.doReleaseYusen05Action}">
																			</hx:commandExButton>
																			<h:selectOneMenu
																				styleClass="selectOneMenu" id="htmlYusenKbn05Detail"
																				onchange="onCangeData();return onCangeYusenKbnDtl05(this, event);"
																				value="#{pc_Xrg00102.propYusenKbn05Detail.value}"
																				tabindex="51" style="width: 300px">
																				<f:selectItems
																					value="#{pc_Xrg00102.propYusenKbn05Detail.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<BR>
															<TABLE border="0" cellspacing="0" class="button_bar" width="90%">
																<TBODY>
																	<TR>
																		<TD nowrap>
																			<hx:commandExButton type="submit" value="確定"
																				styleClass="commandExButton_dat" id="tyusenRegister"
																				tabindex="52" onclick="return doPopupMsg('#{msg.SY_MSG_0002W}');"
																				disabled="#{pc_Xrg00102.propTyusenRegister.disabled}"
																				style="#{pc_Xrg00102.propTyusenRegister.style}"
																				action="#{pc_Xrg00102.doTyusenRegisterAction}">
																			</hx:commandExButton>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</hx:jspPanel>
								<!--↑抽選情報タブ↑-->
							</TR>
						</TBODY>
						
					</TABLE>
				</DIV>
			</DIV>
			<h:inputHidden
				value="#{pc_Xrg00102.propExecutable.integerValue}"
				id="executable">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Xrg00102.changeDataFlg}" ></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg00102.propExecutableDeleteFlg.stringValue}" id="htmlExecutableDeleteFlg">
			</h:inputHidden>
			<h:inputHidden id="htmlHidGakunaigaiKbn" value="#{pc_Xrg00102.hidPropGakunaigaiKbn.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidKaisaitiFixFlg" value="#{pc_Xrg00102.hidPropKaisaitiFixFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidTyusenKbn" value="#{pc_Xrg00102.hidPropTyusenKbn.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn01FixFlg" value="#{pc_Xrg00102.hidPropYusenKbn01FixFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn02FixFlg" value="#{pc_Xrg00102.hidPropYusenKbn02FixFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn03FixFlg" value="#{pc_Xrg00102.hidPropYusenKbn03FixFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn04FixFlg" value="#{pc_Xrg00102.hidPropYusenKbn04FixFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn05FixFlg" value="#{pc_Xrg00102.hidPropYusenKbn05FixFlg.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn01" value="#{pc_Xrg00102.hidPropYusenKbn01.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn02" value="#{pc_Xrg00102.hidPropYusenKbn02.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn03" value="#{pc_Xrg00102.hidPropYusenKbn03.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn04" value="#{pc_Xrg00102.hidPropYusenKbn04.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbn05" value="#{pc_Xrg00102.hidPropYusenKbn05.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbnDtl01" value="#{pc_Xrg00102.hidPropYusenKbnDtl01.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbnDtl02" value="#{pc_Xrg00102.hidPropYusenKbnDtl02.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbnDtl03" value="#{pc_Xrg00102.hidPropYusenKbnDtl03.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbnDtl04" value="#{pc_Xrg00102.hidPropYusenKbnDtl04.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidYusenKbnDtl05" value="#{pc_Xrg00102.hidPropYusenKbnDtl05.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidTab" value="#{pc_Xrg00102.hidPropTab.stringValue}" ></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg00102.propNteiList.scrollPosition}"
				id="scroll"></h:inputHidden>			
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->

		</h:form>

	</hx:scriptCollector>
			
	<jsp:include page="../../rev/inc/footer.jsp" />
	
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

