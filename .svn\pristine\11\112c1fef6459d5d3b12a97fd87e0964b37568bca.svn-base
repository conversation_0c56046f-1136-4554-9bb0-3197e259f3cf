<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kez04202.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkName"; 
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").innerHTML;
	args['code2'] = document.getElementById("form1:htmlKmkCd").value;	//科目コード
	args['code3'] = '0';				//桁数
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_2(thisObj, thisEvent) {
	openModalWindow("", "PCog0301", "<%=com.jast.gakuen.rev.co.PCog0301.getWindowOpenOption()%>");
	setTarget("PCog0301");
	return true;

}
function confirmOk() {
     document.getElementById('form1:htmlExecutableSearchHidden').value ="1";
     indirectClick('init');
}
function confirmCancel() {
     document.getElementById('form1:htmlExecutableSearchHidden').value ="0";
	document.getElementById('form1:scroll').value = 0;
}

function func_3(thisObj, thisEvent) {
	func_1(thisObj, thisEvent);
　　changeScrollPosition('scroll', 'listScroll');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_3(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kez04202.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kez04202.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kez04202.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kez04202.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returndisp" action="#{pc_Kez04202.doReturndispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Kez04202.propKaikeiNendo.name}"
										style="#{pc_Kez04202.propKaikeiNendo.labelStyle}"></h:outputText></TH>
									<TD width="100">									
									<h:outputText styleClass="outputText" id="htmlKaikeiNendo"
										value="#{pc_Kez04202.propKaikeiNendo.dateValue}"
										style="#{pc_Kez04202.propKaikeiNendo.style}">
										<f:convertDateTime pattern="yyyy" />
									</h:outputText></TD>
									<TH width="100" class="v_b"><h:outputText
										styleClass="outputText" id="lblKatudouKbn"
										value="#{pc_Kez04202.propKatudouKbn.labelName}"
										style="#{pc_Kez04202.propKihonkinKmkName.labelStyle}"></h:outputText></TH>
									<TD width="100"><h:outputText styleClass="outputText"
										id="htmlKatudouKbn"
										value="#{pc_Kez04202.propKatudouKbn.stringValue}"
										style="#{pc_Kez04202.propKihonkinKmkName.style}"></h:outputText></TD>
									<TH class="v_a" width="100"><h:outputText
										styleClass="outputText" id="lblShushi" value="#{pc_Kez04202.propShushi.name}"></h:outputText></TH>
									<TD width="100"><h:outputText styleClass="outputText"
										id="htmlShushi" value="#{pc_Kez04202.propShushi.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH>
									<h:outputText styleClass="outputText" id="lblHyoujiKamokuCd" value="#{pc_Kez04202.propHyoujiKamokuCd.labelName}"></h:outputText></TH>
									<TD colspan="5">
									<h:outputText styleClass="outputText" id="htmlHyoujiKamokuCd" value="#{pc_Kez04202.propHyoujiKamokuCd.stringValue}"></h:outputText></TD>
								</TR>	
								<TR>
									<TH>
									<h:outputText styleClass="outputText" id="lblHyoujiKoumokuNm" value="#{pc_Kez04202.propHyoujiKoumokuNm.labelName}"></h:outputText></TH>
									<TD colspan="5">
									<h:outputText styleClass="outputText" id="htmlHyoujiKoumokuNm" value="#{pc_Kez04202.propHyoujiKoumokuNm.stringValue}"></h:outputText></TD>
								</TR>								
								<TR>
									<TH>
									<h:outputText styleClass="outputText" id="lblHyoujiJun" value="表示順"></h:outputText></TH>
									<TD colspan="5">
										<TABLE width="450" border="0" cellpadding="0" cellspacing="0" class="clear_border">
											<TBODY>
												<TR>
													<TD width="240">
														<h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlSortType"
															value="#{pc_Kez04202.propSortType.stringValue}"
															disabled="#{pc_Kez04202.propSortType.disabled}"
															readonly="#{pc_Kez04202.propSortType.disabled}"
															rendered="#{pc_Kez04202.propSortType.rendered}"
															style="width:100%;">
															<f:selectItem itemValue="0" itemLabel="科目コード順" />
															<f:selectItem itemValue="1" itemLabel="管理科目コード順" />
														</h:selectOneRadio>
													</TD>
													<TD width="210">
														<hx:commandExButton
															type="submit"
															id="sort"
															styleClass="commandExButton"
															value="再表示"
															action="#{pc_Kez04202.doSortAction}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
							<TBODY>
								<TR>
									<TD width="50%" align="right" colspan="2"><h:outputText
										styleClass="outputText" id="htmlListCount" value="#{pc_Kez04202.propTableList1.listCount}">

									</h:outputText><h:outputText styleClass="outputText" id="lblKen" value="件"></h:outputText></TD></TR>
								<TR>
									<TD colspan="2">
									<DIV class="listScroll" style="height:330px;width=800px" id="listScroll" align="center" onscroll="setScrollPosition('scroll',this);"><h:dataTable
										border="0" cellpadding="2" cellspacing="0"
										columnClasses="columnClass1,columnClass1,,,"
										headerClass="headerClass" footerClass="footerClass"
										rowClasses="#{pc_Kez04202.propTableList1.rowClasses}"
										styleClass="meisai_scroll" id="htmlTableList1" var="varlist"
										width="0%" value="#{pc_Kez04202.propTableList1.list}">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="lblColKmkCd" styleClass="outputText"
													value="科目コード"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKmkCd"
												value="#{varlist.colKmkCd}"></h:outputText>
											<f:attribute value="100" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText id="lblColKankiKmkCd" styleClass="outputText"
													value="管理科目コード"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKanriKmkCd"
												value="#{varlist.colKanriKmkCd}"></h:outputText>
											<f:attribute value="130" name="width" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText id="lblColKmkName" styleClass="outputText"
													value="科目名称"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKmkName"
												value="#{varlist.colKmkName.displayValue}"></h:outputText>
											<f:attribute value="540" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="選択"
												styleClass="commandExButton" id="select"
												action="#{pc_Kez04202.doSelectAction}"
												disabled="#{pc_Kez04202.propSelect.disabled}"></hx:commandExButton>
											<f:attribute value="39" name="width" />
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" style="" class="table">
							<TBODY>
								<TR>
									<TH class="v_c" width="130"><h:outputText id="lblKmkCd"
										styleClass="outputText"
										value="#{pc_Kez04202.propKmkCd.labelName}"
										style="#{pc_Kez04202.propKmkCd.labelStyle}"></h:outputText></TH>
									<TD colspan="3" width="670" nowrap>
										<DIV style="width:670px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlKmkCd" size="12"
										disabled="#{pc_Kez04202.propKmkCd.disabled}"
										maxlength="#{pc_Kez04202.propKmkCd.maxLength}"
										readonly="#{pc_Kez04202.propKmkCd.readonly}"
										style="#{pc_Kez04202.propKmkCd.style}"
										value="#{pc_Kez04202.propKmkCd.stringValue}"
										onblur="return func_1(this, event);"
										rendered="#{pc_Kez04202.propKmkCd.rendered}"></h:inputText><hx:commandExButton
												type="submit" styleClass="commandExButton_search"
												id="searchKmkCd" onclick="return func_2(this, event);" 
												action="#{pc_Kez04202.doSearchKmkCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlKmkName"
												style="#{pc_Kez04202.propKmkName.style}"
												value="#{pc_Kez04202.propKmkName.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="登録"
										styleClass="commandExButton_dat" id="register"
										action="#{pc_Kez04202.doRegisterAction}"
										confirm="#{msg.SY_MSG_0002W}"
										rendered="#{pc_Kez04202.propRegister.rendered}"
										disabled="#{pc_Kez04202.propRegister.disabled}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="削除"
										styleClass="commandExButton_dat" id="delete"
										action="#{pc_Kez04202.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"
										rendered="#{pc_Kez04202.propDelete.rendered}"
										disabled="#{pc_Kez04202.propDelete.disabled}"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><h:inputHidden id="htmlKihonkinKmkCdHidden"
							value="#{pc_Kez04202.propKihonkinKmkCdHidden.stringValue}">
						</h:inputHidden></TD>
					</TR>
					<TR>
						<TD><h:inputHidden id="htmlKatudouKbnCdHidden"
							value="#{pc_Kez04202.propKatudouKbnCdHidden.stringValue}">
						</h:inputHidden></TD>
					</TR>
					<TR>
						<TD><h:inputHidden id="htmlShushiCdHidden"
							value="#{pc_Kez04202.propShushiCdHidden.stringValue}">
						</h:inputHidden></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Kez04202.propTableList1.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

