<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssd01801T03.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssd01801T03.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css" />

<SCRIPT type="text/javascript">
function confirmOk() {
    // 確認メッセージでＯＫ押下時、しきい値フラグオンして再検索
	document.getElementById('form1:htmlExecutableSearch').value = "1";	
	indirectClick('search');	
}			
function confirmCancel() {
    // 確認メッセージでキャンセル押下時、しきい値フラグオフ
	document.getElementById('form1:htmlExecutableSearch').value = "0";	
}
function confirmCancel() {
	alert('実行を中断しました。');
}
function openGakuseiSearchWindow() {
	// 学生検索画面
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGaksekiCd";
	openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return false;
}

function doGakuseiAjax(thisObj, thisEvent) {
// 学生名称を取得する
	var servlet = "rev/ss/VSsaCsotGakAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;
	args['code2'] = "";
	args['code3'] = "";

	var target = "form1:htmlGakName";

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet,target,args);
}

function checkOnSelect(thisObj, thisEvent) {
check('htmlSelectGakList','htmlSelListCheck');
}
function checkOffSelect(thisObj, thisEvent) {
uncheck('htmlSelectGakList','htmlSelListCheck');
}
function checkOnSearch(thisObj, thisEvent) {
check('htmlSearchGakList','htmlSeaListCheck');
}
function checkOffSearch(thisObj, thisEvent) {
uncheck('htmlSearchGakList','htmlSeaListCheck');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="doGakuseiAjax(document.getElementById('form1:htmlGaksekiCd'), event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssd01801T03.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<DIV style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssd01801T03.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssd01801T03.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssd01801T03.screenName}"></h:outputText></DIV>
			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>
			<!--↓content↓-->
			<DIV class="head_button_area">　</DIV>
			<DIV id="content">
			<DIV class="column">
			<TABLE border="0" class="table" width="840" cellspacing="0"
				cellpadding="0">
				<TBODY>
					<TR>
						<TH class="v_a" width="180"><h:outputText
							styleClass="outputText" id="lblKyujinNendo"
							value="#{pc_Ssd01801T03.ssd01801.propKyujinNendo.labelName}"
							style="#{pc_Ssd01801T03.ssd01801.propKyujinNendo.labelStyle}"></h:outputText></TH>
						<TD width="150"><h:inputText styleClass="inputText"
							id="htmlKyujinNendo"
							value="#{pc_Ssd01801T03.ssd01801.propKyujinNendo.stringValue}"
							style="#{pc_Ssd01801T03.ssd01801.propKyujinNendo.style}" size="5"
							maxlength="#{pc_Ssd01801T03.ssd01801.propKyujinNendo.maxLength}"></h:inputText><h:outputText
							styleClass="outputText" id="lblNen" value="年度"></h:outputText></TD>
						<TH class="v_a" width="100"><h:outputText
							styleClass="outputText" id="lblBnmn" 
							value="#{pc_Ssd01801T03.ssd01801.propBnmn.labelName}"
							style="#{pc_Ssd01801T03.ssd01801.propBnmn.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlBnmn"
							disabled="#{pc_Ssd01801T03.ssd01801.propBnmn.disabled}"
							style="#{pc_Ssd01801T03.ssd01801.propBnmn.style}"
							value="#{pc_Ssd01801T03.ssd01801.propBnmn.value}">
							<f:selectItems value="#{pc_Ssd01801T03.ssd01801.propBnmn.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH class="v_d" colspan="1"><h:outputText
							styleClass="outputText" id="lblOutCond"
							value="#{pc_Ssd01801T03.ssd01801.propOutCond.labelName}"
							style="#{pc_Ssd01801T03.ssd01801.propOutCond.labelStyle}"></h:outputText></TH>
						<TD colspan="3"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlNaiteiSyuSyok"
							value="#{pc_Ssd01801T03.ssd01801.propNaiteiSyusyok.checked}"
							style="#{pc_Ssd01801T03.ssd01801.propNaiteiSyusyok.style}"
							disabled="#{pc_Ssd01801T03.ssd01801.propNaiteiSyusyok.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propNaiteiSyusyok.readonly}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="lblOutputComment1"
							value="内定済み・就職先として決定  "></h:outputText> <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlNaiteiJitai"
							value="#{pc_Ssd01801T03.ssd01801.propNaiteiJitai.checked}"
							style="#{pc_Ssd01801T03.ssd01801.propNaiteiJitai.style}"
							disabled="#{pc_Ssd01801T03.ssd01801.propNaiteiJitai.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propNaiteiJitai.readonly}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="lblOutputComment2" value="内定辞退  "></h:outputText> <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlJitaiIgai"
							value="#{pc_Ssd01801T03.ssd01801.propJitaiIgai.checked}"
							style="#{pc_Ssd01801T03.ssd01801.propJitaiIgai.style}"
							disabled="#{pc_Ssd01801T03.ssd01801.propJitaiIgai.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propJitaiIgai.readonly}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="lblOutputComment3"
							value="内定済み・就職先として決定・内定辞退以外"></h:outputText> <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlRyuOnly"
							value="#{pc_Ssd01801T03.ssd01801.propRyuOnly.checked}"
							style="#{pc_Ssd01801T03.ssd01801.propRyuOnly.style}"
							disabled="#{pc_Ssd01801T03.ssd01801.propRyuOnly.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propRyuOnly.readonly}"></h:selectBooleanCheckbox>
							<h:outputText styleClass="outputText" id="lblOutputComment4"
							value="留年生のみ"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" class="table" width="840" cellspacing="0"
				cellpadding="0" style="margin-top:7px;">
				<TBODY>
					<TR>
						<TH width="78" colspan="1" class="group_label_top"><h:outputText
							styleClass="outputText" id="lblSsenNin" value="宛名ラベル"></h:outputText></TH>
						<TD class="group_item" width="100"></TD>
						<TD colspan="3"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioRendo"
							layout="lineDirection"
							style="#{pc_Ssd01801T03.ssd01801.propRadioRendo.style}"
							value="#{pc_Ssd01801T03.ssd01801.propRadioRendo.value}"
							disabled="#{pc_Ssd01801T03.ssd01801.propRadioRendo.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propRadioRendo.readonly}">
							<f:selectItem itemValue="1" itemLabel="連動出力する" />
							<f:selectItem itemValue="0" itemLabel="連動出力しない" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH class="group_label"></TH>
						<TH nowrap class="v_a"><h:outputText
							styleClass="outputText" id="lblYubin" value="〒表示"></h:outputText></TH>
						<TD width="250"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioYubin"
							layout="lineDirection"
							style="#{pc_Ssd01801T03.ssd01801.propRadioYubin.style}"
							value="#{pc_Ssd01801T03.ssd01801.propRadioYubin.value}"
							disabled="#{pc_Ssd01801T03.ssd01801.propRadioYubin.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propRadioYubin.readonly}">
							<f:selectItem itemValue="1" itemLabel="あり" />
							<f:selectItem itemValue="0" itemLabel="なし" />
						</h:selectOneRadio></TD>
						<TH nowrap class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblLayout" value="レイアウト"></h:outputText></TH>
						<TD><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioLayout"
							layout="lineDirection"
							style="#{pc_Ssd01801T03.ssd01801.propRadioLayout.style}"
							value="#{pc_Ssd01801T03.ssd01801.propRadioLayout.value}"
							disabled="#{pc_Ssd01801T03.ssd01801.propRadioLayout.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propRadioLayout.readonly}">
							<f:selectItem itemValue="1" itemLabel="A4縦" />
							<f:selectItem itemValue="2" itemLabel="A3横" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH class="group_label"></TH>
						<TH nowrap class="v_a"><h:outputText styleClass="outputText"
							id="lblBarcodeSitei" value="バーコード出力"></h:outputText></TH>
						<TD width="250"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlBarcodeSitei"
							style="#{pc_Ssd01801T03.ssd01801.propBarcodeSitei.style}"
							value="#{pc_Ssd01801T03.ssd01801.propBarcodeSitei.stringValue}">
							<f:selectItem itemValue="0" itemLabel="出力する" />
							<f:selectItem itemValue="1" itemLabel="出力しない" />
						</h:selectOneRadio></TD>
						<TH nowrap class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblKaishiIchi" value="#{pc_Ssd01801T03.ssd01801.propKaishiIchi.labelName}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText" id="htmlKaishiIchi"
							value="#{pc_Ssd01801T03.ssd01801.propKaishiIchi.integerValue}"
							style="#{pc_Ssd01801T03.ssd01801.propKaishiIchi.style}" size="2">
							<f:convertNumber type="number" pattern="##;##" />
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="group_label"></TH>
						<TH nowrap class="v_b"><h:outputText
							styleClass="outputText" id="lblPrinter" 
							value="#{pc_Ssd01801T03.ssd01801.propPrinter.labelName}"
							style="#{pc_Ssd01801T03.ssd01801.propPrinter.labelStyle}"></h:outputText></TH>
						<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlPrinter"
							disabled="#{pc_Ssd01801T03.ssd01801.propPrinter.disabled}"
							style="#{pc_Ssd01801T03.ssd01801.propPrinter.style}"
							value="#{pc_Ssd01801T03.ssd01801.propPrinter.value}">
							<f:selectItems value="#{pc_Ssd01801T03.ssd01801.propPrinter.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH class="group_label_bottom"></TH>
						<TH nowrap class="v_c"><h:outputText
							styleClass="outputText" id="lblBiko" value="備考欄(全20)"
							style="#{pc_Ssd01801T03.ssd01801.propBiko.labelStyle}"></h:outputText></TH>
						<TD colspan="3"><h:inputText styleClass="inputText" id="htmlBiko"
							value="#{pc_Ssd01801T03.ssd01801.propBiko.stringValue}"
							style="#{pc_Ssd01801T03.ssd01801.propBiko.style}"
							disabled="#{pc_Ssd01801T03.ssd01801.propBiko.disabled}"
							readonly="#{pc_Ssd01801T03.ssd01801.propBiko.readonly}"
							maxlength="#{pc_Ssd01801T03.ssd01801.propBiko.maxLength}"
							size="45">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH nowrap class="v_c" colspan="2"><h:outputText
							styleClass="outputText" id="lblSaiyoTanto" value="採用担当者氏名"></h:outputText></TH>
						<TD colspan="3"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioSaiyoTanto"
							layout="lineDirection"
							value="#{pc_Ssd01801T03.ssd01801.propRadioSaiyoTanto.value}"
							style="#{pc_Ssd01801T03.ssd01801.propRadioSaiyoTanto.style}">
							<f:selectItem itemValue="1" itemLabel="担当者名称出力" />
							<f:selectItem itemValue="2" itemLabel="固定名称出力" />
						</h:selectOneRadio></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="840" cellpadding="0" cellspacing="0"
				style="margin-top:10px;">
				<TBODY>
					<TR>
						<TD width="800" align="left"><hx:commandExButton type="submit"
							value="一括指定" styleClass="tab_head_off" id="btnSsd01801T01"
							action="#{pc_Ssd01801T03.doSsd01801T01Action}"></hx:commandExButton><hx:commandExButton
							value="企業指定" styleClass="tab_head_off" id="btnSsd01801T02" 
							action="#{pc_Ssd01801T03.doSsd01801T02Action}"></hx:commandExButton><hx:commandExButton 
							value="学生指定" styleClass="tab_head_on" id="btnSsd01801T03"></hx:commandExButton></TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="840" border="0" cellspacing="0" cellpadding="0" class="tab_body">
							<TBODY>
								<TR>
									<TD>
									<DIV class="" style="height:310px">
										<TABLE width="780" border="0" cellspacing="0" cellpadding="0" style="margin-top:10px">
											<TBODY>
												<TR>
													<TD width="740px">
														<TABLE width="720" border="0" cellspacing="0" cellpadding="0" align="center" class="table">
															<TBODY>
																<TR>
																	<TH class="v_a" width="150"><h:outputText
																	styleClass="outputText" id="lblGaksekiCd"
																	value="#{pc_Ssd01801T03.propGaksekiCd.labelName}"
																	style="#{pc_Ssd01801T03.propGaksekiCd.labelStyle}"></h:outputText></TH>
																	<TD width="220" style="border-right-style: none;"><h:inputText styleClass="inputText"
																	id="htmlGaksekiCd"
																	value="#{pc_Ssd01801T03.propGaksekiCd.stringValue}"
																	size="18"
																	disabled="#{pc_Ssd01801T03.propGaksekiCd.disabled}"
																	maxlength="#{pc_Ssd01801T03.propGaksekiCd.maxLength}"
																	readonly="#{pc_Ssd01801T03.propGaksekiCd.readonly}"
																	style="#{pc_Ssd01801T03.propGaksekiCd.style}"
																	onblur="return doGakuseiAjax(this, event);">
																</h:inputText>
																	<hx:commandExButton
																		type="submit" styleClass="commandExButton_search"
																		id="searchGak" style="margin-left:5px;"
																		disabled="#{pc_Ssd01801T03.propSearchGak.disabled}"
																		onclick="return openGakuseiSearchWindow();">
																		</hx:commandExButton><hx:commandExButton
																			type="submit" value="選択" styleClass="commandExButton"
																			id="selectGak" style="margin-left: 5px;"
																			action="#{pc_Ssd01801T03.doSelectGakAction}"
																			disabled="#{pc_Ssd01801T03.propSelectGak.disabled}"></hx:commandExButton>
																	</TD>
																	<TD style="border-left-style:none;"><DIV style="padding: 0 5px;"><h:outputText
																	styleClass="outputText" id="htmlGakName"
																	value="#{pc_Ssd01801T03.propGakName.stringValue}"
																	style="#{pc_Ssd01801T03.propGakName.style}"></h:outputText></DIV></TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
													<TD>
														<TABLE width="100%" border="0" cellspacing="0" cellpadding="0">
															<TBODY>
																<TR>
																	<TD width="100" >
																	</TD>
																</TR>

															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										<TABLE width="780" border="0" cellspacing="0" cellpadding="0">
											<TBODY>
												<TR>
													<TD width="740px">
														<TABLE width="700" border="0" cellspacing="0" cellpadding="0" align="center">
															<TBODY>
																<TR>
																	<TD style="text-align:right;" width="700">
					
																	<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
																		<TBODY>
																			<TR>
																				<TD style="text-align:right;"><h:outputText
																					styleClass="outputText" id="htmlSeaListGakCnt"
																					value="#{pc_Ssd01801T03.propSearchGakList.listCount}"></h:outputText>
																					<h:outputText styleClass="outputText" id="lblSeaKensuu"
																						value="件"></h:outputText>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																</TR>
																<TR>
																	<TD>
																	<DIV id="listScroll" class="listScroll" onscroll="setScrollPosition('scroll',this);"
																		style="height:90px;"><h:dataTable border="0"
																		cellpadding="3" cellspacing="0" headerClass="headerClass"
																		footerClass="footerClass"
																		rowClasses="#{pc_Ssd01801T03.propSearchGakList.rowClasses}"
																		styleClass="meisai_scroll" id="htmlSearchGakList"
																		width="700"
																		value="#{pc_Ssd01801T03.propSearchGakList.list}"
																		var="varlist"
																		first="#{pc_Ssd01801T03.propSearchGakList.first}"
																		rows="#{pc_Ssd01801T03.propSearchGakList.rows}"
																		columnClasses="columnCenter,columnCenter,,,columnCenter,columnLeft">
																		<h:column id="SeaColumn5">
																			<f:facet name="header"></f:facet>
																			<f:attribute value="15" name="width" />
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox" id="htmlSeaListCheck"
																				value="#{varlist.selected}"
																				rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
																		</h:column>
																		<h:column id="SeaColumn1">
																			<f:facet name="header">
																				<h:outputText id="lblSeaListGaksekiCd"
																					styleClass="outputText" value="学籍番号"></h:outputText>
																			</f:facet>
																			<f:attribute value="70" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																			<h:outputText styleClass="outputText"
																				id="htmlListSeaGaksekiCd" value="#{varlist.gakusekiCd}"></h:outputText>
																		</h:column>
																		<h:column id="column2">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="学生氏名"
																					id="lblSeaListGakName">
																				</h:outputText>
																			</f:facet>
																			<f:attribute value="155" name="width" />
																			<h:outputText styleClass="outputText"
																				id="htmlSeaListGakName"
																				value="#{varlist.gakuseiName.displayValue}"
																				rendered="#{varlist.gakuseiName.rendered}"
																				title="#{varlist.gakuseiName.value}">
																			</h:outputText>
																		</h:column>
																		<h:column id="column3">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="留年"
																					id="lblSeaListRyunen"></h:outputText>
																			</f:facet>
																			<f:attribute value="40" name="width" />
																			<f:attribute value="text-align:center" name="style" />
																			<h:outputText styleClass="outputText"
																				id="htmlSeaListRyunen"
																				value="#{varlist.ryunen.displayValue}"
																				title="#{varlist.ryunen.value}"
																				rendered="#{varlist.ryunen.rendered}"></h:outputText>
																		</h:column>
																		<h:column id="SeaColumn6">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="求職状況"
																					id="lblSeaListkyusyok"></h:outputText>
																			</f:facet>
																			<h:outputText styleClass="outputText"
																				id="htmlSeaListKyusyok"
																				value="#{varlist.kyusyok.displayValue}"
																				rendered="#{varlist.kyusyok.rendered}"></h:outputText>
																			<f:attribute value="135" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																		</h:column>
																		<h:column id="SeaColumn4">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="企業コード"
																					id="lblSeaListKgyCd"></h:outputText>
																			</f:facet>
																			<f:attribute value="70" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																			<h:outputText styleClass="outputText"
																				id="htmlSeaListKgyCd" value="#{varlist.kgyCd}"></h:outputText>
																		</h:column>
																		<h:column id="SeaColumn7">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="企業名称"
																					id="lblSeaListKgyName"></h:outputText>
																			</f:facet>
																			<h:outputText styleClass="outputText"
																				id="htmlSeaListKgyName"
																				value="#{varlist.kgyName.displayValue}"
																				rendered="#{varlist.kgyName.rendered}"></h:outputText>
																			<f:attribute value="215" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																		</h:column>
																	</h:dataTable></DIV>
																	<TABLE width="100%" border="0" cellpadding="0"
																		cellspacing="0">
																		<TBODY>
																			<TR>
																				<TD align="left"><hx:jspPanel id="SeajspPanel1">
																					<hx:commandExButton type="submit" value="全選択"
																						styleClass="check" id="check1"
																						onclick="return checkOnSearch(this, event);"></hx:commandExButton>
																					<hx:commandExButton type="submit" value="全解除"
																						styleClass="uncheck" id="uncheck1"
																						onclick="return checkOffSearch(this, event);"></hx:commandExButton>
																				</hx:jspPanel></TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
														<TABLE width="700" border="0" cellspacing="0" cellpadding="0" align="center" style="margin-bottom:10px">
															<TBODY>
																<TR>
																	<TD style="text-align:right;" width="700">
					
																	<TABLE width="100%" border="0" cellpadding="0"
																		cellspacing="0">
																		<TBODY>
																			<TR>
																				<TD style="text-align:right;"><h:outputText
																					styleClass="outputText" id="htmlSelListGakCnt"
																					value="#{pc_Ssd01801T03.propSelectGakList.listCount}"></h:outputText>
																					<h:outputText styleClass="outputText" id="lblSelKensuu"
																						value="件"></h:outputText>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																</TR>
																<TR>
																	<TD>
																	<DIV id="listScroll2" class="listScroll" onscroll="setScrollPosition('scroll2',this);"
																		style="height:90px;"><h:dataTable border="0"
																		cellpadding="3" cellspacing="0" headerClass="headerClass"
																		footerClass="footerClass"
																		rowClasses="#{pc_Ssd01801T03.propSelectGakList.rowClasses}"
																		styleClass="meisai_scroll" id="htmlSelectGakList"
																		width="700"
																		value="#{pc_Ssd01801T03.propSelectGakList.list}"
																		var="varlist"
																		first="#{pc_Ssd01801T03.propSelectGakList.first}"
																		rows="#{pc_Ssd01801T03.propSelectGakList.rows}"
																		columnClasses="columnCenter,columnCenter,,,columnCenter,columnLeft">
																		<h:column id="selColumn5">
																			<f:facet name="header"></f:facet>
																			<f:attribute value="15" name="width" />
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox" id="htmlSelListCheck"
																				value="#{varlist.selected}"
																				rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
																		</h:column>
																		<h:column id="SelColumn1">
																			<f:facet name="header">
																				<h:outputText id="lblSelListGaksekiCd"
																					styleClass="outputText" value="学籍番号"></h:outputText>
																			</f:facet>
																			<f:attribute value="70" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																			<h:outputText styleClass="outputText"
																				id="htmlSelListGaksekiCd" value="#{varlist.gakusekiCd}"></h:outputText>
																		</h:column>
																		<h:column id="SelColumn2">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="学生氏名"
																					id="lblSelListGakName">
																				</h:outputText>
																			</f:facet>
																			<f:attribute value="155" name="width" />
																			<h:outputText styleClass="outputText"
																				id="htmlSelListGakName"
																				value="#{varlist.gakuseiName.displayValue}"
																				rendered="#{varlist.gakuseiName.rendered}"
																				title="#{varlist.gakuseiName.value}">
																			</h:outputText>
																		</h:column>
																		<h:column id="SelColumn3">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="留年"
																					id="lblSelListRyunen"></h:outputText>
																			</f:facet>
																			<f:attribute value="40" name="width" />
																			<f:attribute value="text-align:center" name="style" />
																			<h:outputText styleClass="outputText"
																				id="htmlSelListRyunen"
																				value="#{varlist.ryunen.displayValue}"
																				title="#{varlist.ryunen.value}"
																				rendered="#{varlist.ryunen.rendered}"></h:outputText>
																		</h:column>
																		<h:column id="SelColumn6">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="求職状況"
																					id="lblSelListkyusyok"></h:outputText>
																			</f:facet>
																			<h:outputText styleClass="outputText"
																				id="htmlSelListKyusyok"
																				value="#{varlist.kyusyok.displayValue}"
																				rendered="#{varlist.kyusyok.rendered}"></h:outputText>
																			<f:attribute value="135" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																		</h:column>
																		<h:column id="SelColumn4">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="企業コード"
																					id="lblSelListKgyCd"></h:outputText>
																			</f:facet>
																			<f:attribute value="70" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																			<h:outputText styleClass="outputText"
																				id="htmlSelListKgyCd" value="#{varlist.kgyCd}"></h:outputText>
					
																		</h:column>
																		<h:column id="SelColumn7">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText" value="企業名称"
																					id="lblSelListKgyName"></h:outputText>
																			</f:facet>
																			<h:outputText styleClass="outputText"
																				id="htmlSelListKgyName"
																				value="#{varlist.kgyName.displayValue}"
																				rendered="#{varlist.kgyName.rendered}"></h:outputText>
																			<f:attribute value="215" name="width" />
																			<f:attribute value="text-align:left; padding-left:5px;" name="style" />
																		</h:column>
																	</h:dataTable></DIV>
																		<TABLE width="100%" border="0" cellpadding="0"
																			cellspacing="0">
																			<TBODY>
																				<TR>
																					<TD align="left"><hx:jspPanel id="SeljspPanel1">
																					<hx:commandExButton type="submit" value="全選択"
																						styleClass="check" id="check2"
																						onclick="return checkOnSelect(this, event);"></hx:commandExButton>
																					<hx:commandExButton type="submit" value="全解除"
																						styleClass="uncheck" id="uncheck2"
																						onclick="return checkOffSelect(this, event);"></hx:commandExButton>
																					</hx:jspPanel></TD>
																				</TR>
																			</TBODY>
																		</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
													<TD>
														<TABLE width="100%" border="0" cellspacing="0" cellpadding="0">
															<TBODY>
																<TR height="100">
																	<TD width="100" ><hx:commandExButton type="submit" value="追加"
																		styleClass="commandExButton" id="btnAddGak"
																		style="width:40px;"
																		action="#{pc_Ssd01801T03.doBtnAddAction}"></hx:commandExButton>
																	</TD>
																</TR>
																<TR height="100">
																	<TD width="100"><hx:commandExButton type="submit" value="クリア"
																		styleClass="commandExButton" id="btnClear"
																		style="width:40px;"
																		action="#{pc_Ssd01801T03.doBtnClearAction}"></hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="800" class="button_bar">
								<TBODY>
									<TR>
										<TD><hx:commandExButton type="submit" value="PDF作成"
											styleClass="commandExButton_out" id="pdfout"
											action="#{pc_Ssd01801T03.doPdfoutAction}"
											confirm="#{msg.SY_MSG_0019W}"
											></hx:commandExButton><hx:commandExButton
											type="submit" value="CSV作成"
											styleClass="commandExButton_out" id="csvout"
											action="#{pc_Ssd01801T03.doCsvoutAction}"
											confirm="#{msg.SY_MSG_0020W}"
											></hx:commandExButton><hx:commandExButton
											type="submit" value="出力項目指定"
											styleClass="commandExButton_out" id="setoutput"
											onclick="return setOutput(this, event);"
											action="#{pc_Ssd01801T03.doSetoutputAction}"
											></hx:commandExButton><hx:commandExButton type="submit" value="印刷"
											styleClass="commandExButton_out" id="print"
											action="#{pc_Ssd01801T03.doPrintAction}"
											confirm="#{msg.SY_MSG_0022W}"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlRetField"></h:inputHidden>
			<h:inputHidden value="#{pc_Ssd01801T03.propExecutableSearch.integerValue}" id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="1" id="htmlgetNameFlg"></h:inputHidden>
			<h:inputHidden value="#{pc_Ssd01801T03.propSearchGakList.scrollPosition}" id='scroll'></h:inputHidden>
			<h:inputHidden value="#{pc_Ssd01801T03.propSelectGakList.scrollPosition}" id='scroll2'></h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
	<SCRIPT LANGUAGE="JavaScript">
		window.attachEvent('onload', endload);
		function endload() {
			changeScrollPosition('scroll','listScroll');
			changeScrollPosition('scroll2','listScroll2');
		}
	</SCRIPT>
</HTML>
