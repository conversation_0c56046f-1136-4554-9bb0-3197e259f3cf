<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KMB_SMJK" name="卒業見込条件" prod_id="KM" description="『卒業見込判定』で使用する判定条件を保持します。
『卒業見込条件設定』にて作成されます。">
<STATMENT><![CDATA[
KMB_SMJK
]]></STATMENT>
<COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="卒業見込判定で判定対象となる学生のみなし入学年度が設定されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="卒業見込判定で判定対象となる学生の入学学期が設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="卒業見込判定で判定対象となる学生のカリキュラム学科組織コードが設定されます。"/><COLUMN id="SOTGYO_MKM_JOKEN_CD" name="卒業見込条件コード" type="string" length="5" lengthDP="0" byteLength="5" description="卒業見込条件を識別する任意のコードが設定されます。"/><COLUMN id="OR_GROUP_NO" name="ＯＲグループＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="ＯＲ条件となるグループを表します。
この番号が同じ要素はＡＮＤ条件となります。この番号が異なる要素はＯＲ条件となります。"/><COLUMN id="TITLE" name="タイトル" type="string" length="50" lengthDP="0" byteLength="150" description="この条件がエラーとなった時に表示されるタイトルが設定されます。"/><COLUMN id="MSG" name="メッセージ" type="string" length="100" lengthDP="0" byteLength="300" description="この条件がエラーとなった時に表示されるメッセージが設定されます。"/><COLUMN id="HANTEI_TAISYO_GAKUNEN" name="判定対象学年" type="number" length="1" lengthDP="0" byteLength="0" description="判定対象となる学年が設定されます。"/><COLUMN id="HANTEI_TAISYO_SEMESTER" name="判定対象セメスタ" type="number" length="2" lengthDP="0" byteLength="0" description="判定対象となるセメスタが設定されます。"/><COLUMN id="HANTEI_TAISYO_GAKKI_NO" name="判定対象学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="判定対象となる学期が設定されます。"/>
</TABLE>
