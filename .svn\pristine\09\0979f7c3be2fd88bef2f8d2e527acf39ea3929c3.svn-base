<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaf01101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>資産台帳</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<style type="text/css">
<!--
 .setWidth TD {width: 80px; white-space: nowrap;}
 -->
</style>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_seti(thisObj, thisEvent) {
	// 設置場所
	var servlet = "rev/ka/KazSetiAJAX";
	var args = new Array();
	args['code'] = document.getElementById('form1:htmlSetchiBashoCd').value;
	var target = "form1:htmlSetchiBashoName";	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

}

function func_kanri(thisObj, thisEvent) {
	// 管理部門コード
	var servlet = "rev/co/CogYosanTaniAJAX";	
	var target = "form1:htmlKanriBmnName";
	var args = new Array();
	args['code1'] = document.getElementById('form1:htmlKanzaiNendoHidden').value;
	args['code2'] = document.getElementById('form1:htmlKanriBmnCd').value;
	args['code3'] = "0";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

}

function func_changeSsn(thisObj, thisEvent) {
	//分類区分 clickイベント
	var sisnSyrui = thisObj.value;
	document.getElementById('form1:htmlSsnSyuruiHidden').value = sisnSyrui;
	func_setTitle(sisnSyrui);
}

function func_KeijoYosanTaniAjax(thisObj, thisEvent) {
	// 計上予算単位名称を取得する
	var servlet = "rev/ka/KaCogYosanTaniAJAX";
	var target = "form1:htmlKeijoYosanTaniName";
	var code = new Array();
	code['code1'] = '0';
	code['code2'] = '1';
	code['code3'] = '';
	code['code4'] = thisObj.value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code );
}

function func_ShutokusakiAjax(thisObj, thisEvent) {
	// 取引先名称（取得先名称）を取得する
	var servlet = "rev/co/CogToriKihonAJAX";
	var target = "form1:htmlShutokusakiNameAJAX";
	var code = new Array();
	code['code1'] = thisObj.value;
	code['code2'] = "0";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function func_KanriUserIdAjax(thisObj, thisEvent) {
		// 管理者ID
		// 管理者名称を取得する
		var servlet = "rev/ka/KaCoiJinjAJAX";	
		var target = "form1:htmlKanriUserName";
		var args = new Array();
		args['code'] = thisObj.value;
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}

function func_bmnNameAjax(thisObj, thisEvent) {
	//管理者所属部門
	//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
	//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
	// 部門名称を取得する
	var servlet = "rev/co/CoiBmnBmnNameIJAJAX";
	var target = "form1:htmlKanriUserBmnName";
	var code = new Array();
	code['code1'] = thisObj.value;
	code['code2'] = "null";
	var ajaxUtil = new AjaxUtil();

	ajaxUtil.getCodeName(servlet, target, code);
}

function func_kanriUserBmn(thisObj, thisEvent) {
	//管理者所属部門
	//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
	//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
	// 部門検索画面を呼び出す
	var bmnCd = document.getElementById("form1:htmlKanriUserBmn").value;
	var startDate = document.getElementById("form1:htmlShutokuDateFrom").value;

	var url="${pageContext.request.contextPath}/faces/rev/co/pCoi0401.jsp"
			+"?"
	       	+"bmnTaishoDate="+startDate
	       	+"&"
	       	+"bmnCd="+bmnCd
	       	+"&"
	       	+"bmnCdItem=form1:htmlKanriUserBmn"
           	+"&"
	       	+"bmnNameItem=form1:htmlKanriUserBmnName";
	openModalWindow(url, "PCoi0401", "<%=com.jast.gakuen.rev.co.PCoi0401.getWindowOpenOption() %>");
}


function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutable').value = "0";
}


function loadFunc(){
	// Ajaxの起動
	func_seti(document.getElementById('form1:htmlSetchiBashoCd'), "");
	func_kanri(document.getElementById('form1:htmlKanriBmnCd'), "");
	func_KeijoYosanTaniAjax(document.getElementById('form1:htmlKeijoYosanTaniCd'),"");
	func_bmnNameAjax(document.getElementById('form1:htmlKanriUserBmn'),"");
	func_KanriUserIdAjax(document.getElementById('form1:htmlKanriUserId'), "");
	func_ShutokusakiAjax(document.getElementById('form1:htmlShutokusakiCd'),"");
	func_setTitle(ssnSyurui);
	var ssnSyurui = document.getElementById('form1:htmlSsnSyuruiHidden').value;	
}
function func_setTitle(ssnSyurui) {
	//帳票タイトルに値をセットする
	chohyoTitle = document.getElementById('form1:htmlChohyoTitleSsnHidden').value;
	document.getElementById('form1:htmlChohyoTitle').value = chohyoTitle;
}
window.attachEvent("onload", attachFormatNumber);
window.attachEvent("onload", loadFunc);

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kaf01101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kaf01101.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kaf01101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kaf01101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトのため全角１文字 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>

<!--↓content↓-->
<DIV id="content">			

<!-- ↓ここにコンポーネントを配置 -->
<DIV class="column" align="center">

		<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="915">
			<TBODY>
				<TR style="${pc_Kaf01101.displaySsnBnrSyukeiKeta}">
			    <!-- 分類区分 資産分類集計桁数が0または取得できない場合非表示-->
					<TH class="v_a" width="153">
						<h:outputText styleClass="outputText"
							id="lblBnrKbn"
							value="#{pc_Kaf01101.propBnrKbn.labelName}"
							style="#{pc_Kaf01101.propBnrKbn.labelStyle}"></h:outputText>
					</TH>
					<TD width="232">
						<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlBnrKbn" 
							onclick="return func_changeSsn(this, event);"
							value="#{pc_Kaf01101.propBnrKbn.stringValue}" tabindex="2"
							disabled="#{pc_Kaf01101.propBnrKbn.disabled}"
							readonly="#{pc_Kaf01101.propBnrKbn.readonly}">
						<f:selectItems value="#{pc_Kaf01101.propBnrKbn.list}" />
					</h:selectOneRadio>
					</TD>
					<TD width="10" class="clear_border" style="background-color:transparent;"></TD>
					<TD width="492" class="clear_border" style="background-color:transparent;">
						<hx:commandExButton type="submit" value="選択"
							styleClass="commandExButton" id="select" tabindex="3"
							disabled="#{pc_Kaf01101.propSelect.disabled}"
							rendered="#{pc_Kaf01101.propSelect.rendered}" action="#{pc_Kaf01101.doSelectAction}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="解除"
							styleClass="commandExButton" id="unselect" tabindex="4"
							disabled="#{pc_Kaf01101.propUnselect.disabled}"
							rendered="#{pc_Kaf01101.propUnselect.rendered}" action="#{pc_Kaf01101.doUnselectAction}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		
		<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="900">
			<TBODY>
				<TR class="clear_border">
					<TD width="160" style="background-color:transparent;"></TD>
					<TD width="740" style="background-color:transparent;"></TD>
				</TR>
				<TR>
				<!-- 取得年度 -->
					<TH class="v_a" width="153"><h:outputText styleClass="outputText" id="lblShutokuNendo"
							style="#{pc_Kaf01101.propShutokuNendoFrom.labelStyle}" value="#{pc_Kaf01101.propShutokuNendoFrom.labelName}"></h:outputText></TH>
					<TD colspan="3" nowrap><h:inputText styleClass="inputText" id="htmlShutokuNendoFrom"
							size="4" disabled="#{pc_Kaf01101.propShutokuNendoFrom.disabled}"
							readonly="#{pc_Kaf01101.propShutokuNendoFrom.readonly}"
							style="#{pc_Kaf01101.propShutokuNendoFrom.style}" tabindex="5"
							value="#{pc_Kaf01101.propShutokuNendoFrom.dateValue}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" /></h:inputText> ～ <h:inputText
							styleClass="inputText" id="htmlShutokuNendoTo" size="4"
							disabled="#{pc_Kaf01101.propShutokuNendoTo.disabled}"
							readonly="#{pc_Kaf01101.propShutokuNendoTo.readonly}"
							style="#{pc_Kaf01101.propShutokuNendoTo.style}" tabindex="6"
							value="#{pc_Kaf01101.propShutokuNendoTo.dateValue}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
				</TR>
				<TR>
				<!-- 資産区分 -->
					<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText" id="lblSsnKbn"
							value="#{pc_Kaf01101.propSsnKbn.labelName}"
							style="#{pc_Kaf01101.propSsnKbn.labelStyle}"></h:outputText></TH>
					<TD colspan="3" nowrap><h:selectManyCheckbox
							disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlSsnKbn"
							value="#{pc_Kaf01101.propSsnKbn.stringValue}"
							readonly="#{pc_Kaf01101.propSsnKbn.readonly}" tabindex="7"
							disabled="#{pc_Kaf01101.propSsnKbn.disabled}">
							<f:selectItems value="#{pc_Kaf01101.propSsnKbn.list}" />
						</h:selectManyCheckbox></TD>
				</TR>
				<TR style="${pc_Kaf01101.displaySsnBnr}">
				<!-- 資産分類 -->
					<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText"
						id="lblSsnBnrCd"
						value="#{pc_Kaf01101.propSsnBnrCd.labelName}"
						style="#{pc_Kaf01101.propSsnBnrCd.labelStyle}"></h:outputText></TH>
						<TD colspan="3" nowrap><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSsnBnrCd" value="#{pc_Kaf01101.propSsnBnrCd.stringValue}"
							disabled="#{pc_Kaf01101.propSsnBnrCd.disabled}"
							readonly="#{pc_Kaf01101.propSsnBnrCd.readonly}" tabindex="8">
							<f:selectItems value="#{pc_Kaf01101.propSsnBnrCd.list}" />
						</h:selectOneMenu></TD>
				</TR>
				<TR style="${pc_Kaf01101.displaySsnBnrSyukei}">
				<!-- 資産分類集計 -->
					<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText"
							id="lblSsnBnrSyukei"
							value="#{pc_Kaf01101.propSsnBnrSyukei.labelName}"
							style="#{pc_Kaf01101.propSsnBnrSyukei.labelStyle}"></h:outputText></TH>
							<TD colspan="3" nowrap><h:selectOneMenu styleClass="selectOneMenu"
								id="htmlSsnBnrSyukei" value="#{pc_Kaf01101.propSsnBnrSyukei.stringValue}"
								disabled="#{pc_Kaf01101.propSsnBnrSyukei.disabled}"
								readonly="#{pc_Kaf01101.propSsnBnrSyukei.readonly}" tabindex="9">
								<f:selectItems value="#{pc_Kaf01101.propSsnBnrSyukei.list}" />
							</h:selectOneMenu></TD>
				</TR>	
				<TR>
				<!-- 取得日付 -->
					<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText"
							id="lblIkanDate" value="取得日付"
							style="#{pc_Kaf01101.propShutokuDateFrom.labelStyle}"></h:outputText>					
					</TH>
					<TD colspan="3" nowrap><h:inputText styleClass="inputText" id="htmlShutokuDateFrom"
							value="#{pc_Kaf01101.propShutokuDateFrom.dateValue}" size="10"
							style="#{pc_Kaf01101.propShutokuDateFrom.style}" tabindex="10"
							disabled="#{pc_Kaf01101.propShutokuDateFrom.disabled}"
							readonly="#{pc_Kaf01101.propShutokuDateFrom.readonly}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText>
					～ <h:inputText styleClass="inputText" id="htmlShutokuDateTo"
							value="#{pc_Kaf01101.propShutokuDateTo.dateValue}" size="10"
							style="#{pc_Kaf01101.propShutokuDateTo.style}" tabindex="11"
							disabled="#{pc_Kaf01101.propShutokuDateTo.disabled}"
							readonly="#{pc_Kaf01101.propShutokuDateTo.readonly}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
				</TR>
				<TR>
				<!-- 増加理由 -->
					<TH class="v_a" width="160" nowrap>
						<h:outputText
							styleClass="outputText" 
							id="lblZokaRiyu"
							value="#{pc_Kaf01101.propZokaRiyu.labelName}"
							style="#{pc_Kaf01101.propZokaRiyu.labelStyle}">
						</h:outputText>
					</TH>
					<TD colspan="3" nowrap>
						<h:selectOneMenu styleClass="selectOneMenu" id="htmlZokaRiyu"
							value="#{pc_Kaf01101.propZokaRiyu.value}"
							disabled="#{pc_Kaf01101.propZokaRiyu.disabled}"
							readonly="#{pc_Kaf01101.propZokaRiyu.readonly}">
							<f:selectItems value="#{pc_Kaf01101.propZokaRiyu.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
				<!-- 設置場所コード -->
					<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText" id="lblSetiBashoCd"
							style="#{pc_Kaf01101.propSetchiBashoCd.labelStyle}"
							value="#{pc_Kaf01101.propSetchiBashoCd.labelName}"></h:outputText></TH>
					<TD colspan="3" nowrap>
					<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:inputText
							styleClass="inputText" id="htmlSetchiBashoCd" size="10"
							value="#{pc_Kaf01101.propSetchiBashoCd.stringValue}"
							style="#{pc_Kaf01101.propSetchiBashoCd.style}"
							maxlength="#{pc_Kaf01101.propSetchiBashoCd.maxLength}"
							disabled="#{pc_Kaf01101.propSetchiBashoCd.disabled}"
							readonly="#{pc_Kaf01101.propSetchiBashoCd.readonly}"
							onblur="return func_seti(this, event);" tabindex="11"></h:inputText>
					<h:outputText
							styleClass="outputText" id="lblSetchiBashoCdIchi"
							value="(前方一致)">
					</h:outputText> 
					<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="SearchKanriUserId"
							tabindex="12"
							disabled="#{pc_Kaf01101.propSearchSetchiBashoCd.disabled}" action="#{pc_Kaf01101.doSearchSetchiBashoCdAction}"></hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlSetchiBashoName"></h:outputText></DIV></TD>
				</TR>
				<TR>
				<!-- 管理部門コード -->
				<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText" id="lblKanriBmnCd"
							style="#{pc_Kaf01101.propKanriBmnCd.labelStyle}"
							value="#{pc_Kaf01101.propKanriBmnCd.labelName}"></h:outputText></TH>
					<TD colspan="3" nowrap>
					<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:inputText
							styleClass="inputText" id="htmlKanriBmnCd" size="10"
							value="#{pc_Kaf01101.propKanriBmnCd.stringValue}"
							style="#{pc_Kaf01101.propKanriBmnCd.style}"
							maxlength="#{pc_Kaf01101.propKanriBmnCd.maxLength}"
							disabled="#{pc_Kaf01101.propKanriBmnCd.disabled}"
							readonly="#{pc_Kaf01101.propKanriBmnCd.readonly}"
							 onblur="return func_kanri(this, event);" tabindex="13">
					</h:inputText>
					<h:outputText
							styleClass="outputText" id="lblKanriBmnCdIchi" value="(前方一致)">
					</h:outputText>
					<hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="searchKanriBmnCd"
							tabindex="13"
							disabled="#{pc_Kaf01101.propSearchKanriBmnCd.disabled}" action="#{pc_Kaf01101.doSearchKanriBmnCdAction}"></hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlKanriBmnName"></h:outputText></DIV></TD>
				</TR>
				<TR>
				<!-- 一括予算単位コード -->
					<TH class="v_a" width="160" nowrap><h:outputText 
							styleClass="outputText"
							id="lblKeijoYosanTaniCd"
							style="#{pc_Kaf01101.propKeijoYosanTaniCd.labelStyle}" 
							value="#{pc_Kaf01101.propKeijoYosanTaniCd.labelName}">
					</h:outputText></TH>
					<TD colspan="3">
						<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText id="htmlKeijoYosanTaniCd" styleClass="inputText"
								style="#{pc_Kaf01101.propKeijoYosanTaniCd.style}"
								value="#{pc_Kaf01101.propKeijoYosanTaniCd.stringValue}" size="10"
								maxlength="#{pc_Kaf01101.propKeijoYosanTaniCd.maxLength}"
								tabindex="14"
								onblur="return func_KeijoYosanTaniAjax(this, event);"
								readonly="#{pc_Kaf01101.propKeijoYosanTaniCd.readonly}"
								disabled="#{pc_Kaf01101.propKeijoYosanTaniCd.disabled}">
							</h:inputText>
							<h:outputText 
								styleClass="outputText"value="(前方一致)"id="lblKeijoYosanTaniCdFindType">
							</h:outputText>
							<hx:commandExButton type="submit"
								styleClass="commandExButton_search" id="searchKeijoYosanTani"
								tabindex="15"
								action="#{pc_Kaf01101.doSearchKeijoYosanTaniAction}"
								disabled="#{pc_Kaf01101.propSearchKeijoYosanTani.disabled}">
							</hx:commandExButton>
							<h:outputText styleClass="outputText"
								id="htmlKeijoYosanTaniName">
							</h:outputText>
						</DIV>
					</TD>
				</TR>
				<TR>
				<!-- 管理者所属部門 -->
					<TH class="v_a" width="160" nowrap>
					<h:outputText styleClass="outputText" 
							  id="lblKanriUserBmn" 
							  style="#{pc_Kaf01101.propKanriUserBmn.labelStyle}" 
							  value="#{pc_Kaf01101.propKanriUserBmn.labelName}">
					</h:outputText>
					</TH>
				<TD colspan="3" nowrap>
					<DIV style="width:750px;white-space:nowrap;overflow:hidden;display:block;">
						<h:inputText id="htmlKanriUserBmn" 
								 styleClass="inputText" 
								 size="10" 
								 maxlength="#{pc_Kaf01101.propKanriUserBmn.maxLength}" 
								 value="#{pc_Kaf01101.propKanriUserBmn.stringValue}" 
								 style="#{pc_Kaf01101.propKanriUserBmn.style}" 
								 readonly="#{pc_Kaf01101.propKanriUserBmn.readonly}" 
								 disabled="#{pc_Kaf01101.propKanriUserBmn.disabled}" 
								 tabindex="16"
								 rendered="#{pc_Kaf01101.propKanriUserBmn.rendered}"
								 onblur="return func_bmnNameAjax(this, event);">
						</h:inputText>
						<h:outputText 
								styleClass="outputText"
								value="(前方一致)"
								id="lblKanriUserBmnIch">
						</h:outputText>
						<hx:commandExButton type="button"
							id="searchKanriUserBmn" styleClass="commandExButton_search"
							onclick="return func_kanriUserBmn(this, event);"
							tabindex="17"
							disabled="#{pc_Kaf01101.propSearchKanriUserBmn.disabled}">
						</hx:commandExButton>
						<h:outputText id="htmlKanriUserBmnName"
								  styleClass="outputText"
								  value="#{pc_Kaf01101.propKanriUserBmnName.stringValue}">
						</h:outputText>
						</DIV>
					</TD>
				</TR>
				<TR>
				<!-- 管理者ID -->
				<TH class="v_a" width="160" nowrap>
						<h:outputText styleClass="outputText" 
						   id="lblKanrisyaId"
						   value="#{pc_Kaf01101.propKanriUserId.labelName}"
						   style="#{pc_Kaf01101.propKanriUserId.labelStyle}"></h:outputText>
						</TH>
						<TD colspan="3" nowrap>
						<h:inputText styleClass="inputText" id="htmlKanriUserId" size="20"
							value="#{pc_Kaf01101.propKanriUserId.stringValue}"
							style="#{pc_Kaf01101.propKanriUserId.style}"
							maxlength="#{pc_Kaf01101.propKanriUserId.maxLength}"
							tabindex="18"
							onblur="return func_KanriUserIdAjax(this, event);"
							disabled="#{pc_Kaf01101.propKanriUserId.disabled}"
							readonly="#{pc_Kaf01101.propKanriUserId.readonly}"
							rendered="#{pc_Kaf01101.propKanriUserId.rendered}"></h:inputText>
				 		<hx:commandExButton id="searchKanriUserId"
							styleClass="commandExButton_search" type="submit"
							action="#{pc_Kaf01101.doSearchKanriUserIdAction}" tabindex="19"
							disabled="#{pc_Kaf01101.propSearchKanriUserId.disabled}">
						</hx:commandExButton>
					    <h:outputText id="htmlKanriUserName" styleClass="outputText">
					    </h:outputText>
						</TD>
				</TR>
				<TR>
				<!-- 取得先コード -->
					<TH class="v_a" width="160" nowrap>
						<h:outputText styleClass="outputText" id="lblShutokusakiCd"
									style="#{pc_Kaf01101.propShutokusakiCd.labelStyle}"
									value="#{pc_Kaf01101.propShutokusakiCd.labelName}"></h:outputText></TH>
						<TD colspan="3" nowrap>
							<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" id="htmlShutokusakiCd"
									disabled="#{pc_Kaf01101.propShutokusakiCd.disabled}"
									maxlength="#{pc_Kaf01101.propShutokusakiCd.maxLength}"
									readonly="#{pc_Kaf01101.propShutokusakiCd.readonly}"
									rendered="#{pc_Kaf01101.propShutokusakiCd.rendered}"
									style="#{pc_Kaf01101.propShutokusakiCd.style}"
									value="#{pc_Kaf01101.propShutokusakiCd.stringValue}" size="20"
									tabindex="20"
									onblur="return func_ShutokusakiAjax(this, event);"></h:inputText><h:outputText
									styleClass="outputText" value="(前方一致)"
									id="lblShutokusakiCdFindType">
								</h:outputText><hx:commandExButton type="submit"
							styleClass="commandExButton_search" id="searchShutokusaki"
							action="#{pc_Kaf01101.doSearchShutokusakiAction}"
							tabindex="21"
							disabled="#{pc_Kaf01101.propSearchShutokusaki.disabled}">
						</hx:commandExButton><h:outputText styleClass="outputText"
									id="htmlShutokusakiNameAJAX"></h:outputText></DIV>
						</TD>
				</TR>
				<TR>
				<!-- 取得先名称-->
					 <TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText"
							id="lblShutokusakiName"
							style="#{pc_Kaf01101.propShutokusakiName.labelStyle}"
							value="#{pc_Kaf01101.propShutokusakiName.labelName}"></h:outputText></TH>
					<TD colspan="3" nowrap>
					<TABLE border="0" cellpadding="0" cellspacing="0">
						<TR class="clear_border">
							<TD><h:inputText styleClass="inputText" id="htmlShutokusakiName"
								size="60"
								style="#{pc_Kaf01101.propShutokusakiName.style}"
								value="#{pc_Kaf01101.propShutokusakiName.stringValue}"
								maxlength="#{pc_Kaf01101.propShutokusakiName.maxLength}"
								readonly="#{pc_Kaf01101.propShutokusakiName.readonly}"
								tabindex="22"
								disabled="#{pc_Kaf01101.propShutokusakiName.disabled}">
							</h:inputText></TD>
							<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlShutokusakiNameFindType"
									value="#{pc_Kaf01101.propShutokusakiNameFindType.stringValue}"
									disabled="#{pc_Kaf01101.propShutokusakiNameFindType.disabled}"
									readonly="#{pc_Kaf01101.propShutokusakiNameFindType.readonly}"
									rendered="#{pc_Kaf01101.propShutokusakiNameFindType.rendered}"
									style="#{pc_Kaf01101.propShutokusakiNameFindType.style}"
									 tabindex="23">
							<f:selectItems value="#{pc_Kaf01101.propShutokusakiNameFindType.list}" />
							</h:selectOneRadio></TD>
						</TR>
					</TABLE>
					</TD>
				</TR>
				<TR>
				<!-- 除却 -->
					<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText" id="lblJokyaku"
							style="#{pc_Kaf01101.propJokyaku.labelStyle}"
							value="#{pc_Kaf01101.propJokyaku.labelName}"></h:outputText></TH>
					<TD colspan="3" nowrap>
					<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;"><h:selectManyCheckbox
							disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlJokyaku"
							disabled="#{pc_Kaf01101.propJokyaku.disabled}"
							readonly="#{pc_Kaf01101.propJokyaku.readonly}"
							style="#{pc_Kaf01101.propJokyaku.style}" tabindex="26"
							value="#{pc_Kaf01101.propJokyaku.stringValue}">
							<f:selectItems value="#{pc_Kaf01101.propJokyaku.list}" />
						</h:selectManyCheckbox></DIV></TD>
				</TR>
				<TR>
				<!-- 廃棄 -->
				<TH class="v_a" width="160" nowrap>
					<h:outputText styleClass="outputText" id="lblHaiki"
							value="#{pc_Kaf01101.propHaiki.name}"
							style="#{pc_Kaf01101.propHaiki.labelStyle}">
						</h:outputText>
				</TH>
				<TD colspan="3" nowrap>
				<h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled"
							styleClass="selectManyCheckbox" id="htmlHaiki"
							disabled="#{pc_Kaf01101.propHaiki.disabled}"
							readonly="#{pc_Kaf01101.propHaiki.readonly}"
							rendered="#{pc_Kaf01101.propHaiki.rendered}"
							style="#{pc_Kaf01101.propHaiki.style}"
							tabindex="30"
							value="#{pc_Kaf01101.propHaiki.stringValue}">
							<f:selectItems value="#{pc_Kaf01101.propHaiki.list}" />
						</h:selectManyCheckbox></TD>
				</TR>
				<TR class="clear_border">
					<TD width="160" style="background-color:transparent;"></TD>
					<TD width="740" style="background-color:transparent;"></TD>
				</TR>
				<TR>
				<!-- 帳票タイトル -->
					<TH class="v_a" width="160" nowrap><h:outputText styleClass="outputText" id="lblChohyoTitile"
							value="#{pc_Kaf01101.propChohyoTitle.labelName}"
							style="#{pc_Kaf01101.propChohyoTitle.labelStyle}"></h:outputText></TH>
					<TD colspan="3" nowrap><h:inputText styleClass="inputText" id="htmlChohyoTitle"
							size="60" value="#{pc_Kaf01101.propChohyoTitle.stringValue}"
							style="#{pc_Kaf01101.propChohyoTitle.style}" tabindex="36"
							readonly="#{pc_Kaf01101.propChohyoTitle.readonly}"
							disabled="#{pc_Kaf01101.propChohyoTitle.disabled}"></h:inputText></TD>
				</TR>				
			</TBODY>
			</TABLE>
			<BR>
			<TABLE width="880" border="0" style="" class="button_bar">
				<TBODY>
				<TR>
					<TD>
						<hx:commandExButton type="submit" value="クリア" tabindex="40"
							styleClass="commandExButton_etc" id="clear"
							action="#{pc_Kaf01101.doClearAction}"
							disabled="#{pc_Kaf01101.propClear.disabled}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="PDF作成" tabindex="41"
							styleClass="commandExButton_out" id="pdfout"
							action="#{pc_Kaf01101.doPdfoutAction}"
							confirm="#{msg.SY_MSG_0019W}"
							disabled="#{pc_Kaf01101.propPdfout.disabled}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="EXCEL作成" tabindex="42"
							styleClass="commandExButton_out" id="excelout"
							action="#{pc_Kaf01101.doExcelOutAction}"  
							confirm="#{msg.SY_MSG_0027W}" 
							disabled="#{pc_Kaf01101.propExcelout.disabled}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="CSV作成" tabindex="43"
							styleClass="commandExButton_out" id="csvout"
							action="#{pc_Kaf01101.doCsvoutAction}"
							confirm="#{msg.SY_MSG_0020W}"
							disabled="#{pc_Kaf01101.propCsvout.disabled}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="出力項目指定" tabindex="44"
							styleClass="commandExButton_out" id="setoutput"
							action="#{pc_Kaf01101.doSetoutputAction}"
							disabled="#{pc_Kaf01101.propSetoutput.disabled}">
						</hx:commandExButton>
					</TD>
				</TR>
				</TBODY>
			</TABLE>


</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->

<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden
				value="#{pc_Kaf01101.propKanzaiNendoHidden.stringValue}"
				id="htmlKanzaiNendoHidden">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kaf01101.propExecutable.integerValue}"
				id="htmlExecutable">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="htmlEdaNoFrom=####0;####0 | htmlEdaNoTo=####0;####0"
				id="htmlFormatNumberOption">
			</h:inputHidden>
			<h:inputHidden id="htmlChohyoTitleSsnHidden" value="#{pc_Kaf01101.propChohyoTitleSsnHidden.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlSsnSyuruiHidden" value="#{pc_Kaf01101.propSsnSyuruiHidden.stringValue}"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
							