<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kac01401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>資産振替・移管一括登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kac01401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kac01401.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kac01401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kac01401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<!-- ↓ここに戻る／閉じるボタンを配置 -->

<!-- ↑ここに戻る／閉じるボタンを配置 -->

<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
		<TBODY>					
			<TR>
				<TD>
				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="table">
					<TBODY>
						<TR>
							<TH width="150" class="v_a">
							<h:outputText styleClass="outputText" id="lblShisanShurui"
										value="#{pc_Kac01401.propShisanShurui.name}"
										style="#{pc_Kac01401.propShisanShurui.labelStyle}"></h:outputText>
							</TH>
							<TD class="">
								<TABLE border="0" class="">
								<TBODY>
									<TR>
										<TD class="clear_border">
										<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlShisanShurui"
													disabled="#{pc_Kac01401.propShisanShurui.disabled}"
													readonly="#{pc_Kac01401.propShisanShurui.readonly}"
													rendered="#{pc_Kac01401.propShisanShurui.rendered}"
													style="#{pc_Kac01401.propShisanShurui.style}"
													value="#{pc_Kac01401.propShisanShurui.stringValue}">
													<f:selectItems value="#{pc_Kac01401.propShisanShurui.list}" />
												</h:selectOneRadio></TD>
									</TR>
								</TBODY>
								</TABLE>
							</TD>
						</TR>
						<TR>
							<TH width="150" class="v_a">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
								<TBODY>
									<TR>
										<TH class="clear_border"><h:outputText
											styleClass="outputText" id="lblInputFile"
											value="#{pc_Kac01401.propInputFile.name}"
											style="#{pc_Kac01401.propInputFile.labelStyle}"></h:outputText></TH>
									</TR>
									<TR>
										<TH class="clear_border"><h:outputText
											styleClass="outputText" id="lblInputFilePre"
											value="#{pc_Kac01401.propInputFilePre.name}"
											style="#{pc_Kac01401.propInputFilePre.labelStyle}"></h:outputText></TH>
									</TR>
								</TBODY>
							</TABLE>
							<TD class="">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
								<TBODY>
									<TR>
										<TD class="clear_border"><hx:fileupload
											styleClass="fileupload" id="htmlInputFile" size="50"
											value="#{pc_Kac01401.propInputFile.value}" tabindex="2"
											style="width:530px">
											<hx:fileProp name="fileName"
												value="#{pc_Kac01401.propInputFile.fileName}" />
											<hx:fileProp name="contentType"
												value="#{pc_Kac01401.propInputFile.contentType}" />
										</hx:fileupload></TD>
									</TR>
									<TR>
										<TD class="clear_border"><h:outputText
											styleClass="outputText" id="htmlInputFilePre"
											value="#{pc_Kac01401.propInputFilePre.stringValue}"></h:outputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
						<TR>
							<TH class="v_c"><h:outputText
								styleClass="outputText" id="lblSyoriKbn"
								value="#{pc_Kac01401.propSyoriKbn.name}"
								style="#{pc_Kac01401.propSyoriKbn.labelStyle}"></h:outputText></TH>
							<TD class="">
							<TABLE border="0" class="">
								<TBODY>
									<TR>
										<TD class="clear_border"><h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlSyoriKbn" tabindex="4"
											value="#{pc_Kac01401.propSyoriKbn.checked}"
											disabled="#{pc_Kac01401.propSyoriKbn.disabled}"
											readonly="#{pc_Kac01401.propSyoriKbn.readonly}"></h:selectBooleanCheckbox>
											<h:outputText
											styleClass="outputText" id="lblSyoriKbnL"
											value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
						<TR>
							<TH class="v_d"><h:outputText
								styleClass="outputText" id="lblChkList"
								value="#{pc_Kac01401.propChkListNormal.name}"
								style="#{pc_Kac01401.propChkListNormal.labelStyle}"></h:outputText></TH>
							<TD >
							<TABLE border="0" class="">
								<TBODY>
									<TR>
										<TD class="clear_border"><h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlChkListNormal" tabindex="5"
											value="#{pc_Kac01401.propChkListNormal.checked}"
											disabled="#{pc_Kac01401.propChkListNormal.disabled}"
											readonly="#{pc_Kac01401.propChkListNormal.readonly}"></h:selectBooleanCheckbox>
										<h:outputText styleClass="outputText" id="lblChkListNormal" 
											value="正常データ"></h:outputText>
										</TD>
									</TR>
									<TR>
										<TD class="clear_border"><h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlChkListError" tabindex="6"
											value="#{pc_Kac01401.propChkListError.checked}"
											readonly="#{pc_Kac01401.propChkListError.readonly}"
											disabled="#{pc_Kac01401.propChkListError.disabled}"></h:selectBooleanCheckbox>
										<h:outputText styleClass="outputText" id="lblChkListError"
											value="エラーデータ"></h:outputText></TD>
									</TR>
									<TR>
										<TD class="clear_border">
											<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlChkListWarning" tabindex="7"
											value="#{pc_Kac01401.propChkListWarning.checked}"
											disabled="#{pc_Kac01401.propChkListWarning.disabled}"
											readonly="#{pc_Kac01401.propChkListWarning.readonly}"></h:selectBooleanCheckbox>
										<h:outputText styleClass="outputText" id="lblChkListWarning"
											value="ワーニングデータ"></h:outputText>
                                        </TD>
									</TR>
								</TBODY>
							</TABLE>
                          </TD>
						</TR>
					</TBODY>
				</TABLE><BR>
				</TD>
			</TR>
			<TR>
				<TD>
				<TABLE width="700" border="0" class="button_bar" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
							<TD><hx:commandExButton type="submit" value="入力項目指定"
								styleClass="commandExButton_etc" id="setinput" tabindex="8"
								action="#{pc_Kac01401.doSetinputAction}"
								disabled="#{pc_Kac01401.propSetinput.disabled}"
								rendered="#{pc_Kac01401.propSetinput.rendered}"></hx:commandExButton>
								<hx:commandExButton type="submit" value="実行"
								styleClass="commandExButton_dat" id="exec" tabindex="9"
								action="#{pc_Kac01401.doExecAction}"
								disabled="#{pc_Kac01401.propExec.disabled}"
								rendered="#{pc_Kac01401.propExec.rendered}"
								confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

