<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos00803T09.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<TITLE>Cos00803T09.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
<SCRIPT type="text/javascript">

/*
function confirmOk() {
// はい」を選択された場合、実行される処理
	document.getElementById('form1:propExecutableBtnRegister').value = "1";
	indirectClick('register');
	return;
}
function confirmCancel() {
//「いいえ」を選択された場合、実行される処理
	alert('実行を中断しました。');
	return;
}
*/

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cos00803T09.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!--↓outer↓-->
			<DIV class="outer"><!--↓head↓--><!-- ヘッダーインクルード --><jsp:include
				page="../../rev/inc/header.jsp">
				<hx:panelBox styleClass="panelBox" id="boxHeader"></hx:panelBox>
			</jsp:include><!-- ヘッダーへのデータセット領域 -->
			<DIV style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cos00803T09.doCloseDispAction}"></hx:commandExButton><h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cos00803T09.funcId}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText><h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cos00803T09.screenName}"></h:outputText></DIV>

			<table border="0" width="98%">
				<tr>
					<td align="left">
					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND><h:outputText
						id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false"
						style="color: red; font-size: 8pt; font-weight: bold">
					</h:outputText></FIELDSET>
					</td>
					<td align="right"><%-- 戻るボタン --%><hx:commandExButton type="submit"
						value="戻る" styleClass="commandExButton" id="returnDisp"
						action="#{pc_Cos00803T09.doReturnDispAction}">
					</hx:commandExButton></td>
				</tr>
			</table>
			<!--↑head↑--><!--↓content↓-->
			<DIV id="content">

			<DIV class="column" align="center">
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center" height=""><%-- ↓↓↓ コンテンツ ↓↓↓ --%>
						<table border="0" width="800">
							<tr>
								<td nowrap align="center">
								<TABLE border="0" width="550" cellpadding="3" class="table">
									<TBODY>
										<TR>
											<TH width="200" class="v_a"><h:outputText
												styleClass="outputText" id="labelId"
												value="#{pc_Cos00803T09.common.propId.labelName}"
												style="#{pc_Cos00803T09.common.propId.labelStyle}">
											</h:outputText></TH>
											<TD><h:inputText styleClass="inputText"
												value="#{pc_Cos00803T09.common.propId.stringValue}"
												style="#{pc_Cos00803T09.common.propId.style}"
												readonly="#{pc_Cos00803T09.common.propId.readonly}"
												maxlength="#{pc_Cos00803T09.common.propId.max}"
												disabled="#{pc_Cos00803T09.common.propId.disabled}"
												size="35" id="htmlId">
											</h:inputText></TD>
										</TR>
									</TBODY>
								</TABLE>
								</td>
							</tr>
						</table>

						<HR noshade class="hr">

						<%-- タブ --%>
						<table border="0" cellpadding="0" cellspacing="0" width="938">
							<TBODY>
								<TR>
									<TD nowrap="nowrap" align="left"><hx:commandExButton type="submit"
										action="#{pc_Cos00803T09.doHtmlLinkBasicInfoAction}"
										value="#{pc_Cos00803T09.common.propBasicInfoTabBtn.name}"
										disabled="#{pc_Cos00803T09.common.propBasicInfoTabBtn.disabled}"
										rendered="#{pc_Cos00803T09.common.propBasicInfoTabBtn.rendered}"
										styleClass="#{pc_Cos00803T09.common.propBasicInfoTabBtn.style}"
										id="htmlBasicInfoTabBtn" style="width: 116px;"></hx:commandExButton><hx:commandExButton
										type="submit" action="#{pc_Cos00803T09.doHtmlLinkUserAction}"
										value="#{pc_Cos00803T09.common.propUserTabBtn.name}"
										disabled="#{pc_Cos00803T09.common.propUserTabBtn.disabled}"
										rendered="#{pc_Cos00803T09.common.propUserTabBtn.rendered}"
										styleClass="#{pc_Cos00803T09.common.propUserTabBtn.style}"
										id="htmlUserTabBtn" style="width: 116px;"></hx:commandExButton><hx:commandExButton
										type="submit" value="#{pc_Cos00803T09.common.propPrtBtn.name}"
										action="#{pc_Cos00803T09.doHtmlLinkPrtAction}"
										disabled="#{pc_Cos00803T09.common.propPrtBtn.disabled}"
										rendered="#{pc_Cos00803T09.common.propPrtBtn.rendered}"
										styleClass="#{pc_Cos00803T09.common.propPrtBtn.style}"
										id="htmlPrtBtn" style="width: 116px;"></hx:commandExButton><hx:commandExButton
										type="submit"
										value="#{pc_Cos00803T09.common.propMenuRegTabBtn.name}"
										action="#{pc_Cos00803T09.doHtmlLinkMenuRegAction}"
										disabled="#{pc_Cos00803T09.common.propMenuRegTabBtn.disabled}"
										rendered="#{pc_Cos00803T09.common.propMenuRegTabBtn.rendered}"
										styleClass="#{pc_Cos00803T09.common.propMenuRegTabBtn.style}"
										id="htmlMenuRegTabBtn" style="width: 116px;"></hx:commandExButton><hx:commandExButton
										type="submit"
										value="#{pc_Cos00803T09.common.propAppAuthTabBtn.name}"
										action="#{pc_Cos00803T09.doHtmlLinkAppAuthAction}"
										disabled="#{pc_Cos00803T09.common.propAppAuthTabBtn.disabled}"
										rendered="#{pc_Cos00803T09.common.propAppAuthTabBtn.rendered}"
										styleClass="#{pc_Cos00803T09.common.propAppAuthTabBtn.style}"
										id="htmlAppAuthTabBtn" style="width: 123px;"></hx:commandExButton><hx:commandExButton
										type="submit"
										value="#{pc_Cos00803T01.common.propFrtsyAuthTabBtn.name}"
										action="#{pc_Cos00803T01.doHtmlLinkFrtsyAuthAction}"
										disabled="#{pc_Cos00803T01.common.propFrtsyAuthTabBtn.disabled}"
										rendered="#{pc_Cos00803T01.common.propFrtsyAuthTabBtn.rendered}"
										styleClass="#{pc_Cos00803T01.common.propFrtsyAuthTabBtn.style}"
										id="htmlFrtsyAuthTabBtn" style="width: 117px;"></hx:commandExButton><hx:commandExButton
										type="submit"
										value="#{pc_Cos00803T09.common.propPfileAuthTabBtn.name}"
										action="#{pc_Cos00803T09.doHtmlLinkPfileAuthAction}"
										disabled="#{pc_Cos00803T09.common.propPfileAuthTabBtn.disabled}"
										rendered="#{pc_Cos00803T09.common.propPfileAuthTabBtn.rendered}"
										styleClass="#{pc_Cos00803T09.common.propPfileAuthTabBtn.style}"
										id="htmlPfileAuthTabBtn" style="width: 117px;"></hx:commandExButton><hx:commandExButton
										type="submit"
										value="#{pc_Cos00803T09.common.propEucTabBtn.name}"
										action="#{pc_Cos00803T09.doHtmlLinkEucAction}"
										disabled="#{pc_Cos00803T09.common.propEucTabBtn.disabled}"
										rendered="#{pc_Cos00803T09.common.propEucTabBtn.rendered}"
										styleClass="#{pc_Cos00803T09.common.propEucTabBtn.style}"
										id="htmlEucTabBtn" style="width: 117px;"></hx:commandExButton></td>
								</TR>
								<TR>
									<TD>
									<table width="100%" class="tab_body" border="0" cellpadding="0"
										cellspacing="0">
										<tr>
											<td><%-- コンポーネント --%>
						<table width="800" border="0" cellpadding="3" cellspacing="0" style="margin-top: 10px; margin-bottom: 10px;" >
							<tr>
								<td nowrap align="center">
								<TABLE border="0" width="530" class="table" cellpadding="">
									<TBODY>
										<TR>
											<TH class="v_b" width="182"><h:outputText
												styleClass="outputText" id="labelDbUserId"
												style="#{pc_Cos00803T09.propDbUserId.labelStyle}"
												value="#{pc_Cos00803T09.propDbUserId.labelName}">
											</h:outputText></TH>
											<td width="348"><h:inputText styleClass="inputText"
																	id="htmlDbUserId"
																	maxlength="#{pc_Cos00803T09.propDbUserId.maxLength}"
																	value="#{pc_Cos00803T09.propDbUserId.stringValue}"
																	style="#{pc_Cos00803T09.propDbUserId.style}" size="51">
																</h:inputText></td>
										</TR>
										<TR>
											<TH class="v_b" width="182"><h:outputText
												styleClass="outputText" id="labelDbUserPassword"
												style="#{pc_Cos00803T09.propDbUserPassword.labelStyle}"
												value="#{pc_Cos00803T09.propDbUserPassword.labelName}">
											</h:outputText></TH>
											<td width="348"><h:inputText styleClass="inputText"
																	id="htmlDbUserPassword"
																	maxlength="#{pc_Cos00803T09.propDbUserPassword.maxLength}"
																	value="#{pc_Cos00803T09.propDbUserPassword.stringValue}"
																	style="#{pc_Cos00803T09.propDbUserPassword.style}"
																	size="51">
																</h:inputText></td>
										</TR>
									</TBODY>
								</TABLE>
								<TABLE class="button_bar">
									<TR>
										<TD align="center" width="530"><hx:commandExButton type="submit"
											value="更新" styleClass="commandExButton_dat" id="update"
											confirm="#{msg.SY_MSG_0003W}"
											action="#{pc_Cos00803T09.doUpdateAction}"></hx:commandExButton>
										<hx:commandExButton type="submit" value="クリア"
											styleClass="commandExButton_dat" id="clear"
											action="#{pc_Cos00803T09.doClearAction}"></hx:commandExButton>
										</TD>
									</TR>
								</TABLE>
								</td>
							</tr>
						</table>
						
						</TD>
</TR>
</TABLE>
</TD>
</TR>
</TBODY>
</TABLE>
						
						
						
						
						<%-- ↑↑↑ コンテンツ ↑↑↑ --%></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--><!--↓foot↓--><jsp:include page="../inc/footer.jsp" />
			<!--↑foot↑--></DIV>
			<!--↑outer↑-->
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

