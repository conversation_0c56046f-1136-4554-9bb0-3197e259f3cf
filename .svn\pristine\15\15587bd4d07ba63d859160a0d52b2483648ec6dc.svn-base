<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/hk/PHkd0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE></TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_PHkd0101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_PHkd0101.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_PHkd0101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_PHkd0101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<!--↓content↓-->
			<DIV class="head_button_area">　<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" width="800">
				<TBODY>
					<TR>
						<TD><TABLE border="0" width="100%" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblTorihikiDate"
										value="#{pc_PHkd0101.propTorihikiDate.labelName}"
										style="#{pc_PHkd0101.propTorihikiDate.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:outputText styleClass="outputText"
										id="htmlTorihikiDate"
										value="#{pc_PHkd0101.propTorihikiDate.value}"
										style="#{pc_PHkd0101.propTorihikiDate.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblHakkokiID"
										value="#{pc_PHkd0101.propHakkokiId.labelName}"
										style="#{pc_PHkd0101.propHakkokiId.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlHakkokiID"
										value="#{pc_PHkd0101.propHakkokiId.value}"
										style="#{pc_PHkd0101.propHakkokiId.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblHakkokiName"
										value="#{pc_PHkd0101.propHakkokiName.labelName}"
										style="#{pc_PHkd0101.propHakkokiName.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlHakkokiName"
										value="#{pc_PHkd0101.propHakkokiName.value}"
										style="#{pc_PHkd0101.propHakkokiName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblSotNendo"
										value="#{pc_PHkd0101.propSotNendo.labelName}"
										style="#{pc_PHkd0101.propSotNendo.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlSotNendo"
										value="#{pc_PHkd0101.propSotNendo.value}"
										style="#{pc_PHkd0101.propSotNendo.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblSotGakki"
										value="#{pc_PHkd0101.propSotGakki.labelName}"
										style="#{pc_PHkd0101.propSotGakki.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlSotGakki"
										value="#{pc_PHkd0101.propSotGakki.value}"
										style="#{pc_PHkd0101.propSotGakki.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblGakusekiCd"
										value="#{pc_PHkd0101.propGakusekiCd.labelName}"
										style="#{pc_PHkd0101.propGakusekiCd.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlGakusekiCd"
										value="#{pc_PHkd0101.propGakusekiCd.value}"
										style="#{pc_PHkd0101.propGakusekiCd.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblGakuseiName"
										value="#{pc_PHkd0101.propGakuseiName.labelName}"
										style="#{pc_PHkd0101.propGakuseiName.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlGakuseiName"
										value="#{pc_PHkd0101.propGakuseiName.value}"
										style="#{pc_PHkd0101.propGakuseiName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyHighUnitId"
										value="#{pc_PHkd0101.propEdyHighUnitId.labelName}"
										style="#{pc_PHkd0101.propEdyHighUnitId.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyHighUnitId"
										value="#{pc_PHkd0101.propEdyHighUnitId.value}"
										style="#{pc_PHkd0101.propEdyHighUnitId.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyEmId"
										value="#{pc_PHkd0101.propEdyEmId.labelName}"
										style="#{pc_PHkd0101.propEdyEmId.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyEmId"
										value="#{pc_PHkd0101.propEdyEmId.value}"
										style="#{pc_PHkd0101.propEdyEmId.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyTrnNo"
										value="#{pc_PHkd0101.propEdyTrnNo.labelName}"
										style="#{pc_PHkd0101.propEdyTrnNo.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyTrnNo"
										value="#{pc_PHkd0101.propEdyTrnNo.value}"
										style="#{pc_PHkd0101.propEdyTrnNo.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyCardTrnNo"
										value="#{pc_PHkd0101.propEdyCardTrnNo.labelName}"
										style="#{pc_PHkd0101.propEdyCardTrnNo.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyCardTrnNo"
										value="#{pc_PHkd0101.propEdyCardTrnNo.value}"
										style="#{pc_PHkd0101.propEdyCardTrnNo.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyNo"
										value="#{pc_PHkd0101.propEdyNo.labelName}"
										style="#{pc_PHkd0101.propEdyNo.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyNo"
										value="#{pc_PHkd0101.propEdyNo.value}"
										style="#{pc_PHkd0101.propEdyNo.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyTrnKingaku"
										value="#{pc_PHkd0101.propEdyTrnKingaku.labelName}"
										style="#{pc_PHkd0101.propEdyTrnKingaku.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyTrnKingaku"
										value="#{pc_PHkd0101.propEdyTrnKingaku.value}"
										style="#{pc_PHkd0101.propEdyTrnKingaku.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyBfZangaku"
										value="#{pc_PHkd0101.propEdyBfZangaku.labelName}"
										style="#{pc_PHkd0101.propEdyBfZangaku.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyBfZangaku"
										value="#{pc_PHkd0101.propEdyBfZangaku.value}"
										style="#{pc_PHkd0101.propEdyBfZangaku.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyAfZangaku"
										value="#{pc_PHkd0101.propEdyAfZangaku.labelName}"
										style="#{pc_PHkd0101.propEdyAfZangaku.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyAfZangaku"
										value="#{pc_PHkd0101.propEdyAfZangaku.value}"
										style="#{pc_PHkd0101.propEdyAfZangaku.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyAlarmKbn"
										value="#{pc_PHkd0101.propEdyAlarmKbn.labelName}"
										style="#{pc_PHkd0101.propEdyAlarmKbn.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyAlarmKbn"
										value="#{pc_PHkd0101.propEdyAlarmKbn.value}"
										style="#{pc_PHkd0101.propEdyAlarmKbn.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdyAlarmKekkaKbn"
										value="#{pc_PHkd0101.propEdyAlarmKekkaKbn.labelName}"
										style="#{pc_PHkd0101.propEdyAlarmKekkaKbn.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdyAlarmKekkaKbn"
										value="#{pc_PHkd0101.propEdyAlarmKekkaKbn.value}"
										style="#{pc_PHkd0101.propEdyAlarmKekkaKbn.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblTaioDate"
										value="#{pc_PHkd0101.propTaioDate.labelName}"
										style="#{pc_PHkd0101.propTaioDate.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlTaioDate"
										value="#{pc_PHkd0101.propTaioDate.value}"
										style="#{pc_PHkd0101.propTaioDate.style}"></h:outputText></TD>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblEdySimeDate"
										value="#{pc_PHkd0101.propEdySimeDate.labelName}"
										style="#{pc_PHkd0101.propEdySimeDate.labelStyle}"></h:outputText></TH>
									<TD width="250"><h:outputText styleClass="outputText"
										id="htmlEdySimeDate"
										value="#{pc_PHkd0101.propEdySimeDate.value}"
										style="#{pc_PHkd0101.propEdySimeDate.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<br>
						<TABLE border="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD align="center" width="800"><hx:commandExButton type="submit" value="ＯＫ"
										styleClass="commandExButton_dat" id="ok"
										action="#{pc_PHkd0101.doCloseDispAction}"></hx:commandExButton>
									</TD>
								</TR>
						</TBODY>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/childFooter.jsp" />
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

