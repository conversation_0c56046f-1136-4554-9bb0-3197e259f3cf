<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaz00902T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>資産分類登録更新削除</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function confirmOk() {
	// 登録
	var regStatus = document.getElementById('form1:htmlExecutableRegister').value;
	if (regStatus == "1") {
		// 減価償却方法が「対象外」の場合決算時期区分に設定した内容は登録されません。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableRegister').value = "2";
		indirectClick('register');
	
	} else if (regStatus == "2") {
		// 減価償却方法が「対象外」の場合償却開始時期区分に設定した内容は登録されません。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableRegister').value = "3";
		indirectClick('register');
		
	} else if (regStatus == "3") {
		// 減価償却方法が「対象外」の場合残存価額区分に設定した内容は登録されません。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableRegister').value = "4";
		indirectClick('register');
		
	} else if (regStatus == "4") {
		// 同じ資産分類集計の資産分類集計名称も空白で更新されます。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableRegister').value = "5";
		indirectClick('register');
	}
	
	// 更新
	var updStatus = document.getElementById('form1:htmlExecutableUpdate').value;
	if (updStatus == "1") {
		// 減価償却方法が「対象外」の場合決算時期区分に設定した内容は登録されません。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableUpdate').value = "2";
		indirectClick('update');
	
	} else if (updStatus == "2") {
		// 減価償却方法が「対象外」の場合償却開始時期区分に設定した内容は登録されません。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableUpdate').value = "3";
		indirectClick('update');
		
	} else if (updStatus == "3") {
		// 減価償却方法が「対象外」の場合残存価額区分に設定した内容は登録されません。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableUpdate').value = "4";
		indirectClick('update');
		
	} else if (updStatus == "4") {
		// 同じ資産分類集計の資産分類集計名称も空白で更新されます。
		// よろしいですか？
		document.getElementById('form1:htmlExecutableUpdate').value = "5";
		indirectClick('update');
	}
	
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableRegister').value = "0";
	document.getElementById('form1:htmlExecutableUpdate').value = "0";
}
</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kaz00902T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
	<hx:commandExButton
		type="submit"
		value="閉じる"
		styleClass="commandExButton"
		id="closeDisp"
		action="#{pc_Kaz00902T01.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText
		styleClass="outputText"
		id="htmlFuncId"
		value="#{pc_Kaz00902T01.funcId}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlScrnName"
		value="#{pc_Kaz00902T01.screenName}">
	</h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err">
	<LEGEND>エラーメッセージ</LEGEND>
	<h:outputText
		id="message"
		value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText"
		escape="false">
	</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<hx:commandExButton
		type="submit"
		value="戻る"
		styleClass="commandExButton"
		id="returnDisp"
		action="#{pc_Kaz00902T01.doReturnDispAction}">
	</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="830">
		<TR>
			<TH width="128" class="v_a">
				<h:outputText
					styleClass="outputText"
					id="lblSsnBnrCd"
					value="#{pc_Kaz00902T01.kaz00902.propSsnBnrCd.labelName}"
					style="#{pc_Kaz00902T01.kaz00902.propSsnBnrCd.labelStyle}">
				</h:outputText>
			</TH>
			<TD width="">
				<h:inputText
					styleClass="inputText"
					id="htmlSsnBnrCd"
					maxlength="#{pc_Kaz00902T01.kaz00902.propSsnBnrCd.maxLength}"
					value="#{pc_Kaz00902T01.kaz00902.propSsnBnrCd.stringValue}"
					size="5"
					disabled="#{pc_Kaz00902T01.kaz00902.propSsnBnrCd.disabled}"
					readonly="#{pc_Kaz00902T01.kaz00902.propSsnBnrCd.readonly}"
					style="#{pc_Kaz00902T01.kaz00902.propSsnBnrCd.style}">
				</h:inputText>
			</TD>
		</TR>
	</TABLE>
	<TABLE border="0" cellpadding="0" cellspacing="0" width="850" style="margin-top: 10px;">
		<TR>
			<TD height="27" align="left">
				<hx:commandExButton
					type="submit"
					value="資産分類情報"
					style="width:170px"
					styleClass="tab_head_on"
					id="lblSsnBnr"
				></hx:commandExButton><hx:commandExButton
					type="submit"
					value="科目情報"
					style="width:170px"
					styleClass="tab_head_off"
					id="lblSsnKmk"
					action="#{pc_Kaz00902T01.doLblKmkJyohoAction}"
				></hx:commandExButton>
			</TD>
		</TR>
		<TR>
			<TD>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="850" height="340" class="tab_body">
					<TR>
						<TD align="center" valign="top">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="830" style="margin-top: 10px;">
								<TR>
									<TH width="" class="v_b">
										<h:outputText
											styleClass="outputText"
											id="lblSsnBnrName"
											value="#{pc_Kaz00902T01.propSsnBnrName.labelName}"
											style="#{pc_Kaz00902T01.propSsnBnrName.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="">
										<h:inputText
											styleClass="inputText"
											id="htmlSsnBnrName"
											maxlength="#{pc_Kaz00902T01.propSsnBnrName.maxLength}"
											value="#{pc_Kaz00902T01.propSsnBnrName.stringValue}"
											size="100"
											style="#{pc_Kaz00902T01.propSsnBnrName.style}">
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_c">
										<h:outputText
											styleClass="outputText"
											id="lblSsnBnrNameRyak"
											value="#{pc_Kaz00902T01.propSsnBnrNameRyak.labelName}"
											style="#{pc_Kaz00902T01.propSsnBnrNameRyak.labelStyle}">
										</h:outputText>
									</TH>
									<TD>
										<h:inputText
											styleClass="inputText"
											id="htmlSsnBnrNameRyak"
											maxlength="#{pc_Kaz00902T01.propSsnBnrNameRyak.maxLength}"
											value="#{pc_Kaz00902T01.propSsnBnrNameRyak.stringValue}"
											size="20"
											style="#{pc_Kaz00902T01.propSsnBnrNameRyak.style}">
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_d">
										<h:outputText
											styleClass="outputText"
											id="lblSsnKbn"
											value="#{pc_Kaz00902T01.propSsnKbn.labelName}"
											style="#{pc_Kaz00902T01.propSsnKbn.labelStyle}">
										</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu
											styleClass="selectOneMenu"
											id="htmlSsnKbn"
											value="#{pc_Kaz00902T01.propSsnKbn.value}">
											<f:selectItems value="#{pc_Kaz00902T01.propSsnKbn.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_e">
										<h:outputText
											styleClass="outputText"
											id="lblShokyakuTaishoKbn"
											value="#{pc_Kaz00902T01.propShokyakuTaishoKbn.labelName}"
											style="#{pc_Kaz00902T01.propShokyakuTaishoKbn.labelStyle}">
										</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu
											styleClass="selectOneMenu"
											id="htmlShokyakuTaishoKbn"
											value="#{pc_Kaz00902T01.propShokyakuTaishoKbn.value}">
											<f:selectItems value="#{pc_Kaz00902T01.propShokyakuTaishoKbn.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_f">
										<h:outputText
											styleClass="outputText"
											id="lblKessanJikiKbn"
											value="#{pc_Kaz00902T01.propKessanJikiKbn.labelName}">
										</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu
											styleClass="selectOneMenu"
											id="htmlKessanJikiKbn"
											value="#{pc_Kaz00902T01.propKessanJikiKbn.value}">
											<f:selectItems value="#{pc_Kaz00902T01.propKessanJikiKbn.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_g">
										<h:outputText
											styleClass="outputText"
											id="lblShokyakuStartJikiKbn"
											value="#{pc_Kaz00902T01.propShokyakuStartJikiKbn.labelName}">
										</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu
											styleClass="selectOneMenu"
											id="htmlShokyakuStartJikiKbn"
											value="#{pc_Kaz00902T01.propShokyakuStartJikiKbn.value}">
											<f:selectItems value="#{pc_Kaz00902T01.propShokyakuStartJikiKbn.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_a">
										<h:outputText
											styleClass="outputText"
											id="lblZanzonKagakuKbn"
											value="#{pc_Kaz00902T01.propZanzonKagakuKbn.labelName}">
										</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu
											styleClass="selectOneMenu"
											id="htmlZanzonKagakuKbn"
											value="#{pc_Kaz00902T01.propZanzonKagakuKbn.value}">
											<f:selectItems value="#{pc_Kaz00902T01.propZanzonKagakuKbn.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblBiboKagakuKbn"
										value="#{pc_Kaz00902T01.propBiboKagakuKbn.labelName}">
									</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlBiboKagakuKbn"
										value="#{pc_Kaz00902T01.propBiboKagakuKbn.value}"
										disabled="#{pc_Kaz00902T01.propBiboKagakuKbn.disabled}"
										readonly="#{pc_Kaz00902T01.propBiboKagakuKbn.readonly}">
										<f:selectItems
											value="#{pc_Kaz00902T01.propBiboKagakuKbn.list}" />
									</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_a">
										<h:outputText styleClass="outputText"
										id="lblShokyakuShiwakeShukeiKbn" value="仕訳集計区分（減価償却）">
									</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlShokyakuShiwakeShukeiKbn"
										value="#{pc_Kaz00902T01.propShokyakuShiwakeShukeiKbn.value}"
										readonly="#{pc_Kaz00902T01.propShokyakuShiwakeShukeiKbn.readonly}"
										disabled="#{pc_Kaz00902T01.propShokyakuShiwakeShukeiKbn.disabled}">
										<f:selectItems
											value="#{pc_Kaz00902T01.propShokyakuShiwakeShukeiKbn.list}" />
									</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_a">
										<h:outputText styleClass="outputText"
										id="lblJokyakuShiwakeShukeiKbn"
										value="#{pc_Kaz00902T01.propJokyakuShiwakeShukeiKbn.labelName}"
										style="#{pc_Kaz00902T01.propJokyakuShiwakeShukeiKbn.labelStyle}">
									</h:outputText>
									</TH>
									<TD>
										<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlJokyakuShiwakeShukeiKbn"
										value="#{pc_Kaz00902T01.propJokyakuShiwakeShukeiKbn.value}"
										disabled="#{pc_Kaz00902T01.propJokyakuShiwakeShukeiKbn.disabled}"
										readonly="#{pc_Kaz00902T01.propJokyakuShiwakeShukeiKbn.readonly}">
										<f:selectItems
											value="#{pc_Kaz00902T01.propJokyakuShiwakeShukeiKbn.list}" />
									</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH class="v_a">
										<h:outputText styleClass="outputText"
										id="lblJokyakujiShokyakuKikan"
										value="#{pc_Kaz00902T01.propJokyakujiShokyakuKikan.labelName}"
										style="#{pc_Kaz00902T01.propJokyakujiShokyakuKikan.labelStyle}">
									</h:outputText>
									</TH>
									<TD>
									<h:inputText styleClass="inputText"
										id="htmlJokyakujiShokyakuKikan" size="4"
										value="#{pc_Kaz00902T01.propJokyakujiShokyakuKikan.stringValue}"
										style="#{pc_Kaz00902T01.propJokyakujiShokyakuKikan.style}"
										maxlength="#{pc_Kaz00902T01.propJokyakujiShokyakuKikan.maxLength}">
										
									</h:inputText>
									<h:outputText styleClass="outputText"
										id="commentJokyakujiShokyakuKikan"
										value="※設定期間(月日)以降に除却された資産は減価償却を行います(年次償却のみ有効)">
									</h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_a">
										<h:outputText styleClass="outputText"
											id="lblNengakuKeijo"
											value="#{pc_Kaz00902T01.propNengakuKeijo.labelName}"
											style="#{pc_Kaz00902T01.propNengakuKeijo.labelStyle}">
										</h:outputText>
									</TH>
									<TD>
										<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
											id="htmlNengakuKeijo"
											style="#{pc_Kaz00902T01.propNengakuKeijo.style}"
											value="#{pc_Kaz00902T01.propNengakuKeijo.checked}">
										</h:selectBooleanCheckbox>減価償却を年額で計上します　(月次償却のみ有効)
									</TD>
								</TR>
								<TR style="${pc_Kaz00902T01.trRevo}">
								<TH class="v_c">
										<h:outputText
											styleClass="outputText"
											id="lblSsnBnrSyukeiName"
											value="#{pc_Kaz00902T01.propSsnBnrSyukeiName.labelName}"
											style="#{pc_Kaz00902T01.propSsnBnrSyukeiName.labelStyle}">
										</h:outputText>
									</TH>
									<TD>
										<h:inputText styleClass="inputText" id="htmlSsnBnrSyukeiName"
										value="#{pc_Kaz00902T01.propSsnBnrSyukeiName.stringValue}"
										size="20" style="#{pc_Kaz00902T01.propSsnBnrSyukeiName.style}"
										disabled="#{pc_Kaz00902T01.propSsnBnrSyukeiName.disabled}"
										readonly="#{pc_Kaz00902T01.propSsnBnrSyukeiName.readonly}"
										rendered="#{pc_Kaz00902T01.propSsnBnrSyukeiName.rendered}"
										maxlength="#{pc_Kaz00902T01.propSsnBnrSyukeiName.maxLength}">
									</h:inputText>
									<h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox"
										id="htmlSyukeihyoTaishoFlg"
										disabled="#{pc_Kaz00902T01.propSyukeihyoTaishoFlg.disabled}"
										value="#{pc_Kaz00902T01.propSyukeihyoTaishoFlg.checked}">
									</h:selectBooleanCheckbox>集計表の出力対象
									</TD>
								</TR>
								<TR>
									<TH class="v_b">
										<h:outputText
											styleClass="outputText"
											id="lblBiko"
											value="#{pc_Kaz00902T01.propBiko.labelName}">
										</h:outputText>
									</TH>
									<TD>
									<h:inputTextarea styleClass="inputTextarea" id="htmlBiko"
										cols="80" value="#{pc_Kaz00902T01.propBiko.stringValue}"
										style="#{pc_Kaz00902T01.propBiko.style}"
										rendered="#{pc_Kaz00902T01.propBiko.rendered}"
										readonly="#{pc_Kaz00902T01.propBiko.readonly}"
										disabled="#{pc_Kaz00902T01.propBiko.disabled}"></h:inputTextarea></TD>
								</TR>
								<TR>
									<TH class="v_c">
										<h:outputText
											styleClass="outputText"
											id="lblYukoMukoFlg"
											value="有効無効">
										</h:outputText>
									</TH>
									<TD>
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio"
											id="htmlYukoMukoFlg"
											value="#{pc_Kaz00902T01.propYukoMukoFlg.stringValue}">
											<f:selectItem itemValue="1" itemLabel="有効" />
											<f:selectItem itemValue="0" itemLabel="無効" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TABLE>
						</TD>
					</TR>
				</TABLE>
				<f:facet name="back">
				</f:facet>
				<f:facet name="next">
				</f:facet>
				<f:facet name="finish">
				</f:facet>
				<f:facet name="cancel">
				</f:facet>
			</TD>
		</TR>
	</TABLE>
	<TABLE class="button_bar" width="850" style="margin-top: 10px;">
		<TR>
			<TD>
				<hx:commandExButton
					type="submit"
					value="登録"
					styleClass="commandExButton_dat"
					id="register"
					action="#{pc_Kaz00902T01.doRegisterAction}"
					confirm="#{msg.SY_MSG_0002W}"
					rendered="#{pc_Kaz00902T01.propRegister.rendered}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="更新"
					styleClass="commandExButton_dat"
					id="update"
					action="#{pc_Kaz00902T01.doUpdateAction}"
					rendered="#{pc_Kaz00902T01.propUpdate.rendered}"
					confirm="#{msg.SY_MSG_0003W}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="削除"
					styleClass="commandExButton_dat"
					id="delete"
					action="#{pc_Kaz00902T01.doDeleteAction}"
					rendered="#{pc_Kaz00902T01.propDelete.rendered}"
					confirm="#{msg.SY_MSG_0004W}">
				</hx:commandExButton>
				<hx:commandExButton
					type="submit"
					value="クリア"
					styleClass="commandExButton_etc"
					id="clear"
					action="#{pc_Kaz00902T01.doClearAction}">
				</hx:commandExButton>
			</TD>
		</TR>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/footer.jsp" />
<h:inputHidden id="htmlExecutableRegister"
	value="#{pc_Kaz00902T01.kaz00902.propExecutableRegister.stringValue}">
</h:inputHidden>
<h:inputHidden id="htmlExecutableUpdate"
	value="#{pc_Kaz00902T01.kaz00902.propExecutableUpdate.stringValue}">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

