<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/PKaa0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.system.co.util.UtilCogFormatObject" %>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>決裁パターン検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_Select(thisObj, thisEvent) {
//選択ボタン押下時
	try {
		var SEP = "<%= UtilCogFormatObject.JOIN_STR %>";
		var retIdChotNendo = document.getElementById("form1:htmlHiddenChotNendoId").value;
		var retIdYsnTCd = document.getElementById("form1:htmlHiddenYsnTCdId").value;
		var retIdKsaPaternNo = document.getElementById("form1:htmlHiddenKsaPaternNoId").value;
		var buttonId = document.getElementById("form1:htmlHiddenButtonId").value;
		var focusId = document.getElementById("form1:htmlHiddenFocusId").value;
		var listBox = document.getElementById("form1:htmlKsaiPatanList");
		var noSelectMsg = document.getElementById("form1:htmlNoSelectMsgHidden").value;

		if (window.opener && listBox != null) {
			var indx = listBox.selectedIndex;
			var select = 0;
			if (indx > 0) {
				flapWindow(window);
				select++;
				
				var values = strSprit(listBox.options[indx].value, SEP);
				var target = null;
				for (j = 0; j < values.length; j++) {
					switch (j) {
						case 0:			//調達年度
							target = getElementByIdEx(window.opener.document, retIdChotNendo);
							break;
						case 1:			//予算単位コード
							target = getElementByIdEx(window.opener.document, retIdYsnTCd);
							break;
						case 2:			//決裁パターンNo
							target = getElementByIdEx(window.opener.document, retIdKsaPaternNo);
							break;
						default:
							target = null;
							break;
					}
					if (target != null) {
						setValue(target, values[j]);
						riseEvent(target);
					}
				} 
			}

			if (select > 0) {
			
				//フォーカスを設定する項目ID
				var targets = new Array();
				//ボタン起動あり
				if(buttonId != null && buttonId != '') {
					var button = getElementByIdEx(window.opener.document, buttonId);
					riseEvent(button);
					targets.push(getElementByIdEx(window.opener.document, retIdKsaPaternNo));
				} else {
				//ボタン起動無し（決裁パターンＮＯのロストフォーカス）
					window.opener.document.getElementById(retIdKsaPaternNo).onblur();
					if(focusId != null && focusId != '') {
						//フォーカスする項目IDが設定されている場合
						targets.push(getElementByIdEx(window.opener.document, focusId));
					} else {
						targets.push(getElementByIdEx(window.opener.document, retIdKsaPaternNo));
					}	
				}

				windowClose(targets);
			} else {
				reflapWindow(window);
				setErrMsg(noSelectMsg);
			}
		} else {
			throw "";
		}
	} catch(e) {
		window.moveTo(0, 0);
		alert("呼び出し元画面に値を返せません。");
	}
	return false;
}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PKaa0101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
	<hx:commandExButton
		type="submit"
		value="閉じる"
		styleClass="commandExButton"
		id="closeDisp"
		action="#{pc_PKaa0101.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PKaa0101.funcId}"></h:outputText>
	<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
	<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PKaa0101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err">
	<LEGEND>エラーメッセージ</LEGEND>
	<h:outputText
		id="message"
		value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText"
		escape="false">
	</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウト崩れ防止の為、全角スペースを配置-->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->	
<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
<TBODY>
	<TR>
		<TD>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="732">
			<TBODY>
				<TR>
					<TH class="v_a" width="162"><h:outputText
								styleClass="outputText" id="lblChotNendo"
								value="#{pc_PKaa0101.propChotNendo.name}"
								style="#{pc_PKaa0101.propChotNendo.labelStyle}"></h:outputText></TH>
					<TD><h:outputText styleClass="outputText"
								id="htmlChotNendo"
								value="#{pc_PKaa0101.propChotNendo.integerValue}"
								style="#{pc_PKaa0101.propChotNendo.style}">
								<f:convertNumber pattern="####" />
							</h:outputText></TD>
				</TR>
				<TR>
					<TH class="v_b"><h:outputText styleClass="outputText"
						id="lblYsnTCdList"
						value="#{pc_PKaa0101.propYsnTCdList.name}"
						style="#{pc_PKaa0101.propYsnTCdList.labelStyle}"></h:outputText></TH>
					<TD><h:selectOneMenu styleClass="selectOneMenu"
						id="htmlYsnTCdList"
						disabled="#{pc_PKaa0101.propYsnTCdList.disabled}"
						readonly="#{pc_PKaa0101.propYsnTCdList.readonly}"
						value="#{pc_PKaa0101.propYsnTCdList.value}" tabindex="1"
						style="width: 420px">
						<f:selectItems value="#{pc_PKaa0101.propYsnTCdList.list}" />
					</h:selectOneMenu></TD>	
				</TR>
				<TR>	
					<TH class="v_c" >
						<h:outputText id="lblKsaPaternNo" styleClass="outputText"
								style="#{pc_PKaa0101.propKsaPaternNo.labelStyle}"
								value="#{pc_PKaa0101.propKsaPaternNo.labelName}">
							</h:outputText>
					</TH>
					<TD >
						<h:inputText id="htmlKsaPaternNo" styleClass="inputText"
									style="#{pc_PKaa0101.propKsaPaternNo.style}"
									value="#{pc_PKaa0101.propKsaPaternNo.stringValue}" size="2"
									maxlength="#{pc_PKaa0101.propKsaPaternNo.maxLength}" 
									tabindex="2">
								</h:inputText>
					</TD>			
				</TR>
				<TR>
					<TH class="v_d" >
						<h:outputText styleClass="outputText" id="lblKsaPaternName"
								value="#{pc_PKaa0101.propKsaPaternName.labelName}"
								style="#{pc_PKaa0101.propKsaPaternName.labelStyle}">
							</h:outputText>
					</TH>
					<TD >
						<TABLE class="clear_border">
							<TR>
								<TD>
									<h:inputText id="htmlKsaPaternName" styleClass="inputText"
											style="#{pc_PKaa0101.propKsaPaternName.style}" size="60"
											value="#{pc_PKaa0101.propKsaPaternName.stringValue}"
											maxlength="#{pc_PKaa0101.propKsaPaternName.maxLength}" 
											tabindex="3">
										</h:inputText>
								</TD>
								<TD><h:selectOneRadio 
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlKsaPaternNameFindType" 
											value="#{pc_PKaa0101.propKsaPaternNameFindType.stringValue}" 
											style="text-align: left" 
											tabindex="4"><f:selectItems 
											value="#{pc_PKaa0101.propKsaPaternNameFindType.list}" />
										</h:selectOneRadio>
									</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TH class="v_e" >
						<h:outputText
							styleClass="outputText"
							id="lblKingakuMax"
							value="#{pc_PKaa0101.propKingakuMaxFrom.labelName}"
							style="#{pc_PKaa0101.propKingakuMaxFrom.labelStyle}">
						</h:outputText>
					</TH>
						<TD>
							<h:inputText id="htmlKingakuMaxFrom" styleClass="inputText"
									style="padding-right: 3px; text-align: right;#{pc_PKaa0101.propKingakuMaxFrom.style}"
									value="#{pc_PKaa0101.propKingakuMaxFrom.stringValue}" size="17"
									tabindex="5">
									<hx:inputHelperAssist errorClass="inputText_Error" />
								</h:inputText>
							<h:outputText styleClass="outputText" value="　～" id="lblKara1">
								</h:outputText>
							<h:inputText id="htmlKingakuMaxTo" styleClass="inputText"
									style="padding-right: 3px; text-align: right;#{pc_PKaa0101.propKingakuMaxTo.style}"
									value="#{pc_PKaa0101.propKingakuMaxTo.stringValue}" size="17"
									tabindex="6">
									<hx:inputHelperAssist errorClass="inputText_Error" />
								</h:inputText>
						</TD>
				</TR>
				<TR>
					<TH class="v_f"><h:outputText styleClass="outputText"
									id="lblRingiFlg"
									value="#{pc_PKaa0101.propRingiFlg.name}"
									style="#{pc_PKaa0101.propRingiFlg.labelStyle}">
								</h:outputText></TH>
					<TD>
						<h:selectManyCheckbox styleClass="selectManyCheckbox setWidth"
									id="htmlRingiFlg"
									value="#{pc_PKaa0101.propRingiFlg.value}"
									disabledClass="selectManyCheckbox_Disabled" tabindex="7"
									readonly="#{pc_PKaa0101.propRingiFlg.readonly}"
									disabled="#{pc_PKaa0101.propRingiFlg.disabled}">
									<f:selectItems
										value="#{pc_PKaa0101.propRingiFlg.list}" />
								</h:selectManyCheckbox>
					</TD>
				</TR>
				<TR>
					<TH class="v_f"><h:outputText styleClass="outputText"
									id="lblYukoMukoFlg"
									value="#{pc_PKaa0101.propYukoMukoFlg.name}"
									style="#{pc_PKaa0101.propYukoMukoFlg.labelStyle}">
								</h:outputText></TH>
					<TD>
						<h:selectManyCheckbox styleClass="selectManyCheckbox setWidth"
									id="htmlYukoMukoFlg"
									value="#{pc_PKaa0101.propYukoMukoFlg.value}"
									disabledClass="selectManyCheckbox_Disabled" tabindex="8"
									readonly="#{pc_PKaa0101.propYukoMukoFlg.readonly}"
									disabled="#{pc_PKaa0101.propYukoMukoFlg.disabled}">
									<f:selectItems
										value="#{pc_PKaa0101.propYukoMukoFlg.list}" />
								</h:selectManyCheckbox>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
			<TABLE width="732" style="margin-top:10px;" class="button_bar">
				<TR>
					<TD>
						<hx:commandExButton
							type="submit"
							value="検索"
							styleClass="commandExButton_dat"
							id="search"
							style="#{pc_PKaa0101.propSearch.style}"
							action="#{pc_PKaa0101.doSearchAction}" tabindex="9">
						</hx:commandExButton>
						<hx:commandExButton
							type="submit"
							value="クリア"
							styleClass="commandExButton_etc"
							id="clear"
							style="#{pc_PKaa0101.propClear.style}"
							action="#{pc_PKaa0101.doClearAction}" tabindex="10">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
		</TD>
	</TR>
	<TR>
		<TD>
		<HR noshade>
		</TD>
	</TR>
	<TR>
		<TD>
			<TABLE  border="0" cellpadding="0" cellspacing="0" width="100%">
				<TR>
					<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0">
						<TBODY>
							<TR>
								<TD>
									<TABLE  border="0" cellpadding="0" cellspacing="0">
										<TR>
											<TD style="text-align:right">
												<h:outputText
													styleClass="outputText"
													id="lblListCount"
													value="#{pc_PKaa0101.propListCount.stringValue}">
												</h:outputText>
												<h:outputText
													styleClass="outputText"
													id="lblKen"
													value="件">
												</h:outputText>
											</TD>
										</TR>
										<TR>
										<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">						
												<TR>
											<TD width="34%" style="text-align:left">
												<h:outputText styleClass="outputText" id="lblYsnTCdListHeader"
													value="予算単位"
													style="margin-left: 5px">
												</h:outputText>							
											</TD>
											<TD width="48%" style="text-align:left">
												<h:outputText styleClass="outputText" id="lblKsaPaternListHeader"
													value="決裁パターン"
													style="margin-left: 12px">
												</h:outputText>
											</TD>
											<TD width="10%" style="text-align:left">
												<h:outputText styleClass="outputText" id="lblKingakuMaxListHeader"
													value="上限金額"
													style="margin-left: 12px">
												</h:outputText>
											</TD>
											<TD width="8%" style="text-align:left">
												<h:outputText styleClass="outputText" id="lblYukoMukoListHeader"
													value="有効無効"
													style="margin-left: 5px">
												</h:outputText>
											</TD>
											</TR>
											</TABLE>
											</TD>
										</TR>
										<TR>
											<TD>
												<h:selectOneListbox
													value="#{pc_PKaa0101.propKsaiPatanList.stringValue}"
													disabled="#{pc_PKaa0101.propKsaiPatanList.disabled}"
													rendered="#{pc_PKaa0101.propKsaiPatanList.rendered}"
													id="htmlKsaiPatanList" styleClass="selectOneListbox"
													readonly="#{pc_PKaa0101.propKsaiPatanList.readonly}"
													style="height: 150px; width: 732px; font-size: 9pt;" size="5"
													ondblclick="return func_Select(this, event);" tabindex="11">
													<f:selectItems value="#{pc_PKaa0101.propKsaiPatanList.list}" />
												</h:selectOneListbox>							
											</TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
							<TR>
								<TD>
									<TABLE style="margin-top:10px;" width="723" class="button_bar">
										<TR>
											<TD>
												<hx:commandExButton
													type="button"
													styleClass="commandExButton_etc"
													id="select"
													value="選択"
													style="#{pc_PKaa0101.propSelect.style}"
													onclick="return func_Select(this, event);" tabindex="12">
												</hx:commandExButton>
												<hx:commandExButton
													type="submit"
													value="キャンセル"
													styleClass="commandExButton_etc"
													id="cancel"
													style="#{pc_PKaa0101.propCancel.style}"
													action="#{pc_PKaa0101.doCancelAction}" tabindex="13">
												</hx:commandExButton>
											</TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
						</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
		</TD>
	</TR>
</TBODY>
</TABLE>
</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/childFooter.jsp" />	
<h:inputHidden id="htmlNoSelectMsgHidden" 
	value="#{pc_PKaa0101.propNoSelectMsgHidden.stringValue}"></h:inputHidden> 
<h:inputHidden id="htmlExecutableSearch" 
	value="#{pc_PKaa0101.propExecutableSearch.integerValue}"><f:convertNumber /></h:inputHidden>	
<h:inputHidden value="htmlKingakuMaxFrom=###,###,###,##0;###,###,###,##0 | htmlKingakuMaxTo=###,###,###,##0;###,###,###,##0"
	id="htmlFormatNumberOption">
</h:inputHidden>
<h:inputHidden id="htmlHiddenYsnTCdId"
	value="#{pc_PKaa0101.propHiddenYsnTCdId.value}"></h:inputHidden>
<h:inputHidden id="htmlHiddenButtonId"
	value="#{pc_PKaa0101.propHiddenButtonId.value}"></h:inputHidden>
<h:inputHidden id="htmlHiddenChotNendoId"
	value="#{pc_PKaa0101.propHiddenChotNendoId.value}"></h:inputHidden>
<h:inputHidden id="htmlHiddenFocusId"
	value="#{pc_PKaa0101.propHiddenFocusId.value}"></h:inputHidden>
<h:inputHidden id="htmlHiddenKsaPaternNoId"
	value="#{pc_PKaa0101.propHiddenKsaPaternNoId.value}"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

