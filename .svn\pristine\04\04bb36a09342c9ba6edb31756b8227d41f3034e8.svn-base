<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KMB_KMK_HAI" name="科目明細配当" prod_id="KM" description="みなし入学年度、みなし入学学期、カリキュラム学科組織毎の科目情報を持ちます。">
<STATMENT><![CDATA[
KMB_KMK_HAI
]]></STATMENT>
<COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="科目を配当するみなし入学年度が設定されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="科目を配当する入学学期が設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="科目を配当するカリキュラム学科組織コードが設定されます。"/><COLUMN id="KAMOK_CD" name="科目コード" type="string" length="10" lengthDP="0" byteLength="10" description="配当対象となる科目のコードが設定されさます。"/><COLUMN id="KAMOK_BUNRUI_CD" name="成績用科目分類コード" type="string" length="6" lengthDP="0" byteLength="6" description="成績用科目分類を識別するコードが設定されます。"/><COLUMN id="MEISAI_NO" name="明細ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="科目の明細番号が設定されます。"/><COLUMN id="ROW_NO" name="並び順ＮＯ" type="number" length="5" lengthDP="0" byteLength="0" description="科目の並び順が設定されます。１からの連番となります。"/><COLUMN id="GPA_TSY_FLG" name="ＧＰＡ計算対象フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="ＧＰＡ計算でこの成績用科目分類を計算対象とするかどうかを表すフラグが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN>
</TABLE>
