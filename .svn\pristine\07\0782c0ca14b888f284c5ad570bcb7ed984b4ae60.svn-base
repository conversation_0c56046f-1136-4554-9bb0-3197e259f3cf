<%-- 
	学籍情報照会（自由設定）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob00302T15.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob00302T15.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cob00302T15.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
	value="閉じる" styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cob00302T15.doCloseDispAction}"></hx:commandExButton> <h:outputText
	styleClass="outputText" id="htmlFuncId"
	value="#{pc_Cob00302T15.funcId}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlLoginId"
	value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlScrnName"
	value="#{pc_Cob00302T15.screenName}"></h:outputText></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
	id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText></FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<hx:commandExButton
	type="submit" value="プロファイル"
	styleClass="commandExButton" id="referProfile"
	action="#{pc_Cob00302T01.cob00302.doReferProfileAction}">
</hx:commandExButton>
<hx:commandExButton type="submit"
	value="戻る" styleClass="commandExButton" id="returnDisp"
	action="#{pc_Cob00302T15.doReturnDispAction}"></hx:commandExButton></DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD width="870">
				<TABLE class="table" width="100%">
					<TBODY>
						<TR align="center" valign="middle">
							<TH nowrap class="v_a" width="190">
							<!--学籍番号 -->
								<h:outputText styleClass="outputText" id="lblDspGaksekiCd" 
								value="#{pc_Cob00302T01.cob00302.propGakusekiCd.labelName}"
								style="#{pc_Cob00302T01.cob00302.propGakusekiCd.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblGaksekiCd"
								value="#{pc_Cob00302T01.cob00302.propGakusekiCd.stringValue}"
								style="#{pc_Cob00302T01.cob00302.propGakusekiCd.style}"></h:outputText>
							</TD>
							<TH nowrap class="v_a" width="190">
							<!--学生氏名 -->
								<h:outputText styleClass="outputText" id="lblDspName" 
								value="#{pc_Cob00302T01.cob00302.propName.labelName}"
								style="#{pc_Cob00302T01.cob00302.propName.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblName"
								value="#{pc_Cob00302T01.cob00302.propName.displayValue}"
								title="#{pc_Cob00302T01.cob00302.propName.stringValue}"
								style="#{pc_Cob00302T01.cob00302.propName.style}"></h:outputText>
							</TD>
						</TR>
						<TR>
							<TH nowrap class="v_b" width="190">
							<!-- 学籍状況 -->
								<h:outputText styleClass="outputText" id="lblDspGakJokyo" 
								value="#{pc_Cob00302T01.cob00302.propGakJokyo.labelName}"
								style="#{pc_Cob00302T01.cob00302.propGakJokyo.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblGakJokyo" 
								value="#{pc_Cob00302T01.cob00302.propGakJokyo.stringValue}"></h:outputText></TD>
							<TH nowrap class="v_b" width="190">
							<!-- 旧学籍番号 -->
								<h:outputText styleClass="outputText" id="lblDspKyuGaksekiCd"
								value="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.labelName}"
								style="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblKyuGaksekiCd"
								value="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.stringValue}"></h:outputText></TD>
						</TR>
					</TBODY>
				</TABLE>
				<BR>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
					<TBODY>
						<TR>
							<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
								style="border-bottom-style: none; ">
								<TBODY>
									<TR>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T01" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameKihon.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T01Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T02" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameShozoku.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T02Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="*"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T03" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameSnkCls.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T03Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T04" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameAddr.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T04Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T05" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameKAddr.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T05Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T06" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameHsy.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T06Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T07" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameAtsk.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T07Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T08" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameRyugak.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T08Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T09" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameKyoin.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T09Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T10" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameIdo.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T10Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T11" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameNyushi.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T11Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T12" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameHantei.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T12Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T13" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameClub.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T13Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T14" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameSonota.stringValue}"
											action="#{pc_Cob00302T15.doTabCob00302T14Action}"></hx:commandExButton></TD>
										<TD class="tab_head_on" width="58px"><hx:commandExButton
											type="button" styleClass="tab_head_on" id="tabCob00301T15" 
											value="#{pc_Cob00302T01.cob00302.propTabNameJiyu.stringValue}"
											style="width: 100%"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
						<TR>
							<TD>
							<TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" 
								width="100%" style="border-top-style: none; ">
								<TBODY>
									<TR>
										<TD width="100%">
										<div style="height: 440px">
										<BR>
										<TABLE class="table" width="840">
											<TBODY>
												<TR>
													<TH nowrap class="v_c" width="180">
													<!-- 自由設定対象 -->
														<h:outputText styleClass="outputText" id="lblFreTsyCd"
														value="#{pc_Cob00302T15.propFreTsyCd.labelName}"
														style="#{pc_Cob00302T15.propFreTsyCd.labelStyle}"></h:outputText></TH>
													<TD width="300"><h:selectOneMenu styleClass="selectOneMenu"
																id="htmlFreTsyCd"
																disabled="#{pc_Cob00302T15.propFreTsyCd.disabled}"
																value="#{pc_Cob00302T15.propFreTsyCd.value}"
																style="width:400px;">
																<f:selectItems
																	value="#{pc_Cob00302T15.propFreTsyCd.list}" />
															</h:selectOneMenu>
													</TD>
													<TD class="clear_border"><hx:commandExButton type="submit" value="選択"
														styleClass="commandExButton" id="select"
														disabled="#{pc_Cob00302T15.propSelect.disabled}"
														action="#{pc_Cob00302T15.doSelectAction}"></hx:commandExButton>
														<hx:commandExButton	type="submit" value="解除"
														styleClass="commandExButton" id="unselect"
														disabled="#{pc_Cob00302T15.propUnselect.disabled}"
														action="#{pc_Cob00302T15.doUnselectAction}"></hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										<BR>
										<TABLE class="table" width="840">
											<TBODY>
												<TR>
													<TH nowrap class="v_d" width="180">
													<!-- データＮＯ -->
														<h:outputText styleClass="outputText" id="lblListNo"
														value="#{pc_Cob00302T15.propListNo.labelName}"></h:outputText></TH>
													<TD width="140" align="right" valign="middle"><hx:commandExButton
														type="submit" styleClass="commandExButton" 
														id="btnHead" value="#{pc_Cob00302T15.propToHead.name}"
														disabled="#{pc_Cob00302T15.propToHead.disabled}"
														action="#{pc_Cob00302T15.doBtnHeadAction}"
														style="height: 17px;
															width: 17px;
															border-left-width: 1px;
															border-right-width: 1px;
															border-bottom-width: 1px;
															border-top-width: 1px;
															cursor:pointer;
															text-align:center;
															border-bottom-color: #004d99;
															border-top-color: #C0C0FF; 
															border-left-color: #C0C0FF;
															border-right-color: #004d99;
															
															border-bottom-style: solid;
															border-left-style: solid;
															border-top-style: solid;
															border-right-style: solid;
															background-repeat:no-repeat;
															margin:0px 1px 0px 1px;"></hx:commandExButton>
														<hx:commandExButton type="submit" styleClass="commandExButton"
														id="btnBack" value="#{pc_Cob00302T15.propBack.name}"
														disabled="#{pc_Cob00302T15.propBack.disabled}"
														action="#{pc_Cob00302T15.doBtnBackAction}"
														style="height: 17px;
															width: 17px;
															border-left-width: 1px;
															border-right-width: 1px;
															border-bottom-width: 1px;
															border-top-width: 1px;
															cursor:pointer;
															text-align:center;
															border-bottom-color: #004d99;
															border-top-color: #C0C0FF; 
															border-left-color: #C0C0FF;
															border-right-color: #004d99;
															
															border-bottom-style: solid;
															border-left-style: solid;
															border-top-style: solid;
															border-right-style: solid;
															background-repeat:no-repeat;
															margin:0px 1px 0px 1px;"></hx:commandExButton>
														<h:inputText styleClass="inputText"
														id="htmlInputNo"  value="#{pc_Cob00302T15.propListNo.stringValue}"
														readonly="#{pc_Cob00302T15.propListNo.readonly}"
														maxlength="#{pc_Cob00302T15.propListNo.maxLength}" 
														style="#{pc_Cob00302T15.propListNo.style}" size="3"></h:inputText><hx:commandExButton
														type="submit" value="#{pc_Cob00302T15.propForword.name}"
														styleClass="commandExButton" id="btnForword"
														disabled="#{pc_Cob00302T15.propForword.disabled}"
														action="#{pc_Cob00302T15.doBtnForwordAction}"
														style="height: 17px;
															width: 17px;
															border-left-width: 1px;
															border-right-width: 1px;
															border-bottom-width: 1px;
															border-top-width: 1px;
															cursor:pointer;
															text-align:center;
															border-bottom-color: #004d99;
															border-top-color: #C0C0FF; 
															border-left-color: #C0C0FF;
															border-right-color: #004d99;
															
															border-bottom-style: solid;
															border-left-style: solid;
															border-top-style: solid;
															border-right-style: solid;
															background-repeat:no-repeat;
															margin:0px 1px 0px 1px;"></hx:commandExButton>
														<hx:commandExButton type="submit" styleClass="commandExButton"
														id="btnEnd" value="#{pc_Cob00302T15.propToEnd.name}"
														disabled="#{pc_Cob00302T15.propToEnd.disabled}"
														action="#{pc_Cob00302T15.doBtnEndAction}"
														style="height: 17px;
															width: 17px;
															border-left-width: 1px;
															border-right-width: 1px;
															border-bottom-width: 1px;
															border-top-width: 1px;
															cursor:pointer;
															text-align:center;
															border-bottom-color: #004d99;
															border-top-color: #C0C0FF; 
															border-left-color: #C0C0FF;
															border-right-color: #004d99;
															
															border-bottom-style: solid;
															border-left-style: solid;
															border-top-style: solid;
															border-right-style: solid;
															background-repeat:no-repeat;
															margin:0px 1px 0px 1px;"></hx:commandExButton>
													</TD>
													<TD class="clear_border" width="20"></TD>
													<TD class="clear_border" width="150" valign="middle">
														<h:inputText styleClass="inputText" id="htmlJumpInputNo"
																value="#{pc_Cob00302T15.propJumpListNo.stringValue}"
																disabled="#{pc_Cob00302T15.propJumpListNo.disabled}"
																maxlength="#{pc_Cob00302T15.propJumpListNo.maxLength}"
																style="#{pc_Cob00302T15.propJumpListNo.style}"
																size="3"></h:inputText>
														<hx:commandExButton
														type="submit" styleClass="commandExButton"
														id="jump" value="ジャンプ"
														disabled="#{pc_Cob00302T15.propJumpListNo.disabled}"
														action="#{pc_Cob00302T15.doListNoAction}"></hx:commandExButton>
													</TD>	
													<TD class="clear_border"><DIV align="right">
													<h:outputText styleClass="outputText" id="lblSearchedNoTitle"
																style="font-size: 8pt" value="データNo登録件数"></h:outputText><h:outputText
																styleClass="outputText" id="lblSearchedNo"
																style="font-size: 8pt"
																value="#{pc_Cob00302T15.propSearchedNo.stringValue}"></h:outputText><h:outputText
																styleClass="outputText" id="lblSearchedNoCnt"
																style="font-size: 8pt" value="件"></h:outputText>
													</DIV></TD>	
												</TR>
											</TBODY>
										</TABLE>
										<HR width="100%" class ="hr" noshade>
										<TABLE width="840" border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD align="right" nowrap class="outputText" width="100%"><h:outputText
														styleClass="outputText" id="lblListCnt" value="#{pc_Cob00302T15.propKmiSnfr.listCount}"></h:outputText>件</TD>
												</TR>
												<TR>
													<TD>
													<div class="listScroll" style="height:190px;"><h:dataTable
														border="1" cellpadding="2" cellspacing="0"
														headerClass="headerClass" footerClass="footerClass"
														columnClasses="columnClass1"
														rowClasses="#{pc_Cob00302T15.propKmiSnfr.rowClasses}"
														styleClass="meisai_scroll" id="table1"
														value="#{pc_Cob00302T15.propKmiSnfr.list}" var="varlist"
														style="text-align: center">
														<h:column id="column1">
															<f:facet name="header">
																<h:outputText styleClass="outputText" id="htmlFreKomokNo"
																	value="#{pc_Cob00302T15.propFreKomokNo.labelName}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText" id="lblFreKomokNo"
																value="#{varlist.freKomokNo}"></h:outputText>
															<f:attribute value="110" name="width" />
															<f:attribute value="center" name="align" />
															<f:attribute value="middle" name="valign" />
															<f:attribute
																value="text-align: right; vertical-align: middle"
																name="style" />
														</h:column>
														<h:column id="column2">
															<f:facet name="header">
																<h:outputText styleClass="outputText" id="htmlFreKomokName"
																	value="#{pc_Cob00302T15.propFreKomokName.labelName}"></h:outputText>
															</f:facet>
															<h:outputText styleClass="outputText" id="lblFreKomokName"
																value="#{varlist.freKomokName}"></h:outputText>
															<f:attribute value="220" name="width" />
															<f:attribute value="text-align: left; vertical-align: middle"
																name="style" />
														</h:column>
														<h:column id="column3">
															<f:facet name="header">
																<h:outputText styleClass="outputText" id="htmlFreValue"
																	value="#{pc_Cob00302T15.propFreValue.labelName}"
																	style="#{pc_Cob00302T15.propFreValue.labelStyle}"></h:outputText>
															</f:facet>
															<h:inputTextarea styleClass="inputTextarea"
																id="htmlFreValueArea" value="#{varlist.freValue}"
																onchange="return doFreValueAreaChange(this, event);"
																style="#{pc_Cob00302T15.propFreValue.style}; height: 28px; width :100% "
																readonly="#{pc_Cob00302T15.propFreValue.readonly}"></h:inputTextarea>
															<f:attribute value="490" name="width" />
															<f:attribute value="text-align: right" name="style" />
														</h:column>
													</h:dataTable>
													</div>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										</div>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>

<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--></DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/footer.jsp" />

</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
