<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghg00201.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Ghg00201.jsp</TITLE>

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<script language="JavaScript">
	function fncButtonActive(){
		var codeRegSgnSearch = null;
		var codeRegUnselect = null;
		var codeRegRegist = null;
		var codeRegExec = null;

		//選択(志願者検索用)ボタン
		codeRegSgnSearch = document.getElementById('form1:htmlActiveControlGakSearch').value;
		if(codeRegSgnSearch == 1){
			document.getElementById('form1:gakSearch').disabled = true;
			document.getElementById('form1:popGaksearch').disabled = true;
		}
		//解除ボタン
		codeRegUnselect = document.getElementById('form1:htmlActiveControlUnselect').value;
		if(codeRegUnselect == 1){
			document.getElementById('form1:unselect').disabled = true;
		}
		//新規登録ボタン
		codeRegRegist = document.getElementById('form1:htmlActiveControlRegist').value;
		if(codeRegRegist == 1){
			document.getElementById('form1:regist').disabled = true;
		}

		//スクロール位置保持
		changeScrollPosition('scroll', 'listScroll');
		
		//Ajaxにて取得したの値を保持するためにjavascriptでreadonlyを設定する
		document.getElementById('form1:htmlSgnName').readOnly = true;
		document.getElementById('form1:htmlNysSbtName').readOnly = true;
		document.getElementById('form1:htmlKiboSzks').readOnly = true;
		document.getElementById('form1:htmlNgakInfo').readOnly = true;
		document.getElementById('form1:htmlSzks').readOnly = true;

	}
	
	//志願者/受験者検索画面
	function openPNsc0101Window() {
		openPNsc0101("<%=com.jast.gakuen.rev.ns.PNsc0101.getWindowOpenOption()%>");
		return true;
	}

	//志願者情報取得Ajax呼び出し
	function ajaxGakusekiCd(thisObj) {
		//受験番号
		var jukenCd = document.getElementById('form1:htmlGakusekiCd');
		//学生氏名項目id
		var nameId = "form1:htmlSgnName";
		//所属学科組織項目id
		var szkGakkaId = "form1:htmlSzks";
		//入試種別項目id
		var nysSbtId = "form1:htmlNysSbtName";
		//第一希望学科項目id
		var kiboGakkaId = "form1:htmlKiboSzks";
		//入学情報項目id
		var nyugakId = "form1:htmlNgakInfo";
		
		//志願者情報取得Ajax呼び出し
		funcAjaxSgnName(thisObj, "", nameId);
		//入試種別取得Ajax呼び出し
		funcAjaxSgnNysSbtName(jukenCd, "", nysSbtId);
		//第一希望学科取得Ajax呼び出し
		funcAjaxSgnKiboSgksName(jukenCd, "", kiboGakkaId);
		//入学情報取得Ajax呼び出し
		funcAjaxSgnNyugakInfo(jukenCd, "", nyugakId);
		//所属学科組織名称取得Ajax呼び出し
		funcAjaxSgnSgksName(jukenCd, "", szkGakkaId);
		
		return true;
	}
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('gakSearch');
	}
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}

</script>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="fncButtonActive();">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghg00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Ghg00201.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghg00201.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghg00201.screenName}"></h:outputText>
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>

				<!--↓CONTENT↓-->
				<DIV class="head_button_area" >　
				</DIV>
				<DIV id="content">
					<DIV class="column" align="center">
						<TABLE width="900px">
							<TBODY>
								<TR>
									<TD width="100%">
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
															<TBODY>
																<TR>
																	<TH width="150px" nowrap class="v_a">
																		<!-- 入試年度 -->
																		<h:outputText styleClass="outputText" 
																			id="lblNysNendo"
																			value="#{pc_Ghg00201.propNysNendo.labelName}"
																			style="#{pc_Ghg00201.propNysNendo.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD width="*" nowrap valign="middle">
																		<h:outputText styleClass="outputText" 
																			id="htmlNysNendo"
																			value="#{pc_Ghg00201.propNysNendo.value}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TH width="150px" nowrap class="v_b">
																		<!-- 入試学期NO -->
																		<h:outputText styleClass="outputText" 
																			id="lblNysGakkiNo"
																			value="#{pc_Ghg00201.propNysGakkiNo.labelName}"
																			style="#{pc_Ghg00201.propNysGakkiNo.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD width="*" nowrap valign="middle">
																		<h:outputText styleClass="outputText" 
																			id="htmlNysGakkiNo"
																			value="#{pc_Ghg00201.propNysGakkiNo.value}">
																		</h:outputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD height="20px">
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="0" width="100%" cellpadding="0" cellspacing="0"
															class="table">
															<TBODY>
																<TR>
																	<TH align="center" nowrap class="v_c" width="150px">
																		<h:outputText styleClass="outputText" id="lblGhYear"
																			value="#{pc_Ghg00201.propGhYear.labelName}"
																			style="#{pc_Ghg00201.propGhYear.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD valign="middle" width="145px">
																		<h:inputText styleClass="inputText" id="htmlGhYear"
																			size="4" value="#{pc_Ghg00201.propGhYear.dateValue}"
																			style="#{pc_Ghg00201.propGhYear.style}"
																			maxlength="#{pc_Ghg00201.propGhYear.maxLength}" 
																			disabled="#{pc_Ghg00201.propGhYear.disabled}"
																			tabindex="1">
																			<hx:inputHelperAssist imeMode="inactive"
																				errorClass="inputText_Error" promptCharacter="_" />
																			<f:convertDateTime pattern="yyyy" />
																		</h:inputText>
																	</TD>
																	<TD rowspan="2" width="*">
																		<hx:commandExButton type="submit" value="選択"
																			styleClass="cmdBtn_dat_s" id="gakSearch"
																			action="#{pc_Ghg00201.doGakSearchAction}"
																			tabindex="4"
																			disabled="#{pc_Ghg00201.propActiveControlGakSearch.disabled}">
																		</hx:commandExButton>
																		<hx:commandExButton
																			type="submit" value="解除" styleClass="cmdBtn_etc_s"
																			id="unselect" action="#{pc_Ghg00201.doUnselectAction}"
																			disabled="#{pc_Ghg00201.propActiveControlUnselect.disabled}">
																		</hx:commandExButton>
																	</TD>
																</TR>
																<TR>
																	<TH width="150px" nowrap class="v_d">
																		<h:outputText styleClass="outputText"
																			id="lblGakusekiCd"
																			value="#{pc_Ghg00201.propGakusekiCd.labelName}"
																			style="#{pc_Ghg00201.propGakusekiCd.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD valign="middle" width="*">
																		<h:inputText
																			styleClass="inputText" id="htmlGakusekiCd"
																			style="#{pc_Ghg00201.propGakusekiCd.style}" size="10"
																			value="#{pc_Ghg00201.propGakusekiCd.stringValue}"
																			maxlength="#{pc_Ghg00201.propGakusekiCd.maxLength}"
																			disabled="#{pc_Ghg00201.propGakusekiCd.disabled}"
																			tabindex="2"
																			onblur="return ajaxGakusekiCd(this);">
																		</h:inputText>
																		<hx:commandExButton type="submit"
																			styleClass="commandExButton_search" id="popGaksearch"
																			disabled="#{pc_Ghg00201.propActiveControlGakSearch.disabled}"
																			action="#{pc_Ghg00201.doPopGaksearchAction}"
																			tabindex="3"
																			onclick="return openPNsc0101Window();">
																		</hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD height="20px">
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="0" width="100%" cellpadding="0" cellspacing="0"
															class="table">
															<TBODY>
																<TR>
																	<TH align="center" width="150px" nowrap class="v_e"><h:outputText
																		styleClass="outputText" id="lblSgnName"
																		value="#{pc_Ghg00201.propSgnName.name}"
																		style="#{pc_Ghg00201.propSgnName.labelStyle}"></h:outputText></TH>
																	<TD valign="middle" width="*"><h:inputText
																		styleClass="likeOutput" id="htmlSgnName"
																		style="#{pc_Ghg00201.propSgnName.style}"
																		size="100"
																		value="#{pc_Ghg00201.propSgnName.value}" tabindex="-1"></h:inputText></TD>
																</TR>
																<TR>
																	<TH align="center" nowrap class="v_f"><h:outputText
																		styleClass="outputText" id="lblNysSbtName"
																		value="#{pc_Ghg00201.propNysSbtName.name}"
																		style="#{pc_Ghg00201.propNysSbtName.labelStyle}"></h:outputText></TH>
																	<TD valign="middle"><h:inputText styleClass="likeOutput"
																		id="htmlNysSbtName"
																		style="#{pc_Ghg00201.propNysSbtName.style}"
																		size="60"
																		value="#{pc_Ghg00201.propNysSbtName.value}" tabindex="-1">
																	</h:inputText></TD>
																</TR>
																<TR>
																	<TH align="center" nowrap class="v_g"><h:outputText
																		styleClass="outputText" id="lblKiboSzks"
																		value="#{pc_Ghg00201.propKiboSzks.name}"
																		style="#{pc_Ghg00201.propKiboSzks.labelStyle}"></h:outputText></TH>
																	<TD valign="middle"><h:inputText styleClass="likeOutput"
																		id="htmlKiboSzks" style="#{pc_Ghg00201.propKiboSzks.style}"
																		size="120"
																		value="#{pc_Ghg00201.propKiboSzks.value}" tabindex="-1">
																	</h:inputText></TD>
																</TR>
																<TR>
																	<TH align="center" nowrap class="v_a"><h:outputText
																		styleClass="outputText" id="lblNgakInfo"
																		value="#{pc_Ghg00201.propNgakInfo.name}"
																		style="#{pc_Ghg00201.propNgakInfo.labelStyle}"></h:outputText></TH>
																	<TD valign="middle"><h:inputText styleClass="likeOutput"
																		id="htmlNgakInfo" style="#{pc_Ghg00201.propNgakInfo.style}"
																		value="#{pc_Ghg00201.propNgakInfo.value}" tabindex="-1">
																	</h:inputText></TD>
																</TR>
																<TR>
																	<TH align="center" nowrap class="v_b"><h:outputText
																		styleClass="outputText" id="lblSzks"
																		value="#{pc_Ghg00201.propSgksNameRyak.name}"
																		style="#{pc_Ghg00201.propSgksNameRyak.labelStyle}"></h:outputText></TH>
																	<TD valign="middle"><h:inputText styleClass="likeOutput"
																		id="htmlSzks" style="#{pc_Ghg00201.propSgksNameRyak.style}"
																		size="120"
																		value="#{pc_Ghg00201.propSgksNameRyak.value}" tabindex="-1">
																	</h:inputText></TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="layoutTable">
															<TBODY>
																<TR>
																	<TD align="right">
																		<h:outputText styleClass="outputText"
																			id="text1" value="#{pc_Ghg00201.propPayList.listCount}">
																		</h:outputText>
																		<h:outputText
																			styleClass="outputText" id="text2" value="件">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD>
																		<DIV style="height: 245px; width=100%;" id="listScroll" onscroll="setScrollPosition('scroll',this);" class="listScroll">
																			<h:dataTable border="0" cellpadding="2" cellspacing="0"
																				rowClasses="#{pc_Ghg00201.propPayList.rowClasses}"
																				headerClass="headerClass"
																				footerClass="footerClass" styleClass="meisai_scroll" id="table1"
																				value="#{pc_Ghg00201.propPayList.list}" var="varlist" width="885px">
																				<h:column id="column2">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="納付金" id="text90">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text91">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>								
																					<h:outputText id="text5" value="#{varlist.payCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="57px" name="width" />
																				</h:column>
																				<h:column id="column3">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="パターン" id="text92">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text93">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>								
																					<h:outputText id="text7" value="#{varlist.patternCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="54px" name="width" />
																				</h:column>
																				<h:column id="column4">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="分納区分" id="text94">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text95">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>								
																					<h:outputText id="text9" value="#{varlist.bunnoKbnCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="58px" name="width" />
																					<f:attribute value="text-align: center" name="style" />
																				</h:column>
																				<h:column id="column5">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Ghg00201.propPayName.labelName}" id="lblPayName">
																						</h:outputText>
																					</f:facet>								
																					<h:outputText id="text11" value="#{varlist.payName}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="*" name="width" />
																				</h:column>
																				<h:column id="column6">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText" value="優先" id="text96">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText" value="順位" id="text97">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>								
																					<h:outputText id="text13" value="#{varlist.nyukinPriority}"
																						styleClass="outputText">
																					</h:outputText>
																						<f:attribute value="36px" name="width" />
																						<f:attribute value="text-align: center" name="style" />
																				</h:column>
																				<h:column id="column7">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText" value="#{pc_Ghg00201.propItemGakuTotal.labelName}"
																								id="lblItemGakuTotal">
																						</h:outputText>
																					</f:facet>								
																					<h:outputText id="text15" value="#{varlist.itemTotal}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="78px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column8">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Ghg00201.propMenjKingaku.labelName}"
																							id="lblMenjGakuTotal">
																						</h:outputText>
																					</f:facet>								
																					<h:outputText id="text17" value="#{varlist.menjKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="78px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column9">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Ghg00201.propYoChosyuKingaku.labelName}"
																							id="lblYouCyoshuGaku">
																						</h:outputText>
																					</f:facet>								
																					<h:outputText id="text19" value="#{varlist.cyoushuKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="78px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column19">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Ghg00201.propNofuZumiGaku.labelName}"
																							id="lblNoufuZumiGaku">
																						</h:outputText>
																					</f:facet>								
																					<h:outputText id="text21" value="#{varlist.nyuukinKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="78px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column1">
																					<f:facet name="header">
																					</f:facet>								
																					<hx:commandExButton type="submit" value="編集"
																						styleClass="cmdBtn_dat_s" id="edit"
																						rendered="#{varlist.rendered}"
																						action="#{pc_Ghg00201.doEditAction}">
																					</hx:commandExButton>
																					<f:attribute value="40px" name="width" />
																				</h:column>
																			</h:dataTable>
																		</DIV>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD>
																		<hx:commandExButton type="submit"
																			value="新規登録" styleClass="commandExButton_dat"
																			id="regist" action="#{pc_Ghg00201.doRegistAction}"
																			disabled="#{pc_Ghg00201.propActiveControlRegist.disabled}">
																		</hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			</DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ghg00201.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00201.propPayList.scrollPosition}" id="scroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00201.propActiveControlUnselect.value}"
				id="htmlActiveControlUnselect">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00201.propActiveControlRegist.value}"
				id="htmlActiveControlRegist">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00201.propActiveControlExec.value}"
				id="htmlActiveControlExec">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Ghg00201.propActiveControlGakSearch.value}"
				id="htmlActiveControlGakSearch">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
