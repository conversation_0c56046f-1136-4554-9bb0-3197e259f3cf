#gakuen.properties

INIPATH=${WAR_PATH}/WEB-INF/gakuen.ini
COINIPATH=${WAR_PATH}/WEB-INF/gakuenCO.ini
IMGPATH=${WAR_PATH}/rev/image/ja/menu/
GAKENVPATH=${WAR_PATH}/WEB-INF/gakenv
LOG4JCONFPATH=${WAR_PATH}/WEB-INF/log4j.xml
FRMPATH=${WAR_PATH}/WEB-INF/frm
VFRPATH=${SVF_PATH}
ZIPPATH=${ZIPPATH}
UNZIPPATH=${UNZIPPATH}
SELFEXTRACTINGFILE=${SELFEXTRACTINGFILE}
ZIPBATPATH=${WAR_PATH}/WEB-INF/bin/cmdzip.bat
ZIPSHPATH=${WAR_PATH}/WEB-INF/bin/cmdzip.sh
ZIPBATPATH_NOPASS=${WAR_PATH}/WEB-INF/bin/cmdzipnopass.bat
ZIPSHPATH_NOPASS=${WAR_PATH}/WEB-INF/bin/cmdzipnopass
GAKOUTPATH=${GAKOUT_PATH}



DBTYPE=${DB_TYPE}
DB2DRIVER=com.ibm.db2.jcc.DB2Driver
ORADRIVER=oracle.jdbc.OracleDriver
DATASOURCE=${DATASOURCE}
POOLING=1

OSTYPE=${OS_TYPE}
WAS_VERSION=85
