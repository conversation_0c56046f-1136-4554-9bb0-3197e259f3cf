<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghi00401.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<TITLE>Ghi00401.jsp</TITLE>
<SCRIPT type="text/javascript">

	
</SCRIPT></HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY>
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghi00401.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp"
                    action="#{pc_Ghi00401.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghi00401.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghi00401.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
				</DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
											<TBODY>
												<TR>
													<TH width="150px" nowrap class="v_a">
														<!-- 対象日付 -->
														<h:outputText styleClass="outputText"
															id="lblTargetDate"
															value="#{pc_Ghi00401.propTargetDate.name}"
															style="#{pc_Ghi00401.propTargetDate.labelStyle}">
														</h:outputText>
													</TH>
													<TD colspan="2">
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
															<TBODY>
																<TR>
																	<TD class="clear_border" width="115px">
																		<h:inputText styleClass="inputText" id="htmlTargetDateFrom"
																			value="#{pc_Ghi00401.propTargetDateFrom.dateValue}"
																			size="11" tabindex="1">
																			<f:convertDateTime />
																			<hx:inputHelperDatePicker />
																			<hx:inputHelperAssist errorClass="inputText_Error"
																				promptCharacter="_" />
																		</h:inputText>
																	</TD>
																	<TD class="clear_border" width="25px">
							 											<h:outputText 
							 												styleClass="outputText" 
							 												id="text4" value="～">
							 											</h:outputText>
																	</TD>
																	<TD class="clear_border" width="*">
																		<h:inputText styleClass="inputText" id="htmlTargetDateTo"
																			value="#{pc_Ghi00401.propTargetDateTo.dateValue}" size="11"
																			tabindex="2">
																			<f:convertDateTime />
																			<hx:inputHelperDatePicker />
																			<hx:inputHelperAssist errorClass="inputText_Error"
																				promptCharacter="_" />
																		</h:inputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>	
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="20"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE class="table" width="100%">
											<TBODY>
												<TR align="center">
													<TH nowrap class="v_b" width="150px" rowspan="6">
														<h:outputText styleClass="outputText"
															id="lblChkKomoku" value="#{pc_Ghi00401.propChkKomoku.name}" 
															style="#{pc_Ghi00401.propChkKomoku.style}">
														</h:outputText>
													</TH>
													<TD align="left" style="border-bottom-style:none">
														<h:outputText
														styleClass="outputText" id="lblHikaku"
														value="#{pc_Ghi00401.propLblHikaku.labelName}"
														style="#{pc_Ghi00401.propLblHikaku.labelStyle}"></h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TD align="left" style="border-bottom-style:none;border-top-style:none">
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
															id="htmlSmaller" value="#{pc_Ghi00401.propSmaller.checked}"
															disabled="#{pc_Ghi00401.propSmaller.disabled}"
															style="#{pc_Ghi00401.propSmaller.style}" tabindex="3">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblSmaller"
															value="#{pc_Ghi00401.propSmaller.name}"
															style="#{pc_Ghi00401.propSmaller.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TD align="left" style="border-bottom-style:none;border-top-style:none">
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
															id="htmlBigger" value="#{pc_Ghi00401.propBigger.checked}"
															disabled="#{pc_Ghi00401.propBigger.disabled}"
															style="#{pc_Ghi00401.propBigger.style}" tabindex="4">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblBigger"
															value="#{pc_Ghi00401.propBigger.name}"
															style="#{pc_Ghi00401.propBigger.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TD align="left" style="border-bottom-style:none;border-top-style:none">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlKanaName"
															value="#{pc_Ghi00401.propKanaName.checked}"
															disabled="#{pc_Ghi00401.propKanaName.disabled}"
															style="#{pc_Ghi00401.propKanaName.style}" tabindex="5">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblKanaName"
															value="#{pc_Ghi00401.propKanaName.name}"
															style="#{pc_Ghi00401.propKanaName.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TD align="left" style="border-bottom-style:none;border-top-style:none">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlPayLimit"
															value="#{pc_Ghi00401.propPayLimit.checked}"
															disabled="#{pc_Ghi00401.propPayLimit.disabled}"
															style="#{pc_Ghi00401.propPayLimit.style}" tabindex="6">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblPayLimit"
															value="#{pc_Ghi00401.propPayLimit.name}"
															style="#{pc_Ghi00401.propPayLimit.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TD align="left" style="border-top-style:none">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlTorihikiKouza"
															value="#{pc_Ghi00401.propTorihikiKouza.checked}"
															disabled="#{pc_Ghi00401.propTorihikiKouza.disabled}"
															style="#{pc_Ghi00401.propTorihikiKouza.style}" tabindex="7">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblTorihikiKouza"
															value="#{pc_Ghi00401.propTorihikiKouza.name}"
															style="#{pc_Ghi00401.propTorihikiKouza.style}">
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="20"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE class="table" width="100%">
											<TBODY>
												<TR align="center">
													<TH nowrap class="v_c" width="150px">
														<h:outputText styleClass="outputText" 
															id="lblSyoriKbn" value="#{pc_Ghi00401.propSyoriKbn.name}" 
															style="#{pc_Ghi00401.propSyoriKbn.style}">
														</h:outputText>
													</TH>
													<TD align="left">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlCheckOnly"
															value="#{pc_Ghi00401.propCheckOnly.checked}"
															disabled="#{pc_Ghi00401.propCheckOnly.disabled}"
															style="#{pc_Ghi00401.propCheckOnly.style}" tabindex="8">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblCheckOnly"
															value="#{pc_Ghi00401.propCheckOnly.name}"
															style="#{pc_Ghi00401.propCheckOnly.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TH nowrap class="v_d" rowspan="3">
														<h:outputText styleClass="outputText"
															id="lblChkList" value="#{pc_Ghi00401.propChkList.name}" 
															style="#{pc_Ghi00401.propChkList.style}">
														</h:outputText>
													</TH>
													<TD align="left" style="border-bottom-style:none">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlChkListNormal"
															value="#{pc_Ghi00401.propChkListNormal.checked}"
															disabled="#{pc_Ghi00401.propChkListNormal.disabled}"
															style="#{pc_Ghi00401.propChkListNormal.style}" tabindex="9">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblChkListNormal" 
															value="#{pc_Ghi00401.propChkListNormal.name}" 
															style="#{pc_Ghi00401.propChkListNormal.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TD align="left" style="border-bottom-style:none;border-top-style:none">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlChkListError"
															value="#{pc_Ghi00401.propChkListError.checked}"
															disabled="#{pc_Ghi00401.propChkListError.disabled}"
															style="#{pc_Ghi00401.propChkListError.style}" tabindex="10">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblChkListError"
															value="#{pc_Ghi00401.propChkListError.name}"
															style="#{pc_Ghi00401.propChkListError.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR align="center">
													<TD align="left" style="border-top-style:none">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlChkListWarning"
															value="#{pc_Ghi00401.propChkListWarning.checked}"
															disabled="#{pc_Ghi00401.propChkListWarning.disabled}"
															style="#{pc_Ghi00401.propChkListWarning.style}" tabindex="11">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblChkListWarning"
															value="#{pc_Ghi00401.propChkListWarning.name}"
															style="#{pc_Ghi00401.propChkListWarning.style}">
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton type="submit" value="実行"
															styleClass="commandExButton_dat" id="exec"
															confirm="#{msg.SY_MSG_0001W}"
															action="#{pc_Ghi00401.doExecAction}" tabindex="12">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
                    </DIV>
                </DIV>
                <!--↑CONTENT↑-->
            </DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />

        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
