<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaa00406T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>関連情報登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	


<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

	function loadFunc() {
	　　changeScrollPosition('scroll', 'listScroll');
	}
	
	window.attachEvent("onload", attachFormatNumber);
	window.attachEvent('onload', loadFunc);
	
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kaa00406T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kaa00406T02.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kaa00406T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kaa00406T02.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp" action="#{pc_Kaa00406T02.doReturnDispAction}" ></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>

<!--↓content↓-->
<DIV id="content">			

<!-- ↓ここにコンポーネントを配置 -->
<DIV class="column" align="center">

	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="900">
		<TBODY>
			<TR>
				<TH class="v_a" width="70">
						<h:outputText styleClass="outputText"
							id="lblChotNendo"
							style="#{pc_Kaa00406T02.kaa00406.propChotNendo.labelStyle}"
							value="#{pc_Kaa00406T02.kaa00406.propChotNendo.labelName}"></h:outputText></TH>
				<TD width="110">
						<h:outputText styleClass="outputText" id="htmlChotNendo"
							style="#{pc_Kaa00406T02.kaa00406.propChotNendo.style}"
							value="#{pc_Kaa00406T02.kaa00406.propChotNendo.stringValue}"></h:outputText></TD>
				<TH class="v_b" width="70">
						<h:outputText styleClass="outputText"
							id="lblChotNo" 
							style="#{pc_Kaa00406T02.kaa00406.propChotNo.labelStyle}"
							value="#{pc_Kaa00406T02.kaa00406.propChotNo.labelName}"></h:outputText></TH>
				<TD width="230">
						<DIV style="width:230px;white-space:nowrap;overflow:hidden;display:block;">
						<h:outputText styleClass="outputText" id="htmlChotNo"
							style="#{pc_Kaa00406T02.kaa00406.propChotNo.style}"
							value="#{pc_Kaa00406T02.kaa00406.propChotNo.stringValue}"
							title="#{pc_Kaa00406T02.kaa00406.propChotNo.stringValue}"></h:outputText></DIV></TD>
				<TH class="v_c" width="70">
						<h:outputText styleClass="outputText" id="lblMeisaiNo"
							style="#{pc_Kaa00406T02.kaa00406.propMeisaiNo.labelStyle}"
							value="#{pc_Kaa00406T02.kaa00406.propMeisaiNo.labelName}"></h:outputText></TH>
				<TD width="110">
						<h:outputText styleClass="outputText" id="htmlMeisaiNo"
							style="#{pc_Kaa00406T02.kaa00406.propMeisaiNo.style}"
							value="#{pc_Kaa00406T02.kaa00406.propMeisaiNo.stringValue}"></h:outputText></TD>
				<TH class="v_d" width="70">
						<h:outputText styleClass="outputText" id="lblShutokuKagaku"
							style="#{pc_Kaa00406T02.kaa00406.propShutokuKagaku.labelStyle}"
							value="#{pc_Kaa00406T02.kaa00406.propShutokuKagaku.labelName}"></h:outputText></TH>
				<TD width="170"  style="text-align: right; padding-right: 3px;">
						<h:outputText styleClass="outputText" id="htmlShutokuKagaku"
							style="#{pc_Kaa00406T02.kaa00406.propShutokuKagaku.style}"
							value="#{pc_Kaa00406T02.kaa00406.propShutokuKagaku.longValue}">
							<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" /></h:outputText></TD>
			</TR>
		</TBODY>
	</TABLE>

	<BR>
	<TABLE border="0" cellpadding="0" cellspacing="0" width="900">
		<TBODY>
			<TR>
				<TD align="left" height="27">
					<hx:commandExButton type="submit"
							value="財産目録" styleClass="tab_head_off" id="mkrkInfo" style="width: 150px" action="#{pc_Kaa00406T02.doMkrkInfoAction}"></hx:commandExButton><hx:commandExButton type="submit"
							value="付属品" styleClass="tab_head_on" id="fzkInfo" style="width: 150px" ></hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD align="left" valign="top">
				<TABLE border="1" cellpadding="0" cellspacing="0" height="410" width="900" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center"  valign="top">

									<TABLE border="0" cellpadding="0" cellspacing="0"
										width="880" style="margin-top: 20px;">
										<TBODY>
											<TR>
												<TD align="right"><h:outputText styleClass="outputText"
													id="htmlListCount"
													value="#{pc_Kaa00406T02.propBihnFzkList.listCount}"></h:outputText><h:outputText
													styleClass="outputText" id="textCount" value="件"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE border="0" cellpadding="0" cellspacing="0" 
										width="880">
									<TBODY>
										<TR>
											<TD>
												<DIV class="listScroll"
													 style="height:250px; OVERFLOW:scroll;overflow-x: hidden;"
													 onscroll="setScrollPosition('scroll',this);" id="listScroll">
													 
													<h:dataTable border="0" cellpadding="0" cellspacing="0"
													headerClass="headerClass" footerClass="footerClass"
													rowClasses="#{pc_Kaa00406T02.propBihnFzkList.rowClasses}"
													styleClass="meisai_scroll" id="htmlBihnFzkList"
													value="#{pc_Kaa00406T02.propBihnFzkList.list}"
													var="varlist">
													
													<h:column id="column1">
														<f:facet name="header">
															<h:outputText id="lblColFuzokuhinNo" styleClass="outputText" value="付属品番号"></h:outputText>
														</f:facet>
														<h:outputText styleClass="outputText"
															id="htmlColFuzokuhinNo" value="#{varlist.colFuzokuhinNo}"></h:outputText>
														<f:attribute value="90" name="width" />
														<f:attribute value="padding-right: 3px; text-align: center" name="style" />
													</h:column>
													<h:column id="column2">
														<f:facet name="header">
															<h:outputText styleClass="outputText" value="付属品内容"
																id="lblColFuzokuhinNaiyo"></h:outputText>
														</f:facet>
														<hx:jspPanel id="jspPanel1">
															<DIV style="width:683px;white-space:nowrap;overflow:hidden;display:block;">
															<h:outputText styleClass="outputText"
																id="htmlColFuzokuhinNaiyo"
																value="#{varlist.colFuzokuhinNaiyo}"
																title="#{varlist.colFuzokuhinNaiyo}"></h:outputText></DIV>
														</hx:jspPanel>
														<f:attribute value="683" name="width" />
														<f:attribute value="true" name="nowrap" />
													</h:column>
													<h:column id="column3">
														<f:facet name="header">
															<h:outputText styleClass="outputText" value="数量"
																id="lblColFuzokuhinSuryo"></h:outputText>
														</f:facet>
														<h:outputText styleClass="outputText"
															id="htmlColFuzokuhinSuryo"
															value="#{varlist.colFuzokuhinSuryo}" ></h:outputText>
														<f:attribute value="45" name="width" />
														<f:attribute value="padding-right: 3px; text-align: right" name="style" />
													</h:column>
													<h:column id="column4">
														<f:facet name="header">
														</f:facet>
														<hx:commandExButton type="submit" value="選択"
															styleClass="commandExButton" id="select"  action="#{pc_Kaa00406T02.doSelectAction}"></hx:commandExButton>
														<f:attribute value="39" name="width" />
													</h:column>
												</h:dataTable> 
												</DIV>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
									width="880" style="margin-top: 20px;">
									<TBODY>
										<TR>
											<TH width="150" class="v_e"><h:outputText
													styleClass="outputText" id="lblFuzokuhinNo"
													style="#{pc_Kaa00406T02.propFuzokuhinNo.labelStyle}"
													value="#{pc_Kaa00406T02.propFuzokuhinNo.labelName}"></h:outputText></TH>
											<TD width="290"><h:inputText styleClass="inputText"
													id="htmlFuzokuhinNo"  size="3"
													style="padding-right: 3px; text-align: right; #{pc_Kaa00406T02.propFuzokuhinNo.style}"
													value="#{pc_Kaa00406T02.propFuzokuhinNo.stringValue}"></h:inputText></TD>
											<TH width="150" class="v_f"><h:outputText
													styleClass="outputText" id="lblFuzokuhinSuryo"
													style="#{pc_Kaa00406T02.propFuzokuhinSuryo.labelStyle}"
													value="#{pc_Kaa00406T02.propFuzokuhinSuryo.labelName}"></h:outputText></TH>
											<TD width="290"><h:inputText styleClass="inputText"
													id="htmlFuzokuhinSuryo"  size="3"
													style="padding-right: 3px; text-align: right; #{pc_Kaa00406T02.propFuzokuhinSuryo.style}"
													value="#{pc_Kaa00406T02.propFuzokuhinSuryo.stringValue}"></h:inputText></TD>
										</TR>
										<TR>
											<TH class="v_g"><h:outputText styleClass="outputText"
													id="lblFuzokuhinNaiyo"
													style="#{pc_Kaa00406T02.propFuzokuhinNaiyo.labelStyle}"
													value="#{pc_Kaa00406T02.propFuzokuhinNaiyo.labelName}"></h:outputText></TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
													id="htmlFuzokuhinNaiyo"  size="113"
													style="#{pc_Kaa00406T02.propFuzokuhinNaiyo.style}"
													value="#{pc_Kaa00406T02.propFuzokuhinNaiyo.stringValue}"
													maxlength="#{pc_Kaa00406T02.propFuzokuhinNaiyo.maxLength}"></h:inputText></TD>
										</TR>
									</TBODY>
								</TABLE>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
									width="880" style="margin-top: 10px;">
									<TBODY>
										<TR>
											<TD width="270"></TD>
											<TD width="50"><hx:commandExButton 
													type="submit" value="登録" styleClass="commandExButton_dat" id="register"  action="#{pc_Kaa00406T02.doRegisterAction}"></hx:commandExButton></TD>
											<TD width="50"><hx:commandExButton
													type="submit" value="更新" styleClass="commandExButton_dat" id="update"  action="#{pc_Kaa00406T02.doUpdateAction}"></hx:commandExButton></TD>
											<TD width="50"><hx:commandExButton
													type="submit" value="削除" styleClass="commandExButton_dat" id="delete" action="#{pc_Kaa00406T02.doDeleteAction}"></hx:commandExButton></TD>
											<TD width="50"><hx:commandExButton
													type="submit" value="クリア" styleClass="commandExButton_etc" id="clear"  action="#{pc_Kaa00406T02.doClearAction}"></hx:commandExButton></TD>
											<TD width="270"></TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>	
	
</DIV>
<!-- ↑ここにコンポーネントを配置 -->

</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->

<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Kaa00406T02.propBihnFzkList.scrollPosition}" id="scroll"></h:inputHidden>
			<h:inputHidden
				value="htmlFuzokuhinNo=##0;##0 | htmlFuzokuhinSuryo=##0;##0"
				id="htmlFormatNumberOption"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

