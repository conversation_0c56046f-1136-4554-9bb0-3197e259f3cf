<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb15001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>経理文面設定一覧</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";	
	indirectClick('search');	
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";	
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb15001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Keb15001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb15001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb15001.screenName}"></h:outputText>
</div>			

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton	type="submit" value="新規登録" 
	styleClass="commandExButton" id="register" 
	action="#{pc_Keb15001.doRegisterAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   style=""
		   class="table" 
		   width="675">
		<TBODY>
			<TR>
				<TH style="" class="v_a" width="160px">
					<h:outputText styleClass="outputText" id="lblBumenKbn"
							style="#{pc_Keb15001.propBunmenKbn.labelStyle}"
							value="#{pc_Keb15001.propBunmenKbn.labelName}"
							rendered="#{pc_Keb15001.propBunmenKbn.rendered}">
						</h:outputText>
				</TH>
				<TD width="515px">
				<h:selectOneMenu styleClass="selectOneMenu" id="htmlBunmenKbn"
							value="#{pc_Keb15001.propBunmenKbn.value}"
							style="width : 350px;#{pc_Keb15001.propBunmenKbn.style}"
							disabled="#{pc_Keb15001.propBunmenKbn.disabled}"
							rendered="#{pc_Keb15001.propBunmenKbn.rendered}"
							readonly="#{pc_Keb15001.propBunmenKbn.readonly}">
							<f:selectItems value="#{pc_Keb15001.propBunmenKbn.list}" />
						</h:selectOneMenu></TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="675"
		   style="margin-top:10px;" 
		   class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton
						type="submit" 
						value="検索" 
						styleClass="commandExButton_dat" 
						id="search" 
						action="#{pc_Keb15001.doSearchAction}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE width="100%" border="0" cellpadding="5" height="450">
		<TBODY>
			<TR>
				<TD align="center"><TABLE border="0" cellpadding="0" cellspacing="0" width="675">
					<TBODY>
						<TR>
							<TD align="right" nowrap class="outputText" width="675"><h:outputText
										styleClass="outputText" id="lblBunmenEdabanCnt"
										value="#{pc_Keb15001.propBunmenList.listCount}"></h:outputText>件</TD>
						</TR>
						<TR>
							<TD>
									<div class="listScroll" id="listScroll" style="height: 450px"
										onscroll="setScrollPosition('scroll', this);"><h:dataTable
										border="0" cellpadding="2" cellspacing="0"
										headerClass="headerClass" footerClass="footerClass"
										columnClasses="columnClass1" styleClass="meisai_scroll"
										id="htmlBunmenList" var="varlist"
										value="#{pc_Keb15001.propBunmenList.list}"
										style="table-layout: fixed;"
										rowClasses="#{pc_Keb15001.propBunmenList.rowClasses}" width="657">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="lblEdaban_head" styleClass="outputText"
													value="枝番"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblEdaban_list"
												value="#{varlist.bunmenEdaNo}"></h:outputText>
											<f:attribute value="35" name="width" />
											<f:attribute value="center" name="align" />
											<f:attribute
												value="text-align: center; vertical-align: middle"
												name="style" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText" id="lblTitle_head"
													value="タイトル"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblTitle_list"
												value="#{varlist.title}" title="#{varlist.title}"></h:outputText>
											<f:attribute value="545" name="width" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="編集"
												styleClass="commandExButton" id="button_edit"
												style="width: 38px;" action="#{pc_Keb15001.doEditAction}"></hx:commandExButton>
											<f:attribute value="38" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="ｺﾋﾟｰ"
												styleClass="commandExButton" id="button_copy"
												style="width: 38px;" action="#{pc_Keb15001.doCopyAction}"></hx:commandExButton>
											<f:attribute value="38" name="width" />
										</h:column>
									</h:dataTable><BR>
									</div>
									</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Keb15001.propBunmenList.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Keb15001.propExecutableSearch.integerValue}" id="htmlExecutableSearch"><f:convertNumber /></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('scroll', 'listScroll');
	}
</SCRIPT>

</HTML>
