<%-- 
	卒業生情報登録（専攻コース登録）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob01503.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01503.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

// データテーブル全選択
function listCheck(thisObj, thisEvent) {
  check('htmlSnkCorList', 'htmlCheck');
}

// データテーブル全解除
function listUnCheck(thisObj, thisEvent) {
  uncheck('htmlSnkCorList', 'htmlCheck');
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob01503.onPageLoadBegin}">
<gakuen:itemStateCtrlDef managedbean="pc_Cob01502T01" property="cob01502" />
<gakuen:itemStateCtrl managedbean="pc_Cob01503">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob01503.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob01503.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob01503.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Cob01503.doReturnDispAction}">
			</hx:commandExButton> <!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE class="table" width="850">
							<TBODY>
								<TR align="center" valign="middle">
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblGaksekiCd_head"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.name}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="htmlGaksekiCd"
										value="#{pc_Cob01502T01.cob01502.propGaksekiCd.value}">
									</h:outputText></TD>
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblGakseiName"
										value="#{pc_Cob01502T01.cob01502.propGakseiName.name}"></h:outputText></TH>
									<TD nowrap width="300"><h:outputText styleClass="outputText"
										id="htmlGakseiName"
										value="#{pc_Cob01502T01.cob01502.propGakseiName.value}"></h:outputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSotNendo"
										value="#{pc_Cob01502T01.cob01502.propSotNendo.name}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="htmlSotNendo"
										value="#{pc_Cob01502T01.cob01502.propSotNendo.value}"></h:outputText></TD>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSotGakki"
										value="#{pc_Cob01502T01.cob01502.propSotGakki.name}"></h:outputText></TH>
									<TD nowrap><h:outputText styleClass="outputText"
										id="htmlSotGakki"
										value="#{pc_Cob01502T01.cob01502.propSotGakki.value}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850">
							<TBODY>
								<TR>
									<TD align="right" nowrap class="outputText" width="100%"><h:outputText
										styleClass="outputText" id="lblSnkCorCount" value="#{pc_Cob01503.propSnkCorList.listCount}件"></h:outputText></TD>
								</TR>
								<TR>
									<TD>
										<DIV class="listScroll" style="height:200px"
											onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
											border="1" cellpadding="2" cellspacing="0"
											columnClasses="columnClass1" headerClass="headerClass"
											footerClass="footerClass" styleClass="meisai_scroll"
											rowClasses="#{pc_Cob01503.propSnkCorList.rowClasses}"
											id="htmlSnkCorList" value="#{pc_Cob01503.propSnkCorList.list}"
											var="varlist">
											<!-- チェック -->
											<h:column id="column1">
												<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlCheck" value="#{varlist.delFlg}">
												</h:selectBooleanCheckbox>
												<f:attribute value="26" name="width" />
												<f:attribute value="center" name="align" />
											</h:column>
											<!-- 並び順 -->
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="並び順"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="lblSnkList_RowNo"
													value="#{varlist.rowNo}"></h:outputText>
												<f:attribute value="text-align: right" name="style" />
												<f:attribute value="55" name="width" />
											</h:column>

											<!-- 専攻コース種別（コード、名称） -->
											<h:column id="column3">
												<f:facet name="header">
													<hx:jspPanel id="jspPanel1">
														<TABLE border="0" cellpadding="0" cellspacing="0"
															width="358" height="100%">
															<TBODY>
																<TR>
																	<TH style="border-style: none; text-align: center;"
																		width="349" colspan="2"><h:outputText
																		styleClass="outputText" id="htmlLblSnkList_SbtCd"
																		value="専攻コース種別"></h:outputText>
																	</TH>
																</TR>
																<TR>
																	<TH
																		style="border-left-style: none; border-bottom-style: none; text-align: center;"
																		width="79"><h:outputText styleClass="outputText"
																		id="lblSnkList_SbtCd" value="コード"></h:outputText></TH>
																	<TH
																		style="border-right-style: none; border-bottom-style: none; text-align: center;"
																		width="270"><h:outputText styleClass="outputText"
																		id="lblSnkList_SnkCorSbt" value="名称"></h:outputText></TH>
																</TR>
															</TBODY>
														</TABLE>
													</hx:jspPanel>
												</f:facet>
												<hx:jspPanel id="jspPanel2">
													<TABLE border="0" cellpadding="0" cellspacing="0" width="349"
														height="100%"
														style="border-bottom-style:none;border-top-style:none;">
														<TBODY>
															<TR>
																<TD
																	style="border-top-style:none; border-bottom-style: none; border-left-style: none;"
																	width="79"><h:outputText styleClass="outputText"
																	id="snkList_SbtCd" value="#{varlist.snkSbtCd}"></h:outputText>
																</TD>
																<TD
																	style="border-top-style:none; border-bottom-style: none; border-right-style: none;"
																	width="270"><h:outputText styleClass="outputText"
																	id="snkList_SnkCorSbt" value="#{varlist.snkSbtName}"></h:outputText>
																</TD>
															</TR>
														</TBODY>
													</TABLE>
												</hx:jspPanel>
											</h:column>
											<!-- 種別コード（コード、名称） -->
											<h:column id="column4">
												<f:facet name="header">
													<hx:jspPanel id="jspPanel3">
														<TABLE border="0" cellpadding="0" cellspacing="0"
															width="359" height="100%">
															<TBODY>
																<TR>
																	<TH style="border-style: none; text-align: center;"
																		width="359" colspan="2"><h:outputText
																		styleClass="outputText" id="htmlLblSnkList_SnkCorSbt"
																		value="専攻コース"></h:outputText>
																	</TH>
																</TR>
																<TR>
																	<TH
																		style="border-left-style: none; border-bottom-style: none; text-align: center;"
																		width="89"><h:outputText styleClass="outputText"
																		id="lblListSnkCd" value="コード"></h:outputText></TH>
																	<TH
																		style="border-right-style: none; border-bottom-style: none; text-align: center;"
																		width="270"><h:outputText styleClass="outputText"
																		id="lblListSnkName" value="名称"></h:outputText></TH>
																</TR>
															</TBODY>
														</TABLE>
													</hx:jspPanel>
												</f:facet>
												<hx:jspPanel id="jspPanel4">
													<TABLE border="0" cellpadding="0" cellspacing="0" width="359"
														height="100%"
														style="border-bottom-style:none;border-top-style:none;">
														<TBODY>
															<TR>
																<TD
																	style="border-top-style:none; border-bottom-style: none; border-left-style: none;"
																	width="89"><h:outputText styleClass="outputText"
																	id="snkCd" value="#{varlist.snkCd}"
																	title="#{varlist.snkCd}"></h:outputText></TD>
																<TD
																	style="border-top-style:none; border-bottom-style: none; border-right-style: none;"
																	width="270"><h:outputText styleClass="outputText"
																	id="snkName" value="#{varlist.disp03SnkName}"
																	title="#{varlist.snkName}"></h:outputText></TD>
															</TR>
														</TBODY>
													</TABLE>
												</hx:jspPanel>
											</h:column>
											<!--選択 -->
											<h:column id="column5">
												<f:attribute value="39" name="width" />
												<hx:commandExButton type="submit" value="選択"
													styleClass="commandExButton" id="select"
													action="#{pc_Cob01503.doSelectAction}"></hx:commandExButton>
											</h:column>
										</h:dataTable></DIV>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
										<TBODY>											
											<TR>
												<TD class="footerClass">
												<TABLE class="panelBox">
													<TBODY>											
														<TR>
															<TD align="left"><hx:commandExButton type="button"
																styleClass="check" id="check"
																onclick="return listCheck(this, event);"
																disabled="#{pc_Cob01503.propDelete.disabled}"></hx:commandExButton>
																<hx:commandExButton	type="button" styleClass="uncheck" id="uncheck"
																onclick="return listUnCheck(this, event);"
																disabled="#{pc_Cob01503.propDelete.disabled}"></hx:commandExButton></TD>
															<TD width="5"></TD>
															<TD align="left"><hx:commandExButton type="submit" value="削除"
																id="delete" styleClass="commandExButton"
																disabled="#{pc_Cob01503.propDelete.disabled}"
																action="#{pc_Cob01503.doDeleteAction}"></hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE class="table" width="850">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblSnkSbtSelect"
										value="#{pc_Cob01503.propSnkSbtList.labelName}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSnkSbtList"
										value="#{pc_Cob01503.propSnkSbtList.value}"
										disabled="#{pc_Cob01503.propSnkSbtList.disabled}"
										style="width:350px;">
										<f:selectItems value="#{pc_Cob01503.propSnkSbtList.list}" />
									</h:selectOneMenu> <hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton"
										action="#{pc_Cob01503.doChangeSnkSbtAction}"
										disabled="#{pc_Cob01503.propBtnSnkSbtSelect.disabled}"></hx:commandExButton>
										<hx:commandExButton	type="submit" value="解除" styleClass="commandExButton"
										id="unselect" action="#{pc_Cob01503.doUnselectAction}"
										disabled="#{pc_Cob01503.propBtnSnkSbtUnSelect.disabled}"></hx:commandExButton></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSnkSbtCd"
										value="#{pc_Cob01503.propSnkSbtCd.labelName}"
										style="#{pc_Cob01503.propSnkSbtCd.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlSnkSbtCd" size="3"
										value="#{pc_Cob01503.propSnkSbtCd.stringValue}"
										disabled="#{pc_Cob01503.propSnkSbtCd.disabled}"
										style="#{pc_Cob01503.propSnkSbtCd.style}"
										maxlength="#{pc_Cob01503.propSnkSbtCd.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c"><h:outputText
										styleClass="outputText" id="lblSnkSbtMei"
										value="#{pc_Cob01503.propSnkSbtMei.labelName}"></h:outputText></TH>
									<TD width="571"><h:inputText styleClass="inputText"
										id="htmlSnkSbtMei"
										value="#{pc_Cob01503.propSnkSbtMei.stringValue}"
										disabled="#{pc_Cob01503.propSnkSbtMei.disabled}"
										style="#{pc_Cob01503.propSnkSbtMei.style}"
										maxlength="#{pc_Cob01503.propSnkSbtMei.maxLength}" size="40"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_d"><h:outputText
										styleClass="outputText" id="lblSnkSelect"
										value="#{pc_Cob01503.propSnkList.labelName}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSnkList" value="#{pc_Cob01503.propSnkList.value}"
										disabled="#{pc_Cob01503.propSnkList.disabled}"
										style="width:370px;">
										<f:selectItems value="#{pc_Cob01503.propSnkList.list}" /></h:selectOneMenu>
										<hx:commandExButton type="submit" value="選択" styleClass="commandExButton"
										action="#{pc_Cob01503.doChangeSnkAction}"
										disabled="#{pc_Cob01503.propBtnSnkSelect.disabled}"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_e"><h:outputText
										styleClass="outputText" id="lblSnkCd"
										value="#{pc_Cob01503.propSnkCd.labelName}"
										style="#{pc_Cob01503.propSnkCd.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlSnkCd" size="6"
										value="#{pc_Cob01503.propSnkCd.stringValue}"
										disabled="#{pc_Cob01503.propSnkCd.disabled}"
										style="#{pc_Cob01503.propSnkCd.style}"
										maxlength="#{pc_Cob01503.propSnkCd.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_f"><h:outputText
										styleClass="outputText" id="lblSnkMei"
										value="#{pc_Cob01503.propSnkMei.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlSnkMei" value="#{pc_Cob01503.propSnkMei.stringValue}"
										disabled="#{pc_Cob01503.propSnkMei.disabled}"
										style="#{pc_Cob01503.propSnkMei.style}"
										maxlength="#{pc_Cob01503.propSnkMei.maxLength}" size="40"></h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b"><h:outputText styleClass="outputText"
										id="lblRowNo" value="#{pc_Cob01503.propRowNo.labelName}"
										style="#{pc_Cob01503.propRowNo.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlRowNo" size="4"
										value="#{pc_Cob01503.propRowNo.integerValue}"
										disabled="#{pc_Cob01503.propRowNo.disabled}"
										style="#{pc_Cob01503.propRowNo.style}"
										maxlength="#{pc_Cob01503.propRowNo.maxLength}">
										<f:convertNumber type="number" pattern="##0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
			</TABLE>
			<TABLE class="button_bar" cellspacing="1" cellpadding="1">
				<TBODY>
					<TR>
						<TD align="center" width="792"><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="kakutei"
							action="#{pc_Cob01503.doKakuteiAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Cob01503.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR class="hr" noshade width="100%">
			<TABLE class="button_bar" cellspacing="1" cellpadding="1">
				<TBODY>
					<TR>
						<TD align="center" width="792"><hx:commandExButton type="submit"
							value="一覧確定" styleClass="commandExButton_dat" id="ichirankakutei"
							action="#{pc_Cob01503.doIchiranKakuteiAction}"
							onclick="onChangeData();"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</TD>
			</TR>
			</TBODY>
			</TABLE>
			<h:inputHidden id="htmlHidScroll"
				value="#{pc_Cob01503.propSnkCorList.scrollPosition}"></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Cob01502T01.cob01502.propHidChangeDataFlg.stringValue}" ></h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
  window.attachEvent('onload', endload);

  function endload() {
    changeScrollPosition('htmlHidScroll', 'listScroll');
  }
</SCRIPT>

</HTML>
