<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb11304.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>仮払使用明細入力</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function confirmOk() {
    if(document.getElementById('form1:htmlHenseiCheckHidden').value == 1){
		document.getElementById('form1:htmlHenseiYsnTaniHidden').value = document.getElementById('form1:htmlShikkoYsnTani').value;
		document.getElementById('form1:htmlHenseiMokuHidden').value = document.getElementById('form1:htmlShikkoMoku').value;
		document.getElementById('form1:htmlHenseiKmkHidden').value = document.getElementById('form1:htmlShikkoKmk').value;
		document.getElementById('form1:htmlHenseiKomokuHidden').value = 0;
		
    	document.getElementById('form1:htmlHenseiCheckHidden').value = 0;
    }else{
		var executable = parseInt(document.getElementById('form1:htmlExecutableRegister').value);
		executable = executable + 1;
		document.getElementById('form1:htmlExecutableRegister').value = executable;
	}
	indirectClick('register');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableRegister').value = 1;
}

function func_YsnHenseiInfoClear(thisObj, thisEvent) {
	document.getElementById('form1:htmlHenseiYsnTani').innerHTML = "";
	document.getElementById('form1:htmlHenseiYsnTaniHidden').value = "";
	document.getElementById('form1:htmlHenseiYsnTaniName').innerHTML = "";
	document.getElementById('form1:htmlHenseiMoku').innerHTML = "";
	document.getElementById('form1:htmlHenseiMokuName').innerHTML = "";
	document.getElementById('form1:htmlHenseiKmk').innerHTML = "";
	document.getElementById('form1:htmlHenseiKmkName').innerHTML = "";
	document.getElementById('form1:htmlHenseiKomoku').innerHTML = "";
	document.getElementById('form1:htmlHenseiKomokuName').innerHTML = "";
	document.getElementById('form1:htmlMokuZandaka').innerHTML = "";
	document.getElementById('form1:htmlKmkZandaka').innerHTML = "";
	document.getElementById('form1:htmlKomokuZandaka').innerHTML = "";
}

function func_KenkyuClear() {
	document.getElementById('form1:htmlHakkoNendo').innerHTML = "";
	document.getElementById('form1:htmlHakkoNendoHidden').value = "";
	document.getElementById('form1:htmlKenkyuKadai').innerHTML = "";
	document.getElementById('form1:htmlKenkyuKadaiHidden').value = "";
	document.getElementById('form1:htmlKenkyuKadaiKbnHidden').value = "";
	document.getElementById('form1:htmlKenkyuKadaiName').innerHTML = "";
	document.getElementById('form1:htmlKenkyuUser').innerHTML = "";
	document.getElementById('form1:htmlKenkyuUserHidden').value = "";
	document.getElementById('form1:htmlKenkyuUserName').innerHTML = "";
	document.getElementById('form1:htmlUchiMeisai').innerHTML = "";
	document.getElementById('form1:htmlUchiMeisaiHidden').value = "";
	document.getElementById('form1:htmlUchiPtnCdHidden').value = "";
	document.getElementById('form1:htmlUchiMeisaiName').innerHTML = "";
	document.getElementById('form1:htmlUchiMeisaiZandaka').innerHTML = "";
}

function func_YosanTaniAJAX(thisObj, thisEvent) {
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlShikkoYsnTaniName";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	code['code2'] = thisObj.value;
	code['code3'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function func_YosanMokuAJAX(thisObj, thisEvent) {
	var servlet = 'rev/ke/MokuNameGensenKbnAJAX';
	var target  = '';	// ←空文字だけど必要
	var args    = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	args['code2'] = thisObj.value;
	var methodName = 'callBackMethod1';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, methodName);
}

function callBackMethod1(value) {
	document.getElementById('form1:htmlShikkoMokuName').innerHTML = value['key1'];
	document.getElementById('form1:htmlShikkoMokuName').title = value['key1'];
	if (value['key2'] != '') {
		var comboBox = document.getElementById('form1:htmlSknGensenKbn');
		for (i=0; i<comboBox.length; i++) {
			if (comboBox.options[i].value == value['key2']) {
				comboBox.selectedIndex = i;
			}
		}
	}
}

function func_KeiriKamokAJAX(thisObj, thisEvent) {
	var servlet = 'rev/ke/KmkNameShohizeiKbnAJAX';
	var target  = '';	// ←空文字だけど必要
	var args    = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	args['code2'] = thisObj.value;
	var methodName = 'callBackMethod2';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, methodName);
}

function callBackMethod2(value) {
	document.getElementById('form1:htmlShikkoKmkName').innerHTML = value['key1'];
	document.getElementById('form1:htmlShikkoKmkName').title = value['key1'];
	var kmkCd = document.getElementById('form1:htmlShikkoKmk').value;
	var beforeShikkoKmk = document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value;
	if (kmkCd != beforeShikkoKmk) {
		if (value['key2'] != null && value['key2'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key2']) {
					comboBox.selectedIndex = i;
				}
			}
		} else if (value['key3'] != null && value['key3'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key3']) {
					comboBox.selectedIndex = i;
				}
			}
		}
	}
	document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value = kmkCd;
}

function func_KeiriKamokAJAX2(thisObj, thisEvent) {
	var servlet = 'rev/ke/KmkNameShohizeiKbnAJAX';
	var target  = '';	// ←空文字だけど必要
	var args    = new Array();
	args['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	args['code2'] = thisObj.value;
	var methodName = 'callBackMethod22';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, methodName);
}

function callBackMethod22(value) {
	var kmkCd = document.getElementById('form1:htmlShikkoKmk').value;
	var beforeShikkoKmk = document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value;
	if (kmkCd != beforeShikkoKmk) {
		if (value['key2'] != null && value['key2'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key2']) {
					comboBox.selectedIndex = i;
				}
			}
		} else if (value['key3'] != null && value['key3'] != '') {
			var comboBox = document.getElementById('form1:htmlShohizeiKbn');
			for (i=0; i<comboBox.length; i++) {
				if (comboBox.options[i].value == value['key3']) {
					comboBox.selectedIndex = i;
				}
			}
		}
	}
	document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value = kmkCd;
}

function func_ChuKubunAJAX(thisObj, thisEvent) {
	var servlet = "rev/co/CogChuKubunAJAX";
	var target = "form1:htmlChushutsuKbnName";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	code['code2'] = thisObj.value;
	code['code3'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function func_KobetKubunAJAX(thisObj, thisEvent) {
	var servlet = "rev/co/CogKobetKubunAJAX";
	var target = "form1:htmlKobetsuKbnName";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendoHidden').value;
	code['code2'] = document.getElementById('form1:htmlShikkoYsnTani').value;
	code['code3'] = thisObj.value;
	code['code4'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

var beforeTekiNaiyo = '';
function func_CogKeTekiNaiyoAJAX(thisObj, thisEvent) {
	if (beforeTekiNaiyo != thisObj.value) {
		beforeTekiNaiyo = thisObj.value;
		// 摘要名称（内容）を取得する
		var servlet = "rev/co/CogKeTekiAJAX";
		var target = "form1:htmlTekiyoNameNaiyo";
		var code = new Array();
		code['code1'] = '1';
		code['code2'] = thisObj.value;
		code['code3'] = '0';
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, code);
	}
}
function func_TekiyoNaiyoFocus(thisObj, thisEvent) {
	beforeTekiNaiyo = thisObj.value;
}

var beforeTekiYoto = '';
function func_CogKeTekiYotoAJAX(thisObj, thisEvent) {
	if (beforeTekiYoto != thisObj.value) {
		beforeTekiYoto = thisObj.value;
		// 摘要名称（用途）を取得する
		var servlet = "rev/co/CogKeTekiAJAX";
		var target = "form1:htmlTekiyoNameYoto";
		var code = new Array();
		code['code1'] = '2';
		code['code2'] = thisObj.value;
		code['code3'] = '0';
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, code);
	}
}
function func_TekiyoYotoFocus(thisObj, thisEvent) {
	beforeTekiYoto = thisObj.value;
}

//FWからsubmit時にコールバック
function submitMethod() {
	document.getElementById('form1:htmlBeforeShikkoKmkCdHidden').value = 
		document.getElementById('form1:htmlShikkoKmk').value;
	return true;
}

function loadFunc() {
	func_YosanTaniAJAX(document.getElementById('form1:htmlShikkoYsnTani'), '');
	func_YosanMokuAJAX(document.getElementById('form1:htmlShikkoMoku'), '');
	func_KeiriKamokAJAX(document.getElementById('form1:htmlShikkoKmk'), '');
	func_ChuKubunAJAX(document.getElementById('form1:htmlChushutsuKbn'), '');
	func_KobetKubunAJAX(document.getElementById('form1:htmlKobetsuKbn'), '');
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="return loadFunc();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb11304.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Keb11304.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb11304.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb11304.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" 
					value="戻る" 
					styleClass="commandExButton" 
					id="returnDisp" 
					action="#{pc_Keb11304.doReturnDispAction}" 
					confirm="#{msg.SY_MSG_0014W}">
</hx:commandExButton>
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<CENTER>
		<TABLE border="0" 
			   class="table" 
			   width="900" 
			   cellspacing="0" 
			   cellpadding="0">
			<TBODY>
				<TR>
					<TD class="group_label_top" width="145" colspan="2" nowrap>
						<h:outputText styleClass="outputText" 
									  id="lblYsnHenseiInfo" 
									  value="予算編成情報">
						</h:outputText>
					</TD>
					<TD width="755" colspan="3">
						<hx:commandExButton type="submit" 
											styleClass="commandExButton_search" 
											id="commandExButton_search" 
											value="検" 
											action="#{pc_Keb11304.doYsnHenseiInfoSearchAction}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											styleClass="commandExButton_listclear" 
											id="ysnHenseiInfoClear" 
											value="ク" 
											action="#{pc_Keb11304.doYsnHenseiInfoClearAction}">
						</hx:commandExButton>
						<!-- 画面項目自動表示処理用隠しボタン -->
						<DIV style="display:none;">
							<hx:commandExButton type="submit"
												styleClass="commandExButton"
												id="henseiAutoDisp"
												value="自動表示"
												action="#{pc_Keb11304.doHenseiAutoDispAction}">
							</hx:commandExButton>
						</DIV>
					</TD>
				</TR>
				<TR>
					<TD class="group_label" width="20">
					</TD>
					<TH class="v_a" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblHenseiYsnTani" 
									  style="#{pc_Keb11304.propHenseiYsnTani.labelStyle}" 
									  value="#{pc_Keb11304.propHenseiYsnTani.name}">
						</h:outputText>
					</TH>
					<TD width="755" colspan="3" nowrap>
						<DIV style="width:755px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputHidden value="#{pc_Keb11304.propHenseiYsnTaniHidden.stringValue}"
										   id="htmlHenseiYsnTaniHidden">
							</h:inputHidden>
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiYsnTani" 
										  style="#{pc_Keb11304.propHenseiYsnTani.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiYsnTani.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiYsnTaniName" 
										  style="#{pc_Keb11304.propHenseiYsnTaniName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiYsnTaniName.stringValue}">
							</h:outputText>
							<h:inputHidden value="#{pc_Keb11304.propHenseiCheckHidden.stringValue}"
										   id="htmlHenseiCheckHidden">
							</h:inputHidden>
						</DIV>
					</TD>
				</TR>
				<TR>
					<TD class="group_label" width="20">
					</TD>
					<TH class="v_b" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblHenseiMoku" 
									  style="#{pc_Keb11304.propHenseiMoku.labelStyle}" 
									  value="#{pc_Keb11304.propHenseiMoku.name}">
						</h:outputText>
					</TH>
					<TD width="575" nowrap>
						<DIV style="width:575px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputHidden value="#{pc_Keb11304.propHenseiMokuHidden.stringValue}"
										   id="htmlHenseiMokuHidden">
							</h:inputHidden>
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiMoku" 
										  style="#{pc_Keb11304.propHenseiMoku.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiMoku.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiMokuName" 
										  style="#{pc_Keb11304.propHenseiMokuName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiMokuName.stringValue}"
										  title="#{pc_Keb11304.propHenseiMokuName.stringValue}">
							</h:outputText>
						</DIV>
					</TD>
					<TH class="v_c" width="70">
						<h:outputText styleClass="outputText" 
									  id="lblMokuZandaka" 
									  style="#{pc_Keb11304.propMokuZandaka.labelStyle}" 
									  value="#{pc_Keb11304.propMokuZandaka.name}">
						</h:outputText>
					</TH>
					<TD width="110" style="text-align: right">
						<h:outputText styleClass="outputText" 
									  id="htmlMokuZandaka" 
									  style="#{pc_Keb11304.propMokuZandaka.style}" 
									  value="#{pc_Keb11304.propMokuZandaka.longValue}" 
									  rendered="#{pc_Keb11304.propMokuZandaka.rendered}">
							<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
						</h:outputText>
					</TD>
				</TR>
				<TR>
					<TD class="group_label" width="20">
					</TD>
					<TH class="v_d" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblHenseiKmk" 
									  style="#{pc_Keb11304.propHenseiKmk.labelStyle}" 
									  value="#{pc_Keb11304.propHenseiKmk.name}">
						</h:outputText>
					</TH>
					<TD width="575">
						<DIV style="width:575px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputHidden value="#{pc_Keb11304.propHenseiKmkHidden.stringValue}"
										   id="htmlHenseiKmkHidden">
							</h:inputHidden>
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiKmk" 
										  style="#{pc_Keb11304.propHenseiKmk.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiKmk.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiKmkName" 
										  style="#{pc_Keb11304.propHenseiKmkName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiKmkName.stringValue}"
										  title="#{pc_Keb11304.propHenseiKmkName.stringValue}">
							</h:outputText>
						</DIV>
					</TD>
					<TH class="v_e" width="70">
						<h:outputText styleClass="outputText" 
									  id="lblKmkZandaka" 
									  style="#{pc_Keb11304.propKmkZandaka.labelStyle}" 
									  value="#{pc_Keb11304.propKmkZandaka.name}">
						</h:outputText>
					</TH>
					<TD width="110" style="text-align: right">
						<h:outputText styleClass="outputText" 
									  id="htmlKmkZandaka" 
									  style="#{pc_Keb11304.propKmkZandaka.style}" 
									  value="#{pc_Keb11304.propKmkZandaka.longValue}" 
									  rendered="#{pc_Keb11304.propKmkZandaka.rendered}">
							<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
						</h:outputText>
					</TD>
				</TR>
				<TR>
					<TD class="group_label_bottom" width="20">
					</TD>
					<TH class="v_f" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblHenseiKomoku" 
									  style="#{pc_Keb11304.propHenseiKomoku.labelStyle}" 
									  value="#{pc_Keb11304.propHenseiKomoku.name}">
						</h:outputText>
					</TH>
					<TD width="575">
						<DIV style="width:575px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputHidden value="#{pc_Keb11304.propHenseiKomokuHidden.stringValue}"
										   id="htmlHenseiKomokuHidden">
							</h:inputHidden>
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiKomoku" 
										  style="#{pc_Keb11304.propHenseiKomoku.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiKomoku.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlHenseiKomokuName" 
										  style="#{pc_Keb11304.propHenseiKomokuName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHenseiKomokuName.stringValue}" 
										  title="#{pc_Keb11304.propHenseiKomokuName.stringValue}">
							</h:outputText>
						</DIV>
					</TD>
					<TH class="v_g" width="70">
						<h:outputText styleClass="outputText" 
									  id="lblKomokuZandaka" 
									  style="#{pc_Keb11304.propKomokuZandaka.labelStyle}" 
									  value="#{pc_Keb11304.propKomokuZandaka.name}">
						</h:outputText>
					</TH>
					<TD width="110" style="text-align: right">
						<h:outputText styleClass="outputText" 
									  id="htmlKomokuZandaka" 
									  style="#{pc_Keb11304.propKomokuZandaka.style}" 
									  value="#{pc_Keb11304.propKomokuZandaka.longValue}" 
									  rendered="#{pc_Keb11304.propKomokuZandaka.rendered}">
							<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
						</h:outputText>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		<BR>
		<TABLE border="0" 
			   class="table" 
			   width="900" 
			   cellspacing="0" 
			   cellpadding="0" 
			   style="margin-top: 10px;">
			<TBODY>
				<TR>
					<TH class="v_a" width="145" nowrap>
						<h:outputText styleClass="outputText" 
									  id="lblShikkoYsnTani" 
									  style="#{pc_Keb11304.propShikkoYsnTani.labelStyle}" 
									  value="#{pc_Keb11304.propShikkoYsnTani.labelName}">
						</h:outputText>
					</TH>
					<TD width="275" nowrap colspan="2">
						<DIV style="width:275px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" 
										 id="htmlShikkoYsnTani" 
										 size="10" 
										 style="#{pc_Keb11304.propShikkoYsnTani.style}" 
										 maxlength="#{pc_Keb11304.propShikkoYsnTani.maxLength}" 
										 disabled="#{pc_Keb11304.propShikkoYsnTani.disabled}" 
										 readonly="#{pc_Keb11304.propShikkoYsnTani.readonly}" 
										 rendered="#{pc_Keb11304.propShikkoYsnTani.rendered}" 
										 value="#{pc_Keb11304.propShikkoYsnTani.stringValue}" 
										 onblur="return func_YosanTaniAJAX(this, event);">
							</h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" 
												id="shikkoYsnTaniSearch" 
												action="#{pc_Keb11304.doShikkoYsnTaniSearchAction}">
							</hx:commandExButton>
							<h:outputText styleClass="outputText" 
										  id="htmlShikkoYsnTaniName" 
										  style="#{pc_Keb11304.propShikkoYsnTaniName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propShikkoYsnTaniName.stringValue}" 
										  title="#{pc_Keb11304.propShikkoYsnTaniName.stringValue}" 
										  rendered="#{pc_Keb11304.propShikkoYsnTaniName.rendered}">
							</h:outputText>
						</DIV>
					</TD>
					<TH class="v_b" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblChushutsuKbn" 
									  style="#{pc_Keb11304.propChushutsuKbn.labelStyle}" 
									  value="#{pc_Keb11304.propChushutsuKbn.labelName}">
						</h:outputText>
					</TH>
					<TD width="355" nowrap>
						<DIV style="width:355px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" 
										 id="htmlChushutsuKbn" 
										 size="5" 
										 style="#{pc_Keb11304.propChushutsuKbn.style}" 
										 maxlength="#{pc_Keb11304.propChushutsuKbn.maxLength}" 
										 disabled="#{pc_Keb11304.propChushutsuKbn.disabled}" 
										 readonly="#{pc_Keb11304.propChushutsuKbn.readonly}" 
										 rendered="#{pc_Keb11304.propChushutsuKbn.rendered}" 
										 value="#{pc_Keb11304.propChushutsuKbn.stringValue}" 
										 onblur="return func_ChuKubunAJAX(this, event);">
							</h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" 
												id="chushutsuKbnSearch" 
												action="#{pc_Keb11304.doChushutsuKbnSearchAction}">
							</hx:commandExButton>
							<h:outputText styleClass="outputText" 
										  id="htmlChushutsuKbnName" 
										  style="#{pc_Keb11304.propChushutsuKbnName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propChushutsuKbnName.stringValue}" 
										  title="#{pc_Keb11304.propChushutsuKbnName.stringValue}" 
										  rendered="#{pc_Keb11304.propChushutsuKbnName.rendered}">
							</h:outputText>
						</DIV>
					</TD>
				</TR>
				<TR>
					<TH class="v_c" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblShikkoMoku" 
									  style="#{pc_Keb11304.propShikkoMoku.labelStyle}" 
									  value="#{pc_Keb11304.propShikkoMoku.labelName}">
						</h:outputText>
					</TH>
					<TD width="275" nowrap colspan="2">
						<DIV style="width:275px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" 
										 id="htmlShikkoMoku" 
										 size="10" 
										 style="#{pc_Keb11304.propShikkoMoku.style}" 
										 maxlength="#{pc_Keb11304.propShikkoMoku.maxLength}" 
										 disabled="#{pc_Keb11304.propShikkoMoku.disabled}" 
										 readonly="#{pc_Keb11304.propShikkoMoku.readonly}" 
										 rendered="#{pc_Keb11304.propShikkoMoku.rendered}" 
										 value="#{pc_Keb11304.propShikkoMoku.stringValue}" 
										 onblur="return func_YosanMokuAJAX(this, event);">
							</h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" 
												id="shikkoMokuSearch" 
												action="#{pc_Keb11304.doShikkoMokuSearchAction}">
							</hx:commandExButton>
							<h:outputText styleClass="outputText" 
										  id="htmlShikkoMokuName" 
										  style="#{pc_Keb11304.propShikkoMokuName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propShikkoMokuName.stringValue}" 
										  title="#{pc_Keb11304.propShikkoMokuName.stringValue}" 
										  rendered="#{pc_Keb11304.propShikkoMokuName.rendered}">
							</h:outputText>
						</DIV>
					</TD>
					<TH class="v_d" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblKobetsuKbn" 
									  style="#{pc_Keb11304.propKobetsuKbn.labelStyle}" 
									  value="#{pc_Keb11304.propKobetsuKbn.labelName}">
						</h:outputText>
					</TH>
					<TD width="355" nowrap>
						<DIV style="width:355px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" 
										 id="htmlKobetsuKbn" 
										 size="5" 
										 style="#{pc_Keb11304.propKobetsuKbn.style}" 
										 maxlength="#{pc_Keb11304.propKobetsuKbn.maxLength}" 
										 disabled="#{pc_Keb11304.propKobetsuKbn.disabled}" 
										 readonly="#{pc_Keb11304.propKobetsuKbn.readonly}" 
										 rendered="#{pc_Keb11304.propKobetsuKbn.rendered}" 
										 value="#{pc_Keb11304.propKobetsuKbn.stringValue}" 
										 onblur="return func_KobetKubunAJAX(this, event);">
							</h:inputText><hx:commandExButton type="submit" 
												styleClass="commandExButton_search" 
												id="kobetsuKbnSearch" 
												action="#{pc_Keb11304.doKobetsuKbnSearchAction}">
							</hx:commandExButton>
							<h:outputText styleClass="outputText" 
										  id="htmlKobetsuKbnName" 
										  style="#{pc_Keb11304.propKobetsuKbnName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propKobetsuKbnName.stringValue}" 
										  title="#{pc_Keb11304.propKobetsuKbnName.stringValue}" 
										  rendered="#{pc_Keb11304.propKobetsuKbnName.rendered}">
							</h:outputText>
						</DIV>
					</TD>
				</TR>
				<TR>
					<TH class="v_e" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblShikkoKmk" 
									  style="#{pc_Keb11304.propShikkoKmk.labelStyle}" 
									  value="#{pc_Keb11304.propShikkoKmk.labelName}">
						</h:outputText>
					</TH>
					<TD width="275" colspan="2">
						<DIV style="width:275px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" 
										 id="htmlShikkoKmk" 
										 size="12" 
										 style="#{pc_Keb11304.propShikkoKmk.style}" 
										 maxlength="#{pc_Keb11304.propShikkoKmk.maxLength}" 
										 disabled="#{pc_Keb11304.propShikkoKmk.disabled}" 
										 readonly="#{pc_Keb11304.propShikkoKmk.readonly}" 
										 rendered="#{pc_Keb11304.propShikkoKmk.rendered}" 
										 value="#{pc_Keb11304.propShikkoKmk.stringValue}" 
										 onblur="return func_KeiriKamokAJAX(this, event);">
							</h:inputText>
							<hx:commandExButton type="submit" 
												styleClass="commandExButton_search" 
												id="shikkoKmkSearch" 
												action="#{pc_Keb11304.doShikkoKmkSearchAction}">
							</hx:commandExButton>
							<!-- 画面項目自動表示処理用隠しボタン -->
							<DIV style="display:none;">
								<hx:commandExButton type="button"
													styleClass="commandExButton"
													id="shikkoKmkAutoDisp"
													value="自動表示"
													onclick="return func_KeiriKamokAJAX2(this, event);">
								</hx:commandExButton>
							</DIV>
							<h:outputText styleClass="outputText" 
										  id="htmlShikkoKmkName" 
										  style="#{pc_Keb11304.propShikkoKmkName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propShikkoKmkName.stringValue}" 
										  title="#{pc_Keb11304.propShikkoKmkName.stringValue}" 
										  rendered="#{pc_Keb11304.propShikkoKmkName.rendered}">
							</h:outputText>
						</DIV>
					</TD>
					<TH class="v_f" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblSknGensenKbn" 
									  style="#{pc_Keb11304.propSknGensenKbn.labelStyle}" 
									  value="#{pc_Keb11304.propSknGensenKbn.labelName}">
						</h:outputText>
					</TH>
					<TD width="355">
						<h:selectOneMenu styleClass="selectOneMenu" 
										 id="htmlSknGensenKbn" 
										 value="#{pc_Keb11304.propSknGensenKbn.value}" 
										 style="#{pc_Keb11304.propSknGensenKbn.style}; width: 300px" 
										 disabled="#{pc_Keb11304.propSknGensenKbn.disabled}" 
										 readonly="#{pc_Keb11304.propSknGensenKbn.readonly}" 
										 rendered="#{pc_Keb11304.propSknGensenKbn.rendered}">
							<f:selectItems value="#{pc_Keb11304.propSknGensenKbn.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_g" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblShikkoGaku" 
									  style="#{pc_Keb11304.propShikkoGaku.labelStyle}" 
									  value="#{pc_Keb11304.propShikkoGaku.labelName}">
						</h:outputText>
					</TH>
					<TD width="275" colspan="2">
						<h:inputText styleClass="inputText" 
									 id="htmlShikkoGaku" 
									 size="15" 
									 style="padding-right: 3px; text-align: right; #{pc_Keb11304.propShikkoGaku.style}" 
									 disabled="#{pc_Keb11304.propShikkoGaku.disabled}" 
									 readonly="#{pc_Keb11304.propShikkoGaku.readonly}" 
									 rendered="#{pc_Keb11304.propShikkoGaku.rendered}" 
									 value="#{pc_Keb11304.propShikkoGaku.stringValue}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText>
					</TD>
					<TH class="v_a" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblShohizeiKbn" 
									  style="#{pc_Keb11304.propShohizeiKbn.labelStyle}" 
									  value="#{pc_Keb11304.propShohizeiKbn.labelName}">
						</h:outputText>
					</TH>
					<TD width="355">
						<h:selectOneMenu styleClass="selectOneMenu" 
										 id="htmlShohizeiKbn" 
										 value="#{pc_Keb11304.propShohizeiKbn.value}" 
										 style="#{pc_Keb11304.propShohizeiKbn.style}" 
										 disabled="#{pc_Keb11304.propShohizeiKbn.disabled}" 
										 readonly="#{pc_Keb11304.propShohizeiKbn.readonly}" 
										 rendered="#{pc_Keb11304.propShohizeiKbn.rendered}">
							<f:selectItems value="#{pc_Keb11304.propShohizeiKbn.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_b" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblTekiyoNameHizuke" 
									  style="#{pc_Keb11304.propTekiyoNameHizuke.labelStyle}" 
									  value="#{pc_Keb11304.propTekiyoNameHizuke.labelName}">
						</h:outputText>
					</TH>
					<TD width="775" colspan="4">
						<h:inputText styleClass="inputText" 
									 id="htmlTekiyoNameHizuke" 
									 size="30" 
									 style="#{pc_Keb11304.propTekiyoNameHizuke.style}" 
									 maxlength="#{pc_Keb11304.propTekiyoNameHizuke.maxLength}" 
									 disabled="#{pc_Keb11304.propTekiyoNameHizuke.disabled}" 
									 readonly="#{pc_Keb11304.propTekiyoNameHizuke.readonly}" 
									 rendered="#{pc_Keb11304.propTekiyoNameHizuke.rendered}" 
									 value="#{pc_Keb11304.propTekiyoNameHizuke.stringValue}">
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_c" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblTekiyoCdNaiyo" 
									  style="#{pc_Keb11304.propTekiyoCdNaiyo.labelStyle}" 
									  value="#{pc_Keb11304.propTekiyoCdNaiyo.labelName}">
						</h:outputText>
					</TH>
					<TD width="130">
						<h:inputText styleClass="inputText" 
									 id="htmlTekiyoCdNaiyo" 
									 size="6" 
									 style="#{pc_Keb11304.propTekiyoCdNaiyo.style}" 
									 maxlength="#{pc_Keb11304.propTekiyoCdNaiyo.maxLength}" 
									 disabled="#{pc_Keb11304.propTekiyoCdNaiyo.disabled}" 
									 readonly="#{pc_Keb11304.propTekiyoCdNaiyo.readonly}" 
									 rendered="#{pc_Keb11304.propTekiyoCdNaiyo.rendered}" 
									 value="#{pc_Keb11304.propTekiyoCdNaiyo.stringValue}" 
									 onblur="return func_CogKeTekiNaiyoAJAX(this, event);" 
									 onfocus="return func_TekiyoNaiyoFocus(this, event);">
						</h:inputText><hx:commandExButton type="submit" 
											styleClass="commandExButton_search" 
											id="tekiyoNaiyoSearch" 
											action="#{pc_Keb11304.doTekiyoNaiyoSearchAction}">
						</hx:commandExButton>
					</TD>
					<TH class="v_d" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblTekiyoNameNaiyo" 
									  style="#{pc_Keb11304.propTekiyoNameNaiyo.labelStyle}" 
									  value="#{pc_Keb11304.propTekiyoNameNaiyo.labelName}">
						</h:outputText>
					</TH>
					<TD width="480" colspan="2">
						<h:inputText styleClass="inputText" 
									 id="htmlTekiyoNameNaiyo" 
									 size="68" 
									 style="#{pc_Keb11304.propTekiyoNameNaiyo.style}" 
									 maxlength="#{pc_Keb11304.propTekiyoNameNaiyo.maxLength}" 
									 disabled="#{pc_Keb11304.propTekiyoNameNaiyo.disabled}" 
									 readonly="#{pc_Keb11304.propTekiyoNameNaiyo.readonly}" 
									 rendered="#{pc_Keb11304.propTekiyoNameNaiyo.rendered}" 
									 value="#{pc_Keb11304.propTekiyoNameNaiyo.stringValue}">
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_e" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblTekiyoCdYoto" 
									  style="#{pc_Keb11304.propTekiyoCdYoto.labelStyle}" 
									  value="#{pc_Keb11304.propTekiyoCdYoto.labelName}">
						</h:outputText>
					</TH>
					<TD width="130">
						<h:inputText styleClass="inputText" 
									 id="htmlTekiyoCdYoto" 
									 size="6" 
									 style="#{pc_Keb11304.propTekiyoCdYoto.style}" 
									 maxlength="#{pc_Keb11304.propTekiyoCdYoto.maxLength}" 
									 disabled="#{pc_Keb11304.propTekiyoCdYoto.disabled}" 
									 readonly="#{pc_Keb11304.propTekiyoCdYoto.readonly}" 
									 rendered="#{pc_Keb11304.propTekiyoCdYoto.rendered}" 
									 value="#{pc_Keb11304.propTekiyoCdYoto.stringValue}" 
									 onblur="return func_CogKeTekiYotoAJAX(this, event);" 
									 onfocus="return func_TekiyoYotoFocus(this, event);">
						</h:inputText><hx:commandExButton type="submit" 
											styleClass="commandExButton_search" 
											id="tekiyoYotoSearch" 
											action="#{pc_Keb11304.doTekiyoYotoSearchAction}">
						</hx:commandExButton>
					</TD>
					<TH class="v_f" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblTekiyoNameYoto" 
									  style="#{pc_Keb11304.propTekiyoNameYoto.labelStyle}" 
									  value="#{pc_Keb11304.propTekiyoNameYoto.labelName}">
						</h:outputText>
					</TH>
					<TD width="480" colspan="2">
						<h:inputText styleClass="inputText" 
									 id="htmlTekiyoNameYoto" 
									 size="68" 
									 style="#{pc_Keb11304.propTekiyoNameYoto.style}" 
									 maxlength="#{pc_Keb11304.propTekiyoNameYoto.maxLength}" 
									 disabled="#{pc_Keb11304.propTekiyoNameYoto.disabled}" 
									 readonly="#{pc_Keb11304.propTekiyoNameYoto.readonly}" 
									 rendered="#{pc_Keb11304.propTekiyoNameYoto.rendered}" 
									 value="#{pc_Keb11304.propTekiyoNameYoto.stringValue}">
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_g" width="145">
						<h:outputText styleClass="outputText" 
									  id="lblBiko" 
									  style="#{pc_Keb11304.propBiko.labelStyle}" 
									  value="#{pc_Keb11304.propBiko.labelName}">
						</h:outputText>
					</TH>
					<TD width="755" colspan="4">
						<h:inputTextarea styleClass="inputTextarea" 
										 id="htmlBiko" 
										 cols="88" 
										 rows="2" 
										 style="#{pc_Keb11304.propBiko.style}" 
										 readonly="#{pc_Keb11304.propBiko.readonly}" 
										 disabled="#{pc_Keb11304.propBiko.disabled}" 
										 value="#{pc_Keb11304.propBiko.stringValue}">
						</h:inputTextarea>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		<BR>
		<TABLE border="0" 
			   class="table" 
			   width="900" 
			   cellspacing="0" 
			   cellpadding="0" 
			   style="margin-top: 10px;">
			<TBODY>
				<TR>
					<TD class="group_label_top" width="145" colspan="2" class="clear_border" nowrap>
						<h:outputText styleClass="outputText" 
									  id="lblKenkyu" 
									  value="研究費">
						</h:outputText>
					</TD>
					<TD width="755" colspan="3">
						<hx:commandExButton type="submit" 
											styleClass="commandExButton_search" 
											id="kenkyuSearch" 
											value="検" 
											action="#{pc_Keb11304.doKenkyuSearchAction}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											styleClass="commandExButton_listclear" 
											id="kenkyuClear" 
											value="ク" 
											action="#{pc_Keb11304.doKenkyuClearAction}">
						</hx:commandExButton>
						<!-- 画面項目自動表示処理用隠しボタン -->
						<DIV style="display:none;">
							<hx:commandExButton type="submit"
												styleClass="commandExButton"
												id="kenkyuAutoDisp"
												value="自動表示"
												action="#{pc_Keb11304.doKenkyuAutoDispAction}">
							</hx:commandExButton>
						</DIV>
					</TD>
				</TR>
				<TR>
					<TD class="group_label" width="20">
					</TD>
					<TH class="v_a" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblKenkyuKadai" 
									  style="#{pc_Keb11304.propKenkyuKadai.labelStyle}" 
									  value="#{pc_Keb11304.propKenkyuKadai.name}">
						</h:outputText>
					</TH>
					<TD width="755" colspan="3">
						<DIV style="width:755px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputHidden value="#{pc_Keb11304.propHakkoNendoHidden.stringValue}"
										   id="htmlHakkoNendoHidden">
							</h:inputHidden>
							<h:inputHidden value="#{pc_Keb11304.propKenkyuKadaiKbnHidden.stringValue}"
										   id="htmlKenkyuKadaiKbnHidden">
							</h:inputHidden>
							<h:inputHidden value="#{pc_Keb11304.propKenkyuKadaiHidden.stringValue}"
										   id="htmlKenkyuKadaiHidden">
							</h:inputHidden>
							<h:outputText styleClass="outputText" 
										  id="htmlHakkoNendo" 
										  style="#{pc_Keb11304.propHakkoNendo.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propHakkoNendo.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlKenkyuKadai" 
										  style="#{pc_Keb11304.propKenkyuKadai.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propKenkyuKadai.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlKenkyuKadaiName" 
										  style="#{pc_Keb11304.propKenkyuKadaiName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propKenkyuKadaiName.stringValue}" 
										  title="#{pc_Keb11304.propKenkyuKadaiName.stringValue}">
							</h:outputText>
						</DIV>
					</TD>
				</TR>
				<TR>
					<TD class="group_label" width="20">
					</TD>
					<TH class="v_b" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblKenkyuUser" 
									  style="#{pc_Keb11304.propKenkyuUser.labelStyle}" 
									  value="#{pc_Keb11304.propKenkyuUser.name}">
						</h:outputText>
					</TH>
					<TD width="755" colspan="3">
						<DIV style="width:755px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputHidden value="#{pc_Keb11304.propKenkyuUserHidden.stringValue}"
										   id="htmlKenkyuUserHidden">
							</h:inputHidden>
							<h:outputText styleClass="outputText" 
										  id="htmlKenkyuUser" 
										  style="#{pc_Keb11304.propKenkyuUser.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propKenkyuUser.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlKenkyuUserName" 
										  style="#{pc_Keb11304.propKenkyuUserName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propKenkyuUserName.stringValue}" 
										  title="#{pc_Keb11304.propKenkyuUserName.stringValue}">
							</h:outputText>
						</DIV>
					</TD>
				</TR>
				<TR>
					<TD class="group_label_bottom" width="20">
					</TD>
					<TH class="v_c" width="125">
						<h:outputText styleClass="outputText" 
									  id="lblUchiMeisai" 
									  style="#{pc_Keb11304.propUchiMeisai.labelStyle}" 
									  value="#{pc_Keb11304.propUchiMeisai.name}">
						</h:outputText>
					</TH>
					<TD width="575">
						<DIV style="width:575px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputHidden value="#{pc_Keb11304.propUchiPtnCdHidden.stringValue}"
										   id="htmlUchiPtnCdHidden">
							</h:inputHidden>
							<h:inputHidden value="#{pc_Keb11304.propUchiMeisaiHidden.stringValue}"
										   id="htmlUchiMeisaiHidden">
							</h:inputHidden>
							<h:outputText styleClass="outputText" 
										  id="htmlUchiMeisai" 
										  style="#{pc_Keb11304.propUchiMeisai.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propUchiMeisai.stringValue}">
							</h:outputText>　
							<h:outputText styleClass="outputText" 
										  id="htmlUchiMeisaiName" 
										  style="#{pc_Keb11304.propUchiMeisaiName.style};white-space:nowrap;" 
										  value="#{pc_Keb11304.propUchiMeisaiName.stringValue}" 
										  title="#{pc_Keb11304.propUchiMeisaiName.stringValue}">
							</h:outputText>
						</DIV>
					</TD>
					<TH class="v_d" width="70">
						<h:outputText styleClass="outputText" 
									  id="lblUchiMeisaiZandaka" 
									  style="#{pc_Keb11304.propUchiMeisaiZandaka.labelStyle}" 
									  value="#{pc_Keb11304.propUchiMeisaiZandaka.name}">
						</h:outputText>
					</TH>
					<TD width="110" style="text-align: right">
						<h:outputText styleClass="outputText" 
									  id="htmlUchiMeisaiZandaka" 
									  style="#{pc_Keb11304.propUchiMeisaiZandaka.style}" 
									  value="#{pc_Keb11304.propUchiMeisaiZandaka.longValue}" 
									  rendered="#{pc_Keb11304.propUchiMeisaiZandaka.rendered}">
							<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
						</h:outputText>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		<BR>
		<TABLE border="0" 
			   cellpadding="0" 
			   cellspacing="0" 
			   style="margin-top: 10px;" 
			   class="button_bar" 
			   width="900">
			<TBODY>
				<TR>
					<TD align="center" style="margin-top:10px;">
						<hx:commandExButton type="submit" 
											value="明細登録" 
											styleClass="commandExButton_dat" 
											id="register" 
											action="#{pc_Keb11304.doRegisterAction}" 
											disabled="#{pc_Keb11304.propRegister.disabled}" 
											rendered="#{pc_Keb11304.propRegister.rendered}" 
											style="#{pc_Keb11304.propRegister.style}" 
											confirm="#{msg.SY_MSG_0002W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="明細削除" 
											styleClass="commandExButton_dat" 
											id="delete" 
											action="#{pc_Keb11304.doDeleteAction}" 
											disabled="#{pc_Keb11304.propDelete.disabled}" 
											rendered="#{pc_Keb11304.propDelete.rendered}" 
											style="#{pc_Keb11304.propDelete.style}" 
											confirm="#{msg.SY_MSG_0004W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="クリア" 
											styleClass="commandExButton_etc" 
											id="clear" 
											action="#{pc_Keb11304.doClearAction}" 
											disabled="#{pc_Keb11304.propClear.disabled}" 
											rendered="#{pc_Keb11304.propClear.rendered}" 
											style="#{pc_Keb11304.propClear.style}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</CENTER>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden value="#{pc_Keb11304.propKaikeiNendoHidden.integerValue}" 
			   id="htmlKaikeiNendoHidden">
</h:inputHidden>
<h:inputHidden value="#{pc_Keb11304.propExecutableRegister.integerValue}" 
			   id="htmlExecutableRegister">
</h:inputHidden>
<h:inputHidden value="#{pc_Keb11304.propBeforeShikkoKmkCdHidden.stringValue}" 
			   id="htmlBeforeShikkoKmkCdHidden">
</h:inputHidden>
<h:inputHidden
	id="htmlFormatNumberOption"
	value="htmlShikkoGaku=###,###,###,##0;###,###,###,##0">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
