<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kma00203.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kma00203.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
	function init() {
		if(document.getElementById("form1:htmlGakusekiCd").value != "" && document.getElementById("form1:htmlAjaxName").value != ""){
			document.getElementById("form1:htmlGakusekiName").innerHTML = document.getElementById("form1:htmlAjaxName").value;
		}
	}
	function func_1(thisObj, thisEvent) {
		// 卒業生名称を取得する                                                                                                                                                                                           
		var servlet = "rev/km/KmCobSotAJAX";
		var target = "form1:htmlGakusekiName";
		var args = new Array();
		args['code1'] = document.getElementById('form1:htmlGakusekiCd').value;
		args['code2'] = document.getElementById('form1:htmlSotNendo').value;
		args['code3'] = document.getElementById('form1:htmlSotGakkiNo').value;
		var ajaxUtil = new AjaxUtil();
		//ajaxUtil.getCodeName(servlet, target, args);
		ajaxUtil.getPluralValue(servlet, target, args);
		//return true;
	}
	function func_2(thisObj, thisEvent) {
		//卒業生検索画面を開き
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0201.jsp?retFieldName=form1:htmlGakusekiCd&retFieldName2=form1:htmlSotNendo&retFieldName3=form1:htmlSotGakkiNo";
		 openModalWindow(url, "pCob0201", "<%=com.jast.gakuen.rev.co.PCob0201.getWindowOpenOption() %>");
	}
	function func_3(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		openModalWindow("", "PCos0402",
		"<%=com.jast.gakuen.rev.co.PCos0402.getWindowOpenOption() %>");
		setTarget("PCos0402"); return true;
	}
	function callBackMethod(value){
		try{
			document.getElementById("form1:htmlGakusekiName").innerHTML = value["name"];
			document.getElementById("form1:htmlAjaxName").value = value["name"];
		} catch (e) {
		}
	}
	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfilm").value = "0";
			document.getElementById("form1:htmlAction").value = "";
		} catch (e) {
		}
	}
	// 
	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfilm").value = "1";
			indirectClick(document.getElementById("form1:htmlAction").value);
		} catch (e) {
		}
	}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<body onload="init();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kma00203.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kma00203.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kma00203.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kma00203.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> 
             
             <hx:commandExButton
				type="submit" value="戻　る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Kma00203.doReturnDispAction}"></hx:commandExButton>

			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="800" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="120"><h:outputText styleClass="outputText"
							id="lblBunmenName" value="#{pc_Kma00203.kmCodeBase02.propBunmenName.name}"></h:outputText></TH>
						<TD colspan="3" width="260"><h:outputText styleClass="outputText"
							id="HtmlBunmenName"
							value="#{pc_Kma00203.kmCodeBase02.propBunmenName.stringValue}"></h:outputText></TD>
						<TH class="v_b" width="120"><h:outputText styleClass="outputText"
							id="lblBunmenKbn" value="#{pc_Kma00203.kmCodeBase02.propBunmenKbn.labelName}"
							style="#{pc_Kma00203.kmCodeBase02.propBunmenKbn.labelStyle}"></h:outputText></TH>
						<TD width="260"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlBunmenKbn" value="#{pc_Kma00203.kmCodeBase02.propBunmenKbn.value}"
							style="width:280px;">
							<f:selectItems value="#{pc_Kma00203.kmCodeBase02.propBunmenKbn.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH class="v_c" width="120"><h:outputText styleClass="outputText"
							id="lblSyomMktk" value="#{pc_Kma00203.kmCodeBase02.propSyomMktk.labelName}"
							style="#{pc_Kma00203.kmCodeBase02.propSyomMktk.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSyomMktk" value="#{pc_Kma00203.kmCodeBase02.propSyomMktk.value}"
							style="width:280px;"
							disabled="#{pc_Kma00203.kmCodeBase02.propSyomMktk.disabled}">
							<f:selectItems value="#{pc_Kma00203.kmCodeBase02.propSyomMktk.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			<TABLE width="800" class="table">
				<TBODY>
					<TR>
						<TH width="100" class="v_c"><h:outputText styleClass="outputText"
							id="lblFileUpLoad" value="#{pc_Kma00203.propFileUpLoad.labelName}"
							style="#{pc_Kma00203.propFileUpLoad.labelStyle}"></h:outputText><BR>
						<h:outputText styleClass="outputText" id="text14"
							value="#{pc_Kma00203.propZenkaiFile.labelName}"></h:outputText></TH>
						<TD colspan="3" width="700" valign="middle"><hx:fileupload
							styleClass="fileupload" value="#{pc_Kma00203.propFileUpLoad.value}" id="htmlFileUpLoad" style="width: 90%;">
							<hx:fileProp name="fileName" value="#{pc_Kma00203.propFileUpLoad.fileName}"/>
							<hx:fileProp name="contentType" />
						</hx:fileupload><hx:commandExButton type="submit" value="取込"
							styleClass="commandExButton" id="load"
							action="#{pc_Kma00203.doReadAction}"></hx:commandExButton> <BR>
						<h:outputText styleClass="outputText" id="htmlZenkaiFile"
							value="#{pc_Kma00203.propZenkaiFile.stringValue}"></h:outputText>
						</TD>
					</TR>
					<TR>
						<TH width="100" class="v_d"><h:outputText styleClass="outputText"
							id="lblSotNendo" value="#{pc_Kma00203.propSotNendo.labelName}"
							style="#{pc_Kma00203.propSotNendo.labelStyle}"></h:outputText></TH>
						<TD width="100"><h:inputText styleClass="inputText" id="htmlSotNendo"
							size="4" value="#{pc_Kma00203.propSotNendo.integerValue}"
							maxlength="#{pc_Kma00203.propSotNendo.maxLength}"
							style="#{pc_Kma00203.propSotNendo.style}"
							onblur="return func_1(this, event);">
							<f:convertNumber type="number" pattern="###0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText></TD>
						<TH width="150" class="v_e"><h:outputText styleClass="outputText"
							id="lblSotGakkiNo" value="#{pc_Kma00203.propSotGakkiNo.labelName}"
							style="#{pc_Kma00203.propSotGakkiNo.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlSotGakkiNo" size="2"
							value="#{pc_Kma00203.propSotGakkiNo.integerValue}"
							style="#{pc_Kma00203.propSotGakkiNo.style}"
							maxlength="#{pc_Kma00203.propSotGakkiNo.maxLength}"
							onblur="return func_1(this, event);">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText></TD>
					</TR>
					<TR>
						<TH width="100" class="v_e">
							<h:outputText styleClass="outputText"
							id="lblGakusekiCd" value="#{pc_Kma00203.propGakusekiCd.labelName}">
							</h:outputText>
						</TH>
						<TD colspan="3"><h:inputText styleClass="inputText"
							id="htmlGakusekiCd"  size="11" maxlength="10"
							value="#{pc_Kma00203.propGakusekiCd.stringValue}"
							onblur="return func_1(this, event);"
							style="ime-mode:disabled"></h:inputText><B><hx:commandExButton
							type="button" styleClass="commandExButton_search"
							id="search" onclick="return func_2(this, event);"></hx:commandExButton><hx:commandExButton
							type="submit" value="追加" styleClass="commandExButton" id="add"
							action="#{pc_Kma00203.doAddAction}"></hx:commandExButton></B><h:outputText
							styleClass="outputText" id="lblBlank"  value="　"></h:outputText><h:outputText
							styleClass="outputText" id="htmlGakusekiName"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<CENTER>
			<TABLE width="800">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText styleClass="outputText"
							id="lblChohukuSitei" value="(重複指定可能)"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>
			<BR>
			<BR>
			<CENTER>
			<TABLE width="800">
				<TBODY>
					<TR>
						<TD width="80" align="left"><h:outputText styleClass="outputText"
							id="lblListSotNendo" value="卒業年度"></h:outputText></TD>
						<TD width="100" align="left"><h:outputText styleClass="outputText" 
							id="lblListSotGakkiNo" value="卒業学期ＮＯ"></h:outputText></TD>
						<TD width="80" align="left"><h:outputText styleClass="outputText" 
							id="lblListGakusekiCd" value="学籍番号"></h:outputText></TD>
						<TD width="420" align="left"><h:outputText styleClass="outputText"
							id="lblListGakusekiName" value="卒業時氏名"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="800">
				<TBODY>
					<TR>
						<TD width=700">
							<h:selectManyListbox styleClass="selectManyListbox"
								id="htmlList" size="10" style="width: 100%"
								value="#{pc_Kma00203.propList.value}">
									<f:selectItems value="#{pc_Kma00203.propList.list}" />
							</h:selectManyListbox>
						</TD>
						<TD width="100" valign="top" align="center">
							<hx:commandExButton type="submit"
								value="除外" styleClass="commandExButton" id="remove"
								action="#{pc_Kma00203.doRemoveAction}"
								style="width:60px">
							</hx:commandExButton><BR>
							<h:outputText styleClass="outputText"
								id="lblHukusuSentak" value="(複数選択可)">
							</h:outputText><BR>
							<hx:commandExButton type="submit"
								value="全て除外" styleClass="commandExButton"
								id="removeAll" action="#{pc_Kma00203.doRemoveAllAction}"
								style="width:60px">
							</hx:commandExButton>
						</TD>
					</TR>
						<TR>
							<TD align="right">
								<h:outputText styleClass="outputText"
									id="htmlOutMessage"	value="#{pc_Kma00203.propOutMessage.stringValue}">
								</h:outputText>
							</TD>
							<TD></TD>
						</TR>
					</TBODY>
				</TABLE>
			</CENTER>

			<BR>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfOut"
							action="#{pc_Kma00203.doPdfOutAction}"
							confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="CSV作成" styleClass="commandExButton_out"
							id="csvOut" action="#{pc_Kma00203.doCsvOutAction}"
							confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="出力項目指定" styleClass="commandExButton_out"
							id="setoutput"
							action="#{pc_Kma00203.doSetoutputAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="印刷" styleClass="commandExButton_out"
							id="print" confirm="#{msg.SY_MSG_0022W}"
							action="#{pc_Kma00203.doPrintAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden
				value="#{pc_Kma00203.propConfilm.stringValue}"
				id="htmlConfilm">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kma00203.propAction.stringValue}"
				id="htmlAction">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kma00203.propAjaxName.stringValue}"
				id="htmlAjaxName">
			</h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

