<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab01502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>固定資産併合登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

var execId = '';
function setExecId(id) {
	// 押下したボタンIDをセット
	execId = id;
}
function confirmOk() {
	document.getElementById('form1:htmlExecutableHeigo').value = "1";	
	indirectClick('exec');	
}
function confirmCancel() {
	document.getElementById('form1:htmlSisnMkrkExistChkFlg').value = "1";
	document.getElementById('form1:htmlSisnYotoExistChkFlg').value = "1";
	document.getElementById('form1:htmlOldSyokyakRirkChkFlg').value = "1";
}
function onLoad(){
	// ページ遷移時のイベントを設定
	addCheckEvent('htmlSisnList');
}
function submitMethod() {
	// pager実行時にチェックをクリアする
	if ('' == execId) {
		uncheck('htmlSisnList','htmlCheck');
	}
	execId = '';
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="return onLoad();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kab01502.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				onclick="return setExecId('closeDisp');"
				action="#{pc_Kab01502.doCloseDispAction}"></hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kab01502.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kab01502.screenName}"></h:outputText>
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton" id="returnDisp"
				onclick="return setExecId('returnDisp');"
				action="#{pc_Kab01502.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="860" style="margin-top:10px">
				<TBODY>
					<TR>
						<TD align="right" width="860"><h:outputText
							styleClass="outputText" id="htmlListCount"
							value="#{pc_Kab01502.propSisnList.listCount}件"
							style="font-size: 8pt"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="860" height="310px">
				<TBODY>
					<TR>
						<TD width="860" valign="top">
						<h:dataTable border="0" cellpadding="2"
							cellspacing="0" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Kab01502.propSisnList.rowClasses}"
							styleClass="meisai_page" id="htmlSisnList" width="860"
							value="#{pc_Kab01502.propSisnList.list}" var="varlist"
							first="#{pc_Kab01502.propSisnList.first}"
							rows="#{pc_Kab01502.propSisnList.rows}">
							<h:column id="column1">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="35" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
									id="htmlCheck" value="#{varlist.selected}"></h:selectBooleanCheckbox>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblListSsnKbnName" styleClass="outputText"
										value="資産区分"></h:outputText>
								</f:facet>
								<f:attribute value="113" name="width" />
								<h:outputText styleClass="outputText" id="htmlListSsnKbnName"
									value="#{varlist.ssnKbnName}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblListSsnNo" styleClass="outputText"
										value="#{pc_Kab01502.propSsnNoMoto.name}"></h:outputText>
								</f:facet>
								<f:attribute value="178" name="width" />
								<h:outputText styleClass="outputText" id="htmlListSsnNo"
									value="#{varlist.ssnNo}"></h:outputText>
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblListSsnNameRyak" styleClass="outputText"
										value="#{pc_Kab01502.propSsnNameRyakMoto.name}"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel1">
									<DIV style="width:178px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText styleClass="outputText" id="htmlListSsnNameRyak"
										title="#{varlist.ssnNameRyak}" value="#{varlist.ssnNameRyak}"></h:outputText>
									</DIV>
								</hx:jspPanel>
								<f:attribute value="178" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblListSsnBnrNameRyak"
										styleClass="outputText"
										value="#{pc_Kab01502.propSsnBnrNameRyakMoto.name}"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel2">
									<DIV style="width:178px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText styleClass="outputText"
										id="htmlListSsnBnrNameRyak" title="#{varlist.ssnBnrNameRyak}"
										value="#{varlist.ssnBnrNameRyak}"></h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="178" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText id="lblListSetchiBashoNameRyak"
										styleClass="outputText"
										value="#{pc_Kab01502.propSetchiBashoNameRyakMoto.name}"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel3">
									<DIV style="width:178px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText styleClass="outputText"
										id="htmlListSetchiBashoNameRyak"
										title="#{varlist.setchiBashoNameRyak}"
										value="#{varlist.setchiBashoNameRyak}"></h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="178" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<f:facet name="footer">
								<hx:panelBox styleClass="panelBox" id="box1">
									<hx:pagerDeluxe styleClass="pagerDeluxe" id="deluxe1" />
									<hx:pagerGoto styleClass="pagerGoto" id="goto1" />
								</hx:panelBox>
							</f:facet>
						</h:dataTable>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0"
				style="margin-top: 10px;" width="860" class="table">
				<TBODY>
					<TR>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblJisshiDate"
							value="#{pc_Kab01502.propJisshiDate.labelName}"
							style="#{pc_Kab01502.propJisshiDate.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlJisshiDate" size="10"
							value="#{pc_Kab01502.propJisshiDate.dateValue}"
							style="#{pc_Kab01502.propJisshiDate.style}">
							<f:convertDateTime pattern="yyyy/MM/dd" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="" />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
						<TD colspan="4" class="clear_border" style="background-color:transparent;"></TD>
					</TR>
					<TR class="clear_border">
						<TD width="84" style="background-color:transparent;"></TD>
						<TD width="32" style="background-color:transparent;"></TD>
						<TD width="104" style="background-color:transparent;"></TD>
						<TD width="120" style="background-color:transparent;"></TD>
						<TD width="80" style="background-color:transparent;"></TD>
						<TD width="92" style="background-color:transparent;"></TD>
						<TD width="70" style="background-color:transparent;"></TD>
						<TD width="83" style="background-color:transparent;"></TD>
						<TD width="195" style="background-color:transparent;"></TD>
					</TR>
					<TR>
						<TD align="center" class="clear_border" style="background-color:transparent;">
							<hx:commandExButton
							type="submit" value="併合元選択" styleClass="commandExButton_dat" id="motoSelect"
							onclick="return setExecId('motoSelect');"
							action="#{pc_Kab01502.doMotoSelectAction}"></hx:commandExButton></TD>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TH class="v_b"><h:outputText styleClass="outputText"
							id="lblSsnKbnNameMoto"
							value="#{pc_Kab01502.propSsnKbnNameMoto.name}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText"
							id="htmlSsnKbnNameMoto"
							value="#{pc_Kab01502.propSsnKbnNameMoto.stringValue}"></h:outputText></TD>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblSsnNoMoto" value="#{pc_Kab01502.propSsnNoMoto.name}"></h:outputText></TH>
						<TD colspan="2"><h:outputText styleClass="outputText"
							id="htmlSsnNoMoto"
							value="#{pc_Kab01502.propSsnNoMoto.stringValue}"></h:outputText></TD>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblSsnNameRyakMoto"
							value="#{pc_Kab01502.propSsnNameRyakMoto.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:195px;white-space:nowrap;overflow:hidden;display:block;">
								<h:outputText styleClass="outputText"
									id="htmlSsnNameRyakMoto"
									value="#{pc_Kab01502.propSsnNameRyakMoto.stringValue}" title="#{pc_Kab01502.propSsnNameRyakMoto.stringValue}"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TH class="v_e" nowrap><h:outputText styleClass="outputText"
							id="lblSsnBnrNameRyakMoto"
							value="#{pc_Kab01502.propSsnBnrNameRyakMoto.name}"></h:outputText></TH>
						<TD colspan="2" nowrap>
							<DIV style="width:124px;white-space:nowrap;overflow:hidden;display:block;">
								<h:outputText styleClass="outputText"
									id="htmlSsnBnrNameRyakMoto"
									value="#{pc_Kab01502.propSsnBnrNameRyakMoto.stringValue}" title="#{pc_Kab01502.propSsnBnrNameRyakMoto.stringValue}"></h:outputText>
							</DIV>
						</TD>
						<TH class="v_f"><h:outputText styleClass="outputText"
							id="lblSetchiBashoNameRyakMoto"
							value="#{pc_Kab01502.propSetchiBashoNameRyakMoto.name}"></h:outputText></TH>
						<TD colspan="3" nowrap>
							<DIV style="width:348px;white-space:nowrap;overflow:hidden;display:block;">
								<h:outputText styleClass="outputText"
									id="htmlSetchiBashoNameRyakMoto"
									value="#{pc_Kab01502.propSetchiBashoNameRyakMoto.stringValue}" title="#{pc_Kab01502.propSetchiBashoNameRyakMoto.stringValue}"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TD colspan="8" class="clear_border" style="background-color:transparent;"></TD>
					</TR>
					<TR>
						<TD align="center" class="clear_border" style="background-color:transparent;">
							<hx:commandExButton
							type="submit" value="併合先選択" styleClass="commandExButton_dat" id="sakiSelect"
							onclick="return setExecId('sakiSelect');"
							action="#{pc_Kab01502.doSakiSelectAction}"></hx:commandExButton></TD>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TH class="v_g"><h:outputText styleClass="outputText"
							id="lblSsnKbnNameSaki"
							value="#{pc_Kab01502.propSsnKbnNameSaki.name}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText"
							id="htmlSsnKbnNameSaki"
							value="#{pc_Kab01502.propSsnKbnNameSaki.stringValue}"></h:outputText></TD>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblSsnNoSaki" value="#{pc_Kab01502.propSsnNoSaki.name}"></h:outputText></TH>
						<TD colspan="2"><h:outputText styleClass="outputText"
							id="htmlSsnNoSaki"
							value="#{pc_Kab01502.propSsnNoSaki.stringValue}"></h:outputText></TD>
						<TH class="v_b"><h:outputText styleClass="outputText"
							id="lblSsnNameRyakSaki"
							value="#{pc_Kab01502.propSsnNameRyakSaki.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:195px;white-space:nowrap;overflow:hidden;display:block;">
								<h:outputText styleClass="outputText"
									id="htmlSsnNameRyakSaki"
									value="#{pc_Kab01502.propSsnNameRyakSaki.stringValue}" title="#{pc_Kab01502.propSsnNameRyakSaki.stringValue}"></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TD class="clear_border" style="background-color:transparent;"></TD>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblSsnBnrNameRyakSaki"
							value="#{pc_Kab01502.propSsnBnrNameRyakSaki.name}"></h:outputText></TH>
						<TD colspan="2" nowrap>
							<DIV style="width:124px;white-space:nowrap;overflow:hidden;display:block;">
								<h:outputText styleClass="outputText"
									id="htmlSsnBnrNameRyakSaki"
									value="#{pc_Kab01502.propSsnBnrNameRyakSaki.stringValue}" title="#{pc_Kab01502.propSsnBnrNameRyakSaki.stringValue}"></h:outputText>
							</DIV>
						</TD>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblSetchiBashoNameRyakSaki"
							value="#{pc_Kab01502.propSetchiBashoNameRyakSaki.name}"></h:outputText></TH>
						<TD colspan="3" nowrap>
							<DIV style="width:348px;white-space:nowrap;overflow:hidden;display:block;">
								<h:outputText styleClass="outputText"
									id="htmlSetchiBashoNameRyakSaki"
									value="#{pc_Kab01502.propSetchiBashoNameRyakSaki.stringValue}" title="#{pc_Kab01502.propSetchiBashoNameRyakSaki.stringValue}"></h:outputText>
							</DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="860" border="0" style="margin-top: 10px;" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="併合"
								styleClass="commandExButton_dat" id="exec"
								onclick="return setExecId('exec');"
								action="#{pc_Kab01502.doExecAction}"
								confirm="#{msg.SY_MSG_0001W}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="クリア"
								styleClass="commandExButton_etc" id="clear"
								onclick="return setExecId('clear');"
								action="#{pc_Kab01502.doClearAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<hx:inputHelperSetFocus target="htmlJisshiDate"></hx:inputHelperSetFocus>
			<h:inputHidden
				value="#{pc_Kab01502.propExecutableHeigo.integerValue}"
				id="htmlExecutableHeigo">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab01502.propSisnMkrkExistChkFlg.integerValue}"
				id="htmlSisnMkrkExistChkFlg">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab01502.propSisnYotoExistChkFlg.integerValue}"
				id="htmlSisnYotoExistChkFlg">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab01502.propOldSyokyakRirkChkFlg.integerValue}"
				id="htmlOldSyokyakRirkChkFlg">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kab01502.propHiddenCheck.stringValue}"
				id="htmlHiddenCheck">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

