<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab00812.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>リース支払詳細一覧</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function loadFunc() {
	changeScrollPosition('scroll','listScroll');
}
window.attachEvent("onload", loadFunc);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY>
<hx:scriptCollector id="scriptCollector1"
	preRender="#{pc_Kab00812.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
	value="閉じる" styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kab00812.doCloseDispAction}"></hx:commandExButton> <h:outputText
	styleClass="outputText" id="htmlFuncId"
	value="#{pc_Kab00812.funcId}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlLoginId"
	value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlScrnName"
	value="#{pc_Kab00812.screenName}"></h:outputText></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
	id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText></FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<!-- ↓ここに戻る／閉じるボタンを配置 --> 
	<hx:commandExButton type="submit" value="戻る" styleClass="commandExButton"
	id="returnDisp" action="#{pc_Kab00812.doReturnDispAction}" tabindex="2">
	</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="880" style="margin-top: 20px;">
		<TBODY>
			<TR>
				<TH class="v_a" width="100"><h:outputText styleClass="outputText"
					id="lblLeaseNo"
					value="#{pc_Kab00812.propLeaseNo.labelName}"
					style="#{pc_Kab00812.propLeaseNo.labelStyle}"></h:outputText></TH>
				<TD width="150"><h:outputText styleClass="outputText"
							id="htmlLeaseNo" value="#{pc_Kab00812.propLeaseNo.stringValue}"
							title="#{pc_Kab00812.propLeaseNo.stringValue}"></h:outputText></TD>
				<TH class="v_b" width="100"><h:outputText styleClass="outputText"
							id="lblShiharaiKikan"
							value="#{pc_Kab00812.propShiharaiKikan.labelName}"
							style="#{pc_Kab00812.propShiharaiKikan.labelStyle}"></h:outputText></TH>
				<TD width="140"><h:outputText styleClass="outputText"
							id="htmlShiharaiKikan"
							value="#{pc_Kab00812.propShiharaiKikan.stringValue}"></h:outputText>
				</TD>
				<TH class="v_b" width="100"><h:outputText styleClass="outputText"
							id="lblShiharaiKbn"
							value="#{pc_Kab00812.propShiharaiKbn.labelName}"
							style="#{pc_Kab00812.propShiharaiKbn.labelStyle}"></h:outputText></TH>
				<TD width="100"><h:outputText styleClass="outputText"
							id="htmlShiharaiKbn"
							value="#{pc_Kab00812.propShiharaiKbn.stringValue}"
							title="#{pc_Kab00812.propShiharaiKbn.stringValue}"></h:outputText>
				</TD>
				<TH class="v_d" width="100"><h:outputText styleClass="outputText"
							id="lblKaikeiNendo"
							value="#{pc_Kab00812.propKaikeiNendo.labelName}"
							style="#{pc_Kab00812.propKaikeiNendo.labelStyle}"></h:outputText></TH>
				<TD width="90"><h:outputText styleClass="outputText"
							id="htmlKaikeiNendo"
							value="#{pc_Kab00812.propKaikeiNendo.stringValue}"></h:outputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_e" width="100">
						<h:outputText styleClass="outputText"
							id="lblKeiyakuBukkenName"
							value="#{pc_Kab00812.propKeiyakuBukkenName.labelName}"
							style="#{pc_Kab00812.propKeiyakuBukkenName.labelStyle}"></h:outputText>
				</TH>
				<TD colspan="5" width="590">
				<DIV style="width:590px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlKeiyakuBukkenName"
							value="#{pc_Kab00812.propKeiyakuBukkenName.stringValue}"
							style="#{pc_Kab00812.propKeiyakuBukkenName.style}"
							title="#{pc_Kab00812.propKeiyakuBukkenName.stringValue}"></h:outputText>
				</DIV>			
				</TD>
				<TH class="v_f" width="100"><h:outputText styleClass="outputText"
							id="lblKeiyakuNendo"
							value="#{pc_Kab00812.propKeiyakuNendo.labelName}"
							style="#{pc_Kab00812.propKeiyakuNendo.labelStyle}"></h:outputText></TH>
				<TD width="90"><h:outputText styleClass="outputText"
							id="htmlKeiyakuNendo"
							value="#{pc_Kab00812.propKeiyakuNendo.stringValue}"></h:outputText>
				</TD>
			</TR>	
		</TBODY>
	</TABLE>
	<TABLE border="0" width="880">
		<TBODY>
			<TR>
				<TD align="right"><h:outputText
					styleClass="outputText" id="htmlListCount"
					value="#{pc_Kab00812.propLeaseShriList.listCount}件"
					style="font-size: 8pt"></h:outputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" width="880">
		<TBODY>
			<TR>
				<TD width="880">
					<DIV class="listScroll"
						style="height:400px; OVERFLOW:scroll;overflow-x: hidden;"
						id="listScroll" onscroll="setScrollPosition('scroll', this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Kab00812.propLeaseShriList.rowClasses}"
							styleClass="meisai_scroll" id="htmlLeaseShriList" width="860"
							value="#{pc_Kab00812.propLeaseShriList.list}"
							var="varlist"
							first="#{pc_Kab00812.propLeaseShriList.first}"
							rows="#{pc_Kab00812.propLeaseShriList.rows}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblListYsnTName" styleClass="outputText"
										value="予算単位名称"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel1">
									<DIV
										style="width:152px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText styleClass="outputText" id="htmlListYsnTName"
										value="#{varlist.ysnTName.displayValue}"
										style="white-space:nowrap;" title="#{varlist.ysnTName.value}">
									</h:outputText>
									</DIV>
								</hx:jspPanel>	
								<f:attribute value="152" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblListMokuName" styleClass="outputText"
										value="目的名称"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel2">
									<DIV
										style="width:182px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListMokuName"
										value="#{varlist.mokuName.displayValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.mokuName.value}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="182" name="width" />
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblListKmkName" styleClass="outputText"
										value="科目名称"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel3">
									<DIV
										style="width:190px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListKmkName"
										value="#{varlist.kmkName.displayValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.kmkName.value}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="190" name="width" />
								<f:attribute value="text-align: left" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblListNengetsu" styleClass="outputText"
										value="支払年月"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel4">
									<DIV
										style="width:68px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListNengetsu" value="#{varlist.nengetsu}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.nengetsu}">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="68" name="width" />
								<f:attribute value="text-align: center" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblListShiharaiYoteiGaku"
										styleClass="outputText" value="支払予定額"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel5">
									<DIV
										style="width:105px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListShiharaiYoteiGaku"
										value="#{varlist.shiharaiYoteiGaku}" styleClass="outputText"
										style="white-space:nowrap;">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="105" name="width" />
								<f:attribute value="padding-right: 3px; text-align: right" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText id="lblListShiwakeSakuseiGaku"
										styleClass="outputText" value="仕訳作成額"></h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel6">
									<DIV
										style="width:105px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListShiwakeSakuseiGaku"
										value="#{varlist.shiwakeSakuseiGaku}" styleClass="outputText"
										style="white-space:nowrap;">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="105" name="width" />
								<f:attribute value="padding-right: 3px; text-align: right" name="style" />
								<f:attribute value="true" name="nowrap" />
							</h:column>

							<h:column id="column7">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="detail" value="詳細" style="width:38"
									action="#{pc_Kab00812.doDetailAction}" tabindex="1"></hx:commandExButton>
								<f:attribute value="38px" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable>
					</DIV>
				</TD>
			</TR>
		</TBODY>
	</TABLE>

	<TABLE border="0" width="880">
		<TBODY>
			<TR>
				<TD width="440"></TD>
				<TD width="440">
						<TABLE border="0" width="440" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: black" class="">
							<TBODY>
								<TR>
									<TD align="left" width="80"><h:outputText
										styleClass="outputText" id="lblKeiyakuKei" value="契約計："></h:outputText></TD>
									<TD align="right" width="80"><h:outputText styleClass="outputText"
										id="lblShiharaiYoteiGakuKei" value="支払予定額"></h:outputText> </TD>
									<TD align="right" width="100">	
										<h:outputText styleClass="outputText"
										id="htmlShiharaiYoteiGakuKei" style="font-size: 8pt"
										value="#{pc_Kab00812.propShiharaiYoteiGakuKei.stringValue}">
									</h:outputText></TD>
									<TD align="right" width="80"><h:outputText styleClass="outputText"
										id="lblShiwakeSakuseiGakuKei" value="仕訳作成額"></h:outputText>
									</TD>
									<TD align="right" width="100">	
										<h:outputText styleClass="outputText"
										id="htmlShiwakeSakuseiGakuKei" style="font-size: 8pt"
										value="#{pc_Kab00812.propShiwakeSakuseiGakuKei.stringValue}">
									</h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
			</TR>
		</TBODY>
	</TABLE>



<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--></DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Kab00812.propLeaseShriList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

