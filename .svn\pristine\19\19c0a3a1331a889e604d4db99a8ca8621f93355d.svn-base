<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghf00601T02.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<TITLE>Ghf00601T02.jsp</TITLE>
	<SCRIPT type="text/javascript">
	
	function fncButtonActive(){
	
//	▽▽ 2007-05-25 Tsutsumi 障害No 7200 対応　変更　START ▽▽
//		changeScrollPosition('scrollpay', 'paylistScroll');
		window.attachEvent('onload', endload);		
//	△△ 2007-05-25 Tsutsumi 障害No 7200 対応　変更 END △△　
			
		//フォーカスの設定
		setFocus();
		
	}

//	▽▽ 2007-05-25 Tsutsumi 障害No 7200 対応　追加 START ▽▽
	function endload() {
		changeScrollPosition('scrollpay', 'paylistScroll');
	}
//	△△ 2007-05-25 Tsutsumi 障害No 7200 対応　追加 END △△　

	//フォーカスの設定
	function setFocus(){
		
		var id = null;

		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;

		//リスト内選択ボタンフォーカス時
		if ((id != null) && (id != "")) {
			document.getElementById(id).focus();
		}
		
		//初期化
		document.getElementById('form1:htmlScrollPos').value = "";

	}
		
	function func_syu_check_on(thisObj, thisEvent) {
		check('htmlSyugakSybtList','htmlSyugakCheck');
	}
	
	function func_syu_check_off(thisObj, thisEvent) {
		uncheck('htmlSyugakSybtList','htmlSyugakCheck');
	}
	
	function func_pay_check_on(thisObj, thisEvent) {
		check('htmlPayList','htmlPayCheck');
	}
	
	function func_pay_check_off(thisObj, thisEvent) {
		uncheck('htmlPayList','htmlPayCheck');
	}
	
	//帳票タイトル変更
	function changePdfTitle(thisObj) {
		var code = thisObj.value;
		var befCode = document.getElementById('form1:htmlBeforeValue').value;
		var title = document.getElementById('form1:htmlPdfTitle').value;
		var hid1 = document.getElementById('form1:htmlPdfId01').value;
		var hid2 = document.getElementById('form1:htmlPdfId02').value;
		
		if(befCode == 0){
			document.getElementById('form1:htmlPdfId01').value = title;
		} else {
			document.getElementById('form1:htmlPdfId02').value = title;
		}
		
		if(code == 0){
			document.getElementById('form1:htmlPdfTitle').value = hid1;
		} else {
			document.getElementById('form1:htmlPdfTitle').value = hid2;
		}
		
		document.getElementById('form1:htmlBeforeValue').value = code;
	}
	
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {

		var procPayListFind = document.getElementById('form1:htmlPayListSelect').value;

		//選択処理実行
		if(procPayListFind == "1"){
			indirectClick('search');
		}

	}
	
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlPayListSelect').value = "0";
	}

	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}
	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="fncButtonActive();">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghf00601T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">   

			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />
			
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
			<hx:commandExButton type="submit" value="閉じる"
				styleClass="commandExButton" id="closeDisp"
			    action="#{pc_Ghf00601T02.doCloseDispAction}">
			</hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghf00601T02.funcId}">
			</h:outputText>
			<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}">
			</h:outputText>
			<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghf00601T02.screenName}">
			</h:outputText>
</div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
											<TBODY>
												<TR align="left">
													<TD>
														<hx:commandExButton type="submit" 
															value="教務学生" styleClass="tab_head_off"
															id="kmTab" action="#{pc_Ghf00601T02.doHtmlKmTabAction}" 
															tabindex="1"></hx:commandExButton><hx:commandExButton
															type="submit" value="学費学生" styleClass="tab_head_on"
															id="link01" tabindex="1">
														</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="1" cellpadding="20" cellspacing="0" width="100%" class="tab_body">
															<TBODY>
																<TR align="center">
																	<TD height="20" >
																		<TABLE width="850px">
																			<TR>
																				<TD height="20"></TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR align="center">
																								<TH class="v_a" width="160px">
																									<h:outputText styleClass="outputText" id="lblGakunen"
																										value="#{pc_Ghf00601T02.propGakunen.labelName}"
																										style="#{pc_Ghf00601T02.propGakunen.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:selectOneMenu styleClass="selectOneMenu" id="htmlGakunen"
																										value="#{pc_Ghf00601T02.propGakunen.value}"
																										style="#{pc_Ghf00601T02.propGakunen.style}" tabindex="2">
																										<f:selectItems value="#{pc_Ghf00601T02.propGakunen.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_b" width="160px">
																									<h:outputText styleClass="outputText" id="lblSemester"
																										value="#{pc_Ghf00601T02.propSemester.labelName}"
																										style="#{pc_Ghf00601T02.propSemester.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:selectOneMenu styleClass="selectOneMenu" id="htmlSemester"
																										value="#{pc_Ghf00601T02.propSemester.value}"
																										style="#{pc_Ghf00601T02.propSemester.style}" tabindex="3">
																										<f:selectItems value="#{pc_Ghf00601T02.propSemester.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_c" width="160px">
																									<h:outputText styleClass="outputText" id="lblSzkGakka"
																										value="#{pc_Ghf00601T02.propSzkGakka.labelName}"
																										style="#{pc_Ghf00601T02.propSzkGakka.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:selectOneMenu styleClass="selectOneMenu" id="htmlSzkGakka"
																										value="#{pc_Ghf00601T02.propSzkGakka.value}"
																										style="#{pc_Ghf00601T02.propSzkGakka.style}" tabindex="4">
																										<f:selectItems value="#{pc_Ghf00601T02.propSzkGakka.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20px">
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH class="v_d" width="160px">
																									<h:outputText
																										styleClass="outputText" id="lblZaiSyut" 
																										value="#{pc_Ghf00601T02.propZaisyut.name}" 
																										style="#{pc_Ghf00601T02.propZaisyut.style}">
																									</h:outputText>
																								</TH>
																								<TD width="270px">
																									<h:selectOneRadio
																										disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" id="htmlZaisyut"
																										layout="pageDirection" value="#{pc_Ghf00601T02.propZaisyut.value}" 
																										style="#{pc_Ghf00601T02.propZaisyut.style}" tabindex="5">
																										<f:selectItem itemValue="0" itemLabel="全て" />
																										<f:selectItem itemValue="1" itemLabel="在学者" />
																										<f:selectItem itemValue="2" itemLabel="出学者" />
																									</h:selectOneRadio>
																								</TD>
																								<TH width="150px" rowspan="2" class="v_e" width="160px">
																									<h:outputText
																										styleClass="outputText" id="lblRyugak"
																										value="#{pc_Ghf00601T02.propNotRyugak.name}"
																										style="#{pc_Ghf00601T02.propNotRyugak.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD rowspan="2" width="*">
																									<TABLE border="0" cellpadding="0" cellspacing="0">
																										<TBODY>
																											<TR>
																												<TD class="clear_border">
																													<h:selectBooleanCheckbox
																														styleClass="selectBooleanCheckbox" id="htmlNotRyugak"
																														value="#{pc_Ghf00601T02.propNotRyugak.checked}" tabindex="7">
																													</h:selectBooleanCheckbox> <h:outputText
																														styleClass="outputText" id="text5" value="留学生以外">
																													</h:outputText>
																												</TD>
																											</TR>
																											<TR>
																												<TD class="clear_border">
																													<h:selectBooleanCheckbox
																														styleClass="selectBooleanCheckbox" id="htmlKokuhi"
																														value="#{pc_Ghf00601T02.propKokuhi.checked}" tabindex="8">
																													</h:selectBooleanCheckbox> <h:outputText
																														styleClass="outputText" id="text7" value="国費留学生">
																													</h:outputText>
																												</TD>
																											</TR>
																											<TR>
																												<TD class="clear_border">
																													<h:selectBooleanCheckbox
																														styleClass="selectBooleanCheckbox" id="htmlShihi"
																														value="#{pc_Ghf00601T02.propShihi.checked}" tabindex="9">
																													</h:selectBooleanCheckbox> <h:outputText
																														styleClass="outputText" id="text8" value="私費留学生">
																													</h:outputText>
																												</TD>
																											</TR>
																											<TR>
																												<TD class="clear_border">
																													<h:selectBooleanCheckbox
																														styleClass="selectBooleanCheckbox" id="htmlGaikoku"
																														value="#{pc_Ghf00601T02.propGaikoku.checked}" tabindex="10">
																													</h:selectBooleanCheckbox> <h:outputText
																														styleClass="outputText" id="text9" value="外国政府留学生">
																													</h:outputText>
																												</TD>
																											</TR>
																										</TBODY>
																									</TABLE>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_f" width="160px">
																									<h:outputText
																										styleClass="outputText" id="lblSyutgakNendo"
																										value="#{pc_Ghf00601T02.propSyutgakNendo.labelName}"
																										style="#{pc_Ghf00601T02.propSyutgakNendo.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:inputText styleClass="inputText" id="htmlSyutgakNendo"
																										value="#{pc_Ghf00601T02.propSyutgakNendo.value}"
																										style="#{pc_Ghf00601T02.propSyutgakNendo.style}" size="4"
																										maxlength="#{pc_Ghf00601T02.propSyutgakNendo.maxLength}" tabindex="6">
																									</h:inputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20px">
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																						
																							<TR>
																								<TH width="160px" class="v_b">
																									<h:outputText
																										styleClass="outputText" id="lblPayOutType"
																										value="#{pc_Ghf00601T02.propPayOutType.labelName}"
																										style="#{pc_Ghf00601T02.propPayOutType.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*" colspan=2>
																									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" 
																										 tabindex="11"
																										disabled="#{pc_Ghf00601T02.propPayOutType.disabled}"
																										id="htmlPayOutType"
																										value="#{pc_Ghf00601T02.propPayOutType.value}">
																										<f:selectItem itemValue="0" itemLabel="一覧より選択" />
																										<f:selectItem itemValue="1" itemLabel="入力条件で出力" />
																									</h:selectOneRadio>
																								</TD>
																								<TD rowspan=4 >
																									<hx:commandExButton type="submit"
																													value="選択" styleClass="cmdBtn_dat_s" id="search"
																													action="#{pc_Ghf00601T02.doSearchAction}" 
																													disabled="#{pc_Ghf00601T02.propGhYearFr.disabled}" tabindex="18">
																												</hx:commandExButton>
																												<hx:commandExButton type="submit"
																													value="解除" styleClass="cmdBtn_etc_s" id="unselect"
																													action="#{pc_Ghf00601T02.doUnselectAction}" 
																													disabled="#{!pc_Ghf00601T02.propGhYearFr.disabled}" tabindex="19">
																									</hx:commandExButton>
																								</TD>
																							</TR>
																						
																							<TR>
																								<TH width="160px" class="v_b">
																									<h:outputText
																										styleClass="outputText" id="lblGhYear"
																										value="#{pc_Ghf00601T02.propGhYear.labelName}"
																										style="#{pc_Ghf00601T02.propGhYear.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*" colspan=2>
																									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																										<TBODY>
																											<TR>
																												<TD class="clear_border" width="70px">
																													<h:inputText styleClass="inputText"
																														id="htmlGhYearFr"
																														value="#{pc_Ghf00601T02.propGhYearFr.dateValue}"
																														size="4"
																														disabled="#{pc_Ghf00601T02.propGhYearFr.disabled}"
																														maxlength="#{pc_Ghf00601T02.propGhYearFr.maxLength}"
																														style="#{pc_Ghf00601T02.propGhYearFr.style}" tabindex="12">
																														<hx:inputHelperAssist imeMode="inactive"
																															errorClass="inputText_Error" promptCharacter="_" />
																														<f:convertDateTime pattern="yyyy" />
																													</h:inputText>
																												</TD>
																												<TD class="clear_border" width="25px">
																													<h:outputText 
																														styleClass="outputText" 
																														id="text4" value="～">
																													</h:outputText>
																												</TD>
																												<TD class="clear_border" width="*">
																													<h:inputText styleClass="inputText"
																														id="htmlGhYearTo"
																														value="#{pc_Ghf00601T02.propGhYearTo.dateValue}"
																														size="4"
																														disabled="#{pc_Ghf00601T02.propGhYearTo.disabled}"
																														maxlength="#{pc_Ghf00601T02.propGhYearTo.maxLength}"
																														style="#{pc_Ghf00601T02.propGhYearTo.style}" tabindex="13">
																														<hx:inputHelperAssist imeMode="inactive"
																															errorClass="inputText_Error" promptCharacter="_" />
																														<f:convertDateTime pattern="yyyy" />
																													</h:inputText>
																												</TD>
																											</TR>
																										</TBODY>
																									</TABLE>
																								</TD>
																							</TR>
																							
																							
																							<TR>
																								<TH class="v_a" nowrap width="160px">
																									<h:outputText styleClass="outputText"
																										id="lblPayCode" 
																										value="#{pc_Ghf00601T02.propPayCode.labelName}"
																										style="#{pc_Ghf00601T02.propPayCode.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD nowrap width="*">
																									<h:inputText styleClass="inputText"
																										id="htmlPayCode" 
																										value="#{pc_Ghf00601T02.propPayCode.stringValue}"
																										style="#{pc_Ghf00601T02.propPayCode.style}" 
																										 tabindex="14"
																										disabled="#{pc_Ghf00601T02.propPayCode.disabled}"
																										size="6"
																										maxlength="#{pc_Ghf00601T02.propPayCode.maxLength}">
																									</h:inputText>
																								</TD>
																								<TD nowrap width="180px">
																									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" 
																										 tabindex="15"
																										disabled="#{pc_Ghf00601T02.propPayCdFindType.disabled}"
																										id="htmlPayCdFindType"
																										value="#{pc_Ghf00601T02.propPayCdFindType.value}">
																										<f:selectItem itemValue="0" itemLabel="前方一致" />
																										<f:selectItem itemValue="1" itemLabel="部分一致" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_b" nowrap nowrap width="160px">
																									<h:outputText styleClass="outputText" 
																										id="lblPayNm"
																										value="#{pc_Ghf00601T02.propPayNm.labelName}"
																										style="#{pc_Ghf00601T02.propPayNm.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD nowrap width="*">
																									<h:inputText styleClass="inputText"
																										id="htmlPayNm" 
																										value="#{pc_Ghf00601T02.propPayNm.value}"
																										style="#{pc_Ghf00601T02.propPayNm.style}" 
																										 tabindex="16"
																										disabled="#{pc_Ghf00601T02.propPayNm.disabled}"
																										size="40"
																										maxlength="#{pc_Ghf00601T02.propPayNm.maxLength}">
																									</h:inputText>
																								</TD>
																								<TD nowrap width="180px" >
																									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" 
																										 tabindex="17"
																										disabled="#{pc_Ghf00601T02.propPayNmFindType.disabled}"
																										id="htmlPayNmFindType"
																										value="#{pc_Ghf00601T02.propPayNmFindType.value}">
																										<f:selectItem itemValue="0" itemLabel="前方一致" />
																										<f:selectItem itemValue="1" itemLabel="部分一致" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="layoutTable">
																						<TBODY>
																							<TR>
																								<TD align="right">
																									<h:outputText styleClass="outputText"
																										id="payCount"
																										value="#{pc_Ghf00601T02.propPayList.listCount}">
																									</h:outputText>
																									<h:outputText
																										styleClass="outputText" id="payCountText" value="件">
																									</h:outputText>
																								</TD>
																							</TR>
																							<TR>
																								<TD>
																									<DIV style="height: 230px; width=100%;" id="paylistScroll" 
																										onscroll="setScrollPosition('scrollpay',this);"
																										class="listScroll"><h:dataTable
																										headerClass="headerClass" footerClass="footerClass"
																										styleClass="meisai_scroll" id="htmlPayList"
																										value="#{pc_Ghf00601T02.propPayList.list}" var="varlist"
																										width="831px" rows="#{pc_Ghf00601T02.propPayList.rows}"
																										rowClasses="#{pc_Ghf00601T02.propPayList.rowClasses}">
																										<%-- チェックボックス --%>
																										<h:column id="clmPayCheck">
																											<f:facet name="header">
																		
																											</f:facet>								
																											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
																												id="htmlPayCheck" value="#{varlist.payChecked}" 
																												rendered="#{varlist.rendered}" tabindex="20">
																											</h:selectBooleanCheckbox>
																											<f:attribute value="30px" name="width" />
																											<f:attribute value="text-align: center" name="style" />
																											<f:attribute value="center" name="align" />
																										</h:column>
																										<h:column id="clmWariateNendo">
																											<f:facet name="header">
																												<h:outputText styleClass="outputText" value="#{pc_Ghf00601T02.propWariateNendo.labelName}"
																													id="text1">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText styleClass="outputText" id="nendo"
																												value="#{varlist.nendo}"></h:outputText>
																											<f:attribute value="60px" name="width" />
																											<f:attribute value="text-align: center" name="style" />
																										</h:column>
																										<h:column id="clmPayCd">
																											<f:facet name="header">
																												<h:outputText styleClass="outputText" value="#{pc_Ghf00601T02.propPayCd.labelName}"
																													id="lblPayCd">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText styleClass="outputText" id="payCd"
																												value="#{varlist.payCd}"></h:outputText>
																											<f:attribute value="80px" name="width" />
																											<f:attribute value="text-align: left" name="style" />
																										</h:column>
																										<h:column id="clmPatternCd">
																											<f:facet name="header">
																												<h:outputText styleClass="outputText" value="#{pc_Ghf00601T02.propPatternCd.labelName}"
																													id="lblPatternCd">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText styleClass="outputText" id="patternCd"
																												value="#{varlist.patternCd}"></h:outputText>
																											<f:attribute value="100px" name="width" />
																											<f:attribute value="text-align: left" name="style" />
																										</h:column>
																										<h:column id="clmBunnoKbnCd">
																											<f:facet name="header">
																												<h:outputText styleClass="outputText" value="#{pc_Ghf00601T02.propBunnoKbnName.labelName}"
																													id="lblBunnoKbnName">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText styleClass="outputText" id="bunnoKbnName"
																												value="#{varlist.bunnoKbnCd}"></h:outputText>
																											<f:attribute value="100px" name="width" />
																											<f:attribute value="text-align: center" name="style" />
																										</h:column>
																										<h:column id="clmPayName">
																											<f:facet name="header">
																												<h:outputText styleClass="outputText" value="#{pc_Ghf00601T02.propPayName.labelName}" 
																													id="lblPayName">
																												</h:outputText>
																											</f:facet>								
																											<h:outputText styleClass="outputText" id="payName"
																												value="#{varlist.payName}"></h:outputText>
																											<f:attribute value="*" name="width" />
																											<f:attribute value="text-align: left" name="style" />
																										</h:column>
																										</h:dataTable>
																									</DIV>
																								</TD>
																							</TR>
																							<TR>
																								<TD>
																									<TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
																										<TBODY>
																											<TR>
																												<TD class="footerClass">
																													<TABLE class="panelBox">
																														<TBODY>
																															<TR>
																																<TD>
																																	<%-- 全選択・全解除 --%>
																																	<hx:jspPanel id="jspPanel2">
																																		<INPUT type="button" name="check" value="on"
																																			onclick="return func_pay_check_on(this, event);"
																																			class="check" tabindex="21">
																																		<INPUT type="button" name="uncheck" value="off"
																																			onclick="return func_pay_check_off(this, event);"
																																			class="uncheck" tabindex="22">
																																	</hx:jspPanel>
																																</TD>
																															</TR>
																														</TBODY>
																													</TABLE>
																												</TD>
																											</TR>
																										</TBODY>
																									</TABLE>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20px">
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH width="160px" class="v_a">
																									<h:outputText
																										styleClass="outputText" id="lblTargetDate"
																										value="#{pc_Ghf00601T02.propTargetDate.name}"
																										style="#{pc_Ghf00601T02.propTargetDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="110px" style="border-right-style: none;">
																									<h:inputText styleClass="inputText"
																										id="htmlTargetDate"
																										value="#{pc_Ghf00601T02.propTargetDate.dateValue}" size="10" tabindex="23">
																										<f:convertDateTime />
																										<hx:inputHelperDatePicker />
																										<hx:inputHelperAssist errorClass="inputText_Error"
																										promptCharacter="_" />
																									</h:inputText>
																								</TD>
																								<TD width="270px" style="border-left-style: none;">
																								</TD>
																								<TH rowspan="3" width="120px" class="v_b">
																									<h:outputText
																										styleClass="outputText" id="lblTarget"
																										value="#{pc_Ghf00601T02.propTarget.name}"
																										style="#{pc_Ghf00601T02.propTarget.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD rowspan="3" width="140px">
																									<h:selectOneRadio
																										disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" id="htmlTarget"
																										layout="pageDirection" value="#{pc_Ghf00601T02.propTarget.value}"
																										style="#{pc_Ghf00601T02.propTarget.style}" tabindex="24">
																										<f:selectItem itemValue="0" itemLabel="全て" />
																										<f:selectItem itemValue="1" itemLabel="未納" />
																										<f:selectItem itemValue="2" itemLabel="完納" />
																										<f:selectItem itemValue="3" itemLabel="超過金あり" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							<TR>
																								<TH rowspan="2" class="v_c" width="*">
																									<h:outputText
																										styleClass="outputText" id="lblPdfType"
																										value="#{pc_Ghf00601T02.propPdfType.name}"
																										style="#{pc_Ghf00601T02.propPdfType.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD colspan="2" width="*" style="border-bottom-style: none;">
																									<h:selectOneRadio
																										disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" id="htmlPdfType"
																										onclick="changePdfTitle(this);" layout="pageDirection"
																										value="#{pc_Ghf00601T02.propPdfType.value}"
																										style="#{pc_Ghf00601T02.propPdfType.style}" tabindex="25">
																										<f:selectItem itemValue="0" itemLabel="収納状況" />
																										<f:selectItem itemValue="1" itemLabel="収納履歴" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							<TR>
																								<TD width="*" style="text-align:right;border-right-style: none;border-top-style: none;">
																									<h:outputText
																										styleClass="outputText" id="lblWariateOut"
																										value="割当履歴出力：" style="#{pc_Ghf00601T02.propWariateOut.style};font-size: 9pt;">
																									</h:outputText>
																								</TD>
																								<TD width="*" style="text-align:left;border-left-style: none;border-top-style: none;">
																									<h:selectOneRadio
																										disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" id="htmlWariateOut"
																										layout="lineDirection" value="#{pc_Ghf00601T02.propWariateOut.value}"
																										style="#{pc_Ghf00601T02.propWariateOut.style}" tabindex="26">
																										<f:selectItem itemValue="0" itemLabel="出力しない" />
																										<f:selectItem itemValue="1" itemLabel="割当のみ出力する" />
																										<f:selectItem itemValue="2" itemLabel="全て出力する" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20px">
																				</TD>
																			</TR>
																			<TR>
																				<TD style="text-align:left;">
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH width="160px" nowrap align="center"><CENTER><h:outputText styleClass="outputText"
																									id="lblNo" value="優先順位">
																									</h:outputText></CENTER>
																								</TH>
																								<TH width="300px" nowrap align="center"><CENTER>
																									<h:outputText
																										styleClass="outputText" id="lblKomoku" value="項目">
																									</h:outputText></CENTER>
																								</TH>
																								<TH width="*" nowrap align="center"><CENTER>
																									<h:outputText styleClass="outputText"
																										id="lblSortOrder" value="ソート順">
																									</h:outputText></CENTER>
																								</TH>
																							</TR>
																							<TR>
																								<TH class="v_d" width="160px">
																									<h:outputText
																										styleClass="outputText" id="lblNo1" value="１">
																									</h:outputText>
																								</TH>
																								<TD>
																									<h:selectOneMenu styleClass="selectOneMenu"
																										id="htmlComboOrder1" value="#{pc_Ghf00601T02.propComboOrder1.value}" 
																										tabindex="27">
																										<f:selectItems value="#{pc_Ghf00601T02.propComboOrder1.list}" />
																									</h:selectOneMenu>
																								</TD>
																								<TD>
																									<h:selectOneRadio
																										disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" id="htmlRadioOrderType1" value="#{pc_Ghf00601T02.propRadioOrderType1.value}" style="#{pc_Ghf00601T02.propRadioOrderType1.style}" tabindex="28">
																										<f:selectItem itemValue="0" itemLabel="昇順" />
																										<f:selectItem itemValue="1" itemLabel="降順" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_e">
																									<h:outputText
																										styleClass="outputText" id="lblNo2" value="２">
																									</h:outputText>
																								</TH>
																								<TD>
																									<h:selectOneMenu styleClass="selectOneMenu"
																										id="htmlComboOrder2" value="#{pc_Ghf00601T02.propComboOrder2.value}" 
																										style="#{pc_Ghf00601T02.propComboOrder2.style}" tabindex="29">
																										<f:selectItems value="#{pc_Ghf00601T02.propComboOrder2.list}" />
																									</h:selectOneMenu>
																								</TD>
																								<TD>
																									<h:selectOneRadio
																										disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" id="htmlRadioOrderType2" 
																										value="#{pc_Ghf00601T02.propRadioOrderType2.value}" 
																										style="#{pc_Ghf00601T02.propRadioOrderType2.style}" tabindex="30">
																										<f:selectItem itemValue="0" itemLabel="昇順" />
																										<f:selectItem itemValue="1" itemLabel="降順" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_f">
																									<h:outputText
																										styleClass="outputText" id="lblNo3" value="３">
																									</h:outputText>
																								</TH>
																								<TD>
																									<h:selectOneMenu styleClass="selectOneMenu"
																										id="htmlComboOrder3" value="#{pc_Ghf00601T02.propComboOrder3.value}" 
																										style="#{pc_Ghf00601T02.propComboOrder3.style}" tabindex="31">
																										<f:selectItems value="#{pc_Ghf00601T02.propComboOrder3.list}" />
																									</h:selectOneMenu>
																								</TD>
																								<TD>
																									<h:selectOneRadio
																										disabledClass="selectOneRadio_Disabled"
																										styleClass="selectOneRadio" id="htmlRadioOrderType3" value="#{pc_Ghf00601T02.propRadioOrderType3.value}" style="#{pc_Ghf00601T02.propRadioOrderType3.style}" tabindex="32">
																										<f:selectItem itemValue="0" itemLabel="昇順" />
																										<f:selectItem itemValue="1" itemLabel="降順" />
																									</h:selectOneRadio>
																								</TD>
																							</TR>
																							<TR>
																								<TH width="150" class="v_g" align="center">
																									<h:outputText
																										styleClass="outputText" id="lblKaiPage"
																										value="#{pc_Ghf00601T02.propKaiPage.name}"
																										style="#{pc_Ghf00601T02.propKaiPage.style}">
																									</h:outputText>
																								</TH>
																								<TD width="*" colspan="2">
																									<h:selectOneMenu
																										styleClass="selectOneMenu" id="htmlKaiPage" 
																										value="#{pc_Ghf00601T02.propKaiPage.value}" tabindex="33">
																										<f:selectItems value="#{pc_Ghf00601T02.propKaiPage.list}" />
																									</h:selectOneMenu>
																									<h:outputText
																										styleClass="note" id="htmlNotes" value="改頁指定は収納状況選択時のみ有効です。" 
																										style="margin-left: 20px">
																									</h:outputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20px">
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH width="160px" class="v_a">
																									<h:outputText
																										styleClass="outputText" id="lblSum"
																										value="#{pc_Ghf00601T02.propSum.name}"
																										style="#{pc_Ghf00601T02.propSum.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																								  <h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
																									value="#{pc_Ghf00601T02.propSum.checked}" id="htmlSum" tabindex="34">
																								  </h:selectBooleanCheckbox>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_b" width="160px">
																									<h:outputText
																										styleClass="outputText" id="lblPdfTitle"
																										style="#{pc_Ghf00601T02.propPdfTitle.labelStyle}"
																										value="#{pc_Ghf00601T02.propPdfTitle.labelName}">
																									</h:outputText>
																								</TH>
																								<TD width="*">
																									<h:inputText styleClass="inputText" id="htmlPdfTitle"
																										value="#{pc_Ghf00601T02.propPdfTitle.stringValue}"
																										style="#{pc_Ghf00601T02.propPdfTitle.style}"
																										maxlength="#{pc_Ghf00601T02.propPdfTitle.maxLength}"
																										size="100" tabindex="35">
																									</h:inputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																			<TR>
																				<TD height="20px">
																				</TD>
																			</TR>
																		</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar">
															<TBODY>
																<TR>
																	<TD>
																		<hx:commandExButton type="submit" value="PDF作成"
																			styleClass="commandExButton_out" id="pdfout"
																			confirm="#{msg.SY_MSG_0019W}"
																			disabled="#{!pc_Ghf00601T02.propGhYearFr.disabled}" 
																			action="#{pc_Ghf00601T02.doPdfoutAction}" tabindex="36">
																		</hx:commandExButton>&nbsp;
																		<hx:commandExButton type="submit" value="EXCEL作成"
																			styleClass="commandExButton_out" id="excelout"
																			confirm="#{msg.SY_MSG_0027W}"
																			disabled="#{!pc_Ghf00601T02.propGhYearFr.disabled}" 
																			action="#{pc_Ghf00601T02.doExceloutAction}" tabindex="37">
																		</hx:commandExButton>&nbsp;
																		<hx:commandExButton type="submit" value="CSV作成"
																			styleClass="commandExButton_out" id="csvout"
																			confirm="#{msg.SY_MSG_0020W}"
																			disabled="#{!pc_Ghf00601T02.propGhYearFr.disabled}" 
																			action="#{pc_Ghf00601T02.doCsvoutAction}" tabindex="38">
																		</hx:commandExButton>&nbsp;
																		<hx:commandExButton type="submit" value="出力項目指定"
																			styleClass="commandExButton_out" id="setoutput"
																			disabled="#{!pc_Ghf00601T02.propGhYearFr.disabled}" 
																			action="#{pc_Ghf00601T02.doSetoutputAction}" tabindex="39">
																		</hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			</DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Ghf00601T02.propButton.integerValue}" id="htmlButton">
			<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Ghf00601T02.propBeforeValue.stringValue}" id="htmlBeforeValue">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Ghf00601T02.propPdfId01.stringValue}" id="htmlPdfId01">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Ghf00601T02.propPdfId02.stringValue}" id="htmlPdfId02">
			</h:inputHidden>

			<h:inputHidden
				value="#{pc_Ghf00601T02.propPayList.scrollPosition}"
				id="scrollpay"></h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghf00601T02.propScrollPos.stringValue}" id="htmlScrollPos">
			</h:inputHidden>
			
			<h:inputHidden value="#{pc_Ghf00601T02.propPayListSelect.integerValue}" id="htmlPayListSelect"><f:convertNumber /></h:inputHidden>
			<h:inputHidden value="#{pc_Ghf00601T02.propActionType.stringValue}" id="htmlActionType"></h:inputHidden>
			
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
