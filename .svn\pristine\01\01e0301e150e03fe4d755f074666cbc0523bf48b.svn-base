﻿@charset "UTF-8";

* {
margin: 0;
padding: 0;
font-style: normal;
font-weight: normal;
word-break:break-all;
}

select {
font-family: 'ＭＳ ゴシック';
}

.outputText{
font-size: 10pt;
}

.outputText_label{
font-size: 10pt;
padding-left:8px;
background-position: left center;
background-repeat:no-repeat;
}

.outputFormat{
font-size: 10pt;
}

.inputText {
padding-left:2px;
letter-spacing: normal;
word-spacing: normal;
}

.inputSecret {
padding-left:2px;
letter-spacing: normal;
word-spacing: normal;
}

.likeOutput {
height: 14px;
letter-spacing: normal;
line-height: 14px;
vertical-align: middle;
word-spacing: normal;
border: 0px solid #888888;
}

.commandExButton {
height: 20px;
border-left-width: 1px;
border-top-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
cursor:pointer;
background-color: #DDEAFF;
color: #000000;
text-align:center;
border-bottom-color: #AAAAAA;
border-right-color: #AAAAAA;
border-bottom-style: solid;
border-right-style: solid;
margin:0px 0px 0px 0px;
font-size:12px
}

.pagerGoto input{
height: 20px;
border-left-width: 1px;
border-top-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
cursor:pointer;
background-color: #DDEAFF;
color: #000000;
text-align:center;
border-bottom-color: #AAAAAA;
border-right-color: #AAAAAA;
border-bottom-style: solid;
border-right-style: solid;
margin:0px 0px 0px 0px;
font-size:12px
}

.commandExButton_s {
height: 20px;
border-right-width: 2px;
border-bottom-width: 2px;
cursor:pointer;
background-color: #DDEAFF;
color: #000000;
text-align:center;
border-bottom-color: #AAAAAA;
border-right-color: #AAAAAA;
border-bottom-style: solid;
border-right-style: solid;
margin:0px 0px 0px 0px;
}

.commandExButton_dat {
font-size: 10px;
height: 22px;
width: 80px;
border-left-width: 0px;
border-right-width: 0px;
border-bottom-width: 0px;
border-top-width: 0px;
cursor:pointer;
text-align:center;
background-image: url(../image/ja/button_bg.gif);
background-repeat:no-repeat;
background-color: #F6F6F6;
}

.tab_body .commandExButton_dat {
background-color: #e6e6e8;
}

.tabbedPanel .commandExButton_dat {
background-color: #e6e6e8;
}

.cmdBtn_dat_s {
height: 20px;
border-left-width: 1px;
border-top-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
cursor:pointer;
background-color: #DDEAFF;
color: #000000;
text-align:center;
border-bottom-color: #AAAAAA;
border-right-color: #AAAAAA;
border-bottom-style: solid;
border-right-style: solid;
margin:0px 0px 0px 0px;
font-size:12px
}

.cmdBtn_out_s {
height: 20px;
border-right-width: 2px;
border-bottom-width: 2px;
cursor:pointer;
background-color: #DDEAFF;
color: #000000;
text-align:center;
border-bottom-color: #AAAAAA;
border-right-color: #AAAAAA;
border-bottom-style: solid;
border-right-style: solid;
margin:0px 0px 0px 0px;

}

.cmdBtn_etc_s {
height: 20px;
border-left-width: 1px;
border-top-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
cursor:pointer;
background-color: #DDEAFF;
color: #000000;
text-align:center;
border-bottom-color: #AAAAAA;
border-right-color: #AAAAAA;
border-bottom-style: solid;
border-right-style: solid;
margin:0px 0px 0px 0px;
font-size:12px
}

body {
 background-color: #F7F7F7;
 background-repeat: repeat;
 margin: 0px;
}

.bottom_space {
 margin-bottom: 20px;
} 

h1 {
float:left;
padding: 0 0 0 20px;
margin:0px;
font-size: 10pt;
line-height:28px;
font-weight: bold;
color: #FFFFFF;
}

h2 {
float:right;
font-size: 8pt;
line-height:28px;
color: #FFFFFF;
}

h2 a {
color: #FFFFFF;
}

h3 {
width:942px;
height:20px;
background-color: #1B508C;
background-image: url(../image/ja/dropfolio_bg.gif);
margin:0 auto 0 auto;
font-size: 8pt;
line-height:20px;
color: #FFFFFF;
text-indent:10px;
}

.outer {
width:987px;
padding: 0px;
margin:0 auto 0 auto;
background-color: #F7F7F7;
}

/* ::::::::::: head 1  ::::::::::: */
.head1 {
width:987px;
height:18px;
background-color: #F2F2F2;
margin:0 auto 0px auto;
padding: 1px 0 1px 0;
}

/* IE7向け定義 */
*:first-child+html .head1 {
min-height:18px;
height:auto;
}


.head1 p {
font-size: 8pt;
line-height:20px;
color: #333333;
text-indent:10px;
}

.head1 .function_name {
width:270px; height:20px;
float:left;
margin:0 0 0 2px;
background-color: #FFFFFF;
border-top: 1px solid #CCCCCC;
border-right: 1px dotted #CCCCCC;
border-bottom: 1px solid #CCCCCC;
border-left: 1px solid #CCCCCC;
overflow:hidden;
text-overflow:ellipsis;
}

.head1 .function_name p {
font-weight: bold;
color: #000080;
overflow:hidden;
white-space:nowrap;
}

#funcName {
font-weight: bold;
}

.head1 .function_id {
width:80px; height:20px;
float:left;
margin:0;
background-color: #FFFFFF;
border-top: 1px solid #CCCCCC;
border-right: 1px solid #CCCCCC;
border-bottom: 1px solid #CCCCCC;
border-left: 0px;
overflow:hidden;
white-space:nowrap;
text-overflow:ellipsis;
}

#htmlFuncId {
color:#000080;
}

.head1 .user_id {
float:left;
width:140px; height:20px;
margin:0 0 0 10px;
background-color: #FFFFFF;
border-top: 1px solid #CCCCCC;
border-right: 1px dotted #CCCCCC;
border-bottom: 1px solid #CCCCCC;
border-left: 1px solid #CCCCCC;
overflow:hidden;
white-space:nowrap;
text-overflow:ellipsis;
}

#htmlLoginId{
color:#000080;
}

.head1 .user_name {

width:270px; height:20px;
float:left;
margin:0px;
background-color: #FFFFFF;
border-top: 1px solid #CCCCCC;
border-right: 1px solid #CCCCCC;
border-bottom: 1px solid #CCCCCC;
border-left: 0px;
overflow:hidden;
white-space:nowrap;
text-overflow:ellipsis;
}

#userName{
color:#000080;
}

.navigation{
text-align:right;
width:620px; 
overflow:hidden;
white-space:nowrap;
text-overflow:ellipsis;
}

.head1 .menu {

float:right;
display:block;
width:118px; height:18px;
margin: 0 10px 0 0 ;
}

#help{
margin-right:5px;
}

.otherErr {
z-index: 99;
font-size: 8pt;
border-width:2px;
border-style:solid;
border-color:#0043B3;
background-color: #F7F7F7;
PADDING-RIGHT: 5px;
DISPLAY: none;
PADDING-LEFT: 5px;
PADDING-BOTTOM: 5px;
OVERFLOW: hidden;
PADDING-TOP: 5px;
POSITION: absolute;
white-space:nowrap;
}


/* ::::::::::: head 2  ::::::::::: */
.head2 {
width:100%;
height:28px;
background-image: url(../image/ja/header_bg.gif);
margin:0px auto 0px auto;
padding-top:0px;
}

.head2 #navi {
float:right;
padding: 0 10px 0 0 ;
font-size: 8pt;
line-height:28px;
color: #FFFFFF;
}

#content {
width:942px;
margin:0 auto 5px auto;
}

#content .column {
width:938px;
height:547px;
margin:0px auto 0px auto;
padding: 1px 0px 5px 0px;
padding-top: 8px;
text-align: center;
background-color: #F7F7F7;
}

/* IE7向け定義 */
*:first-child+html #content .column {
min-height:545px;
height:auto;
}

#content .column .table {
border-collapse :collapse;
font-size: 8pt;
color: #333333;
border-width: 1px;
}

#content .column .table .group_label_top  {
border-left-style:none;
border-left-color:#CCCCCC;
border-right-style: none;
border-bottom-style: none;
background-color: #82A3C4;
color:#FFFFFF;
}

#content .column .table .group_label {
border-left-style:none;
border-left-color:#CCCCCC;
border-right-style: none;
border-bottom-style: none;
border-top-style: none;
background-color: #82A3C4;
color:#FFFFFF;
}

#content .column .table .group_label_bottom  {
border-left-width:4px;
border-left-style:none;
border-left-color:#CCCCCC;
border-top-style: none;
border-right-style: none;
background-color: #82A3C4;
color:#FFFFFF;
}

#content .column .table .group_item {
border-left-style: none;
background-color: #82A3C4;
color:#FFFFFF;
}

#content .column .table .clear_border {
border-right-style: none;
border-bottom-style: none;
border-top-style:none;
border-left-style:none;
padding:0px;
margin:0px;
border-collapse :collapse;
}

#content .column .table .clear_border th{
border-right-style: none;
border-bottom-style: none;
border-top-style:none;
border-left-style:none;
padding:0px;
margin:0px;
}

#content .column .table .clear_border td{
border-right-style: none;
border-bottom-style: none;
border-top-style:none;
border-left-style:none;
padding:0px;
margin:0px;
}

#content .column .table .clear_border tr{
border-right-style: none;
border-bottom-style: none;
border-top-style:none;
border-left-style:none;
padding:0px;
margin:0px;
}

#content .column .table th {
background-color:#82A3C4;
border-bottom: 1px solid #CCCCCC;
border-right: 1px solid #CCCCCC;
border-top: 1px solid #CCCCCC;
padding-left:2px;
text-align:left;
color: #FFFFFF;
height:20px;
}

#content .column .table th span {
padding-left:8px;
background-position: left center;
background-repeat:no-repeat;
}


#content .column .table td {
text-align:left;
vertical-align:middle;
border: 1px solid #CCCCCC;
background-color:#FFFFFF;
padding: 0px;
empty-cells: hide;
height:20px;
padding-left:2px;
}

#content .column .table td .outputText {
padding-left:2px;
background-position: left center;
background-repeat:no-repeat;
}

#content .column .selectManyCheckbox TD{
border-collapse :collapse;
border-right: #CCCCCC 0px solid;
padding-right: 0px;
border-top: #CCCCCC 0px solid;
padding-left: 0px;
padding-bottom: 0px;
border-left: #CCCCCC 0px solid;
padding-top: 0px;
border-bottom: #CCCCCC 0px solid;
background-color: #FFFFFF;
text-align: left;
border-style:none;
}

#content .column .selectManyCheckbox{
border-style:none;
border-collapse :collapse;
}

#content .column .selectOneRadio td{
border-collapse :collapse;
border-right: #CCCCCC 0px solid;
padding-right: 0px;
border-top: #CCCCCC 0px solid;
padding-left: 0px;
padding-bottom: 0px;
border-left: #CCCCCC 0px solid;
padding-top: 0px;
border-bottom: #CCCCCC 0px solid;
background-color: #FFFFFF;
text-align: left;
border-style:none;
}

#content .column .selectOneRadio {
border-style:none;
border-collapse :collapse;
}

#content .column .v_a {
border-left: 1px solid #CCCCCC;
}

#content .column .v_b {
border-left: 1px solid #CCCCCC;
}

#content .column .v_c {
border-left: 1px solid #CCCCCC;
}

#content .column .v_d {
border-left: 1px solid #CCCCCC;
}

#content .column .v_e {
border-left: 1px solid #CCCCCC;
}

#content .column .v_f {
border-left: 1px solid #CCCCCC;
}

#content .column .v_g {
border-left: 1px solid #CCCCCC;
}

#content .column .h_a {
border-left: 1px solid #CCCCCC;
}

#content .column .h_b {
border-left: 1px solid #CCCCCC;
}

#content .column .h_c {
border-left: 1px solid #CCCCCC;
}

#content .column .h_d {
border-left: 1px solid #CCCCCC;
}

#content .column .h_e {
border-left: 1px solid #CCCCCC;
}

#content .column .h_f {
border-left: 1px solid #CCCCCC;
}

#content .column .h_g {
border-left: 1px solid #CCCCCC;
}

#content .tab {
margin: 0 auto 3px auto;
width:580px; height:30px;
text-align:center;
border-top: none;
border-right: none;
border-bottom: 4px solid #BA4848;
border-left: none;
}

#content .column .tab_a_on {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#FFFFFF;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BA4848;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_a_off {
width:120px; 
height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#E8E8E8;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BA4848;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_b_on {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#FFFFFF;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BA7848;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_b_off {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#E8E8E8;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BA7848;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_c_on {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#FFFFFF;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BABA48;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_c_off {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#E8E8E8;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BABA48;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_d_on {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#FFFFFF;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #48BA48;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_d_off {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#E8E8E8;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #48BA48;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_e_on {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#FFFFFF;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #8148BA;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_e_off {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#E8E8E8;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #8148BA;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_f_on {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#FFFFFF;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BA4881;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_f_off {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#E8E8E8;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BA4881;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_g_on {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#FFFFFF;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BABABA;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .column .tab_g_off {
width:120px; height:26px;
float:left;
margin: 0 3px 0 0;
background-color:#E8E8E8;
border-top: 4px solid;
border-right: 1px solid;
border-bottom: none;
border-left: 1px solid;
border-color: #BABABA;
text-align:center;
font-size: 8pt;
color: #333333;
line-height:30px;
}

#content .base {
margin: 0 auto 3px auto;
width:578px; 
border: 1px solid #CCCCCC;
background-color:#FFFFFF;
padding: 20px 0 20px 0;
}

.button_bar {
height:35px;
margin: 0 0 0px 0;
border-collapse: collapse;
}

.button_bar TR {
text-align:center;
}

.noboder {
border:0px;
}

.button_bg {
background-image: url(../image/ja/button_bg.gif);
padding: 1px;
border: 1px solid #CCCCCC;
}


.button_text  {
font-size: 8pt;
line-height:16px;
color: #333333;
}

.foot{
width:100%;
background-image: url(../image/ja/footer_bg.gif);
margin:0px auto 0 auto;
}

.foot .logo{
float:left;
width:106px;
border: 0;
}

.foot p{
float:center;
margin: 3px 20px 0px 0px;
font-size: 8pt;
color: #cecfce;
text-align:right;
}

/* #################### 明細テーブル用スタイル(ページング) ################# */
#content .column .meisai_page .selectiveLine{
background-color: #8EEBFF;
}

#content .column .meisai_page .evenNumberLine{
background-color: #EFF2F4; 
/* background-color: white;*/
}

#content .column .meisai_page .oddNumberLine{
background-color: #FCFCFC;
/*background-color: white;*/
}

#content .column .meisai_page th {
background-color:#82A3C4;
border-color:#CCCCCC;
/* IE8対応 2009-11-27 -> */
border-style:solid;
/* IE8対応 2009-11-27 <- */

line-height:150%;
vertical-align:bottom;
border-top:9px;
border:1px;
text-indent: 5px;
text-align:center;
color:#FFFFFF;
}

#content .column .meisai_page {
border-collapse: collapse;
font-size: 8pt;
color: #333333;
background-color:white;
}

#content .column .meisai_page td {
height:18px;
text-align:left;
border: 1px solid #CCCCCC;
padding: 0px;
text-indent:3px;
}

#content .column .meisai_page .selectManyCheckbox TD{
PADDING-RIGHT: 0px;
PADDING-LEFT: 0px;
PADDING-BOTTOM: 0px;
PADDING-TOP: 0px;
TEXT-ALIGN: left;
border-style:none;
border-collapse :collapse;
background-color:transparent ;
}

#content .column .meisai_page .selectOneRadio td{
PADDING-RIGHT: 0px;
PADDING-LEFT: 0px;
PADDING-BOTTOM: 0px;
PADDING-TOP: 0px;
TEXT-ALIGN: left;
border-style:none;
border-collapse :collapse;
background-color:transparent ;

}

/* #################### 明細テーブル用スタイル ################# */
#content .column .meisai_scroll .selectiveLine{
background-color: #8EEBFF;
}

#content .column .meisai_scroll .evenNumberLine{
background-color: #EFF2F4;
/* background-color:white;*/
}

#content .column .meisai_scroll .oddNumberLine{
background-color: #FCFCFC;
/*background-color:white;*/
}

#content .column .meisai_scroll th {
background-color:#82A3C4;
border-color:#CCCCCC;
line-height:150%;
text-indent: 4px;
border-collapse: collapse;
color:#FFFFFF;
border-style:solid;
border-right-style:none;
border-width:1px;
height:20px;
}

#content .column .meisai_scroll .headerClass {
position:relative;
top:-1px;
}

#content .column .meisai_scroll {
border-collapse: collapse;
font-size: 8pt;
color: #333333;
}

table.table_head {

background-color:#82A3C4;
border: solid 1px #CCCCCC;
border-collapse: collapse;
font-size: 8pt;
overflow-y:visible;
}

#content .column .table_head th {
background-color:#82A3C4;
border-color:#CCCCCC;
line-height:150%;
vertical-align:bottom;
border-width:1px;
text-indent: 4px;
border-collapse: collapse;
text-align: center;
background-repeat:no-repeat;
overflow-y:visible;
}

#content .column .table_head td {
background-color:#82A3C4;
border-color:#CCCCCC;
line-height:150%;
vertical-align:bottom;
border-width:1px;
text-indent: 4px;
border-collapse: collapse;
text-align: center;
overflow-y:visible;
}

.table_footer{
background-color: #A4B2BF;
color: WindowText;
border-width: 1px;
border-color:#CCCCCC;
border-style:solid;
border-top-style:none;
padding:0px;
padding-left:4pt;
padding-right:4pt;
font-weight: 400;
height:33px;
border-collapse :collapse;
}

#content .column .meisai_scroll td {
height:18px;
text-align:left;
border: 1px solid #CCCCCC;
padding: 0px;
text-indent:3px;

}

#content .column .meisai_scroll .selectManyCheckbox TD{
padding-right: 0px;
padding-left: 0px;
padding-bottom: 0px;
padding-top: 0px;
text-align: left;
border-style:none;
border-collapse :collapse;
background-color:transparent ;

}


#content .column .meisai_scroll .selectOneRadio td{
padding-right: 0px;
padding-left: 0px;
padding-bottom: 0px;
padding-top: 0px;
text-align: left;
border-style:none;
border-collapse :collapse;
background-color:transparent ;

}

.listScroll{
border-collapse: collapse;
overflow-y:scroll;
border-style:solid;
border-width: 1px;
border-color: #CCCCCC;
background-color:white;
}



.commandExButton_search{
line-height:1000px;
height: 17px;
width: 17px;
border-left-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/searchBtn_bg.gif);
background-repeat:no-repeat;
}

.commandExButton_large1 {
font-size: 10px;
height: 22px;
border-left-width: 0px;
border-right-width: 0px;
border-bottom-width: 0px;
border-top-width: 0px;
cursor:pointer;
text-align:center;
background-image: url(../image/ja/button_bg_large1.gif);
background-repeat:no-repeat;
background-color: #F6F6F6;
}

.tab_body .commandExButton_large1 {
background-color: #e6e6e8;
}

.commandExButton_large2 {
font-size: 10px;
height: 22px;
width: 118px;
border-left-width: 0px;
border-right-width: 0px;
border-bottom-width: 0px;
border-top-width: 0px;
cursor:pointer;
text-align:center;
background-image: url(../image/ja/button_bg_large2.gif);
background-repeat:no-repeat;
background-color: #F6F6F6;
}

.tab_body .commandExButton_large2 {
background-color: #e6e6e8;
}
.commandExButton_large3 {
font-size: 10px;
height: 22px;
width: 132px;
border-left-width: 0px;
border-right-width: 0px;
border-bottom-width: 0px;
border-top-width: 0px;
cursor:pointer;
text-align:center;
background-image: url(../image/ja/button_bg_large3.gif);
background-repeat:no-repeat;
background-color: #F6F6F6;
}

.tab_body .commandExButton_large3 {
background-color: #e6e6e8;
}
.commandExButton_large4 {
font-size: 10px;
height: 22px;
width: 146px;
border-left-width: 0px;
border-right-width: 0px;
border-bottom-width: 0px;
border-top-width: 0px;
cursor:pointer;
text-align:center;
background-image: url(../image/ja/button_bg_large4.gif);
background-repeat:no-repeat;
background-color: #F6F6F6;
}

.tab_body .commandExButton_large4 {
background-color: #e6e6e8;
}
.check{
height: 17px;
width: 17px;
border-left-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/button_selectall.gif);
background-repeat:no-repeat;
margin:0px 1px 0px 1px;
line-height:1000px;
}

.uncheck{
height: 17px;
width: 17px;
border-left-width: 1px;
border-right-width: 1px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/button_removeall.gif);
background-repeat:no-repeat;
margin:0px 1px 0px 1px;
line-height:1000px;
}

.errButton{
height: 19px;
width: 19px;
cursor:pointer;
text-align:center;
border-bottom-color: #004d99;
border-top-color: #C0C0FF; 
border-left-color: #C0C0FF;
border-right-color: #004d99;
border-style:none;
background-image: url(../image/ja/errorBtn_bg.gif);
background-repeat:no-repeat;
margin:0px 1px 0px 1px;
}

.head_button_area {
vertical-align:middle;
text-align:right;
margin-right: 30px;
margin-top:3px;
border-width: 1px;
border-color: black;
}

.head_button_area .commandExButton{
margin-left: 10px;
width: 80px
}

.fieldset {
font-size: 8pt;
width:30px;
padding-left: 6px;
padding-right: 6px;
padding-top: 6px;
padding-bottom: 6px;
}

.fieldset LEGEND{
color: red;
font-size: 8pt;
}

.fieldset_err {
border: 1px solid red;
height:28px;
padding-bottom: 3px;
padding-left: 4px;
padding-right: 4px;
padding-top: 0px;
margin-left: 20px;
margin-bottom: 2px;
color:red;
float: left;
}

/* IE7向け定義 */
*:first-child+html .fieldset_err {
min-height:28px;
height:auto;
}


.fieldset_err LEGEND{
color: red;
font-size:7px;
margin: 0px;
}

#content .tab_head_on {
/*
height: 25px;
width: 50px;
color: navy;
background-color: #FFFFFF;
font-size: 8pt;
text-align:center;
border: none
*/
}

#content .tab_head_off {
/*
height: 25px;
width: 50px;
color: #999999;
font-size: 8pt;
text-align:center;
border: 1px solid #CCCCCC;
background-color: #EEEEEE;
*/
}

#content .tab_body {
vertical-align: top;
background-color: #e6e6e8;
border-style:solid;
border-collapse :collapse;
border-width:1px;
border-color:#9d9d9d;
}

#content input.tab_head_off {
border-collapse :collapse;
background-color: #e6e6e8;
background-color: #FFFFCC;
border-bottom-color: #9d9d9d;
border-bottom-style: none;
border-bottom-width: 1px;
border-left-color: #9d9d9d;
border-left-style: solid;
border-left-width: 1px;
border-right-color: #9d9d9d;
border-right-style: solid;
border-right-width: 1px;
border-top-color: #9d9d9d;
border-top-style: solid;
border-top-width: 1px;
border-width: 1px;
color: navy;
height: 25px;
margin-bottom: 0px;
margin-left: 0px;
margin-right: 0px;
margin-top: 0px;
padding-bottom: 0px;
padding-left: 0px;
padding-right: 0px;
padding-top: 0px;
text-align: center;
width:auto;
height: 25px;
}

#content input.tab_head_on {
border-collapse :collapse;
background-color: #e6e6e8;
border-bottom-color: #9d9d9d;
border-bottom-style: none;
border-bottom-width: 1px;
border-left-color: #9d9d9d;
border-left-style: solid;
border-left-width: 1px;
border-right-color: #9d9d9d;
border-right-style: solid;
border-right-width: 1px;
border-top-color: #9d9d9d;
border-top-style: solid;
border-top-width: 1px;
border-width: 1px;
height: 25px;
margin-bottom: 0px;
margin-left: 0px;
margin-right: 0px;
margin-top: 0px;
padding-bottom: 0px;
padding-left: 0px;
padding-right: 0px;
padding-top: 0px;
width:auto;
}

hr {
    border: 0 none; 
    height: 1px; 
    color: #CCCCCC;
    background-color: #CCCCCC;
    margin:0px;
    padding:0px;
}

.inputText_dis {
padding-left:2px;
letter-spacing: normal;
word-spacing: normal;
color: #808080;
background-color:#F2EEE6;
}

.commandExButton_dat_dis_s {
height: 17px;
border-left-width: 3px;
border-right-width: 3px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
background-color: #e3e3e3;
text-align:center;
border-bottom-color: #a8aaaa;
border-top-color: #a8aaaa; 
border-left-color: #ba4848;
border-right-color: #ba4848;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/button_bg.gif);
margin:0px 1px 0px 1px;
color: #ACA899;
}

.commandExButton_out_dis_s {
height: 17px;
border-left-width: 3px;
border-right-width: 3px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
background-color: #e3e3e3;
text-align:center;
border-bottom-color: #a8aaaa;
border-top-color: #a8aaaa; 
border-left-color: #ba7848;
border-right-color: #ba7848;;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/button_bg.gif);
margin:0px 1px 0px 1px;
color: #ACA899;
}

.commandExButton_etc_dis_s {
height: 17px;
border-left-width: 3px;
border-right-width: 3px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
background-color: #e3e3e3;
text-align:center;
border-bottom-color: #a8aaaa;
border-top-color: #a8aaaa; 
border-left-color: #baba48;
border-right-color: #baba48;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/button_bg.gif);
margin:0px 1px 0px 1px;
color: #ACA899;
}

.commandExButton_dat_dis {
height: 17px;
width: 80px;
border-left-width: 3px;
border-right-width: 3px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
background-color: #e3e3e3;
text-align:center;
border-bottom-color: #a8aaaa;
border-top-color: #a8aaaa; 
border-left-color: #ba4848;
border-right-color: #ba4848;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/button_bg.gif);
margin:0px 1px 0px 1px;
color: #ACA899;
}

.commandExButton_out_dis {
height: 17px;
width: 80px;
border-left-width: 3px;
border-right-width: 3px;
border-bottom-width: 1px;
border-top-width: 1px;
cursor:pointer;
background-color: #e3e3e3;
text-align:center;
border-bottom-color: #a8aaaa;
border-top-color: #a8aaaa; 
border-left-color: #ba7848;
border-right-color: #ba7848;;
border-bottom-style: solid;
border-left-style: solid;
border-top-style: solid;
border-right-style: solid;
background-image: url(../image/ja/button_bg.gif);
margin:0px 1px 0px 1px;
color: #ACA899;
}

.commandExButton_etc {
font-size: 10px;
height: 22px;
width: 80px;
border-left-width: 0px;
border-right-width: 0px;
border-bottom-width: 0px;
border-top-width: 0px;
cursor:pointer;
text-align:center;
background-image: url(../image/ja/button_bg.gif);
background-repeat:no-repeat;
background-color: #F6F6F6;
}

.tab_body .commandExButton_etc {
  background-color: #e6e6e8;
}

.commandExButton .commandExButton_etc {
  background-color: #e6e6e8;
}

.commandExButton_out {
font-size: 10px;
height: 22px;
width: 80px;
border-left-width: 0px;
border-right-width: 0px;
border-bottom-width: 0px;
border-top-width: 0px;
cursor:pointer;
text-align:center;
background-image: url(../image/ja/button_bg.gif);
background-repeat:no-repeat;
background-color: #F6F6F6;
}

.tab_body .commandExButton_out {
  background-color: #e6e6e8;
}

.tabbedPanel .commandExButton_out {
  background-color: #e6e6e8;
}

.note {
font-size: 10pt;
color: #000088;
}

.list_table {
}

.list_table th {
font-size: 12px;
height: 19px;
vertical-align: bottom;
text-align: left;
border: 1px solid #AAAAAA;
background-color: #EEEEEE;
}

.list_table td {
margin:0px 0px 0px 1px;
font-size: 11px;
vertical-align: middle;
text-align: center;
empty-cells: hide;
border: 1px solid #AAAAAA;
}

/* ------------------------*/
/* 住所検索用スタイル　　　 */
/* ------------------------*/
#livesearch {
margin: 0px 25px 15px 25px;
padding:0px;
width: 140px;
display: block;
border:1px solid #99b58d;
}

#LSResult {    
position: absolute;
background-color: #aaa; 
min-width: 180px; 
margin: 1px 0px 2px 0px;
padding: 0px;
}

#LSResult li {
font-size: 12px;
padding-bottom: 2px;
padding-top: 2px;
line-height:15px;
margin-bottom: 0px;
margin-left:4px;
}
      
ul#LSShadow {
position: relative;
right: 1px;
margin: 0px;
padding: 0px;
background-color: #666; /*shadow color*/
color: inherit;
}

#LSResult ul {
margin-bottom: -5px;
margin-top: 0px;
padding-top: 0px;  
margin: 0px;
padding: 0px;
}

#LSResult ul a {
color: #222;
background-color:transparent;
}

#LSResult ul a:hover {
color: #564b47;  
background-color: #ccc;
}

#LSResult ul a:active { 
color: #564b47;  
background-color: #ccc;
}

#LSResult ul li {
text-indent: -20px;
padding: 0px 15px 3px 20px;
}
 
.LSRes {
position: relative;
bottom: 1px;
right: 1px;
background-color: white;
border:  1px solid #AAA;
}
  
#LSHighlight {
color: #564b47;  
background-color: #ccc;
}

.selectOneMenu { 
height:20px;
} 

.selectManyListbox {
font-size: 11pt;
}

