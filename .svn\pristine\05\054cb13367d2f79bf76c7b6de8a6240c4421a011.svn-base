<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab00809T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>リース契約詳細(管理情報)</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="inc/gakuenKA.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kab00809T02.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">

	<!-- ヘッダーインクルード -->
	<jsp:include page="../inc/header.jsp" />

	<!-- ヘッダーへのデータセット領域 -->
	<div style="display:none;"><hx:commandExButton type="submit"
		value="閉じる" styleClass="commandExButton" id="closeDisp"
		action="#{pc_Kab00809T02.doCloseDispAction}"></hx:commandExButton> <h:outputText
		styleClass="outputText" id="htmlFuncId"
		value="#{pc_Kab00809T02.funcId}"></h:outputText> <h:outputText
		styleClass="outputText" id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
		styleClass="outputText" id="htmlScrnName"
		value="#{pc_Kab00809T02.screenName}"></h:outputText></div>

	<!--↓outer↓-->
	<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
		id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText></FIELDSET>

	<!--↓content↓-->
	<DIV class="head_button_area">
	<!-- ↓ここに戻る／閉じるボタンを配置 --> 
	<hx:commandExButton
		type="submit" value="戻る" styleClass="commandExButton"
		id="returnDisp" action="#{pc_Kab00809T02.doReturnDispAction}"
		rendered="#{pc_Kab00809T02.kab00809.propReturnDisp.rendered}" tabindex="5"></hx:commandExButton>
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	<DIV id="content">
	<DIV class="column" align="center">
	
	<!-- ↓ここにコンポーネントを配置 -->
	
	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="880" style="margin-top: 20px;">
		<TBODY>
			<TR>
				<TH class="v_a" width="140"><h:outputText styleClass="outputText"
							id="lblLeaseNo"
							style="#{pc_Kab00809T02.kab00809.propLeaseNo.labelStyle}"
							value="#{pc_Kab00809T02.kab00809.propLeaseNo.labelName}"></h:outputText></TH>
				<TD><h:outputText styleClass="outputText" id="htmlLeaseNo"
							value="#{pc_Kab00809T02.kab00809.propLeaseNo.stringValue}"
							title="#{pc_Kab00809T02.kab00809.propLeaseNo.stringValue}"></h:outputText></TD>
			</TR>
		</TBODY>
	</TABLE>		
	
	<TABLE border="0" cellpadding="0" cellspacing="0" width="880" height="407" style="margin-top: 10px;">
		<TBODY>
			<TR>
				<TD align="left" height="27">
					<hx:commandExButton type="submit" style="width: 164px" value="契約情報"
							styleClass="tab_head_off" id="keiyakuInfo" action="#{pc_Kab00809T02.doKeiyakuInfoAction}" tabindex="1"></hx:commandExButton><hx:commandExButton
							type="submit" style="width: 164px" value="管理情報"
							styleClass="tab_head_on" id="kanriInfo" tabindex="2">
						</hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD height="100%" align="left" valign="top">
				<TABLE border="1" cellpadding="20" cellspacing="0" height="380" width="880" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center"  valign="top">
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
									width="860" style="margin-top: 20px;">
									<TBODY>
										<TR>
											<TH width="140" class="v_f" colspan="2"><h:outputText styleClass="outputText"
												id="lblShutokuRingiNo"
												value="#{pc_Kab00809T02.propShutokuRingiNo.labelName}"
												style="#{pc_Kab00809T02.propShutokuRingiNo.labelStyle}"></h:outputText></TH>
											<TD colspan="3" width="720">
											<h:outputText styleClass="outputText" id="htmlShutokuRingiNo"
													value="#{pc_Kab00809T02.propShutokuRingiNo.stringValue}"
													title="#{pc_Kab00809T02.propShutokuRingiNo.stringValue}"></h:outputText>
											</TD>
										</TR>
										<TR>
											<TH width="140" class="group_label_top" colspan="2"><h:outputText
												styleClass="outputText" id="lblKenkyuhi" value="研究費"></h:outputText></TH>
												<TD colspan="3"></TD>
											</TR>
										<TR>
											<TH width="20" class="group_label"></TH>
											<TH width="120" class="v_c"><h:outputText
													styleClass="outputText" id="lblKenkyuKadai" value="研究課題"></h:outputText></TH>
											<TD colspan="3">
											<DIV style="width:720px;white-space:nowrap;overflow:hidden;display:block;">											
											<h:outputText styleClass="outputText" id="htmlHakkoNendo"
													style="#{pc_Kab00809T02.propHakkoNendo.style}"
													value="#{pc_Kab00809T02.propHakkoNendo.stringValue}"></h:outputText>
											<h:outputText styleClass="outputText" id="htmlKenkyuKadaiNo"
													style="#{pc_Kab00809T02.propKenkyuKadaiNo.style}"
													value="#{pc_Kab00809T02.propKenkyuKadaiNo.stringValue}"
													title="#{pc_Kab00809T02.propKenkyuKadaiNo.stringValue}"></h:outputText>
											<h:outputText styleClass="outputText"
													id="htmlKenkyuKadaiName"
													style="#{pc_Kab00809T02.propKenkyuKadaiName.style}"
													value="#{pc_Kab00809T02.propKenkyuKadaiName.stringValue}"
													title="#{pc_Kab00809T02.propKenkyuKadaiName.stringValue}"></h:outputText>
											</DIV>		
											</TD>
										</TR>
										<TR>
											<TH width="20" class="group_label"></TH>
											<TH width="120" class="v_c"><h:outputText
													styleClass="outputText" id="lblKenkyusha" value="研究者"></h:outputText></TH>
											<TD colspan="3">
											<DIV style="width:720px;white-space:nowrap;overflow:hidden;display:block;">
											<h:outputText styleClass="outputText" id="htmlKenkyushaCd"
													style="#{pc_Kab00809T02.propKenkyushaCd.style}"
													value="#{pc_Kab00809T02.propKenkyushaCd.stringValue}"
													title="#{pc_Kab00809T02.propKenkyushaCd.stringValue}"></h:outputText>
											<h:outputText styleClass="outputText" id="htmlKenkyushaName"
													style="#{pc_Kab00809T02.propKenkyushaName.style}"
													value="#{pc_Kab00809T02.propKenkyushaName.stringValue}"
													title="#{pc_Kab00809T02.propKenkyushaName.stringValue}"></h:outputText>
											</DIV>
											</TD>
										</TR>
										<TR>
											<TH width="20" class="group_label_bottom"></TH>
											<TH width="120" class="v_c"><h:outputText
													styleClass="outputText" id="lblUchiMeisai" value="内訳明細"></h:outputText></TH>
											<TD colspan="3">
											<DIV style="width:720px;white-space:nowrap;overflow:hidden;display:block;">
											<h:outputText styleClass="outputText" id="htmlUchiMeisaiCd"
													style="#{pc_Kab00809T02.propUchiMeisaiCd.style}"
													value="#{pc_Kab00809T02.propUchiMeisaiCd.stringValue}"
													title="#{pc_Kab00809T02.propUchiMeisaiCd.stringValue}"></h:outputText>
											<h:outputText styleClass="outputText" id="htmlUchiMeisaiName"
													style="#{pc_Kab00809T02.propUchiMeisaiName.style}"
													value="#{pc_Kab00809T02.propUchiMeisaiName.stringValue}"
													title="#{pc_Kab00809T02.propUchiMeisaiName.stringValue}"></h:outputText>
											</DIV>
											</TD>
										</TR>	
										<TR>
											<TH class="v_a" colspan="2"><h:outputText styleClass="outputText"
												id="lblSaiLeaseRyo"
												value="#{pc_Kab00809T02.propSaiLeaseRyo.labelName}"
												style="#{pc_Kab00809T02.propSaiLeaseRyo.labelStyle}"></h:outputText></TH>
											<TD colspan="3">
											<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">
												<TR>
													<TD width="100" style="text-align: right">
														<h:outputText styleClass="outputText" id="htmlSaiLeaseRyo"
																value="#{pc_Kab00809T02.propSaiLeaseRyo.stringValue}"></h:outputText>
													</TD>
													<TD></TD>
												</TR>
											</TABLE>
											</TD>
										</TR>	
										<TR>	
											<TH class="v_b" colspan="2"><h:outputText styleClass="outputText"
												id="lblZenLeaseNo"
												value="#{pc_Kab00809T02.propZenLeaseNo.labelName}"
												style="#{pc_Kab00809T02.propZenLeaseNo.labelStyle}"></h:outputText></TH>
											<TD colspan="3">
											<h:outputText styleClass="outputText" id="htmlZenLeaseNo"
													value="#{pc_Kab00809T02.propZenLeaseNo.stringValue}"
													title="#{pc_Kab00809T02.propZenLeaseNo.stringValue}"></h:outputText>
											</TD>
										</TR>
										<TR>
											<TH class="v_d" colspan="2"><h:outputText styleClass="outputText"
												id="lblWariyasuKonyuSentakuken"
												value="#{pc_Kab00809T02.propWariyasuKonyuSentakuken.name}"
												style="#{pc_Kab00809T02.propWariyasuKonyuSentakuken.labelStyle}"></h:outputText></TH>
											<TD colspan="3">
											<h:outputText styleClass="outputText"
													id="htmlWariyasuKonyuSentakuken"
													value="#{pc_Kab00809T02.propWariyasuKonyuSentakuken.stringValue}"
													title="#{pc_Kab00809T02.propWariyasuKonyuSentakuken.stringValue}"></h:outputText>
											</TD>
										</TR>
										<TR>
											<TH class="v_e" colspan="2"><h:outputText styleClass="outputText"
												id="lblSpecialShiyoNaiyo"
												value="#{pc_Kab00809T02.propSpecialShiyoNaiyo.labelName}"
												style="#{pc_Kab00809T02.propSpecialShiyoNaiyo.labelStyle}"></h:outputText></TH>
											<TD colspan="3">
											<DIV style="width:720px;white-space:nowrap;overflow:hidden;display:block;">
											<h:outputText styleClass="outputText"
													id="htmlSpecialShiyoNaiyo"
													value="#{pc_Kab00809T02.propSpecialShiyoNaiyo.stringValue}"
													title="#{pc_Kab00809T02.propSpecialShiyoNaiyo.stringValue}"></h:outputText>
											</DIV>		
											</TD>
										</TR>
										<TR>
											<TH class="v_b" colspan="2"><h:outputText
													styleClass="outputText" id="lblBiko"
													value="#{pc_Kab00809T02.propBiko.name}"
													style="#{pc_Kab00809T02.propBiko.labelStyle}"></h:outputText></TH>
											<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
												id="htmlBiko" cols="80" rows="5"
												value="#{pc_Kab00809T02.propBiko.stringValue}"
												readonly="#{pc_Kab00809T02.propBiko.readonly}"
												style="#{pc_Kab00809T02.propBiko.style}" disabled="true"></h:inputTextarea></TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>

			<TABLE width="880" border="0" style="" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="資産情報"
							styleClass="commandExButton_etc" id="shisanInfo"
							action="#{pc_Kab00809T02.doShisanInfoAction}" tabindex="3">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="支払情報"
							styleClass="commandExButton_etc" id="shiharaiInfo"
							action="#{pc_Kab00809T02.doShiharaiInfoAction}" tabindex="4">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

	<!-- ↑ここにコンポーネントを配置 --></DIV>
	</DIV>
	<!--↑content↑-->
	</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

