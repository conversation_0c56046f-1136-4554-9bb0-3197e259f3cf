<%--
  校友会グループ情報登録（連絡先）

  <AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/kk/Kkc00101T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kkc00101T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">

// 画面ロード時のグループ名称再取得
function loadAction(event){
  doGrpNameAjax(document.getElementById('form1:htmlGrpCd'), event, 'form1:ajaxGrpNm');
  setZipMenu('form1:htmlGrpAddrNo','form1:htmlGrpAddr1','form1:htmlGrpAddrKana1');
}

// グループ名称を取得する
function doGrpNameAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/kk/KkcGroupAJAX";
  var args = new Array();
  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// 住所検索画面（引数：①②郵便番号、③④住所１、⑤住所カナ１）
function openJyusyoSubWindow(field1, field2, field3) {
  var zipNo = document.getElementById(field1).value;
  zipNo = encodeURIComponent(zipNo);
  var add = document.getElementById(field2).value;
  add = encodeURIComponent(add);
  var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp"
    +"?"
    +"zipNo=" + field1
    +"&zipNoValue=" + zipNo
    +"&jyusyoKanji=" + field2
    +"&jyusyoValue=" + add
    +"&jyusyoKana=" + field3
    +"&chihoCd=";
  openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");
  return true;
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// 確定時のクリック処理
function onClickUpdate(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// 削除時のクリック処理
function onClickDelete(id) {
  var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
  if (kbn == "0") {
    return confirm(id);
  }
  return true;
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}
// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {

}

</SCRIPT></HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kkc00101T02.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Kkc00101T02">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Kkc00101T02.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kkc00101T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kkc00101T02.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

  <TABLE border="0" cellpadding="5">
    <TBODY>
      <TR>
        <TD width="870">
        <TABLE class="table" width="100%">
          <TBODY>
            <TR align="center" valign="middle">
              <!--グループコード -->
              <TH nowrap class="v_a" width="190">
                <h:outputText
                  id="lblGrpCd" styleClass="outputText"
                  value="#{pc_Kkc00101T01.kkc00101.propGrpCd.labelName}"
                  style="#{pc_Kkc00101T01.kkc00101.propGrpCd.labelStyle}">
                </h:outputText>
              </TH>
              <TD width="500">
                <h:inputText
                  id="htmlGrpCd" styleClass="inputText" size="18"
                  maxlength="#{pc_Kkc00101T01.kkc00101.propGrpCd.maxLength}"
                  disabled="#{pc_Kkc00101T01.kkc00101.propGrpCd.disabled}"
                  value="#{pc_Kkc00101T01.kkc00101.propGrpCd.stringValue}"
                  readonly="#{pc_Kkc00101T01.kkc00101.propGrpCd.readonly}"
                  style="#{pc_Kkc00101T01.kkc00101.propGrpCd.style}"
                  onblur="return doGrpNameAjax(this, event, 'form1:ajaxGrpNm');">
                </h:inputText>
                <!-- 検ボタン -->
                <hx:commandExButton
                  type="submit" id="search" styleClass="commandExButton_search"
                  value="検"
                  disabled="#{pc_Kkc00101T01.kkc00101.propSearch.disabled}"
                  action="#{pc_Kkc00101T01.doSearchAction}">
                </hx:commandExButton>
                <h:inputText
                  id="ajaxGrpNm" styleClass="likeOutput" tabindex="-1" size="50"
                  readonly="true"
                  value="#{pc_Kkc00101T01.kkc00101.propGrpNm.stringValue}">
                </h:inputText>
              </TD>
              <TD style="background-color: transparent; text-align: right" class="clear_border">
                <!-- 選択ボタン -->
                <hx:commandExButton
                  type="submit" id="select" styleClass="commandExButton"
                  value="選　択"
                  disabled="#{pc_Kkc00101T01.kkc00101.propSelect.disabled}"
                  action="#{pc_Kkc00101T01.doSelectAction}">
                </hx:commandExButton>
                <!-- 解除ボタン -->
                <hx:commandExButton
                  type="submit" id="unselect" styleClass="commandExButton"
                  value="解　除"
                  disabled="#{pc_Kkc00101T01.kkc00101.propUnSelect.disabled}"
                  action="#{pc_Kkc00101T01.doUnselectAction}">
                </hx:commandExButton>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <BR>
        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TBODY>
            <TR align="left">
              <TD>
              <TABLE border="0" cellpadding="0" cellspacing="0"
                style="border-bottom-style: none; ">
                <TBODY>
                  <TR>
                    <TD class="tab_head_on" width="100">
                      <hx:commandExButton
                        type="submit" id="tabKkc00101T01" styleClass="tab_head_off"
                        value="#{pc_Kkc00101T01.kkc00101.propTabNameGrp.stringValue}"
                        action="#{pc_Kkc00101T02.doTabKkc00101T01Action}"
                        style="width: 100%">
                      </hx:commandExButton>
                    </TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="button" id="tabKkc00101T02" styleClass="tab_head_on"
                        value="#{pc_Kkc00101T01.kkc00101.propTabNameAddr.stringValue}"
                        style="width:100%">
                      </hx:commandExButton></TD>
                    <TD class="tab_head_off" width="100">
                      <hx:commandExButton
                        type="submit" id="tabKkc00101T03" styleClass="tab_head_off"
                        value="#{pc_Kkc00101T01.kkc00101.propTabNameAction.stringValue}"
                        action="#{pc_Kkc00101T02.doTabKkc00101T03Action}"
                        style="width:100%">
                      </hx:commandExButton>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
            <TR>
              <TD>
              <TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" width="100%">
                <TBODY>
                  <TR>
                    <TD width="100%">
                    <div style="height: 400px">
                    <BR>
                    <TABLE class="table" width="810">
                      <TBODY>
                        <TR>
                          <!-- グループ郵便番号 -->
                          <TH nowrap class="v_a">
                            <h:outputText
                              id="lblGrpAddrNo" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propGrpAddrNo.labelName}"
                              style="#{pc_Kkc00101T02.propGrpAddrNo.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddrNo" styleClass="inputText" size="7"
                              maxlength="#{pc_Kkc00101T02.propGrpAddrNo.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddrNo.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddrNo.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddrNo.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddrNo.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- グループ住所 -->
                          <TH nowrap class="v_b" rowspan="3">
                            <h:outputText
                              id="lblGrpAddr1" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propGrpAddr1.labelName}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddr1" styleClass="inputText" size="50"
                              maxlength="#{pc_Kkc00101T02.propGrpAddr1.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddr1.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddr1.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddr1.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddr1.style}">
                            </h:inputText>
                            <hx:commandExButton
                              type="button" id="addrSearch" styleClass="commandExButton_search"
                              value="〒"
                              onclick="openJyusyoSubWindow('form1:htmlGrpAddrNo', 'form1:htmlGrpAddr1', 'form1:htmlGrpAddrKana1');">
                            </hx:commandExButton>
                            <h:outputText
                              styleClass="outputText" id="text1"
                              value="（都道府県市区町村大字）">
                            </h:outputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddr2" styleClass="inputText" size="50"
                              maxlength="#{pc_Kkc00101T02.propGrpAddr2.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddr2.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddr2.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddr2.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddr2.style}">
                            </h:inputText>
                            <h:outputText
                              id="text2" styleClass="outputText"
                              value="（丁目・字以下）">
                            </h:outputText></TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddr3" styleClass="inputText" size="50"
                              maxlength="#{pc_Kkc00101T02.propGrpAddr3.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddr3.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddr3.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddr3.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddr3.style}">
                            </h:inputText>
                            <h:outputText
                              id="text3" styleClass="outputText"
                              value="（マンション/ビル名 号室）">
                            </h:outputText></TD>
                        </TR>
                        <TR>
                          <!-- グループ住所カナ -->
                          <TH nowrap class="v_c" rowspan="3">
                            <h:outputText
                              id="lblGrpAddrKana1" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propGrpAddrKana1.labelName}"
                              style="#{pc_Kkc00101T02.propGrpAddrKana1.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddrKana1" styleClass="inputText" size="100"
                              maxlength="#{pc_Kkc00101T02.propGrpAddrKana1.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddrKana1.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddrKana1.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddrKana1.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddrKana1.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddrKana2" styleClass="inputText" size="100"
                              maxlength="#{pc_Kkc00101T02.propGrpAddrKana2.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddrKana2.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddrKana2.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddrKana2.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddrKana2.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddrKana3" styleClass="inputText" size="100"
                              maxlength="#{pc_Kkc00101T02.propGrpAddrKana3.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddrKana3.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddrKana3.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddrKana3.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddrKana3.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- グループ住所英語 -->
                          <TH nowrap class="v_d" rowspan="3">
                            <h:outputText id="lblGrpAddrEng1" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propGrpAddrEng1.labelName}"
                              style="#{pc_Kkc00101T02.propGrpAddrEng1.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddrEng1" styleClass="inputText" size="100"
                              maxlength="#{pc_Kkc00101T02.propGrpAddrEng1.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddrEng1.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddrEng1.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddrEng1.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddrEng1.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddrEng2" styleClass="inputText" size="100"
                              maxlength="#{pc_Kkc00101T02.propGrpAddrEng2.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddrEng2.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddrEng2.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddrEng2.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddrEng2.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpAddrEng3" styleClass="inputText" size="100"
                              maxlength="#{pc_Kkc00101T02.propGrpAddrEng3.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpAddrEng3.disabled}"
                              value="#{pc_Kkc00101T02.propGrpAddrEng3.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpAddrEng3.readonly}"
                              style="#{pc_Kkc00101T02.propGrpAddrEng3.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- グループ電話番号 -->
                          <TH nowrap class="v_a">
                            <h:outputText
                              id="lblGrpTel" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propGrpTel.labelName}"
                              style="#{pc_Kkc00101T02.propGrpTel.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpTel" styleClass="inputText" size="25"
                              maxlength="#{pc_Kkc00101T02.propGrpTel.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpTel.disabled}"
                              value="#{pc_Kkc00101T02.propGrpTel.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpTel.readonly}"
                              style="#{pc_Kkc00101T02.propGrpTel.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- グループＦＡＸ番号 -->
                          <TH nowrap class="v_a">
                            <h:outputText
                              id="lblGrpFax" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propGrpFax.labelName}"
                              style="#{pc_Kkc00101T02.propGrpFax.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:inputText
                              id="htmlGrpFax" styleClass="inputText" size="25"
                              maxlength="#{pc_Kkc00101T02.propGrpFax.maxLength}"
                              disabled="#{pc_Kkc00101T02.propGrpFax.disabled}"
                              value="#{pc_Kkc00101T02.propGrpFax.stringValue}"
                              readonly="#{pc_Kkc00101T02.propGrpFax.readonly}"
                              style="#{pc_Kkc00101T02.propGrpFax.style}">
                            </h:inputText>
                          </TD>
                        </TR>
                        <TR>
                          <!-- 海外所在 -->
                          <TH nowrap class="v_a" width="150">
                            <h:outputText id="lblKaigaiSyozai" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propKaigaiSyozaiCheckBox.labelName}"
                              style="#{pc_Kkc00101T02.propKaigaiSyozaiCheckBox.labelStyle}">
                            </h:outputText></TH>
                          <TD colspan="3">
                            <h:selectBooleanCheckbox id="htmlKaigaiSyozai" styleClass="selectBooleanCheckbox" 
                              disabled="#{pc_Kkc00101T02.propKaigaiSyozaiCheckBox.disabled}"
                              value="#{pc_Kkc00101T02.propKaigaiSyozaiCheckBox.checked}">
                            </h:selectBooleanCheckbox>
                            <h:outputText id="lblKaigaiSyozaiTxt" styleClass="outputText"
                              value="海外である" />
                          </TD>
                        </TR>
                        <TR>
                          <!-- 地域 -->
                          <TH nowrap class="v_a" width="150">
                            <h:outputText
                              id="lblChiikiCombo" styleClass="outputText"
                              value="#{pc_Kkc00101T02.propChiikiCombo.labelName}"
                              style="#{pc_Kkc00101T02.propChiikiCombo.labelStyle}">
                            </h:outputText>
                          </TH>
                          <TD colspan="3">
                            <h:selectOneMenu
                              id="htmlChiikiCombo" styleClass="selectOneMenu"
                              value="#{pc_Kkc00101T02.propChiikiCombo.value}"
                              style="width:270px;">
                              <f:selectItems value="#{pc_Kkc00101T02.propChiikiCombo.list}" />
                            </h:selectOneMenu>
                          </TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="810">
                      <TBODY>
                        <TR align="right">
                          <TD align="center"><hx:commandExButton
                            type="submit" value="クリア" styleClass="commandExButton_etc"
                            id="clear" disabled="#{pc_Kkc00101T01.kkc00101.propUpdate.disabled}"
                            onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
                            action="#{pc_Kkc00101T02.doClearAction}"></hx:commandExButton>
                          </TD>
                        </TR>
                      </TBODY>
                    </TABLE>
                    <BR>
                    </div>
                    </TD>
                  </TR>
                </TBODY>
              </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="100%">
          <TBODY>
            <TR align="right">
              <TD align="center">
                <hx:commandExButton type="submit" id="update" styleClass="commandExButton_dat"
                  value="確定"
                  disabled="#{pc_Kkc00101T01.kkc00101.propUpdate.disabled}"
                  onclick="return onClickUpdate('#{msg.SY_MSG_0001W}');"
                  action="#{pc_Kkc00101T02.doUpdateAction}">
                </hx:commandExButton>
                <hx:commandExButton type="submit" id="delete" styleClass="commandExButton_dat"
                  value="削除"
                  disabled="#{pc_Kkc00101T01.kkc00101.propDelete.disabled}"
                  onclick="return onClickDelete('#{msg.SY_MSG_0004W}');"
                  action="#{pc_Kkc00101T02.doDeleteAction}">
                </hx:commandExButton></TD>
            </TR>
          </TBODY>
        </TABLE>
        </TD>
      </TR>
    </TBODY>
  </TABLE>
  <h:inputHidden id="htmlHidButtonKbn" value="#{pc_Kkc00101T01.kkc00101.propHidButtonKbn.integerValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidAction" value="#{pc_Kkc00101T01.kkc00101.propHidAction.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidInsUpdKbn" value="#{pc_Kkc00101T01.kkc00101.propHidInsUpdKbn.stringValue}"></h:inputHidden>
  <h:inputHidden id="htmlHidErrMessage" value="#{pc_Kkc00101T01.kkc00101.propHidErrMessage.value}"></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
