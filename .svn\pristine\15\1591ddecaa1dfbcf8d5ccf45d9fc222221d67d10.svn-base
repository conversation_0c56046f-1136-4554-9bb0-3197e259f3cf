<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghf01502.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>

<TITLE>Ghf01502.jsp</TITLE>

<SCRIPT type="text/javascript">

	window.attachEvent("onload", attachFormatNumber);

	//金融機関取引口座検索画面へ遷移
	function openPCog1101Window() {
		openPCog1101("<%=com.jast.gakuen.rev.co.PCog1101.getWindowOpenOption() %>");
		return true;
	}
	
	//金融機関情報検索画面へ遷移
	function openPCog0901Window() {
		openPCog0901("<%=com.jast.gakuen.rev.co.PCog0901.getWindowOpenOption() %>");
		return true;
	}
	
	function defaultLoad() {
	
		changeScrollPosition('scrollHenkinList','listHenkinScroll');
	
		var buttonState = null;
		buttonState = document.getElementById('form1:htmlActiveControl').value;
		
		// 各種ボタンの活性・非活性化
		if (buttonState == 0) {
			// 確定・取消・クリアボタンの非活性化
			document.getElementById('form1:regist').disabled = true;
			document.getElementById('form1:revoke').disabled = true;
			document.getElementById('form1:clear').disabled = true;
		}
			
		if (buttonState == 1) {
			// 確定・クリアボタンの活性化、取消ボタンの非活性化
			document.getElementById('form1:regist').disabled = false;
			document.getElementById('form1:revoke').disabled = true;
			document.getElementById('form1:clear').disabled = false;
		}
			
		if (buttonState == 2) {
			// 確定・取消・クリアボタンの活性化
			document.getElementById('form1:regist').disabled = false;
			document.getElementById('form1:revoke').disabled = false;
			document.getElementById('form1:clear').disabled = false;
		}
		
		//Ajaxにて取得したの値を保持するためにjavascriptでreadonlyを設定する
		document.getElementById('form1:htmlBankName').readOnly = true;
		document.getElementById('form1:htmlSitenName').readOnly = true;
		document.getElementById('form1:htmlStuBankName').readOnly = true;
		document.getElementById('form1:htmlStuSitenName').readOnly = true;
		
		//フォーカスの設定
		setFocus();

		//銀行名称取得・支店名称取得Ajax呼び出し
		ajaxStuBankSitenCd();
		
	}

	//フォーカスの設定
	function setFocus(){
		
		var id = null;

		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;

		//リスト内選択ボタンフォーカス時
		if ((id != null) && (id != "")) {
			document.getElementById(id).focus();
		}
		
		//初期化
		document.getElementById('form1:htmlScrollPos').value = "";

	}
	
		//メッセージ出力(OKボタン押下)
	function confirmOk() {
	
		var procRegistFind = document.getElementById('form1:htmlRegistFind').value;

		//確定処理実行
		if(procRegistFind == "1"){
			indirectClick('regist');
		}

	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlRegistFind').value = 0;
	}

	//銀行名称取得・支店名称取得Ajax呼び出し
	function ajaxBankSitenCd() {
		//銀行コード
		var bankCd = document.getElementById('form1:htmlBankCd').value;
		//支店コード
		var sitenCd = document.getElementById('form1:htmlSitenCd').value;
		//銀行名称項目id
		var bankNameId = "form1:htmlBankName";
		//支店名称項目id
		var sitenNameId = "form1:htmlSitenName";
		
		//銀行名称取得Ajax呼び出し
		funcAjaxSetBankCd(bankCd, bankNameId)
		//支店名称取得Ajax呼び出し
		funcAjaxSetSitenCd(bankCd, sitenCd, sitenNameId)
		return true;
	}

	//銀行名称取得・支店名称取得Ajax呼び出し
	function ajaxStuBankSitenCd() {
		//銀行コード
		var bankCd = document.getElementById('form1:htmlStuBankCd').value;
		//支店コード
		var sitenCd = document.getElementById('form1:htmlStuSitenCd').value;
		//銀行名称項目id
		var bankNameId = "form1:htmlStuBankName";
		//支店名称項目id
		var sitenNameId = "form1:htmlStuSitenName";
		
		//銀行名称取得Ajax呼び出し
		funcAjaxSetBankCd(bankCd, bankNameId)
		//支店名称取得Ajax呼び出し
		funcAjaxSetSitenCd(bankCd, sitenCd, sitenNameId)
		return true;
	}
	
	// チェックボックスの一括チェック
	function func_check_on(thisObj, thisEvent) {
		check('htmlPayHenkinList','inputCheckbox');
	}
	
	// チェックボックスの一括解除
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPayHenkinList','inputCheckbox');
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="defaultLoad();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ghf01502.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ghf01502.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Ghf01502.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ghf01502.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

			<DIV class="head_button_area" >
				<hx:commandExButton type="submit"
					value="戻る"
					styleClass="commandExButton_etc"
					id="returnDisp"
					action="#{pc_Ghf01502.doReturnDispAction}">
				</hx:commandExButton>
			</DIV>

			<!--↓content↓-->
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE width="900px">
							<TBODY>
								<TR align="center" valign="middle">
									<TD>
										<TABLE border="0" width="100%" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
													<TH align="center" nowrap class="v_a" width="150px">
														<h:outputText styleClass="outputText" id="lblGaksekiCd"
															value="#{pc_Ghf01502.propGaksekiCd.labelName}"
															style="#{pc_Ghf01502.propGaksekiCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap width="*">
														<h:outputText styleClass="outputText" 
															id="htmlGaksekiCd" style="#{pc_Ghf01502.propGaksekiCd.style}"
															value="#{pc_Ghf01502.propGaksekiCd.value}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_b" width="150px">
														<h:outputText styleClass="outputText" id="lblName"
															value="#{pc_Ghf01502.propGakseiName.labelName}"
															style="#{pc_Ghf01502.propGakseiName.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap width="*">
														<h:outputText styleClass="outputText"
															id="htmlName" style="#{pc_Ghf01502.propGakseiName.style}"
															value="#{pc_Ghf01502.propGakseiName.value}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_c" width="150px">
														<h:outputText styleClass="outputText" id="lblGaknen"
															value="#{pc_Ghf01502.propGakunen.labelName}"
															style="#{pc_Ghf01502.propGakunen.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap width="*">
														<h:outputText styleClass="outputText" id="htmlGaknen"
															style="#{pc_Ghf01502.propGakunen.style}"
															value="#{pc_Ghf01502.propGakunen.value}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_d" width="150px">
														<h:outputText styleClass="outputText" id="lblSemester"
															value="#{pc_Ghf01502.propSemester.labelName}"
															style="#{pc_Ghf01502.propSemester.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap width="*">
														<h:outputText styleClass="outputText"
															id="htmlSemester" style="#{pc_Ghf01502.propSemester.style}"
															value="#{pc_Ghf01502.propSemester.value}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_e" width="150px">
														<h:outputText styleClass="outputText" id="lblSzks"
															value="#{pc_Ghf01502.propSgksNameRyak.labelName}"
															style="#{pc_Ghf01502.propSgksNameRyak.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap width="*">
														<h:outputText styleClass="outputText"
															id="htmlSzks" style="#{pc_Ghf01502.propSgksNameRyak.style}"
															value="#{pc_Ghf01502.propSgksNameRyak.value}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_f" width="150px">
														<h:outputText styleClass="outputText" id="lblIdo"
															value="#{pc_Ghf01502.propIdoSyutgakSbtName.labelName}"
															style="#{pc_Ghf01502.propIdoSyutgakSbtName.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap width="*">
														<h:outputText styleClass="outputText" id="htmlIdo"
															style="#{pc_Ghf01502.propIdoSyutgakSbtName.style}"
															value="#{pc_Ghf01502.propIdoSyutgakSbtName.value}">
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="20"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
													<TH align="left" width="150" nowrap class="v_g"><h:outputText
														styleClass="outputText" id="lblNendoLabel"
														value="#{pc_Ghf01502.propNendo.labelName}"
														style="#{pc_Ghf01502.propNendo.labelStyle}"></h:outputText></TH>
													<TD align="left" width="90"><h:outputText styleClass="outputText"
														id="lblNendo" value="#{pc_Ghf01502.propNendo.stringValue}"
														style="#{pc_Ghf01502.propNendo.labelStyle}"></h:outputText></TD>
													<TH align="left" width="90" nowrap class="v_a"><h:outputText
														styleClass="outputText" id="lblPayCdLabel"
														value="#{pc_Ghf01502.propPayCd.labelName}"
														style="#{pc_Ghf01502.propPayCd.labelStyle}"></h:outputText></TH>
													<TD align="left" width="100"><h:outputText styleClass="outputText"
														id="lblPayCd" value="#{pc_Ghf01502.propPayCd.stringValue}"
														style="#{pc_Ghf01502.propPayCd.labelStyle}"></h:outputText></TD>
													<TH align="left" width="100" nowrap class="v_b"><h:outputText
														styleClass="outputText" id="lblPatternCdLabel"
														value="#{pc_Ghf01502.propPatternCd.labelName}"
														style="#{pc_Ghf01502.propPatternCd.labelStyle}"></h:outputText></TH>
													<TD align="left" width="90"><h:outputText styleClass="outputText"
														id="lblPatternCd"
														value="#{pc_Ghf01502.propPatternCd.stringValue}"
														style="#{pc_Ghf01502.propPatternCd.labelStyle}"></h:outputText></TD>
							
													<TH align="left" width="100" nowrap class="v_c"><h:outputText
														styleClass="outputText" id="lblBunnoKbnCdLabel"
														value="#{pc_Ghf01502.propBunnoKbnCd.labelName}"
														style="#{pc_Ghf01502.propBunnoKbnCd.labelStyle}"></h:outputText></TH>
													<TD align="left" width="*"><h:outputText styleClass="outputText"
														id="lblBunnoKbnCd"
														value="#{pc_Ghf01502.propBunnoKbnCd.stringValue}"
														style="#{pc_Ghf01502.propBunnoKbnCd.labelStyle}"></h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="left" nowrap class="v_d"><h:outputText
														styleClass="outputText" id="lblPayNameLabel"
														value="#{pc_Ghf01502.propPayName.labelName}"
														style="#{pc_Ghf01502.propPayName.labelStyle}"></h:outputText></TH>
													<TD colspan="7" align="left"><h:outputText styleClass="outputText"
														id="lblPayName" value="#{pc_Ghf01502.propPayName.stringValue}"
														style="#{pc_Ghf01502.propPayName.labelStyle}"></h:outputText></TD>
												</TR>
												<TR>
													<TH align="left" nowrap class="v_e"><h:outputText
														styleClass="outputText" id="lblBunnoKbnNameLabel"
														value="#{pc_Ghf01502.propBunnoKbnName.labelName}"
														style="#{pc_Ghf01502.propBunnoKbnName.labelStyle}"></h:outputText></TH>
													<TD colspan="7" align="left"><h:outputText styleClass="outputText"
														id="lblBunnoKbnName"
														value="#{pc_Ghf01502.propBunnoKbnName.stringValue}"
														style="#{pc_Ghf01502.propBunnoKbnName.labelStyle}">
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD align="right"><h:outputText styleClass="outputText"
													id="henkinListSize"
													value="#{pc_Ghf01502.propPayHenkinList.listCount}">
													</h:outputText> <h:outputText styleClass="outputText"
													id="henkinListSizeName" value="件">
													</h:outputText></TD>
												</TR>
												<TR>
													<TD>
													<DIV style="height: 253px; width=100%;" id="listHenkinScroll" 
														onscroll="setScrollPosition('scrollHenkinList',this);"
														class="listScroll"><h:dataTable
														footerClass="footerClass"
														headerClass="headerClass"
														rowClasses="#{pc_Ghf01502.propPayHenkinList.rowClasses}"
														styleClass="meisai_scroll" id="htmlPayHenkinList"
														value="#{pc_Ghf01502.propPayHenkinList.list}" var="varlist"
														width="882px" rows="#{pc_Ghf01502.propPayHenkinList.rows}">
							
																	<%-- チェックボックス --%>
																	<h:column id="column1">
																		<f:facet name="header">
												
																		</f:facet>								
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
																			id="inputCheckbox" value="#{varlist.selected}" 
																			rendered="#{varlist.rendered}" 
																			disabled="#{varlist.propHenkinGaku.disabled}" 
																			tabindex="1">
																		</h:selectBooleanCheckbox>
																		<f:attribute value="30px" name="width" />
																		<f:attribute value="text-align: center" name="style" />
																		<f:attribute value="center" name="align" />
																	</h:column>
																	<%-- 分割NO --%>
																	<h:column id="column2">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propBunkatsuNo.name}" 
																									style="#{pc_Ghf01502.propBunkatsuNo.labelStyle}" 
																									id="txtBunkatsuNoLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:outputText styleClass="outputText" id="text42"
																			value="#{varlist.bunkatsuNo}"></h:outputText>
																		<f:attribute value="60px" name="width" />
																		<f:attribute value="text-align: center" name="style" />
																	</h:column>
																	<%-- 内訳NO --%>
																	<h:column id="column3">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propPayItemNo.labelName}" 
																									style="#{pc_Ghf01502.propPayItemNo.labelStyle}" 
																									id="txtPayItemNoLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:outputText styleClass="outputText" id="text43"
																			value="#{varlist.payItemNo}"></h:outputText>
																		<f:attribute value="60px" name="width" />
																		<f:attribute value="text-align: center" name="style" />
																	</h:column>
																	<%-- 内訳名称 --%>
																	<h:column id="column4">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propItemName.labelName}" 
																									style="#{pc_Ghf01502.propItemName.labelStyle}" 
																									id="txtItemNameLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:outputText styleClass="outputText" id="text44"
																			value="#{varlist.propItemName.displayValue}"
																			title="#{varlist.propItemName.value}"></h:outputText>
																		<f:attribute value="*" name="width" />
																		<f:attribute value="text-align: left" name="style" />
																	</h:column>
																	<%-- 納付金額 --%>
																	<h:column id="column5">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propPayItemGaku.labelName}" 
																									style="#{pc_Ghf01502.propPayItemGaku.labelStyle}" 
																									id="txtPayItemGakuLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:outputText styleClass="outputText" id="text45"
																			value="#{varlist.payItemGaku}"></h:outputText>
																		<f:attribute value="100px" name="width" />
																		<f:attribute value="text-align: right" name="style" />
																	</h:column>
																	<%-- 免除金額 --%>
																	<h:column id="column6">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propPayMenjGaku.labelName}" 
																									style="#{pc_Ghf01502.propPayMenjGaku.labelStyle}" 
																									id="txtPayMenjGakuLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:outputText styleClass="outputText" id="text46"
																			value="#{varlist.payMenjGaku}"></h:outputText>
																		<f:attribute value="100px" name="width" />
																		<f:attribute value="text-align: right" name="style" />
																	</h:column>
																	<%-- 納付済額 --%>
																	<h:column id="column7">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propNyukinGaku.labelName}" 
																									style="#{pc_Ghf01502.propNyukinGaku.labelStyle}" 
																									id="txtNyukinGakuLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:outputText styleClass="outputText" id="text47"
																			value="#{varlist.nyukinGaku}"></h:outputText>
																		<f:attribute value="100px" name="width" />
																		<f:attribute value="text-align: right" name="style" />
																	</h:column>
																	<%-- 返金合計 --%>
																	<h:column id="column8">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propHenkinGokei.labelName}" 
																									style="#{pc_Ghf01502.propHenkinGokei.labelStyle}" 
																									id="txtHenkinGokeiLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:outputText styleClass="outputText" id="text48"
																			value="#{varlist.henkinGokei}"></h:outputText>
																		<f:attribute value="100px" name="width" />
																		<f:attribute value="text-align: right" name="style" />
																	</h:column>
																	<%-- 返金金額 --%>
																	<h:column id="column9">
																		<f:facet name="header">
																			<h:outputText styleClass="outputText"
																									value="#{pc_Ghf01502.propHenkinGaku.labelName}" 
																									id="txtHenkinGakuLabel">
																								</h:outputText>
																		</f:facet>								
																		<h:inputText id="txtHenkinGaku"
																			value="#{varlist.propHenkinGaku.stringValue}" 
																			style="#{varlist.propHenkinGaku.style}; padding-right: 3px; text-align: right"
																			disabled="#{varlist.propHenkinGaku.disabled}"
																			size="10"
																			styleClass="inputText" 
																			rendered="#{varlist.rendered}" tabindex="2">
																		</h:inputText>
																		<f:attribute value="130px" name="width" />
																	</h:column>
							
													</h:dataTable></DIV>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
															<TBODY>
																<TR>
																	<TD class="footerClass">
																		<TABLE class="panelBox">
																			<TBODY>
																				<TR>
																					<TD>
																						<%-- 全選択・全解除 --%>
																						<hx:jspPanel id="inputJspPanel">
																							<INPUT type="button" name="check" value="on"
																								onclick="return func_check_on(this, event);"
																								class="check" tabindex="3">
																							<INPUT type="button" name="uncheck" value="off"
																								onclick="return func_check_off(this, event);"
																								class="uncheck" tabindex="4">
																						</hx:jspPanel>
																					</TD>
																				</TR>
																			</TBODY>
																		</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="20"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
													<TH align="left" width="150px" nowrap class="v_f"><h:outputText
														styleClass="outputText" id="lblChoukaGaku"
														value="#{pc_Ghf01502.propChoukaGaku.labelName}"
														style="#{pc_Ghf01502.propChoukaGaku.labelStyle}"></h:outputText>
													</TH>
													<TD width="300px" nowrap align="left">
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
															id="htmlCheckChoukaGaku" value="#{pc_Ghf01502.propBlnChoukaGaku.checked}"
															disabled="#{pc_Ghf01502.propBlnChoukaGaku.disabled}" 
															tabindex="-1">
														</h:selectBooleanCheckbox>
														<h:outputText styleClass="outputText" id="htmlChoukaGaku"
															value="#{pc_Ghf01502.propChoukaGaku.stringValue}"
															style="#{pc_Ghf01502.propChoukaGaku.style}">
														</h:outputText>
													</TD>
													<TH align="left" width="150px" nowrap class="v_g"><h:outputText
														styleClass="outputText" id="lblChoukaHenkanGaku"
														value="#{pc_Ghf01502.propChoukaHenkanGaku.labelName}"
														style="#{pc_Ghf01502.propChoukaHenkanGaku.labelStyle}"></h:outputText>
													</TH>
													<TD width="300px" nowrap align="left">
														<h:outputText styleClass="outputText" id="htmlChoukaHenkanGaku"
															value="#{pc_Ghf01502.propChoukaHenkanGaku.stringValue}"
															style="#{pc_Ghf01502.propChoukaHenkanGaku.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR>				
													<TH align="left" nowrap class="v_a"><h:outputText
																	styleClass="outputText" id="lblJuriDate"
																	value="#{pc_Ghf01502.propJuriDate.labelName}"
																	style="#{pc_Ghf01502.propJuriDate.labelStyle}"></h:outputText>
													</TH>
													<TD nowrap valign="middle">
																	<h:inputText styleClass="inputText" id="htmlJuriDate"
																		value="#{pc_Ghf01502.propJuriDate.dateValue}"
																		disabled="#{pc_Ghf01502.propJuriDate.disabled}"
																		size="10" tabindex="5">
																		<f:convertDateTime pattern="yyyy/MM/dd" />
																		<hx:inputHelperDatePicker />
																		<hx:inputHelperAssist errorClass="inputText_Error"
																			promptCharacter="_" imeMode="inactive" />
																	</h:inputText>
													</TD>
													<TH align="left" nowrap class="v_b"><h:outputText
														styleClass="outputText" id="lblHenkinDate"
														value="#{pc_Ghf01502.propHenkinDate.labelName}"
														style="#{pc_Ghf01502.propHenkinDate.labelStyle}"></h:outputText>
													</TH>
													<TD nowrap valign="middle">
														<h:inputText styleClass="inputText" id="htmlHenkinDate"
															value="#{pc_Ghf01502.propHenkinDate.dateValue}"
															disabled="#{pc_Ghf01502.propHenkinDate.disabled}"
															size="10" tabindex="6">
															<f:convertDateTime pattern="yyyy/MM/dd" />
															<hx:inputHelperDatePicker />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" imeMode="inactive" />
														</h:inputText>
													</TD>
												</TR>
												<TR>					
													<TH align="left" nowrap class="v_c"><h:outputText
														styleClass="outputText" id="lblHenkinMethod"
														value="#{pc_Ghf01502.propHenkinMethod.labelName}"
														style="#{pc_Ghf01502.propHenkinMethod.labelStyle}"></h:outputText>
													</TH>
													<TD nowrap valign="middle">
														<h:selectOneRadio
															styleClass="selectOneRadio" id="htmlHenkinMethod"
															value="#{pc_Ghf01502.propHenkinMethod.stringValue}"
															style="#{pc_Ghf01502.propHenkinMethod.style}"
															disabled="#{pc_Ghf01502.propHenkinMethod.disabled}" 
															disabledClass="selectOneRadio_Disabled" 
															layout="lineDirection" tabindex="7">
															<f:selectItem itemValue="0" itemLabel="預金返金" />
															<f:selectItem itemValue="1" itemLabel="現金返金" />
														</h:selectOneRadio>
													</TD>
													<TH align="left" nowrap class="v_d"><h:outputText
														styleClass="outputText" id="lblTainouKanriFlg"
														value="#{pc_Ghf01502.propTainouKanriFlg.labelName}"
														style="#{pc_Ghf01502.propTainouKanriFlg.labelStyle}"></h:outputText>
													</TH>
													<TD nowrap valign="middle">
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
														id="htmlTainouKanriFlg" value="#{pc_Ghf01502.propTainouKanriFlg.checked}"
														disabled="#{pc_Ghf01502.propTainouKanriFlg.disabled}" tabindex="8"></h:selectBooleanCheckbox>
													</TD>
												</TR>
												<TR>
													<TH align="left" nowrap class="v_e"><h:outputText
														styleClass="outputText" id="lblHenkinRiyu"
														value="#{pc_Ghf01502.propHenkinRiyu.labelName}"
														style="#{pc_Ghf01502.propHenkinRiyu.labelStyle}"></h:outputText>
													</TH>
													<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
														id="htmlHenkinRiyu"
														value="#{pc_Ghf01502.propHenkinRiyu.stringValue}"
														style="#{pc_Ghf01502.propHenkinRiyu.style}"
														disabled="#{pc_Ghf01502.propHenkinRiyu.disabled}" 
														rows="3" cols="80" tabindex="9"></h:inputTextarea>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="20"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD align="center" valign="top" width="50%">
														<CENTER>
															<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
																width="100%">
																<THEAD>
																	<TR>
																		<TH width="100%" colspan="3">
																			<h:outputText styleClass="outputText" id="htmlGakkoInfo"
																				value="#{pc_Ghf01502.propGakkoInfo.labelName}"
																				style="#{pc_Ghf01502.propGakkoInfo.labelStyle}"></h:outputText>
																		</TH>
																	</TR>
																</THEAD>
																<TBODY>
																	<TR>
																		<TH align="left" nowrap class="v_f" width="150px"><h:outputText
																			styleClass="outputText" id="lblBankCd"
																			value="#{pc_Ghf01502.propBankCd.labelName}"
																			style="#{pc_Ghf01502.propBankCd.labelStyle}"></h:outputText></TH>
																		<TD align="left" width="250px"><h:inputText styleClass="inputText" id="htmlBankCd" size="4"
																			value="#{pc_Ghf01502.propBankCd.stringValue}"
																			style="#{pc_Ghf01502.propBankCd.style}"
																			disabled="#{pc_Ghf01502.propBankCd.disabled}"
																			maxlength="#{pc_Ghf01502.propBankCd.maxLength}"
																			onblur="return ajaxBankSitenCd();" tabindex="10"></h:inputText></TD>
																		<TD align="left" width="*" rowspan="6">
																			<hx:commandExButton type="submit"
																						styleClass="commandExButton_search" id="sitenInfoSearch"
																						onclick="return openPCog1101Window();"
																						action="#{pc_Ghf01502.doSearchSitenAction}"
																						disabled="#{pc_Ghf01502.propBankCd.disabled}" tabindex="15">
																					</hx:commandExButton>
																		</TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_g"><h:outputText
																			styleClass="outputText" id="lblSitenCd"
																			value="#{pc_Ghf01502.propSitenCd.labelName}"
																			style="#{pc_Ghf01502.propSitenCd.labelStyle}"></h:outputText></TH>
																		<TD align="left"><h:inputText styleClass="inputText" id="htmlSitenCd" size="3"
																			value="#{pc_Ghf01502.propSitenCd.stringValue}"
																			style="#{pc_Ghf01502.propSitenCd.style}"
																			disabled="#{pc_Ghf01502.propSitenCd.disabled}"
																			maxlength="#{pc_Ghf01502.propSitenCd.maxLength}"
																			onblur="return ajaxBankSitenCd();" tabindex="11"></h:inputText></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_a"><h:outputText
																			styleClass="outputText" id="lblBankName"
																			value="#{pc_Ghf01502.propBankName.name}"
																			style="#{pc_Ghf01502.propBankName.labelStyle}"></h:outputText></TH>
																		<TD align="left">
																			<h:inputText styleClass="likeOutput" id="htmlBankName"
																			tabindex="-1" value="#{pc_Ghf01502.propBankName.stringValue}"
																			style="#{pc_Ghf01502.propBankName.style}" size="30"></h:inputText></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_b"><h:outputText
																			styleClass="outputText" id="lblSitenName"
																			value="#{pc_Ghf01502.propSitenName.name}"
																			style="#{pc_Ghf01502.propSitenName.labelStyle}"></h:outputText></TH>
																		<TD align="left">
																			<h:inputText styleClass="likeOutput" id="htmlSitenName"
																			tabindex="-1" value="#{pc_Ghf01502.propSitenName.stringValue}"
																			style="#{pc_Ghf01502.propSitenName.style}" size="30"></h:inputText></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_c"><h:outputText
																			styleClass="outputText" id="lblYokinShumoku"
																			value="#{pc_Ghf01502.propYokinShumoku.name}"
																			style="#{pc_Ghf01502.propYokinShumoku.labelStyle}"></h:outputText>
																		</TH>
																		<TD align="left"><h:selectOneMenu styleClass="selectOneMenu"
																						id="htmlYokinItem"
																						value="#{pc_Ghf01502.propYokinShumoku.stringValue}"
																						style="#{pc_Ghf01502.propYokinShumoku.style}"
																						disabled="#{pc_Ghf01502.propYokinShumoku.disabled}"
																						tabindex="12">
																						<f:selectItems value="#{pc_Ghf01502.propYokinShumoku.list}" />
																					</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_d"><h:outputText
																			styleClass="outputText" id="lblKouzaNo"
																			value="#{pc_Ghf01502.propKouzaNo.labelName}"
																			style="#{pc_Ghf01502.propKouzaNo.labelStyle}"></h:outputText>
																		</TH>
																		<TD><h:inputText styleClass="inputText" id="htmlKouzaNo"
																						size="10" value="#{pc_Ghf01502.propKouzaNo.stringValue}"
																						style="#{pc_Ghf01502.propKouzaNo.style}"
																						disabled="#{pc_Ghf01502.propKouzaNo.disabled}"
																						maxlength="#{pc_Ghf01502.propKouzaNo.maxLength}"
																						tabindex="13"></h:inputText></TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
													</TD>
													<TD align="center" valign="top" width="50%">
														<CENTER>
															<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
																width="100%">
																<THEAD>
																	<TR>
																		<TH colspan="3" align="center">
																			<h:outputText styleClass="outputText" id="htmlGakuseiInfo"
																				value="#{pc_Ghf01502.propGakuseiInfo.labelName}"
																				style="#{pc_Ghf01502.propGakuseiInfo.labelStyle}"></h:outputText>
																		</TH>
																	</TR>
																</THEAD>
																<TBODY>
																	<TR>
																		<TH align="left" nowrap class="v_e" width="150px"><h:outputText
																			styleClass="outputText" id="lblStuBankCd"
																			value="#{pc_Ghf01502.propStuBankCd.labelName}"
																			style="#{pc_Ghf01502.propStuBankCd.labelStyle}"></h:outputText></TH>
																		<TD align="left" width="100px"><h:inputText
																						styleClass="inputText" id="htmlStuBankCd" size="4"
																						value="#{pc_Ghf01502.propStuBankCd.stringValue}"
																						style="#{pc_Ghf01502.propStuBankCd.style}"
																						disabled="#{pc_Ghf01502.propStuBankCd.disabled}"
																						maxlength="#{pc_Ghf01502.propStuBankCd.maxLength}"
																						onblur="return ajaxStuBankSitenCd();" tabindex="16"></h:inputText></TD>
																		<TD align="left" width="*" rowspan="2">
																			<hx:commandExButton type="submit"
																						styleClass="commandExButton_search" id="sitenStuInfoSearch"
																						onclick="return openPCog0901Window();"
																						action="#{pc_Ghf01502.doSearchStuSitenAction}"
																						disabled="#{pc_Ghf01502.propStuBankCd.disabled}"
																						tabindex="18"></hx:commandExButton></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_f"><h:outputText
																			styleClass="outputText" id="lblStuSitenCd"
																			value="#{pc_Ghf01502.propStuSitenCd.labelName}"
																			style="#{pc_Ghf01502.propStuSitenCd.labelStyle}"></h:outputText></TH>
																		<TD align="left"><h:inputText styleClass="inputText"
																						id="htmlStuSitenCd" size="3"
																						value="#{pc_Ghf01502.propStuSitenCd.stringValue}"
																						style="#{pc_Ghf01502.propStuSitenCd.style}"
																						disabled="#{pc_Ghf01502.propStuSitenCd.disabled}"
																						maxlength="#{pc_Ghf01502.propStuSitenCd.maxLength}"
																						onblur="return ajaxStuBankSitenCd();" tabindex="17"></h:inputText></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_g"><h:outputText
																			styleClass="outputText" id="lblStuBankName"
																			value="#{pc_Ghf01502.propStuBankName.labelName}"
																			style="#{pc_Ghf01502.propStuBankName.labelStyle}"></h:outputText></TH>
																		<TD align="left" colspan="2"><h:outputText styleClass="outputText" id="htmlStuBankName"
																			value="#{pc_Ghf01502.propStuBankName.stringValue}"
																			style="#{pc_Ghf01502.propStuBankName.style}"></h:outputText></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_a"><h:outputText
																			styleClass="outputText" id="lblStuSitenName"
																			value="#{pc_Ghf01502.propStuSitenName.labelName}"
																			style="#{pc_Ghf01502.propStuSitenName.labelStyle}"></h:outputText></TH>
																		<TD align="left" colspan="2"><h:outputText styleClass="outputText" id="htmlStuSitenName"
																			value="#{pc_Ghf01502.propStuSitenName.stringValue}"
																			style="#{pc_Ghf01502.propStuSitenName.style}"></h:outputText></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_b"><h:outputText
																			styleClass="outputText" id="lblStuYokinShumoku"
																			value="#{pc_Ghf01502.propYokinShumoku.name}"
																			style="#{pc_Ghf01502.propYokinShumoku.labelStyle}"></h:outputText>
																		</TH>
																		<TD align="left" colspan="2"><h:selectOneMenu
																						styleClass="selectOneMenu" id="htmlStuYokinShumoku"
																						readonly="#{pc_Ghf01502.propStuYokinShumoku.readonly}"
																						value="#{pc_Ghf01502.propStuYokinShumoku.value}"
																						style="#{pc_Ghf01502.propStuYokinShumoku.style}"
																						disabled="#{pc_Ghf01502.propStuYokinShumoku.disabled}"
																						tabindex="19">
																						<f:selectItems
																							value="#{pc_Ghf01502.propStuYokinShumoku.list}" />
																					</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_c"><h:outputText
																			styleClass="outputText" id="lblStuKouzaNo"
																			value="#{pc_Ghf01502.propStuKouzaNo.labelName}"
																			style="#{pc_Ghf01502.propStuKouzaNo.labelStyle}"></h:outputText>
																		</TH>
																		<TD align="left" colspan="2"><h:inputText
																						styleClass="inputText" id="htmlStuKouzaNo" size="10"
																						value="#{pc_Ghf01502.propStuKouzaNo.stringValue}"
																						style="#{pc_Ghf01502.propStuKouzaNo.style}"
																						disabled="#{pc_Ghf01502.propStuKouzaNo.disabled}"
																						maxlength="#{pc_Ghf01502.propStuKouzaNo.maxLength}"
																						tabindex="20"></h:inputText></TD>
																	</TR>
																	<TR>
																		<TH align="left" nowrap class="v_c"><h:outputText
																			styleClass="outputText" id="lblStuKouzaNameKana"
																			value="#{pc_Ghf01502.propStuKouzaNameKana.labelName}"
																			style="#{pc_Ghf01502.propStuKouzaNameKana.labelStyle}"></h:outputText>
																		</TH>
																		<TD align="left" colspan="2"><h:inputText
																						styleClass="inputText" id="htmlStuKouzaNameKana" size="10"
																						value="#{pc_Ghf01502.propStuKouzaNameKana.stringValue}"
																						style="#{pc_Ghf01502.propStuKouzaNameKana.style}"
																						disabled="#{pc_Ghf01502.propStuKouzaNameKana.disabled}"
																						maxlength="#{pc_Ghf01502.propStuKouzaNameKana.maxLength}"
																						size="40"
																						tabindex="21"></h:inputText></TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton type="submit" value="確定"
																	styleClass="commandExButton_dat" id="regist"
																	confirm="#{msg.SY_MSG_0001W}"
																	action="#{pc_Ghf01502.doRegistAction}" tabindex="22">
																</hx:commandExButton>&nbsp;
														<hx:commandExButton type="submit" value="取消"
																	styleClass="commandExButton_dat" id="revoke"
																	confirm="#{msg.SY_MSG_0004W}"
																	action="#{pc_Ghf01502.doRevokeAction}" tabindex="23">
																</hx:commandExButton>&nbsp;
														<hx:commandExButton type="submit" value="クリア"
																	styleClass="commandExButton_etc" id="clear"
																	action="#{pc_Ghf01502.doClearAction}" tabindex="24">
																</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
                </DIV>
                <!--↑CONTENT↑-->
            </DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden
			value="htmlPayHenkinList:txtHenkinGaku=###,###,###;"
			id="htmlFormatNumberOption"></h:inputHidden>

			<h:inputHidden 
				value="#{pc_Ghf01502.propPayHenkinList.scrollPosition}" id="scrollHenkinList">
			</h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghf01502.propActiveControl.value}" id="htmlActiveControl">
			</h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghf01502.propRegistFind.integerValue}" id="htmlRegistFind">
			</h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghf01502.propScrollPos.stringValue}" id="htmlScrollPos">
			</h:inputHidden>

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
