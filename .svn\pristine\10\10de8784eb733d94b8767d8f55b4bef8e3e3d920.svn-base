<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_GHA_BUN_TMP" name="文面テンプレート" prod_id="GH" description="学費で利用する文面テンプレートの情報を持ちます。">
<STATMENT><![CDATA[
GHA_BUN_TMP
]]></STATMENT>
<COLUMN id="BUNMEN_KBN" name="文面区分" type="string" length="7" lengthDP="0" byteLength="7" description="各種証明書を識別する為のコードが設定されます。"/><COLUMN id="TEMPLATE_FILE_NAME" name="テンプレートファイル名" type="string" length="512" lengthDP="0" byteLength="512" description="テンプレートのファイル名です。"/><COLUMN id="TEMPLATE_TITLE" name="テンプレートタイトル" type="string" length="100" lengthDP="0" byteLength="300" description="テンプレートのタイトルが設定されます。"/><COLUMN id="PAY_MEISAI_HYOJI_FLG" name="納付金明細表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="明細項目を表示するかどうかが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="PAY_MEISAI_MENJO_HYOJI_FLG" name="納付金明細免除表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="明細項目に免除を表示するかどうかが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="GINKO_MEISAI_HYOJI_FLG" name="銀行口座明細表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="銀行口座明細項目を表示するかどうかが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="ITTAIGATA_FLG" name="送付状一体型納付書フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="送付状と一体となった納付書かどうかが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SOU_MEISAI_HYOJI_FLG" name="送付部明細表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="送付部に明細項目を表示するかどうかが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SOU_MEISAI_MENJO_HYOJI_FLG" name="送付部明細免除表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="送付部に明細免除項目を表示するかどうかが設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN>
</TABLE>
