<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssa00901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssa00901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css" />
<SCRIPT type="text/javascript">
function checkOn(thisObj, thisEvent) {
	check('htmlFreList','htmlListCheck');
}
function checkOff(thisObj, thisEvent) {
	uncheck('htmlFreList','htmlListCheck');
}
</SCRIPT></HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssa00901.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssa00901.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssa00901.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssa00901.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
<TABLE>
<TBODY>
	<TR>
		<TD>
			<TABLE width="800" border="0" cellpadding="3" cellspacing="0" class="table">
			<TBODY>
				<TR>
					<TH class="v_a" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblGakusekiCd"
							value="#{pc_Ssa00901.propGakusekiCd.labelName}"
							style="#{pc_Ssa00901.propGakusekiCd.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="30%">
						<h:inputText
							styleClass="inputText"
							id="htmlGakusekiCd"
							size="30"
							style="#{pc_Ssa00901.propGakusekiCd.style}"
							value="#{pc_Ssa00901.propGakusekiCd.stringValue}"
							readonly="#{pc_Ssa00901.propGakusekiCd.readonly}"
							maxlength="#{pc_Ssa00901.propGakusekiCd.maxLength}"
							disabled="#{pc_Ssa00901.propGakusekiCd.disabled}">
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_b" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblName"
							value="#{pc_Ssa00901.propName.labelName}"
							style="#{pc_Ssa00901.propName.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="30%">
						<h:inputText
							styleClass="inputText"
							id="htmlName"
							size="30"
							style="#{pc_Ssa00901.propName.style}"
							value="#{pc_Ssa00901.propName.stringValue}"
							disabled="#{pc_Ssa00901.propName.disabled}"
							readonly="#{pc_Ssa00901.propName.readonly}"
							maxlength="#{pc_Ssa00901.propName.maxLength}">
						</h:inputText>
					</TD>
					<TH class="v_c" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblNameKana"
							value="#{pc_Ssa00901.propNameKana.labelName}"
							style="#{pc_Ssa00901.propNameKana.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="30%">
						<h:inputText
							styleClass="inputText"
							id="htmlNameKana"
							size="30"
							style="#{pc_Ssa00901.propNameKana.style}"
							value="#{pc_Ssa00901.propNameKana.stringValue}"
							disabled="#{pc_Ssa00901.propNameKana.disabled}"
							readonly="#{pc_Ssa00901.propNameKana.readonly}"
							maxlength="#{pc_Ssa00901.propNameKana.maxLength}">
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_d" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblSgks"
							value="#{pc_Ssa00901.propSgks.labelName}"
							style="#{pc_Ssa00901.propSgks.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="80%" colspan="3">
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlSgks"
							style="width:615px"
							value="#{pc_Ssa00901.propSgks.value}"
							disabled="#{pc_Ssa00901.propSgks.disabled}"
							readonly="#{pc_Ssa00901.propSgks.readonly}">
							<f:selectItems
								value="#{pc_Ssa00901.propSgks.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_e" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblGakunen"
							value="#{pc_Ssa00901.propGakunen.labelName}"
							style="#{pc_Ssa00901.propGakunen.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="30%">
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlGakunen"
							style="#{pc_Ssa00901.propGakunen.style}"
							value="#{pc_Ssa00901.propGakunen.value}"
							disabled="#{pc_Ssa00901.propGakunen.disabled}"
							readonly="#{pc_Ssa00901.propGakunen.readonly}">
							<f:selectItems
								value="#{pc_Ssa00901.propGakunen.list}" />
						</h:selectOneMenu>
					</TD>
					<TH class="v_f" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblSemester"
							value="#{pc_Ssa00901.propSemester.labelName}"
							style="#{pc_Ssa00901.propSemester.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="30%">
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlSemester"
							style="#{pc_Ssa00901.propSemester.style}"
							value="#{pc_Ssa00901.propSemester.value}"
							disabled="#{pc_Ssa00901.propSemester.disabled}"
							readonly="#{pc_Ssa00901.propSemester.readonly}">
							<f:selectItems
								value="#{pc_Ssa00901.propSemester.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_g" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblKanriTsy"
							value="#{pc_Ssa00901.propKanriTsy.labelName}"
							style="#{pc_Ssa00901.propKanriTsy.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="30%">
						<h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlKanriTsy"
							style="#{pc_Ssa00901.propKanriTsy.style}"
							value="#{pc_Ssa00901.propKanriTsy.value}"
							disabled="#{pc_Ssa00901.propKanriTsy.disabled}"
							readonly="#{pc_Ssa00901.propKanriTsy.readonly}">
							<f:selectItem itemValue="2" itemLabel="含まない" />
							<f:selectItem itemValue="1" itemLabel="含む" />
							<f:selectItem itemValue="3" itemLabel="のみ" />
						</h:selectOneRadio>
					</TD>
					<TH class="v_a" width="20%">
						<h:outputText
							styleClass="outputText"
							id="lblKtdoJyokyoKbn"
							value="#{pc_Ssa00901.propKtdoJokyoKbn.labelName}"
							style="#{pc_Ssa00901.propKtdoJokyoKbn.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="30%">
						<h:selectOneMenu
							styleClass="selectOneMenu"
							id="htmlKtdoJokyoKbn"
							style="#{pc_Ssa00901.propKtdoJokyoKbn.style}"
							value="#{pc_Ssa00901.propKtdoJokyoKbn.value}"
							disabled="#{pc_Ssa00901.propKtdoJokyoKbn.disabled}"
							readonly="#{pc_Ssa00901.propKtdoJokyoKbn.readonly}">
							<f:selectItems
								value="#{pc_Ssa00901.propKtdoJokyoKbn.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
		</TD>
	</TR>
	<TR>
		<TD style="text-align:right;" colspan="4" class="note">
		<h:outputText styleClass="note"
		id="lblSearchGakuseiCmt" value="※学生氏名、学生氏名カナは部分一致"></h:outputText></TD>
	</TR>
</TBODY>
</TABLE>

			<TABLE width="780" border="0" cellspacing="0" cellpadding="0"
				class="" align="center" style="margin-top:10px;">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText styleClass="outputText"
							id="lblCount" value="#{pc_Ssa00901.propFreList.listCount}"></h:outputText>件</TD>
					</TR>
					<TR>
						<TD>
						<DIV id="listScroll" class="listScroll" onscroll="setScrollPosition('scroll',this);" style="height:233px;"><h:dataTable
							border="0" cellspacing="0" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Ssa00901.propFreList.rowClasses}"
							styleClass="meisai_scroll" id="htmlFreList" width="780"
							value="#{pc_Ssa00901.propFreList.list}" var="varlist"
							first="#{pc_Ssa00901.propFreList.first}"
							rows="#{pc_Ssa00901.propFreList.rows}"
							columnClasses="columnCenter,columnLeft,columnLeft">
							<h:column id="column5">
								<f:facet name="header"></f:facet>
								<f:attribute value="15" name="width" />
								<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
									id="htmlListCheck" value="#{varlist.selected}"
									rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblListKgyCd" styleClass="outputText"
										value="自由設定対象コード"></h:outputText>
								</f:facet>
								<f:attribute value="150" name="width" />
								<h:outputText styleClass="outputText" id="htmlListFreTsyCd"
									value="#{varlist.freTsyCd}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="自由設定対象名称"
										id="lblListKgyName">
									</h:outputText>
								</f:facet>
								<f:attribute value="615" name="width" />
								<h:outputText styleClass="outputText" id="htmlListFreTsyName"
									value="#{varlist.freTsyName.displayValue}"
									rendered="#{varlist.rendered}"
									title="#{varlist.freTsyName.value}">
								</h:outputText>
							</h:column>
						</h:dataTable></DIV>
						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD align="left"><hx:commandExButton type="submit"
										styleClass="check" id="btnAllSelect"
										onclick="return checkOn(this, event);">
									</hx:commandExButton> <hx:commandExButton type="submit"
										styleClass="uncheck" id="btnAllUnSelect"
										onclick="return checkOff(this, event);">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="950" class="button_bar" style="margin-top:5px;">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="CSV作成"
							styleClass="commandExButton_out" id="csvout"
							action="#{pc_Ssa00901.doCsvoutAction}"
							confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
							<hx:commandExButton
							type="submit" value="出力項目指定"
							styleClass="commandExButton_out" id="setoutput"
							action="#{pc_Ssa00901.doSetoutputAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
	<h:inputHidden value="#{pc_Ssa00901.propFreList.scrollPosition}" id='scroll'></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	function endload() {
		changeScrollPosition('scroll','listScroll');
	}
</SCRIPT>
</HTML>

