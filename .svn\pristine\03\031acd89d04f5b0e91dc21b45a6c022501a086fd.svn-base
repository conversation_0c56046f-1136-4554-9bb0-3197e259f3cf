<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghd00102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Ghd00102.jsp</TITLE>

<script language="JavaScript">

    //ボタンの活性化制御
    function fncButtonActive(){
    
    	//選択ボタン押下時
		if (document.getElementById('form1:htmlPayList:0:listSelect') != null) {
			document.getElementById("form1:htmlPayList:0:listSelect").focus();				
		}
    
		//スクロール位置保持
		changeScrollPosition('scroll', 'listScroll');
	}
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('search');
	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
		indirectClick('initlist');
	}

	
</script>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="fncButtonActive();">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghd00102.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp" 
                    action="#{pc_Ghd00102.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghd00102.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghd00102.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓CONTENT↓-->
                <DIV class="head_button_area" >
					<hx:commandExButton type="submit" 
						value="戻る" 
						styleClass="commandExButton_etc"
						id="returnDisp" 
						action="#{pc_Ghd00102.doReturnDispAction}">
					</hx:commandExButton>
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<TABLE border="0" width="100%" cellpadding="0" cellspacing="0"
															class="table">
															<TBODY>
																<TR>
																	<TH align="center" nowrap class="v_a" width="150px">
																		<h:outputText styleClass="outputText" id="lblGhYearLabel"
																			value="#{pc_Ghd00102.propGhYear.name}"
																			style="#{pc_Ghd00102.propGhYear.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD width="*">
																		<h:outputText styleClass="outputText" id="lblGhYear"
																			value="#{pc_Ghd00102.propGhYear.value}"
																			style="#{pc_Ghd00102.propGhYear.style}">
																		</h:outputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD align="right"><h:outputText styleClass="outputText" id="text1" value="#{pc_Ghd00102.propPayList.listCount}"></h:outputText><h:outputText
																		styleClass="outputText" id="text2" value="件"></h:outputText></TD>
																</TR>
																<TR>
																	<TD>
																		<DIV style="height: 338px; width=100%;" id="listScroll" onscroll="setScrollPosition('scroll',this);" class="listScroll">
																			<h:dataTable
																				rows="#{pc_Ghd00102.propPayList.rows}"
																				rowClasses="#{pc_Ghd00102.propPayList.rowClasses}"
																				headerClass="headerClass"
																				footerClass="footerClass"
																				styleClass="meisai_scroll" id="htmlPayList"
																				value="#{pc_Ghd00102.propPayList.list}" var="varlist"
																				width="881px">
																				<h:column id="column2">
																					<f:facet name="header">
																						<h:outputText id="text99"
																							value="#{pc_Ghd00102.propPayCd.name}"
																							styleClass="outputText"
																							style="#{pc_Ghd00102.propPayCd.style}">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text5" value="#{varlist.payCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="151px" name="width" />
																				</h:column>
																				<h:column id="column3">
																					<f:facet name="header">
																						<h:outputText id="text100"
																							value="#{pc_Ghd00102.propPayName.name}"
																							styleClass="outputText"
																							style="#{pc_Ghd00102.propPayName.style}">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text7" value="#{varlist.payName}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="*" name="width" />
																				</h:column>
																				<h:column id="column1">
																					<f:facet name="header">
																					</f:facet>
																					<f:attribute value="40px" name="width" />
																					<hx:commandExButton type="submit" value="選択"
																						styleClass="cmdBtn_dat_s" id="listSelect"
																						rendered="#{varlist.rendered}"
																						action="#{pc_Ghd00102.doListSelectAction}">
																					</hx:commandExButton>
																				</h:column>
																			</h:dataTable>
																		</DIV>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
    					</TABLE>
                    </DIV>
                </DIV>
                <!--↑CONTENT↑-->
            </DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />
            
			<h:inputHidden value="#{pc_Ghd00102.propPayList.scrollPosition}" id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Ghd00102.propExecutableSearch.integerValue}" id="htmlExecutableSearch"></h:inputHidden>
			
			<%-- 一覧初期化 --%>
			<hx:commandExButton type="submit"
				value="一覧初期化"
				style="visibility:hidden"
				styleClass="commandExButton_etc"
				id="initList" action="#{pc_Ghd00102.doInitListAction}">
			</hx:commandExButton>
			<%-- 一覧表示 --%>
			<hx:commandExButton type="submit"
				value="一覧表示"
				style="visibility:hidden"
				styleClass="commandExButton_etc"
				id="search" action="#{pc_Ghd00102.doSearchAction}">
			</hx:commandExButton>

        </h:form>
    </hx:scriptCollector>

	</BODY>
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
