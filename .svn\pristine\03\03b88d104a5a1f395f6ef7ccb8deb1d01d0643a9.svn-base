<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmh00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmh00701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {

		risyuKamok = document.getElementById("form1:htmlRisyuKamok").checked;
				
		if(!risyuKamok){
			document.getElementById("form1:htmlTaniSyukei").checked = false;
			document.getElementById("form1:htmlTaniSyukei").disabled = true;
			
			risyuKamok = document.getElementById("form1:htmlRisyuKamok").value = false;
		}
		else {
			document.getElementById("form1:htmlTaniSyukei").checked = false;
			document.getElementById("form1:htmlTaniSyukei").disabled = false;
		
			risyuKamok = document.getElementById("form1:htmlRisyuKamok").value = true;
		}
}</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmh00701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmh00701.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmh00701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmh00701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="600" class="table">
				<TBODY>
					<TR>
						<TH width="150" class="v_b"><h:outputText styleClass="outputText"
							id="lblBunmenKbn" value="#{pc_Kmh00701.propBunmenKbn.labelName}"
							style="#{pc_Kmh00701.propBunmenKbn.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlBunmenKbn" value="#{pc_Kmh00701.propBunmenKbn.value}"
							style="width:190px;">
							<f:selectItems value="#{pc_Kmh00701.propBunmenKbn.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH width="150" class="v_b"><h:outputText styleClass="outputText"
							id="lblHakkouNo" value="#{pc_Kmh00701.propHakkouNo.labelName}"
							style="#{pc_Kmh00701.propHakkouNo.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlHakkouNo"
							value="#{pc_Kmh00701.propHakkouNo.stringValue}"
							style="#{pc_Kmh00701.propHakkouNo.style}">
							<f:selectItem itemValue="0" itemLabel="出力する" />
							<f:selectItem itemValue="1" itemLabel="出力しない" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH width="150" class="v_b"><h:outputText styleClass="outputText"
							id="lblOutputNendo"
							value="#{pc_Kmh00701.propOutputNendo.labelName}"
							style="#{pc_Kmh00701.propOutputNendo.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlOutputNendo" size="4"
							value="#{pc_Kmh00701.propOutputNendo.integerValue}"
							maxlength="#{pc_Kmh00701.propOutputNendo.maxLength}"
							style="#{pc_Kmh00701.propOutputNendo.style}">
							<f:convertNumber type="number" pattern="###0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH width="150" class="v_c"><h:outputText styleClass="outputText"
							id="lblOutputGakkiNo"
							value="#{pc_Kmh00701.propOutputGakkiNo.labelName}"
							style="#{pc_Kmh00701.propOutputGakkiNo.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlOutputGakkiNo" size="2"
							value="#{pc_Kmh00701.propOutputGakkiNo.integerValue}"
							maxlength="#{pc_Kmh00701.propOutputGakkiNo.maxLength}"
							style="#{pc_Kmh00701.propOutputGakkiNo.style}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText></TD>
					</TR>
					<TR>
						<TH width="150" class="v_c"><h:outputText styleClass="outputText"
							id="lblSikak" value="#{pc_Kmh00701.propSikak.labelName}"
							style="#{pc_Kmh00701.propSikak.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSikak" value="#{pc_Kmh00701.propSikak.value}" style="width:450px;">
							<f:selectItems value="#{pc_Kmh00701.propSikak.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH width="150" class="v_e"><h:outputText styleClass="outputText"
							id="lblHsySbt" value="#{pc_Kmh00701.propHsySbt.labelName}"
							style="#{pc_Kmh00701.propHsySbt.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlHsySbt" value="#{pc_Kmh00701.propHsySbt.value}"
							style="width:370px;">
							<f:selectItems value="#{pc_Kmh00701.propHsySbt.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH width="150" class="v_f"><h:outputText styleClass="outputText"
							id="lblHakkouDate" value="#{pc_Kmh00701.propHakkouDate.name}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlHakkouDate" value="#{pc_Kmh00701.propHakkouDate.dateValue}">
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE class="table" width="600">
				<TBODY>
					<TR>
						<TH width="200" class="v_d"><h:outputText styleClass="outputText"
							id="lblSotuGyoTani" value="#{pc_Kmh00701.propSotuGyoTani.labelName}"
							style="#{pc_Kmh00701.propSotuGyoTani.labelStyle}"></h:outputText></TH>
						<TD width="400"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlSotuGyoTani" 
							value="#{pc_Kmh00701.propSotuGyoTani.checked}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="lblSotuGyoTani2" value="帳票に出力する"></h:outputText></TD>
					</TR>
					<TR>
						<TH width="200" class="v_b"><h:outputText styleClass="outputText"
							id="lblRisyuKamok" value="#{pc_Kmh00701.propRisyuKamok.labelName}"
							style="#{pc_Kmh00701.propRisyuKamok.labelStyle}"></h:outputText></TH>
						<TD width="400">
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" 
							id="htmlRisyuKamok" value="#{pc_Kmh00701.propRisyuKamok.checked}" 
							onclick="return func_1(this, event);">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblRisyuKamok2" value="履修中科目を表示する">
						</h:outputText>
						<h:outputText
							styleClass="outputText" id="lblEtcBlank1" value="　">
						</h:outputText>
						<h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlTaniSyukei"
							value="#{pc_Kmh00701.propTaniSyukei.checked}"
							disabled="#{pc_Kmh00701.propTaniSyukei.disabled}">
						</h:selectBooleanCheckbox>
						<h:outputText
							styleClass="outputText" id="lblTaniSyukei" value="単位集計する">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH width="200" class="v_d"><h:outputText styleClass="outputText"
							id="lblFurikaeKamokTitle"
							value="#{pc_Kmh00701.propFurikaeKamokTitle.labelName}"></h:outputText></TH>
						<TD width="400"><h:inputText styleClass="inputText"
							id="htmlFurikaeKamokTitle" size="30"
							value="#{pc_Kmh00701.propFurikaeKamokTitle.stringValue}"
							maxlength="#{pc_Kmh00701.propFurikaeKamokTitle.maxLength}"
							style="#{pc_Kmh00701.propFurikaeKamokTitle.style}"></h:inputText></TD>
					</TR>
					<TR>
						<TH width="200" class="v_e"><h:outputText styleClass="outputText"
						       id="lblKokaiDate"
						       value="#{pc_Kmh00701.propKokaiDate.labelName}"
						       style="#{pc_Kmh00701.propKokaiDate.labelStyle}"></h:outputText></TH>
						<TD width="400"><h:selectBooleanCheckbox
						       styleClass="selectBooleanCheckbox" id="htmlKokaiDate"
						       value="#{pc_Kmh00701.propKokaiDate.checked}"></h:selectBooleanCheckbox><h:outputText
						       styleClass="outputText" id="lblKokaiDate2" value="公開開始日を経過した成績のみ対象"></h:outputText></TD>
					</TR>
					<TR>
						<TH width="200" class="v_e"><h:outputText styleClass="outputText"
						       id="lblYusenFlg"
						       value="#{pc_Kmh00701.propYusenFlg.labelName}"
						       style="#{pc_Kmh00701.propYusenFlg.labelStyle}"></h:outputText></TH>
						<TD width="400"><h:selectBooleanCheckbox
						       styleClass="selectBooleanCheckbox" id="htmlYusenFlg"
						       value="#{pc_Kmh00701.propYusenFlg.checked}"></h:selectBooleanCheckbox><h:outputText
						       styleClass="outputText" id="lblYusenKbn2" value="科目標準名称を優先して出力する"></h:outputText></TD>
					</TR>
					
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" styleClass="commandExButton_dat" id="register" 
						action="#{pc_Kmh00701.doRegisterAction}" value="確定"></hx:commandExButton><hx:commandExButton type="submit" 
						styleClass="commandExButton_etc" id="clear" action="#{pc_Kmh00701.doClearAction}" value="クリア"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>
