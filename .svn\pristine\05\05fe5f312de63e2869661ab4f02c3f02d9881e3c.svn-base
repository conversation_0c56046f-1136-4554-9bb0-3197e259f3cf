<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab02007.java" --%><%-- /jsf:pagecode --%><%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab00808.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>リース支払詳細</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="../ka/inc/gakuenKA.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY>
<hx:scriptCollector id="scriptCollector1"
	preRender="#{pc_Kab02007.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
	value="閉じる" styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kab02007.doCloseDispAction}"></hx:commandExButton> <h:outputText
	styleClass="outputText" id="htmlFuncId"
	value="#{pc_Kab02007.funcId}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlLoginId"
	value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlScrnName"
	value="#{pc_Kab02007.screenName}"></h:outputText></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
	id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText></FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp"
				action="#{pc_Kab02007.doReturnDispAction}"
				rendered="#{pc_Kab02007.propReturnDisp.rendered}" tabindex="24">
			</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="880">	
		<TBODY>		
			<TR>
				<TH class="v_a" width="110"><h:outputText styleClass="outputText"
							id="lblLeaseNo" style="#{pc_Kab02007.propLeaseNo.labelStyle}"
							value="#{pc_Kab02007.propLeaseNo.labelName}"></h:outputText></TH>
				<TD width="140"><h:outputText styleClass="outputText"
							id="htmlLeaseNo" style="#{pc_Kab02007.propLeaseNo.style}"
							value="#{pc_Kab02007.propLeaseNo.stringValue}"
							title="#{pc_Kab02007.propLeaseNo.stringValue}"></h:outputText></TD>
				<TH class="v_b" width="100"><h:outputText styleClass="outputText"
							id="lblShiharaiKikan"
							style="#{pc_Kab02007.propShiharaiKikan.labelStyle}"
							value="#{pc_Kab02007.propShiharaiKikan.labelName}"></h:outputText></TH>
				<TD width="140"><h:outputText styleClass="outputText"
							id="htmlShiharaiKikan"
							style="#{pc_Kab02007.propShiharaiKikan.style}"
							value="#{pc_Kab02007.propShiharaiKikan.stringValue}"></h:outputText>
				</TD>
				<TH class="v_b" width="100"><h:outputText styleClass="outputText"
							id="lblShiharaiKbn"
							style="#{pc_Kab02007.propShiharaiKbn.labelStyle}"
							value="#{pc_Kab02007.propShiharaiKbn.labelName}"></h:outputText></TH>
				<TD width="100"><h:outputText styleClass="outputText"
							id="htmlShiharaiKbn" style="#{pc_Kab02007.propShiharaiKbn.style}"
							value="#{pc_Kab02007.propShiharaiKbn.stringValue}"
							title="#{pc_Kab02007.propShiharaiKbn.stringValue}"></h:outputText>
				</TD>
				<TH class="v_d" width="100"><h:outputText styleClass="outputText"
							id="lblKaikeiNendo"
							style="#{pc_Kab02007.propKaikeiNendo.labelStyle}"
							value="#{pc_Kab02007.propKaikeiNendo.name}"></h:outputText></TH>
				<TD width="90"><h:outputText styleClass="outputText"
							id="htmlKaikeiNendo"
							style="#{pc_Kab02007.propKaikeiNendo.style}"
							value="#{pc_Kab02007.propKaikeiNendo.stringValue}"></h:outputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_e" width="110"><h:outputText styleClass="outputText"
							id="lblKeiyakuBukkenName"
							style="#{pc_Kab02007.propKeiyakuBukkenName.labelStyle}"
							value="#{pc_Kab02007.propKeiyakuBukkenName.labelName}"></h:outputText></TH>
				<TD colspan="5" width="580">
				<DIV style="width:580px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlKeiyakuBukkenName"
							style="#{pc_Kab02007.propKeiyakuBukkenName.style}"
							value="#{pc_Kab02007.propKeiyakuBukkenName.stringValue}"
							title="#{pc_Kab02007.propKeiyakuBukkenName.stringValue}"></h:outputText>
				</DIV>
				</TD>
				<TH class="v_f" width="100"><h:outputText styleClass="outputText"
							id="lblKeiyakuNendo"
							style="#{pc_Kab02007.propKeiyakuNendo.labelStyle}"
							value="#{pc_Kab02007.propKeiyakuNendo.labelName}"></h:outputText></TH>
				<TD width="90"><h:outputText styleClass="outputText"
							id="htmlKeiyakuNendo"
							style="#{pc_Kab02007.propKeiyakuNendo.style}"
							value="#{pc_Kab02007.propKeiyakuNendo.stringValue}"></h:outputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="880" style="margin-top: 20px;">	
		<TBODY>
			<TR>
				<TH class="v_d" width="140" colspan="2">
					<h:outputText styleClass="outputText" id="lblKeiriHikitsugi"
							style="#{pc_Kab02007.propKeiriHikitsugi.labelStyle}"
							value="#{pc_Kab02007.propKeiriHikitsugi.labelName}"></h:outputText>
				</TH>
				<TD colspan="3" width="740">
					<h:outputText styleClass="outputText" id="htmlKeiriHikitsugi"
							value="#{pc_Kab02007.propKeiriHikitsugi.stringValue}"
							title="#{pc_Kab02007.propKeiriHikitsugi.stringValue}"></h:outputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_c" width="140" colspan="2">
					<h:outputText styleClass="outputText" id="lblKeiriShiwake"
							style="#{pc_Kab02007.propKeiriShiwake.labelStyle}"
							value="#{pc_Kab02007.propKeiriShiwake.labelName}"></h:outputText>
				</TH>
				<TD width="300">
					<h:outputText styleClass="outputText" id="htmlKeiriShiwake"
							style="#{pc_Kab02007.propKeiriShiwake.style}"
							value="#{pc_Kab02007.propKeiriShiwake.stringValue}"
							title="#{pc_Kab02007.propKeiriShiwake.stringValue}"></h:outputText>
				</TD>
				<TH class="v_d" width="140">
					<h:outputText styleClass="outputText" id="lblDenpyoNo"
							style="#{pc_Kab02007.propDenpyoNo.labelStyle}"
							value="#{pc_Kab02007.propDenpyoNo.labelName}"></h:outputText>
				</TH>
				<TD width="300">
					<h:outputText styleClass="outputText" id="htmlDenpyoNo"
							style="#{pc_Kab02007.propDenpyoNo.style}"
							value="#{pc_Kab02007.propDenpyoNo.stringValue}"
							title="#{pc_Kab02007.propDenpyoNo.stringValue}"></h:outputText>
				</TD>
			</TR>			
			<TR>
				<TH class="v_c" width="140" colspan="2">
					<h:outputText styleClass="outputText" id="lblShiharaiNengetsu"
							style="#{pc_Kab02007.propShiharaiNengetsu.labelStyle}"
							value="#{pc_Kab02007.propShiharaiNengetsu.labelName}"></h:outputText>
				</TH>
				<TD width="300">
					<h:outputText styleClass="outputText" id="htmlShiharaiNengetsu"
							value="#{pc_Kab02007.propShiharaiNengetsu.stringValue}"></h:outputText>
				</TD>
				<TH class="v_d" width="140">
					<h:outputText styleClass="outputText" id="lblSuitoDate"
							style="#{pc_Kab02007.propSuitoDate.labelStyle}"
							value="#{pc_Kab02007.propSuitoDate.name}"></h:outputText>
				</TH>
				<TD width="300">
					<h:outputText styleClass="outputText" id="htmlSuitoDate"
							style="#{pc_Kab02007.propSuitoDate.style}"
							value="#{pc_Kab02007.propSuitoDate.stringValue}"></h:outputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_g" width="140" colspan="2">
					<h:outputText styleClass="outputText" id="lblYsnTCd"
							value="#{pc_Kab02007.propYsnTCd.labelName}"
							style="#{pc_Kab02007.propYsnTCd.labelStyle}"></h:outputText>
				</TH>
				<TD colspan="3">
				<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlYsnTCd"
							value="#{pc_Kab02007.propYsnTCd.stringValue}"
							title="#{pc_Kab02007.propYsnTCd.stringValue}"></h:outputText>
				</DIV>			
				</TD>
			</TR>
			<TR>
				<TH class="v_a" colspan="2">
					<h:outputText styleClass="outputText"
						id="lblMokuCd"
						value="#{pc_Kab02007.propMokuCd.labelName}"
						style="#{pc_Kab02007.propMokuCd.labelStyle}"></h:outputText>
				</TH>
				<TD colspan="3">
				<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlMokuCd"
							value="#{pc_Kab02007.propMokuCd.stringValue}"
							title="#{pc_Kab02007.propMokuCd.stringValue}"></h:outputText>
				</DIV>			
				</TD>
			</TR>
			<TR>
				<TH class="v_b" colspan="2">
					<h:outputText styleClass="outputText" id="lblKmkCd"
						value="#{pc_Kab02007.propKmkCd.labelName}"
						style="#{pc_Kab02007.propKmkCd.labelStyle}"></h:outputText>
				</TH>
				<TD colspan="3">
				<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlKmkCd"
							value="#{pc_Kab02007.propKmkCd.stringValue}"
							title="#{pc_Kab02007.propKmkCd.stringValue}"></h:outputText>
				</DIV>			
				</TD>
			</TR>
			<TR>
				<TH class="v_e" colspan="2">
					<h:outputText styleClass="outputText" id="lblShiharaiYoteiGaku"
							style="#{pc_Kab02007.propShiharaiYoteiGaku.labelStyle}"
							value="#{pc_Kab02007.propShiharaiYoteiGaku.labelName}"></h:outputText>
				</TH>
				<TD colspan="3">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border" width="100%">
						<TBODY>
							<TR>
								<TD width="90" style="text-align: right;">
									<h:outputText styleClass="outputText" id="htmlShiharaiYoteiGaku"
											value="#{pc_Kab02007.propShiharaiYoteiGaku.stringValue}"></h:outputText>
								</TD>
								<TD></TD>
							</TR>
						</TBODY>
					</TABLE>			
				</TD>
			</TR>
			<TR>
				<TH class="v_f" colspan="2">
					<h:outputText styleClass="outputText" id="lblShiwakeSakuseiGaku"
							style="#{pc_Kab02007.propShiwakeSakuseiGaku.labelStyle}"
							value="#{pc_Kab02007.propShiwakeSakuseiGaku.labelName}"></h:outputText>
				</TH>
				<TD colspan="3">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="clear_border" width="100%">
						<TBODY>
							<TR>
								<TD width="90" style="text-align: right;">
									<h:outputText styleClass="outputText" id="htmlShiwakeSakuseiGaku"
											value="#{pc_Kab02007.propShiwakeSakuseiGaku.stringValue}"></h:outputText>
								</TD>
								<TD></TD>
							</TR>
						</TBODY>
					</TABLE>						
				</TD>
			</TR>
			<TR>
				<TH class="v_g" width="140" colspan="2">
					<h:outputText styleClass="outputText" id="lblKobetsuKbn"
							style="#{pc_Kab02007.propKobetsuKbn.labelStyle}"
							value="#{pc_Kab02007.propKobetsuKbn.labelName}"></h:outputText>
				</TH>
				<TD colspan="3">
				<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlKobetsuKbn"
							value="#{pc_Kab02007.propKobetsuKbn.stringValue}"
							title="#{pc_Kab02007.propKobetsuKbn.stringValue}"></h:outputText>
				</DIV>			
				</TD>
			</TR>
			<TR>
				<TH class="v_a" colspan="2">
					<h:outputText styleClass="outputText" id="lblChushutsuKbn"
							style="#{pc_Kab02007.propChushutsuKbn.labelStyle}"
							value="#{pc_Kab02007.propChushutsuKbn.labelName}"></h:outputText>
				</TH>
				<TD colspan="3">
				<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlChushutsuKbn"
							value="#{pc_Kab02007.propChushutsuKbn.stringValue}"
							title="#{pc_Kab02007.propChushutsuKbn.stringValue}"></h:outputText>
				</DIV>			
				</TD>
			</TR>
			<TR>
				<TH class="v_b" colspan="2">
					<h:outputText styleClass="outputText" id="lblSknGensenKbn"
							style="#{pc_Kab02007.propSknGensenKbn.labelStyle}"
							value="#{pc_Kab02007.propSknGensenKbn.labelName}"></h:outputText>
				</TH>
				<TD colspan="3">
				<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlSknGensenKbn"
							value="#{pc_Kab02007.propSknGensenKbn.stringValue}"
							title="#{pc_Kab02007.propSknGensenKbn.stringValue}"></h:outputText>
				</DIV>			
				</TD>
			</TR>
			<TR>
				<TH class="v_c" colspan="2">
					<h:outputText styleClass="outputText" id="lblTekiyoNameHizuke"
							style="#{pc_Kab02007.propTekiyoNameHizuke.labelStyle}"
							value="#{pc_Kab02007.propTekiyoNameHizuke.labelName}"></h:outputText>
				</TH>
				<TD colspan="3">
				<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText styleClass="outputText" id="htmlTekiyoNameHizuke"
							value="#{pc_Kab02007.propTekiyoNameHizuke.stringValue}"
							title="#{pc_Kab02007.propTekiyoNameHizuke.stringValue}"></h:outputText>
				</DIV>			
				</TD>
			</TR>
			<TR>
				<TH width="140" class="group_label_top" colspan="2"><h:outputText
							styleClass="outputText" id="lblKenkyuhi" value="研究費"></h:outputText></TH>
						<TD colspan="3">
						</TD>
					</TR>
			<TR>
				<TH width="20" class="group_label" colspan=""></TH>
				<TH class="v_d" width="120"><h:outputText
							styleClass="outputText" id="lblKenkyuKadai" value="研究課題"></h:outputText></TH>
				<TD width="740px" nowrap colspan="3">
					<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
						<h:outputText styleClass="outputText" id="htmlHakkoNendo"
							value="#{pc_Kab02007.propHakkoNendo.stringValue}"
							style="#{pc_Kab02007.propHakkoNendo.style}"></h:outputText>
						<h:outputText styleClass="outputText" id="htmlKenkyuKadaiNo"
							style="#{pc_Kab02007.propKenkyuKadaiNo.style}"
							value="#{pc_Kab02007.propKenkyuKadaiNo.stringValue}"
							title="#{pc_Kab02007.propKenkyuKadaiNo.stringValue}"></h:outputText>
						<h:outputText styleClass="outputText" id="htmlKenkyuKadaiName"
							style="#{pc_Kab02007.propKenkyuKadaiName.style}"
							value="#{pc_Kab02007.propKenkyuKadaiName.stringValue}"
							title="#{pc_Kab02007.propKenkyuKadaiName.stringValue}"></h:outputText>		
					</DIV>
				</TD>
			</TR>
			<TR>
				<TH width="20" class="group_label" colspan=""></TH>
				<TH class="v_d" width="120"><h:outputText
							styleClass="outputText" id="lblKenkyusha" value="研究者"></h:outputText></TH>
				<TD width="740px" nowrap colspan="3">
					<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
						<h:outputText styleClass="outputText" id="htmlKenkyushaCd"
							style="#{pc_Kab02007.propKenkyushaCd.style}"
							value="#{pc_Kab02007.propKenkyushaCd.stringValue}"
							title="#{pc_Kab02007.propKenkyushaCd.stringValue}"></h:outputText>
						<h:outputText styleClass="outputText" id="htmlKenkyushaName"
							style="#{pc_Kab02007.propKenkyushaName.style}"
							value="#{pc_Kab02007.propKenkyushaName.stringValue}"
							title="#{pc_Kab02007.propKenkyushaName.stringValue}"></h:outputText>
					</DIV>
				</TD>
			</TR>
			<TR>
				<TH class="group_label_bottom" colspan=""></TH>
				<TH class="v_e"><h:outputText styleClass="outputText"
							id="lblUchiMeisai" value="内訳明細"></h:outputText></TH>
				<TD width="740px" nowrap colspan="3">
					<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
						<h:outputText styleClass="outputText" id="htmlUchiMeisaiCd"
							style="#{pc_Kab02007.propUchiMeisaiCd.style}"
							value="#{pc_Kab02007.propUchiMeisaiCd.stringValue}"
							title="#{pc_Kab02007.propUchiMeisaiCd.stringValue}"></h:outputText>
						<h:outputText styleClass="outputText" id="htmlUchiMeisaiName"
							style="#{pc_Kab02007.propUchiMeisaiName.style}"
							value="#{pc_Kab02007.propUchiMeisaiName.stringValue}"
							title="#{pc_Kab02007.propUchiMeisaiName.stringValue}"></h:outputText>
					</DIV>
				</TD>
			</TR>	
		</TBODY>
	</TABLE>

<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--></DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

