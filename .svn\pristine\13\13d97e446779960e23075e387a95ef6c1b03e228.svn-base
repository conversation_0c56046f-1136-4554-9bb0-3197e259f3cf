<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KMH_ZSK_SYOM" name="発行管理＿在籍期間証明書" prod_id="KM" description="在籍期間証明書の発行情報を持ちます。
『在籍期間証明書』を発行した際に作成されます。">
<STATMENT><![CDATA[
KMH_ZSK_SYOM
]]></STATMENT>
<COLUMN id="HAKKO_NENDO" name="発行年度" type="number" length="4" lengthDP="0" byteLength="0" description="証明書を発行した年度が設定されます。"/><COLUMN id="PATTERN_NO" name="パターンＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="発行ＮＯをカウントするパターンＮＯが設定されます。
"/><COLUMN id="HAKKO_NO" name="発行ＮＯ" type="number" length="6" lengthDP="0" byteLength="0" description="証明書に出力した発行番号が設定されます。
パターンＮＯ毎に発行ＮＯがカウントアップされます。"/><COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生を一意に識別する為の番号です"/><COLUMN id="GAKUSEKI_CD" name="学籍番号" type="string" length="10" lengthDP="0" byteLength="10" description="学籍番号です。"/><COLUMN id="SZK_GAKKA_CD" name="所属学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="学生が所属する学科組織のコードが設定されます。"/><COLUMN id="NAME" name="学生氏名" type="string" length="40" lengthDP="0" byteLength="120" description="在籍期間証明書を発行した学生の氏名が設定されます。"/><COLUMN id="NAME_KANA" name="学生氏名＿カナ" type="string" length="80" lengthDP="0" byteLength="240" description="学生のカナ氏名が設定されます。"/><COLUMN id="HAKKO_DATE" name="発行日付" type="date" length="0" lengthDP="0" byteLength="0" description="証明書の発行日付が設定されます。"/><COLUMN id="OUTPUT_DATE" name="出力日付" type="date" length="0" lengthDP="0" byteLength="0" description="証明書を実際に発行した日付が設定されます。"/><COLUMN id="ITIRAN_SAKUSEI_FLG" name="発行一覧作成済フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="証明書発行管理台帳を出力したかどうかを表します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SYOMEI_MOKUTEKI_CD" name="証明書使用目的コード" type="string" length="3" lengthDP="0" byteLength="3" description="証明書の使用目的を識別するコードが設定されます。"/>
</TABLE>
