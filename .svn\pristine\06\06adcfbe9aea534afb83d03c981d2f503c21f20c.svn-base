<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssd00301T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssd00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css"
	type="text/css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssd00301T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<DIV style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssd00301T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssd00301T01.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssd00301T01.screenName}"></h:outputText></DIV>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　</DIV>

			<DIV id="content">
			<DIV class="column">

			<TABLE border="0" class="table" width="800" cellspacing="0"
				cellpadding="0">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="209"><h:outputText
							styleClass="outputText" id="lblKyujinNendo"
							value="#{pc_Ssd00301T01.ssd00301.propKyujinNendo.labelName}"
							style="#{pc_Ssd00301T01.ssd00301.propKyujinNendo.labelStyle}"></h:outputText></TH>
						<TD nowrap width="234"><h:inputText styleClass="inputText"
							id="htmlKyujinNendo"
							value="#{pc_Ssd00301T01.ssd00301.propKyujinNendo.dateValue}"
							style="#{pc_Ssd00301T01.ssd00301.propKyujinNendo.style}" size="5">
							<hx:inputHelperAssist errorClass="inputText_Error"
							imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" /></h:inputText><h:outputText
							styleClass="outputText" id="lblNen" value="年度"></h:outputText></TD>
						<TH nowrap class="v_a" width="130"><h:outputText
							styleClass="outputText" id="lblBnmn" 
							value="#{pc_Ssd00301T01.ssd00301.propBnmn.labelName}"
							style="#{pc_Ssd00301T01.ssd00301.propBnmn.labelStyle}"></h:outputText></TH>
						<TD nowrap width="225"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlBnmn"
							disabled="#{pc_Ssd00301T01.ssd00301.propBnmn.disabled}"
							style="#{pc_Ssd00301T01.ssd00301.propBnmn.style}"
							value="#{pc_Ssd00301T01.ssd00301.propBnmn.value}" style="width:200px">
							<f:selectItems value="#{pc_Ssd00301T01.ssd00301.propBnmn.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH nowrap class="v_b" width="207"><h:outputText
							styleClass="outputText" id="lblOutputComment" value="出力条件"></h:outputText></TH>
						<TD colspan="3"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlMihasoOnly"
							value="#{pc_Ssd00301T01.ssd00301.propMihasoOnly.checked}"
							style="#{pc_Ssd00301T01.ssd00301.propMihasoOnly.style}"
							disabled="#{pc_Ssd00301T01.ssd00301.propMihasoOnly.disabled}"
							readonly="#{pc_Ssd00301T01.ssd00301.propMihasoOnly.readonly}"></h:selectBooleanCheckbox>
						<h:outputText styleClass="outputText" id="lblOutputComment1"
							value="未発送分のみ出力"></h:outputText> <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlKyujinNoUpd"
							value="#{pc_Ssd00301T01.ssd00301.propKyujinNoUpd.checked}"
							style="#{pc_Ssd00301T01.ssd00301.propKyujinNoUpd.style}"
							disabled="#{pc_Ssd00301T01.ssd00301.propKyujinNoUpd.disabled}"
							readonly="#{pc_Ssd00301T01.ssd00301.propKyujinNoUpd.readonly}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="lblOutputComment2"
							value="求人票礼状台帳を更新しない"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" class="table" width="800" cellspacing="0"
				cellpadding="0" style="margin-top:7px;">
				<TBODY>

					<TR>
						<TH width="83" colspan="1" class="group_label_top"><h:outputText
							styleClass="outputText" id="lblSsenNin" value="宛名ラベル"></h:outputText></TH>
						<TD class="group_item" width=""></TD>
						<TD colspan="3"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioRendo"
							layout="lineDirection"
							style="#{pc_Ssd00301T01.ssd00301.propRadioRendo.style}"
							value="#{pc_Ssd00301T01.ssd00301.propRadioRendo.value}"
							disabled="#{pc_Ssd00301T01.ssd00301.propRadioRendo.disabled}"
							readonly="#{pc_Ssd00301T01.ssd00301.propRadioRendo.readonly}">
							<f:selectItem itemValue="1" itemLabel="連動出力する" />
							<f:selectItem itemValue="0" itemLabel="連動出力しない" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH width="18" class="group_label"></TH>
						<TH nowrap class="v_a" width="123"><h:outputText
							styleClass="outputText" id="lblYubin" value="〒表示"></h:outputText></TH>
						<TD width="235"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioYubin"
							layout="lineDirection"
							style="#{pc_Ssd00301T01.ssd00301.propRadioYubin.style}"
							value="#{pc_Ssd00301T01.ssd00301.propRadioYubin.value}"
							disabled="#{pc_Ssd00301T01.ssd00301.propRadioYubin.disabled}"
							readonly="#{pc_Ssd00301T01.ssd00301.propRadioYubin.readonly}">
							<f:selectItem itemValue="1" itemLabel="あり" />
							<f:selectItem itemValue="0" itemLabel="なし" />
						</h:selectOneRadio></TD>
						<TH nowrap class="v_a" width="132"><h:outputText
							styleClass="outputText" id="lblLayout" value="レイアウト"></h:outputText></TH>
						<TD width="223"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioLayout"
							layout="lineDirection"
							style="#{pc_Ssd00301T01.ssd00301.propRadioLayout.style}"
							value="#{pc_Ssd00301T01.ssd00301.propRadioLayout.value}"
							disabled="#{pc_Ssd00301T01.ssd00301.propRadioLayout.disabled}"
							readonly="#{pc_Ssd00301T01.ssd00301.propRadioLayout.readonly}">
							<f:selectItem itemValue="1" itemLabel="A4縦" />
							<f:selectItem itemValue="2" itemLabel="A3横" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH width="18" class="group_label"></TH>
						<TH nowrap class="v_a" width="123"><h:outputText styleClass="outputText"
							id="lblBarcodeSitei" value="バーコード出力"></h:outputText></TH>
						<TD width="235"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlBarcodeSitei"
							style="#{pc_Ssd00301T01.ssd00301.propBarcodeSitei.style}"
							value="#{pc_Ssd00301T01.ssd00301.propBarcodeSitei.stringValue}">
							<f:selectItem itemValue="0" itemLabel="出力する" />
							<f:selectItem itemValue="1" itemLabel="出力しない" />
						</h:selectOneRadio></TD>
						<TH nowrap class="v_a" width="132"><h:outputText styleClass="outputText"
							id="lblKaishiIchi" value="#{pc_Ssd00301T01.ssd00301.propKaishiIchi.labelName}"></h:outputText></TH>
						<TD width="223"><h:inputText styleClass="inputText" id="htmlKaishiIchi"
							value="#{pc_Ssd00301T01.ssd00301.propKaishiIchi.integerValue}"
							style="#{pc_Ssd00301T01.ssd00301.propKaishiIchi.style}" size="2">
							<f:convertNumber type="number" pattern="##;##" />
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH width="18" class="group_label"></TH>
						<TH nowrap class="v_b" width="123"><h:outputText
							styleClass="outputText" id="lblPrinter" 
							value="#{pc_Ssd00301T01.ssd00301.propPrinter.labelName}"
							style="#{pc_Ssd00301T01.ssd00301.propPrinter.labelStyle}"></h:outputText></TH>
						<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlPrinter"
							disabled="#{pc_Ssd00301T01.ssd00301.propPrinter.disabled}"
							style="#{pc_Ssd00301T01.ssd00301.propPrinter.style}"
							value="#{pc_Ssd00301T01.ssd00301.propPrinter.value}">
							<f:selectItems value="#{pc_Ssd00301T01.ssd00301.propPrinter.list}" />
						</h:selectOneMenu></TD>
					</TR>

					<TR>
						<TH width="18" class="group_label"></TH>
						<TH nowrap class="v_c" width="123"><h:outputText
							styleClass="outputText" id="lblBiko" value="備考欄(全20)"
							style="#{pc_Ssd00301T01.ssd00301.propBiko.labelStyle}"></h:outputText></TH>
						<TD colspan="3"><h:inputText styleClass="inputText" id="htmlBiko"
							value="#{pc_Ssd00301T01.ssd00301.propBiko.stringValue}"
							style="#{pc_Ssd00301T01.ssd00301.propBiko.style}"
							disabled="#{pc_Ssd00301T01.ssd00301.propBiko.disabled}"
							readonly="#{pc_Ssd00301T01.ssd00301.propBiko.readonly}"
							maxlength="#{pc_Ssd00301T01.ssd00301.propBiko.maxLength}"
							size="45">
						</h:inputText></TD>
					</TR>

					<TR>
						<TH nowrap class="v_c" colspan="2"><h:outputText
							styleClass="outputText" id="lblSaiyoTanto" value="採用担当者氏名"></h:outputText></TH>
						<TD colspan="3"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlRadioSaiyoTanto"
							layout="lineDirection"
							value="#{pc_Ssd00301T01.ssd00301.propRadioSaiyoTanto.value}"
							style="#{pc_Ssd00301T01.ssd00301.propRadioSaiyoTanto.style}">
							<f:selectItem itemValue="1" itemLabel="担当者名称出力" />
							<f:selectItem itemValue="2" itemLabel="固定名称出力" />
						</h:selectOneRadio></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" class="table" width="800" cellspacing="0"
				cellpadding="0" style="margin-top:7px;">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="206"><h:outputText
							styleClass="outputText" id="lblGakko"
							value="#{pc_Ssd00301T01.ssd00301.propGakko.labelName}"
							style="#{pc_Ssd00301T01.ssd00301.propGakko.labelStyle}"></h:outputText></TH>
						<TD width="592"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlGakko"
							disabled="#{pc_Ssd00301T01.ssd00301.propGakko.disabled}"
							style="#{pc_Ssd00301T01.ssd00301.propGakko.style}"
							value="#{pc_Ssd00301T01.ssd00301.propGakko.value}">
							<f:selectItems value="#{pc_Ssd00301T01.ssd00301.propGakko.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="800" cellpadding="0" cellspacing="0"
				style="margin-top:20px;">
				<TBODY>
					<TR>
						<TD width="800" align="left"><hx:commandExButton value="一括指定"
							styleClass="tab_head_on" id="btnSsd00301T01"></hx:commandExButton><hx:commandExButton
							type="submit" value="企業指定" styleClass="tab_head_off"
							id="btnSsd00301T03"
							action="#{pc_Ssd00301T01.doSsd00301T03Action}"></hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="800" border="0" cellspacing="0" cellpadding="0"class="tab_body">
								<TBODY>
									<TR>
										<TD align="left">
											<DIV style="height:255px">
												<TABLE border="0" cellpadding="0" cellspacing="0" width="500" style="margin-top:20px; margin-left:40px;">
													<TBODY>
														<TR>
															<TD style="text-align:left"><h:outputText
																styleClass="outputText" id="lblGyosyuHeaderCmt"
																value="業種一覧"></h:outputText></TD>
															<TD style="text-align: right" width="8%"><h:outputFormat
																styleClass="outputFormat" id="format1" value="{0}件">
																<f:param name="normalCount"
																	value="#{pc_Ssd00301T01.propListGyosyu.listCount}"></f:param>
															</h:outputFormat></TD>
														</TR>
														<TR>
															<TD colspan="2">
																<TABLE border="0" cellpadding="0" cellspacing="0" width="500" class="list_table">
																	<TR>
																		<TD><h:selectManyListbox
																			styleClass="selectManyListbox" id="htmlListGyosyu"
																			value="#{pc_Ssd00301T01.propListGyosyu.value}" size="9"
																			style="width:100%;"
																			disabled="#{pc_Ssd00301T01.propListGyosyu.disabled}"
																			readonly="#{pc_Ssd00301T01.propListGyosyu.readonly}">
																			<f:selectItems
																				value="#{pc_Ssd00301T01.propListGyosyu.list}" />
																		</h:selectManyListbox></TD>
																	</TR>	
																</TABLE>		
															</TD>
														</TR>
													</TBODY>
												</TABLE>

												<TABLE border="0" cellpadding="0" cellspacing="1" width="500" style="margin-left:40px;">
													<TBODY>
														<TR>
															<TD style="text-align:right"><h:outputText styleClass="note"
																id="lblMultiCmt" value="（複数選択可）"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												<TABLE width="600" border="0" cellspacing="0" cellpadding="3" class="table" style="margin-top:20px; margin-left:40px;">
													<TBODY>
														<TR>
															<TH nowrap class="v_a" width="100"><h:outputText styleClass="outputText"
																id="labelSort" value="並び順指定"></h:outputText></TH>
															<TD><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlSort"
																layout="lineDirection"
																value="#{pc_Ssd00301T01.propSort.value}"
																style="#{pc_Ssd00301T01.propSort.style}"
																disabled="#{pc_Ssd00301T01.propSort.disabled}"
																readonly="#{pc_Ssd00301T01.propSort.readonly}">
																<f:selectItem itemValue="1" itemLabel="郵便番号順" />
																<f:selectItem itemValue="5" itemLabel="業種コード順" />
																<f:selectItem itemValue="2" itemLabel="地域コード順" />
																<f:selectItem itemValue="3" itemLabel="企業コード順" />
																<f:selectItem itemValue="4" itemLabel="企業名称カナ順" />
															</h:selectOneRadio></TD>
														</TR>
													</TBODY>
												</TABLE>
											</DIV>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="100%" class="button_bar">
								<TBODY>
									<TR>
										<TD><hx:commandExButton type="submit" value="PDF作成"
											styleClass="commandExButton_out" id="pdfout"
											action="#{pc_Ssd00301T01.doPdfoutAction}"
											confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton><hx:commandExButton
											type="submit" value="CSV作成"
											styleClass="commandExButton_out" id="csvout"
											action="#{pc_Ssd00301T01.doCsvoutAction}"
											confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton
											type="submit" value="出力項目指定"
											styleClass="commandExButton_out" id="setoutput"
											action="#{pc_Ssd00301T01.doSetoutputAction}"
											></hx:commandExButton><hx:commandExButton type="submit" value="印刷"
											styleClass="commandExButton_out" id="print"
											action="#{pc_Ssd00301T01.doPrintAction}"
											confirm="#{msg.SY_MSG_0022W}"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
