<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea02203T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kea02203T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<style type="text/css">
<!--
.setWidth TD {width: 100px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function callBackMethod(value){
	var key1 = value['key1'];
	var key2 = value['key2'];
	if (key1 != "error") {
		document.getElementById('form1:htmlMotoShinseiGaku').innerHTML = value['key1'];
	}
	if (key2 != "error") {
		document.getElementById('form1:htmlSakiShinseiGaku').innerHTML = value['key2'];
	}
}
window.attachEvent("onload", attachFormatNumber);
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageKE" var="msgKE"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea02203T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea02203T01.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea02203T01.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea02203T01.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returndisp"
				action="#{pc_Kea02203T01.doReturndispAction}" tabindex="16"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD height="55">

						<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
							<TBODY>
								<TR>
									<TH width="110" class="v_a"><h:outputText 
										styleClass="outputText" id="lblKaikeiNendo" 
										value="#{pc_Kea02203T01.kea02203.propKaikeiNendo.name}" 
										style="#{pc_Kea02203T01.kea02203.propKaikeiNendo.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText" 
										id="htmlKaikeiNendo" value="#{pc_Kea02203T01.kea02203.propKaikeiNendo.stringValue}" 
										style="#{pc_Kea02203T01.kea02203.propKaikeiNendo.style}"></h:outputText></TD>
									<TH class="v_b" width="110"><h:outputText 
										styleClass="outputText" id="lblShinseiNo" 
										value="#{pc_Kea02203T01.kea02203.propShinseiNo.name}" 
										style="#{pc_Kea02203T01.kea02203.propShinseiNo.labelStyle}"></h:outputText></TH>
									<TD colspan="" width="150"><h:outputText
										styleClass="outputText" id="htmlShinseiNo"
										value="#{pc_Kea02203T01.kea02203.propShinseiNo.integerValue}"
										style="#{pc_Kea02203T01.kea02203.propShinseiNo.style}">
										<f:convertNumber pattern="00000" />
									</h:outputText></TD>
									<TH class="v_c" width="110"><h:outputText 
										styleClass="outputText" id="lblShinseiUser" 
										value="#{pc_Kea02203T01.kea02203.propShinseiUser.name}" 
										style="#{pc_Kea02203T01.kea02203.propShinseiUser.labelStyle}"></h:outputText></TH>
									<TD colspan="" nowrap>
									<DIV style="width:280px;white-spce:nowrap;overflow:hidden;display:block;">
									<h:outputText styleClass="outputText" id="htmlShinseiUser"
										value="#{pc_Kea02203T01.kea02203.propShinseiUser.stringValue}"
										style="#{pc_Kea02203T01.kea02203.propShinseiUser.style}"
										title="#{pc_Kea02203T01.kea02203.propShinseiUser.stringValue}"></h:outputText></DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblShinseiDate"
										value="#{pc_Kea02203T01.kea02203.propShinseiDate.name}"
										style="#{pc_Kea02203T01.kea02203.propShinseiDate.labelStyle}"></h:outputText></TH>
									<TD colspan=""><h:outputText styleClass="outputText"
										id="htmlShinseiDate"
										value="#{pc_Kea02203T01.kea02203.propShinseiDate.dateValue}"
										style="ShinseiDate.style}">
										
									<f:convertDateTime /></h:outputText></TD>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblShinseiKbn" value="#{pc_Kea02203T01.kea02203.propShinseiKbnName.name}"
										style="#{pc_Kea02203T01.kea02203.propShinseiKbnName.labelStyle}"></h:outputText></TH>
									<TD colspan=""><h:outputText styleClass="outputText"
										id="htmlShinseiKbn"
										value="#{pc_Kea02203T01.kea02203.propShinseiKbnName.stringValue}"
										style="#{pc_Kea02203T01.kea02203.propShinseiKbnName.style}"></h:outputText></TD>
									<TH class="v_f"><h:outputText styleClass="outputText"
										id="lblShinseiJotai"
										value="#{pc_Kea02203T01.kea02203.propShinseiJotai.name}"
										style="#{pc_Kea02203T01.kea02203.propShinseiJotai.labelStyle}"></h:outputText></TH>
									<TD colspan=""><h:outputText
										styleClass="outputText" id="htmlShinseiJotai"
										value="#{pc_Kea02203T01.kea02203.propShinseiJotai.stringValue}"
										style="#{pc_Kea02203T01.kea02203.propShinseiJotai.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="372" height="28" style="border-bottom-style: none;">
										<TBODY>
											<TR>
												<TD class="tab_head_on" width="50%" style="border-left-style: none;">
													<hx:commandExButton type="button" value="申請情報"
													styleClass="tab_head_on" id="selectTab1"
													style="width:100%" tabindex="1"
													action="#{pc_Kea02203T01.doSelectTab1Action}"></hx:commandExButton>
												</TD>
												<TD class="tab_head_off" width="50%">
													<hx:commandExButton type="submit" value="変更先詳細情報"
													styleClass="tab_head_off" id="selectTab2"
													style="width:100%" tabindex="2"
													action="#{pc_Kea02203T01.doSelectTab2Action}"></hx:commandExButton>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD>
										<TABLE border="1" cellpadding="0" cellspacing="0"  width="558"  height="27" style="border-top-style: none; border-right-style: none;">
											<TBODY>
												<TR>
													<TD></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>									
								</TR>
								<TR>
									<TD colspan="2">
									<TABLE border="0" cellpadding="0" cellspacing="4" class="tab_body" 
										width="100%" height="380" style="border-top-style: none; ">
										<TBODY>
											<TR><TD height="10"></TD></TR>							
											<TR>
												<TD>

												<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="table">
													<TBODY>
														<TR>
															<TH class="group_label_top" colspan="5">
															<TABLE border="0" cellpadding="0" cellspacing="0"
																width="100%">
																<TBODY>
																	<TR>
																		<TD width="20%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none"><h:outputText
																			styleClass="outputText" id="lblhenkoMoto"
																			value="［変更元］予算編成情報"></h:outputText></TD>
																		<TD class="group_item" width="45%"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none"></TD>
																		<TD width="11%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none"><DIV align="right"><h:outputText
																			styleClass="outputText" id="htmlMotoShinseiGakuL"
																			value="#{pc_Kea02203T01.propMotoShinseiGakuL.stringValue}"
																			style="#{pc_Kea02203T01.propMotoShinseiGakuL.style}"></h:outputText></DIV></TD>
																		<TD width="14%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none"><DIV align="right"><h:outputText
																			styleClass="outputText" id="htmlMotoShinseiGaku"
																			value="#{pc_Kea02203T01.propMotoShinseiGaku.longValue}"
																			style="#{pc_Kea02203T01.propMotoShinseiGaku.style}">
																			<f:convertNumber pattern="###,###,###,##0" />
																		</h:outputText></DIV></TD>
																		<TD width="10%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none">
																		<h:outputText styleClass="outputText"></h:outputText></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TH>
														</TR>
														<TR>
															<TH class="group_label" width="16"></TH>
															<TH class="v_g" width="100"><h:outputText
																styleClass="outputText" id="lblMotoYsnTCd"
																value="#{pc_Kea02203T01.propMotoYsnTCd.name}"
																style="#{pc_Kea02203T01.propMotoYsnTCd.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="80"><h:outputText
																			styleClass="outputText" id="htmlMotoYsnTCd"
																			value="#{pc_Kea02203T01.propMotoYsnTCd.stringValue}"
																			style="#{pc_Kea02203T01.propMotoYsnTCd.style}"></h:outputText></TD>
																		<TD class="clear_border" nowrap>
																		<DIV style="width:250px;white-spce:nowrap;overflow:hidden;display:block;">
																		<h:outputText styleClass="outputText"
																			id="htmlMotoYsnTName"
																			value="#{pc_Kea02203T01.propMotoYsnTName.stringValue}"
																			style="#{pc_Kea02203T01.propMotoYsnTName.style}"
																			title="#{pc_Kea02203T01.propMotoYsnTName.stringValue}"></h:outputText></DIV>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															<TH width="100" class="v_a"><h:outputText
																styleClass="outputText" id="lblMotoMokuCd"
																value="#{pc_Kea02203T01.propMotoMokuCd.name}"
																style="#{pc_Kea02203T01.propMotoMokuCd.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0"width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="80"><h:outputText
																			styleClass="outputText" id="htmlMotoMokuCd"
																			value="#{pc_Kea02203T01.propMotoMokuCd.stringValue}"
																			style="#{pc_Kea02203T01.propMotoMokuCd.style}"></h:outputText></TD>
																		<TD class="clear_border" nowrap >
																		<DIV style="width:279px;white-spce:nowrap;overflow:hidden;display:block;">
																		<h:outputText styleClass="outputText"
																			id="htmlMotoMokuName"
																			value="#{pc_Kea02203T01.propMotoMokuName.stringValue}"
																			style="#{pc_Kea02203T01.propMotoMokuName.style}"
																			title="#{pc_Kea02203T01.propMotoMokuName.stringValue}"></h:outputText></DIV>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
														</TR>
														<TR>
															<TH class="group_label_bottom"></TH>
															<TH class="v_b"><h:outputText styleClass="outputText"
																id="lblMotoKmkCd"
																value="#{pc_Kea02203T01.propMotoKmkCd.name}"
																style="#{pc_Kea02203T01.propMotoKmkCd.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="80"><h:outputText
																			styleClass="outputText" id="htmlMotoKmkCd"
																			value="#{pc_Kea02203T01.propMotoKmkCd.stringValue}"
																			style="#{pc_Kea02203T01.propMotoKmkCd.style}"></h:outputText></TD>
																		<TD class="clear_border" nowrap width="">
																		<DIV style="width:250px;white-spce:nowrap;overflow:hidden;display:block;">
																		<h:outputText styleClass="outputText"
																			id="htmlMotoKmkName"
																			style="#{pc_Kea02203T01.propMotoKmkName.style}"
																			value="#{pc_Kea02203T01.propMotoKmkName.stringValue}"
																			title="#{pc_Kea02203T01.propMotoKmkName.stringValue}"></h:outputText></DIV>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															<TH class="v_c"><h:outputText styleClass="outputText"
																id="lblMotoKomokuNaiyo"
																value="#{pc_Kea02203T01.propMotoKomokuNaiyo.name}"
																style="#{pc_Kea02203T01.propMotoKomokuNaiyo.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0"
																width="100%">
																<TBODY>
																	<TR>
																	<TD  class="clear_border" nowrap>
																	<DIV
																		style="width:359px;white-spce:nowrap;overflow:hidden;display:block;"><h:outputText
																		styleClass="outputText" id="htmlMotoKomokuNaiyo"
																		value="#{pc_Kea02203T01.propMotoKomokuNaiyo.stringValue}"
																		style="#{pc_Kea02203T01.propMotoKomokuNaiyo.style}"
																		title="#{pc_Kea02203T01.propMotoKomokuNaiyo.stringValue}"></h:outputText></DIV>
																	</TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															
														</TR>
														<TR>
															<TH class="group_label_top" colspan="5">
															<TABLE border="0" cellpadding="0" cellspacing="0"
																width="100%">
																<TBODY>
																	<TR>
																		<TD width="20%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none">
																			<h:outputText styleClass="outputText" id="lblhenkoSaki"
																			value="［変更先］予算編成情報"></h:outputText></TD>
																		<TD class="group_item" width="45%"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none"></TD>
																		<TD width="11%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none">
																			<DIV align="right"><h:outputText styleClass="outputText" id="htmlSakiShinseiGakuL"
																			value="#{pc_Kea02203T01.propSakiShinseiGakuL.stringValue}"
																			style="#{pc_Kea02203T01.propSakiShinseiGakuL.style}"></h:outputText></DIV></TD>
																		<TD width="14%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none;border-right-style:none">
																		<DIV align="right"><h:outputText
																			styleClass="outputText" id="htmlSakiShinseiGaku"
																			value="#{pc_Kea02203T01.propSakiShinseiGaku.longValue}"
																			style="#{pc_Kea02203T01.propSakiShinseiGaku.style}">
																			<f:convertNumber pattern="###,###,###,##0" />
																		</h:outputText></DIV>
																		</TD>
																		<TD width="10%" class="group_item"
																			style="border-top-style:none;border-bottom-style:none"><h:outputText
																			styleClass="outputText"></h:outputText></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TH>
														</TR>
														<TR>
															<TH class="group_label"></TH>
															<TH class="v_d"><h:outputText
																styleClass="outputText" id="lblSakiYsnTCd"
																value="#{pc_Kea02203T01.propSakiYsnTCd.name}"
																style="#{pc_Kea02203T01.propSakiYsnTCd.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="80"><h:outputText
																			styleClass="outputText" id="htmlSakiYsnTCd"
																			value="#{pc_Kea02203T01.propSakiYsnTCd.stringValue}"
																			style="#{pc_Kea02203T01.propSakiYsnTCd.style}"></h:outputText></TD>
																		<TD class="clear_border" nowrap><DIV style="width:250px;white-spce:nowrap;overflow:hidden;display:block;">
																		<h:outputText styleClass="outputText"
																			id="htmlSakiYsnTName"
																			value="#{pc_Kea02203T01.propSakiYsnTName.stringValue}"
																			style="#{pc_Kea02203T01.propSakiYsnTName.style}"
																			title="#{pc_Kea02203T01.propSakiYsnTName.stringValue}"></h:outputText></DIV></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															<TH class="v_e"><h:outputText
																styleClass="outputText" id="lblSakiMokuCd"
																value="#{pc_Kea02203T01.propSakiMokuCd.name}"
																style="#{pc_Kea02203T01.propSakiMokuCd.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="80"><h:outputText
																			styleClass="outputText" id="htmlSakiMokuCd"
																			value="#{pc_Kea02203T01.propSakiMokuCd.stringValue}"
																			style="#{pc_Kea02203T01.propSakiMokuCd.style}"></h:outputText></TD>
																		<TD class="clear_border" nowrap><DIV style="width:279px;white-spce:nowrap;overflow:hidden;display:block;">
																		<h:outputText styleClass="outputText"
																			id="htmlSakiMokuName"
																			value="#{pc_Kea02203T01.propSakiMokuName.stringValue}"
																			style="#{pc_Kea02203T01.propSakiMokuName.style}"
																			title="#{pc_Kea02203T01.propSakiMokuName.stringValue}"></h:outputText></DIV></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>

														</TR>
														<TR>
															<TH class="group_label_bottom"></TH>
															<TH class="v_f"><h:outputText styleClass="outputText"
																id="lblSakiKmkCd"
																value="#{pc_Kea02203T01.propSakiKmkCd.name}"
																style="#{pc_Kea02203T01.propSakiKmkCd.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="80"><h:outputText
																			styleClass="outputText" id="htmlSakiKmkCd"
																			value="#{pc_Kea02203T01.propSakiKmkCd.stringValue}"
																			style="#{pc_Kea02203T01.propSakiKmkCd.style}"></h:outputText></TD>
																		<TD class="clear_border" nowrap><DIV style="width:250px;white-spce:nowrap;overflow:hidden;display:block;">
																		<h:outputText styleClass="outputText"
																			id="htmlSakiKmkName"
																			value="#{pc_Kea02203T01.propSakiKmkName.stringValue}"
																			style="#{pc_Kea02203T01.propSakiKmkName.style}"
																			title="#{pc_Kea02203T01.propSakiKmkName.stringValue}"></h:outputText></DIV></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															<TH class="v_g"><h:outputText styleClass="outputText"
																id="lblSakiKomokuNaiyo"
																value="#{pc_Kea02203T01.propSakiKomokuNaiyo.name}"
																style="#{pc_Kea02203T01.propSakiKomokuNaiyo.labelStyle}"></h:outputText></TH>
															<TD>
															<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																<TBODY>
																	<TR>
																	<TD  class="clear_border" nowrap>
																	<DIV
																		style="width:359px;white-spce:nowrap;overflow:hidden;display:block;">
																		<h:outputText styleClass="outputText"
																		id="htmlSakiKomokuNaiyo"
																		value="#{pc_Kea02203T01.propSakiKomokuNaiyo.stringValue}"
																		style="#{pc_Kea02203T01.propSakiKomokuNaiyo.style}"
																		title="#{pc_Kea02203T01.propSakiKomokuNaiyo.stringValue}"></h:outputText></DIV>
																	</TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
															
															
															
														</TR>
														<TR>
															<TH class="v_a" colspan="2"><h:outputText
																styleClass="outputText" id="htmlShinseiGakuL"
																value="#{pc_Kea02203T01.propShinseiGakuL.stringValue}"
																style="#{pc_Kea02203T01.propShinseiGakuL.style}"></h:outputText></TH>
															<TD colspan="3">
															<TABLE border="0" cellpadding="0" cellspacing="0"
																width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="120">
																		<DIV align="right"><h:outputText
																			styleClass="outputText" id="htmlShinseiGaku"
																			value="#{pc_Kea02203T01.propShinseiGaku.longValue}"
																			style="#{pc_Kea02203T01.propShinseiGaku.style}">
																			<f:convertNumber pattern="###,###,###,##0" />
																		</h:outputText></DIV>
																		</TD>
																		<TD class="clear_border" width="12"></TD>
																		<TD class="clear_border"><h:outputText
																			styleClass="outputText" id="htmlShinseiGakuTani"
																			value="#{pc_Kea02203T01.propShinseiGakuTani.stringValue}"
																			style="#{pc_Kea02203T01.propShinseiGakuTani.style}"></h:outputText></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
														</TR>
														<TR>
															<TH class="v_b" colspan="2" height=""><h:outputText
																styleClass="outputText" id="lblShinseiRiyu"
																value="#{pc_Kea02203T01.propShinseiRiyu.name}"
																style="#{pc_Kea02203T01.propShinseiRiyu.labelStyle}"></h:outputText></TH>
															<TD colspan="3">
																	<h:inputTextarea styleClass="inputTextarea"
																id="htmlShinseiRiyu"
																value="#{pc_Kea02203T01.propShinseiRiyu.stringValue}"
																style="#{pc_Kea02203T01.propShinseiRiyu.style}"
																disabled="#{pc_Kea02203T01.propShinseiRiyu.disabled}"
																cols="85" rows="2" readonly="true"></h:inputTextarea>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR><TD height="10"></TD></TR>
											<TR>
												<TD>
												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="table" width="100%">
													<TBODY>
														<TR>
															<TH class="v_c" width="120"><h:outputText
																styleClass="outputText" id="htmlShoninGakuL"
																value="#{pc_Kea02203T01.propShoninGakuL.stringValue}"
																style="#{pc_Kea02203T01.propShoninGaku.labelStyle}"></h:outputText></TH>
															<TD colspan="3">
															<TABLE border="0" cellpadding="0" cellspacing="0"
																width="100%">
																<TBODY>
																	<TR>
																		<TD class="clear_border" width="120"><h:inputText
																			styleClass="inputText" id="htmlShoninGaku" size="15"
																			disabled="#{pc_Kea02203T01.propShoninGaku.disabled}"
																			readonly="#{pc_Kea02203T01.propShoninGaku.readonly}"
																			style="#{pc_Kea02203T01.propShoninGaku.style} ;padding-left: 0px; padding-right: 3px; text-align: right;"
																			value="#{pc_Kea02203T01.propShoninGaku.stringValue}"
																			tabindex="3">
																			<hx:inputHelperAssist errorClass="inputText_Error" />
																		</h:inputText></TD>
																		<TD class="clear_border" width="5"></TD>
																		<TD class="clear_border" width="120"><h:outputText
																			styleClass="outputText" id="htmlShoninGakuTani"
																			value="#{pc_Kea02203T01.propShoninGakuTani.stringValue}"
																			style="#{pc_Kea02203T01.propShoninGakuTani.style}"></h:outputText></TD>
																		<TD style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none">
																			<hx:commandExButton
																				type="submit" value="申請額計算"
																				styleClass="commandExButton" id="keisan"
																				tabindex="4" style="width:80px"
																				action="#{pc_Kea02203T01.doKeisanAction}"
																				disabled="#{pc_Kea02203T01.kea02203.propButtonKeisan.disabled}"></hx:commandExButton></TD>
																	</TR>
																</TBODY>
															</TABLE>
															</TD>
														</TR>
														<TR>
															<TH class="v_d" rowspan="3"><h:outputText
																styleClass="outputText" id="lblShoninTaisho"
																value="承認対象"></h:outputText></TH>
															<TD width="250"><h:selectManyCheckbox
																disabledClass="selectManyCheckbox_Disabled"
																styleClass="selectManyCheckbox setWidth" 
																id="htmlBmnSateiFlg"
																disabled="#{pc_Kea02203T01.propBmnSateiFlg.disabled}"
																readonly="#{pc_Kea02203T01.propBmnSateiFlg.readonly}"
																value="#{pc_Kea02203T01.propBmnSateiFlg.value}"
																tabindex="5">
																<f:selectItem itemValue="0" itemLabel="部門査定中" />
																<f:selectItem itemValue="1" itemLabel="部門査定確定" />
															</h:selectManyCheckbox></TD>

															<TH class="v_e" width="130"><h:outputText
																styleClass="outputText" id="lblBmnSateiRiyu"
																value="#{pc_Kea02203T01.propBmnSateiRiyu.name}"
																style="#{pc_Kea02203T01.propBmnSateiRiyu.labelStyle}"></h:outputText></TH>
															<TD colspan=""><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlBmnSateiRiyu"
																style="width:350px"
																disabled="#{pc_Kea02203T01.propBmnSateiRiyu.disabled}"
																readonly="#{pc_Kea02203T01.propBmnSateiRiyu.readonly}"
																value="#{pc_Kea02203T01.propBmnSateiRiyu.value}"
																tabindex="6">
																<f:selectItems
																	value="#{pc_Kea02203T01.propBmnSateiRiyu.list}" />
															</h:selectOneMenu></TD>
														</TR>
														<TR>
															<TD colspan=""><h:selectManyCheckbox
																disabledClass="selectManyCheckbox_Disabled"
																styleClass="selectManyCheckbox" id="htmlKeiriSateiFlg"
																disabled="#{pc_Kea02203T01.propKeiriSateiFlg.disabled}"
																readonly="#{pc_Kea02203T01.propKeiriSateiFlg.readonly}"
																value="#{pc_Kea02203T01.propKeiriSateiFlg.value}"
																tabindex="7">
																<f:selectItem itemValue="0" itemLabel="経理査定済" />
															</h:selectManyCheckbox></TD>
															<TH class="v_f"><h:outputText styleClass="outputText"
																id="lblKeiriSateiRiyu"
																value="#{pc_Kea02203T01.propKeiriSateiRiyu.name}"
																style="#{pc_Kea02203T01.propKeiriSateiRiyu.labelStyle}"></h:outputText></TH>
															<TD colspan=""><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlKeiriSateiRiyu"
																style="width:350px"
																disabled="#{pc_Kea02203T01.propKeiriSateiRiyu.disabled}"
																readonly="#{pc_Kea02203T01.propKeiriSateiRiyu.readonly}"
																value="#{pc_Kea02203T01.propKeiriSateiRiyu.value}"
																tabindex="8">
																<f:selectItems
																	value="#{pc_Kea02203T01.propKeiriSateiRiyu.list}" />
															</h:selectOneMenu></TD>
														</TR>
														<TR>
															<TD colspan=""><h:selectManyCheckbox
																disabledClass="selectManyCheckbox_Disabled"
																styleClass="selectManyCheckbox" id="htmlKeiriKetteiFlg"
																disabled="#{pc_Kea02203T01.propKeiriKetteiFlg.disabled}"
																readonly="#{pc_Kea02203T01.propKeiriKetteiFlg.readonly}"
																value="#{pc_Kea02203T01.propKeiriKetteiFlg.value}"
																tabindex="9">
																<f:selectItem itemValue="0" itemLabel="経理決定済" />
															</h:selectManyCheckbox></TD>
															<TH class="v_g"><h:outputText styleClass="outputText"
																id="lblKeiriKetteiRiyu"
																value="#{pc_Kea02203T01.propKeiriKetteiRiyu.name}"
																style="#{pc_Kea02203T01.propKeiriKetteiRiyu.labelStyle}"></h:outputText></TH>
															<TD colspan=""><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlKeiriKetteiRiyu"
																style="width:350px"
																disabled="#{pc_Kea02203T01.propKeiriKetteiRiyu.disabled}"
																readonly="#{pc_Kea02203T01.propKeiriKetteiRiyu.readonly}"
																value="#{pc_Kea02203T01.propKeiriKetteiRiyu.value}"
																tabindex="10">
																<f:selectItems
																	value="#{pc_Kea02203T01.propKeiriKetteiRiyu.list}" />
															</h:selectOneMenu></TD>
														</TR>
														<TR>
															<TH class="v_a"><h:outputText styleClass="outputText"
																id="lblShoninRiyu"
																value="#{pc_Kea02203T01.propShoninRiyu.labelName}"
																style="#{pc_Kea02203T01.propShoninRiyu.labelStyle}"></h:outputText>
																<BR></TH>
															<TD colspan="3"><h:inputTextarea
																styleClass="inputTextarea" id="htmlShoninRiyu" rows="2"
																cols="85"
																disabled="#{pc_Kea02203T01.propShoninRiyu.disabled}"
																readonly="#{pc_Kea02203T01.propShoninRiyu.readonly}"
																style="#{pc_Kea02203T01.propShoninRiyu.style}"
																value="#{pc_Kea02203T01.propShoninRiyu.stringValue}"
																tabindex="11"></h:inputTextarea></TD>
														</TR>
													</TBODY>
												</TABLE></TD>
											</TR>
											<TR><TD height="8"></TD></TR>											
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="承認"
										styleClass="commandExButton_dat" id="shonin" style="width:80px"
										tabindex="12" action="#{pc_Kea02203T01.doShoninAction}"
										disabled="#{pc_Kea02203T01.kea02203.propButtonShonin.disabled}" confirm="#{msgKE.KE_MSG_0021W}"></hx:commandExButton> 
									<hx:commandExButton type="submit" value="否認"
										styleClass="commandExButton_dat" id="hinin" style="width:80px"
										tabindex="13" action="#{pc_Kea02203T01.doHininAction}"
										disabled="#{pc_Kea02203T01.kea02203.propButtonHinin.disabled}" confirm="#{msgKE.KE_MSG_0022W}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="前へ"
										styleClass="commandExButton_etc" id="prev" style="width:80px"
										tabindex="14" action="#{pc_Kea02203T01.doPrevAction}"
										disabled="#{pc_Kea02203T01.kea02203.propButtonPrev.disabled}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="次へ"
										styleClass="commandExButton_etc" id="next" style="width:80px"
										tabindex="15" action="#{pc_Kea02203T01.doNextAction}"
										disabled="#{pc_Kea02203T01.kea02203.propButtonNext.disabled}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Kea02203T01.propMotoGenShinseiGakuHidden.longValue}"
				id="htmlMotoGenShinseiGakuHidden">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea02203T01.propSakiGenShinseiGakuHidden.longValue}"
				id="htmlSakiGenShinseiGakuHidden">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea02203T01.kea02203.propTani.stringValue}"
				id="htmlTani"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea02203T01.kea02203.propShinseiKbn.stringValue}"
				id="htmlShinseiKbnHidden"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea02203T01.propFormatNumberOption.stringValue}"
				id="htmlFormatNumberOption"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />								
</f:view>

</HTML>

