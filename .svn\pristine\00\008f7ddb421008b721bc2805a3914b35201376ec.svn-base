<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsz02001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsz02001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Nsz02001.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Nsz02001.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Nsz02001.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Nsz02001.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->

			</DIV>
			<DIV id="content">

			<DIV class="column"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 --><BR>

			<DIV>

			<TABLE width="">
				<TBODY>
					<%-- //@@@@(F-BL-0008-00)JAST se-tanaka 2006/6/22 START --%>
					<TR>
						<TD width="10%"></TD>
						<TD width="80%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="150" height="14"><h:outputText
										styleClass="outputText" id="lblNyushiNendo"
										value="#{pc_Nsz02001.propNyushiNendo.labelName}"
										style="#{pc_Nsz02001.propNyushiNendo.labelStyle}"></h:outputText></TH>
									<TD height="14" width="150"><h:inputText styleClass="inputText"
										id="htmlNyushiNendo" size="4"
										disabled="#{pc_Nsz02001.propNyushiNendo.disabled}"
										readonly="#{pc_Nsz02001.propNyushiNendo.readonly}"
										value="#{pc_Nsz02001.propNyushiNendo.dateValue}"
										style="#{pc_Nsz02001.propNyushiNendo.style}">

										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />

										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH class="v_b" height="14" width="150"><h:outputText
										styleClass="outputText" id="lblNyushiGakkiNo"
										value="#{pc_Nsz02001.propNyushiGakkiNo.labelName}"
										style="#{pc_Nsz02001.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
									<TD height="14" width="150"><h:inputText styleClass="inputText"
										id="htmlNyushiGakkiNo" size="2"
										disabled="#{pc_Nsz02001.propNyushiGakkiNo.disabled}"
										readonly="#{pc_Nsz02001.propNyushiGakkiNo.readonly}"
										maxlength="#{pc_Nsz02001.propNyushiGakkiNo.maxLength}"
										style="#{pc_Nsz02001.propNyushiGakkiNo.style}"
										value="#{pc_Nsz02001.propNyushiGakkiNo.integerValue}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<f:convertNumber pattern="#0" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<%-- //@@@@(F-BL-0008-00)JAST se-tanaka 2006/6/22 END --%>

					<TR>
						<TD width="10%"></TD>
						<TD width="80%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_c"><h:outputText
										value="#{pc_Nsz02001.propFreKomokName.labelName}"
										id="lblFreKomokName"
										style="#{pc_Nsz02001.propFreKomokName.labelStyle}"
										styleClass="outputText"></h:outputText></TH>
									<TD width="454"><h:inputText styleClass="inputText"
										id="htmlFreKomokName" size="65"
										value="#{pc_Nsz02001.propFreKomokName.stringValue}"
										style="#{pc_Nsz02001.propFreKomokName.style}"
										maxlength="#{pc_Nsz02001.propFreKomokName.maxLength}">
										<hx:inputHelperAssist errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="10"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfout"
							confirm="#{msg.SY_MSG_0019W}"
							action="#{pc_Nsz02001.doPdfoutAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="CSV作成" styleClass="commandExButton_out"
							id="csvout" confirm="#{msg.SY_MSG_0020W}"
							action="#{pc_Nsz02001.doCsvoutAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="出力項目指定" styleClass="commandExButton_out"
							id="setoutput" action="#{pc_Nsz02001.doSetoutputAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>


			<BR>
			</DIV>

			<!--↑content↑--></DIV>
			</DIV>
			<!--↑outer↑--> <!-- フッダーインクルード --> <jsp:include
				page="../inc/footer.jsp" /></DIV>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

