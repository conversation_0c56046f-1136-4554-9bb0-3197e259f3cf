<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc00501.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc00501.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc00501.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc00501.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>

						<TABLE border="0" cellpadding="0" cellspacing="0" width="80%">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%">
										<TBODY>
											<TR>
												<TH class="v_a" width="150"><h:outputText
													styleClass="outputText" id="lblNyushiNendo"
													value="#{pc_Nsc00501.propNyushiNendo.labelName}"
													style="#{pc_Nsc00501.propNyushiNendo.labelStyle}"></h:outputText></TH>
												<TD width="120"><h:inputText styleClass="inputText"
													id="htmlNyushiNendo" size="4" tabindex="1"
													value="#{pc_Nsc00501.propNyushiNendo.dateValue}"
													disabled="#{pc_Nsc00501.propNyushiNendo.disabled}"
													readonly="#{pc_Nsc00501.propNyushiNendo.readonly}"
													style="#{pc_Nsc00501.propNyushiNendo.style}">
													<f:convertDateTime pattern="yyyy" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TH width="150" class="v_b"><h:outputText
													styleClass="outputText" id="lblNyushiGakkiNo"
													value="#{pc_Nsc00501.propNyushiGakkiNo.labelName}"
													style="#{pc_Nsc00501.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
												<TD><h:inputText styleClass="inputText"
													id="htmlNyushiGakkiNo" size="2" tabindex="2"
													disabled="#{pc_Nsc00501.propNyushiGakkiNo.disabled}"
													maxlength="#{pc_Nsc00501.propNyushiGakkiNo.maxLength}"
													readonly="#{pc_Nsc00501.propNyushiGakkiNo.readonly}"
													style="#{pc_Nsc00501.propNyushiGakkiNo.style}"
													value="#{pc_Nsc00501.propNyushiGakkiNo.integerValue}">
													<f:convertNumber pattern="#0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="20"></TD>
									<TD width="60"></TD>
									<TD width="60"></TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%">
										<TBODY>
											<TR>
												<TH class="v_a" width="150" style="border-top:none;"><h:outputText
                                              		styleClass="outputText" id="lblNysSbtBunrui"
                                             		value="#{pc_Nsc00501.propNysSbtBunrui.labelName}"
                                              		style="#{pc_Nsc00501.propNysSbtBunrui.labelStyle}"></h:outputText></TH>
                                      			<TD colspan="3" style="border-top:none;"><h:selectOneMenu styleClass="selectOneMenu"
                                              		id="htmlNysSbtBunrui" tabindex="3"
                                              		disabled="#{pc_Nsc00501.propNysSbtBunrui.disabled}"
                                              		readonly="#{pc_Nsc00501.propNysSbtBunrui.readonly}"
                                              		value="#{pc_Nsc00501.propNysSbtBunrui.stringValue}">
                                              		<f:selectItems value="#{pc_Nsc00501.propNysSbtBunrui.list}" />
                                              		</h:selectOneMenu></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="20"></TD>
									<TD width="60"><hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select"
										action="#{pc_Nsc00501.doSelectAction}"
										disabled="#{pc_Nsc00501.propSelect.disabled}" tabindex="4"></hx:commandExButton></TD>
									<TD width="60"><hx:commandExButton type="submit" value="解除"
										styleClass="commandExButton" id="reset"
										action="#{pc_Nsc00501.doResetAction}"
										disabled="#{pc_Nsc00501.propReset.disabled}" tabindex="5"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style=""
							class="table" width="80%">
							<TBODY>
								<TR>
									<TH style="" class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblNysSbt"
										value="#{pc_Nsc00501.propNysSbt.labelName}"
										style="#{pc_Nsc00501.propNysSbt.labelStyle}"></h:outputText><BR>
									<h:outputText styleClass="outputText" id="lblFukususentakuN"
										value="(複数選択可)"></h:outputText></TH>
									<TD colspan="" class=""><h:selectManyListbox
										styleClass="selectManyListbox" id="htmlNysSbt"
										style="width: 95%" size="7"
										value="#{pc_Nsc00501.propNysSbt.value}"
										disabled="#{pc_Nsc00501.propNysSbt.disabled}"
										readonly="#{pc_Nsc00501.propNysSbt.readonly}" tabindex="6">
										<f:selectItems value="#{pc_Nsc00501.propNysSbt.list}" />
									</h:selectManyListbox></TD>
								</TR>
								<TR>
									<TH class="v_d" width="150"><h:outputText
										styleClass="outputText" id="lblGakka"
										value="#{pc_Nsc00501.propGakka.labelName}"
										style="#{pc_Nsc00501.propGakka.labelStyle}"></h:outputText><BR>
									<h:outputText styleClass="outputText" id="lblFukususentakuG"
										value="(複数選択可)"></h:outputText></TH>
									<TD colspan="" class=""><h:selectManyListbox
										styleClass="selectManyListbox" id="htmlGakka"
										style="width: 95%" size="7"
										value="#{pc_Nsc00501.propGakka.value}"
										disabled="#{pc_Nsc00501.propGakka.disabled}"
										readonly="#{pc_Nsc00501.propGakka.readonly}" tabindex="7">
										<f:selectItems value="#{pc_Nsc00501.propGakka.list}" />
									</h:selectManyListbox></TD>
								</TR>
								<TR>
									<TH class="v_e" width="150"><h:outputText
										styleClass="outputText" id="lblSetOutputL"
										value="#{pc_Nsc00501.propSetOutput.labelName}"
										style="#{pc_Nsc00501.propSetOutput.labelStyle}"></h:outputText></TH>
									<TD class=""><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSetOutput"
										value="#{pc_Nsc00501.propSetOutput.checked}"
										disabled="#{pc_Nsc00501.propSetOutput.disabled}"
										readonly="#{pc_Nsc00501.propSetOutput.readonly}"
										style="#{pc_Nsc00501.propSetOutput.style}" tabindex="8"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblSetOutput" value="受験番号未付番者のみ出力"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_f" width="150"><h:outputText
										styleClass="outputText" id="lblSetCsvOutput"
										value="#{pc_Nsc00501.propSetCsvOutput.labelName}"></h:outputText></TH>
									<TD class="">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
										<TBODY>
											<TR>
												<TD colspan="2" rowspan="2" class="clear_border" width="175"><h:selectManyCheckbox
													disabledClass="selectManyCheckbox_Disabled"
													styleClass="selectManyCheckbox" id="htmlSetCsvOutput"
													value="#{pc_Nsc00501.propSetCsvOutput.value}"
													disabled="#{pc_Nsc00501.propSetCsvOutput.disabled}"
													readonly="#{pc_Nsc00501.propSetCsvOutput.readonly}"
													style="#{pc_Nsc00501.propSetCsvOutput.style}"
													layout="pageDirection" tabindex="9">
													<f:selectItem itemValue="0" itemLabel="志願者受験準備情報" />
													<f:selectItem itemValue="1" itemLabel="志願者一括登録用" />
												</h:selectManyCheckbox></TD><TD colspan="2" class="clear_border" width="416"><hx:commandExButton
													type="submit" value="出力項目指定" styleClass="commandExButton_out"
													id="setoutputjnb"
													disabled="#{pc_Nsc00501.propSetOutputButton1.disabled}"
													tabindex="10" action="#{pc_Nsc00501.doSetOutPutJnbAction}"></hx:commandExButton></TD></TR>
											<TR><TD colspan="2" class="clear_border" width="416"><hx:commandExButton
													type="submit" value="出力項目指定" styleClass="commandExButton_out"
													id="setoutputikk" action="#{pc_Nsc00501.doSetOutPutIkkAction}"
													disabled="#{pc_Nsc00501.propSetOutputButton2.disabled}" tabindex="11"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style=""
							class="table" width="80%">
							<TBODY>
								<TR>
									<TH style="" class="v_g" width="150"><h:outputText
										styleClass="outputText" id="lblTitle"
										value="#{pc_Nsc00501.propTitle.labelName}"
										style="#{pc_Nsc00501.propTitle.labelStyle}"></h:outputText></TH>
									<TD colspan="3" class="v_c"><h:inputText styleClass="inputText"
										id="htmlTitle" size="68"
										value="#{pc_Nsc00501.propTitle.stringValue}"
										disabled="#{pc_Nsc00501.propTitle.disabled}"
										maxlength="#{pc_Nsc00501.propTitle.maxLength}"
										readonly="#{pc_Nsc00501.propTitle.readonly}"
										style="#{pc_Nsc00501.propTitle.style}" tabindex="12"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD>
										<hx:commandExButton
											type="submit" value="PDF作成"
											styleClass="commandExButton_out" id="pdfout"
											action="#{pc_Nsc00501.doPdfoutAction}"
											disabled="#{pc_Nsc00501.propPdfOut.disabled}"
											confirm="#{msg.SY_MSG_0019W}" tabindex="13">
										</hx:commandExButton>
										<hx:commandExButton
											type="submit" value="EXCEL作成"
											styleClass="commandExButton_out" id="excelout"
											action="#{pc_Nsc00501.doExceloutAction}"
											disabled="#{pc_Nsc00501.propExcelOut.disabled}"
											confirm="#{msg.SY_MSG_0027W}" tabindex="14">
										</hx:commandExButton>
										<hx:commandExButton
											type="submit" value="CSV作成"
											styleClass="commandExButton_out" id="csvout"
											action="#{pc_Nsc00501.doCsvoutAction}"
											disabled="#{pc_Nsc00501.propCsvOut.disabled}"
											confirm="#{msg.SY_MSG_0020W}" tabindex="15">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

