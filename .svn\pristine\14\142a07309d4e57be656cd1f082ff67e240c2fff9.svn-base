<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/PCog1901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.system.co.util.UtilCogFormatObject" %>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pCog1901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}
function setFocusDateFindType() {
     var target = document.getElementsByName("form1:htmlDateFindType");
     for (var i = 0;target.length > i;i++){
          if (target[i].nodeName.toUpperCase() == 'INPUT'){
               target[i].focus();
               break;
          }
     }
     return true;
}
function func_1(thisObj, thisEvent) {
	try {
		var SEPC = "<%= UtilCogFormatObject.JOIN_STR %>";
		var len = 0;
		var pos = 0;
		var pos2 = 0;
		var strx = "";
		var select = 0;
		var option = null;
		var set = (thisObj.id == "form1:htmlBankKozaOneList")?true:false;
		var noSelectMsg = document.getElementById("form1:htmlNoSelectMsgHidden").value;

		//マスク項目
		var bnkCdId = document.getElementById("form1:prmBnkCdId").value;
		var stnCdId = document.getElementById("form1:prmStnCdId").value;
		var yokinSmkCdId = document.getElementById("form1:prmYokinSmkCdId").value;
		var kozaNoId = document.getElementById("form1:prmKozaNoId").value;
		var kozaNameKanaId = document.getElementById("form1:prmKozaNameKanaId").value;

		//マスク無し項目
		var trueBnkCdId = document.getElementById("form1:prmTrueBnkCdId").value;
		var trueStnCdId = document.getElementById("form1:prmTrueStnCdId").value;
		var trueKozaNoId = document.getElementById("form1:prmTrueKozaNoId").value;
		var trueKozaNameKanaId = document.getElementById("form1:prmTrueKozaNameKanaId").value;

		var bnkNameId = document.getElementById("form1:prmBnkNameId").value;
		var stnNameId = document.getElementById("form1:prmStnNameId").value;
		var focusId = document.getElementById("form1:prmFocusId").value;
		var listBox =document.getElementById("form1:htmlBankKozaOneList");

		//経理部門ユーザかどうかのフラグ
		var keiriBmnFlg = document.getElementById("form1:htmlKeiriBmnFlgHidden").value;
		//デフォルト設定がマスクするかどうかのフラグ
		var maskFlg = document.getElementById("form1:htmlMaskFlgHidden").value;

//有効／無効の判定結果表示用
		var mukouStr ="";
//振込先銀行コード
		var bnkCd = "";
//振込先銀行名称
		var bnkName = "";
//振込先支店コード
		var stnCd = "";
//振込先支店名称
		var stnName = "";
//振込先預金種目コード
		var yokinSmkCd = "";
//振込先預金種目名称
		var yokinSmkName
//振込先口座番号
		var kozaNo = "";
//振込先口座名義人カナ
		var kozaNameKana = "";
//振込先銀行コード（マスク無し）
		var trueBnkCd = "";
//振込先支店コード（マスク無し）
		var trueStnCd = "";
//振込先口座番号（マスク無し）
		var trueKozaNo = "";
//振込先口座名義人カナ（マスク無し）
		var trueKozaNameKana = "";
//振込区分コード
		var furikomiKbnCd = "";

//振込区分名
		var furikomiKbnName = "";
		if (window.opener && listBox != null) {
			for (i = 1; i < listBox.options.length; i++) {
				option = listBox.options[ i ];
				if (option.selected) {
					flapWindow(window);
					values = option.value.split(SEPC);
					for (j = 0; j < values.length; j++) {
						switch (j) {
							case 0:			//銀行名称
								bnkName = values[ j ];
								break;

							case 1:			//支店名称
								stnName = values[ j ];
								break;

							case 2:			//預金種目名
								yokinSmkName = values[ j ];
								break;

							case 3:			//口座番号
								kozaNo = values[ j ];
								break;

							case 4:			//振込区分名
								furikomiKbnName = values[ j ];
								break;

							case 5:			//銀行コード
								bnkCd = values[ j ];
								break;

							case 6:			//支店コード
								stnCd = values[ j ];
								break;

							case 7:			//預金種目コード
								yokinSmkCd = values[ j ];
								break;

							case 8:			//振込区分コード
								furikomiKbnCd = values[ j ];
								break;

							case 9:			//口座名義人カナ
								kozaNameKana = values[ j ];
								break;
								
							case 10:		//銀行コード（マスク無し）
								trueBnkCd = values[ j ];
								break;
							
							case 11:		//支店コード（マスク無し）
								trueStnCd = values[ j ];
								break;
									
							case 12:		//口座番号（マスク無し）
								trueKozaNo = values[ j ];
								break;
								
							case 13:		//口座名義人カナ（マスク無し）
								trueKozaNameKana = values[ j ];
								break;

						}
					}
					var bnkCdId2 = getElementByIdEx(window.opener.document, bnkCdId);
					var stnCdId2 = getElementByIdEx(window.opener.document, stnCdId);
					var yokinSmkCdId2 = getElementByIdEx(window.opener.document, yokinSmkCdId);
					var kozaNoId2 = getElementByIdEx(window.opener.document, kozaNoId);
					var kozaNameKanaId2 = getElementByIdEx(window.opener.document, kozaNameKanaId);
					var bnkNameId2 = getElementByIdEx(window.opener.document, bnkNameId);
					var stnNameId2 = getElementByIdEx(window.opener.document, stnNameId);
					//マスク無し項目
					var trueBnkCdObj = getElementByIdEx(window.opener.document, trueBnkCdId);
					var trueStnCdObj = getElementByIdEx(window.opener.document, trueStnCdId);
					var trueKozaNoObj = getElementByIdEx(window.opener.document, trueKozaNoId);
					var trueKozaNameKanaObj = getElementByIdEx(window.opener.document, trueKozaNameKanaId);

					if (keiriBmnFlg == 1) {
						if (maskFlg == 1) {
							//経理部門ユーザでマスク無しの場合、正常値を返す
							setValue(bnkCdId2, bnkCd);
							riseEvent(bnkCdId2);
							setValue(stnCdId2, stnCd);
							riseEvent(stnCdId2);
							setValue(yokinSmkCdId2 , yokinSmkCd);
							riseEvent(yokinSmkCdId2);
							setValue(kozaNoId2 , kozaNo);
							riseEvent(kozaNoId2);
							setValue(kozaNameKanaId2, kozaNameKana);
							riseEvent(kozaNameKanaId2);
							setValue(bnkNameId2 , bnkName);
							riseEvent(bnkNameId2);
							setValue(stnNameId2 , stnName);
							riseEvent(stnNameId2);
							//マスク無し項目						
							setValue(trueBnkCdObj, trueBnkCd);
							riseEvent(trueBnkCdObj);
							setValue(trueStnCdObj, trueStnCd);
							riseEvent(trueStnCdObj);
							setValue(trueKozaNoObj , trueKozaNo);
							riseEvent(trueKozaNoObj);
							setValue(trueKozaNameKanaObj, trueKozaNameKana);
							riseEvent(trueKozaNameKanaObj);
						} else {
							//経理部門ユーザでマスク有りの場合、マスク値と正常値を返す
							setValue(bnkCdId2, bnkCd);
							riseEvent(bnkCdId2);
							setValue(stnCdId2, stnCd);
							riseEvent(stnCdId2);
							setValue(yokinSmkCdId2 , yokinSmkCd);
							riseEvent(yokinSmkCdId2);
							setValue(kozaNoId2 , kozaNo);
							riseEvent(kozaNoId2);
							setValue(kozaNameKanaId2, kozaNameKana);
							riseEvent(kozaNameKanaId2);
							setValue(bnkNameId2 , bnkName);
							riseEvent(bnkNameId2);
							setValue(stnNameId2 , stnName);
							riseEvent(stnNameId2);
							//マスク無し項目						
							setValue(trueBnkCdObj, trueBnkCd);
							riseEvent(trueBnkCdObj);
							setValue(trueStnCdObj, trueStnCd);
							riseEvent(trueStnCdObj);
							setValue(trueKozaNoObj , trueKozaNo);
							riseEvent(trueKozaNoObj);
							setValue(trueKozaNameKanaObj, trueKozaNameKana);
							riseEvent(trueKozaNameKanaObj);
						}
						
					} else {
							//経理部門ユーザでマスク有りの場合、マスク値と正常値を返す
							setValue(bnkCdId2, bnkCd);
							riseEvent(bnkCdId2);
							setValue(stnCdId2, stnCd);
							riseEvent(stnCdId2);
							setValue(yokinSmkCdId2 , yokinSmkCd);
							riseEvent(yokinSmkCdId2);
							setValue(kozaNoId2 , kozaNo);
							riseEvent(kozaNoId2);
							setValue(kozaNameKanaId2, kozaNameKana);
							riseEvent(kozaNameKanaId2);
							setValue(bnkNameId2 , bnkName);
							riseEvent(bnkNameId2);
							setValue(stnNameId2 , stnName);
							riseEvent(stnNameId2);
							//マスク無し項目						
							setValue(trueBnkCdObj, trueBnkCd);
							riseEvent(trueBnkCdObj);
							setValue(trueStnCdObj, trueStnCd);
							riseEvent(trueStnCdObj);
							setValue(trueKozaNoObj , trueKozaNo);
							riseEvent(trueKozaNoObj);
							setValue(trueKozaNameKanaObj, trueKozaNameKana);
							riseEvent(trueKozaNameKanaObj);
						
					}

					select++;
					break;
				}
			}
			if (select > 0) {
				//フォーカスを設定する項目ID
				var targets = new Array();
				targets.push(getElementByIdEx(window.opener.document, focusId));
				targets.push(getElementByIdEx(window.opener.document, bnkCdId));
				targets.push(getElementByIdEx(window.opener.document, stnCdId));
				targets.push(getElementByIdEx(window.opener.document, yokinSmkCdId));
				targets.push(getElementByIdEx(window.opener.document, kozaNoId));
				targets.push(getElementByIdEx(window.opener.document, kozaNameKanaId));
				targets.push(getElementByIdEx(window.opener.document, bnkNameId));
				targets.push(getElementByIdEx(window.opener.document, stnNameId));
				windowClose(targets);
			} else {
				reflapWindow(window);
				setErrMsg(noSelectMsg);
			}
		} else {
			throw "";
		}
	} catch(e) {
		window.moveTo(0, 0);
		alert("呼び出し元画面に値を返せません。");
	}
	return false;
}
function func_2(thisObj, thisEvent) {
	var bnkCdId = document.getElementById("form1:prmBnkCdId").value;
	var stnCdId = document.getElementById("form1:prmStnCdId").value;
	var yokinSmkCdId = document.getElementById("form1:prmYokinSmkCdId").value;
	var kozaNoId = document.getElementById("form1:prmKozaNoId").value;
	var kozaNameKanaId = document.getElementById("form1:prmKozaNameKanaId").value;
	var bnkNameId = document.getElementById("form1:prmBnkNameId").value;
	var stnNameId = document.getElementById("form1:prmStnNameId").value;
	var focusId = document.getElementById("form1:prmFocusId").value;
	
	//フォーカスを設定する項目ID
	var targets = new Array();
	targets.push(getElementByIdEx(window.opener.document, focusId));
	targets.push(getElementByIdEx(window.opener.document, bnkCdId));
	targets.push(getElementByIdEx(window.opener.document, stnCdId));
	targets.push(getElementByIdEx(window.opener.document, yokinSmkCdId));
	targets.push(getElementByIdEx(window.opener.document, kozaNoId));
	targets.push(getElementByIdEx(window.opener.document, kozaNameKanaId));
	targets.push(getElementByIdEx(window.opener.document, bnkNameId));
	targets.push(getElementByIdEx(window.opener.document, stnNameId));
	windowClose(targets);
	return false;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="setFocusDateFindType()">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PCog1901.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_PCog1901.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PCog1901.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PCog1901.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- レイアウトずれ対応の全角スペース -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600px">
							<TBODY>
								<TR>
									<TD>
									<TABLE width="600px" border="0" cellpadding="0" cellspacing="0" class="table">
										<TBODY>
											<TR>
												<TH nowrap width="145px" class="v_a">
													<h:outputText styleClass="outputText" id="lblSimei"
													style="#{pc_PCog1901.propJinjiNo.labelStyle}"
													value="#{pc_PCog1901.propJinjiNo.labelName}">
												</h:outputText>
												</TH>
												<TD nowrap width="450px">
												<TABLE width="100%" border="0" cellpadding="0"
													cellspacing="0" class="clear_border">
													<TBODY>
														<TR>
															<TD nowrap>
																<h:outputText styleClass="outputText" id="htmlJinjiNo"
																value="#{pc_PCog1901.propJinjiNo.stringValue}"
																style="#{pc_PCog1901.propJinjiNo.style}">
															</h:outputText></TD>
															<TD nowrap>　</TD>
															<TD nowrap>
																<DIV style="width:318px;white-spce:nowrap;overflow:hidden;display:block;">
																	<h:outputText styleClass="outputText"
																id="htmlJinjiName"
																value="#{pc_PCog1901.propJinjiName.stringValue}"
																style="#{pc_PCog1901.propJinjiName.style}">
															</h:outputText></DIV>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TH nowrap width="145px" class="v_b"><h:outputText
													styleClass="outputText" id="lblDate"
													value="#{pc_PCog1901.propTaisyobi.labelName}"
													style="#{pc_PCog1901.propTaisyobi.labelStyle}"></h:outputText>
												</TH>
												<TD>
													<TABLE width="450px" border="0" cellpadding="0" cellspacing="0" class="clear_border">
														<TBODY>
															<TR>
															<TD width="100px" colspan="3"><h:inputText
																styleClass="inputText" id="htmlTaisyobi"
																value="#{pc_PCog1901.propTaisyobi.dateValue}"
																disabled="#{pc_PCog1901.propTaisyobi.disabled}"
																readonly="#{pc_PCog1901.propTaisyobi.readonly}"
																size="14" style="#{pc_PCog1901.propTaisyobi.style}"
																tabindex="3">
																<f:convertDateTime pattern="yyyy/MM/dd" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" imeMode="inactive" />
																<hx:inputHelperDatePicker />
															</h:inputText></TD>
														</TR>
														</TBODY>
													</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
						</TBODY>
					</TABLE>
					</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="600"
							class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="検索"
										styleClass="commandExButton_dat" id="search" action="#{pc_PCog1901.doSearchAction}" tabindex="4">
									</hx:commandExButton><hx:commandExButton type="submit"
										value="クリア" styleClass="commandExButton_etc" id="clear"
										action="#{pc_PCog1901.doClearAction}" tabindex="5">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<HR noshade>
						</TD>
					</TR>
					<TR>
						<TD>
						<hx:jspPanel id="jspPanel1">
							<TABLE border="0" cellpadding="0" cellspacing="0">
								<TBODY>
									<TR>
										<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD width="500px" style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none"></TD>
													<TD width="100px"
														style="border-bottom-style: none; border-left-style: none; border-top-style: none; border-right-style: none; text-align: right"
														align="right"><h:outputText styleClass="outputText"
														id="htmlListCount"
														value="#{pc_PCog1901.propListCount.integerValue}"
														style="#{pc_PCog1901.propListCount.style}">
														<f:convertNumber />
													</h:outputText><h:outputText styleClass="outputText"
														id="lblKensu" value="件" style="text-align: right"></h:outputText></TD>
												</TR>
												<TR>
													<TH width="600px" colspan="2">
													<TABLE border="0" cellpadding="0" cellspacing="0"
														width="100%" height="100%">
														<TBODY>
															<TR>
																<TD
																	style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify"
																	width="168px"><h:outputText styleClass="outputText" id="lblListFrkmskBank" value="振込先銀行"></h:outputText></TD>
																<TD
																	style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify"
																	width="163px"><h:outputText styleClass="outputText" id="lblListFrkmskStn" value="振込先支店"></h:outputText></TD>
																<TD
																	style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify"
																	width="90px"><h:outputText styleClass="outputText" id="lblListFrkmskYokinSmk" value="預金種目"></h:outputText></TD>
																<TD
																	style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify"
																	width="90px" colspan=""><h:outputText styleClass="outputText" id="lblListFrkmskKozaNo" value="口座番号"></h:outputText></TD>
																<TD
																	style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align:justify"
																	width="89px" colspan=""><h:outputText
																	styleClass="outputText" id="lblListFrkmskKozaSyurui"
																	value="口座種類"></h:outputText></TD>
															</TR>
														</TBODY>
													</TABLE>
													</TH>
												</TR>
												<TR>
													<TD width="600px" colspan="2">
													<h:selectOneListbox styleClass="selectOneListbox"
														id="htmlBankKozaOneList"
														style="width:100%;"
														size="20"
														disabled="#{pc_PCog1901.propBankKozaOneList.disabled}"
														readonly="#{pc_PCog1901.propBankKozaOneList.readonly}"
														value="#{pc_PCog1901.propBankKozaOneList.value}"
														ondblclick="return func_1(this, event);"
														tabindex="6">
														<f:selectItems
															value="#{pc_PCog1901.propBankKozaOneList.list}" />
													</h:selectOneListbox>
													</TD>
												</TR>
											</TBODY>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar">
											<TBODY>
												<TR>
													<TD width="600px"><hx:commandExButton type="submit" styleClass="commandExButton_etc" 
														id="select" value="選択" action="#{pc_PCog1901.doSelectAction}" onclick="return func_1(this, event);" tabindex="7">
													</hx:commandExButton><hx:commandExButton type="submit" value="キャンセル" 
													styleClass="commandExButton_etc" id="cancel" action="#{pc_PCog1901.doCancelAction}" onclick="return func_2(this, event);" tabindex="8">
													</hx:commandExButton></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</hx:jspPanel>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			
			<h:inputHidden id="htmlJinjiNoHidden" value="#{pc_PCog1901.propJinjiNoHidden.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlTaisyobiHidden" value="#{pc_PCog1901.propTaisyobiHidden.dateValue}"><f:convertDateTime /></h:inputHidden>
			<h:inputHidden id="htmlNoSelectMsgHidden" value="#{pc_PCog1901.propNoSelectMsgHidden.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlExecutableSearchHidden" value="#{pc_PCog1901.propExecutableSearchHidden.integerValue}"><f:convertNumber /></h:inputHidden>

<!--			<h:inputHidden id="htmlKaikeiNendoHidden" value="#{pc_PCog1901.propKaikeiNendoHidden.integerValue}"></h:inputHidden> -->
			<h:inputHidden id="htmlKeiriBmnFlgHidden" value="#{pc_PCog1901.propKeiriBmnFlgHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlMaskFlgHidden" value="#{pc_PCog1901.propMaskFlgHidden.integerValue}"></h:inputHidden>			

			<input id="form1:prmBnkCdId" type="hidden" name="prmBnkCdId" value="<c:out value="${pc_PCog1901.prmBnkCdId}"/>">
			<input id="form1:prmStnCdId" type="hidden" name="prmStnCdId" value="<c:out value="${pc_PCog1901.prmStnCdId}"/>">
			<input id="form1:prmKozaNoId" type="hidden" name="prmKozaNoId" value="<c:out value="${pc_PCog1901.prmKozaNoId}"/>">
			<input id="form1:prmKozaNameKanaId" type="hidden" name="prmKozaNameKanaId" value="<c:out value="${pc_PCog1901.prmKozaNameKanaId}"/>">

			<input id="form1:prmTrueBnkCdId" type="hidden" name="prmTrueBnkCdId" value="<c:out value="${pc_PCog1901.prmTrueBnkCdId}"/>">
			<input id="form1:prmTrueStnCdId" type="hidden" name="prmTrueStnCdId" value="<c:out value="${pc_PCog1901.prmTrueStnCdId}"/>">
			<input id="form1:prmTrueKozaNoId" type="hidden" name="prmTrueKozaNoId" value="<c:out value="${pc_PCog1901.prmTrueKozaNoId}"/>">
			<input id="form1:prmTrueKozaNameKanaId" type="hidden" name="prmTrueKozaNameKanaId" value="<c:out value="${pc_PCog1901.prmTrueKozaNameKanaId}"/>">

			<input id="form1:prmYokinSmkCdId" type="hidden" name="prmYokinSmkCdId" value="<c:out value="${pc_PCog1901.prmYokinSmkCdId}"/>">
			<input id="form1:prmBnkNameId" type="hidden" name="prmBnkNameId" value="<c:out value="${pc_PCog1901.prmBnkNameId}"/>">
			<input id="form1:prmStnNameId" type="hidden" name="prmStnNameId" value="<c:out value="${pc_PCog1901.prmStnNameId}"/>">
			
			<input id="form1:prmFocusId" type="hidden" name="prmFocusId" value="<c:out value="${pc_PCog1901.prmFocusId}"/>">
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/childFooter.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

