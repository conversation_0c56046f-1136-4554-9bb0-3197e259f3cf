<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kac01102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>優先順位指定</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
	self.close();
	return true;

}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kac01102.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kac01102.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kac01102.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kac01102.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> &#160; <!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="580" style="margin-top: 20px;">
				<TBODY>
					<TR>
						<TH width="100" class="v_a">
						<CENTER>優先順位</CENTER>
						</TH>
						<TH width="280" class="v_b">
						<CENTER>項目</CENTER>
						</TH>
						<TH width="200" class="v_c">
						<CENTER>ソート順</CENTER>
						</TH>
					</TR>
					<TR style="${pc_Kac01102.tr1SsnStyle}">
						<TD>
						<CENTER>1</CENTER>
						</TD>
						<TD><h:outputText styleClass="outputText" id="htmlFirstOutSsn"
							value="資産区分"></h:outputText></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFirstOutJijyoSsn"
							value="#{pc_Kac01102.propFirstOutJijyoSsn.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr1BhnStyle}">
						<TD>
						<CENTER>1</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlFirstOutBhn"
							value="#{pc_Kac01102.propFirstOutBhn.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propFirstOutBhn.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFirstOutJijyoBhn"
							value="#{pc_Kac01102.propFirstOutJijyoBhn.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr1LeaseStyle}">
						<TD>
						<CENTER>1</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlFirstOutLease"
							value="#{pc_Kac01102.propFirstOutLease.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propFirstOutLease.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFirstOutJijyoLease"
							value="#{pc_Kac01102.propFirstOutJijyoLease.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr2Style}">
						<TD>
						<CENTER>2</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlSecondOut"
							value="#{pc_Kac01102.propSecondOut.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propSecondOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSecondOutJijyo"
							value="#{pc_Kac01102.propSecondOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr3Style}">
						<TD>
						<CENTER>3</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlThirdOut"
							value="#{pc_Kac01102.propThirdOut.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propThirdOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlThirdOutJijyo"
							value="#{pc_Kac01102.propThirdOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr4Style}">
						<TD>
						<CENTER>4</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlFourthOut"
							value="#{pc_Kac01102.propFourthOut.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propFourthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFourthOutJijyo"
							value="#{pc_Kac01102.propFourthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr5Style}">
						<TD>
						<CENTER>5</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlFifthOut"
							value="#{pc_Kac01102.propFifthOut.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propFifthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlFifthOutJijyo"
							value="#{pc_Kac01102.propFifthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr6Style}">
						<TD>
						<CENTER>6</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlSixthOut"
							value="#{pc_Kac01102.propSixthOut.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propSixthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSixthOutJijyo"
							value="#{pc_Kac01102.propSixthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr7Style}">
						<TD>
						<CENTER>7</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSeventhOut"
							value="#{pc_Kac01102.propSeventhOut.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propSeventhOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSeventhOutJijyo"
							value="#{pc_Kac01102.propSeventhOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR style="${pc_Kac01102.tr8Style}">
						<TD>
						<CENTER>8</CENTER>
						</TD>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlEighthOut"
							value="#{pc_Kac01102.propEighthOut.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propEighthOut.list}" />
						</h:selectOneMenu></TD>
						<TD>
						<CENTER><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlEighthOutJijyo"
							value="#{pc_Kac01102.propEighthOutJijyo.stringValue}">
							<f:selectItem itemValue="0" itemLabel="昇順" />
							<f:selectItem itemValue="1" itemLabel="降順" />
						</h:selectOneRadio></CENTER>
						</TD>
					</TR>
					<TR class="clear_border">
						<TD colspan="3" style="background-color:transparent;"></TD>
					</TR>
					<TR>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblKaiPageSitei" value="改頁指定"></h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlKaiPageSitei"
							value="#{pc_Kac01102.propKaiPageSitei.stringValue}">
							<f:selectItems value="#{pc_Kac01102.propKaiPageSitei.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE width="580" border="0" style="" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="選択"
								styleClass="commandExButton_etc" id="select"
								action="#{pc_Kac01102.doSelectAction}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="キャンセル"
								styleClass="commandExButton_etc" id="cancel"
								onclick="return func_1(this, event);">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/childFooter.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

