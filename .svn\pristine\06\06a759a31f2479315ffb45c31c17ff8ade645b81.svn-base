<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb15002.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>経理文面設定</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<!--①エディタ部品のjsファイルをinclude-->
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/system/inc/svfEditor/svfEditor.js"></SCRIPT>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/system/inc/svfEditor/svfEditorFrame.js"></SCRIPT>

<SCRIPT type="text/javascript">
function onReturnClick(id) {
	return true;
}

//②エディタ部品のインスタンスを生成する関数
function createEditer(thisObj, thisEvent) {
	/*第1引数：<textarea>のID  = h:formのID + "." + h:inputTextareaのID
	  第2引数：予約語XMLのID
	  第3引数：エディタ部品のインスタンスの(任意の)名前
	  第4引数：コンテキストパス
	  第5引数：用紙サイズ(A3、A4、A5、B4、B5、LETTER、POSTCARD）(※)null指定時は、ユーザ指定(※1)
	  第6引数：用紙方向（縦：'portrait'、横：'landscape'）(※)null指定時は、ユーザ指定(※1)
	  第7引数：編集エリアの横サイズ（cssで指定可能な形式）


	  第8引数：編集エリアの縦サイズ（cssで指定可能な形式）


	  戻り値：エディタ部品のインスタンス
	  (※1)テンプレートfrmを使用時は、ＳＶＦ設計部での指定値となりユーザ変更できません。*/

	var keyword = document.getElementById("form1:htmlHiddenKeyword").value;
	SVFED.createEditor('form1:textarea1',
	                   keyword,
	                   'edit1',
	                   '${pageContext.request.contextPath}',
	                   'A4',
	                   'portrait',
	                   '95%',
                       '350px'
	                  );

	return true;
	/*【補足】


	(※)エディタ部品のインスタンスは、複数生成可能（但しinputTextareaと一対）


	各エディタ部品のインスタンスは、createEditerの第3引数で識別
	各エディタ部品の識別名は、'SVFED.editor.'+第3引数 となる。


	exp. SVFED.editor.edit1
	又、SVFED.createEditorの戻り値によって、各エディタ部品のインスタンスにアクセスすることも可能。


	*/
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<!-- ③BODYのonloadで、エディタ部品のインスタンスインスタンス生成メソッドを実行する。 -->
<BODY onload="return createEditer(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb15002.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Keb15002.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb15002.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb15002.screenName}"></h:outputText>
</div>			

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
	styleClass="commandExButton" id="returnDisp"
	onclick="return onReturnClick('#{msg.SY_MSG_0014W}');"
	action="#{pc_Keb15002.doReturnAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE width="100%" border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD>
				<TABLE class="table" width="900">
					<TBODY>
						<TR>
							<TH nowrap class="v_a" width="120">
								<h:outputText styleClass="outputText" id="lblBunmenKbn"
										value="#{pc_Keb15002.propBunmenKbn.labelName}"
										style="#{pc_Keb15002.propBunmenKbn.labelStyle}"></h:outputText></TH>
							<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlBunmenKbn"
										disabled="#{pc_Keb15002.propBunmenKbn.disabled}"
										readonly="#{pc_Keb15002.propBunmenKbn.readonly}"
										rendered="#{pc_Keb15002.propBunmenKbn.rendered}"
										style="width : 350px;#{pc_Keb15002.propBunmenKbn.style}"
										value="#{pc_Keb15002.propBunmenKbn.value}">
										<f:selectItems value="#{pc_Keb15002.propBunmenKbn.list}"/>
									</h:selectOneMenu></TD>
						</TR>
						<TR>
							<TH nowrap class="v_b" width="120">
								<h:outputText styleClass="outputText" id="lblBunmenEdaban"
										value="#{pc_Keb15002.propBunmenEdano.labelName}"
										style="#{pc_Keb15002.propBunmenEdano.labelStyle}"></h:outputText></TH>
							<TD><h:inputText styleClass="inputText" id="htmlBunmenEdano"
										size="2" maxlength="#{pc_Keb15002.propBunmenEdano.maxLength}"
										disabled="#{pc_Keb15002.propBunmenEdano.disabled}"
										value="#{pc_Keb15002.propBunmenEdano.integerValue}"
										readonly="#{pc_Keb15002.propBunmenEdano.readonly}"
										style="text-align:right;padding-right: 3px;#{pc_Keb15002.propBunmenEdano.style}">
										<f:convertNumber pattern="#0;#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
						</TR>
						<TR>
							<TH nowrap class="v_b" width="120">
								<h:outputText styleClass="outputText" id="lblTitleCaption"
										value="#{pc_Keb15002.propTitle.labelName}"
										style="#{pc_Keb15002.propTitle.labelStyle}"></h:outputText></TH>
							<TD><h:inputText styleClass="inputText" id="htmlTitle" size="35"
										disabled="#{pc_Keb15002.propTitle.disabled}"
										maxlength="#{pc_Keb15002.propTitle.maxLength}"
										readonly="#{pc_Keb15002.propTitle.readonly}"
										rendered="#{pc_Keb15002.propTitle.rendered}"
										style="#{pc_Keb15002.propTitle.style}"
										value="#{pc_Keb15002.propTitle.value}"></h:inputText></TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
			<TR>
				<TD>
				<BR>
				<TABLE align="center" width="100%" border="0" cellpadding="3" cellspacing="0">
					<TBODY>
						<TR>
							<TD>
								<!-- ④文面ＨＴＭＬを保持する h:inputTextarea -->
								<h:inputTextarea styleClass="inputTextarea" id="textarea1"
									value="#{pc_Keb15002.propBunmenLayout.stringValue}" cols="100" rows="35" style="display : none;"></h:inputTextarea>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
			<TR>	
				<TD>
				<TABLE  align="center" cellspacing="1" cellpadding="1" class="button_bar" width="900">
					<TBODY>
						<TR>
							<TD align="center"><hx:commandExButton type="submit"
										value="プレビュー" styleClass="commandExButton_out" id="preview"
										onclick="cancelSubmitCtrl();return SVFED.editor.edit1.beforeSubmit();"
										action="#{pc_Keb15002.doPreviewAction}"
										rendered="#{pc_Keb15002.propPreview.rendered}"></hx:commandExButton><hx:commandExButton
										type="submit" value="登録" styleClass="commandExButton_dat"
										id="register"
										onclick="return SVFED.editor.edit1.beforeSubmit();"
										action="#{pc_Keb15002.doRegisterAction}"
										confirm="#{msg.SY_MSG_0002W}"
										rendered="#{pc_Keb15002.propRegister.rendered}"></hx:commandExButton><hx:commandExButton
										type="submit" value="更新" styleClass="commandExButton_dat"
										id="update"
										onclick="return SVFED.editor.edit1.beforeSubmit();"
										action="#{pc_Keb15002.doUpdateAction}"
										confirm="#{msg.SY_MSG_0003W}"
										rendered="#{pc_Keb15002.propEdit.rendered}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Keb15002.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"
										rendered="#{pc_Keb15002.propDelete.rendered}"></hx:commandExButton></TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden id="htmlHiddenKeyword"
				value="#{pc_Keb15002.propHiddenKeyWord.stringValue}"></h:inputHidden>
			<h:inputHidden value="#{pc_Keb15002.propHiddenBunmenKbn.stringValue}"
				id="htmlHiddenBunmenKbn"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Keb15002.propHiddenBunmenEdano.integerValue}"
				id="htmlHiddenBunmenEdano">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Keb15002.propHiddenFormId.stringValue}"
				id="htmlHiddenFormId"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

<!-- ⑦予約語ＸＭＬ（このIDをSVFED.createEditorの第2引数で渡す。） -->
<XML ID="KEYWORD1" SRC="${pageContext.request.contextPath}/rev/ke/inc/keywords/KeXML01.xml" />

</HTML>
