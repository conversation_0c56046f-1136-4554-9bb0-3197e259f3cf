<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc01003T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib prefix="gakuen" uri="/gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsc01003T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc01003T01.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Nsc01003T01">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc01003T01.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc01003T01.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc01003T01.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトの問題の為、全角スペースを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD align="left">
						<TABLE width="100%" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<hx:jspPanel rendered="#{!pc_Nsc01003T01.nsc01003.ksknGhgrFlg}">
								<TR>
									<TH class="v_a" width="120"><h:outputText
										styleClass="outputText" id="lblJukenCd"
										value="#{pc_Nsc01003T01.propJukenCd.name}"
										style="#{pc_Nsc01003T01.propJukenCd.labelStyle}"></h:outputText></TH>
									<TD width="140"><h:outputText
										styleClass="outputText" id="htmlJukenCd"
										style="#{pc_Nsc01003T01.propJukenCd.style}"
										value="#{pc_Nsc01003T01.propJukenCd.stringValue}"></h:outputText></TD>
									<TH class="v_b" width="120"><h:outputText 
										styleClass="outputText" id="lblSgnCd" 
										value="#{pc_Nsc01003T01.propSgnCd.name}" 
										style="#{pc_Nsc01003T01.propSgnCd.labelStyle}"></h:outputText></TH>
									<TD width="140"><h:outputText 
										styleClass="outputText" id="htmlSgnCd" 
										style="#{pc_Nsc01003T01.propSgnCd.style}" 
										value="#{pc_Nsc01003T01.propSgnCd.stringValue}"></h:outputText></TD>
									<TH class="v_c" width="120"><h:outputText 
										styleClass="outputText" id="lblSgnName" 
										value="#{pc_Nsc01003T01.propSgnName.name}" 
										style="#{pc_Nsc01003T01.propSgnName.labelStyle}"></h:outputText></TH>
									<TD><h:outputText 
										styleClass="outputText" id="htmlSgnName" 
										value="#{pc_Nsc01003T01.propSgnName.stringValue}" 
										style="#{pc_Nsc01003T01.propSgnName.style}"></h:outputText></TD>
								</TR>
								</hx:jspPanel>
								<hx:jspPanel rendered="#{pc_Nsc01003T01.nsc01003.ksknGhgrFlg}">
								<TR>
									<TH class="v_a" width="90"><h:outputText
										styleClass="outputText" id="lblJukenCdKskn"
										value="#{pc_Nsc01003T01.propJukenCd.name}"
										style="#{pc_Nsc01003T01.propJukenCd.labelStyle}"></h:outputText></TH>
									<TD width="90"><h:outputText
										styleClass="outputText" id="htmlJukenCdKskn"
										style="#{pc_Nsc01003T01.propJukenCd.style}"
										value="#{pc_Nsc01003T01.propJukenCd.stringValue}"></h:outputText></TD>
									<TH class="v_a" width="90"><h:outputText
										styleClass="outputText" id="lblkyotuJukenCd"
										value="#{pc_Nsc01003T01.propKyotuJukenCd.name}"
										style="#{pc_Nsc01003T01.propKyotuJukenCd.labelStyle}"></h:outputText></TH>
									<TD width="90"><h:outputText
										styleClass="outputText" id="htmlKyotuJukenCd"
										style="#{pc_Nsc01003T01.nsc01003.propKyotuJukenCd.style}"
										value="#{pc_Nsc01003T01.nsc01003.propKyotuJukenCd.stringValue}"></h:outputText></TD>
									<TH class="v_b" width="90"><h:outputText 
										styleClass="outputText" id="lblSgnCdKskn" 
										value="#{pc_Nsc01003T01.propSgnCd.name}" 
										style="#{pc_Nsc01003T01.propSgnCd.labelStyle}"></h:outputText></TH>
									<TD width="90"><h:outputText 
										styleClass="outputText" id="htmlSgnCdKskn" 
										style="#{pc_Nsc01003T01.propSgnCd.style}" 
										value="#{pc_Nsc01003T01.propSgnCd.stringValue}"></h:outputText></TD>
									<TH class="v_c" width="80"><h:outputText 
										styleClass="outputText" id="lblSgnNameKskn" 
										value="#{pc_Nsc01003T01.propSgnName.name}" 
										style="#{pc_Nsc01003T01.propSgnName.labelStyle}"></h:outputText></TH>
									<TD><h:outputText 
										styleClass="outputText" id="htmlSgnNameKskn" 
										value="#{pc_Nsc01003T01.propSgnName.stringValue}" 
										style="#{pc_Nsc01003T01.propSgnName.style}"></h:outputText></TD>
								</TR>
								</hx:jspPanel>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>

						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-top:15px">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="tab_head_on" width="13%"><hx:commandExButton type="submit"
													styleClass="tab_head_on" id="selectTab1" value="受験者情報" tabindex="1"
													style="width: 100%"
													action="#{pc_Nsc01003T01.doSelectTab1Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab1.disabled}"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="13%"><hx:commandExButton type="submit"
													styleClass="tab_head_off" id="selectTab2" value="受験情報" tabindex="2"
													style="width:100%"
													action="#{pc_Nsc01003T01.doSelectTab2Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab2.disabled}"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="13%"><hx:commandExButton type="submit"
													styleClass="tab_head_off" id="selectTab3" value="入試成績情報" tabindex="3"
													style="width:100%"
													action="#{pc_Nsc01003T01.doSelectTab3Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab3.disabled}"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="13%"><hx:commandExButton type="submit"
													styleClass="tab_head_off" id="selectTab4" value="入学手続" tabindex="4"
													style="width:100%"
													action="#{pc_Nsc01003T01.doSelectTab4Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab4.disabled}"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="12%"><hx:commandExButton type="submit"
													styleClass="tab_head_off" id="selectTab5" value="出身校情報" tabindex="5"
													style="width:100%"
													action="#{pc_Nsc01003T01.doSelectTab5Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab5.disabled}"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="12%"><hx:commandExButton type="submit"
													styleClass="tab_head_off" id="selectTab6" value="保証人情報" tabindex="6"
													style="width:100%"
													action="#{pc_Nsc01003T01.doSelectTab6Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab6.disabled}"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="12%"><hx:commandExButton type="submit"
													styleClass="tab_head_off" id="selectTab7" value="自由設定" tabindex="7"
													style="width:100%"
													action="#{pc_Nsc01003T01.doSelectTab7Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab7.disabled}"></hx:commandExButton></TD>
												<TD class="tab_head_off" width="12%"><hx:commandExButton type="submit"
													styleClass="tab_head_off" id="selectTab8" value="留学生情報" tabindex="8"
													style="width:100%"
													action="#{pc_Nsc01003T01.doSelectTab8Action}"
													disabled="#{pc_Nsc01003T01.propSelectTab8.disabled}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0"
											class="tab_body" width="100%" height="470">
										<TBODY>

											<TR>
												<TD valign="top"><BR>
												<TABLE width="880" border="0" cellpadding="0"
													cellspacing="0" style="" class="table">
													<TBODY>
														<TR>
															<TH style="" class="v_d" width="150"><h:outputText
																styleClass="outputText" id="lblSgnNameKana" 
																style="#{pc_Nsc01003T01.propSgnNameKana.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnNameKana.name}"></h:outputText></TH>
															<TD colspan="5"><h:outputText
																styleClass="outputText" id="htmlSgnNameKana"
																style="#{pc_Nsc01003T01.propSgnNameKana.style}"
																value="#{pc_Nsc01003T01.propSgnNameKana.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH style="" class="v_e" width="150"><h:outputText
																styleClass="outputText" id="lblSgnNameEng" 
																style="#{pc_Nsc01003T01.propSgnNameEng.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnNameEng.name}"></h:outputText></TH>
															<TD colspan="5"><h:outputText
																styleClass="outputText" id="htmlSgnNameEng"
																style="#{pc_Nsc01003T01.propSgnNameEng.style}"
																value="#{pc_Nsc01003T01.propSgnNameEng.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH style="" class="v_f" width="150"><h:outputText
																styleClass="outputText" id="lblSgnNameWeb"
																style="#{pc_Nsc01003T01.propSgnNameWeb.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnNameWeb.name}"></h:outputText></TH>
															<TD colspan="5"><h:outputText
																styleClass="outputText" id="htmlSgnNameWeb"
																style="#{pc_Nsc01003T01.propSgnNameWeb.style}"
																value="#{pc_Nsc01003T01.propSgnNameWeb.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_g" width="150"><h:outputText
																styleClass="outputText" id="lblSeibetu" 
																style="#{pc_Nsc01003T01.propSeibetu.labelStyle}"
																value="#{pc_Nsc01003T01.propSeibetu.name}"></h:outputText></TH>
															<TD width="160"><h:outputText
																styleClass="outputText" id="htmlSeibetu"
																style="#{pc_Nsc01003T01.propSeibetu.style}"
																value="#{pc_Nsc01003T01.propSeibetu.stringValue}"></h:outputText></TD>
															<TH class="v_a" width="150"><h:outputText
																styleClass="outputText" id="lblBirth"
																style="#{pc_Nsc01003T01.propBirth.labelStyle}"
																value="#{pc_Nsc01003T01.propBirth.name}"></h:outputText></TH>
															<TD width="150">
																<h:outputText styleClass="outputText" id="htmlBirth"
																style="#{pc_Nsc01003T01.propBirth.style}"
																value="#{pc_Nsc01003T01.propBirth.dateValue}">
																<f:convertDateTime pattern="yyyy/MM/dd" />
															</h:outputText></TD>
															<TH class="v_b" width="100">
																<h:outputText styleClass="outputText" id="lblSgnAge" 
																style="#{pc_Nsc01003T01.propSgnAge.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnAge.name}"></h:outputText></TH>
															<TD><h:outputText styleClass="outputText" id="htmlSgnAge"
																style="#{pc_Nsc01003T01.propSgnAge.style}"
																value="#{pc_Nsc01003T01.propSgnAge.integerValue}">
																<f:convertNumber pattern="##0"/>
															</h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_c" width="150"><h:outputText
																styleClass="outputText" id="lblCountryCd" 
																style="#{pc_Nsc01003T01.propCountryCd.labelStyle}"
																value="#{pc_Nsc01003T01.propCountryCd.name}"></h:outputText></TH>
															<TD><h:outputText
																styleClass="outputText" id="htmlCountryCd"
																style="#{pc_Nsc01003T01.propCountryCd.style}"
																value="#{pc_Nsc01003T01.propCountryCd.stringValue}"></h:outputText></TD>
															<TH class="v_d" width="150">
																<h:outputText styleClass="outputText" id="lblCountryName" 
																style="#{pc_Nsc01003T01.propCountryName.labelStyle}"
																value="#{pc_Nsc01003T01.propCountryName.name}"></h:outputText></TH>
															<TD colspan="3">
																<h:outputText styleClass="outputText" id="htmlCountryName"
																style="#{pc_Nsc01003T01.propCountryName.style}"
																value="#{pc_Nsc01003T01.propCountryName.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_e" width="150"><h:outputText
																styleClass="outputText" id="lblHonsekiCd" 
																style="#{pc_Nsc01003T01.propHonsekiCd.labelStyle}"
																value="#{pc_Nsc01003T01.propHonsekiCd.name}"></h:outputText></TH>
															<TD><h:outputText styleClass="outputText" id="htmlHonsekiCd"
																style="#{pc_Nsc01003T01.propHonsekiCd.style}"
																value="#{pc_Nsc01003T01.propHonsekiCd.stringValue}"></h:outputText></TD>
															<TH class="v_f" width="150">
																<h:outputText styleClass="outputText" id="lblHonsekiName"
																style="#{pc_Nsc01003T01.propHonsekiName.labelStyle}"
																value="#{pc_Nsc01003T01.propHonsekiName.name}"></h:outputText></TH>
															<TD colspan="3">
																<h:outputText styleClass="outputText" id="htmlHonsekiName"
																style="#{pc_Nsc01003T01.propHonsekiName.style}"
																value="#{pc_Nsc01003T01.propHonsekiName.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_g" width="150"><h:outputText
																styleClass="outputText" id="lblSyussinCd" 
																style="#{pc_Nsc01003T01.propSyussinCd.labelStyle}"
																value="#{pc_Nsc01003T01.propSyussinCd.name}"></h:outputText></TH>
															<TD width="250"><h:outputText
																styleClass="outputText" id="htmlSyussinCd"
																style="#{pc_Nsc01003T01.propSyussinCd.style}"
																value="#{pc_Nsc01003T01.propSyussinCd.stringValue}"></h:outputText></TD>
															<TH class="v_a" width="150">
																<h:outputText styleClass="outputText" id="lblSyussinName" 
																style="#{pc_Nsc01003T01.propSyussinName.labelStyle}"
																value="#{pc_Nsc01003T01.propSyussinName.name}"></h:outputText></TH>
															<TD colspan="3">
																<h:outputText styleClass="outputText" id="htmlSyussinName"
																style="#{pc_Nsc01003T01.propSyussinName.style}"
																value="#{pc_Nsc01003T01.propSyussinName.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_b" width="150"><h:outputText
																styleClass="outputText" id="lblChikuCd"
																style="#{pc_Nsc01003T01.propChikuCd.labelStyle}"
																value="#{pc_Nsc01003T01.propChikuCd.name}"></h:outputText></TH>
															<TD><h:outputText
																styleClass="outputText" id="htmlChikuCd"
																style="#{pc_Nsc01003T01.propChikuCd.style}"
																value="#{pc_Nsc01003T01.propChikuCd.stringValue}"></h:outputText></TD>
															<TH class="v_c" width="150">
																<h:outputText styleClass="outputText" id="lblChikuName"
																style="#{pc_Nsc01003T01.propChikuName.labelStyle}"
																value="#{pc_Nsc01003T01.propChikuName.name}"></h:outputText></TH>
															<TD colspan="3">
																<h:outputText styleClass="outputText" id="htmlChikuName"
																style="#{pc_Nsc01003T01.propChikuName.style}"
																value="#{pc_Nsc01003T01.propChikuName.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_d" width="150"><h:outputText
																styleClass="outputText" id="lblSgnTel1" 
																style="#{pc_Nsc01003T01.propSgnTel1.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnTel1.name}"></h:outputText></TH>
															<TD><h:outputText
																styleClass="outputText" id="htmlSgnTel1"
																style="#{pc_Nsc01003T01.propSgnTel1.style}"
																value="#{pc_Nsc01003T01.propSgnTel1.stringValue}"></h:outputText></TD>
															<TH class="v_e" width="150"><h:outputText
																styleClass="outputText" id="lblSgnTel2" 
																style="#{pc_Nsc01003T01.propSgnTel2.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnTel2.name}"></h:outputText></TH>
															<TD colspan="3"><h:outputText
																styleClass="outputText" id="htmlSgnTel2"
																style="#{pc_Nsc01003T01.propSgnTel2.style}"
																value="#{pc_Nsc01003T01.propSgnTel2.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_f" width="150"><h:outputText
																styleClass="outputText" id="lblSgnKeitaiTel"
																style="#{pc_Nsc01003T01.propSgnKeitaiTel.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnKeitaiTel.name}"></h:outputText></TH>
															<TD><h:outputText
																styleClass="outputText" id="htmlSgnKeitaiTel"
																style="#{pc_Nsc01003T01.propSgnKeitaiTel.style}"
																value="#{pc_Nsc01003T01.propSgnKeitaiTel.stringValue}"></h:outputText></TD>
															<TH class="v_g" width="150"><h:outputText
																styleClass="outputText" id="lblSgnFax" 
																style="#{pc_Nsc01003T01.propSgnFax.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnFax.name}"></h:outputText></TH>
															<TD colspan="3"><h:outputText
																styleClass="outputText" id="htmlSgnFax"
																style="#{pc_Nsc01003T01.propSgnFax.style}"
																value="#{pc_Nsc01003T01.propSgnFax.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_a" width="150"><h:outputText
																styleClass="outputText" id="lblSgnEMail"
																style="#{pc_Nsc01003T01.propSgnEMail.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnEMail.name}"></h:outputText></TH>
															<TD colspan="5"><h:outputText
																styleClass="outputText" id="htmlSgnEMail"
																style="#{pc_Nsc01003T01.propSgnEMail.style}"
																value="#{pc_Nsc01003T01.propSgnEMail.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH style="" class="v_b" width="150"><h:outputText
																styleClass="outputText" id="lblSgnAddrNo"
																style="#{pc_Nsc01003T01.propSgnAddrNo.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnAddrNo.name}"></h:outputText></TH>
															<TD colspan="5"><h:outputText 
																styleClass="outputText" id="htmlSgnAddrNo"
																style="#{pc_Nsc01003T01.propSgnAddrNo.style}"
																value="#{pc_Nsc01003T01.propSgnAddrNo.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH style="" class="v_c" width="150" rowspan="3"><h:outputText
																styleClass="outputText" id="lblSgnAddr"
																style="#{pc_Nsc01003T01.propSgnAddr1.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnAddr1.name}"></h:outputText></TH>
															<TD colspan="5" style="border-bottom-style:none;"><h:outputText
																styleClass="outputText" id="htmlSgnAddr1"
																style="#{pc_Nsc01003T01.propSgnAddr1.style}"
																value="#{pc_Nsc01003T01.propSgnAddr1.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="5" style="border-top-style:none;border-bottom-style:none;"><h:outputText
																styleClass="outputText" id="htmlSgnAddr2"
																style="#{pc_Nsc01003T01.propSgnAddr2.style}"
																value="#{pc_Nsc01003T01.propSgnAddr2.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="5"
																style="border-top-style:none;"><h:outputText
																styleClass="outputText" id="htmlSgnAddr3"
																style="#{pc_Nsc01003T01.propSgnAddr3.style}"
																value="#{pc_Nsc01003T01.propSgnAddr3.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH style="" class="v_d" width="150" rowspan="3"><h:outputText
																styleClass="outputText" id="lblSgnAddrKana"
																style="#{pc_Nsc01003T01.propSgnAddrKana1.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnAddrKana1.name}"></h:outputText></TH>
															<TD colspan="5" style="border-bottom-style:none;"><h:outputText
																styleClass="outputText" id="htmlSgnAddrKana1"
																style="#{pc_Nsc01003T01.propSgnAddrKana1.style}"
																value="#{pc_Nsc01003T01.propSgnAddrKana1.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="5" style="border-top-style:none;border-bottom-style:none;"><h:outputText
																styleClass="outputText" id="htmlSgnAddrKana2"
																style="#{pc_Nsc01003T01.propSgnAddrKana2.style}"
																value="#{pc_Nsc01003T01.propSgnAddrKana2.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="5" style="border-top-style:none;"><h:outputText
																styleClass="outputText" id="htmlSgnAddrKana3"
																style="#{pc_Nsc01003T01.propSgnAddrKana3.style}"
																value="#{pc_Nsc01003T01.propSgnAddrKana3.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_e" width="150"><h:outputText
																styleClass="outputText" id="lblSgnRenraku"
																style="#{pc_Nsc01003T01.propSgnRenraku.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnRenraku.name}"></h:outputText></TH>
															<TD colspan="5"><h:outputText 
																styleClass="outputText" id="htmlSgnRenraku"
																style="#{pc_Nsc01003T01.propSgnRenraku.style}"
																value="#{pc_Nsc01003T01.propSgnRenraku.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_f" width="150"><h:outputText
																styleClass="outputText" id="lblSgnRenrakuTel"
																style="#{pc_Nsc01003T01.propSgnRenrakuTel.labelStyle}"
																value="#{pc_Nsc01003T01.propSgnRenrakuTel.name}"></h:outputText></TH>
															<TD colspan="5"><h:outputText 
																styleClass="outputText" id="htmlSgnRenrakuTel"
																style="#{pc_Nsc01003T01.propSgnRenrakuTel.style}"
																value="#{pc_Nsc01003T01.propSgnRenrakuTel.stringValue}"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

