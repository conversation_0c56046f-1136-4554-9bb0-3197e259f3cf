<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg01701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg01701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

	// 出力対象ラジオボタン変更
	function targetChange(){
		try{
			indirectClick('targetChange');
		} catch (e) {
		}
	}
	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfilm").value = "1";
			var action = document.getElementById("form1:htmlAction").value;
			indirectClick(action);
		} catch (e) {
		}
	}

	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfilm").value = "0";
		} catch (e) {
		}
	}

	// 学籍番号検索アイコン押下時処理
	function openGakusekiNoSearchWindow(thisObj, thisEvent) {
		
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlTargetGakusekiNo";
		openModalWindow(url, "pCob0101", "<%= com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
		return true;
	}
	
	// 学生氏名を取得する
	function getGakuseiNmOnAjax(thisObj, thisEven, targetLabel) {
	
	    var servlet = "rev/co/CobGakseiAJAX";
	    var args = new Array();
	    args['code1'] = thisObj.value;
	
	    var ajaxUtil = new AjaxUtil();
	    ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	var schSbtCd = "";
	function getSchoolingSbtCb() {
		// スクーリング種別コンボボックス取得AJAX
		var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
		var args = new Array();
		args['nendo'] = document.getElementById('form1:htmlNendo').value;
		args['bunrui'] = "1";
		args['kakuteiKbn'] = "40";
		var target = "";
		
		comb = document.getElementById('form1:htmlSchooling');
		schSbtCd = comb.options[comb.selectedIndex].value;
		
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getPluralValue(servlet, target, args);
	}
	
	function callBackMethod(value){
		var comb = document.getElementById('form1:htmlSchooling');
		var length = value['length'];
		comb.length = length;
		for(i = 0; i < length; i++){
			comb.options[i].value = value['key' + i];
			comb.options[i].text = value['value' + i];
			if(i == 0){
				comb.options[i].selected = true;
			}
			if(schSbtCd == comb.options[i].value){
				comb.options[i].selected = true;
			}
		}
	}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg01701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg01701.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg01701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg01701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5" width="680">
				<TBODY>
					<TR>
						<TD width="680" valign="top">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_b" width="200">
											<h:outputText styleClass="outputText"
												id="lblNendo"
												value="#{pc_Xrg01701.propNendo.labelName}" 
												style="#{pc_Xrg01701.propNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="480">
											<h:inputText styleClass="inputText"
												id="htmlNendo"
												value="#{pc_Xrg01701.propNendo.dateValue}"
												style="#{pc_Xrg01701.propNendo.style}" size="10"
												onblur="getSchoolingSbtCb();"
												tabindex="1" >
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="inactive" promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_b" width="200">
											<h:outputText styleClass="outputText" id="lblSchoolingSbtCd" 
												value="#{pc_Xrg01701.propSchooling.labelName}" 
												style="#{pc_Xrg01701.propSchooling.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlSchooling"
												value="#{pc_Xrg01701.propSchooling.value}"
												tabindex="2" style="width: 200px">
												<f:selectItems
													value="#{pc_Xrg01701.propSchooling.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_a" width="200">
											<h:outputText styleClass="outputText" id="lblKekkaTsuchiHakkobi"
												value="#{pc_Xrg01701.propKekkaTsuchiHakkobi.labelName}"
												style="#{pc_Xrg01701.propKekkaTsuchiHakkobi.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputText id="htmlKekkaTsuchiHakkobi"
												styleClass="inputText"
												readonly="#{pc_Xrg01701.propKekkaTsuchiHakkobi.readonly}"
												style="#{pc_Xrg01701.propKekkaTsuchiHakkobi.style}"
												value="#{pc_Xrg01701.propKekkaTsuchiHakkobi.dateValue}"
												tabindex="3">
												<f:convertDateTime />
												<hx:inputHelperDatePicker />
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_a" width="200">
											<h:outputText styleClass="outputText" id="lblTsushinran"
												value="通信欄 (1行25文字4行以内)"
												style="#{pc_Xrg01701.propTsushinran.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputTextarea
												styleClass="inputTextarea" id="htmlTsushinran" cols="45" rows="4"
												value="#{pc_Xrg01701.propTsushinran.stringValue}"
												style="#{pc_Xrg01701.propTsushinran.style}"
												tabindex="4" >
											</h:inputTextarea>
										</TD>
									</TR>
									<TR>
										<TH class="v_a" width="200">
											<h:outputText styleClass="outputText" id="lblTargetOutput"
												value="#{pc_Xrg01701.propTargetOutput.labelName}"
												style="#{pc_Xrg01701.propTargetOutput.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneRadio
												disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlTargetOutput"
												value="#{pc_Xrg01701.propTargetOutput.stringValue}"
												disabled="#{pc_Xrg01701.propTargetOutput.disabled}"
												style="#{pc_Xrg01701.propTargetOutput.style}"
												onclick="targetChange()"
												tabindex="5" >
												<f:selectItems
													value="#{pc_Xrg01701.propTargetOutput.list}" />
											</h:selectOneRadio>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="5" width="680" class="table">
				<TBODY>
					<TR>
						<TH class="v_c" width="200"><h:outputText styleClass="outputText"
							id="lblTargetGakusekiNo"
							value="#{pc_Xrg01701.propTargetGakusekiNo.labelName}"
							style="#{pc_Xrg01701.propTargetGakusekiNo.labelStyle}">
						</h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
								id="htmlTargetGakusekiNo" tabindex="6"
								value="#{pc_Xrg01701.propTargetGakusekiNo.stringValue}"
								readonly="#{pc_Xrg01701.propTargetGakusekiNo.readonly}"
								style="#{pc_Xrg01701.propTargetGakusekiNo.style}"
								disabled="#{pc_Xrg01701.propTargetGakusekiNo.disabled}"
								maxlength="#{pc_Xrg01701.propTargetGakusekiNo.maxLength}" size="20"
								onblur="return getGakuseiNmOnAjax(this, event, 'form1:htmlGakuseiName');">
							</h:inputText>
							<hx:commandExButton type="button"
								styleClass="commandExButton_search" id="searchGakusekiNo"
								onclick="return openGakusekiNoSearchWindow(this, event);"
								tabindex="7"
								disabled="#{pc_Xrg01701.propSearchGakusekiNo.disabled}"
								style="#{pc_Xrg01701.propSearchGakusekiNo.style}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="追加"
								styleClass="commandExButton" id="addGakusei"
								action="#{pc_Xrg01701.doAddAction}" tabindex="8"
								disabled="#{pc_Xrg01701.propAdd.disabled}"
								style="#{pc_Xrg01701.propAdd.style}"
								rendered="#{pc_Xrg01701.propAdd.rendered}">
							</hx:commandExButton>
							<h:outputText styleClass="outputText"
								id="htmlGakuseiName"
								value="#{pc_Xrg01701.propTargetGakusekiName.displayValue}"
								style="#{pc_Xrg01701.propTargetGakusekiName.style}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="5" width="680">
				<TBODY>
					<TR>
						<TD align="left">
							<h:outputText styleClass="outputText" id="lblTargetStudentList"
								value="#{pc_Xrg01701.propTargetStudentList.labelName}"
								style="#{pc_Xrg01701.propTargetStudentList.labelStyle}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TD>
							<CENTER>
								<TABLE width="680">
									<TBODY>
										<TR>
											<TD align="left" width="500">
												<h:selectManyListbox
													styleClass="selectManyListbox"
													id="htmlTargetStudentList" size="10" tabindex="9"
													value="#{pc_Xrg01701.propTargetStudentList.value}"
													readonly="#{pc_Xrg01701.propTargetStudentList.readonly}"
													disabled="#{pc_Xrg01701.propTargetStudentList.disabled}"
													style="width:100%">
													<f:selectItems
													value="#{pc_Xrg01701.propTargetStudentList.list}" />
												</h:selectManyListbox>										
											</TD>
											<TD align="left" valign="top">
												<hx:commandExButton type="submit"
													value="全て除外" styleClass="commandExButton" id="removeAll"
													action="#{pc_Xrg01701.doRemoveAllAction}" 
													disabled="#{pc_Xrg01701.propRemoveAll.disabled}"
													tabindex="10">
												</hx:commandExButton>
												<BR/>
												(複数選択可)
												<BR/>
												<hx:commandExButton type="submit"
													value="　除外　" styleClass="commandExButton" id="remove"
													action="#{pc_Xrg01701.doRemoveAction}"
													disabled="#{pc_Xrg01701.propRemove.disabled}"
													tabindex="11">
												</hx:commandExButton>
											</TD>
										</TR>
										<TR>
											<TD align="center" rowspan="2">
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="makePDF" tabindex="12" value="PDF作成"
													action="#{pc_Xrg01701.doMakePDFAction}" 
													disabled="#{pc_Xrg01701.propMakePDF.disabled}"
													style="#{pc_Xrg01701.propMakePDF.style}">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</CENTER>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrg01701.propConfilm.stringValue}"
				id="htmlConfilm">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrg01701.propAction.stringValue}"
				id="htmlAction">
			</h:inputHidden>
			<hx:commandExButton type="submit"
				styleClass="commandExButton" id="targetChange"
				action="#{pc_Xrg01701.doTargetChangeAction}"
				style="display:none;">
			</hx:commandExButton>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

