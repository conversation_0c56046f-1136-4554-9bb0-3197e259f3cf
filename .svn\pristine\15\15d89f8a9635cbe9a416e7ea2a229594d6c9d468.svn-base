<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_COB_SGKS_MEI" name="所属学科組織明細" prod_id="CO" description="学科組織の明細情報を持ちます。">
<STATMENT><![CDATA[
COB_SGKS_MEI
]]></STATMENT>
<COLUMN id="SZK_GAKKA_CD" name="所属学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="所属する学科組織コードです。"/><COLUMN id="SGKS_MEISAI_NO" name="所属学科組織明細ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="所属学科組織の明細番号が設定されます。"/><COLUMN id="SGKS_MEI_NAME" name="所属学科組織明細名称" type="string" length="50" lengthDP="0" byteLength="150" description="所属学科組織の明細名称が設定されます。"/><COLUMN id="SGKS_MEI_NAME_RYAK" name="所属学科組織明細略称" type="string" length="16" lengthDP="0" byteLength="48" description="所属学科組織の明細略称が設定されます。"/><COLUMN id="SGKS_MEI_NAME_ENG" name="所属学科組織明細名称＿英語" type="string" length="70" lengthDP="0" byteLength="210" description="所属学科組織の明細名称（英語）が設定されます。"/><COLUMN id="GAKUSI_NAME" name="学士名称" type="string" length="50" lengthDP="0" byteLength="150" description="所属学科組織の学士名称が設定されます。"/><COLUMN id="GAKUSI_NAME_ENG" name="学士名称＿英語" type="string" length="70" lengthDP="0" byteLength="210" description="所属学科組織の学士名称（英語）が設定されます。"/><COLUMN id="HYOJUN_ZAIGAKU_NENGEN" name="標準在学年限" type="number" length="1" lengthDP="0" byteLength="0" description="最上位レベルの所属学科組織毎に設定する標準在学年数です。下位レベルの学科組織では、最上位レベルで設定された年数をそのまま採用し、設定されません。"/><COLUMN id="HYOJUN_ZAIGAKU_SEMESTER" name="標準在学セメスタ" type="number" length="2" lengthDP="0" byteLength="0" description="最上位レベルの所属学科組織毎に設定する標準在学セメスタ数です。下位レベルの学科組織では、最上位レベルで設定された年数をそのまま採用し、設定されません。
"/><COLUMN id="SAIKO_ZAIGAKU_NENGEN" name="最高在学年限" type="number" length="2" lengthDP="0" byteLength="0" description="最上位レベルの所属学科組織毎に設定する最高在学年数です。下位レベルの学科組織では、最上位レベルで設定された年数をそのまま採用し、設定されません。
※EUC用項目"/><COLUMN id="SAIKO_ZAIGAKU_SEMESTER" name="最高在学セメスタ" type="number" length="2" lengthDP="0" byteLength="0" description="最上位レベルの所属学科組織毎に設定する最高在学セメスタ数です。下位レベルの学科組織では、最上位レベルで設定された年数をそのまま採用し、設定されません。
※EUC用項目"/><COLUMN id="NENKAN_SEMESTER" name="年間セメスタ" type="number" length="1" lengthDP="0" byteLength="0" description="最上位レベルの所属学科組織毎に設定する年間セメスタ数です。下位レベルの学科組織では、最上位レベルで設定された年数をそのまま採用し、設定されません。
"/><COLUMN id="PATTERN_NO" name="学期名称パターンＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="学期名称のパターンＮＯが設定されます。
最上位レベルの学科組織のみ必須項目です。"/><COLUMN id="IDO_SYUTGAK_SBT_CD" name="異動出学種別コード" type="string" length="2" lengthDP="0" byteLength="2" description="異動出学種別を表す一意のコードが設定されます。卒業要件名称に出力する際に使用します。当項目には、異動出学区分が「２：出学」のデータのみ登録可能です。"/>
</TABLE>
