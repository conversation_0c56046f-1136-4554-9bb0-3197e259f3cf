<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos03902T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<TITLE>Cos03902T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css" title="Style">

<SCRIPT type="text/javascript">
	function func_check_on(listId, selectedId) {
		check(listId, selectedId);
	}
	function func_check_off(listId, selectedId) {
		uncheck(listId, selectedId);
	}
	function confirmOk() {
		document.getElementById('form1:htmlHidwarningFlg').value = "1";
		if (document.getElementById('form1:htmlHidActionKbn').value == "1") {
			indirectClick('register');
		}else if (document.getElementById('form1:htmlHidActionKbn').value == "2") {
			indirectClick('delete');
		}
	}		
		
	function confirmCancel() {
		document.getElementById('form1:htmlHidwarningFlg').value = "0";
		document.getElementById('form1:htmlHidActionKbn').value = "0";
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
		<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cos03902T01.onPageLoadBegin}">
			<h:form styleClass="form" id="form1">
				<!--↓outer↓-->
				<DIV class="outer">
					<!--↓head↓-->
					<!-- ヘッダーインクルード -->
					<jsp:include page="../../rev/inc/header.jsp">
						<hx:panelBox styleClass="panelBox" id="boxHeader"></hx:panelBox>
					</jsp:include><!-- ヘッダーへのデータセット領域 -->
					<DIV style="display:none;"><hx:commandExButton type="submit"
						value="閉じる" styleClass="commandExButton" id="closeDisp"
						action="#{pc_Cos03902T01.doCloseDispAction}"></hx:commandExButton><h:outputText
						styleClass="outputText" id="htmlFuncId"
						value="#{pc_Cos03902T01.funcId}"></h:outputText><h:outputText
						styleClass="outputText" id="htmlLoginId"
						value="#{SYSTEM_DATA.loginID}"></h:outputText><h:outputText
						styleClass="outputText" id="htmlScrnName"
						value="#{pc_Cos03902T01.screenName}"></h:outputText>
					</DIV>
					<table border="0" width="98%">
						<tr>
							<td align="left">
								<FIELDSET class="fieldset_err">
									<LEGEND>エラーメッセージ</LEGEND>
									<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
										styleClass="outputText" escape="false"
										style="color: red; font-size: 8pt; font-weight: bold">
									</h:outputText>
								</FIELDSET>
							</td>
							<td align="right"><%-- 戻るボタン --%>
								<hx:commandExButton type="submit" value="戻る" styleClass="commandExButton" id="returnDisp"
									action="#{pc_Cos03902T01.doReturnDispAction}">
								</hx:commandExButton>
							</td>
						</tr>
					</table>
					<!--↑head↑-->
					
					<!--↓content↓-->
					<DIV id="content">
						<DIV class="column" align="center">
							<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
								<TBODY>
									<TR>
										<TD align="center" height=""><%-- ↓↓↓ コンテンツ ↓↓↓ --%>
											<table border="0" width="800">
												<tr>
													<td nowrap align="center">
														<TABLE border="0" width="800" cellpadding="4" class="table">
															<TBODY>
																<TR>
																	<TH width="130" class="v_a"><h:outputText
																		styleClass="outputText" id="labelkengenPtnCd"
																		value="#{pc_Cos03902T01.propKengenPtnCd.labelName}"
																		style="#{pc_Cos03902T01.propKengenPtnCd.labelStyle}">
																	</h:outputText></TH>
																	<TD width="130"><h:outputText styleClass="outputText"
																		value="#{pc_Cos03902T01.propKengenPtnCd.stringValue}"
																		title="#{pc_Cos03902T01.propKengenPtnCd.stringValue}"
																		style="#{pc_Cos03902T01.propKengenPtnCd.style}"
																		id="htmlKengenPtnCd">
																	</h:outputText></TD>
																	<TH width="130" class="v_a"><h:outputText
																		styleClass="outputText" id="labelkengenPtnName"
																		value="#{pc_Cos03902T01.propKengenPtnName.labelName}"
																		style="#{pc_Cos03902T01.propKengenPtnName.labelStyle}">
																	</h:outputText></TH>
																	<TD width="410"><h:outputText styleClass="outputText"
																		value="#{pc_Cos03902T01.propKengenPtnName.stringValue}"
																		title="#{pc_Cos03902T01.propKengenPtnName.stringValue}"
																		style="#{pc_Cos03902T01.propKengenPtnName.style}"
																		id="htmlKengenPtnName">
																	</h:outputText></TD>
																</TR>
																<TR>
																	<TH width="130" class="v_a"><h:outputText
																		styleClass="outputText" id="labelManagerUserId"
																		value="#{pc_Cos03902T01.propManagerUserId.labelName}"
																		style="#{pc_Cos03902T01.propManagerUserId.labelStyle}">
																	</h:outputText></TH>
																	<TD colspan=3><h:outputText styleClass="outputText"
																		value="#{pc_Cos03902T01.propManagerUserId.stringValue}"
																		title="#{pc_Cos03902T01.propManagerUserId.stringValue}"
																		style="#{pc_Cos03902T01.propManagerUserId.style}"
																		id="htmlManagerUserId">
																	</h:outputText></TD>
																</TR>
															</TBODY>
														</TABLE>
													</td>
												</tr>
											</table>
											<HR noshade class="hr">
											<%-- タブ --%>
											<table border="0" cellpadding="0" cellspacing="0" width="97%">
												<TBODY>
													<TR>
														<TD nowrap="nowrap" align="left">
															<hx:commandExButton
																type="submit"
																action="#{pc_Cos03902T01.doHtmlLinkSgksAction}"
																value="#{pc_Cos03902T01.propSgksTabBtn.name}"
																disabled="#{pc_Cos03902T01.propSgksTabBtn.disabled}"
																rendered="#{pc_Cos03902T01.propSgksTabBtn.rendered}"
																styleClass="#{pc_Cos03902T01.propSgksTabBtn.style}"
																id="htmlSgksTabBtn" style="width: 140px;"></hx:commandExButton><hx:commandExButton
																type="submit"
																action="#{pc_Cos03902T01.doHtmlLinkBsyoAction}"
																value="#{pc_Cos03902T01.propBsyoTabBtn.name}"
																disabled="#{pc_Cos03902T01.propBsyoTabBtn.disabled}"
																rendered="#{pc_Cos03902T01.propBsyoTabBtn.rendered}"
																styleClass="#{pc_Cos03902T01.propBsyoTabBtn.style}"
																id="htmlBsyoTabBtn" style="width: 140px;"></hx:commandExButton>
														</TD>
													</TR>
													<TR>
														<TD>
															<table width="100%" class="tab_body" border="0" cellpadding="0"
																cellspacing="0">
																<tr>
																	<td>
																		<%-- 所属学科組織一覧 (上部) --%>
																		<table border="0" width="800" height="20">
																			<tr>
																				<td align="right">
																					<h:outputText styleClass="outputText" value="#{pc_Cos03902T01.propSgksList.listCount}"></h:outputText>
																					<h:outputText styleClass="outputText" value="件"></h:outputText>
																				</td>
																			</tr>
																		</table>
																		<table width="800" border="0" cellpadding="0" cellspacing="0">
																			<tr>
																				<td>
																					<div style="height: 148px;" id="htmlSgksListDiv"
																						onscroll="setScrollPosition('htmlSgksListScrollPosition', this);" class="listScroll">
																						<h:dataTable id="htmlSgksList"
																							border="0" cellpadding="2" cellspacing="0"
																							columnClasses="columnClass1" headerClass="headerClass" footerClass="footerClass"
																							rowClasses="#{pc_Cos03902T01.propSgksList.rowClasses}" styleClass="meisai_scroll"
																							value="#{pc_Cos03902T01.propSgksList.list}" var="varlist">

																							<h:column>
																								<h:selectBooleanCheckbox
																									id="htmlSgksListSelected"
																									styleClass="selectBooleanCheckbox"
																									value="#{varlist.selected}"
																									rendered="#{varlist.rendered}">
																								</h:selectBooleanCheckbox>
																								<f:facet name="header"></f:facet>
																								<f:attribute value="20" name="width" />
																							</h:column>

																							<h:column>
																								<h:outputText
																									styleClass="outputText"
																									value="#{varlist.sotSgksKbn}"
																									style = "text-align: center">
																								</h:outputText>
																								<f:facet name="header">
																								</f:facet>
																								<f:attribute value="text-align: center; vertical-align: middle" name="style" />
																								<f:attribute value="20" name="width" />
																							</h:column>

																							<h:column>
																								<h:outputText
																									styleClass="outputText"
																									value="#{varlist.sgksCd}"
																									title="#{varlist.sgksCd}">
																								</h:outputText>
																								<f:facet name="header">
																									<h:outputText styleClass="outputText" value="#{pc_Cos03902T01.propSgksCd.labelName}"></h:outputText>
																								</f:facet>
																								<f:attribute value="150" name="width" />
																							</h:column>

																							<h:column>
																								<hx:jspPanel>
																									<div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 99%;">
																										<h:outputText styleClass="outputText" value="#{varlist.sgksName}" title="#{varlist.sgksName}"></h:outputText>
																									</div>
																								</hx:jspPanel>
																								<f:facet name="header">
																									<h:outputText styleClass="outputText" value="#{pc_Cos03902T01.propSgksName.labelName}"></h:outputText>
																								</f:facet>
																								<f:attribute value="610" name="width" />
																							</h:column>
																						</h:dataTable>
																					</div>
																				</td>
																			</tr>
																			<tr>
																				<td class="footerClass">
																					<table width="100%">
																						<tr>
																							<td align="left">
																								<hx:panelBox styleClass="panelBox">
																									<hx:jspPanel>
																										<INPUT type="button" name="check" value="on"
																											onclick="return func_check_on('htmlSgksList','htmlSgksListSelected');" class="check">
																										<INPUT type="button" name="uncheck" value="off"
																											onclick="return func_check_off('htmlSgksList','htmlSgksListSelected');" class="uncheck">
																									</hx:jspPanel>
																								</hx:panelBox>
																							</td>
																						</tr>
																					</table>
																				</td>
																			</tr>
																			<tr>
																				<td align="center">
																					<hx:commandExButton type="submit" value="追加" id="register"
																						styleClass="commandExButton_dat" style="margin: 5px;"
																						action="#{pc_Cos03902T01.doRegisterAction}"
																						disabled="#{pc_Cos03902T01.propRegistBtn.disabled}"
																						confirm="#{msg.SY_MSG_0003W}">
																					</hx:commandExButton>
																				</td>
																			</tr>
																		</table>
																		
																		<%-- 対象所属学科組織一覧 (下部) --%>
																		<table border="0" width="800" height="20">
																			<tr>
																				<td align="right">
																					<h:outputText styleClass="outputText" value="#{pc_Cos03902T01.propAuthSgksList.listCount}"></h:outputText>
																					<h:outputText styleClass="outputText" value="件"></h:outputText>
																				</td>
																			</tr>
																		</table>
																		<table width="800" border="0" cellpadding="0" cellspacing="0" style="margin-bottom: 10px;">
																			<tr>
																				<td>
																					<div style="height: 148px;" id="htmlAuthSgksListDiv"
																						onscroll="setScrollPosition('htmlAuthSgksListScrollPosition', this);" class="listScroll">
																						<h:dataTable id="htmlAuthSgksList"
																							border="0" cellpadding="2" cellspacing="0"
																							columnClasses="columnClass1" headerClass="headerClass" footerClass="footerClass"
																							rowClasses="#{pc_Cos03902T01.propAuthSgksList.rowClasses}" styleClass="meisai_scroll"
																							value="#{pc_Cos03902T01.propAuthSgksList.list}" var="varlist">

																							<h:column>
																								<h:selectBooleanCheckbox
																									id="htmlAuthSgksListSelected"
																									styleClass="selectBooleanCheckbox"
																									value="#{varlist.selected}"
																									rendered="#{varlist.rendered}">
																								</h:selectBooleanCheckbox>
																								<f:facet name="header"></f:facet>
																								<f:attribute value="20" name="width" />
																							</h:column>

																							<h:column>
																								<h:outputText
																									styleClass="outputText"
																									value="#{varlist.sotSgksKbn}">
																								</h:outputText>
																								<f:facet name="header">
																								</f:facet>
																								<f:attribute value="text-align: center; vertical-align: middle" name="style" />
																								<f:attribute value="20" name="width" />
																							</h:column>

																							<h:column>
																								<h:outputText
																									styleClass="outputText"
																									value="#{varlist.sgksCd}"
																									title="#{varlist.sgksCd}">
																								</h:outputText>
																								<f:facet name="header">
																									<h:outputText styleClass="outputText" value="#{pc_Cos03902T01.propSgksCd.labelName}"></h:outputText>
																								</f:facet>
																								<f:attribute value="150" name="width" />
																							</h:column>

																							<h:column>
																								<hx:jspPanel>
																									<div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 99%;">
																										<h:outputText styleClass="outputText" value="#{varlist.sgksName}" title="#{varlist.sgksName}"></h:outputText>
																									</div>
																								</hx:jspPanel>
																							
																								<f:facet name="header">
																									<h:outputText styleClass="outputText" value="#{pc_Cos03902T01.propSgksName.labelName}"></h:outputText>
																								</f:facet>
																								<f:attribute value="610" name="width" />
																							</h:column>
																						</h:dataTable>
																					</div>
																				</td>
																			</tr>
																			<tr>
																				<td class="footerClass">
																					<table width="100%">
																						<tr>
																							<td align="left">
																								<hx:panelBox styleClass="panelBox">
																									<hx:commandExButton type="submit" value="削除"
																										styleClass="commandExButton" id="delete"
																										action="#{pc_Cos03902T01.doDeleteAction}"
																										disabled="#{pc_Cos03902T01.propDeleteBtn.disabled}"
																										confirm="#{msg.SY_MSG_0004W}">
																									</hx:commandExButton>
																									<hx:jspPanel>
																										<INPUT type="button" name="check" value="on"
																											onclick="return func_check_on('htmlAuthSgksList','htmlAuthSgksListSelected');" class="check">
																										<INPUT type="button" name="uncheck" value="off"
																											onclick="return func_check_off('htmlAuthSgksList','htmlAuthSgksListSelected');" class="uncheck">
																									</hx:jspPanel>
																								</hx:panelBox>
																							</td>
																						</tr>
																					</table>
																				</td>
																			</tr>
																			<TR>
																				<td align="left">
																					<h:outputText styleClass="note"
																					id="lblCmt" value="※先頭にアスタリスク（*）のある所属学科組織は卒業生のみに使用されています"></h:outputText>
																				</td>
																			</TR>
																		</table>
																	</TD>
																</TR>
															</TABLE>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</DIV>
						
						<%-- ↓↓↓ hidden項目、隠しボタン ↓↓↓ --%>
						<h:inputHidden value="#{pc_Cos03902T01.propSgksList.scrollPosition}" id="htmlSgksListScrollPosition"></h:inputHidden>
						<h:inputHidden value="#{pc_Cos03902T01.propAuthSgksList.scrollPosition}" id="htmlAuthSgksListScrollPosition"></h:inputHidden>
						<h:inputHidden value="#{pc_Cos03902T01.propHidWarningFlg.integerValue}" id="htmlHidwarningFlg"><f:convertNumber /></h:inputHidden>
						<h:inputHidden value="#{pc_Cos03902T01.propHidActionKbn.integerValue}" id="htmlHidActionKbn"><f:convertNumber /></h:inputHidden>
						<%-- ↑↑↑ hidden項目、隠しボタン ↑↑↑ --%>
					</DIV>
					<!--↑content↑-->
					<!--↓foot↓-->
					<jsp:include page="../inc/footer.jsp" />
					<!--↑foot↑-->
				</DIV>
				<!--↑outer↑-->
			</h:form>
		</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', function() {
		changeScrollPosition('htmlSgksListScrollPosition', 'htmlSgksListDiv');
		changeScrollPosition('htmlAuthSgksListScrollPosition', 'htmlAuthSgksListDiv');
	});
</SCRIPT>

</HTML>

