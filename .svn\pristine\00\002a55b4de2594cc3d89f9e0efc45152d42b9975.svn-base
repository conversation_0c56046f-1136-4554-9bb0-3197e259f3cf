<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Gha00702.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Gha00702.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/rev/inc/gakuen.css" >
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript">

	//非活性化関数
	//@param thisObj		キー用オブジェクト
	//@param id				アクションID
	function funcGetBunmenTemp(thisObj, id) {
		//アクション送信
		indirectClick(id);
		//連続変更防止の為、一旦非活性化
		thisObj.disabled = true;
	}

	// ラジオボタン変更時にサーバに飛ぶ処理（隠しボタンをクリックする。）
	function RadioChange() {
		try{
			window.document.getElementById("form1:hideButton").click();
		}
		catch (e) {
		}
	}

	// ラジオボタン変更時にサーバに飛ぶ処理（隠しボタンをクリックする。）
	function RadioChange2() {
		try{
			window.document.getElementById("form1:hideButton2").click();
		}
		catch (e) {
		}
	}
	
	function confirmOk() {
		document.getElementById('form1:htmlHidwarningFlg').value = "1";
		indirectClick('next');
	}		
		
	function confirmCancel() {
		document.getElementById('form1:htmlHidwarningFlg').value = "0";
	}
	
	
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Gha00702.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Gha00702.doCloseDispAction}"></hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Gha00702.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Gha00702.screenName}"></h:outputText>
				<hx:commandExButton type="button" type="submit" value="ラジオチェンジ明細表示フラグ"
					styleClass="commandExButton" id="hideButton" action="#{pc_Gha00702.doMeisaiRadioAction}">
				</hx:commandExButton>
				<hx:commandExButton type="button" type="submit" value="ラジオチェンジ明細免除表示フラグ"
					styleClass="commandExButton" id="hideButton2" action="#{pc_Gha00702.doMeisaiMenjoRadioAction}">
				</hx:commandExButton>
            </div>  
            <!--↓outer↓-->
            <DIV class="outer">

                <FIELDSET class="fieldset_err">
                	<LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

				<!--↓content↓-->
				<DIV class="head_button_area" >
					<!-- ↓ここに戻る／閉じるボタンを配置 -->
					<hx:commandExButton
						type="submit"
						value="戻る"
						styleClass="commandExButton_etc"
						id="returnDisp"
						action="#{pc_Gha00702.doReturnAction}">
					</hx:commandExButton>
					<!-- ↑ここに戻る／閉じるボタンを配置 -->
				</DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE width="900px">
							<TBODY>
								<TR>
									<TD height="40px"></TD>
								</TR>
								<TR align="center" valign="middle">
									<TD>
										<TABLE border="0" width="100%" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
													<TH nowrap class="v_a" width="150px">
														<!-- 文面区分 -->
														<h:outputText
															styleClass="outputText"
															id="lblBunmenKbnOut"
															value="#{pc_Gha00702.propBunmenKbnName.labelName}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:outputText
															styleClass="outputText"
															id="lblBunmenKbn"
															value="#{pc_Gha00702.propBunmenKbnName.stringValue}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_b" width="150px">
														<!-- 文面枝番 -->
														<h:outputText
															styleClass="outputText"
															id="lblBunmenEdabanOut"
															value="#{pc_Gha00702.propBunmenEdaban.labelName}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:outputText
															styleClass="outputText"
															id="lblBunmenEdaban"
															value="#{pc_Gha00702.propBunmenEdaban.stringValue}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_c" width="150px">
														<!-- タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblTitle"
															value="#{pc_Gha00702.propTitle.name}">
														</h:outputText>
													</TH>
													<TD valign="middle" nowrap width="*">
														<h:outputText
															styleClass="outputText"
															id="htmlTitle"
															value="#{pc_Gha00702.propTitle.stringValue}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_e" width="150px">
													<!-- 明細表示 -->
														<h:outputText
															styleClass="outputText"
															id="lblPayMeisaiHyojiFlg"
															value="#{pc_Gha00702.propPayMeisaiHyojiFlg.labelName}"
															style="#{pc_Gha00702.propPayMeisaiHyojiFlg.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:selectOneRadio
															id="htmlPayMeisaiHyojiFlg"
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio"
															disabled="#{pc_Gha00702.propPayMeisaiHyojiFlg.disabled}"
															value="#{pc_Gha00702.propPayMeisaiHyojiFlg.value}"
															onclick="return RadioChange();">
															<f:selectItems value="#{pc_Gha00702.propPayMeisaiHyojiFlg.list}" />
														</h:selectOneRadio>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_f" width="150px">
													<!-- 明細免除表示 -->
														<h:outputText
															styleClass="outputText"
															id="lblPayMeisaiMenjoHyojiFlg"
															value="#{pc_Gha00702.propPayMeisaiMenjoHyojiFlg.labelName}"
															style="#{pc_Gha00702.propPayMeisaiMenjoHyojiFlg.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:selectOneRadio
															id="htmlPayMeisaiMenjoHyojiFlg"
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio"
															disabled="#{pc_Gha00702.propPayMeisaiMenjoHyojiFlg.disabled}"
															value="#{pc_Gha00702.propPayMeisaiMenjoHyojiFlg.value}"
															onclick="return RadioChange2();">
															<f:selectItems value="#{pc_Gha00702.propPayMeisaiMenjoHyojiFlg.list}" />
														</h:selectOneRadio>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_g" width="150px">
													<!-- 明細タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblMeisaiTitle"
															value="#{pc_Gha00702.propMeisaiTitle.labelName}"
															style="#{pc_Gha00702.propMeisaiTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlMeisaiTitle"
															styleClass="inputText"
															size="50"
															maxlength="#{pc_Gha00702.propMeisaiTitle.maxLength}"
															disabled="#{pc_Gha00702.propMeisaiTitle.disabled}"
															value="#{pc_Gha00702.propMeisaiTitle.value}"
															style="#{pc_Gha00702.propMeisaiTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_a" width="150px">
													<!-- 明細ＮＯタイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblMeisaiNoTitle"
															value="#{pc_Gha00702.propMeisaiNoTitle.labelName}"
															style="#{pc_Gha00702.propMeisaiNoTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlMeisaiNoTitle"
															styleClass="inputText"
															size="6"
															maxlength="#{pc_Gha00702.propMeisaiNoTitle.maxLength}"
															disabled="#{pc_Gha00702.propMeisaiNoTitle.disabled}"
															value="#{pc_Gha00702.propMeisaiNoTitle.value}"
															style="#{pc_Gha00702.propMeisaiNoTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_b" width="150px">
													<!-- 明細名称タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblMeisaiNameTitle"
															value="#{pc_Gha00702.propMeisaiNameTitle.labelName}"
															style="#{pc_Gha00702.propMeisaiNameTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlMeisaiNameTitle"
															styleClass="inputText"
															size="40"
															maxlength="#{pc_Gha00702.propMeisaiNameTitle.maxLength}"
															disabled="#{pc_Gha00702.propMeisaiNameTitle.disabled}"
															value="#{pc_Gha00702.propMeisaiNameTitle.value}"
															style="#{pc_Gha00702.propMeisaiNameTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_c" width="150px">
													<!-- 滞納金額タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblTainoKinTitle"
															value="#{pc_Gha00702.propTainoKinTitle.labelName}"
															style="#{pc_Gha00702.propTainoKinTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlTainoKinTitle"
															styleClass="inputText"
															size="12"
															maxlength="#{pc_Gha00702.propTainoKinTitle.maxLength}"
															disabled="#{pc_Gha00702.propTainoKinTitle.disabled}"
															value="#{pc_Gha00702.propTainoKinTitle.value}"
															style="#{pc_Gha00702.propTainoKinTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_c" width="150px">
													<!-- 入金済金額タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblNyukinzumiKinTitle"
															value="#{pc_Gha00702.propNyukinzumiKinTitle.labelName}"
															style="#{pc_Gha00702.propNyukinzumiKinTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlNyukinzumiKinTitle"
															styleClass="inputText"
															size="12"
															maxlength="#{pc_Gha00702.propNyukinzumiKinTitle.maxLength}"
															disabled="#{pc_Gha00702.propNyukinzumiKinTitle.disabled}"
															value="#{pc_Gha00702.propNyukinzumiKinTitle.value}"
															style="#{pc_Gha00702.propNyukinzumiKinTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_c" width="150px">
													<!-- 明細金額タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblMeisaiKinTitle"
															value="#{pc_Gha00702.propMeisaiKinTitle.labelName}"
															style="#{pc_Gha00702.propMeisaiKinTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlMeisaiKinTitle"
															styleClass="inputText"
															size="12"
															maxlength="#{pc_Gha00702.propMeisaiKinTitle.maxLength}"
															disabled="#{pc_Gha00702.propMeisaiKinTitle.disabled}"
															value="#{pc_Gha00702.propMeisaiKinTitle.value}"
															style="#{pc_Gha00702.propMeisaiKinTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_c" width="150px">
													<!-- 免除金額タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblMenjoKinTitle"
															value="#{pc_Gha00702.propMenjoKinTitle.labelName}"
															style="#{pc_Gha00702.propMenjoKinTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlMenjoKinTitle"
															styleClass="inputText"
															size="12"
															maxlength="#{pc_Gha00702.propMenjoKinTitle.maxLength}"
															disabled="#{pc_Gha00702.propMenjoKinTitle.disabled}"
															value="#{pc_Gha00702.propMenjoKinTitle.value}"
															style="#{pc_Gha00702.propMenjoKinTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_d" width="150px">
													<!-- 合計表示タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblGokeiHyojiTitle"
															value="#{pc_Gha00702.propGokeiHyojiTitle.labelName}"
															style="#{pc_Gha00702.propGokeiHyojiTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlGokeiHyojiTitle"
															styleClass="inputText"
															size="4"
															maxlength="#{pc_Gha00702.propGokeiHyojiTitle.maxLength}"
															disabled="#{pc_Gha00702.propGokeiHyojiTitle.disabled}"
															value="#{pc_Gha00702.propGokeiHyojiTitle.value}"
															style="#{pc_Gha00702.propGokeiHyojiTitle.style}">
														</h:inputText>
													</TD>
												</TR>
												<TR>
													<TH nowrap class="v_d" width="150px">
													<!-- 振込金額タイトル -->
														<h:outputText
															styleClass="outputText"
															id="lblFurikomiTitle"
															value="#{pc_Gha00702.propFurikomiTitle.labelName}"
															style="#{pc_Gha00702.propFurikomiTitle.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText
															id="htmlFurikomiTitle"
															styleClass="inputText"
															size="8"
															maxlength="#{pc_Gha00702.propFurikomiTitle.maxLength}"
															disabled="#{pc_Gha00702.propFurikomiTitle.disabled}"
															value="#{pc_Gha00702.propFurikomiTitle.value}"
															style="#{pc_Gha00702.propFurikomiTitle.style}">
														</h:inputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR align="right">
													<TD align="center">
														<hx:commandExButton
															type="submit"
															value="次へ"
															styleClass="commandExButton_dat"
															id="next"
															action="#{pc_Gha00702.doNextAction}">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton
															type="submit"
															value="クリア"
															styleClass="commandExButton_etc"
															id="clear"
															action="#{pc_Gha00702.doClearAction}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
                <!--↑CONTENT↑-->
            </DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />
            
			<h:inputHidden
				value="#{pc_Gha00702.propHidBunmenKbn.value}"
				id="htmlHidBunmenKbn">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidTemplate.value}"
				id="htmlHidTemplate"
				onchange=>
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidPayMeisaiHyojiFlg.value}"
				id="htmlHidPayMeisaiHyojiFlg">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidPayMeisaiMenjoHyojiFlg.value}"
				id="htmlHidPayMeisaiMenjoHyojiFlg">
			</h:inputHidden>
		
			<h:inputHidden
				value="#{pc_Gha00702.propHidMeisaiTitle.stringValue}"
				id="htmlHidMeisaiTitle">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidMeisaiNoTitle.stringValue}"
				id="htmlHidMeisaiNoTitle">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidMeisaiNameTitle.stringValue}"
				id="htmlHidMeisaiNameTitle">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidMeisaiKinTitle.stringValue}"
				id="htmlHidMeisaiKinTitle">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidMeisaiMenjoTitle.stringValue}"
				id="htmlHidMeisaiMenjoTitle">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidFurikomiTitle.stringValue}"
				id="htmlHidFurikomiTitle">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Gha00702.propHidWarningFlg.integerValue}"
				id="htmlHidwarningFlg">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
    </hx:scriptCollector>
    </BODY>
    
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
