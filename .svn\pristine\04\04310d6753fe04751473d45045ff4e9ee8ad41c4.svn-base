<%-- 
	学籍情報照会（留学生）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob00302T08.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob003002T08.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cob00302T08.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Cob00302T08">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
	value="閉じる" styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cob00302T08.doCloseDispAction}"></hx:commandExButton> <h:outputText
	styleClass="outputText" id="htmlFuncId"
	value="#{pc_Cob00302T08.funcId}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlLoginId"
	value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlScrnName"
	value="#{pc_Cob00302T08.screenName}"></h:outputText></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
	id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText></FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area">
<hx:commandExButton
	type="submit" value="プロファイル"
	styleClass="commandExButton" id="referProfile"
	action="#{pc_Cob00302T01.cob00302.doReferProfileAction}">
</hx:commandExButton>
<hx:commandExButton type="submit"
	value="戻る" styleClass="commandExButton" id="returnDisp"
	action="#{pc_Cob00302T08.doReturnDispAction}"></hx:commandExButton></DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD width="870">
				<TABLE class="table" width="100%">
					<TBODY>
						<TR align="center" valign="middle">
							<TH nowrap class="v_a" width="190">
							<!--学籍番号 -->
								<h:outputText styleClass="outputText" id="lblDspGaksekiCd" 
								value="#{pc_Cob00302T01.cob00302.propGakusekiCd.labelName}"
								style="#{pc_Cob00302T01.cob00302.propGakusekiCd.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblGaksekiCd"
								value="#{pc_Cob00302T01.cob00302.propGakusekiCd.stringValue}"
								style="#{pc_Cob00302T01.cob00302.propGakusekiCd.style}"></h:outputText>
							</TD>
							<TH nowrap class="v_a" width="190">
							<!--学生氏名 -->
								<h:outputText styleClass="outputText" id="lblDspName" 
								value="#{pc_Cob00302T01.cob00302.propName.labelName}"
								style="#{pc_Cob00302T01.cob00302.propName.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblName"
								value="#{pc_Cob00302T01.cob00302.propName.displayValue}"
								title="#{pc_Cob00302T01.cob00302.propName.stringValue}"
								style="#{pc_Cob00302T01.cob00302.propName.style}"></h:outputText>
							</TD>
						</TR>
						<TR>
							<TH nowrap class="v_b" width="190">
							<!-- 学籍状況 -->
								<h:outputText styleClass="outputText" id="lblDspGakJokyo" 
								value="#{pc_Cob00302T01.cob00302.propGakJokyo.labelName}"
								style="#{pc_Cob00302T01.cob00302.propGakJokyo.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblGakJokyo" 
								value="#{pc_Cob00302T01.cob00302.propGakJokyo.stringValue}"></h:outputText></TD>
							<TH nowrap class="v_b" width="190">
							<!-- 旧学籍番号 -->
								<h:outputText styleClass="outputText" id="lblDspKyuGaksekiCd"
								value="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.labelName}"
								style="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.labelStyle}"></h:outputText></TH>
							<TD width="500"><h:outputText styleClass="outputText" id="lblKyuGaksekiCd"
								value="#{pc_Cob00302T01.cob00302.propKyuGakusekiCd.stringValue}"></h:outputText></TD>
						</TR>
					</TBODY>
				</TABLE>
				<BR>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
					<TBODY>
						<TR>
							<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
								style="border-bottom-style: none; ">
								<TBODY>
									<TR>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T01" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameKihon.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T01Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T02" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameShozoku.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T02Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="*"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T03" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameSnkCls.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T03Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T04" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameAddr.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T04Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T05" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameKAddr.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T05Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T06" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameHsy.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T06Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T07" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameAtsk.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T07Action}"></hx:commandExButton></TD>
										<TD class="tab_head_on" width="58px"><hx:commandExButton
											type="button" styleClass="tab_head_on" id="tabCob00301T08" 
											value="#{pc_Cob00302T01.cob00302.propTabNameRyugak.stringValue}"
											style="width: 100%"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T09" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameKyoin.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T09Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T10" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameIdo.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T10Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T11" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameNyushi.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T11Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="57px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T12" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameHantei.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T12Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T13" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameClub.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T13Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T14" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameSonota.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T14Action}"></hx:commandExButton></TD>
										<TD class="tab_head_off" width="58px"><hx:commandExButton
											type="submit" styleClass="tab_head_off" id="tabCob00301T15" style="width:100%"
											value="#{pc_Cob00302T01.cob00302.propTabNameJiyu.stringValue}"
											action="#{pc_Cob00302T08.doTabCob00302T15Action}"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
						<TR>
							<TD>
							<TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" 
								width="100%" style="border-top-style: none; ">
								<TBODY>
									<TR>
										<TD width="100%">
										<div style="height: 440px">
										<BR>
										<TABLE class="table" width="840">
											<TBODY>
												<TR>
													<TH nowrap class="v_a" width="180">
													<!-- 留学生 -->
														<h:outputText styleClass="outputText" id="lblDspRgakFlg"
														value="#{pc_Cob00302T08.propRgakFlg.labelName}"
														style="#{pc_Cob00302T08.propRgakFlg.labelStyle}"></h:outputText></TH>
													<TD width="240"><h:outputText styleClass="outputText" id="htmlRgakFlg"
														value="#{pc_Cob00302T08.propRgakFlg.stringValue}"
														style="#{pc_Cob00302T08.propRgakFlg.style}"></h:outputText></TD>
													<TH nowrap class="v_a" width="180">
													<!-- 留学生カリキュラム -->
														<h:outputText styleClass="outputText" id="lblDspRgakCurFlg"
														value="#{pc_Cob00302T08.propRgakCurFlg.labelName}"
														style="#{pc_Cob00302T08.propRgakCurFlg.labelStyle}"></h:outputText></TH>
													<TD width="240"><h:outputText styleClass="outputText" id="lblRgakCurFlg"
														value="#{pc_Cob00302T08.propRgakCurFlg.stringValue}"
														style="#{pc_Cob00302T08.propRgakCurFlg.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_b">
													<!-- 留学生区分 -->
														<h:outputText styleClass="outputText" id="lblDspRyugakKbnName"
														value="#{pc_Cob00302T08.propRyugakKbnName.labelName}"
														style="#{pc_Cob00302T08.propRyugakKbnName.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlRyugakKbnName"
														value="#{pc_Cob00302T08.propRyugakKbnName.stringValue}"
														style="#{pc_Cob00302T08.propRyugakKbnName.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_c">
													<!-- 外国人登録証明書番号 -->
														<h:outputText styleClass="outputText" id="lblDspSyoumeiNo" 
														value="#{pc_Cob00302T08.propSyoumeiNo.labelName}"
														style="#{pc_Cob00302T08.propSyoumeiNo.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlSyoumeiNo" 
														value="#{pc_Cob00302T08.propSyoumeiNo.stringValue}"
														style="#{pc_Cob00302T08.propSyoumeiNo.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_c">
													<!-- 在留資格コード -->
														<h:outputText styleClass="outputText" id="lblDspZaryuSkkCd"
														value="#{pc_Cob00302T08.propZaryuSkkCd.labelName}"
														style="#{pc_Cob00302T08.propZaryuSkkCd.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlZaryuSkkCd"
														value="#{pc_Cob00302T08.propZaryuSkkCd.stringValue}"
														style="#{pc_Cob00302T08.propZaryuSkkCd.style}"></h:outputText></TD>
													<TH nowrap class="v_c">
													<!-- 在留資格名称 -->
														<h:outputText styleClass="outputText" id="lblDspZaryuSkkName"
														value="#{pc_Cob00302T08.propZaryuSkkName.labelName}"
														style="#{pc_Cob00302T08.propZaryuSkkName.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlZaryuSkkName"
														value="#{pc_Cob00302T08.propZaryuSkkName.stringValue}"
														style="#{pc_Cob00302T08.propZaryuSkkName.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_d">
													<!-- ビザ有効期間 -->
														<h:outputText styleClass="outputText" id="lblDspVisaYukoKikan"
														value="#{pc_Cob00302T08.propVisaYukoKikanFrom.labelName}"
														style="#{pc_Cob00302T08.propVisaYukoKikanFrom.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlVisaYukoKikanFrom"
														value="#{pc_Cob00302T08.propVisaYukoKikanFrom.dateValue}"
														style="#{pc_Cob00302T08.propVisaYukoKikanFrom.style}"><f:convertDateTime /></h:outputText>
														<h:outputText styleClass="outputText" 
														id="lblDspVisaKknText"	value="～"
														rendered="#{pc_Cob00302T08.propVisaYukoKikan.rendered}"></h:outputText>
														<h:outputText styleClass="outputText" id="htmlVisaYukoKikanTo"
														value="#{pc_Cob00302T08.propVisaYukoKikanTo.dateValue}"
														style="#{pc_Cob00302T08.propVisaYukoKikanTo.style}"><f:convertDateTime /></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_e">
													<!-- 渡日年月 -->
														<h:outputText styleClass="outputText" id="lblDspTonichiYm"
														value="#{pc_Cob00302T08.propTonichiYm.labelName}"
														style="#{pc_Cob00302T08.propTonichiYm.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlTonichiYm"
														value="#{pc_Cob00302T08.propTonichiYm.dateValue}"
														style="#{pc_Cob00302T08.propTonichiYm.style}"><f:convertDateTime pattern="yyyy/MM" /></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_f">
													<!-- 保険証番号 -->
														<h:outputText styleClass="outputText" id="lblDspHokensyoNo" 
														value="#{pc_Cob00302T08.propHokensyoNo.labelName}"
														style="#{pc_Cob00302T08.propHokensyoNo.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlHokensyoNo" 
														value="#{pc_Cob00302T08.propHokensyoNo.stringValue}"
														style="#{pc_Cob00302T08.propHokensyoNo.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_f">
													<!-- 旅券番号 -->
														<h:outputText styleClass="outputText" id="lblDspRyokenNo"
														value="#{pc_Cob00302T08.propRyokenNo.labelName}"
														style="#{pc_Cob00302T08.propRyokenNo.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlRyokenNo"
														value="#{pc_Cob00302T08.propRyokenNo.stringValue}"
														style="#{pc_Cob00302T08.propRyokenNo.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_g">
													<!-- 旅券発行年月日 -->
														<h:outputText styleClass="outputText" id="lblDspRyokenDate"
														value="#{pc_Cob00302T08.propRyokenDate.labelName}"
														style="#{pc_Cob00302T08.propRyokenDate.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="lblRyokenDate"
														value="#{pc_Cob00302T08.propRyokenDate.dateValue}"
														style="#{pc_Cob00302T08.propRyokenDate.style}"><f:convertDateTime /></h:outputText></TD>
													<TH nowrap class="v_g">
													<!-- 在留期間 -->
														<h:outputText styleClass="outputText" id="lblDspZaryuKigen"
														value="#{pc_Cob00302T08.propZaryuKigen.labelName}"
														style="#{pc_Cob00302T08.propZaryuKigen.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlZaryuKigen"
														value="#{pc_Cob00302T08.propZaryuKigen.dateValue}"
														style="#{pc_Cob00302T08.propZaryuKigen.style}"><f:convertDateTime /></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_a">
													<!-- 次回確認申請期間 -->
														<h:outputText styleClass="outputText" id="lblDspJikaiSinseiKikan"
														value="#{pc_Cob00302T08.propJikaiSinseiKikan.labelName}"
														style="#{pc_Cob00302T08.propJikaiSinseiKikan.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlJikaiSinseiKikan"
														value="#{pc_Cob00302T08.propJikaiSinseiKikan.dateValue}"
														style="#{pc_Cob00302T08.propJikaiSinseiKikan.style}"><f:convertDateTime /></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_b">
													<!-- 発行者 -->
														<h:outputText styleClass="outputText" id="lblDspHakkosya"
														value="#{pc_Cob00302T08.propHakkosya.labelName}"
														style="#{pc_Cob00302T08.propHakkosya.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlHakkosya"
														value="#{pc_Cob00302T08.propHakkosya.stringValue}"
														style="#{pc_Cob00302T08.propHakkosya.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_c">
													<!-- 資格外活動 -->
														<h:outputText styleClass="outputText" id="lblDspSikakugaiKatudo"
														value="#{pc_Cob00302T08.propSikakugaiKatudo.labelName}"
														style="#{pc_Cob00302T08.propSikakugaiKatudo.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlSikakugaiKatudo"
														value="#{pc_Cob00302T08.propSikakugaiKatudo.stringValue}"
														style="#{pc_Cob00302T08.propSikakugaiKatudo.style}"></h:outputText></TD>
												</TR>
											</TBODY>
										</TABLE>
										</div>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>


<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--></DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/footer.jsp" />

</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
