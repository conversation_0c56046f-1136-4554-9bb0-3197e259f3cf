<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssa00601T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssa00601.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css" title="Style">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<!--
<BODY onLoad="resizeTo('1024', '768');">
-->
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssa00601T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<DIV style="display:none;">
	<hx:commandExButton
		type="submit"
		value="閉じる"
		styleClass="commandExButton"
		id="closeDisp"
		action="#{pc_Ssa00601T01.doCloseDispAction}">
	</hx:commandExButton>
	<h:outputText
		styleClass="outputText"
		id="htmlFuncId"
		value="#{pc_Ssa00601T01.funcId}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlLoginId"
		value="#{SYSTEM_DATA.loginID}">
	</h:outputText>
	<h:outputText
		styleClass="outputText"
		id="htmlScrnName"
		value="#{pc_Ssa00601T01.screenName}">
	</h:outputText>
</DIV>

<!--↓outer↓-->
<DIV class="outer">
	<FIELDSET class="fieldset_err">
		<LEGEND>エラーメッセージ</LEGEND>
		<h:outputText
			id="message"
			value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			styleClass="outputText"
			escape="false">
		</h:outputText>
	</FIELDSET>
	
	<DIV class="head_button_area" >　
	<!-- ↓ここに戻る／閉じるボタンを配置 -->　
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	<!--↓content↓-->
	<DIV id="content">
		<!--↓column↓-->
		<DIV class="column">
		<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="550" border="0" cellpadding="0" cellspacing="0">
			<TBODY>
				<TR>
					<TD>
						<TABLE border="0" class="table" width="730" cellspacing="0" cellpadding="0">
						<TBODY>
							<TR>
								<TH width="20%">
									<h:outputText
										styleClass="outputText"
										id="lblKey"
										value="優先順位">
									</h:outputText></TH>
								<TH width="45%">
									<h:outputText
										styleClass="outputText"
										id="lblItem"
										value="項目">
									</h:outputText></TH>
								<TH width="35%">
									<h:outputText
										styleClass="outputText"
										id="lblSort"
										value="ソート順">
									</h:outputText></TH>
							</TR>
							<TR>
								<TH nowrap class="v_a" width="20%">
									<h:outputText
										styleClass="outputText"
										id="lblSortKey1"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey1.labelName}"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey1.labelStyle}">
									</h:outputText>
								</TH>
								<TD width="45%">
									<h:selectOneMenu
										styleClass="selectOneMenu"
										id="htmlSortKey1"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey1.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey1.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSortKey1.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSortKey1.readonly}">
										<f:selectItems
											value="#{pc_Ssa00601T01.ssa00601.propSortKey1.list}" />
									</h:selectOneMenu>
								</TD>
								<TD width="35%">
									<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio"
										id="htmlSort1"
										style="#{pc_Ssa00601T01.ssa00601.propSort1.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSort1.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSort1.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSort1.readonly}">
										<f:selectItem itemValue="1" itemLabel="昇順" />
										<f:selectItem itemValue="2" itemLabel="降順" />
									</h:selectOneRadio>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_b">
									<h:outputText
										styleClass="outputText"
										id="lblSortKey2"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey2.labelStyle}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey2.labelName}">
									</h:outputText>
								</TH>
								<TD>
									<h:selectOneMenu
										styleClass="selectOneMenu"
										id="htmlSortKey2"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey2.value}"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey2.style}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSortKey2.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSortKey2.readonly}">
										<f:selectItems
											value="#{pc_Ssa00601T01.ssa00601.propSortKey2.list}" />
									</h:selectOneMenu>
								</TD>
								<TD>
									<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio"
										id="htmlSort2"
										value="#{pc_Ssa00601T01.ssa00601.propSort2.value}"
										style="#{pc_Ssa00601T01.ssa00601.propSort2.style}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSort2.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSort2.readonly}">
										<f:selectItem itemValue="1" itemLabel="昇順" />
										<f:selectItem itemValue="2" itemLabel="降順" />
									</h:selectOneRadio>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_c">
									<h:outputText
										styleClass="outputText"
										id="lblSortKey3"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey3.labelStyle}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey3.labelName}">
									</h:outputText>
								</TH>
								<TD>
									<h:selectOneMenu
										styleClass="selectOneMenu"
										id="htmlSortKey3"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey3.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey3.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSortKey3.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSortKey3.readonly}">
										<f:selectItems
											value="#{pc_Ssa00601T01.ssa00601.propSortKey3.list}" />
									</h:selectOneMenu>
								</TD>
								<TD>
									<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio"
										id="htmlSort3"
										style="#{pc_Ssa00601T01.ssa00601.propSort3.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSort3.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSort3.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSort3.readonly}">
										<f:selectItem itemValue="1" itemLabel="昇順" />
										<f:selectItem itemValue="2" itemLabel="降順" />
									</h:selectOneRadio>
								</TD>
							</TR>
								<TR>
								<TH nowrap class="v_d">
									<h:outputText
										styleClass="outputText"
										id="lblSortKey4"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey4.labelStyle}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey4.labelName}">
									</h:outputText>
								</TH>
								<TD>
									<h:selectOneMenu
										styleClass="selectOneMenu"
										id="htmlSortKey4"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey4.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey4.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSortKey4.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSortKey4.readonly}">
										<f:selectItems
											value="#{pc_Ssa00601T01.ssa00601.propSortKey4.list}" />
									</h:selectOneMenu>
								</TD>
								<TD>
									<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio"
										id="htmlSort4"
										value="#{pc_Ssa00601T01.ssa00601.propSort4.value}"
										style="#{pc_Ssa00601T01.ssa00601.propSort4.style}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSort4.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSort4.readonly}">
										<f:selectItem itemValue="1" itemLabel="昇順" />
										<f:selectItem itemValue="2" itemLabel="降順" />
									</h:selectOneRadio>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_e">
									<h:outputText
										styleClass="outputText"
										id="lblSortKey5"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey5.labelStyle}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey5.labelName}">
									</h:outputText>
								</TH>
								<TD>
									<h:selectOneMenu
										styleClass="selectOneMenu"
										id="htmlSortKey5"
										style="#{pc_Ssa00601T01.ssa00601.propSortKey5.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSortKey5.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSortKey5.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSortKey5.readonly}">
										<f:selectItems
											value="#{pc_Ssa00601T01.ssa00601.propSortKey5.list}" />
									</h:selectOneMenu>
								</TD>
								<TD>
									<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio"
										id="htmlSort5"
										style="#{pc_Ssa00601T01.ssa00601.propSort5.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSort5.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSort5.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSort5.readonly}">
										<f:selectItem itemValue="1" itemLabel="昇順" />
										<f:selectItem itemValue="2" itemLabel="降順" />
									</h:selectOneRadio>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_f" width="20%">
									<h:outputText
										styleClass="outputText"
										id="lblPageBreak"
										value="#{pc_Ssa00601T01.ssa00601.propPageBreak.labelName}"
										style="#{pc_Ssa00601T01.ssa00601.propPageBreak.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:selectOneMenu
										styleClass="selectOneMenu"
										id="htmlPageBreak"
										style="#{pc_Ssa00601T01.ssa00601.propPageBreak.style}"
										value="#{pc_Ssa00601T01.ssa00601.propPageBreak.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propPageBreak.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propPageBreak.readonly}">
										<f:selectItems
											value="#{pc_Ssa00601T01.ssa00601.propPageBreak.list}" />
									</h:selectOneMenu>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_g" width="20%">
									<h:outputText
										styleClass="outputText"
										id="lblSzkGakkaLvl"
										value="#{pc_Ssa00601T01.ssa00601.propSzkGakkaLvl.labelName}"
										style="#{pc_Ssa00601T01.ssa00601.propSzkGakkaLvl.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:selectOneMenu
										styleClass="selectOneMenu"
										id="htmlSzkGakkaLvl"
										style="#{pc_Ssa00601T01.ssa00601.propSzkGakkaLvl.style}"
										value="#{pc_Ssa00601T01.ssa00601.propSzkGakkaLvl.value}"
										disabled="#{pc_Ssa00601T01.ssa00601.propSzkGakkaLvl.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propSzkGakkaLvl.readonly}">
										<f:selectItems
											value="#{pc_Ssa00601T01.ssa00601.propSzkGakkaLvl.list}" />
									</h:selectOneMenu>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_a" width="20%">
									<h:outputText
										styleClass="outputText"
										id="lblChohyoTitle"
										style="#{pc_Ssa00601T01.ssa00601.propChohyoTitle.labelStyle}"
										value="#{pc_Ssa00601T01.ssa00601.propChohyoTitle.labelName}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:inputText
										styleClass="inputText"
										id="htmlChohyoTitle"
										size="80"
										value="#{pc_Ssa00601T01.ssa00601.propChohyoTitle.stringValue}"
										style="#{pc_Ssa00601T01.ssa00601.propChohyoTitle.style}"
										disabled="#{pc_Ssa00601T01.ssa00601.propChohyoTitle.disabled}"
										readonly="#{pc_Ssa00601T01.ssa00601.propChohyoTitle.readonly}"
										maxlength="#{pc_Ssa00601T01.ssa00601.propChohyoTitle.maxLength}">
									</h:inputText>
								</TD>
							</TR>
						</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
			<TABLE border="0" cellspacing="0" cellpadding="0" width="730" style="margin-top:10px;">
			<TBODY>
				<TR>
					<TD class="" align="left">
						<hx:commandExButton type="submit"
							value="学年・学部指定" styleClass="tab_head_on" id="btnSsa00601T01"
							readonly="true" style="width:14%"></hx:commandExButton><hx:commandExButton 
							type="submit" value="ゼミ教員指定" styleClass="tab_head_off" id="btnSsa00601T02"
							action="#{pc_Ssa00601T01.doBtnSsa00601T02Action}"
							style="width:14%"></hx:commandExButton><hx:commandExButton type="submit"
							value="学生指定" styleClass="tab_head_off" id="btnSsa00601T03"
							action="#{pc_Ssa00601T01.doBtnSsa00601T03Action}"
							style="width:14%">
						</hx:commandExButton>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE width="100%" border="0" cellspacing="0" cellpadding="0" class="tab_body">
						<TBODY>
							<TR>
							<TD align="center">
							<DIV style="height:240px">
								<TABLE width="670" border="0" cellpadding="0" cellspacing="0" class="table" style="margin-top:20px;">
								<TBODY>
									<TR>
										<TH width="20%" class="v_a">
											<h:outputText styleClass="outputText" id="lblGakunen"
													style="#{pc_Ssa00601T01.propGakunen.labelStyle}"
													value="#{pc_Ssa00601T01.propGakunen.labelName}">
												</h:outputText>
										</TH>
										<TD width="80%">
											<h:selectOneMenu styleClass="selectOneMenu" id="htmlGakunen"
													style="#{pc_Ssa00601T01.propGakunen.style}"
													value="#{pc_Ssa00601T01.propGakunen.stringValue}"
													disabled="#{pc_Ssa00601T01.propGakunen.disabled}"
													readonly="#{pc_Ssa00601T01.propGakunen.readonly}">
													<f:selectItems value="#{pc_Ssa00601T01.propGakunen.list}" />
												</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_b">
											<h:outputText
												styleClass="outputText"
												id="lblSemester"
												style="#{pc_Ssa00601T01.propSemester.labelStyle}"
												value="#{pc_Ssa00601T01.propSemester.labelName}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu
												styleClass="selectOneMenu"
												id="htmlSemester"
												style="#{pc_Ssa00601T01.propSemester.style}"
												value="#{pc_Ssa00601T01.propSemester.stringValue}"
												disabled="#{pc_Ssa00601T01.propSemester.disabled}"
												readonly="#{pc_Ssa00601T01.propSemester.readonly}">
												<f:selectItems
													value="#{pc_Ssa00601T01.propSemester.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_c">
											<h:outputText
												styleClass="outputText"
												id="lblSgks"
												style="#{pc_Ssa00601T01.propSgks.labelStyle}"
												value="#{pc_Ssa00601T01.propSgks.labelName}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu
												styleClass="selectOneMenu"
												id="htmlSgks"
												style="#{pc_Ssa00601T01.propSgks.style}"
												value="#{pc_Ssa00601T01.propSgks.stringValue}"
												disabled="#{pc_Ssa00601T01.propSgks.disabled}"
												readonly="#{pc_Ssa00601T01.propSgks.readonly}"
												style="width:500px">
												<f:selectItems
													value="#{pc_Ssa00601T01.propSgks.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									
									<TR>
										<TH class="v_d">
											<h:outputText styleClass="outputText" id="lblClsSbt"
												value="#{pc_Ssa00601T01.propClsSbt.labelName}"
												style="#{pc_Ssa00601T01.propClsSbt.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu styleClass="selectOneMenu" id="htmlClsSbt"
												value="#{pc_Ssa00601T01.propClsSbt.stringValue}"
												style="#{pc_Ssa00601T01.propClsSbt.style}"
												disabled="#{pc_Ssa00601T01.propClsSbt.disabled}"
												readonly="#{pc_Ssa00601T01.propClsSbt.readonly}">
												<f:selectItems
													value="#{pc_Ssa00601T01.propClsSbt.list}" />
											</h:selectOneMenu>
											<hx:commandExButton type="submit" value="選択"
													styleClass="commandExButton" id="selectClsSbt"
													style="#{pc_Ssa00601T01.propSelectClsSbt.style}"
													action="#{pc_Ssa00601T01.doSelectClsSbtAction}"
													disabled="#{pc_Ssa00601T01.propSelectClsSbt.disabled}"></hx:commandExButton>
											<hx:commandExButton type="submit" value="解除"
													styleClass="commandExButton" id="unSelectClsSbt"
													style="#{pc_Ssa00601T01.propUnSelectClsSbt.style}"
													action="#{pc_Ssa00601T01.doUnSelectClsSbtAction}"
													disabled="#{pc_Ssa00601T01.propUnSelectClsSbt.disabled}"></hx:commandExButton>
										</TD>
									</TR>
									<TR>
										<TH class="v_e">
											<h:outputText styleClass="outputText" id="lblCls"
												value="#{pc_Ssa00601T01.propCls.labelName}"
												style="#{pc_Ssa00601T01.propCls.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu styleClass="selectOneMenu" id="htmlCls"
												value="#{pc_Ssa00601T01.propCls.stringValue}"
												style="#{pc_Ssa00601T01.propCls.style}"
												disabled="#{pc_Ssa00601T01.propCls.disabled}"
												readonly="#{pc_Ssa00601T01.propCls.readonly}">
												<f:selectItems value="#{pc_Ssa00601T01.propCls.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									
									
									
									
									<TR>
										<TH class="v_f">
											<h:outputText
												styleClass="outputText"
												id="lblKanriTsy"
												value="#{pc_Ssa00601T01.propKanriTsy.labelName}"
												style="#{pc_Ssa00601T01.propKanriTsy.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneRadio
												disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio"
												id="htmlKanriTsy"
												style="#{pc_Ssa00601T01.propKanriTsy.style}"
												value="#{pc_Ssa00601T01.propKanriTsy.value}"
												disabled="#{pc_Ssa00601T01.propKanriTsy.disabled}"
												readonly="#{pc_Ssa00601T01.propKanriTsy.readonly}">
												<f:selectItem itemValue="2" itemLabel="含まない" />
												<f:selectItem itemValue="1" itemLabel="含む" />
												<f:selectItem itemValue="3" itemLabel="のみ" />
											</h:selectOneRadio>
										</TD>
									</TR>
									<TR>
										<TH class="v_g">
											<h:outputText
												styleClass="outputText"
												id="lblKtdoJyokyoKbn"
												value="#{pc_Ssa00601T01.propKtdoJokyoKbn.labelName}"
												style="#{pc_Ssa00601T01.propKtdoJokyoKbn.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu
												styleClass="selectOneMenu"
												id="htmlKtdoJokyoKbn"
												style="#{pc_Ssa00601T01.propKtdoJokyoKbn.style}"
												value="#{pc_Ssa00601T01.propKtdoJokyoKbn.value}"
												disabled="#{pc_Ssa00601T01.propKtdoJokyoKbn.disabled}"
												readonly="#{pc_Ssa00601T01.propKtdoJokyoKbn.readonly}">
												<f:selectItems
													value="#{pc_Ssa00601T01.propKtdoJokyoKbn.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
							</DIV>
							</TD>
							</TR>
						</TBODY>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE width="100%" class="button_bar">
						<TBODY>
							<TR>
								<TD>
									<hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout"
										action="#{pc_Ssa00601T01.doPdfoutAction}"
										confirm="#{msg.SY_MSG_0019W}">
									</hx:commandExButton>
									<hx:commandExButton type="submit" value="EXCEL作成"
										styleClass="commandExButton_out" id="excelout"
										action="#{pc_Ssa00601T01.doExceloutAction}"
										confirm="#{msg.SY_MSG_0027W}">
									</hx:commandExButton>
									<hx:commandExButton type="submit" value="CSV作成"
										styleClass="commandExButton_out" id="csvout"
										action="#{pc_Ssa00601T01.doCsvoutAction}"
										confirm="#{msg.SY_MSG_0020W}">
									</hx:commandExButton>
									<hx:commandExButton	type="submit" value="出力項目指定"
										styleClass="commandExButton_out" id="setoutput"
										action="#{pc_Ssa00601T01.doSetoutputAction}">
									</hx:commandExButton>
								</TD>
							</TR>
						</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
		<!-- ↑ここにコンポーネントを配置 -->
		</DIV>
		<!--↑column↑--> 
	</DIV>
	<!--↑content↑--> 
	<!-- フッダーインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
</DIV>
<!--↑outer↑-->
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
