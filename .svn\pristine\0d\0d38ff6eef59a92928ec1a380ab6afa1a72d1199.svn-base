<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kea11102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function confirmOk() {
	
	// 確定
	var execStatus = document.getElementById("form1:htmlExecutableExec").value;
	if (execStatus == "1") {
		// KE_MSG_0030W
		document.getElementById("form1:htmlExecutableExec").value = "2";
		indirectClick("exec");
	}

	// 戻る
	var returnStatus = document.getElementById("form1:htmlExecutableReturn").value;
	if (returnStatus == "1") {
		// KE_MSG_0203W
		document.getElementById("form1:htmlExecutableReturn").value = "2";
		indirectClick("returndisp");
	
	} else if (returnStatus == "2") {
		// KE_MSG_0029W
		document.getElementById("form1:htmlExecutableReturn").value = "3";
		indirectClick("returndisp");
		
	}
}

function confirmCancel() {
	document.getElementById("form1:htmlExecutableExec").value = "0";
	document.getElementById("form1:htmlExecutableReturn").value = "0";
}

window.attachEvent("onload", attachFormatNumber);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kea11102.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kea11102.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kea11102.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kea11102.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returndisp" action="#{pc_Kea11102.doReturndispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="926" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="table" width="890">
							<TBODY>
								<TR>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Kea11102.propKaikeiDayJotai.name}"
										style="#{pc_Kea11102.propKaikeiDayJotai.style}"></h:outputText></TH>
												<TD width="300"><h:outputText styleClass="outputText"
										id="htmlKaikeiDayJotai"
										value="#{pc_Kea11102.propKaikeiDayJotai.stringValue}"
										style="#{pc_Kea11102.propKaikeiDayJotai.style}"></h:outputText></TD>

									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblSknShohiKbn"
										value="#{pc_Kea11102.propSknShohiKbn.name}"
										style="#{pc_Kea11102.propSknShohiKbn.style}"></h:outputText></TH>
									<TD width="290"><h:outputText styleClass="outputText" id="htmlSknShohiKbn" value="#{pc_Kea11102.propSknShohiKbn.stringValue}" style="#{pc_Kea11102.propSknShohiKbn.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_c"><h:outputText
										styleClass="outputText" id="lblBmn"
										value="#{pc_Kea11102.propBmnCd.name}"
										style="#{pc_Kea11102.propBmnCd.style}"></h:outputText></TH>
									<TD  colspan="3" width="740">
										<TABLE class="clear_border" cellpadding="0" cellspacing="0" style="table-layout:fixed;"><TBODY><TR>
											<TD nowrap width="70"><h:outputText styleClass="outputText" id="htmlBmnCd" value="#{pc_Kea11102.propBmnCd.stringValue}" style="#{pc_Kea11102.propBmnCd.style}"></h:outputText>
											</TD>
											<TD nowrap width="650">
												<h:outputText
													styleClass="outputText" id="htmlBmnName"
													value="#{pc_Kea11102.propBmnName.stringValue}"
													style="#{pc_Kea11102.propBmnName.style};white-space:nowrap;"
													title="#{pc_Kea11102.propBmnName.stringValue}"></h:outputText>
											</TD>
										</TR></TBODY></TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_d" width="150"><h:outputText styleClass="outputText"
										id="lblAnbKmk" value="#{pc_Kea11102.propAnbKmkCd.name}"
										style="#{pc_Kea11102.propAnbKmkCd.style}"></h:outputText></TH>
									<TD  colspan="3" width="740">
										<TABLE class="clear_border" cellpadding="0" cellspacing="0" style="table-layout:fixed;"><TBODY><TR>
											<TD nowrap width="70"><h:outputText styleClass="outputText" id="htmlAnbKmkCd" value="#{pc_Kea11102.propAnbKmkCd.stringValue}" style="#{pc_Kea11102.propAnbKmkCd.style}"></h:outputText>
											</TD>
											<TD nowrap width="650">
												<h:outputText
													styleClass="outputText" id="htmlAnbKmkName"
													value="#{pc_Kea11102.propAnbKmkName.stringValue}"
													style="#{pc_Kea11102.propAnbKmkName.style};white-space:nowrap;"
													title="#{pc_Kea11102.propAnbKmkName.stringValue}"></h:outputText>
											</TD>
										</TR></TBODY></TABLE>
									</TD>
									
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="926">
							<TBODY>
								<TR>
									<TD width="18">　</TD>
									<TD width="790" align="left"></TD>
									<TD width="100" align="right"><h:outputText
										styleClass="outputText" id="htmlListCount" value="#{pc_Kea11102.propTableList1.listCount}">

										<f:convertNumber type="number"/>
									</h:outputText><h:outputText
										styleClass="outputText" id="lblKen" value="件"></h:outputText></TD>
									<TD width="18">　</TD>
								</TR>
								<TR>
									<TD width="18">　</TD>
									<TD colspan="3" align="left">
										<TABLE border="1" class="meisai_page" width="890">
											<TR>
												<TH align="center" nowrap width="31"><h:outputText id="lblRowNo" styleClass="outputText"
													value="NO"></h:outputText></TH>
												<TH align="center" nowrap width="151" ><h:outputText id="lblColYsnTName" styleClass="outputText" 
													value="予算単位"></h:outputText></TH>
												<TH align="center" nowrap width="151"><h:outputText id="lblColMokuName" styleClass="outputText"
													value="目的"></h:outputText></TH>
												<TH align="center" nowrap width="151"><h:outputText id="lblColKmkName" styleClass="outputText"
													value="科目"></h:outputText></TH>
												<TH align="center" nowrap width="227"><h:outputText id="lblColKomokuNaiyo" styleClass="outputText"
													value="項目内容"></h:outputText></TH>
												<TH align="center" nowrap width="115">
													<DIV style="width:115">
														<h:outputText
															styleClass="outputText_label"
															value="#{pc_Kea11102.propAnbGakuAtoFormat.labelName}"
															style="#{pc_Kea11102.propAnbGakuAtoFormat.labelStyle}"></h:outputText>
													</DIV>
												</TH>
												<TH align="center" width="80" nowrap ><h:outputText id="lblColButton" styleClass="outputText"
													value="　"></h:outputText></TH>
											</TR>
										</TABLE>
									</TD></TR>
								<TR>
									<TD width="18">　</TD>
									<TD colspan="3" align="left">
									<DIV class="listScroll" onscroll="setScrollPosition('scroll', this);" style="height:369px; width:908px"
										id="listScroll" ><h:dataTable border="0"
										cellpadding="2" cellspacing="0" headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_Kea11102.propTableList1.rowClasses}"
										styleClass="meisai_scroll" id="htmlTableList1" var="varlist"
										width="888" value="#{pc_Kea11102.propTableList1.list}"
										style="table-layout:fixed;">
										<h:column id="column0">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlRowNo"
												value="#{varlist.recNo}"></h:outputText>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: right" name="style" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="150" name="width" />
											<h:outputText styleClass="outputText" id="htmlColYsnTName"
												value="#{varlist.colYsnTName.value}"
												title="#{varlist.colYsnTName.value}"
												style="white-space:nowrap;"></h:outputText>
										</h:column>
										<h:column id="column1">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColMokuName"
												value="#{varlist.colMokuName.value}"
												title="#{varlist.colMokuName.value}"
												style="white-space:nowrap;"></h:outputText>
											<f:attribute value="150" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKmkName"
												value="#{varlist.colAnbKmkName.value}"
												title="#{varlist.colAnbKmkName.value}"
												style="white-space:nowrap;"></h:outputText>
											<f:attribute value="150" name="width" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColKomokuNaiyo"
												value="#{varlist.colKomokuNaiyo.value}"
												title="#{varlist.colKomokuNaiyo.value}"
												style="white-space:nowrap;"></h:outputText>
											<f:attribute value="225" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<h:inputText id="htmlColAnbGakuAto" size="12"
												disabled="#{varlist.colAnbGakuAto.disabled}"
												maxlength="#{varlist.colAnbGakuAto.maxLength}"
												readonly="#{varlist.colAnbGakuAto.readonly}"
												value="#{varlist.colAnbGakuAto.stringValue}"
												styleClass="#{varlist.colAnbGakuAto.style}"
												style="#{varlist.colAnbGakuAto.style}; text-align: right; padding-right: 3px;"
												rendered="#{varlist.colAnbGakuAto.rendered}">

												<hx:inputHelperAssist errorClass="inputText_Error" />
											</h:inputText>
											<f:attribute value="115" name="width" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
										<h:column id="column7">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="編集"
												styleClass="commandExButton" id="edit"
												action="#{pc_Kea11102.doEditAction}"
												disabled="#{varlist.colEditButton.disabled}" style="font-size: 9pt; vertical-align: middle; width: 30px"></hx:commandExButton>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="ｺﾋﾟｰ"
												styleClass="commandExButton" id="copy"
												action="#{pc_Kea11102.doCopyAction}"
												disabled="#{varlist.colCopyButton.disabled}" style="font-size: 9pt; vertical-align: middle; width: 30px"></hx:commandExButton>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
									</h:dataTable></DIV>
									</TD></TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="table" width="890">
							<TBODY>
								<TR>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblYsnGakuSum"
										value="#{pc_Kea11102.propYsnGakuSum.name}"
										style="#{pc_Kea11102.propYsnGakuSum.style}"></h:outputText></TH>
									<TD width="150"><DIV align="right"><h:outputText
										styleClass="outputText" id="htmlYsnGakuSum"
										value="#{pc_Kea11102.propYsnGakuSum.longValue}"
										style="#{pc_Kea11102.propYsnGakuSum.style}">
										<f:convertNumber type="number"/>
									</h:outputText></DIV></TD>

									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblAnbGakuAtoSum"
										value="#{pc_Kea11102.propAnbGakuAtoSum.name}"
										style="#{pc_Kea11102.propAnbGakuAtoSum.style}"></h:outputText></TH>
									<TD width="150"><DIV align="right"><h:outputText
										styleClass="outputText" id="htmlAnbGakuAtoSum"
										value="#{pc_Kea11102.propAnbGakuAtoSum.longValue}"
										style="#{pc_Kea11102.propAnbGakuAtoSum.style}">
										<f:convertNumber />
									</h:outputText></DIV></TD>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblSaGaku"
										value="#{pc_Kea11102.propSaGakuSum.name}"
										style="#{pc_Kea11102.propSaGakuSum.style}"></h:outputText></TH>
									<TD width="140"><DIV align="right"><h:outputText styleClass="outputText"
										id="htmlSaGakuSum"
										value="#{pc_Kea11102.propSaGakuSum.longValue}"
										style="#{pc_Kea11102.propSaGakuSum.style}">
										<f:convertNumber />
									</h:outputText></DIV></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="button_bar" width="890">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="exec" action="#{pc_Kea11102.doExecAction}" confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden id="htmlKaikeiNendoAjaxHidden"
				value="#{pc_Kea11102.propKaikeiNendoAjaxHidden.stringValue}">
			</h:inputHidden>
			<h:inputHidden id="htmlTableList1Row">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kea11102.propTableList1.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea11102.propFormatNumberOption.stringValue}"
				id="htmlFormatNumberOption"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea11102.propExecutableExec.stringValue}"
				id="htmlExecutableExec"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Kea11102.propExecutableReturn.stringValue}"
				id="htmlExecutableReturn"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition('scroll', 'listScroll');
</SCRIPT>
</HTML>

