<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab00810T04.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>リース資産詳細(管理情報)</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function loadFunc() {
}

window.attachEvent("onload", loadFunc);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY>
<hx:scriptCollector id="scriptCollector1"
	preRender="#{pc_Kab00810T04.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../../rev/inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
	value="閉じる" styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kab00810T04.doCloseDispAction}"></hx:commandExButton> <h:outputText
	styleClass="outputText" id="htmlFuncId"
	value="#{pc_Kab00810T04.funcId}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlLoginId"
	value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
	styleClass="outputText" id="htmlScrnName"
	value="#{pc_Kab00810T04.screenName}"></h:outputText></div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
	id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText></FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
	type="submit" value="戻る" styleClass="commandExButton"
	id="returnDisp" action="#{pc_Kab00810T04.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="900" style="margin-top: 20px;">
		<TBODY>
			<TR>
				<TH class="v_a" width="140"><h:outputText styleClass="outputText"
					id="lblLeaseNo"
					value="#{pc_Kab00810T04.kab00810.propLeaseNo.name}"
					style="#{pc_Kab00810T04.kab00810.propLeaseNo.labelStyle}"></h:outputText></TH>
				<TD width="150"><h:outputText styleClass="outputText"
							id="htmlLeaseNo"
							value="#{pc_Kab00810T04.kab00810.propLeaseNo.stringValue}"
							title="#{pc_Kab00810T04.kab00810.propLeaseNo.stringValue}"></h:outputText></TD>
				<TH class="v_c" width="140"><h:outputText styleClass="outputText"
					id="lblKeiyakuBukkenName"
					value="#{pc_Kab00810T04.kab00810.propKeiyakuBukkenName.name}"
					style="#{pc_Kab00810T04.kab00810.propKeiyakuBukkenName.labelStyle}"></h:outputText></TH>
				<TD width="450">
				<DIV style="width:450px;white-space:nowrap;overflow:hidden;display:block;">
				<h:outputText styleClass="outputText" id="htmlKeiyakuBukkenName"
							value="#{pc_Kab00810T04.kab00810.propKeiyakuBukkenName.stringValue}"
							title="#{pc_Kab00810T04.kab00810.propKeiyakuBukkenName.stringValue}"></h:outputText>
				</DIV>	
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" width="900">
		<TBODY>
			<TR>
				<TD align="right"><h:outputText
					styleClass="outputText" id="htmlListCount"
					value="#{pc_Kab00810T04.kab00810.propLeaseSisnList.listCount}件"
					style="font-size: 8pt"></h:outputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" width="900">
		<TBODY>
			<TR>
				<TD width="900">
					<DIV class="listScroll"
						style="height:86px; OVERFLOW:scroll;overflow-x: hidden;" 
						id="listScroll" onscroll="setScrollPosition('scroll', this);"><h:dataTable
						border="0" cellpadding="2" cellspacing="0"
						headerClass="headerClass" footerClass="footerClass"
						rowClasses="#{pc_Kab00810T01.kab00810.propLeaseSisnList.rowClasses}"
						styleClass="meisai_scroll" id="htmlLeaseSisnList" width="880"
						value="#{pc_Kab00810T01.kab00810.propLeaseSisnList.list}"
						var="varlist"
						first="#{pc_Kab00810T01.kab00810.propLeaseSisnList.first}"
						rows="#{pc_Kab00810T01.kab00810.propLeaseSisnList.rows}">
						<h:column id="column1">
							<f:facet name="header">
									<h:outputText id="lblListLeaseSsnNo" styleClass="outputText"
										value="リース資産番号"></h:outputText>
								</f:facet>
							<f:attribute value="90" name="width" />
								<h:outputText styleClass="outputText" id="htmlListLeaseSsnNo"
									value="#{varlist.leaseSsnNo.stringValue}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column2">
							<f:facet name="header">
									<h:outputText id="lblListSsnName" styleClass="outputText"
										value="資産名称"></h:outputText>
								</f:facet>
							<hx:jspPanel id="jspPanel1">
								<DIV style="width:220px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListSsnName"
										value="#{varlist.ssnName.stringValue}" styleClass="outputText"
										style="white-space:nowrap;"
										title="#{varlist.ssnName.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="220" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />	
						</h:column>
						<h:column id="column3">
							<f:facet name="header">
									<h:outputText id="lblListSetchiBashoName"
										styleClass="outputText" value="設置場所名称"></h:outputText>
								</f:facet>
							<hx:jspPanel id="jspPanel2">
								<DIV style="width:180px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListSetchiBashoName"
										value="#{varlist.setchiBashoName.stringValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.setchiBashoName.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="180" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />	
						</h:column>
						<h:column id="column4">
							<f:facet name="header">
									<h:outputText id="lblListKanriBmnName" styleClass="outputText"
										value="管理部門名称"></h:outputText>
								</f:facet>
							<hx:jspPanel id="jspPanel3">
								<DIV style="width:180px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListKanriBmnName"
										value="#{varlist.kanriBmnName.stringValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.kanriBmnName.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="180" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />	
						</h:column>
						<h:column id="column5">
							<f:facet name="header">
								<h:outputText id="lblListShutokuKagaku" styleClass="outputText" value="取得価額"></h:outputText>
							</f:facet>
							<hx:jspPanel id="jspPanel4">
								<DIV style="width:140px;white-space:nowrap;overflow:hidden;display:block;text-align:right;">
									<h:outputText id="htmlListShutokuKagaku"
										value="#{varlist.shutokuKagaku.stringValue}"
										styleClass="outputText" style="white-space:nowrap;"
										title="#{varlist.shutokuKagaku.stringValue}">
									</h:outputText>
								</DIV>
							</hx:jspPanel>	
							<f:attribute value="140" name="width" />
							<f:attribute value="text-align: left" name="style" />
							<f:attribute value="true" name="nowrap" />
						</h:column>
						<h:column id="column6">
							<f:facet name="header">
							</f:facet>
							<hx:commandExButton type="submit" styleClass="commandExButton"
								id="select" value="選択"
								style="width:38"
								action="#{pc_Kab00810T01.doSelectAction}"></hx:commandExButton>
							<f:attribute value="38px" name="width" />
							<f:attribute value="text-align: center; vertical-align: middle"
								name="style" />
						</h:column>
					</h:dataTable>
					</DIV>
				</TD>
				
			</TR>
		</TBODY>
	</TABLE>

	<TABLE border="0" cellpadding="0" cellspacing="0" width="900" height="390" style="margin-top: 10px;">
		<TBODY>
			<TR>
				<TD align="left" height="27">
					<hx:commandExButton
						type="submit"
						style="width: 164px"
						value="資産情報１"
						styleClass="tab_head_off"
						id="ssnInfo1"
						action="#{pc_Kab00810T04.doSsnInfo1Action}"></hx:commandExButton><hx:commandExButton
						type="submit"
						style="width: 164px"
						value="資産情報２"
						styleClass="tab_head_off"
						id="ssnInfo2"
						action="#{pc_Kab00810T04.doSsnInfo2Action}"></hx:commandExButton><hx:commandExButton
						type="submit"
						style="width: 164px"
						value="形態情報"
						styleClass="tab_head_off"
						id="keitaiInfo"
						action="#{pc_Kab00810T04.doKeitaiInfoAction}"></hx:commandExButton><hx:commandExButton
						type="submit"
						style="width: 164px"
						value="管理情報"
						styleClass="tab_head_on"
						id="kanriInfo">
					</hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD height="100" align="left" valign="top">
					<TABLE border="1" cellpadding="20" cellspacing="0" height="390" width="900" class="tab_body">
					<TBODY>
						<TR>
							<TD align="center"  valign="top">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="880" class="table" style="margin-top: 10px;">
								<TBODY>
									<TR>
										<TH class="v_a" width="175" nowrap>
										<h:outputText styleClass="outputText"
													id="lblHoteiTaiyoNensuShurui"
													style="#{pc_Kab00810T04.propHoteiTaiyoNensuShurui.labelStyle}"
													value="#{pc_Kab00810T04.propHoteiTaiyoNensuShurui.labelName}"></h:outputText></TH>
										<TD width="265" nowrap>
											<DIV style="width:265px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText"
													id="htmlHoteiTaiyoNensuShurui"
													value="#{pc_Kab00810T04.propHoteiTaiyoNensuShurui.stringValue}"
													title="#{pc_Kab00810T04.propHoteiTaiyoNensuShurui.stringValue}">
												</h:outputText>
											</DIV>
										</TD>
										<TH class="v_a" width="175">
											<h:outputText styleClass="outputText" id="lblTaiyoNensu"
														style="#{pc_Kab00810T04.propTaiyoNensu.labelStyle}"
														value="#{pc_Kab00810T04.propTaiyoNensu.name}">
											</h:outputText></TH>
										<TD width="265">
											<h:outputText styleClass="outputText" id="htmlTaiyoNensu"
														rendered="#{pc_Kab00810T04.propTaiyoNensu.rendered}"
														style="padding-right: 3px; text-align: right;#{pc_Kab00810T04.propTaiyoNensu.style}"
														value="#{pc_Kab00810T04.propTaiyoNensu.stringValue}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblShokyakuGaku"
													style="#{pc_Kab00810T04.propShokyakuGaku.labelStyle}"
													value="#{pc_Kab00810T04.propShokyakuGaku.name}"></h:outputText></TH>
										<TD style="width:90px;text-align:right;">
										<h:outputText styleClass="outputText" id="htmlShokyakuGaku"
													rendered="#{pc_Kab00810T04.propShokyakuGaku.rendered}"
													style="padding-right: 3px; text-align: right;#{pc_Kab00810T04.propShokyakuGaku.style}"
													value="#{pc_Kab00810T04.propShokyakuGaku.stringValue}"></h:outputText></TD>
										<TH class="v_a">
										<h:outputText styleClass="outputText"
													id="lblShokyakuRuikeiGaku"
													style="#{pc_Kab00810T04.propShokyakuRuikeiGaku.labelStyle}"
													value="#{pc_Kab00810T04.propShokyakuRuikeiGaku.name}"></h:outputText></TH>
										<TD style="width:90px;text-align:right;">
										<h:outputText styleClass="outputText"
													id="htmlShokyakuRuikeiGaku"
													rendered="#{pc_Kab00810T04.propShokyakuRuikeiGaku.rendered}"
													style="padding-right: 3px; text-align: right;#{pc_Kab00810T04.propShokyakuRuikeiGaku.style}"
													value="#{pc_Kab00810T04.propShokyakuRuikeiGaku.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText"
													id="lblTonendoShokyakuGaku"
													style="#{pc_Kab00810T04.propTonendoShokyakuGaku.labelStyle}"
													value="#{pc_Kab00810T04.propTonendoShokyakuGaku.name}"></h:outputText></TH>
										<TD colspan="3" style="width:90px;text-align:right;">
										<h:outputText styleClass="outputText"
													id="htmlTonendoShokyakuGaku"
													style="#{pc_Kab00810T04.propTonendoShokyakuGaku.style}"
													value="#{pc_Kab00810T04.propTonendoShokyakuGaku.stringValue}">
													<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
												</h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText"
													id="lblShokyakuKaishiNendo"
													style="#{pc_Kab00810T04.propShokyakuKaishiNendo.labelStyle}"
													value="#{pc_Kab00810T04.propShokyakuKaishiNendo.name}"></h:outputText></TH>
										<TD>
										<h:outputText styleClass="outputText"
													id="htmlShokyakuKaishiNendo"
													rendered="#{pc_Kab00810T04.propShokyakuKaishiNendo.rendered}"
													style="#{pc_Kab00810T04.propShokyakuKaishiNendo.style}"
													value="#{pc_Kab00810T04.propShokyakuKaishiNendo.stringValue}">
													<f:convertDateTime pattern="yyyy" />
												</h:outputText></TD>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblShokyakuKaisu"
													style="#{pc_Kab00810T04.propShokyakuKaisu.labelStyle}"
													value="#{pc_Kab00810T04.propShokyakuKaisu.name}"></h:outputText></TH>
										<TD>
										<h:outputText styleClass="outputText" id="htmlShokyakuKaisu"
													rendered="#{pc_Kab00810T04.propShokyakuKaisu.rendered}"
													style="padding-right: 3px; text-align: right;#{pc_Kab00810T04.propShokyakuKaisu.style}"
													value="#{pc_Kab00810T04.propShokyakuKaisu.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_g"><h:outputText
											id="lblShutokuRingiNo" styleClass="outputText"
											value="#{pc_Kab00810T04.propShutokuRingiNo.name}">
										</h:outputText></TH>
										<TD><h:outputText styleClass="outputText"
													id="htmlShutokuRingiNo"
													value="#{pc_Kab00810T04.propShutokuRingiNo.stringValue}"
													style="#{pc_Kab00810T04.propShutokuRingiNo.style}">
												</h:outputText></TD>
										<TH class="v_g">
										<h:outputText styleClass="outputText"
													id="lblShutokuDenpyoKanriNo"
													style="#{pc_Kab00810T04.propShutokuDenpyoKanriNo.labelStyle}"
													value="#{pc_Kab00810T04.propShutokuDenpyoKanriNo.name}"></h:outputText></TH>
										<TD>
										<h:outputText styleClass="outputText"
													id="htmlShutokuDenpyoKanriNo"
													rendered="#{pc_Kab00810T04.propShutokuDenpyoKanriNo.rendered}"
													style="#{pc_Kab00810T04.propShutokuDenpyoKanriNo.style}"
													value="#{pc_Kab00810T04.propShutokuDenpyoKanriNo.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_c"><h:outputText id="lblBikoShutoku"
											value="#{pc_Kab00810T04.propBikoShutoku.name}"
											styleClass="outputText">
										</h:outputText></TH>
										<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
													id="htmlBikoShutoku" cols="85" rows="2"
													readonly="#{pc_Kab00810T04.propBikoShutoku.readonly}"
													disabled="#{pc_Kab00810T04.propBikoShutoku.disabled}"
													value="#{pc_Kab00810T04.propBikoShutoku.stringValue}"
													style="#{pc_Kab00810T04.propBikoShutoku.style}">
												</h:inputTextarea></TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblJokyokuRingiNo"
													style="#{pc_Kab00810T04.propJokyakuRingiNo.labelStyle}"
													value="#{pc_Kab00810T04.propJokyakuRingiNo.name}"></h:outputText></TH>
										<TD colspan="3">
										<h:outputText styleClass="outputText" id="htmlJokyakuRingiNo"
													rendered="#{pc_Kab00810T04.propJokyakuRingiNo.rendered}"
													style="#{pc_Kab00810T04.propJokyakuRingiNo.style}"
													value="#{pc_Kab00810T04.propJokyakuRingiNo.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_a" width="175">
										<h:outputText styleClass="outputText" id="lblJokyakuJokyo"
													style="#{pc_Kab00810T04.propJokyakuJokyo.labelStyle}"
													value="#{pc_Kab00810T04.propJokyakuJokyo.name}"></h:outputText></TH>
										<TD width="265">
										<h:outputText styleClass="outputText" id="htmlJokyakuJokyo"
													style="#{pc_Kab00810T04.propJokyakuJokyo.style}"
													value="#{pc_Kab00810T04.propJokyakuJokyo.stringValue}" title="#{pc_Kab00810T04.propJokyakuJokyo.stringValue}"></h:outputText></TD>
										<TH class="v_a" width="175">
										<h:outputText styleClass="outputText" id="lblJokyakuDate"
													style="#{pc_Kab00810T04.propJokyakuDate.labelStyle}"
													value="#{pc_Kab00810T04.propJokyakuDate.name}"></h:outputText></TH>
										<TD width="265">
										<h:outputText styleClass="outputText" id="htmlJokyakuDate"
													style="#{pc_Kab00810T04.propJokyakuDate.style}"
													value="#{pc_Kab00810T04.propJokyakuDate.stringValue}">
												<f:convertDateTime pattern="yyyy/MM/dd" />
											</h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText"
													id="lblJokyakuShiwakeSakusei"
													style="#{pc_Kab00810T04.propJokyakuShiwakeSakusei.labelStyle}"
													value="#{pc_Kab00810T04.propJokyakuShiwakeSakusei.name}"></h:outputText></TH>
										<TD>
										<h:outputText styleClass="outputText"
													id="htmlJokyakuShiwakeSakusei"
													style="#{pc_Kab00810T04.propJokyakuShiwakeSakusei.style}"
													value="#{pc_Kab00810T04.propJokyakuShiwakeSakusei.stringValue}"></h:outputText></TD>
										<TH class="v_a">
										<h:outputText styleClass="outputText"
													id="lblJokyakuDenpyoKanriNo"
													style="#{pc_Kab00810T04.propJokyakuDenpyoKanriNo.labelStyle}"
													value="#{pc_Kab00810T04.propJokyakuDenpyoKanriNo.name}"></h:outputText></TH>
										<TD>
										<h:outputText styleClass="outputText"
													id="htmlJokyakuDenpyoKanriNo"
													style="#{pc_Kab00810T04.propJokyakuDenpyoKanriNo.style}"
													value="#{pc_Kab00810T04.propJokyakuDenpyoKanriNo.stringValue}"></h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblBikoJokyaku"
													style="#{pc_Kab00810T04.propBikoJokyaku.labelStyle}"
													value="#{pc_Kab00810T04.propBikoJokyaku.name}"></h:outputText></TH>
										<TD colspan="3">
										<h:inputTextarea styleClass="inputTextarea"
													id="htmlBikoJokyaku" cols="85"
													disabled="#{pc_Kab00810T04.propBikoJokyaku.disabled}"
													readonly="#{pc_Kab00810T04.propBikoJokyaku.readonly}"
													rendered="#{pc_Kab00810T04.propBikoJokyaku.rendered}"
													rows="2" style="#{pc_Kab00810T04.propBikoJokyaku.style}"
													value="#{pc_Kab00810T04.propBikoJokyaku.stringValue}"></h:inputTextarea></TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblHaikiJokyo"
													style="#{pc_Kab00810T04.propHaikiJokyo.labelStyle}"
													value="#{pc_Kab00810T04.propHaikiJokyo.name}"></h:outputText></TH>
										<TD>
										<h:outputText styleClass="outputText" id="htmlHaikiJokyo"
													style="#{pc_Kab00810T04.propHaikiJokyo.style}"
													value="#{pc_Kab00810T04.propHaikiJokyo.stringValue}"></h:outputText></TD>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblHaikiDate"
													style="#{pc_Kab00810T04.propHaikiDate.labelStyle}"
													value="#{pc_Kab00810T04.propHaikiDate.name}"></h:outputText></TH>
										<TD>
										<h:outputText styleClass="outputText" id="htmlHaikiDate"
													style="#{pc_Kab00810T04.propHaikiDate.style}"
													value="#{pc_Kab00810T04.propHaikiDate.stringValue}">
													<f:convertDateTime pattern="yyyy/MM/dd" />
												</h:outputText></TD>
									</TR>
									<TR>
										<TH class="v_a">
										<h:outputText styleClass="outputText" id="lblBikoHaiki"
													style="#{pc_Kab00810T04.propBikoHaiki.labelStyle}"
													value="#{pc_Kab00810T04.propBikoHaiki.name}"></h:outputText></TH>
										<TD colspan="3">
										<h:inputTextarea styleClass="inputTextarea" id="htmlBikoHaiki"
													cols="85" rows="2"
													disabled="#{pc_Kab00810T04.propBikoHaiki.disabled}"
													readonly="#{pc_Kab00810T04.propBikoHaiki.readonly}"
													rendered="#{pc_Kab00810T04.propBikoHaiki.rendered}"
													style="#{pc_Kab00810T04.propBikoHaiki.style}"
													value="#{pc_Kab00810T04.propBikoHaiki.stringValue}"></h:inputTextarea></TD>
									</TR>
								</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE width="900" border="0" style="" class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton type="submit" value="関連情報"
							styleClass="commandExButton_etc" id="kanrenInfo"
							action="#{pc_Kab00810T04.doKanrenInfoAction}">
						</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>

<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--></DIV>
<SCRIPT type="text/javascript">
	setMaxWidth(document.getElementById("form1:htmlHoteiTaiyoNensuShurui"), 260);
</SCRIPT>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../../rev/inc/footer.jsp" />
<h:inputHidden value="htmlTaiyoNensu=#0;#0 | htmlShokyakuGaku=###,###,###,##0;###,###,###,##0 | htmlShokyakuRuikeiGaku=###,###,###,##0;###,###,###,##0 | htmlShokyakuKaisu=##0;##0"
	id="htmlFormatNumberOption">
</h:inputHidden>
		<h:inputHidden
			value="#{pc_Kab00810T04.kab00810.propLeaseSisnList.scrollPosition}"
			id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

