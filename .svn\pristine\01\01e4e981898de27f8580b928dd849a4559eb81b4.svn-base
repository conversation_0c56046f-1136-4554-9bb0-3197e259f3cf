<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsz03001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsz03001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

// 業者コンボの設定
function setComb() {
var gyoSya=document.getElementById('form1:htmlGyosya').value;
var options=document.getElementById('form1:htmlGyosyaId').options;
var i=0;
for(i=0;i<options.length;i++){
	if(gyoSya==options[i].value){
		options[i].selected = true;
		return true;		
	}
}
options[0].selected = true;
return false;
}

// 業者コードの設定</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Nsz03001.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Nsz03001.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Nsz03001.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Nsz03001.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --><!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" width="700px">
				<TBODY>
					<TR>
						<TD width="700px">
						<TABLE border="0" class="table" width="700px">
							<TBODY>
								<TR>
									<TH width="190px" class="v_a"><h:outputText styleClass="outputText"
										id="text5" value="募集媒体分類区分桁数"></h:outputText></TH>
									<TD width="510"><h:outputText styleClass="outputText"
										id="text6" value="　　　　　　　　大分類（上"></h:outputText>
										<h:inputText
										styleClass="inputText_dis" id="htmlDai"
										value="#{pc_Nsz03001.propDai.stringValue}" style="font-size: 8pt; width: 12px" disabled="true"></h:inputText>
										<h:outputText
										styleClass="outputText" id="text7" value="桁）　　　　　　　　　　　　中分類（上"></h:outputText>
										<h:inputText
										styleClass="inputText_dis" id="htmlTyu"
										value="#{pc_Nsz03001.propTyu.stringValue}" style="font-size: 8pt; width: 12px" disabled="true"></h:inputText>
										<h:outputText
										styleClass="outputText" id="Keat" value="桁）"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="700px">
				<TBODY>
					<TR>
						<TD valign="bottom" align="right" height="17" nowrap width="67%">
						<h:outputText styleClass="outputText" id="text19"
							style="font-size: 8pt"
							value="#{pc_Nsz03001.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text20" style="font-size: 8pt"
							value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="740px" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="40"></TD>
						<TD>
						<DIV class="listScroll" id="listScroll"
							style="height:168px;width:717px;"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Nsz03001.propBsyBit.list}" var="varlist" width="700px"
							rowClasses="#{pc_Nsz03001.propBsyBit.rowClasses}"
							style="#{pc_Nsz03001.propBsyBit.style}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblCd" styleClass="outputText" value="コード"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text1"
									value="#{varlist.baiTaiCd}"></h:outputText>
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="区分" id="lblKubn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text2"
									value="#{varlist.baiTaiKubn}"></h:outputText>
								<f:attribute value="120" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称"
										id="lblMeisyou"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text3"
									value="#{varlist.baiTaiName}"></h:outputText>
								<f:attribute value="490" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>

								<f:attribute value="20" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Nsz03001.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="2" cellspacing="0" width="700px" style="margin-top:10px;">
				<TBODY>
					<TR>
						<TD>
						<TABLE class="table" border="0" cellpadding="0" cellspacing="0"
							width="700px">
							<TBODY>
								<TR>
									<TH width="190" class="v_a"><h:outputText
										styleClass="outputText" id="lblCode"
										value="#{pc_Nsz03001.propCd.labelName}"
										style="#{pc_Nsz03001.propCd.labelStyle}">
									</h:outputText></TH>
									<TD width="510" ><h:inputText styleClass="inputText"
										id="htmlCd" value="#{pc_Nsz03001.propCd.stringValue}" size="5"
										maxlength="#{pc_Nsz03001.propCd.maxLength}"
										style="#{pc_Nsz03001.propCd.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="190" class="v_b" ><h:outputText
										styleClass="outputText" id="lblGyosya"
										value="#{pc_Nsz03001.propGyosyaId.labelName}"
										style="#{pc_Nsz03001.propGyosyaId.labelStyle}">
									</h:outputText></TH>
									<TD width="510" ><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlGyosyaId"
										value="#{pc_Nsz03001.propGyosyaId.value}">
										<f:selectItems value="#{pc_Nsz03001.propGyosyaId.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="190" class="v_c" ><h:outputText
										styleClass="outputText" id="lblName"
										value="#{pc_Nsz03001.propName.labelName}"
										style="#{pc_Nsz03001.propName.labelStyle}"></h:outputText></TH>
									<TD width="510" height="34"><h:inputText styleClass="inputText"
										id="htmlName" value="#{pc_Nsz03001.propName.stringValue}"
										size="60" maxlength="#{pc_Nsz03001.propName.maxLength}"
										style="#{pc_Nsz03001.propName.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="190" class="v_d" ><h:outputText
										styleClass="outputText" id="lblNameKana"
										value="#{pc_Nsz03001.propNameKana.labelName}"
										style="#{pc_Nsz03001.propNameKana.labelStyle}"></h:outputText></TH>
									<TD width="510" height="29"><h:inputText styleClass="inputText"
										id="htmlNameKana"
										value="#{pc_Nsz03001.propNameKana.stringValue}" size="60"
										maxlength="#{pc_Nsz03001.propNameKana.maxLength}"
										style="#{pc_Nsz03001.propNameKana.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="190" class="v_e" ><h:outputText
										styleClass="outputText" id="lblNameRyak"
										value="#{pc_Nsz03001.propNameRyak.labelName}"
										style="#{pc_Nsz03001.propNameRyak.labelStyle}"></h:outputText></TH>
									<TD width="510" height="26"><h:inputText styleClass="inputText"
										id="htmlNameRyak"
										value="#{pc_Nsz03001.propNameRyak.stringValue}" size="30"
										maxlength="#{pc_Nsz03001.propNameRyak.maxLength}"
										style="#{pc_Nsz03001.propNameRyak.style}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE class="button_bar" width="700px">
				<TBODY>
					<TR>
						<TD align="center" width="700px"><hx:commandExButton
				type="submit" value="確定" styleClass="commandExButton_dat"
				id="register" action="#{pc_Nsz03001.doRegisterAction}"
				confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Nsz03001.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Nsz03001.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<!--↑content↑-->
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Nsz03001.propBsyBit.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
