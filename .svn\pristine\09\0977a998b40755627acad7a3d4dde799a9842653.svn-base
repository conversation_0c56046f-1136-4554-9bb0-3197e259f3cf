<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmc02302T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kmc02302T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">


// すべてのチェックボックスにチェックをつける
function func_srcCheck(thisObj, thisEvent) {
	check('htmlSrcGakkaSoshikiList','htmlColSrcCheck');
}
// すべてのチェックボックスからチェックをはずす
function func_srcUncheck(thisObj, thisEvent) {
	uncheck('htmlSrcGakkaSoshikiList','htmlColSrcCheck');
}

// すべてのチェックボックスにチェックをつける
function func_dstCheck(thisObj, thisEvent) {
	check('htmlDstCurGakkaSoshikiList','htmlColDstCheck');
}
// すべてのチェックボックスからチェックをはずす
function func_dstUncheck(thisObj, thisEvent) {
	uncheck('htmlDstCurGakkaSoshikiList','htmlColDstCheck');
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}


</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kmc02302T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmc02302T02.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmc02302T02.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmc02302T02.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right">
						<hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						action="#{pc_Kmc02302T02.doReturnDispAction}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここに戻るボタンを配置 --></DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5" width="800px">
			<TBODY>
				<TR>
					<TD valign="top">
						<!--↓タブ間共有テーブル↓-->
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="800px">
						<TBODY>
							<TR>
								<TH class="v_a" width="140px">
								<h:outputText styleClass="outputText" id="lblNendo"
										style="#{pc_Kmc02302T02.kmc02302.propNendo.labelStyle}"
										value="#{pc_Kmc02302T02.kmc02302.propNendo.labelName}">
									</h:outputText></TH>
								<TD colspan="3">
								<h:outputText styleClass="outputText" id="htmlNendo"
										value="#{pc_Kmc02302T02.kmc02302.propNendo.dateValue}">
										<f:convertDateTime pattern="yyyy" />
									</h:outputText></TD>
							</TR>
							<TR>
								<TH class="v_b" width="140px">
									<h:outputText styleClass="outputText" id="lblSetCd"
										style="#{pc_Kmc02302T02.kmc02302.propSetCd.labelStyle}" value="#{pc_Kmc02302T02.kmc02302.propSetCd.labelName}"></h:outputText></TH>
								<TD width="130px">
									<h:outputText styleClass="outputText" id="htmlSetCd"
										rendered="#{pc_Kmc02302T02.kmc02302.propSetCd.rendered}" style="#{pc_Kmc02302T02.kmc02302.propSetCd.style}" value="#{pc_Kmc02302T02.kmc02302.propSetCd.stringValue}"></h:outputText></TD>
								<TH class="v_c" width="140px">
									<h:outputText styleClass="outputText" id="lblTitle"
										style="#{pc_Kmc02302T02.kmc02302.propTitle.labelStyle}" value="#{pc_Kmc02302T02.kmc02302.propTitle.labelName}"></h:outputText></TH>
								<TD width="340px">
									<DIV style="overflow:hidden; white-space:nowrap; text-overflow:ellipsis; width:340px">
									<h:outputText styleClass="outputText" id="htmlTitle" rendered="#{pc_Kmc02302T02.kmc02302.propTitle.rendered}" style="#{pc_Kmc02302T02.kmc02302.propTitle.style}" title="#{pc_Kmc02302T02.kmc02302.propTitle.stringValue}" value="#{pc_Kmc02302T02.kmc02302.propTitle.displayValue}"></h:outputText>
									</DIV>
								</TD>
							</TR>
						</TBODY>
						</TABLE>
						<!--↑タブ間共有テーブル↑-->
						<BR>
						<!--↓タブ用テーブル↓-->
						<TABLE border="0" cellpadding="0" cellspacing="0" width="800px">
						<TBODY>
							<TR>
								<TD align="left">
								<TABLE border="0" cellpadding="0" cellspacing="0">
								<TBODY>
									<TR align="left">
										<TD class="tab_head_on" width="60">
											<hx:commandExButton type="submit" id="moveJugyoTab"
													value="#{pc_Kmc02302T02.kmc02302.propMoveJugyoTab.name}"
													styleClass="tab_head_off"
													action="#{pc_Kmc02302T02.doMoveJugyoTabAction}"
													disabled="#{pc_Kmc02302T02.kmc02302.propMoveJugyoTab.disabled}"
													style="#{pc_Kmc02302T02.kmc02302.propMoveJugyoTab.style}">
												</hx:commandExButton>
										</TD>
										<TD class="tab_head_off" width="60">
											<hx:commandExButton type="button" id="moveTaishoTab"
													value="#{pc_Kmc02302T02.kmc02302.propMoveTaishoTab.name}"
													styleClass="tab_head_on"
													disabled="#{pc_Kmc02302T02.kmc02302.propMoveTaishoTab.disabled}"
													style="#{pc_Kmc02302T02.kmc02302.propMoveTaishoTab.style}">
												</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
								</TD>
							</TR>
							<TR>
								<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="800px" height="410px">
									<TBODY>
										<TR>
											<TD align="center">
												<TABLE border="0" cellpadding="0" cellspacing="0" width="750px" style="margin-top:0px">
												<TBODY>
													<TR>
														<TD width="650px">
															<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
															<TBODY>
																<TR>
																	<TH class="v_b" width="150px">
																		<h:outputText styleClass="outputText"
																			id="lblNyugakuNendo"
																			style="#{pc_Kmc02302T02.propNyugakuNendo.labelStyle}"
																			value="#{pc_Kmc02302T02.propNyugakuNendo.labelName}"></h:outputText></TH>
																	<TD width="180px">
																		<h:inputText styleClass="inputText"
																			id="htmlNyugakuNendo" size="4"
																			disabled="#{pc_Kmc02302T02.propNyugakuNendo.disabled}"
																			readonly="#{pc_Kmc02302T02.propNyugakuNendo.readonly}"
																			rendered="#{pc_Kmc02302T02.propNyugakuNendo.rendered}"
																			style="#{pc_Kmc02302T02.propNyugakuNendo.style}"
																			value="#{pc_Kmc02302T02.propNyugakuNendo.dateValue}">
																			<f:convertDateTime pattern="yyyy" />
																			<hx:inputHelperAssist errorClass="inputText_Error"
																				promptCharacter="_" />
																		</h:inputText></TD>
																	<TH class="v_b" width="140px">
																		<h:outputText styleClass="outputText"
																			id="lblNyugakuGakkiNo"
																			style="#{pc_Kmc02302T02.propNyugakuGakkiNo.labelStyle}"
																			value="#{pc_Kmc02302T02.propNyugakuGakkiNo.labelName}"></h:outputText></TH>
																	<TD width="180px">
																		<h:inputText styleClass="inputText"
																			id="htmlNyugakuGakkiNo" size="2"
																			disabled="#{pc_Kmc02302T02.propNyugakuGakkiNo.disabled}"
																			maxlength="#{pc_Kmc02302T02.propNyugakuGakkiNo.maxLength}"
																			readonly="#{pc_Kmc02302T02.propNyugakuGakkiNo.readonly}"
																			rendered="#{pc_Kmc02302T02.propNyugakuGakkiNo.rendered}"
																			style="#{pc_Kmc02302T02.propNyugakuGakkiNo.style}"
																			value="#{pc_Kmc02302T02.propNyugakuGakkiNo.integerValue}">
																			<f:convertNumber pattern="0" />
																			<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																		</h:inputText></TD>
																</TR>
																<TR>
																	<TH class="v_b" width="150px">
																		<h:outputText styleClass="outputText"
																			id="lblCurGakkaSoshiki"
																			style="#{pc_Kmc02302T02.propCurGakkaSoshiki.labelStyle}"
																			value="#{pc_Kmc02302T02.propCurGakkaSoshiki.labelName}"></h:outputText></TH>
																	<TD colspan="3">
																			<h:selectOneMenu styleClass="selectOneMenu"
																			id="htmlCurGakkaSoshiki"
																			disabled="#{pc_Kmc02302T02.propCurGakkaSoshiki.disabled}"
																			readonly="#{pc_Kmc02302T02.propCurGakkaSoshiki.readonly}"
																			rendered="#{pc_Kmc02302T02.propCurGakkaSoshiki.rendered}"
																			style="#{pc_Kmc02302T02.propCurGakkaSoshiki.style};width:480px"
																			value="#{pc_Kmc02302T02.propCurGakkaSoshiki.stringValue}">
																			<f:selectItems
																				value="#{pc_Kmc02302T02.propCurGakkaSoshiki.list}" />
																		</h:selectOneMenu></TD>
																</TR>
															</TBODY>
															</TABLE>
														</TD>
														<TD align="left" valign="bottom" width="100px">
															<TABLE border="0" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD>
																		<hx:commandExButton type="submit" value="選択"
																			styleClass="commandExButton" id="select"
																			disabled="#{pc_Kmc02302T02.propSelect.disabled}"
																			style="#{pc_Kmc02302T02.propSelect.style}"
																			rendered="#{pc_Kmc02302T02.propSelect.rendered}"
																			action="#{pc_Kmc02302T02.doSelectAction}"></hx:commandExButton>
																		<hx:commandExButton type="submit" value="解除"
																			styleClass="commandExButton" id="unselect"
																			disabled="#{pc_Kmc02302T02.propUnselect.disabled}"
																			rendered="#{pc_Kmc02302T02.propUnselect.rendered}"
																			style="#{pc_Kmc02302T02.propUnselect.style}"
																			action="#{pc_Kmc02302T02.doUnselectAction}"></hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
															</TABLE>
														</TD>
													</TR>
												</TBODY>
												</TABLE>
												<TABLE border="0" cellpadding="0" cellspacing="0" width="750px">
												<TBODY>
													<TR>
														<TD align="right" width="750px"><h:outputText
															styleClass="outputText" id="lblSrcCount"
															value="#{pc_Kmc02302T02.propSrcCurGakkaSoshikiList.listCount}"></h:outputText><h:outputText
															styleClass="outputText" id="lblSrcKen" value="件"></h:outputText></TD>
													</TR>
													<TR>
														<TD align="center">
														<h:dataTable
															border="0" cellpadding="2" cellspacing="0"
															headerClass="headerClass" footerClass="footerClass" width="750"
															rowClasses="#{pc_Kmc02302T02.propSrcCurGakkaSoshikiList.rowClasses}"
															styleClass="meisai_scroll" id="htmlSrcCurGakkaSoshikiListHd">
															<h:column id="colSrcChkHd">
																<f:facet name="header">
																</f:facet>
																<f:attribute value="30" name="width" />
															</h:column>
															<h:column id="colSrcNyugakuNendoHd">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="入学年度" id="headColSrcNyugakuNendo"></h:outputText>
																</f:facet>
																<f:attribute value="110" name="width" />
															</h:column>
															<h:column id="colSrcNyugakuGakkiNoHd">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="入学学期ＮＯ" id="headColSrcNyugakuGakkiNo"></h:outputText>
																</f:facet>
																<f:attribute value="110" name="width" />
															</h:column>
															<h:column id="colHeadSrcCurGakkaSoshikiNameHd">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="カリキュラム学科組織名称" id="headColSrcGakkaSoshikiName"></h:outputText>
																</f:facet>
																<f:attribute value="500" name="width" />
															</h:column>
														</h:dataTable>
														<div class="listScroll" style="height:106px;width:750px;"
															id="listScroll" onscroll="setScrollPosition('srcScroll',this);"><h:dataTable
															border="0" cellpadding="2" cellspacing="0"
															headerClass="headerClass" footerClass="footerClass" width="733"
															rowClasses="#{pc_Kmc02302T02.propSrcCurGakkaSoshikiList.rowClasses}"
															styleClass="meisai_scroll" id="htmlSrcGakkaSoshikiList"
															value="#{pc_Kmc02302T02.propSrcCurGakkaSoshikiList.list}" var="varlist">
															<h:column id="colSrcChk">
																<f:attribute value="30" name="width" />
																	<h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox"
																		id="htmlColSrcCheck"
																		value="#{varlist.propCheck.checked}" disabled="#{varlist.propCheck.disabled}" readonly="#{varlist.propCheck.readonly}" rendered="#{varlist.propCheck.rendered}" style="#{varlist.propCheck.style}"></h:selectBooleanCheckbox>
																</h:column>
															<h:column id="colSrcNyugakuNendo">
																<f:attribute value="110" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlColSrcNyugakuNendo" value="#{varlist.nyugakuNendo}">
																		<f:convertNumber pattern="0000" />
																	</h:outputText>
																</h:column>
															<h:column id="colSrcNyugakuGakkiNo">
																<f:attribute value="110" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlColSrcNyugakuGakkiNo" value="#{varlist.nyugakuGakkiNo}">
																		<f:convertNumber pattern="0"/>
																	</h:outputText>
																</h:column>
															<h:column id="colSrcCurGakkaSoshikiName">
																<f:attribute value="483" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlColSrcCurGakkaSoshikiName"
																		value="#{varlist.propCurGakkaSoshikiName.displayValue}" title="#{varlist.propCurGakkaSoshikiName.stringValue}"></h:outputText>
																</h:column>
														</h:dataTable>
														</div>
														</TD>
													</TR>
													<TR>
														<TD align="left">
															<TABLE  border="0" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD align="left" class="clear_border" width="60">
																		<hx:commandExButton type="button" styleClass="check"
																			id="srccheck"  onclick="return func_srcCheck(this, event);">
																		</hx:commandExButton><hx:commandExButton type="button" styleClass="uncheck" 
																			id="srcuncheck" onclick="return func_srcUncheck(this, event);"></hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
															</TABLE>
														</TD>
													</TR>
													<TR>
														<TD align="center">
														<hx:commandExButton type="submit" value="追加"
																styleClass="commandExButton_dat" id="add"
																disabled="#{pc_Kmc02302T02.propAdd.disabled}"
																rendered="#{pc_Kmc02302T02.propAdd.rendered}"
																style="#{pc_Kmc02302T02.propAdd.style}"
																action="#{pc_Kmc02302T02.doAddAction}"></hx:commandExButton></TD>
													</TR>
													<TR>
														<TD>
															<TABLE  border="0" cellpadding="0" cellspacing="0" width="100%">
															<TBODY>
																<TR>
																	<TD align="left" width="300px">
																	<h:outputText styleClass="outputText" id="text1"
																			value="＜対象カリキュラム＞"></h:outputText></TD>
																	<TD align="right"><h:outputText
																		styleClass="outputText" id="lblDstCount"
																		value="#{pc_Kmc02302T02.propDstCurGakkaSoshikiList.listCount}"></h:outputText><h:outputText
																		styleClass="outputText" id="lblDstKen" value="件"></h:outputText></TD>
																</TR>
															</TBODY>
															</TABLE>
														</TD>
													</TR>
													<TR>
														<TD align="center" width="750px">
														<h:dataTable
															border="0" cellpadding="2" cellspacing="0"
															headerClass="headerClass" footerClass="footerClass" width="750"
															rowClasses="#{pc_Kmc02302T02.propDstCurGakkaSoshikiList.rowClasses}"
															styleClass="meisai_scroll" id="htmlDstGakkaSoshikiListHd">
															<h:column id="colDstChkHd">
																<f:facet name="header">
																</f:facet>
																<f:attribute value="30" name="width" />
															</h:column>
															<h:column id="colDstNyugakuNendoHd">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="入学年度" id="headColDstNyugakuNendo"></h:outputText>
																</f:facet>
																<f:attribute value="110" name="width" />
															</h:column>
															<h:column id="colDstNyugakuGakkiNoHd">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="入学学期ＮＯ" id="headColDstNyugakuGakkiNo"></h:outputText>
																</f:facet>
																<f:attribute value="110" name="width" />
															</h:column>
															<h:column id="colDstCurGakkaSoshikiNameHd">
																<f:facet name="header">
																	<h:outputText styleClass="outputText" value="カリキュラム学科組織名称" id="headColDstGakkaSoshikiName"></h:outputText>
																</f:facet>
																<f:attribute value="500" name="width" />
															</h:column>
														</h:dataTable>
														<div class="listScroll" style="height:106px;width:750px;"
															id="listScroll" onscroll="setScrollPosition('dstScroll',this);"><h:dataTable
															border="0" cellpadding="2" cellspacing="0"
															headerClass="headerClass" footerClass="footerClass" width="733"
															rowClasses="#{pc_Kmc02302T02.propDstCurGakkaSoshikiList.rowClasses}"
															styleClass="meisai_scroll" id="htmlDstCurGakkaSoshikiList"
															value="#{pc_Kmc02302T02.propDstCurGakkaSoshikiList.list}" var="varlist">
															<h:column id="colDstChk">
																<f:attribute value="30" name="width" />
																	<h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox" id="htmlColDstCheck" disabled="#{varlist.propCheck.disabled}" readonly="#{varlist.propCheck.readonly}" rendered="#{varlist.propCheck.rendered}" style="#{varlist.propCheck.style}" value="#{varlist.propCheck.checked}"></h:selectBooleanCheckbox>
																</h:column>
															<h:column id="colDstNyugakuNendo">
																<f:attribute value="110" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlColDstNyugakuNendo" value="#{varlist.nyugakuNendo}">
																		<f:convertNumber pattern="0" />
																	</h:outputText>
																</h:column>
															<h:column id="colDstNyugakuGakkiNo">
																<f:attribute value="110" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlColDstNyugakuGakkiNo" value="#{varlist.nyugakuGakkiNo}">
																		<f:convertNumber pattern="0" />
																	</h:outputText>
																</h:column>
															<h:column id="colDstCurGakkaSoshikiName">
																<f:attribute value="483" name="width" />
																	<h:outputText styleClass="outputText"
																		id="htmlCurGakkaSoshikiName" value="#{varlist.propCurGakkaSoshikiName.displayValue}" title="#{varlist.propCurGakkaSoshikiName.stringValue}"></h:outputText>
																</h:column>
														</h:dataTable>
														</div>
														</TD>
													</TR>
													<TR>
														<TD align="left">
															<TABLE  border="0" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD align="left" class="clear_border">
																		<hx:commandExButton type="button" styleClass="check"
																			id="dstcheck"  onclick="return func_dstCheck(this, event);">
																		</hx:commandExButton><hx:commandExButton type="button" styleClass="uncheck" 
																			id="dstuncheck" onclick="return func_dstUncheck(this, event);"></hx:commandExButton>
																		<hx:commandExButton id="deleteSrcCurGakkaSoshikiList"
																			styleClass="commandExButton" type="submit" value="削除"
																			disabled="#{pc_Kmc02302T02.propDeleteSrcCurGakkaSoshiki.disabled}"
																			rendered="#{pc_Kmc02302T02.propDeleteSrcCurGakkaSoshiki.rendered}"
																			style="#{pc_Kmc02302T02.propDeleteSrcCurGakkaSoshiki.style}"
																			action="#{pc_Kmc02302T02.doDeleteSrcCurGakkaSoshikiListAction}">
																		</hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
															</TABLE>
														</TD>
													</TR>
												</TBODY>
												</TABLE>										
											</TD>
										</TR>
									</TBODY>
									</TABLE>
								</TD>
							</TR>
						</TBODY>
						</TABLE>
						<!--↑タブ用テーブル↑-->
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE border="0" cellspacing="0" class="button_bar" width="100%" style="margin-top:5px">
							<TBODY>
								<TR>
									<TD nowrap>
										<hx:commandExButton type="submit" value="確定"
												styleClass="commandExButton_dat" id="register"
												confirm="#{msg.SY_MSG_0002W}"
												action="#{pc_Kmc02302T02.doRegisterAction}"
												disabled="#{pc_Kmc02302T02.kmc02302.propRegister.disabled}"
												style="#{pc_Kmc02302T02.kmc02302.propRegister.style}"
												rendered="#{pc_Kmc02302T02.kmc02302.propRegister.rendered}">
											</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<h:inputHidden id="srcScroll"
				value="#{pc_Kmc02302T02.propSrcCurGakkaSoshikiList.scrollPosition}">
			</h:inputHidden>
			<h:inputHidden id="dstScroll"
				value="#{pc_Kmc02302T02.propDstCurGakkaSoshikiList.scrollPosition}">
			</h:inputHidden>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page ="../inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
	
</f:view>

</HTML>
