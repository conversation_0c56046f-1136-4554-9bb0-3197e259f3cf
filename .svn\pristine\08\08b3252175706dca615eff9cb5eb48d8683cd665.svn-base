<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssd01201T03.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssd01201T03.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css" type="text/css" />

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssd01201T03.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">

	<!-- ヘッダーインクルード -->
	<jsp:include page ="../inc/header.jsp" />

	<!-- ヘッダーへのデータセット領域 -->
	<DIV style="display:none;">
		<hx:commandExButton type="submit" value="閉じる"
			styleClass="commandExButton" id="closeDisp"
			action="#{pc_Ssd01201T03.doCloseDispAction}"></hx:commandExButton>
		<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssd01201T03.funcId}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
		<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssd01201T03.screenName}"></h:outputText>
	</DIV>

	<!--↓outer↓-->
	<DIV class="outer">

	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
		<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			styleClass="outputText" escape="false">
		</h:outputText>
	</FIELDSET>
			
	<!--↓content↓-->
	<DIV class="head_button_area" >
　<!-- ←レイアウトの問題の為に、全角スペースを配置 -->
	</DIV>

	<DIV id="content">
		<DIV class="column">
			<TABLE border="0" class="table" width="800" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblKjnNendo"
							value="#{pc_Ssd01201T01.ssd01201.propKjnNendo.labelName}"
							style="#{pc_Ssd01201T01.ssd01201.propKjnNendo.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:inputText styleClass="inputText" id="htmlKjnNendo"
							value="#{pc_Ssd01201T01.ssd01201.propKjnNendo.value}"
							readonly="#{pc_Ssd01201T01.ssd01201.propKjnNendo.readonly}"
							style="#{pc_Ssd01201T01.ssd01201.propKjnNendo.style}"
							disabled="#{pc_Ssd01201T01.ssd01201.propKjnNendo.disabled}"
							size="6">
							<hx:inputHelperAssist imeMode="inactive"
								errorClass="inputText_Error" promptCharacter="_" />

							<f:convertDateTime pattern="yyyy" />
						</h:inputText><h:outputText
							styleClass="outputText" value="年度"></h:outputText></TD>
					</TR>
					<TR>
						<TH nowrap class="v_b" width="150"><h:outputText styleClass="outputText"
							id="lblChohyoTitle" 
							value="#{pc_Ssd01201T01.ssd01201.propChohyoTitle.labelName}"
							style="#{pc_Ssd01201T01.ssd01201.propChohyoTitle.labelStyle}"></h:outputText></TH>
						<TD width="650"><h:inputText styleClass="inputText"
							id="htmlChohyoTitle"
							value="#{pc_Ssd01201T01.ssd01201.propChohyoTitle.stringValue}"
							style="#{pc_Ssd01201T01.ssd01201.propChohyoTitle.style}" size="70"
							disabled="#{pc_Ssd01201T01.ssd01201.propChohyoTitle.disabled}"
							maxlength="#{pc_Ssd01201T01.ssd01201.propChohyoTitle.maxLength}"
							readonly="#{pc_Ssd01201T01.ssd01201.propChohyoTitle.readonly}">
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<TABLE border="0" class="table" width="800" cellspacing="0" cellpadding="0" style="margin-top:3px;">
				<TBODY>
					<TR>
						<TH class="v_a" width="151"><h:outputText
							styleClass="outputText" id="lblGakko"
							value="#{pc_Ssd01201T01.ssd01201.propGakko.labelName}"
							style="#{pc_Ssd01201T01.ssd01201.propGakko.labelStyle}"></h:outputText></TH>
						<TD width="649"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlGakko"
							disabled="#{pc_Ssd01201T01.ssd01201.propGakko.disabled}"
							style="#{pc_Ssd01201T01.ssd01201.propGakko.style}"
							value="#{pc_Ssd01201T01.ssd01201.propGakko.value}">
							<f:selectItems value="#{pc_Ssd01201T01.ssd01201.propGakko.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<TABLE width="800" border="0" cellpadding="0" cellspacing="0" style="margin-top:15px;">
				<TBODY>
					<TR>
						<TD style="text-align:left;">
						<hx:commandExButton	type="submit"
							value="全件指定" styleClass="tab_head_off" 
							id="btnSsd01201T01"
							action="#{pc_Ssd01201T03.doBtnSsd01201T01Action}" style="width:13%"></hx:commandExButton><hx:commandExButton
							type="submit" value="郵便番号指定" styleClass="tab_head_off"
							id="btnSsd01201T02"
							action="#{pc_Ssd01201T03.doBtnSsd01201T02Action}" style="width:13%"></hx:commandExButton><hx:commandExButton
							type="submit" value="地域指定" styleClass="tab_head_on"
							id="btnSsd01201T03" style="width:13%"></hx:commandExButton><hx:commandExButton
							type="submit" value="業種指定" styleClass="tab_head_off"
							id="btnSsd01201T04"
							action="#{pc_Ssd01201T03.doBtnSsd01201T04Action}" style="width:13%"></hx:commandExButton><hx:commandExButton 
							type="submit" value="企業指定" styleClass="tab_head_off"
							id="btnSsd01201T05"
							action="#{pc_Ssd01201T03.doBtnSsd01201T05Action}" style="width:13%"></hx:commandExButton></TD>
					</TR>
					<TR>
						<TD style="align:left;">
						<TABLE width="100%" border="0" cellspacing="0" cellpadding="0" class="tab_body">
							<TBODY>
								<TR>
									<TD align="center">
									<DIV style="height:395px">
										<TABLE width="600" border="0" cellspacing="0" class=""  style="margin-top:50px;">
											<TBODY>
												<TR>
													<TH width="80%" align="left"><h:outputText styleClass="outputText"
														id="lblGyosyuHeaderCmt" value="地域一覧"></h:outputText></TH>
													<TD width="20%" style="text-align: right">
														<h:outputText styleClass="outputText" id="htmlListChiikiCnt" value="#{pc_Ssd01201T03.propListChiiki.listCount == 0 ? 0 : pc_Ssd01201T03.propListChiiki.listCount - 1}"></h:outputText>
														<h:outputText styleClass="outputText" id="lblListChiikiCnt" value="件"></h:outputText>
													</TD>
												</TR>
												<TR>
												<TD colspan="2">
													<TABLE width="600" border="0" cellspacing="0" class="list_table">
													<TBODY>
														<TR>
															<TD colspan="2"><h:selectManyListbox
																	styleClass="selectManyListbox" id="htmlListChiiki"
																	size="18" style="width:100%;"
																	disabled="#{pc_Ssd01201T03.propListChiiki.disabled}"
																	readonly="#{pc_Ssd01201T03.propListChiiki.readonly}"
																	value="#{pc_Ssd01201T03.propListChiiki.value}">
																	<f:selectItems
																		value="#{pc_Ssd01201T03.propListChiiki.list}" />
															</h:selectManyListbox></TD>
														</TR>
													</TBODY>
													</TABLE>
												</TD>
												</TR>
												<TR>
													<TD colspan="2" style="text-align:right;" width=""><h:outputText
														styleClass="note" id="lblSelectCmt"
														value="（複数選択可）"></h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</DIV>
									</TD>
								</TR>
								
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE width="100%" class="button_bar">
								<TBODY>
									<TR>
										<TD><hx:commandExButton type="submit" value="PDF作成"
											styleClass="commandExButton_out" id="pdfout"
											action="#{pc_Ssd01201T03.doPdfoutAction}"
											confirm="#{msg.SY_MSG_0019W}"
											></hx:commandExButton><hx:commandExButton type="submit" value="CSV作成"
											styleClass="commandExButton_out" id="csvout"
											action="#{pc_Ssd01201T03.doCsvoutAction}"
											confirm="#{msg.SY_MSG_0020W}"
											></hx:commandExButton><hx:commandExButton type="submit" value="出力項目指定"
											styleClass="commandExButton_out" id="setoutput"
											action="#{pc_Ssd01201T03.doSetoutputAction}"></hx:commandExButton></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
		</DIV>
	</DIV>
	<!--↑outer↑-->
	<!-- フッダーインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
	</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
	</f:view>
</HTML>
