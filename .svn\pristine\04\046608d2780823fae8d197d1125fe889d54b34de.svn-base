<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc02001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
<BODY >
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Nsc02001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Nsc02001.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Nsc02001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Nsc02001.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>


						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="80%">
							<TBODY>
								<TR>
									<TH class="v_a" width="187"><h:outputText
										styleClass="outputText" id="lblNyushiNendo" value="#{pc_Nsc02001.propNyushiNendo.labelName}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="htmlNyushiNendo" value="#{pc_Nsc02001.propNyushiNendo.stringValue}"></h:outputText></TD>
									<TH width="187" class="v_b"><h:outputText
										styleClass="outputText" id="lblNyushiGakki" value="#{pc_Nsc02001.propNyushiGakki.labelName}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlNyushiGakki" value="#{pc_Nsc02001.propNyushiGakki.stringValue}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style="margin-top:10px"
							class="table" width="80%">
							<TBODY>
								<TR>
									<TH colspan="" class="v_c" width="200"><h:outputText
										styleClass="outputText" id="lblFileSyurui" value="入力ファイル種類"></h:outputText></TH>
									<TD class="v_d"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlFileSyurui"
										layout="pageDirection" value="#{pc_Nsc02001.propFileSyurui.value}" tabindex="1">
										<f:selectItem itemValue="0" itemLabel="共通テスト成績提供データ" />
										<f:selectItem itemValue="1" itemLabel="志願者情報データ" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="" width="200">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFile" value="#{pc_Nsc02001.propInputFile.labelName}"
													style="#{pc_Nsc02001.propInputFile.labelStyle}"></h:outputText></TH>
											</TR>
											<TR>
												<TH class="clear_border"><h:outputText
													styleClass="outputText" id="lblInputFilePre"
													value="(前回ファイル)"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									</TH>
									<TD class="">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border"><hx:fileupload
													styleClass="fileupload" id="htmlInputFile" size="50"
													value="#{pc_Nsc02001.propInputFile.value}" tabindex="2"
													style="width: 550px">
													<hx:fileProp name="fileName"
														value="#{pc_Nsc02001.propInputFile.fileName}" />
													<hx:fileProp name="contentType"
														value="#{pc_Nsc02001.propInputFile.contentType}" />
												</hx:fileupload></TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:outputText
													styleClass="outputText" id="htmlInputFilePre" value="#{pc_Nsc02001.propInputFilePre.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_h" width="200"><h:outputText
										styleClass="outputText" id="lblTelKbn" value="#{pc_Nsc02001.propTelKbn.labelName}">
										</h:outputText><BR>　(共通テスト成績提供データ)
									</TH>
									<TD class=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlTelKbn"
										layout="pageDirection" value="#{pc_Nsc02001.propTelKbn.value}" tabindex="3">
										<f:selectItem itemValue="0" itemLabel="志願者電話番号1" />
										<f:selectItem itemValue="1"
											itemLabel="連絡先電話番号" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_e" width="200"><h:outputText
										styleClass="outputText" id="lblDataKbn" value="#{pc_Nsc02001.propDataKbn.labelName}"></h:outputText></TH>
									<TD class=""><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlDataKbn"
										layout="pageDirection" value="#{pc_Nsc02001.propDataKbn.value}" tabindex="4">
										<f:selectItem itemValue="2" itemLabel="データを更新します。全項目を更新します。" />
										<f:selectItem itemValue="3"
											itemLabel="データを更新します。入力ファイルに指定された項目のみ更新します。" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH colspan="" class="v_f" width="200"><h:outputText
										styleClass="outputText" id="lblSyoriKubunL" value="#{pc_Nsc02001.propSyoriKbn.labelName}"></h:outputText></TH>
									<TD class="v_e"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSyoriKbn" value="#{pc_Nsc02001.propSyoriKbn.checked}" tabindex="5"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblSyoriKubun"
										value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g" width="200"><h:outputText
										styleClass="outputText" id="lblCheckList" value="#{pc_Nsc02001.propCheckList.labelName}"></h:outputText></TH>
									<TD class="v_e"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlCheckList"
										layout="pageDirection" value="#{pc_Nsc02001.propCheckList.value}" tabindex="6">
										<f:selectItem itemValue="0" itemLabel="正常データ" />
										<f:selectItem itemValue="1" itemLabel="エラーデータ" />
										<f:selectItem itemValue="2" itemLabel="ワーニングデータ" />
									</h:selectManyCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar" >
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="入力項目指定"
										styleClass="commandExButton_etc" id="setinput"
										tabindex="7"
										action="#{pc_Nsc02001.doSetinputAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="実行" styleClass="commandExButton_dat"
										id="exec" confirm="#{msg.SY_MSG_0001W}"
										action="#{pc_Nsc02001.doExecAction}" tabindex="8"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

