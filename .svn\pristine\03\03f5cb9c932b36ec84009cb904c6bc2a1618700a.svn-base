<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghe00401T02.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Ghe00401.jsp</TITLE>
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<SCRIPT type="text/javascript">

	function func_check_on(thisObj, thisEvent) {
		check('htmlPayhItemList','htmlSelectedList');
	}
	
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPayhItemList','htmlSelectedList');
	}
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		document.getElementById('form1:htmlExecutableSearch').value = 1;
		indirectClick('search');
	}
	
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}

	function fncButtonActive(){
	    var codeRegSearch = null;
	    var codeRegExec = null;

		//選択ボタン
		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch').value;
		if(codeRegSearch == 1){
			document.getElementById('form1:search').disabled = true;
		} else {
			document.getElementById('form1:unselect').disabled = true;
			document.getElementById('form1:takein').disabled = true;
			document.getElementById('form1:addition').disabled = true;
			document.getElementById('form1:exclusion').disabled = true;	
			document.getElementById('form1:allExclusion').disabled = true;	
		}
		
		//実行ボタン
		codeRegExec = document.getElementById('form1:htmlActiveControlExec').value;
		if(codeRegExec == 1){
			document.getElementById('form1:execute').disabled = true;
		}
		
//	▽▽ 2007-05-25 Tsutsumi 障害No 7200 対応　変更　START ▽▽
//		//スクロール位置保持
//		changeScrollPosition('scrollPayh', 'listScrollPayh');
//		changeScrollPosition('scrollPayhItem', 'listScrollPayhItem');
		window.attachEvent('onload', endload);		
//	△△ 2007-05-25 Tsutsumi 障害No 7200 対応　変更 END △△　
		
		//フォーカスの設定
		setFocus();
	}

//	▽▽ 2007-05-25 Tsutsumi 障害No 7200 対応　追加 START ▽▽
	function endload() {
		//スクロール位置保持
		changeScrollPosition('scrollPayh', 'listScrollPayh');
		changeScrollPosition('scrollPayhItem', 'listScrollPayhItem');
	}
//	△△ 2007-05-25 Tsutsumi 障害No 7200 対応　追加 END △△　

	//学費学生検索画面へ遷移
	function openPGhz0301Window() {
		openPGhz0301("<%=com.jast.gakuen.rev.gh.PGhz0301.getWindowOpenOption() %>");
		return true;
	}
	
	//学籍情報取得Ajax呼び出し
	function ajaxGakusekiCd(thisObj, targetLabel) {
		
		//学籍氏名取得Ajax呼び出し
		funcAjaxGakusekiCd(thisObj, '1', targetLabel);
		
		return true;
	}
	
	//フォーカスの設定
	function setFocus(){
		var id = null;
		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;
		//リスト内選択ボタンフォーカス時
		if ((id != null) && (id != "")) {
			document.getElementById(id).focus();
		}
		//初期化
		document.getElementById('form1:htmlScrollPos').value = "";
	}
		
	
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY onload="fncButtonActive();">
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghe00401T02.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp"
                    action="#{pc_Ghe00401T01.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghe00401T02.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghe00401T02.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">           
                    <DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
											<TBODY>
												<TR align="left">
													<TD>
														<hx:commandExButton 
															type="submit" value="一括指定" 
															styleClass="tab_head_off" id="htmlTab1" 
															action="#{pc_Ghe00401T02.doIkkatsuTabAction}" tabindex="1"></hx:commandExButton><hx:commandExButton 
															type="submit" value="学生指定" 
															styleClass="tab_head_on" id="htmlTab2" tabindex="2">
														</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="1" cellpadding="20" cellspacing="0" width="100%" class="tab_body">
															<TBODY>
																<TR align="center">
																	<TD>
																		<DIV style="height: 605px">
																			<TABLE width="850px">
																				<TBODY>
																					<TR>
																						<TD height="3px"></TD>
																					</TR>
																					<TR>
																						<TD>
																							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																								<TBODY>
																									<TR>
																										<TH class="v_a" width="120px">
																											<h:outputText
																												styleClass="outputText" id="lblNendo"
																												value="#{pc_Ghe00401T02.propNendo.labelName}"
																												style="#{pc_Ghe00401T02.propNendo.labelStyle}">
																											</h:outputText>
																										</TH>
																										<TD width="180px">
																											<h:inputText
																												styleClass="inputText" id="htmlNendo"
																												value="#{pc_Ghe00401T02.propNendo.dateValue}"
																												style="#{pc_Ghe00401T02.propNendo.style}" size="4"
																												maxlength="#{pc_Ghe00401T02.propNendo.maxLength}"
																												disabled="#{pc_Ghe00401T02.propNendo.disabled}" tabindex="3">
																											<hx:inputHelperAssist imeMode="inactive"
																													errorClass="inputText_Error" promptCharacter="_" />
																												<f:convertDateTime pattern="yyyy" />
																											</h:inputText>
																											<hx:commandExButton type="submit" value="選択"
																												styleClass="cmdBtn_dat_s" id="search"
																												action="#{pc_Ghe00401T02.doSearchAction}" tabindex="4">
																											</hx:commandExButton>
																											<hx:commandExButton 
																												type="submit" value="解除"
																												styleClass="cmdBtn_dat_s" id="unselect" tabindex="5" 
																												action="#{pc_Ghe00401T02.doUnselectAction}">
																											</hx:commandExButton>
																										</TD>
																										
																										<TH width="120px" class="v_b">
																											<h:outputText
																												styleClass="outputText" id="lblMenjWay"
																												value="#{pc_Ghe00401T02.propMenjWay.labelName}"
																												style="#{pc_Ghe00401T02.propMenjWay.labelStyle}">
																											</h:outputText>
																										</TH>
																										<TD width="210px" style="border-right-style:none;">
																											<h:selectOneRadio
																												disabledClass="selectOneRadio_Disabled"
																												styleClass="selectOneRadio" id="htmlMenjWay"
																												value="#{pc_Ghe00401T02.propMenjWay.value}"
																												style="#{pc_Ghe00401T02.propMenjWay.style}"
																												disabled="#{pc_Ghe00401T02.propMenjWay.disabled}"
																												layout="lineDirection" tabindex="6">
																												<f:selectItem itemValue="1" itemLabel="金額指定" />
																												<f:selectItem itemValue="2" itemLabel="免除割合(％) (半3)" />
																											</h:selectOneRadio>
																										</TD>
																										<TD nowrap width="*" valign="bottom" style="border-left-style:none;">
																											<h:inputText
																												styleClass="inputText" id="htmlMenjRate"
																												value="#{pc_Ghe00401T02.propMenjRate.value}"
																												style="#{pc_Ghe00401T02.propMenjRate.style}" size="3"
																												maxlength="#{pc_Ghe00401T02.propMenjRate.maxLength}"
																												disabled="#{pc_Ghe00401T02.propMenjRate.disabled}" tabindex="7">
																											</h:inputText>
																										</TD>
																										
																										
																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>

																					<TR>
																						<TD>
																							<TABLE width="100%" class="layoutTable" border="0" cellpadding="0" cellspacing="0">
																								<TBODY>
																									<TR>
																										<TD align="right">
																											<h:outputText styleClass="outputText"
																												id="lblCount" value="#{pc_Ghe00401T02.propPayhList.listCount}">
																											</h:outputText>
																											<h:outputText
																												styleClass="outputText" id="htmlCount" value="件">
																											</h:outputText>
																										</TD>
																									</TR>
																									<TR>
																										<TD>
																											<DIV style="height: 108px; width=100%;" id="listScrollPayh" onscroll="setScrollPosition('scrollPayh',this);" class="listScroll">
																												<h:dataTable 
																													rows="#{pc_Ghe00401T02.propPayhList.rows}"
																													rowClasses="#{pc_Ghe00401T02.propPayhList.rowClasses}"
																													headerClass="headerClass"
																													footerClass="footerClass" styleClass="meisai_scroll"
																													id="htmlPayHList"
																													value="#{pc_Ghe00401T02.propPayhList.list}"
																													var="varlist" width="827px">
																													<h:column id="column3">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText" 
																																value="#{pc_Ghe00401T02.propPayCd.labelName}"
																																id="text6">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="text7" 
																															value="#{varlist.payCd}"
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="120px" name="width" />
																													</h:column>
																													<h:column id="column4">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText" 
																																value="#{pc_Ghe00401T02.propPatternCd.labelName}"
																																id="text8">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="text9" 
																															value="#{varlist.patternCd}"
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="120px" name="width" />
																													</h:column>
																													<h:column id="column5">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText"
																																value="#{pc_Ghe00401T02.propBunnoKbnCd.labelName}" 
																																id="text10">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="text11" 
																															value="#{varlist.bunnoKbnCd}"
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="100px" name="width" />
																														<f:attribute value="text-align: center" name="style" />
																													</h:column>
																													<h:column id="column6">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText" 
																																value="#{pc_Ghe00401T02.propPayName.labelName}"
																																id="text12">
																															</h:outputText>
																														</f:facet>
																														<h:outputText id="text13"
																															value="#{varlist.payName}" 
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="*" name="width" />
																													</h:column>
																													<h:column id="column7">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText" 
																																value="#{pc_Ghe00401T02.propBunkatsuNo.labelName}"
																																id="text14">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="text15" 
																															value="#{varlist.bunkatsuNo}"
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="100px" name="width" />
																														<f:attribute value="text-align: center" name="style" />
																													</h:column>				
																													<h:column id="column11">
																														<f:facet name="header">
																														</f:facet>
																														<hx:commandExButton type="submit" value="選択"
																															styleClass="cmdBtn_dat_s" id="select"
																															disabled="#{varlist.select}"
																															rendered="#{varlist.rendered}"  
																															action="#{pc_Ghe00401T02.doSelectAction}" tabindex="8">
																														</hx:commandExButton>
																													<f:attribute value="40px" name="width" />
																													</h:column>
																												</h:dataTable>
																											</DIV>
																										</TD>
																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>
																					<TR>
																						<TD height="3px"></TD>
																					</TR>
																					<TR>
																						<TD>
																							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																								<TBODY>
																									<TR>
																										<TH nowrap class="v_c" width="100px">
																											<!-- 納付金コード -->
																											<h:outputText
																												styleClass="outputText" id="lblPayCd"
																												value="#{pc_Ghe00401T02.propPayCd.labelName}"
																												style="#{pc_Ghe00401T02.propPayCd.labelStyle}">
																											</h:outputText>
																										</TH>
																										<TD width="150px">
																											<h:outputText 
																												styleClass="outputText" id="htmlPayCd"
																													value="#{pc_Ghe00401T02.propPayCd.stringValue}"
																													style="#{pc_Ghe00401T02.propPayCd.style}">
																												</h:outputText>
																											</TD>
																											<TH nowrap class="v_g" width="100px">
																												<!-- 納付金名称 -->
																												<h:outputText
																													styleClass="outputText" id="lblPayName"
																													value="#{pc_Ghe00401T02.propPayName.labelName}"
																													style="#{pc_Ghe00401T02.propPayName.labelStyle}">
																												</h:outputText>
																											</TH>
																											<TD width="250px" colspan="7">
																												<h:outputText 
																													styleClass="outputText"
																													id="htmlPayName"
																													value="#{pc_Ghe00401T02.propPayName.stringValue}"
																													style="#{pc_Ghe00401T02.propPayName.style}">
																												</h:outputText>
																											</TD>
																											<TH nowrap class="v_d" width="100px">
																												<!-- パターンコード -->
																												<h:outputText
																													styleClass="outputText" id="lblPatternCd"
																													value="#{pc_Ghe00401T02.propPatternCd.labelName}"
																													style="#{pc_Ghe00401T02.propPatternCd.labelStyle}">
																												</h:outputText>
																											</TH>
																											<TD width="150px">
																												<h:outputText 
																													styleClass="outputText" id="htmlPatternCd"
																													value="#{pc_Ghe00401T02.propPatternCd.stringValue}"
																													style="#{pc_Ghe00401T02.propPatternCd.style}">
																												</h:outputText>
																											</TD>
																										</TR>
																										<TR>
																											<TH nowrap class="v_e" width="100px">
																												<!-- 分納区分コード -->
																												<h:outputText
																													styleClass="outputText" id="lblBunnoKbnCd"
																													value="#{pc_Ghe00401T02.propBunnoKbnCd.labelName}"
																													style="#{pc_Ghe00401T02.propBunnoKbnCd.labelStyle}">
																												</h:outputText>
																											</TH>
																											<TD width="150px">
																												<h:outputText 
																													styleClass="outputText" id="htmlBunnoKbnCd"
																													value="#{pc_Ghe00401T02.propBunnoKbnCd.stringValue}"
																													style="#{pc_Ghe00401T02.propBunnoKbnCd.style}">
																												</h:outputText>
																											</TD>
																											<TH nowrap class="v_a" width="100px">
																												<!-- 分納区分名称 -->
																												<h:outputText
																													styleClass="outputText" id="lblBunnoKbnName"
																													value="#{pc_Ghe00401T02.propBunnoKbnName.labelName}"
																													style="#{pc_Ghe00401T02.propBunnoKbnName.labelStyle}">
																												</h:outputText>
																											</TH>
																											<TD width="250px" colspan="7">
																												<h:outputText 
																													styleClass="outputText"
																													id="htmlBunnoKbnName"
																													value="#{pc_Ghe00401T02.propBunnoKbnName.stringValue}"
																													style="#{pc_Ghe00401T02.propBunnoKbnName.style}">
																											</h:outputText>
																										</TD>
																											<TH nowrap class="v_f" width="100px">
																												<!-- 分割NO -->
																												<h:outputText
																													styleClass="outputText" id="lblBunkatsuNo"
																													value="#{pc_Ghe00401T02.propBunkatsuNo.labelName}"
																													style="#{pc_Ghe00401T02.propBunkatsuNo.labelStyle}">
																												</h:outputText>
																											</TH>
																											<TD width="150px">
																												<h:outputText 
																													styleClass="outputText" id="htmlBunkatsuNo"
																													value="#{pc_Ghe00401T02.propBunkatsuNo.stringValue}"
																													style="#{pc_Ghe00401T02.propBunkatsuNo.style}">
																												</h:outputText>
																											</TD>

																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>
																					
																					<TR>
																						<TD height="3px"></TD>
																					</TR>
																					
																					<TR>
																						<TD>
																							<TABLE width="100%" class="layoutTable" border="0" cellpadding="0" cellspacing="0">
																								<TBODY>	
																									<TR>
																										<TD>
																											<DIV style="height: 94px; width=100%;" id="listScrollPayhItem" onscroll="setScrollPosition('scrollPayhItem',this);" class="listScroll">
																												<h:dataTable 
																													rows="#{pc_Ghe00401T02.propPayhItemList.rows}"
																													rowClasses="#{pc_Ghe00401T02.propPayhItemList.rowClasses}"
																													headerClass="headerClass"
																													footerClass="footerClass" styleClass="meisai_scroll"
																													id="htmlPayhItemList"
																													value="#{pc_Ghe00401T02.propPayhItemList.list}"
																													var="varlist" width="827px">
																													<h:column id="column2">
																														<f:facet name="header">
																														</f:facet>
																														<h:selectBooleanCheckbox 
																															styleClass="selectBooleanCheckbox"
																															value="#{varlist.selected}" 
																															id="htmlSelectedList"
																															rendered="#{varlist.rendered}"
																															disabled="#{varlist.txtmenjyoGaku.disabled}" tabindex="9">
																														</h:selectBooleanCheckbox>
																														<f:attribute value="30px" name="width" />
																														<f:attribute value="text-align: left" name="style" />
																													</h:column>
																													<h:column id="colOkNgName">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText" 
																																value="#{pc_Ghe00401T02.propOkNg.name}"
																																id="lblOkNgNameList">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="htmlOkNgNameList" 
																															value="#{varlist.okNgName}"
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="80px" name="width" />
																														<f:attribute value="text-align: left" name="style" />
																													</h:column>
																													<h:column id="colItemNo">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText"
																																value="#{pc_Ghe00401T02.propPayItemNo.labelName}"
																																id="lbllItemNoList">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="htmlItemNoList" 
																															value="#{varlist.itemNo}"
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="80px" name="width" />
																														<f:attribute value="text-align: left" name="style" />
																													</h:column>
																													<h:column id="colItemName">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText"
																																value="#{pc_Ghe00401T02.propPayItemName.labelName}"
																																id="lblItemNameList">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="htmlItemNameList"
																															value="#{varlist.itemName}" 
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="*" name="width" />
																														<f:attribute value="text-align: left" name="style" />
																													</h:column>
																													<h:column id="colItemGaku">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText"
																																value="#{pc_Ghe00401T02.propPayItemGaku.name}"
																																id="lblItemGakuList">
																															</h:outputText>
																														</f:facet>
																														<h:outputText 
																															id="htmlItemGakuList"
																															value="#{varlist.itemGaku}" 
																															styleClass="outputText">
																														</h:outputText>
																														<f:attribute value="100px" name="width" />
																														<f:attribute value="text-align: left" name="style" />
																													</h:column>
																													<h:column id="colTxtMenjyoGaku">
																														<f:facet name="header">
																															<h:outputText 
																																styleClass="outputText"
																																value="#{pc_Ghe00401T02.propMenjGaku.labelName}"
																																id="lblTxtMenjyoGakuList">
																															</h:outputText>
																														</f:facet>
																														<h:inputText styleClass="inputText"
																															id="htmlTxtMenjyoGakuList"
																															value="#{varlist.txtmenjyoGaku.stringValue}"
																															maxlength="#{varlist.txtmenjyoGaku.maxLength}"
																															size="9" rendered="#{varlist.rendered}" 
																															style="#{varlist.txtmenjyoGaku.style};text-align: right;padding-right: 3px;"
																															disabled="#{varlist.txtmenjyoGaku.disabled}" tabindex="10">
																															<hx:inputHelperAssist errorClass="inputText_Error" />
																														</h:inputText>
																														<f:attribute value="100px" name="width" />
																														<f:attribute value="text-align: left" name="style" />
																													</h:column>
																												</h:dataTable>
																											</DIV>
																										</TD>
																									</TR>
																									<TR>
																										<TD>
																											<TABLE border="0" cellpadding="2" cellspacing="0" class="meisai_scroll" width="100%">
																												<TBODY>
																													<TR>
																														<TD class="footerClass">
																															<TABLE class="panelBox">
																																<TBODY>
																																	<TR>
																																		<TD>
																																			<%-- 全選択・全解除 --%>
																																			<hx:jspPanel id="bankJspPanel">
																																				<INPUT type="button" name="check" value="on"
																																					onclick="return func_check_on(this, event, 0);"
																																					class="check" tabindex="11">
																																				<INPUT type="button" name="uncheck" value="off"
																																					onclick="return func_check_off(this, event, 0);"
																																					class="uncheck" tabindex="12">
																																			</hx:jspPanel>
																																		</TD>
																																	</TR>
																																</TBODY>
																															</TABLE>
																														</TD>
																													</TR>
																												</TBODY>
																											</TABLE>
																										</TD>
																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>
																					<TR>
																						<TD height="10"></TD>
																					</TR>
																					<TR>
																						<TD>
																							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																								<TBODY>
																									<TR>
																										<TH class="v_b" width="150px">
																											<h:outputText 
																												styleClass="outputText" id="lblMenjDate"
																												value="#{pc_Ghe00401T02.propMenjDate.labelName}"
																												style="#{pc_Ghe00401T02.propMenjDate.labelStyle}">
																											</h:outputText>
																										</TH>
																										<TD width="150px" nowrap>
																											<h:inputText 
																												styleClass="inputText" id="htmlMenjDate"
																												value="#{pc_Ghe00401T02.propMenjDate.dateValue}"
																												disabled="#{pc_Ghe00401T02.propMenjDate.disabled}"
																												size="11" tabindex="13">
																												<f:convertDateTime pattern="yyyy/MM/dd" />
																												<hx:inputHelperDatePicker />
																												<hx:inputHelperAssist errorClass="inputText_Error"
																													promptCharacter="_" imeMode="inactive" />
																											</h:inputText>
																										</TD>
																										<TH width="150px" class="v_c">
																											<h:outputText
																												styleClass="outputText" id="lblMenjSbt"
																												value="#{pc_Ghe00401T02.propMenjSbt.labelName}"
																												style="#{pc_Ghe00401T02.propMenjSbt.labelStyle}">
																											</h:outputText>
																										</TH>
																										<TD width="400px">
																											<h:selectOneMenu
																												styleClass="selectOneMenu"
																												id="htmlMenjSbt" value="#{pc_Ghe00401T02.propMenjSbt.value}"
																												style="#{pc_Ghe00401T02.propMenjSbt.style}"
																												disabled="#{pc_Ghe00401T02.propMenjSbt.disabled}" tabindex="14">
																												<f:selectItems value="#{pc_Ghe00401T02.propMenjSbt.list}" />
																											</h:selectOneMenu>
																										</TD>
																									</TR>
																									<TR>
																										<TH nowrap class="v_d" width="150px">
																											<h:outputText
																												styleClass="outputText" id="lblMenjRiyu"
																												value="#{pc_Ghe00401T02.propMenjRiyu.labelName}"
																												style="#{pc_Ghe00401T02.propMenjRiyu.labelStyle}">
																											</h:outputText>
																										</TH>
																										<TD width="700px" colspan="3">
																											<h:inputText 
																												styleClass="inputText"
																												id="htmlMenjRiyu"
																												value="#{pc_Ghe00401T02.propMenjRiyu.stringValue}"
																												style="#{pc_Ghe00401T02.propMenjRiyu.style}"
																												disabled="#{pc_Ghe00401T02.propMenjRiyu.disabled}" 
																												size="100" maxlength="#{pc_Ghe00401T02.propMenjRiyu.maxLength}" tabindex="15">
																											</h:inputText>
																										</TD>
																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>
																					<TR>
																						<TD height="3px"></TD>
																					</TR>
																					<TR>
																						<TD>
																							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
																								<TBODY>
																									<TR>
																										<TH width="150px" class="v_e">
																											<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																												<TBODY>
																													<TR>
																														<TH class="clear_border">
																															<!-- 入力ファイル -->
																															<h:outputText styleClass="outputText"
																																id="lblinputFile"
																																style="#{pc_Ghe00401T02.propInputFile.labelStyle}"
																																value="#{pc_Ghe00401T02.propInputFile.labelName}">
																															</h:outputText>
																														</TH>
																													</TR>
																													<TR>
																														<TH class="clear_border">
																															<!-- 前回ファイル -->
																															<h:outputText styleClass="outputText"
																																id="lblInputFileOld" 
																																value="#{pc_Ghe00401T02.propInputFileOld.labelName}">
																															</h:outputText>
																														</TH>
																													</TR>
																												</TBODY>
																											</TABLE>
																										</TH>
																										<TD width="*" nowrap>
																											<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																												<TBODY>
																													<TR>
																														<TD class="clear_border">
																															<hx:fileupload styleClass="fileupload"
																																id="htmlInputFile" size="50"
																																value="#{pc_Ghe00401T02.propInputFile.value}"
																																style="#{pc_Ghe00401T02.propInputFile.style}"
																																disabled="#{pc_Ghe00401T02.propGakusekiCd.disabled}"
																																style="#{pc_Ghe00401T02.propGakusekiCd.style} width:580px"
																																size="50" tabindex="16">
																																<hx:fileProp name="fileName"
																																	value="#{pc_Ghe00401T02.propInputFile.fileName}" />
																																<hx:fileProp name="contentType"
																																	value="#{pc_Ghe00401T02.propInputFile.contentType}" />
																															</hx:fileupload>
																															<hx:commandExButton type="submit" value="取込"
																																styleClass="commandExButton" id="takein"
																																action="#{pc_Ghe00401T02.doTakeinAction}" tabindex="17">
																															</hx:commandExButton>
																														</TD>
																													</TR>
																													<TR>
																														<TD class="clear_border"><h:outputText 
																																styleClass="outputText" 
																																id="htmlInputFileOld" 
																																value="#{pc_Ghe00401T02.propInputFileOld.value}"></h:outputText></TD>
																													</TR>
																												</TBODY>
																											</TABLE>
																										</TD>
																									</TR>
																									<TR>
																										<TH width="150px" nowrap class="v_f">
																											<!-- 学籍番号 -->
																											<h:outputText styleClass="outputText"
																												id="lblGakusekiCd"
																												style="#{pc_Ghe00401T02.propGakusekiCd.labelStyle}" 
																												value="#{pc_Ghe00401T02.propGakusekiCd.labelName}">
																											</h:outputText>
																										</TH>
																										<TD nowrap width="*">
																											<h:inputText styleClass="inputText"
																												id="htmlGakusekiCd" size="10"
																												value="#{pc_Ghe00401T02.propGakusekiCd.value}"
																												maxlength="#{pc_Ghe00401T02.propGakusekiCd.maxLength}"
																												disabled="#{pc_Ghe00401T02.propGakusekiCd.disabled}"
																												style="#{pc_Ghe00401T02.propGakusekiCd.style}" 
																												onblur="return ajaxGakusekiCd(this, 'form1:htmlNameHd');" 
																												tabindex="18">
																											</h:inputText>
																											<hx:commandExButton type="submit"
																												styleClass="commandExButton_search"
																												onclick="return openPGhz0301Window();" id="popGakSearch"
																												action="#{pc_Ghe00401T02.doPopGakSearchAction}" tabindex="19">
																											</hx:commandExButton>
																											<hx:commandExButton type="submit" value="追加"
																												styleClass="commandExButton" id="addition"
																												action="#{pc_Ghe00401T02.doAdditionAction}" tabindex="20">
																											</hx:commandExButton>
																											<h:inputText
																												styleClass="likeOutput"
																												id="htmlNameHd"
																												size="61"
																												readonly="#{pc_Ghe00401T02.propName.readonly}"
																												value="#{pc_Ghe00401T02.propName.stringValue}"
																												tabindex="-1">
																											</h:inputText>
																										</TD>
																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>
																					<TR>
																						<TD height="10"></TD>
																					</TR>
																					<TR>
																						<TD>
																							<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																								<TBODY>
																									<TR>
																										<TD width="600px">
																											<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																												<TBODY>
																													<TR>
																														<TD align="left">
																															<h:outputText styleClass="outputText"
																																id="lblListName" 
																																value="#{pc_Ghe00401T02.propListName.labelName}">
																															</h:outputText>
																														</TD>
																													</TR>
																													<TR>
																														<TD>
																															<h:selectManyListbox 
																																styleClass="selectManyListbox"
																																id="htmlGakseiList" 
																																style="width:100%" size="4"
																																value="#{pc_Ghe00401T02.propGakseiList.value}">
																															<f:selectItems
																																value="#{pc_Ghe00401T02.propGakseiList.list}" />
																															</h:selectManyListbox>
																														</TD>
																													</TR>
																												</TBODY>
																											</TABLE>
																										</TD>
																										<TD width="*" align="left">
																											<BR>
																												<hx:commandExButton type="submit" value="除外"
																													styleClass="commandExButton" id="exclusion"
																													action="#{pc_Ghe00401T02.doExclusionAction}" tabindex="21">
																												</hx:commandExButton>
																											<BR>
																												<h:outputText styleClass="note" 
																													id="lblExclusionCmt"
																													value="(複数選択可)">
																												</h:outputText>
																											<BR>
																												<hx:commandExButton type="submit" value="全て除外"
																													styleClass="commandExButton" id="allExclusion"
																													action="#{pc_Ghe00401T02.doAllExclusionAction}"
																													tabindex="22">
																												</hx:commandExButton>
																											<BR>
																										</TD>
																									</TR>
																									<TR>
																										<TD width="600px" align="right">
																											<h:outputText styleClass="outputText"
																												id="lblSumCount" 
																												value="#{pc_Ghe00401T02.propSumCount.labelName}">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="htmlSumCount" 
																												value="#{pc_Ghe00401T02.propSumCount.value}">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="lblSumCountName" 
																												value="件">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="lblNormalCount" 
																												value="#{pc_Ghe00401T02.propNormalCount.labelName}">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="htmlNormalCount" 
																												value="#{pc_Ghe00401T02.propNormalCount.value}">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="lblNormalCountName" 
																												value="件">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="lblErrorCount" 
																												value="#{pc_Ghe00401T02.propErrorCount.labelName}">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="htmlErrorCount" 
																												value="#{pc_Ghe00401T02.propErrorCount.value}">
																											</h:outputText>
																											<h:outputText styleClass="outputText"
																												id="lblErrorCountName" 
																												value="件">
																											</h:outputText>
																										</TD>
																										<TD width="*"></TD>
																									</TR>
																								</TBODY>
																							</TABLE>
																						</TD>
																					</TR>
																				</TBODY>
																			</TABLE>
																		</DIV>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="20px"></TD>
								</TR>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
											<TBODY>
											
												<TR>
													<TH width="150px" nowrap class="v_a">
														<h:outputText styleClass="outputText" 
															id="lblRegKbn"
															value="#{pc_Ghe00401T02.propRegKbn.name}"
															style="#{pc_Ghe00401T02.propRegKbn.labelStyle}">
														</h:outputText>
													</TH>
													<TD width="*" nowrap>
														<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" 
															id="htmlRegKbn"
															layout="pageDirection"
															value="#{pc_Ghe00401T02.propRegKbn.stringValue}"  tabindex="23">
															<f:selectItem itemValue="1"
																itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
															<f:selectItem itemValue="4" itemLabel="指定されたデータを削除します。" />
														</h:selectOneRadio>
													</TD>
												</TR>
											
												<TR>
													<TH width="150px" class="v_g">
														<h:outputText
															styleClass="outputText" id="htmlProcessKbnLabel"
															value="#{pc_Ghe00401T02.propProcessKbnLabel.name}"
															style="#{pc_Ghe00401T02.propProcessKbnLabel.style}">
														</h:outputText>
													</TH>
													<TD width="*">
														<h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox"
															value="#{pc_Ghe00401T02.propProcessKbn.checked}"
															id="htmlProcessKbn" style="#{pc_Ghe00401T02.propProcessKbn.style}"
															disabled="#{pc_Ghe00401T02.propProcessKbn.disabled}" tabindex="24">
														</h:selectBooleanCheckbox>
														<h:outputText
															styleClass="outputText" id="lblProcessKbn"
															value="#{pc_Ghe00401T02.propProcessKbn.name}"
															style="#{pc_Ghe00401T02.propProcessKbn.labelStyle}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH width="150px" class="v_a">
														<h:outputText
															styleClass="outputText" id="htmlChkListLabel"
															value="#{pc_Ghe00401T02.propChkListLabel.name}"
															style="#{pc_Ghe00401T02.propChkListLabel.labelStyle}">
														</h:outputText>
													</TH>
													<TD width="*">
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
															<TBODY>
																<TR>
																	<TD class="clear_border">
																		<h:selectBooleanCheckbox
																			styleClass="selectBooleanCheckbox" id="htmlChkListNormal" 
																			value="#{pc_Ghe00401T02.propChkListNormal.checked}"
																			style="#{pc_Ghe00401T02.propChkListNormal.style}"
																			disabled="#{pc_Ghe00401T02.propChkListNormal.disabled}" tabindex="25">
																		</h:selectBooleanCheckbox>
																		<h:outputText
																			styleClass="outputText" id="lblChkListNormal"
																			value="#{pc_Ghe00401T02.propChkListNormal.name}"
																			style="#{pc_Ghe00401T02.propChkListNormal.labelStyle}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD class="clear_border">
																		<h:selectBooleanCheckbox
																			styleClass="selectBooleanCheckbox" id="htmlChkListError" 
																			value="#{pc_Ghe00401T02.propChkListError.checked}"
																			style="#{pc_Ghe00401T02.propChkListError.style}"
																			disabled="#{pc_Ghe00401T02.propChkListError.disabled}" tabindex="26">
																		</h:selectBooleanCheckbox>
																		<h:outputText
																			styleClass="outputText" id="lblChkListError"
																			value="#{pc_Ghe00401T02.propChkListError.name}"
																			style="#{pc_Ghe00401T02.propChkListError.labelStyle}">
																		</h:outputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton
															type="submit" value="実行"
															styleClass="commandExButton_dat" id="execute" 
															confirm="#{msg.SY_MSG_0001W}"
															action="#{pc_Ghe00401T02.doExecAction}" tabindex="27">
														</hx:commandExButton>&nbsp;
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
                    </DIV>
                </DIV>
                <!--↑CONTENT↑-->
            </DIV>
            <!--↑outer↑-->

            <!-- フッダーインクルード -->
            <jsp:include page ="../inc/footer.jsp" />
            <h:inputHidden 	
		   		value="#{pc_Ghe00401T02.propExecutableSearch.integerValue}" 
		   		id="htmlExecutableSearch">
		   	</h:inputHidden>
		   	<h:inputHidden
				value="#{pc_Ghe00401T02.propActiveControlSearch.value}"
				id="htmlActiveControlSearch">
			</h:inputHidden>
			<h:inputHidden 	
		   		value="#{pc_Ghe00401T02.propActiveControlExec.value}" 
		   		id="htmlActiveControlExec">
		   	</h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghe00401T02.propPayhList.scrollPosition}" 
				id="scrollPayh">
			</h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghe00401T02.propPayhItemList.scrollPosition}" 
				id="scrollPayhItem">
			</h:inputHidden>
			<h:inputHidden 
				value="#{pc_Ghe00401T02.propScrollPos.stringValue}" 
				id="htmlScrollPos">
			</h:inputHidden>
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
