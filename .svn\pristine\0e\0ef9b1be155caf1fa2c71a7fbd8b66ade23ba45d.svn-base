<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_COB_SOT_HSY" name="卒業生保証人" prod_id="CO" description="卒業生の保証人情報を持ちます。">
<STATMENT><![CDATA[
COB_SOT_HSY
]]></STATMENT>
<COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生を一意に識別する為の番号です。"/><COLUMN id="HSY_NO" name="保証人ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="保証人種別を識別する任意の番号です。"/><COLUMN id="HSY_SBT_NAME" name="保証人種別名称" type="string" length="14" lengthDP="0" byteLength="42" description="保証人種別の名称（呼称）が設定されます。"/><COLUMN id="HSY_NAME" name="保証人氏名" type="string" length="40" lengthDP="0" byteLength="120" description="保証人の氏名が設定されます。"/><COLUMN id="HSY_NAME_KANA" name="保証人氏名＿カナ" type="string" length="80" lengthDP="0" byteLength="240" description="保証人氏名の読み仮名です。"/><COLUMN id="ZOKUGARA_CD" name="続柄コード" type="string" length="2" lengthDP="0" byteLength="2" description="続柄を表すコードです。"/><COLUMN id="ZOKUGARA_NAME" name="続柄名称" type="string" length="20" lengthDP="0" byteLength="60" description="続柄の名称が設定されます。"/><COLUMN id="ADDR_CD" name="郵便番号" type="string" length="7" lengthDP="0" byteLength="7" description="保証人の郵便番号が設定されます。（前詰めで設定されます。また、’-’は含みません。）"/><COLUMN id="HSY_ADDR1" name="住所１" type="string" length="50" lengthDP="0" byteLength="150" description="住所１です。"/><COLUMN id="HSY_ADDR2" name="住所２" type="string" length="50" lengthDP="0" byteLength="150" description="住所２です。"/><COLUMN id="HSY_ADDR3" name="住所３" type="string" length="50" lengthDP="0" byteLength="150" description="住所３です。"/><COLUMN id="ADDR_KANA1" name="住所＿カナ１" type="string" length="100" lengthDP="0" byteLength="300" description="住所１の読み仮名が設定されます。"/><COLUMN id="ADDR_KANA2" name="住所＿カナ２" type="string" length="100" lengthDP="0" byteLength="300" description="住所２の読み仮名が設定されます。"/><COLUMN id="ADDR_KANA3" name="住所＿カナ３" type="string" length="100" lengthDP="0" byteLength="300" description="住所３の読み仮名が設定されます。"/><COLUMN id="TEL" name="電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="電話番号です。"/><COLUMN id="KEITAI_TEL" name="携帯電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="携帯電話の電話番号です。"/><COLUMN id="FAX" name="ＦＡＸ番号" type="string" length="25" lengthDP="0" byteLength="25" description="FAX番号が設定されます。"/><COLUMN id="HSY_RENRAKU" name="保証人連絡先" type="string" length="100" lengthDP="0" byteLength="300" description="連絡先が設定されます。"/><COLUMN id="HSY_RENRAKU_TEL" name="保証人連絡先電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="連絡先の電話番号が設定されます。"/>
</TABLE>
