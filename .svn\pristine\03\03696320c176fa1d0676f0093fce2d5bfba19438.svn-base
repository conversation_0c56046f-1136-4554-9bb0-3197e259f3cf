<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_NSC_KSKN_SSK" name="共通試験入試成績" prod_id="NS" description="志願者の共通試験の入試成績情報を共通受験番号単位に持ちます。
『共通試験採点一括登録』『共通試験採点登録』にて得点が設定されます。
『入試期末処理』にて今年度、期の志願者情報を過年度志願者情報に登録後、削除されます。
">
<STATMENT><![CDATA[
NSC_KSKN_SSK
]]></STATMENT>
<COLUMN id="KYOTU_JUKEN_CD" name="共通受験番号" type="string" length="10" lengthDP="0" byteLength="10" description="志願者の共通試験に対する共通受験番号です。同じ共通試験を併願している場合、複数の受験番号に対して、同じ共通受験番号が付番されます。
"/><COLUMN id="KAMOK_CD" name="科目コード" type="string" length="3" lengthDP="0" byteLength="3" description="入試科目のコードです。"/><COLUMN id="TOKTEN" name="得点" type="string" length="6" lengthDP="0" byteLength="9" description="志願者の共通試験科目毎の得点（素点）です。評価入力の科目は文字が設定されます。"/><COLUMN id="KANSAN_TEN" name="換算点" type="number" length="4" lengthDP="1" byteLength="0" description="志願者の共通試験科目毎の換算点です。業務設定にて「換算点を使用する」と設定されている場合は、成績集計にて得点ではなく換算点を使用して処理が行われます。"/><COLUMN id="KESEKI_FLG" name="欠席フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="志願者の共通試験科目毎の出欠情報です。『共通試験採点一括登録』『共通試験採点登録』にて設定されます。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN>
</TABLE>
