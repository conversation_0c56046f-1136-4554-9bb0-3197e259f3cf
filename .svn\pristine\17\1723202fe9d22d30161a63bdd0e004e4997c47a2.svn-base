<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cos00804.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/childGakuen.css" rel="stylesheet" type="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<TITLE>Cos00804.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cos00804.onPageLoadBegin}">
<h:form styleClass="form" id="form1">
		<!--↓outer↓-->
		<DIV class="outer" align="left"><!--↓head↓--><!-- ヘッダーインクルード --><jsp:include
			page="../../rev/inc/childHeader.jsp">
			<hx:panelBox styleClass="panelBox" id="boxHeader"></hx:panelBox>
		</jsp:include><!-- ヘッダーへのデータセット領域 -->
		<DIV style="display:none;"><hx:commandExButton type="submit"
			value="閉じる" styleClass="commandExButton" id="closeDisp"
			action="#{pc_Cos00804.doCloseDispAction}"></hx:commandExButton><h:outputText
			styleClass="outputText" id="htmlFuncId"
			value="#{pc_Cos00804.funcId}"></h:outputText><h:outputText
			styleClass="outputText" id="htmlLoginId"
			value="#{SYSTEM_DATA.loginID}"></h:outputText><h:outputText
			styleClass="outputText" id="htmlScrnName"
			value="#{pc_Cos00804.screenName}"></h:outputText></DIV>
		<!--↑head↑--><!--↓content↓-->
		<DIV id="content">
		<DIV class="column" align="left">
		
			<%-- グループID --%>
			<TABLE class="table" width="400">
				<TBODY>
					<TR>
						<TH nowrap width="100">
							<h:outputText
								styleClass="outputText" id="labelGrpId"
								value="#{pc_Cos00804.propGrpId.labelName}"
								style="#{pc_Cos00804.propGrpId.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:outputText styleClass="outputText" id="htmlGrpId" value="#{pc_Cos00804.propGrpId.stringValue}"></h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<hr noshade class="hr">
			
			<TABLE border="0" cellpadding="0" cellspacing="0" width="910" align="center">
				<TBODY>
					<tr>
						<td width="500" align="left" valign="top">
						
							<%-- [グループ] --%>
							<table width="500">
								<TBODY>
									<TR height="20">
										<TD width="500">
											<h:outputText styleClass="outputText" value="[グループ]"></h:outputText>
										</TD>
									</TR>
								</TBODY>
							</table>
							<TABLE class="table" width="500" height="60">
								<TBODY>
									<TR>
										<TH nowrap width="120">
											<h:outputText
												styleClass="outputText" id="labelGrpName"
												value="#{pc_Cos00804.propGrpName.labelName}">
											</h:outputText>
										</TH>
										<TD>
											<h:outputText
												styleClass="outputText" id="htmlGrpName"
												value="#{pc_Cos00804.propGrpName.stringValue}"
												title="#{pc_Cos00804.propGrpName.stringValue}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap width="120">
											<h:outputText
												styleClass="outputText" id="labelManagerUserId"
												value="#{pc_Cos00804.propManagerUserId.labelName}">
											</h:outputText>
										</TH>
										<TD>
											<h:outputText
												styleClass="outputText" id="htmlManagerUserId"
												value="#{pc_Cos00804.propManagerUserId.stringValue}"
												title="#{pc_Cos00804.propManagerUserId.stringValue}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap width="120">
											<h:outputText
												styleClass="outputText" id="labelManagerUserName"
												value="#{pc_Cos00804.propManagerUserName.labelName}">
											</h:outputText>
										</TH>
										<TD>
											<h:outputText
												styleClass="outputText" id="htmlManagerUserName"
												value="#{pc_Cos00804.propManagerUserName.stringValue}"
												title="#{pc_Cos00804.propManagerUserName.stringValue}">
											</h:outputText>
										</TD>
									</TR>
								</TBODY>
							</table>
							
							<%-- [ユーザ] --%>
							<table width="500">
								<tr height="20">
									<td align="left">
										<h:outputText styleClass="outputText" value="[ユーザ]"></h:outputText>
									</td>
									<td align="right">
										<h:outputText styleClass="outputText" value="#{pc_Cos00804.propUserList.listCount}件"></h:outputText>
									</td>
								</tr>
							</table>
							<table width="500">
								<tr height="192">
									<td width="500" valign="top">
									<div style="height: 192px;" class="listScroll" align="center">
									<h:dataTable border="0" cellpadding="2" cellspacing="0"
										columnClasses="columnClass1" headerClass="headerClass"
										styleClass="meisai_scroll" footerClass="footerClass"
										rowClasses="#{pc_Cos00804.propUserList.rowClasses}"
										id="htmlUserList" value="#{pc_Cos00804.propUserList.list}" var="varlist">

										<%-- ユーザID --%>
										<h:column id="columnU1">
											<hx:jspPanel id="jspPaneUserId">
											<div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 120px;">
												<h:outputText styleClass="outputText"
													title="#{varlist.userIdOut.stringValue}"
													value="#{varlist.userIdOut.stringValue}">
												</h:outputText>
											</div>
											</hx:jspPanel>

											<f:facet name="header">
												<h:outputText id="dth2" styleClass="outputText" value="ユーザＩＤ"></h:outputText>
											</f:facet>
											<f:attribute value="120" name="width" />
										</h:column>

										<%-- ユーザ名称 --%>
										<h:column id="columnU2">
											<hx:jspPanel id="jspPaneUserName">
											<div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 160px;">
												<h:outputText styleClass="outputText"
													title="#{varlist.userNameOut.stringValue}"
													value="#{varlist.userNameOut.stringValue}">
												</h:outputText>
											</div>
											</hx:jspPanel>

											<f:facet name="header">
												<h:outputText id="dth3" styleClass="outputText" value="ユーザ名称"></h:outputText>
											</f:facet>
											<f:attribute value="160" name="width" />
										</h:column>

										<%-- 有効期限FROM --%>
										<h:column id="columnU3">
											<h:outputText styleClass="outputText"
												value="#{varlist.avlDateFrom}">
												<f:convertDateTime />
											</h:outputText>
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="有効期限FROM"></h:outputText>
											</f:facet>
											<f:attribute value="100" name="width" />
										</h:column>

										<%-- 有効期限TO --%>
										<h:column id="columnU4">
											<h:outputText styleClass="outputText"
												value="#{varlist.avlDateTo}">
												<f:convertDateTime />
											</h:outputText>
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="有効期限TO"></h:outputText>
											</f:facet>
											<f:attribute value="100" name="width" />
										</h:column>
									</h:dataTable></div>
									</td>
								</tr>
							</table>
							
							<%-- [プリンタ] --%>
							<table width="500">
								<tr height="20">
									<td align="left">
										<h:outputText styleClass="outputText" value="[プリンタ]"></h:outputText>
									</td>
									<td align="right">
										<h:outputText styleClass="outputText" value="#{pc_Cos00804.propGrpPrtList.listCount}件"></h:outputText>
									</td>
								</tr>
							</table>
							<table>
								<tr>
									<td width="500">
									<div style="height: 75px;" align="center" class="listScroll">
									<h:dataTable border="0" cellpadding="2" cellspacing="0"
										columnClasses="columnClass1" headerClass="headerClass"
										styleClass="meisai_scroll" footerClass="footerClass"
										rowClasses="#{pc_Cos00804.propGrpPrtList.rowClasses}"
										id="htmlGrpPrtList" value="#{pc_Cos00804.propGrpPrtList.list}" var="varlist">

										<%-- プリンタID --%>
										<h:column id="columnP1">
											<hx:jspPanel id="jspPaneGrpPrtId">
											<div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 120px;">
												<h:outputText styleClass="outputText" id="htmlListGrpPrdId"
													title="#{varlist.propPrtId.stringValue}"
													value="#{varlist.propPrtId.stringValue}">
												</h:outputText>
											</div>
											</hx:jspPanel>

											<f:facet name="header">
												<h:outputText styleClass="outputText" value="プリンタＩＤ" id="textP1"></h:outputText>
											</f:facet>
											<f:attribute value="top" name="valign" />
											<f:attribute value="120" name="width" />
										</h:column>

										<%-- プリンタ名称 --%>
										<h:column id="columnP2">
											<hx:jspPanel id="jspPanePrtName">
											<div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 360px;">
												<h:outputText styleClass="outputText" id="htmlListPrtName"
													title="#{varlist.propPrtName.stringValue}"
													value="#{varlist.propPrtName.stringValue}">
												</h:outputText>
											</div>
											</hx:jspPanel>

											<f:facet name="header">
												<h:outputText styleClass="outputText" value="プリンタ名称" id="textP2"></h:outputText>
											</f:facet>
											<f:attribute value="360" name="width" />
											<f:attribute value="top" name="valign" />
										</h:column>
									</h:dataTable></div>
									</td>
								</tr>
							</table>
							
						</td>
						<td width="400" align="left" valign="top">
						
							<%-- [アプリケーション権限] --%>
							<table width="400">
								<tr height="20">
									<td align="left" nowrap>
										<h:outputText styleClass="outputText" value="[アプリケーション権限]"></h:outputText>
									</td>
									<td align="right">
										<h:outputText styleClass="outputText" value="#{pc_Cos00804.propAppAuthList.listCount}件"></h:outputText>
									</td>
								</tr>
							</table>
							<table>
								<tr height="385">
									<td width="400">
										<div style="height: 385px;" id="listScrollAppAuth" class="listScroll" align="center">
										<h:dataTable border="0"
											cellpadding="2" cellspacing="0" columnClasses="columnClass1"
											headerClass="headerClass" footerClass="footerClass"
											rowClasses="#{pc_Cos00804.propAppAuthList.rowClasses}"
											styleClass="meisai_scroll" id="htmlAppAuthList"
											value="#{pc_Cos00804.propAppAuthList.list}" var="varlist">

											<h:column id="column11">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="アプリＩＤ"></h:outputText>
												</f:facet>
											
												<h:outputText
													styleClass="outputText"
													title="#{varlist.funcId}"
													value="#{varlist.funcId}">
												</h:outputText>
												
												<f:attribute value="80" name="width" />
												<f:attribute value="left" name="align" />
												<f:attribute value="middle" name="valign" />
											</h:column>

											<h:column id="column14">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="アプリケーション名称"></h:outputText>
												</f:facet>
											
												<hx:jspPanel id="jspPaneFuncName">
												<div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 300px;">
													<h:outputText
														styleClass="outputText"
														title="#{varlist.funcName}"
														value="#{varlist.funcName}">
													</h:outputText>
												</div>
												</hx:jspPanel>
												
												<f:attribute value="300" name="width" />
												<f:attribute value="left" name="align" />
												<f:attribute value="middle" name="valign" />
											</h:column>

										</h:dataTable>
										</div>
									</td>
								</tr>
							</table>
							<table width="400">
								<tr height="20">
									<td align="left" nowrap>
										<h:outputText styleClass="note" value=" ※利用可能なアプリケーションを表示"></h:outputText>
									</td>
								</tr>
							</table>

						</td>
					</tr>
				</TBODY>
			</TABLE>
		</DIV>
		</DIV>
		<!--↑content↑--><!--↓foot↓--><!-- フッダーインクルード -->
		<jsp:include page="../../rev/inc/childFooter.jsp" />
		<!--↑foot↑-->
		</DIV>
		<!--↑outer↑-->
	</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

