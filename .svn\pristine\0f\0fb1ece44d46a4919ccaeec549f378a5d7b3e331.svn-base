<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kme00801T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<HTML>
<HEAD>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kme00801T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kme00801T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kme00801T01.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kme00801T01.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kme00801T01.screenName}"></h:outputText>
</div>
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" width="850">
				<TBODY>
				<%--EUC DEL 取得見込資格（出力する）,エラー内容 2014/06/23 k-sou 
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850" class="table">
							<TBODY>
								<TR>
									<TH bgcolor="" width="170" class="v_a"><h:outputText
										styleClass="outputText" id="lblStkMkmSikaku"
										value="#{pc_Kme00801T01.kme00801.propStkMkmSikaku.labelName}"
										style="#{pc_Kme00801T01.kme00801.propOutputTani.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlStkMkmSikaku"
										value="#{pc_Kme00801T01.kme00801.propStkMkmSikaku.checked}" style="#{pc_Kme00801T01.kme00801.propStkMkmSikaku.style}"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblStkMkmSikaku2" value="出力する"></h:outputText></TD>
								</TR>
								<TR>
									<TH bgcolor="" width="170" class="v_b"><h:outputText
										styleClass="outputText" id="lblErrorContent"
										value="#{pc_Kme00801T01.kme00801.propErrorContent.labelName}"
										style="#{pc_Kme00801T01.kme00801.propErrorContent.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlErrorContent"
										value="#{pc_Kme00801T01.kme00801.propErrorContent.stringValue}"
										style="#{pc_Kme00801T01.kme00801.propErrorContent.style}">
										<f:selectItem itemValue="0" itemLabel="エラー内容・科目を出力" />
										<f:selectItem itemValue="1" itemLabel="エラー内容のみ出力" />
										<f:selectItem itemValue="2" itemLabel="出力しない" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					EUC DEL 取得見込資格（出力する）,エラー内容 2014/06/23 k-sou  --%>
					<TR>
						<TD align="center" style="height:10px"></TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="850"
							class="table">
							<TBODY>
								<%--EUC DEL 単位集計出力方法 2014/06/23 k-sou 
								<TR>
									<TH bgcolor="" width="170" class="v_c"><h:outputText
										styleClass="outputText" id="lblOutputTani"
										value="#{pc_Kme00801T01.kme00801.propOutputTani.labelName}"
										style="#{pc_Kme00801T01.kme00801.propOutputTani.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlOutputTani"
										layout="pageDirection"
										value="#{pc_Kme00801T01.kme00801.propOutputTani.stringValue}"
										style="#{pc_Kme00801T01.kme00801.propOutputTani.style}">
										<f:selectItem itemValue="0" itemLabel="全て出力" />
										<f:selectItem itemValue="1" itemLabel="成績用科目分類の単位表示フラグにより出力" />
									</h:selectOneRadio></TD>
								</TR>
								EUC DEL 単位集計出力方法 2014/06/23 k-sou  --%>
								<TR>
									<TH bgcolor="" width="170" class="v_d"><h:outputText
										styleClass="outputText" id="lblPdfTitle"
										value="#{pc_Kme00801T01.kme00801.propPdfTitle.labelName}"
										style="#{pc_Kme00801T01.kme00801.propPdfTitle.labelStyle}"></h:outputText></TH>
									<TD width="480"><h:inputText styleClass="inputText"
										id="htmlPdfTitle"
										value="#{pc_Kme00801T01.kme00801.propPdfTitle.stringValue}"
										style="#{pc_Kme00801T01.kme00801.propPdfTitle.style}"
										size="70"
										maxlength="#{pc_Kme00801T01.kme00801.propPdfTitle.maxLength}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center" style="height:10px"></TD>
					</TR>
					<TR>
						<TD width="850" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="140">
							<TBODY>
								<TR>
									<TD width="11" style="" class=""><hx:commandExButton
										type="button" value="一括指定" styleClass="tab_head_on"
										id="ikkatsuSitei"></hx:commandExButton></TD>
									<TD width="9" class=""><hx:commandExButton type="submit"
										value="学生指定" styleClass="tab_head_off" id="GakuseiSitei"
										action="#{pc_Kme00801T01.doGakuseiSiteiAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="850" align="left">
							<TABLE border="0" cellpadding="0" cellspacing="0"
								class="tab_body" width="100%" height="360">
								<TBODY>
									<TR>
										<TD align="center">
										<TABLE border="0" cellpadding="0" cellspacing="0"
											width="800" class="table">
											<TBODY>
												<TR>
													<TH bgcolor="" width="170" class="v_a"><h:outputText
													styleClass="outputText" id="lblYoteiHi"
													value="#{pc_Kme00801T01.propYoteiHi1.labelName}"
													style="#{pc_Kme00801T01.propYoteiHi1.labelStyle}"></h:outputText></TH>
													<TD width="480"><h:inputText styleClass="inputText"
														id="htmlYoteiHi1"
														value="#{pc_Kme00801T01.propYoteiHi1.dateValue}">
														<f:convertDateTime pattern="yyyy/MM" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText> 
												<%--EUC DEL 卒業予定年月 2014/08/11 k-sou 
												<h:outputText styleClass="outputText"
														id="text6" value="～"></h:outputText> <h:inputText
														styleClass="inputText" id="htmlYoteiHi2"
														value="#{pc_Kme00801T01.propYoteiHi2.dateValue}">
														<f:convertDateTime pattern="yyyy/MM" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />

												</h:inputText>
												EUC DEL卒業予定年月 2014/08/11 k-sou --%>
												</TD>
												</TR>
												<TR>
													<TH bgcolor="" width="170" class="v_b"><h:outputText
													styleClass="outputText" id="lblHanteiHi"
													value="#{pc_Kme00801T01.propHanteiHi1.labelName}"
													style="#{pc_Kme00801T01.propHanteiHi1.labelStyle}"></h:outputText></TH>
													<TD width="480"><h:inputText styleClass="inputText"
														id="htmlHanteiHi1"
														value="#{pc_Kme00801T01.propHanteiHi1.dateValue}">
														<f:convertDateTime />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<hx:inputHelperDatePicker />
												</h:inputText> <h:outputText styleClass="outputText"
														id="text7" value="～"></h:outputText> <h:inputText
														styleClass="inputText" id="htmlHanteiHi2"
														value="#{pc_Kme00801T01.propHanteiHi2.dateValue}">
														<f:convertDateTime />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												</TR>
												<%--EUC ADD カリキュラム学科組織 2014/06/23 k-sou--%>
												<TR>
													<TH bgcolor="" width="170" class="v_d"><h:outputText
														styleClass="outputText" id="lblCurGakkaCd"
														value="#{pc_Kme00801T01.propCurGakkaCd.labelName}"
														style="#{pc_Kme00801T01.propCurGakkaCd.labelStyle}"></h:outputText></TH>
													<TD width="480"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlCurGakkaCd"
													value="#{pc_Kme00801T01.propCurGakkaCd.stringValue}"
													style="#{pc_Kme00801T01.propCurGakkaCd.style};width:470px">
													<f:selectItems
														value="#{pc_Kme00801T01.propCurGakkaCd.list}" />
												</h:selectOneMenu></TD>
												</TR>
												<%--EUC ADD カリキュラム学科組織 2014/06/23 k-sou  --%>
											</TBODY>
										</TABLE>
										<TABLE border="0" cellpadding="0" cellspacing="0">
											<TR>
												<TD align="center" style="height:15px"></TD>
											</TR>
										</TABLE>
										<%--EUC DEL 管理部署,カリキュラム学科組織 2014/06/23 k-sou 
										<TABLE border="0" cellpadding="0" cellspacing="0"
											width="800" class="table">
											<TBODY>
												<TR>
													<TH bgcolor="" width="170" class="v_c"><h:outputText
														styleClass="outputText" id="lblKanriBsyoCd"
														value="#{pc_Kme00801T01.propKanriBsyoCd.labelName}"
														style="#{pc_Kme00801T01.propKanriBsyoCd.labelStyle}"></h:outputText></TH>
													<TD width="480"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlKanriBsyoCd"
													value="#{pc_Kme00801T01.propKanriBsyoCd.stringValue}"
													style="#{pc_Kme00801T01.propKanriBsyoCd.style};width:410px">
													<f:selectItems
														value="#{pc_Kme00801T01.propKanriBsyoCd.list}" />
												</h:selectOneMenu></TD>
												</TR>
												<TR>
													<TH bgcolor="" width="170" class="v_d"><h:outputText
														styleClass="outputText" id="lblCurGakkaCd"
														value="#{pc_Kme00801T01.propCurGakkaCd.labelName}"
														style="#{pc_Kme00801T01.propCurGakkaCd.labelStyle}"></h:outputText></TH>
													<TD width="480"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlCurGakkaCd"
													value="#{pc_Kme00801T01.propCurGakkaCd.stringValue}"
													style="#{pc_Kme00801T01.propCurGakkaCd.style};width:470px">
													<f:selectItems
														value="#{pc_Kme00801T01.propCurGakkaCd.list}" />
												</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>
										EUC DEL 管理部署,カリキュラム学科組織 2014/06/23 k-sou --%>
										<TABLE border="0" cellpadding="0" cellspacing="0">
											<TR>
												<TD align="center" style="height:15px"></TD>
											</TR>
										</TABLE>

										<TABLE border="0" cellpadding="0" cellspacing="0"
											width="800" class="table">
											<TBODY>
												<TR>
													<TH bgcolor="" width="170" class="v_e"><h:outputText
														styleClass="outputText" id="lblSinkyuMkmKbn"
														value="#{pc_Kme00801T01.propSinkyuMkmKbn.labelName}"
														style="#{pc_Kme00801T01.propSinkyuMkmKbn.labelStyle}"></h:outputText></TH>
													<TD width="480"><h:selectManyCheckbox
														disabledClass="selectManyCheckbox_Disabled"
														styleClass="selectManyCheckbox" id="htmlSinkyuMkmKbn"
														layout="pageDirection"
														value="#{pc_Kme00801T01.propSinkyuMkmKbn.stringValue}"
														style="#{pc_Kme00801T01.propSinkyuMkmKbn.style}">
														<f:selectItems
															value="#{pc_Kme00801T01.propSinkyuMkmKbn.list}" />
													</h:selectManyCheckbox></TD>
												</TR>
												<%--EUC ADD 休学中 2014/06/23 k-sou  --%>
												<TR>
												<TH class="v_g"><h:outputText
													styleClass="outputText" id="lblKyuuGakuSya"
													value="#{pc_Kme00801T01.propKyuuGakuSya.labelName}"
													style="#{pc_Kme00801T01.propKyuuGakuSya.labelStyle}"></h:outputText></TH>
												<TD><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlKyuuGakuSya"
													value="#{pc_Kme00801T01.propKyuuGakuSya.checked}"
													style="#{pc_Kme00801T01.propKyuuGakuSya.style}"></h:selectBooleanCheckbox><h:outputText
													styleClass="outputText" id="lblKyuuGakuSya1" value="含む"></h:outputText></TD>
												</TR>
												<%--EUC ADD 休学中 2014/06/23 k-sou  --%>
											</TBODY>
										</TABLE>
										<TABLE border="0" cellpadding="0" cellspacing="0">
											<TR>
												<TD align="center" style="height:15px"></TD>
											</TR>
										</TABLE>
										<%--EUC DEL 出力順 2014/06/23 k-sou 
										<TABLE border="0" cellpadding="0" cellspacing="0"
											width="800" class="table">
											<TBODY>
												<TR>
													<TH bgcolor="" width="170" class="v_f"><h:outputText
														styleClass="outputText" id="lblSort"
														value="#{pc_Kme00801T01.propSort.labelName}"
														style="#{pc_Kme00801T01.propSort.labelStyle}"></h:outputText></TH>
													<TD width="480"><h:selectOneRadio
														disabledClass="selectOneRadio_Disabled"
														styleClass="selectOneRadio" id="htmlSort"
														layout="pageDirection"
														value="#{pc_Kme00801T01.propSort.stringValue}"
														style="#{pc_Kme00801T01.propSort.style}">
														<f:selectItem itemValue="0" itemLabel="みなし入学年度・学期昇順" />
														<f:selectItem itemValue="1" itemLabel="みなし入学年度・学期降順" />
													</h:selectOneRadio></TD>
												</TR>
											</TBODY>
										</TABLE>
										EUC DEL 出力順 2014/06/23 k-sou --%>
										<TABLE border="0" cellpadding="0" cellspacing="0">
											<TR>
												<TD align="center" style="height:15px"></TD>
											</TR>
										</TABLE>
										<TABLE width="100%" border="0" cellpadding="0"
											cellspacing="0" class="button_bar">
											<TBODY>
												<TR class="">
													<TD width="800">
													
														<hx:commandExButton type="submit"
														value="PDF作成" styleClass="commandExButton_out"
														id="pdfout" action="#{pc_Kme00801T01.doPdfoutAction}"
														confirm="#{msg.SY_MSG_0019W}">
														</hx:commandExButton>
														
														<hx:commandExButton type="submit"
														value="EXCEL作成" styleClass="commandExButton_out"
														id="excelout" action="#{pc_Kme00801T01.doExceloutAction}"
														confirm="#{msg.SY_MSG_0027W}">
														</hx:commandExButton>
														
														<hx:commandExButton
														type="submit" value="CSV作成"
														styleClass="commandExButton_out" id="csvout"
														action="#{pc_Kme00801T01.doCsvoutAction}"
														confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton
														type="submit" value="出力項目指定"
														styleClass="commandExButton_out" id="setoutput"
														action="#{pc_Kme00801T01.doSetoutputAction}"></hx:commandExButton></TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

