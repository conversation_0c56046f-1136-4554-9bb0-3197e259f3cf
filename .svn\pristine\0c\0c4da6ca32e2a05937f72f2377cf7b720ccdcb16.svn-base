<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Col00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Coz00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Col00101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Col00101.doCloseDispAction}" tabindex="8">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Col00101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Col00101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻るボタンを配置 -->
　
<!-- ↑ここに戻るボタンを配置 -->
</DIV>



<DIV id="content">			
<DIV class="column" align="center">

<!-- ↓ここにコンポーネントを配置 -->


			<TABLE width="682" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<DIV align="right">
							<h:outputText styleClass="outputText" id="sum" value="#{pc_Col00101.propMibList.listCount}"></h:outputText>
							<h:outputText styleClass="outputText" id="lblKen" value="件"></h:outputText>
						</DIV>
						</TD>
					</TR>
					<TR>
						<TD>
						<DIV class="listScroll" id="listScroll" style="height: 128px"
							onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							columnClasses="columnClass1" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Col00101.propMibList.rowClasses}"
							styleClass="meisai_scroll" id="htmlMibList"
							value="#{pc_Col00101.propMibList.list}" var="varlist">

							<h:column id="colum1">
								<f:facet name="header">
									<h:outputText id="textCode" styleClass="outputText"
										value="身分識別コード"></h:outputText>
								</f:facet>
								<f:attribute value="100" name="width" />
								<h:outputText styleClass="outputText" id="lblMibList_code"
									value="#{varlist.mibCd}">
								</h:outputText>
							</h:column>

							<h:column id="colum2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="身分区分"
										id="textName"></h:outputText>
								</f:facet>
								<f:attribute value="100" name="width" />
								<h:outputText styleClass="outputText" id="lblMibList_kbn"
									value="#{varlist.mibKbnNm}">
								</h:outputText>
							</h:column>

							<h:column id="colum3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="身分名称"
										id="nameTest"></h:outputText>
								</f:facet>
								<f:attribute value="450" name="width" />
								<h:outputText styleClass="outputText" id="lblMibList_name"
									value="#{varlist.mibNm}">
								</h:outputText>
							</h:column>

							<h:column id="colum4">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="32" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton"
									action="#{pc_Col00101.doSelectAction}" tabindex="1">
								</hx:commandExButton>
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<BR>
			
			<TABLE class="table" border="0" width="682">
				<TR>
					<TH nowrap class="v_a" width="150">
						<h:outputText 
							styleClass="outputText" 
							id="labelMibCd" 
							value="#{pc_Col00101.propMibCd.labelName}" 
							style="#{pc_Col00101.propMibCd.labelStyle}">
						</h:outputText>
					</TH>
					<TD nowrap width="532">
						<h:inputText styleClass="inputText" id="htmlMibCd"
						style="#{pc_Col00101.propMibCd.style}"
						value="#{pc_Col00101.propMibCd.stringValue}"
						maxlength="#{pc_Col00101.propMibCd.maxLength}"
						disabled="#{pc_Col00101.propMibCd.disabled}" size="2" tabindex="2">
						<hx:inputHelperAssist errorClass="inputText_Error"/>
					</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH nowrap class="v_b" width="150">
						<h:outputText 
							styleClass="outputText" 
							id="labelMibNm" 
							value="#{pc_Col00101.propMibKbn.labelName}" 
							style="#{pc_Col00101.propMibKbn.labelStyle}">
						</h:outputText>
					</TH>
					<TD nowrap width="532">
					<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
						styleClass="selectOneRadio" id="htmlMibKbn" layout="lineDirection"
						value="#{pc_Col00101.propMibKbn.stringValue}" tabindex="3">
						<f:selectItem itemValue="1" itemLabel="学生" />
						<f:selectItem itemValue="2" itemLabel="教職員" />
					</h:selectOneRadio></TD>
				</TR>
				<TR>
					<TH nowrap class="v_c" width="150">
						<h:outputText 
							styleClass="outputText" 
							id="labelMibNmEn" 
							value="#{pc_Col00101.propMibNm.labelName}"
							style="#{pc_Col00101.propMibNm.labelStyle}">
						</h:outputText>
					</TH>

					<TD nowrap width="532">
						<h:inputText styleClass="inputText" id="htmlMibNmEn"
						style="#{pc_Col00101.propMibNm.style}"
						value="#{pc_Col00101.propMibNm.stringValue}"
						maxlength="#{pc_Col00101.propMibNm.maxLength}"
						disabled="#{pc_Col00101.propMibNm.disabled}" size="40" tabindex="4">
					</h:inputText>
					</TD>
				</TR>
			</TABLE>
			<TABLE class="button_bar" width="682" align="center" cellspacing="1" cellpadding="1">
				<TR>
					<TD align="center">
						<hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Col00101.doRegisterAction}"
							tabindex="5" confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
							<hx:commandExButton	type="submit"
							value="削除" styleClass="commandExButton_dat" id="delete"
							action="#{pc_Col00101.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}" tabindex="6"></hx:commandExButton>
							<hx:commandExButton	type="submit"
							value="クリア" styleClass="commandExButton_etc" id="clear"
							action="#{pc_Col00101.doClearAction}" tabindex="7"></hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<h:inputHidden id="htmlHidScroll" value="#{pc_Col00101.propMibList.scrollPosition}"></h:inputHidden>

			<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>

</HTML>

