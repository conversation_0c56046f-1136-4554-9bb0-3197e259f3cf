<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_COG_KENGN_PATAN" name="権限パターン" prod_id="CO" description="プログラム毎に権限パターンを管理します。">
<STATMENT><![CDATA[
COG_KENGN_PATAN
]]></STATMENT>
<COLUMN id="FUNC_ID" name="機能ＩＤ" type="string" length="10" lengthDP="0" byteLength="10" description="アプリケーション機能を識別するＩＤが設定されます。"/><COLUMN id="CTRL_PRD_ID" name="主管プロダクトＩＤ" type="string" length="2" lengthDP="0" byteLength="2" description="主管プロダクトを識別するＩＤです。"><CODE><CASE value="GH" display="学費"/><CASE value="JI" display="人事"/><CASE value="KA" display="管財"/><CASE value="KE" display="経理"/></CODE></COLUMN><COLUMN id="SHOKI_NENDO_KBN" name="初期表示年度区分" type="string" length="1" lengthDP="0" byteLength="1" description="画面の初期表示年度の区分が設定されます。"><CODE><CASE value="1" display="経理予算年度"/><CASE value="2" display="経理執行年度"/><CASE value="3" display="経理決算年度"/><CASE value="4" display="管財業務年度"/><CASE value="5" display="管財調達年度"/></CODE></COLUMN><COLUMN id="YSN_T_KENGEN_KBN" name="予算単位権限区分" type="string" length="1" lengthDP="0" byteLength="1" description="予算単位の権限チェックパターンが設定されます。"><CODE><CASE value="0" display="規制なし(全て許可)"/><CASE value="1" display="権限1"/><CASE value="2" display="権限2"/><CASE value="3" display="権限3"/><CASE value="9" display="権限なし(全てエラー)"/></CODE></COLUMN><COLUMN id="KAIKEI_T_KENGEN_KBN" name="会計単位権限区分" type="string" length="1" lengthDP="0" byteLength="1" description="会計単位の権限チェックパターンが設定されます。"><CODE><CASE value="0" display="規制なし(全て許可)"/><CASE value="1" display="権限1"/><CASE value="2" display="権限2"/><CASE value="3" display="権限3"/><CASE value="9" display="権限なし(全てエラー)"/></CODE></COLUMN><COLUMN id="GKN_SUM_KENGEN_KBN" name="学内集計権限区分" type="string" length="1" lengthDP="0" byteLength="1" description="学内集計チェックパターンの権限が設定されます。"><CODE><CASE value="0" display="規制なし(全て許可)"/><CASE value="1" display="権限1"/><CASE value="2" display="権限2"/><CASE value="3" display="権限3"/><CASE value="9" display="権限なし(全てエラー)"/></CODE></COLUMN><COLUMN id="SONOTA_KENGEN_KBN" name="その他権限区分" type="string" length="1" lengthDP="0" byteLength="1" description="その他の権限が設定されます。"><CODE><CASE value="0" display="規制なし(全て許可)"/><CASE value="1" display="権限1"/><CASE value="2" display="権限2"/><CASE value="3" display="権限3"/><CASE value="9" display="権限なし(全てエラー)"/></CODE></COLUMN>
</TABLE>
