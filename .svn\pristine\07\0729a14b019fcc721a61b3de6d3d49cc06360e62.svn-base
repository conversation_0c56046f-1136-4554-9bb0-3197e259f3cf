<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaa00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" 
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>調達物検索</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" 
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>

<style type="text/css">
<!--
 -->
</style>

<SCRIPT type="text/javascript">
		
	function func_SinseiIdAjax(thisObj, thisEvent) {
		// 申請者名称を取得する
		var servlet = 'rev/ka/SinseiNameAJAX';
		var target  = "form1:htmlSinseiName";
		var args    = document.getElementById("form1:htmlSinseiId").value;
		getCodeName(servlet, target, args);
	}
	
	function func_SinseiBumonCdAjax(thisObj, thisEvent) {
		// 申請部門名称を取得する
		var servlet = "rev/co/CogYosanTaniAJAX";
		var target = "form1:htmlSinseiBumonName";
		var args = new Array();
		args['code1'] = document.getElementById('form1:htmlChotNendo').value;
		args['code2'] = document.getElementById('form1:htmlSinseiBumonCd').value;
		args['code3'] = "";
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
	
	function func_YsnTCdAjax(thisObj, thisEvent) {
		// 起票予算単位名称を取得する
		var servlet = "rev/co/CogYosanTaniAJAX";
		var target = "form1:htmlYsnTName";
		var args = new Array();
		args['code1'] = document.getElementById('form1:htmlChotNendo').value;
		args['code2'] = document.getElementById('form1:htmlYsnTCd').value;
		args['code3'] = "";
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
	
	
	function confirmOk() {						
		document.getElementById('form1:htmlExecutableSearch').value = "1";				
		indirectClick('search');				
	}

	function confirmCancel() {						
		document.getElementById('form1:htmlExecutableSearch').value = "0";
		document.getElementById('form1:htmlChotNendo').focus();				
	}

	function lostFocus_ChotNendo() {
		func_SinseiBumonCdAjax(document.getElementById('form1:htmlSinseiBumonCd'), '');
		func_YsnTCdAjax(document.getElementById('form1:htmlYsnTCd'), '');
	}
	
	function loadFunc() {
		func_SinseiIdAjax(document.getElementById('form1:htmlSinseiId'), '');
		func_SinseiBumonCdAjax(document.getElementById('form1:htmlSinseiBumonCd'), '');
		func_YsnTCdAjax(document.getElementById('form1:htmlYsnTCd'), '');
	}

	window.attachEvent("onload", loadFunc);
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kaa00401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kaa00401.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kaa00401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kaa00401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<hx:commandExButton type="submit" value="新規登録"
				styleClass="commandExButton" id="newRegister" 
				action="#{pc_Kaa00401.doNewRegisterAction}"
				disabled="#{pc_Kaa00401.propNewRegister.disabled}" ></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>

<!--↓content↓-->
<DIV id="content">			

<!-- ↓ここにコンポーネントを配置 -->
<DIV class="column" align="center">

	<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="900">
		<TBODY>
		    <TR>
				<TH class="v_a" width="180"><h:outputText styleClass="outputText"
					id="lblChotNendo"
					value="#{pc_Kaa00401.propChotNendo.labelName}"
					style="#{pc_Kaa00401.propChotNendo.labelStyle}"></h:outputText></TH>
				<TD><h:inputText styleClass="inputText"
						id="htmlChotNendo" size="4"
						value="#{pc_Kaa00401.propChotNendo.dateValue}"
						style="#{pc_Kaa00401.propChotNendo.style}" 
						onblur="return lostFocus_ChotNendo();">
						<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" />
						<f:convertDateTime pattern="yyyy" />
					</h:inputText>
				</TD>
		    </TR>
		    <TR>
				<TH class="v_b" width="180"><h:outputText styleClass="outputText"
					id="lblChotNo"
					value="#{pc_Kaa00401.propChotNo.labelName}"
					style="#{pc_Kaa00401.propChotNo.labelStyle}"></h:outputText></TH>
				<TD><h:inputText styleClass="inputText" id="htmlChotNo" size="20"
							maxlength="#{pc_Kaa00401.propChotNo.maxLength}"
							value="#{pc_Kaa00401.propChotNo.stringValue}"
							style="#{pc_Kaa00401.propChotNo.style}" >
						</h:inputText>
					<h:outputText styleClass="outputText" id="lblChotNoFindType" value="(前方一致)"></h:outputText>
				</TD>
		    </TR>
		    <TR>
				<TH class="v_c" width="180"><h:outputText styleClass="outputText"
					id="lblRingiNo"
					value="#{pc_Kaa00401.propRingiNo.labelName}"
					style="#{pc_Kaa00401.propRingiNo.labelStyle}"></h:outputText></TH>
				<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0">
						<TR class="clear_border">
						<TD>
							<h:inputText styleClass="inputText" id="htmlRingiNo" size="40"
									value="#{pc_Kaa00401.propRingiNo.stringValue}"
									style="#{pc_Kaa00401.propRingiNo.style}"
									maxlength="#{pc_Kaa00401.propRingiNo.maxLength}" >
								</h:inputText>
						</TD>
						<TD>
							<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlRingiNoFindType"
									layout="lineDirection"
									value="#{pc_Kaa00401.propRingiNoFindType.stringValue}">
									<f:selectItems
										value="#{pc_Kaa00401.propRingiNoFindType.list}" />
								</h:selectOneRadio>
						</TD>
						</TR>
						</TABLE>
				</TD>
		    </TR>
			<TR>
				<TH class="v_d" width="180"><h:outputText styleClass="outputText"
					id="lblSinseiDate"
					value="#{pc_Kaa00401.propSinseiDate.labelName}"
					style="#{pc_Kaa00401.propSinseiDate.labelStyle}"></h:outputText></TH>
				<TD>
					<h:inputText styleClass="inputText" id="htmlSinseiDateFrom"
						size="10" value="#{pc_Kaa00401.propSinseiDateFrom.dateValue}"
						style="#{pc_Kaa00401.propSinseiDateFrom.style}"
						onblur="return func_SinseiBumonCdAjax(this, event);"	>
						<f:convertDateTime pattern="yyyy/MM/dd" />
						<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText>
					<h:outputText styleClass="outputText" id="text1" value="～"></h:outputText>
					<h:inputText styleClass="inputText" id="htmlSinseiDateTo" size="10"
						value="#{pc_Kaa00401.propSinseiDateTo.dateValue}"
						style="#{pc_Kaa00401.propSinseiDateTo.style}">
					<f:convertDateTime pattern="yyyy/MM/dd" />
					<hx:inputHelperAssist errorClass="inputText_Error"
						promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText></TD>
			</TR>
			<TR>
				<TH class="v_e" width="180"><h:outputText styleClass="outputText"
					id="lblSinseiId"
					value="#{pc_Kaa00401.propSinseiId.labelName}"
					style="#{pc_Kaa00401.propSinseiId.labelStyle}"></h:outputText></TH>
				<TD nowrap>
					<DIV style="width:720px;white-space:nowrap;overflow:hidden;display:block;">
						<h:inputText styleClass="inputText" id="htmlSinseiId"
						size="30" value="#{pc_Kaa00401.propSinseiId.stringValue}"
						style="#{pc_Kaa00401.propSinseiId.style}"
						onblur="return func_SinseiIdAjax(this, event);"
						maxlength="#{pc_Kaa00401.propSinseiId.maxLength}"></h:inputText><hx:commandExButton
						type="submit" styleClass="commandExButton_search"
						id="searchSinseiId"
						action="#{pc_Kaa00401.doSearchSinseiIdAction}">
					</hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlSinseiName">
					</h:outputText>
					</DIV>
				</TD>		
		    </TR>
		    <TR>
				<TH class="v_f" width="180"><h:outputText styleClass="outputText"
					id="lblSinseiBumonCd"
					value="#{pc_Kaa00401.propSinseiBumonCd.labelName}"
					style="#{pc_Kaa00401.propSinseiBumonCd.labelStyle}"></h:outputText></TH>
				<TD nowrap>
					<DIV style="width:720px;white-space:nowrap;overflow:hidden;display:block;">
						<h:inputText styleClass="inputText" id="htmlSinseiBumonCd"
						size="10" value="#{pc_Kaa00401.propSinseiBumonCd.stringValue}"
						style="#{pc_Kaa00401.propSinseiBumonCd.style}"
						onblur="return func_SinseiBumonCdAjax(this, event);"
						maxlength="#{pc_Kaa00401.propSinseiBumonCd.maxLength}"></h:inputText>
						<h:outputText styleClass="outputText" id="lblSinseiBumonFindType" value="(前方一致)"></h:outputText><hx:commandExButton
						type="submit" styleClass="commandExButton_search"
						id="searchSinseiBumonCd"
						action="#{pc_Kaa00401.doSearchSinseiBumonCdAction}">
					</hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlSinseiBumonName">
					</h:outputText>
					</DIV>
				</TD>		
		    </TR>
		    <TR>
				<TH class="v_g" width="180"><h:outputText styleClass="outputText"
					id="lblYsnTCd"
					value="#{pc_Kaa00401.propYsnTCd.labelName}"
					style="#{pc_Kaa00401.propYsnTCd.labelStyle}"></h:outputText></TH>
				<TD nowrap>
					<DIV style="width:720px;white-space:nowrap;overflow:hidden;display:block;">
						<h:inputText styleClass="inputText" id="htmlYsnTCd"
						size="10" value="#{pc_Kaa00401.propYsnTCd.stringValue}"
						style="#{pc_Kaa00401.propYsnTCd.style}"
						onblur="return func_YsnTCdAjax(this, event);"
						maxlength="#{pc_Kaa00401.propYsnTCd.maxLength}"></h:inputText>
						<h:outputText styleClass="outputText" id="lblYsnTCdFindType" value="(前方一致)"></h:outputText><hx:commandExButton
						type="submit" styleClass="commandExButton_search"
						id="searchYsnTCd"
						action="#{pc_Kaa00401.doSearchYsnTCdAction}">
					</hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlYsnTName">
					</h:outputText>
					</DIV>
				</TD>		
		    </TR>
		    <TR>
				<TH class="v_a" width="180"><h:outputText styleClass="outputText"
					id="lblChotName"
					value="#{pc_Kaa00401.propChotName.labelName}"
					style="#{pc_Kaa00401.propChotName.labelStyle}"></h:outputText></TH>
				<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0">
						<TR class="clear_border">
						<TD>
							<h:inputText styleClass="inputText" id="htmlChotName" size="90"
									value="#{pc_Kaa00401.propChotName.stringValue}"
									style="#{pc_Kaa00401.propChotName.style}"
									maxlength="#{pc_Kaa00401.propChotName.maxLength}" >
								</h:inputText>
						</TD>
						<TD>
							<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlChotNameFindType"
									layout="lineDirection"
									value="#{pc_Kaa00401.propChotNameFindType.stringValue}">
									<f:selectItems
										value="#{pc_Kaa00401.propChotNameFindType.list}" />
								</h:selectOneRadio>
						</TD>
						</TR>
						</TABLE>
				</TD>
		    </TR>
		    <TR>
				<TH class="v_b" width="180"><h:outputText styleClass="outputText"
					id="lblNonyuDate"
					value="#{pc_Kaa00401.propNonyuDate.labelName}"
					style="#{pc_Kaa00401.propNonyuDate.labelStyle}"></h:outputText></TH>
				<TD>
					<h:inputText styleClass="inputText" id="htmlNonyuDateFrom"
						size="10" value="#{pc_Kaa00401.propNonyuDateFrom.dateValue}"
						style="#{pc_Kaa00401.propNonyuDateFrom.style}"	>
						<f:convertDateTime pattern="yyyy/MM/dd" />
						<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText>
					<h:outputText styleClass="outputText" id="text0" value="～"></h:outputText>
					<h:inputText styleClass="inputText" id="htmlNonyuDateTo" size="10"
						value="#{pc_Kaa00401.propNonyuDateTo.dateValue}"
						style="#{pc_Kaa00401.propNonyuDateTo.style}">
					<f:convertDateTime pattern="yyyy/MM/dd" />
					<hx:inputHelperAssist errorClass="inputText_Error"
						promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText></TD>
			</TR>
			<TR>
				<TH class="v_c" width="180"><h:outputText styleClass="outputText"
					id="lblKensyuDate"
					value="#{pc_Kaa00401.propKensyuDate.labelName}"
					style="#{pc_Kaa00401.propKensyuDate.labelStyle}"></h:outputText></TH>
				<TD>
					<h:inputText styleClass="inputText" id="htmlKensyuDateFrom"
						size="10" value="#{pc_Kaa00401.propKensyuDateFrom.dateValue}"
						style="#{pc_Kaa00401.propKensyuDateFrom.style}"	>
						<f:convertDateTime pattern="yyyy/MM/dd" />
						<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText>
					<h:outputText styleClass="outputText" id="text2" value="～"></h:outputText>
					<h:inputText styleClass="inputText" id="htmlKensyuDateTo" size="10"
						value="#{pc_Kaa00401.propKensyuDateTo.dateValue}"
						style="#{pc_Kaa00401.propKensyuDateTo.style}">
					<f:convertDateTime pattern="yyyy/MM/dd" />
					<hx:inputHelperAssist errorClass="inputText_Error"
						promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText></TD>
			</TR>
		    <TR>
				<TH class="v_d" width="180"><h:outputText styleClass="outputText"
					id="lblChotJotaiKbn"
					value="#{pc_Kaa00401.propChotJotaiKbn.labelName}"
					style="#{pc_Kaa00401.propChotJotaiKbn.labelStyle}"></h:outputText></TH>
				<TD><h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled"
						styleClass="selectManyCheckbox" id="htmlChotJotaiKbn"
						style="#{pc_Kaa00401.propChotJotaiKbn.style}"
						value="#{pc_Kaa00401.propChotJotaiKbn.stringValue}" >
						<f:selectItems value="#{pc_Kaa00401.propChotJotaiKbn.list}" />
					</h:selectManyCheckbox>
				</TD>
		    </TR>
		</TBODY>
	</TABLE>

	<BR>
	<TABLE width="900" class="button_bar">
	<TR>
		<TD align="center">
			<hx:commandExButton type="submit" value="検索"
					styleClass="commandExButton_etc" id="search"  action="#{pc_Kaa00401.doSearchAction}">
			</hx:commandExButton>
			<hx:commandExButton type="submit" value="クリア"
					styleClass="commandExButton_etc" id="clear"  action="#{pc_Kaa00401.doClearAction}">
				</hx:commandExButton>
		</TD>
	</TR>
	</TABLE>	
	
</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->

<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />			
			<h:inputHidden
				value="#{pc_Kaa00401.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden 
			   value="#{pc_Kaa00401.propHiddenSystemDate.dateValue}"
			   id="htmlHiddenSystemDate">
				<f:convertDateTime />
			</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

