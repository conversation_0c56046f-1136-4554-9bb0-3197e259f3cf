<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cog00402.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.jast.gakuen.system.co.constant.code.SetteiTaniIdKbn"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cog00402.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
//----------↑経理検索部品共通js↓----------
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}
//----------↑経理検索部品共通js↑----------
// ユーザー検索
function openUserSearch() {
	var conPath ="${pageContext.request.contextPath}";
	var jspPath = "/faces/rev/co/pCos0101.jsp";
	var urlParam = "?selectId=form1:htmlId&execItem=form1:htmlId&execEvent=blur";

	var url = conPath + jspPath + urlParam;
	openModalWindow(url, "PCos0101", "<%=com.jast.gakuen.rev.co.PCos0101.getWindowOpenOption() %>");
}
// グループ検索
function openGrpSearch() {
	var conPath ="${pageContext.request.contextPath}";
	var jspPath = "/faces/rev/co/pCos0201.jsp";
	var urlParam = "?selectId=form1:htmlId&execItem=form1:htmlId&execEvent=blur";

	var url = conPath + jspPath + urlParam;
	openModalWindow(url, "PCos0201", "<%=com.jast.gakuen.rev.co.PCos0201.getWindowOpenOption() %>");
}
function func_3(thisObj, thisEvent) {

	//予算単位検索画面を表示する
	openModalWindow("", "PCog0101", "<%=com.jast.gakuen.rev.co.PCog0101.getWindowOpenOption()%>");
	setTarget("PCog0101");
	return true;

}
function func_4(thisObj, thisEvent) {

	//予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlYsnTName"; 
	var args = new Array();

	//画面．開始日入力ありの時
	if (document.getElementById("form1:htmlStartDate").value != "") {
		//開始日の年
		args['code1'] = document.getElementById("form1:htmlStartDate").value.substring(0, 4);
		var month = document.getElementById("form1:htmlStartDate").value.substring(5, 7);
		if (month < 4) {
			args['code1'] = args['code1'] - 1;
		}		
	}else{
		//システム日付の年
		args['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	}
	args['code2'] = document.getElementById("form1:htmlYsnTCd").value;
	args['code3'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_5(thisObj, thisEvent) {

	//ＩＤ区分が「ユーザ」、「グループ」のどちらを選択されているか

	//ユーザ検索画面、もしくは、グループ検索画面を表示する
	if (document.getElementsByName("form1:htmlIdKbn")[1].checked) {
		var form = null;
		//ユーザの時
		openUserSearch();
	} else {
		var form = null;
		//グループの時
		openGrpSearch();
	}
	return false;
}
function func_6(thisObj, thisEvent) {


	//ＩＤ名称を取得する
	var servlet = "rev/co/CogIdNameAJAX";
	var target = "form1:htmlIdName"; 
	var args = new Array();

	//ＩＤ区分が「ユーザ」、「グループ」のどちらを選択されているか
	if (document.getElementsByName("form1:htmlIdKbn")[1].checked) {
		args['idkbn'] = "<%= SetteiTaniIdKbn.USER.getCode() %>";
	} else {
		args['idkbn'] = "<%= SetteiTaniIdKbn.GROUP.getCode() %>";
	}

	args['id'] = document.getElementById("form1:htmlId").value;
	args['length'] = '0';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_7(thisObj, thisEvent) {
	//ＩＤ名称を取得する
	func_6(thisObj, thisEvent);
	//予算単位名称を取得する
	func_4(thisObj, thisEvent);
	//フォーカスの設定
	if (document.getElementById("form1:htmlId").disabled == true) {
	} else {
		var target = document.getElementsByName("form1:htmlIdKbn");
		for (var i = 0;target.length > i;i++){
	    	if (target[i].nodeName.toUpperCase() == 'INPUT'){
				if (target[i].checked == true) {				
	            	target[i].focus();
	            	break;
				}
			}
	     }
	}
}
function func_8(thisObj, thisEvent) {
	//ＩＤ名称を取得する
	func_3(thisObj, thisEvent);
}
function func_9(thisObj, thisEvent) {
	//予算単位名称を取得する
	func_4(thisObj, thisEvent);
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="return func_7(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cog00402.onPageLoadBegin}">
<h:form styleClass="form" id="form1">
<style type="text/css">
<!--
.setWidth TD {width: 100px; white-space: nowrap;}
-->
</style>
<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/childHeader.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cog00402.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Cog00402.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Cog00402.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
	styleClass="commandExButton" id="returndisp" action="#{pc_Cog00402.doReturndispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
							style="" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="150" colspan="2"><h:outputText
										styleClass="outputText" id="lblIdKbn"
										value="#{pc_Cog00402.propIdKbn.labelName}"
										style="#{pc_Cog00402.propIdKbn.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlIdKbn" value="#{pc_Cog00402.propIdKbn.stringValue}"
										tabindex="1" disabled="#{pc_Cog00402.propIdKbn.disabled}"
										readonly="#{pc_Cog00402.propIdKbn.readonly}" onclick="return func_6(this, event);">
										<f:selectItems value="#{pc_Cog00402.propIdKbn.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblId"
										value="#{pc_Cog00402.propId.labelName}"
										style="#{pc_Cog00402.propId.labelStyle}"></h:outputText></TH>
									<TD nowrap>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlId" size="36"
												maxlength="#{pc_Cog00402.propId.maxLength}"
												style="#{pc_Cog00402.propId.style}"
												value="#{pc_Cog00402.propId.stringValue}"
												tabindex="2"
												disabled="#{pc_Cog00402.propId.disabled}"
												readonly="#{pc_Cog00402.propId.readonly}" onblur="return func_6(this, event);"></h:inputText> 
												<hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchId"
												tabindex="3"
												onclick="return func_5(this, event);"
												disabled="#{pc_Cog00402.propSearchId.disabled}"></hx:commandExButton>
											<h:outputText styleClass="outputText"
												id="htmlIdName" style="#{pc_Cog00402.propIdName.style}"
												value="#{pc_Cog00402.propIdName.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblStartDate"
										value="#{pc_Cog00402.propStartDate.labelName}"
										style="#{pc_Cog00402.propStartDate.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlStartDate"
										size="12" style="#{pc_Cog00402.propStartDate.style}"
										value="#{pc_Cog00402.propStartDate.dateValue}"
										disabled="#{pc_Cog00402.propStartDate.disabled}"
										readonly="#{pc_Cog00402.propStartDate.readonly}" tabindex="4" onblur="return func_9(this, event);">
										<f:convertDateTime pattern="yyyy/MM/dd" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2"><h:outputText
										styleClass="outputText" id="lblEndDate"
										value="#{pc_Cog00402.propEndDate.labelName}"
										style="#{pc_Cog00402.propEndDate.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlEndDate"
										size="12" style="#{pc_Cog00402.propEndDate.style}"
										value="#{pc_Cog00402.propEndDate.dateValue}"
										disabled="#{pc_Cog00402.propEndDate.disabled}"
										readonly="#{pc_Cog00402.propEndDate.readonly}" tabindex="5">
										<f:convertDateTime pattern="yyyy/MM/dd" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblYsnTCd"
										value="#{pc_Cog00402.propYsnTCd.labelName}"
										style="#{pc_Cog00402.propYsnTCd.labelStyle}"></h:outputText></TH>
									<TD>
										<DIV style="width:650px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText"
												id="htmlYsnTCd" size="12"
												disabled="#{pc_Cog00402.propYsnTCd.disabled}"
												maxlength="#{pc_Cog00402.propYsnTCd.maxLength}"
												readonly="#{pc_Cog00402.propYsnTCd.readonly}"
												style="#{pc_Cog00402.propYsnTCd.style}"
												value="#{pc_Cog00402.propYsnTCd.stringValue}"
												tabindex="6" onblur="return func_4(this, event);"></h:inputText><hx:commandExButton
												type="submit"
												styleClass="commandExButton_search" id="searchYsnTCd"
												tabindex="7" onclick="return func_8(this, event);"
												action="#{pc_Cog00402.doSearchYsnTCdAction}"
												disabled="#{pc_Cog00402.propSearchYsnTCd.disabled}"></hx:commandExButton>
											<h:outputText styleClass="outputText"
												id="htmlYsnTName" style="#{pc_Cog00402.propYsnTName.style}"
												value="#{pc_Cog00402.propYsnTName.stringValue}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_f" colspan="2"><h:outputText
										styleClass="outputText" id="lblShozokuYsnTFlg"
										value="#{pc_Cog00402.propShozokuYsnTFlg.labelName}"
										style="#{pc_Cog00402.propShozokuYsnTFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlShozokuYsnTFlg"
										disabled="#{pc_Cog00402.propShozokuYsnTFlg.disabled}"
										readonly="#{pc_Cog00402.propShozokuYsnTFlg.readonly}"
										value="#{pc_Cog00402.propShozokuYsnTFlg.stringValue}"
										tabindex="8">
										<f:selectItems value="#{pc_Cog00402.propShozokuYsnTFlg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH width="150" class="group_label_top" colspan="2"><h:outputText
										styleClass="outputText" id="lblKakuninKengen1" value="確認権限有無"
										style="#{pc_Cog00402.propRequiredDummy.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="lblKakuninKengen2" value="※所属する予算単位で有効"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="group_label" width="30"></TH>
									<TH class="v_g" width="120"><h:outputText
										styleClass="outputText" id="lblKakunin1KengenFlg"
										value="#{pc_Cog00402.propKakunin1KengenFlg.labelName}"
										style="#{pc_Cog00402.propKakunin1KengenFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlKakunin1KengenFlg"
										value="#{pc_Cog00402.propKakunin1KengenFlg.stringValue}"
										disabled="#{pc_Cog00402.propKakunin1KengenFlg.disabled}"
										readonly="#{pc_Cog00402.propKakunin1KengenFlg.readonly}" tabindex="9">
										<f:selectItems
											value="#{pc_Cog00402.propKakunin1KengenFlg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_a"><h:outputText styleClass="outputText"
										id="lblKakunin2KengenFlg"
										value="#{pc_Cog00402.propKakunin2KengenFlg.labelName}"
										style="#{pc_Cog00402.propKakunin2KengenFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlKakunin2KengenFlg"
										value="#{pc_Cog00402.propKakunin2KengenFlg.stringValue}"
										disabled="#{pc_Cog00402.propKakunin2KengenFlg.disabled}"
										readonly="#{pc_Cog00402.propKakunin2KengenFlg.readonly}" tabindex="10">
										<f:selectItems
											value="#{pc_Cog00402.propKakunin2KengenFlg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblKakunin3KengenFlg"
										value="#{pc_Cog00402.propKakunin3KengenFlg.labelName}"
										style="#{pc_Cog00402.propKakunin3KengenFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlKakunin3KengenFlg"
										value="#{pc_Cog00402.propKakunin3KengenFlg.stringValue}"
										tabindex="11"
										disabled="#{pc_Cog00402.propKakunin3KengenFlg.disabled}"
										readonly="#{pc_Cog00402.propKakunin3KengenFlg.readonly}">
										<f:selectItems
											value="#{pc_Cog00402.propKakunin3KengenFlg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblKakunin4KengenFlg"
										value="#{pc_Cog00402.propKakunin4KengenFlg.labelName}"
										style="#{pc_Cog00402.propKakunin4KengenFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlKakunin4KengenFlg"
										value="#{pc_Cog00402.propKakunin4KengenFlg.stringValue}"
										disabled="#{pc_Cog00402.propKakunin4KengenFlg.disabled}"
										readonly="#{pc_Cog00402.propKakunin4KengenFlg.readonly}" tabindex="12">
										<f:selectItems
											value="#{pc_Cog00402.propKakunin4KengenFlg.list}" />
									</h:selectOneRadio></TD>

								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblKakunin5KengenFlg"
										value="#{pc_Cog00402.propKakunin5KengenFlg.labelName}"
										style="#{pc_Cog00402.propKakunin5KengenFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlKakunin5KengenFlg"
										value="#{pc_Cog00402.propKakunin5KengenFlg.stringValue}"
										disabled="#{pc_Cog00402.propKakunin5KengenFlg.disabled}"
										readonly="#{pc_Cog00402.propKakunin5KengenFlg.readonly}" tabindex="13">
										<f:selectItems
											value="#{pc_Cog00402.propKakunin5KengenFlg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblKakunin6KengenFlg"
										value="#{pc_Cog00402.propKakunin6KengenFlg.labelName}"
										style="#{pc_Cog00402.propKakunin6KengenFlg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlKakunin6KengenFlg"
										value="#{pc_Cog00402.propKakunin6KengenFlg.stringValue}"
										disabled="#{pc_Cog00402.propKakunin6KengenFlg.disabled}"
										readonly="#{pc_Cog00402.propKakunin6KengenFlg.readonly}" tabindex="14">
										<f:selectItems
											value="#{pc_Cog00402.propKakunin6KengenFlg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_g" width="120"><h:outputText styleClass="outputText"
										id="lblSasimodosi1Flg"
										value="#{pc_Cog00402.propSasimodosi1Flg.labelName}"
										style="#{pc_Cog00402.propSasimodosi1Flg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlSasimodosi1Flg"
										value="#{pc_Cog00402.propSasimodosi1Flg.stringValue}"
										disabled="#{pc_Cog00402.propSasimodosi1Flg.disabled}"
										readonly="#{pc_Cog00402.propSasimodosi1Flg.readonly}" tabindex="15">
										<f:selectItems
											value="#{pc_Cog00402.propSasimodosi1Flg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblSasimodosi2Flg"
										value="#{pc_Cog00402.propSasimodosi2Flg.labelName}"
										style="#{pc_Cog00402.propSasimodosi2Flg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlSasimodosi2Flg"
										value="#{pc_Cog00402.propSasimodosi2Flg.stringValue}"
										disabled="#{pc_Cog00402.propSasimodosi2Flg.disabled}"
										readonly="#{pc_Cog00402.propSasimodosi2Flg.readonly}" tabindex="16">
										<f:selectItems
											value="#{pc_Cog00402.propSasimodosi2Flg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblSasimodosi3Flg"
										value="#{pc_Cog00402.propSasimodosi3Flg.labelName}"
										style="#{pc_Cog00402.propSasimodosi3Flg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlSasimodosi3Flg"
										value="#{pc_Cog00402.propSasimodosi3Flg.stringValue}"
										disabled="#{pc_Cog00402.propSasimodosi3Flg.disabled}"
										readonly="#{pc_Cog00402.propSasimodosi3Flg.readonly}" tabindex="17">
										<f:selectItems
											value="#{pc_Cog00402.propSasimodosi3Flg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblSasimodosi4Flg"
										value="#{pc_Cog00402.propSasimodosi4Flg.labelName}"
										style="#{pc_Cog00402.propSasimodosi4Flg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlSasimodosi4Flg"
										value="#{pc_Cog00402.propSasimodosi4Flg.stringValue}"
										disabled="#{pc_Cog00402.propSasimodosi4Flg.disabled}"
										readonly="#{pc_Cog00402.propSasimodosi4Flg.readonly}" tabindex="18">
										<f:selectItems
											value="#{pc_Cog00402.propSasimodosi4Flg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label"></TH>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblSasimodosi5Flg"
										value="#{pc_Cog00402.propSasimodosi5Flg.labelName}"
										style="#{pc_Cog00402.propSasimodosi5Flg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlSasimodosi5Flg"
										value="#{pc_Cog00402.propSasimodosi5Flg.stringValue}"
										disabled="#{pc_Cog00402.propSasimodosi5Flg.disabled}"
										readonly="#{pc_Cog00402.propSasimodosi5Flg.readonly}" tabindex="19">
										<f:selectItems
											value="#{pc_Cog00402.propSasimodosi5Flg.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="group_label_bottom"></TH>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblSasimodosi6Flg"
										value="#{pc_Cog00402.propSasimodosi6Flg.labelName}"
										style="#{pc_Cog00402.propSasimodosi6Flg.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneRadio styleClass="selectOneRadio setWidth"
										id="htmlSasimodosi6Flg"
										value="#{pc_Cog00402.propSasimodosi6Flg.stringValue}"
										disabled="#{pc_Cog00402.propSasimodosi6Flg.disabled}"
										readonly="#{pc_Cog00402.propSasimodosi6Flg.readonly}" tabindex="20">
										<f:selectItems
											value="#{pc_Cog00402.propSasimodosi6Flg.list}" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="登録"
										styleClass="commandExButton_dat" id="register" tabindex="21"
										action="#{pc_Cog00402.doRegisterAction}"
										confirm="#{msg.SY_MSG_0002W}"
										rendered="#{pc_Cog00402.propRegister.rendered}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="更新"
										styleClass="commandExButton_dat" id="update" tabindex="22"
										action="#{pc_Cog00402.doUpdateAction}"
										confirm="#{msg.SY_MSG_0003W}"
										rendered="#{pc_Cog00402.propUpdate.rendered}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="削除"
										styleClass="commandExButton_dat" id="delete" tabindex="23"
										action="#{pc_Cog00402.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"
										rendered="#{pc_Cog00402.propDelete.rendered}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="クリア"
										styleClass="commandExButton_etc" id="clear" tabindex="24"
										action="#{pc_Cog00402.doClearAction}"
										rendered="#{pc_Cog00402.propClear.rendered}"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
	<h:inputHidden value="#{pc_Cog00402.propKaikeiNendoHidden.stringValue}"
		id="htmlKaikeiNendoHidden">
	</h:inputHidden>
	<h:inputHidden
		value="#{pc_Cog00402.propExecutableSearchHidden.integerValue}"
		id="htmlExecutableSearchHidden">

		<f:convertNumber />
	</h:inputHidden>

</h:form>
</hx:scriptCollector>
	</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

