<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaz01301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>決裁権限パターン登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_YsnTCdAAjax(thisObj, thisEvent) {
	// 予算単位名称(検索条件項目)を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlSearchYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlSearchChotNendo").value;
	args['code2'] = document.getElementById("form1:htmlSearchYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function func_YsnTCdBAjax(thisObj, thisEvent) {
	// 予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlChotNendo").value;
	args['code2'] = document.getElementById("form1:htmlYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function func_UserIdAjax(thisObj, thisEvent) {
	// ユーザ名称を取得する
	var servlet = "rev/co/CosUserAJAX";
	var target = "form1:htmlUserName";
	var args = document.getElementById("form1:htmlSearchUserId").value;
	getCodeName(servlet, target, args);

}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function loadFunc() {
	changeScrollPosition('scroll', 'listScroll');
	func_YsnTCdAAjax(document.getElementById('form1:htmlSearchYsnTCd'), '');
	func_YsnTCdBAjax(document.getElementById('form1:htmlYsnTCd'), '');
	func_UserIdAjax(document.getElementById('form1:htmlSearchUserId'), '');
}

window.attachEvent("onload", attachFormatNumber);
window.attachEvent("onload", loadFunc);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="return loadFunc();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kaz01301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kaz01301.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kaz01301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kaz01301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<!-- ↓ここに戻る／閉じるボタンを配置 --> 
　<!-- ← レイアウト調整の為に、、全角スペースを配置-->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="880" style="margin-top: 20px;">
				<TBODY>
					
					<TR>
						<TH class="v_a" width="140"><h:outputText styleClass="outputText"
							id="lblSearchChotNendo" value="#{pc_Kaz01301.propSearchChotNendo.labelName}"
							style="#{pc_Kaz01301.propSearchChotNendo.labelStyle}"></h:outputText></TH>
						<TD width="130"><h:inputText styleClass="inputText"
							id="htmlSearchChotNendo" size="4"
							value="#{pc_Kaz01301.propSearchChotNendo.dateValue}"
							onblur="return func_YsnTCdAAjax(this, event);"
							style="#{pc_Kaz01301.propSearchChotNendo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
							</h:inputText></TD>
						<TH class="v_b" width="160"><h:outputText styleClass="outputText"
							id="lblSearchYsnTCd" value="#{pc_Kaz01301.propSearchYsnTCd.labelName}"
							style="#{pc_Kaz01301.propSearchYsnTCd.labelStyle}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:450px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" id="htmlSearchYsnTCd"
							size="15" value="#{pc_Kaz01301.propSearchYsnTCd.stringValue}"
							style="#{pc_Kaz01301.propSearchYsnTCd.style}"
							onblur="return func_YsnTCdAAjax(this, event);"
							maxlength="#{pc_Kaz01301.propSearchYsnTCd.maxLength}"></h:inputText><h:outputText styleClass="outputText" value="（前方一致）" id="lblSearchYsnTCdFindType">
							</h:outputText><hx:commandExButton
							type="submit" styleClass="commandExButton_search"
							id="searchYsnTCdA"
							action="#{pc_Kaz01301.doSearchYsnTCdAAction}">
						</hx:commandExButton>
							<h:outputText styleClass="outputText" id="htmlSearchYsnTName">
							</h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TH class="v_c" width="140"><h:outputText styleClass="outputText"
							id="lblSearchKsaPaternNo" value="#{pc_Kaz01301.propSearchKsaPaternNo.labelName}"
							style="#{pc_Kaz01301.propSearchKsaPaternNo.labelStyle}"></h:outputText></TH>
						<TD width="130"><h:inputText styleClass="inputText" id="htmlSearchKsaPaternNo" size="2"
							value="#{pc_Kaz01301.propSearchKsaPaternNo.stringValue}"
							style="#{pc_Kaz01301.propSearchKsaPaternNo.style}"
							maxlength="#{pc_Kaz01301.propSearchKsaPaternNo.maxLength}"></h:inputText></TD>	
						<TH class="v_d" width="160"><h:outputText styleClass="outputText"
							id="lblSearchKsaPaternName" value="#{pc_Kaz01301.propSearchKsaPaternName.labelName}"
							style="#{pc_Kaz01301.propSearchKsaPaternName.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText" id="htmlSearchKsaPaternName" size="55"
							value="#{pc_Kaz01301.propSearchKsaPaternName.stringValue}"
							style="#{pc_Kaz01301.propSearchKsaPaternName.style}"
							maxlength="#{pc_Kaz01301.propSearchKsaPaternName.maxLength}"></h:inputText>
							<h:outputText styleClass="outputText" value="（部分一致）" id="lblSearchKsaPaternNameFindType">
							</h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_e" width="140"><h:outputText styleClass="outputText"
							id="lblSearchKingakuMax" value="上限金額(半12)"></h:outputText></TH>
						<TD colspan="3">	
							<h:inputText id="htmlSearchKingakuMaxFrom" styleClass="inputText"
							style="padding-right: 3px; text-align: right; #{pc_Kaz01301.propSearchKingakuMaxFrom.style}"
							value="#{pc_Kaz01301.propSearchKingakuMaxFrom.stringValue}" size="16"
							maxlength="#{pc_Kaz01301.propSearchKingakuMaxFrom.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText>
						<h:outputText styleClass="outputText" value="～"
							id="lblSearchKingakuMaxType">
						</h:outputText><h:inputText id="htmlSearchKingakuMaxTo" styleClass="inputText"
							style="padding-right: 3px; text-align: right; #{pc_Kaz01301.propSearchKingakuMaxTo.style}"
							value="#{pc_Kaz01301.propSearchKingakuMaxTo.stringValue}" size="16"
							maxlength="#{pc_Kaz01301.propSearchKingakuMaxTo.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_f" width="140"><h:outputText styleClass="outputText"
							id="lblSearchRingiFlg" value="#{pc_Kaz01301.propSearchRingiFlg.labelName}"></h:outputText></TH>
						<TD width="130">	
							<h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled"
								styleClass="selectManyCheckbox" id="htmlSearchRingiFlg"
								style="text-align: left"
								value="#{pc_Kaz01301.propSearchRingiFlg.stringValue}">
							<f:selectItems value="#{pc_Kaz01301.propSearchRingiFlg.list}" />
							</h:selectManyCheckbox></TD>
						<TH class="v_g" width="160"><h:outputText styleClass="outputText"
							id="lblSearchYukoMukoFlg" value="#{pc_Kaz01301.propSearchYukoMukoFlg.labelName}"></h:outputText></TH>
						<TD width="450"><h:selectManyCheckbox disabledClass="selectManyCheckbox_Disabled"
								styleClass="selectManyCheckbox" id="htmlSearchYukoMukoFlg"
								style="text-align: left"
								value="#{pc_Kaz01301.propSearchYukoMukoFlg.stringValue}">
							<f:selectItems value="#{pc_Kaz01301.propSearchYukoMukoFlg.list}" />
							</h:selectManyCheckbox></TD>
					</TR>					
					<TR>
						<TH class="v_a" width="140"><h:outputText styleClass="outputText"
							id="lblSearchUserId" value="#{pc_Kaz01301.propSearchUserId.labelName}"
							style="#{pc_Kaz01301.propSearchUserId.labelStyle}"></h:outputText></TH>
						<TD colspan="3" nowrap>
							<DIV style="width:740px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" id="htmlSearchUserId" size="35"
							value="#{pc_Kaz01301.propSearchUserId.stringValue}"
							style="#{pc_Kaz01301.propSearchUserId.style}"
							onblur="return func_UserIdAjax(this, event);"
							maxlength="#{pc_Kaz01301.propSearchUserId.maxLength}"></h:inputText><hx:commandExButton
							type="submit" styleClass="commandExButton_search"
							id="searchUserId" action="#{pc_Kaz01301.doSearchUserIdAction}">
						</hx:commandExButton>
							<h:outputText styleClass="outputText" id="htmlUserName" 
							value="#{pc_Kaz01301.propUserName.stringValue}">
							</h:outputText>
							</DIV>
						</TD>				
					</TR>
					
				</TBODY>
				
			</TABLE>
			
			<BR>
			<TABLE width="880" border="0" style="" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center" width="40%"></TD>
						<TD align="center"><hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search"
							action="#{pc_Kaz01301.doSearchAction}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clearA"
							action="#{pc_Kaz01301.doClearAAction}"></hx:commandExButton></TD>
						<TD align="center" width="40%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="880" class="" style="margin-top:0px">
				<TBODY>
					<TR>
						<TD width="855" align="right"><h:outputText
							styleClass="outputText" id="htmlKsaiListCount"
							value="#{pc_Kaz01301.propKsaiList.listCount}"
							style="font-size: 8pt"></h:outputText><SPAN
							style="font-size: 8pt">件</SPAN></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="880">
				<TBODY>
				<TR>
					<TD>
						<DIV class="listScroll"
							style="height:150px; OVERFLOW:scroll;overflow-x: hidden;" 
							id="listScroll" 
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Kaz01301.propKsaiList.rowClasses}"
							styleClass="meisai_scroll" id="htmlKsaiList" width="860"
							value="#{pc_Kaz01301.propKsaiList.list}" var="varlist"
							first="#{pc_Kaz01301.propKsaiList.first}"
							rows="#{pc_Kaz01301.propKsaiList.rows}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListYsnTName" value="予算単位名称">
									</h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel1">
									<DIV
										style="width:200px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListYsnTName"
										value="#{varlist.ysnTName.stringValue}"
										title="#{varlist.ysnTName.stringValue}" styleClass="outputText"
										style="white-space:nowrap;">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="200" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText"
										value="ＮＯ" id="lblListKsaPaternNo">
									</h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel2">
									<DIV
										style="width:30px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListKsaPaternNo"
										value="#{varlist.ksaPaternNo.stringValue}"
										title="#{varlist.ksaPaternNo.stringValue}"
										styleClass="outputText" style="white-space:nowrap;">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="30" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="決裁パターン名称"
										id="lblListKsaPaternName">
									</h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel3">
									<DIV
										style="width:343px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListKsaPaternName"
										value="#{varlist.ksaPaternName.stringValue}"
										title="#{varlist.ksaPaternName.stringValue}"
										styleClass="outputText" style="white-space:nowrap;">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="343" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="上限金額"
										id="lblListKingakuMax">
									</h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel4">
									<DIV
										style="width:86px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListKingakuMax"
										value="#{varlist.kingakuMax.stringValue}"
										title="#{varlist.kingakuMax.stringValue}"
										styleClass="outputText" style="white-space:nowrap; text-align: right">
										<f:convertNumber pattern="###,###,###,##0;###,###,###,##0" />
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="86" name="width" />
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="padding-right: 3px; text-align: right" name="style" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="稟議番号"
										id="lblListRingiNo">
									</h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel5">
									<DIV
										style="width:60px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListRingiNo"
										value="#{varlist.ringiNo.stringValue}"
										title="#{varlist.ringiNo.stringValue}"
										styleClass="outputText" style="text-align: center">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="60" name="width" />
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListYukoMukoFlg"
										value="状況">
									</h:outputText>
								</f:facet>
								<hx:jspPanel id="jspPanel6">
									<DIV
										style="width:36px;white-space:nowrap;overflow:hidden;display:block;">
									<h:outputText id="htmlListYukoMukoFlg"
										value="#{varlist.yukoMukoFlg.stringValue}"
										title="#{varlist.yukoMukoFlg.stringValue}"
										styleClass="outputText" style="text-align: center">
									</h:outputText></DIV>
								</hx:jspPanel>
								<f:attribute value="36" name="width" />
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header"></f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Kaz01301.doSelectAction}" style="width:33px">
								</hx:commandExButton>
								<f:attribute value="33px" name="width" />
							</h:column>
							<h:column id="column8">
								<f:facet name="header"></f:facet>
								<hx:commandExButton type="submit" value="決裁者登録"
									styleClass="commandExButton" id="ksaiSyaIns"
									action="#{pc_Kaz01301.doKsaiSyaInsAction}" style="width:72px">
								</hx:commandExButton>
								<f:attribute value="72px" name="width" />
							</h:column>
						</h:dataTable></DIV>
					</TD>
				<TD></TD>
				</TR>					
				</TBODY>
			</TABLE>
			

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="880" style="margin-top: 20px;">
				<TBODY>

					<TR>
						<TH class="v_b" width="140"><h:outputText styleClass="outputText"
							id="lblChotNendo"
							value="#{pc_Kaz01301.propChotNendo.labelName}"
							style="#{pc_Kaz01301.propChotNendo.labelStyle}"></h:outputText></TH>
						<TD width="130"><h:inputText styleClass="inputText"
							id="htmlChotNendo" size="4"
							value="#{pc_Kaz01301.propChotNendo.dateValue}"
							onblur="return func_YsnTCdBAjax(this, event);"
							style="#{pc_Kaz01301.propChotNendo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
							</h:inputText></TD>
						<TH class="v_c" width="160"><h:outputText styleClass="outputText"
							id="lblYsnTCd" value="#{pc_Kaz01301.propYsnTCd.labelName}"
							style="#{pc_Kaz01301.propYsnTCd.labelStyle}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:450px;white-space:nowrap;overflow:hidden;display:block;">
							<h:inputText styleClass="inputText" id="htmlYsnTCd"
							size="15" value="#{pc_Kaz01301.propYsnTCd.stringValue}"
							style="#{pc_Kaz01301.propYsnTCd.style}"
							onblur="return func_YsnTCdBAjax(this, event);"
							maxlength="#{pc_Kaz01301.propYsnTCd.maxLength}"></h:inputText><hx:commandExButton
							type="submit" styleClass="commandExButton_search"
							id="searchYsnTCdB"
							action="#{pc_Kaz01301.doSearchYsnTCdBAction}">
						</hx:commandExButton>
							<h:outputText styleClass="outputText" id="htmlYsnTName">
							</h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TH class="v_d" width="140"><h:outputText styleClass="outputText"
							id="lblKsaPaternNo" value="#{pc_Kaz01301.propKsaPaternNo.labelName}"
							style="#{pc_Kaz01301.propKsaPaternNo.labelStyle}"></h:outputText></TH>
						<TD width="130"><h:inputText styleClass="inputText" id="htmlKsaPaternNo" size="2"
							value="#{pc_Kaz01301.propKsaPaternNo.stringValue}"
							style="#{pc_Kaz01301.propKsaPaternNo.style}"
							maxlength="#{pc_Kaz01301.propKsaPaternNo.maxLength}"></h:inputText></TD>	
						<TH class="v_e" width="160"><h:outputText styleClass="outputText"
							id="lblKsaPaternName" value="#{pc_Kaz01301.propKsaPaternName.labelName}"
							style="#{pc_Kaz01301.propKsaPaternName.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText" id="htmlKsaPaternName" size="55"
							value="#{pc_Kaz01301.propKsaPaternName.stringValue}"
							style="#{pc_Kaz01301.propKsaPaternName.style}"
							maxlength="#{pc_Kaz01301.propKsaPaternName.maxLength}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_f" width="140"><h:outputText styleClass="outputText"
							id="lblKingakuMax" value="上限金額(半12)"></h:outputText></TH>
						<TD colspan="3">	
							<h:inputText id="htmlKingakuMax" styleClass="inputText"
							style="padding-right: 3px; text-align: right; #{pc_Kaz01301.propKingakuMax.style}"
							value="#{pc_Kaz01301.propKingakuMax.stringValue}" size="16"
							maxlength="#{pc_Kaz01301.propKingakuMax.maxLength}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText>						
					</TR>
					<TR>
						<TH class="v_g" width="140"><h:outputText styleClass="outputText"
							id="lblRingiFlg" value="#{pc_Kaz01301.propRingiFlg.labelName}"></h:outputText></TH>
						<TD width="130">	
							<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
								styleClass="selectOneRadio" id="htmlRingiFlg"
								style="text-align: left"
								value="#{pc_Kaz01301.propRingiFlg.stringValue}">
							<f:selectItems value="#{pc_Kaz01301.propRingiFlg.list}" />
							</h:selectOneRadio></TD>
						<TH class="v_a" width="160"><h:outputText styleClass="outputText"
							id="lblYukoMukoFlg" value="#{pc_Kaz01301.propYukoMukoFlg.labelName}"></h:outputText></TH>
						<TD width="450"><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
								styleClass="selectOneRadio" id="htmlYukoMukoFlg"
								style="text-align: left"
								value="#{pc_Kaz01301.propYukoMukoFlg.stringValue}">
							<f:selectItems value="#{pc_Kaz01301.propYukoMukoFlg.list}" />
							</h:selectOneRadio></TD>
					</TR>
					
				</TBODY>

			</TABLE>
			<BR>
			<TABLE width="880" border="0" style="" class="button_bar">
				<TBODY>
				<TR>
					<TD align="center">
						<hx:commandExButton type="submit" value="登録"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Kaz01301.doRegisterAction}"
							confirm="#{msg.SY_MSG_0002W}">
						</hx:commandExButton>
							<hx:commandExButton type="submit" value="更新"
							styleClass="commandExButton_dat" id="update"
							confirm="#{msg.SY_MSG_0003W}"
							action="#{pc_Kaz01301.doUpdateAction}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="削除"
							styleClass="commandExButton_dat" id="delete"
							confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Kaz01301.doDeleteAction}">
						</hx:commandExButton>
						<hx:commandExButton	type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clearB"
							action="#{pc_Kaz01301.doClearBAction}">
						</hx:commandExButton>
						<hx:commandExButton	type="submit" value="CSV作成"
							styleClass="commandExButton_out" id="csvout"
							action="#{pc_Kaz01301.doCsvoutAction}"
							confirm="#{msg.SY_MSG_0020W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="出力項目指定"
							styleClass="commandExButton_out" id="setoutput"
							action="#{pc_Kaz01301.doSetoutputAction}">
						</hx:commandExButton>
					</TD>
				</TR>
					
				</TBODY>
			</TABLE>
			

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<%-- 隠しコード --%>
			<h:inputHidden value="#{pc_Kaz01301.propExecutableSearch.integerValue}"
					id="htmlExecutableSearch">
			</h:inputHidden>
			<h:inputHidden value="htmlSearchKingakuMaxFrom=ZZZ,ZZZ,ZZZ,ZZ9;ZZZ,ZZZ,ZZZ,ZZ9 | htmlSearchKingakuMaxTo=ZZZ,ZZZ,ZZZ,ZZ9;ZZZ,ZZZ,ZZZ,ZZ9 | htmlKingakuMax=ZZZ,ZZZ,ZZZ,ZZ9;ZZZ,ZZZ,ZZZ,ZZ9"
				id="htmlFormatNumberOption">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Kaz01301.propKsaiList.scrollPosition}"
				id="scroll">
			</h:inputHidden>			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

