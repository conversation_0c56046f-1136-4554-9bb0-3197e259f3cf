<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsc00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Nsc00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_2(thisObj, thisEvent) {
document.forms[0].elements.item("form1:htmlSelJknCd").checked = false;
document.forms[0].elements.item("form1:htmlHidden").value = "0";
}
function func_3(thisObj, thisEvent) {
document.forms[0].elements.item("form1:htmlSelSgnCd").checked = false;document.forms[0].elements.item("form1:htmlHidden").value = "1";
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Nsc00101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Nsc00101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Nsc00101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Nsc00101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="80%">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblNyushiNendo"
										value="#{pc_Nsc00101.propNyushiNendo.labelName}"
										style="#{pc_Nsc00101.propNyushiNendo.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:inputText styleClass="inputText"
										id="htmlNyushiNendo" size="5"
										value="#{pc_Nsc00101.propNyushiNendo.dateValue}" tabindex="1"
										style="#{pc_Nsc00101.propNyushiNendo.style}">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblNyushiGakkiNo"
										style="#{pc_Nsc00101.propNyushiGakkiNo.labelStyle}"
										value="#{pc_Nsc00101.propNyushiGakkiNo.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText" id="htmlNyushiGakkiNo"
										size="2" value="#{pc_Nsc00101.propNyushiGakkiNo.integerValue}"
										disabled="#{pc_Nsc00101.propNyushiNendo.disabled}"
										maxlength="#{pc_Nsc00101.propNyushiNendo.maxLength}"
										readonly="#{pc_Nsc00101.propNyushiNendo.readonly}"
										style="#{pc_Nsc00101.propNyushiNendo.style}" tabindex="2">
										<f:convertNumber pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblSelectWay" value="登録方法選択"></h:outputText></TH>
									<TD colspan="3">
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="100%" bgcolor="" bordercolor="">
										<TBODY>
											<TR>
												<TD width="20" class="clear_border"></TD>
												<TD class="clear_border"></TD>
											</TR>
											<TR>
												<TD class="clear_border">
												<DIV align="right"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlSelSgnCd"
													onclick="return func_2(this, event);" tabindex="3"
													value="#{pc_Nsc00101.propSelSgnCd.value}">
													<f:selectItem itemValue="0" itemLabel=" " />
												</h:selectOneRadio></DIV>
												</TD>
												<TD class="clear_border"><h:outputText
													styleClass="outputText" id="lblSelectSgn1"
													value="志願者番号を指定して処理する。"></h:outputText></TD>
											</TR>
											<TR>
												<TD class="clear_border">&nbsp;</TD>
												<TD class="clear_border"><B><h:outputText
													styleClass="outputText" id="lblSelectSgn2"
													value="志願者個人の情報を登録、修正します。" style="font-weight: bold"></h:outputText></B></TD>
											</TR>
											<TR>
												<TD class="clear_border">&nbsp;</TD>
												<TD class="clear_border"><B><h:outputText
													styleClass="outputText" id="lblSelectSgn3"
													value="また、併願状況の照会も行えます。" style="font-weight: bold"></h:outputText></B></TD>
											</TR>
											<TR>
												<TD class="clear_border">&nbsp;</TD>
												<TD class="clear_border"></TD>
											</TR>
											<TR>
												<TD class="clear_border">
												<DIV align="right"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlSelJknCd"
													onclick="return func_3(this, event);" tabindex="4"
													value="#{pc_Nsc00101.propSelJknCd.value}">
													<f:selectItem itemValue="1" itemLabel=" " />
												</h:selectOneRadio></DIV>
												</TD>
												<TD class="clear_border"><h:outputText
													styleClass="outputText" id="lblSelectJkn1"
													value="受験番号を指定して処理する。"></h:outputText></TD>
											</TR>
											<TR>
												<TD class="clear_border">&nbsp;</TD>
												<TD class="clear_border"><B><h:outputText
													styleClass="outputText" id="lblSelectJkn2"
													value="受験情報を登録、修正します。" style="font-weight: bold"></h:outputText></B></TD>
											</TR>
											<TR>
												<TD class="clear_border">&nbsp;</TD>
												<TD class="clear_border"><B><h:outputText
													styleClass="outputText" id="lblSelectJkn3"
													value="志願者の個人情報を登録、修正も行えます。" style="font-weight: bold"></h:outputText></B></TD>
											</TR>
											<TR>
												<TD class="clear_border">&nbsp;</TD>
												<TD class="clear_border">&nbsp;</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>


						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="exec" tabindex="5"
										action="#{pc_Nsc00101.doExecAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlHidden"
				value="#{pc_Nsc00101.propHidden.stringValue}"></h:inputHidden></DIV>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

