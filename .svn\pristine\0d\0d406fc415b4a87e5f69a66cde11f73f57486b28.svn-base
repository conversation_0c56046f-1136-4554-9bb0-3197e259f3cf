<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kag00402.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kag00402.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

function doCheck(listId, boxId) {
	check(listId, boxId);
}
function doUncheck(listId, boxId) {	
	uncheck(listId, boxId);
}

function confirmOk() {
	document.getElementById('form1:htmlConfRes').value = "1";
	indirectClick('ACTION_EXEC');
}
function confirmCancel() {
	document.getElementById('form1:htmlConfRes').value = "0";
}

function func_YsnTAJAX(thisObj, thisEvent) {
	// 予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlYsnTNameLbl";
	var code = new Array();
	code['code1'] = document.getElementById("form1:htmlKaikeiNendoHidden").value;
	code['code2'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function endload(){
	changeScrollPosition('scroll', 'listScroll');
	// ajax
	func_YsnTAJAX(document.getElementById("form1:htmlYsnTCd"), null);
}
//onloadイベントにアタッチ
window.attachEvent('onload', endload);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kag00402.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Kag00402.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kag00402.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kag00402.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err">
<LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<hx:commandExButton
		type="submit"
		value="戻る"
		styleClass="commandExButton"
		id="back"
		action="#{pc_Kag00402.doBackAction}">
	</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style=""
		   class="table" 
		   width="900">
		<TBODY>
			<TR>
				<TH class="v_a" width="160">
					<h:outputText
						styleClass="outputText" 
						id="lblKaikeiNendoStr" 
						style="#{pc_Kag00402.propKaikeiNendoStr.labelStyle}" 
						value="#{pc_Kag00402.propKaikeiNendoStr.labelName}">
					</h:outputText>
				</TH>
				<TD width="290">
					<h:outputText
						styleClass="outputText"
						id="htmlKaikeiNendoStr"
						style="#{pc_Kag00402.propKaikeiNendoStr.style}"
						value="#{pc_Kag00402.propKaikeiNendoStr.stringValue}"
						rendered="#{pc_Kag00402.propKaikeiNendoStr.rendered}">
					</h:outputText>
					<h:inputHidden
						id="htmlKaikeiNendoHidden"
						value="#{pc_Kag00402.kaikeiNendo}">
					</h:inputHidden>
				</TD>
				<TH class="v_a" width="160">
					<h:outputText
						styleClass="outputText" 
						id="lblKeijyoTuki" 
						style="#{pc_Kag00402.propKeijyoTuki.labelStyle}" 
						value="#{pc_Kag00402.propKeijyoTuki.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:outputText
						styleClass="outputText"
						id="htmlKeijyoTuki"
						style="#{pc_Kag00402.propKeijyoTuki.style}"
						value="#{pc_Kag00402.propKeijyoTuki.stringValue}"
						rendered="#{pc_Kag00402.propKeijyoTuki.rendered}">
					</h:outputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText" 
						id="lblShokyakuType" 
						style="#{pc_Kag00402.propShokyakuType.labelStyle}" 
						value="#{pc_Kag00402.propShokyakuType.labelName}">
					</h:outputText>
				</TH>
				<TD colspan="3">
					<h:outputText
						styleClass="outputText"
						id="htmlShokyakuType"
						style="#{pc_Kag00402.propShokyakuType.style}"
						value="#{pc_Kag00402.propShokyakuType.stringValue}"
						rendered="#{pc_Kag00402.propShokyakuType.rendered}">
					</h:outputText>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="900" 
		   class="table"
		   style="margin-top:10px;">
		<TBODY>
			<TR>
				<TH class="v_a" width="160">
					<h:outputText
						styleClass="outputText" 
						id="lblShimeKbn" 
						style="#{pc_Kag00402.propShimeKbn.labelStyle}" 
						value="#{pc_Kag00402.propShimeKbn.labelName}">
					</h:outputText>
				</TH>
				<TD width="740">
					<h:selectOneMenu
						styleClass="outputText"
						id="htmlShimeKbn"
						style="#{pc_Kag00402.propShimeKbn.style}"
						value="#{pc_Kag00402.propShimeKbn.stringValue}"
						disabled="#{pc_Kag00402.propShimeKbn.disabled}"
						rendered="#{pc_Kag00402.propShimeKbn.rendered}">
							<f:selectItems value="#{pc_Kag00402.propShimeKbn.list}" />
					</h:selectOneMenu>
				</TD>
			</TR>
			<TR>
				<TH class="v_a" nowrap>
				<DIV style="width:160px;white-space:nowrap;overflow:hidden;display:block;">
					<h:outputText
						styleClass="outputText" 
						id="lblYsnTCd" 
						style="#{pc_Kag00402.propYsnTCd.labelStyle}" 
						value="#{pc_Kag00402.propYsnTCd.labelName}">
					</h:outputText>
				</DIV>
				</TH>
				<TD nowrap>
					<DIV style="width:640px;white-space:nowrap;overflow:hidden;display:block;">
						<h:inputText
							styleClass="inputText"
							id="htmlYsnTCd"
							size="20"
							disabled="#{pc_Kag00402.propYsnTCd.disabled}"
							maxlength="#{pc_Kag00402.propYsnTCd.maxLength}"
							readonly="#{pc_Kag00402.propYsnTCd.readonly}"
							rendered="#{pc_Kag00402.propYsnTCd.rendered}"
							style="#{pc_Kag00402.propYsnTCd.style}"
							value="#{pc_Kag00402.propYsnTCd.stringValue}"
							onblur="return func_YsnTAJAX(this, event);">
						</h:inputText>
						<hx:commandExButton
							type="submit"
							styleClass="commandExButton_search"
							id="ysnTSearch"
							disabled="#{pc_Kag00402.propYsnTSearch.disabled}"
							action="#{pc_Kag00402.doYsnTSearchAction}">
						</hx:commandExButton>
						<h:outputText
							styleClass="outputText"
							id="htmlYsnTNameLbl">
						</h:outputText>
					</DIV>
				</TD>
			</TR>
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText" 
						id="lblSuitoDate" 
						style="#{pc_Kag00402.propSuitoDate.labelStyle}" 
						value="#{pc_Kag00402.propSuitoDate.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:inputText styleClass="inputText"
						id="htmlSuitoDate"
						size="12"
						disabled="#{pc_Kag00402.propSuitoDate.disabled}"
						readonly="#{pc_Kag00402.propSuitoDate.readonly}"
						rendered="#{pc_Kag00402.propSuitoDate.rendered}"
						style="#{pc_Kag00402.propSuitoDate.style}"
						value="#{pc_Kag00402.propSuitoDate.dateValue}">
						<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" />
						<hx:inputHelperDatePicker />
						<f:convertDateTime pattern="yyyy/MM/dd" />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText" 
						id="lblIkkatsuKbn" 
						style="#{pc_Kag00402.propIkkatsuKbn.labelStyle}" 
						value="#{pc_Kag00402.propIkkatsuKbn.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:selectOneMenu
						styleClass="outputText"
						id="htmlIkkatsuKbn"
						style="#{pc_Kag00402.propIkkatsuKbn.style}"
						value="#{pc_Kag00402.propIkkatsuKbn.stringValue}"
						rendered="#{pc_Kag00402.propIkkatsuKbn.rendered}">
							<f:selectItems value="#{pc_Kag00402.propIkkatsuKbn.list}" />
					</h:selectOneMenu>
				</TD>
			</TR>
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText" 
						id="lblDenpyoSplit" 
						style="#{pc_Kag00402.propDenpyoSplit.labelStyle}" 
						value="#{pc_Kag00402.propDenpyoSplit.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
						id="htmlDenpyoSplit"
						style="#{pc_Kag00402.propDenpyoSplit.style}"
						value="#{pc_Kag00402.propDenpyoSplit.checked}"
						disabled="#{pc_Kag00402.propDenpyoSplit.disabled}"
						readonly="#{pc_Kag00402.propDenpyoSplit.readonly}"
						rendered="#{pc_Kag00402.propDenpyoSplit.rendered}">
					</h:selectBooleanCheckbox>
					<h:outputLabel id="textDenpyoSplit" for="htmlDenpyoSplit">
						<h:outputText value="資産分類毎に伝票を分ける">
						</h:outputText>
					</h:outputLabel>
				</TD>
			</TR>
			<TR>
				<TH class="v_a">
					<h:outputText
						styleClass="outputText" 
						id="lblSyoriKbn" 
						style="#{pc_Kag00402.propSyoriKbn.labelStyle}" 
						value="#{pc_Kag00402.propSyoriKbn.labelName}">
					</h:outputText>
				</TH>
				<TD>
					<h:selectBooleanCheckbox
						styleClass="selectBooleanCheckbox"
						id="htmlSyoriKbn"
						style="#{pc_Kag00402.propSyoriKbn.style}"
						value="#{pc_Kag00402.propSyoriKbn.checked}"
						disabled="#{pc_Kag00402.propSyoriKbn.disabled}"
						readonly="#{pc_Kag00402.propSyoriKbn.readonly}"
						rendered="#{pc_Kag00402.propSyoriKbn.rendered}">
					</h:selectBooleanCheckbox>
					<h:outputLabel id="textSyoriKbn" for="htmlSyoriKbn">
						<h:outputText value="チェックのみ（データの妥当性チェックのみ行います）">
						</h:outputText>
					</h:outputLabel>
						
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="900" 
		   
		   style="margin-top:0px; border-collapse: collapse;">
		<TBODY>
			<TR>
				<TD>
					<TABLE width="100%">
						<TR>
							<TD align="right">
								<h:outputText
									styleClass="outputText"
									id="htmlListCnt"
									style="font-size: 8pt"
									value="#{pc_Kag00402.propDataList.listCount}">
									<f:convertNumber pattern="#,##0" />
								</h:outputText>
								<h:outputText
									styleClass="outputText" 
									value="件">
								</h:outputText>
							</TD>
						</TR>
					</TABLE>
				</TD>
			</TR>
			<TR>
				<TD valign="top" height="270">
					<DIV style="height: 270px;overflow-y:scroll" 
						 id="listScroll" 
						 onscroll="setScrollPosition('scroll',this);" 
						 class="listScroll">
						<h:dataTable
							style="table-layout: fixed;"
							columnClasses="columnClass1"
							headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Kag00402.propDataList.rowClasses}" 
							styleClass="meisai_scroll"
							id="htmlDataList"
							var="varlist"
							border="0"
							cellpadding="0"
							cellspacing="0"
							value="#{pc_Kag00402.propDataList.list}">
							<h:column id="colSiwake">
								<f:facet name="header">
									<h:outputText
										id="lblShiwake"
										styleClass="outputText"
										value="　">
									</h:outputText>
								</f:facet>
								<h:selectBooleanCheckbox
									id="htmlSiwake"
									styleClass="selectBooleanCheckbox"
									style="#{varlist.propSiwake.style}"
									value="#{varlist.propSiwake.checked}"
									readonly="#{varlist.propSiwake.readonly}"
									disabled="#{varlist.propSiwake.disabled}">
								</h:selectBooleanCheckbox>
								<f:attribute value="text-align: center" name="style" />
								<f:attribute value="30" name="width" />
							</h:column>
							<h:column id="colSsnKbn">
								<f:facet name="header">
									<h:outputText
										id="lblSsnKbn"
										styleClass="outputText"
										value="資産区分">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlSsnKbn"
									styleClass="outputText"
									style="white-space:nowrap;"
									value="#{varlist.propSsnKbn.stringValue}"
									title="#{varlist.propSsnKbn.stringValue}">
								</h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="colSsnBnrCd">
								<f:facet name="header">
									<h:outputText
										id="lblSsnBnrCd"
										styleClass="outputText"
										value="資産分類コード">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlSsnBnrCd"
									styleClass="outputText"
									style="white-space:nowrap;"
									value="#{varlist.propSsnBnrCd.stringValue}"
									title="#{varlist.propSsnBnrCd.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="colKeijyoTuki">
								<f:facet name="header">
									<h:outputText
										id="lblColKeijyoTuki"
										styleClass="outputText"
										value="計上月">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColKeijyoTuki"
									styleClass="outputText"
									style="white-space:nowrap;"
									value="#{varlist.propKeijyoTuki.integerValue}">
								</h:outputText>
								<f:attribute value="60" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="colSsnBnrName">
								<f:facet name="header">
									<h:outputText
										id="lblSsnBnrName"
										styleClass="outputText"
										value="資産分類名称">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlSsnBnrName"
									styleClass="outputText"
									style="white-space:nowrap;"
									value="#{varlist.propSsnBnrName.stringValue}"
									title="#{varlist.propSsnBnrName.stringValue}">
								</h:outputText>
								<f:attribute value="569" name="width" />
							</h:column>
						</h:dataTable>
					</DIV>
				</TD>
			</TR>
			<TR>
				<TD align="left">
					<hx:panelBox styleClass="panelBox" id="box">
						<hx:jspPanel id="jspPanel">
							<hx:commandExButton
								type="button" value="on"
								styleClass="check" id="checkBtn"
								onclick="return doCheck('htmlDataList', 'htmlSiwake');"></hx:commandExButton>
							<hx:commandExButton
								type="button" value="off"
								styleClass="uncheck" id="uncheckBtn"
								onclick="return doUncheck('htmlDataList', 'htmlSiwake');"></hx:commandExButton>
						</hx:jspPanel>
					</hx:panelBox>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   width="850"
		   style="margin-top:10px;" 
		   class="button_bar">
		<TBODY>
			<TR>
				<TD align="center">
					<hx:commandExButton
						type="submit" 
						value="仕訳作成" 
						styleClass="commandExButton_dat" 
						id="ACTION_EXEC" 
						action="#{pc_Kag00402.doExecAction}" 
						disabled="#{pc_Kag00402.propExec.disabled}" 
						rendered="#{pc_Kag00402.propExec.rendered}" 
						style="#{pc_Kag00402.propExec.style}">
					</hx:commandExButton>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden
	id="scroll"
	value="#{pc_Kag00402.propDataList.scrollPosition}">
</h:inputHidden>

<h:inputHidden
	id="htmlConfRes"
	value="#{pc_Kag00402.propConfRes.integerValue}">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

