<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kac00102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kac01201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<style type="text/css">
<!--
 .setWidth TD {width: 80px; white-space: nowrap;}
 -->
</style>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
	changeScrollPosition('scroll','listscroll');
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_1(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kac00102.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kac00102.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kac00102.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kac00102.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="新規登録"
				styleClass="commandExButton" id="newRegister" action="#{pc_Kac00102.doNewRegisterAction}"></hx:commandExButton><hx:commandExButton type="submit" value="戻る" styleClass="commandExButton" id="returnDisp" 
				action="#{pc_Kac00102.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>

<!--↓content↓-->
<DIV id="content">			

<!-- ↓ここにコンポーネントを配置 -->
<DIV class="column" align="center">

		<TABLE border="0" width="910" style="margin-top:0px">
			<TBODY>
				<TR class="clear_border">
					<TD style="background-color:transparent;"></TD>
				</TR>
				<TR style="${pc_Kac00102.displayIknInfo}">
					<TD align="right">
						<h:outputText styleClass="outputText" id="htmlListCount"
							value="#{pc_Kac00102.propIchiranDataTable.listCount}"></h:outputText>
						<h:outputText styleClass="outputText" id="textCount" value="件"></h:outputText>
					</TD>
				</TR>
				<TR style="${pc_Kac00102.displaySsnInfo}">
					<TD align="right">
						<h:outputText styleClass="outputText" id="htmlSsnInfoListCount"
							value="#{pc_Kac00102.propIchiranDataTableSsnInfo.listCount}"></h:outputText>
						<h:outputText styleClass="outputText" id="textSsnInfoCount" value="件"></h:outputText>
					</TD>
				</TR>
			</TBODY>
		</TABLE>


			<CENTER>
			<TABLE border="0" width="900" style="margin-top:0px; ${pc_Kac00102.displayIknInfo}" cellpadding="0"
				cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" width="100%" class="meisai_scroll" height="20">
							<TBODY>
								<TR>
									<TH width="37" valign="middle"><h:outputText
										styleClass="outputText" id="lblColIkanJokyo" value="移"></h:outputText></TH>
									<TH width="100" style="${pc_Kac00102.displaySsn}"><h:outputText
										styleClass="outputText" id="lblColSsnKbn" value="資産区分"></h:outputText></TH>
									<TH width="148" style="${pc_Kac00102.displaySsn}"><h:outputText
										styleClass="outputText" id="lblColSsnNo" value="資産番号"></h:outputText></TH>
									<TH width="168" style="${pc_Kac00102.displayBhn}"><h:outputText
										styleClass="outputText" id="lblColBhnNo" value="備品番号"></h:outputText></TH>
									<TH width="81" style="${pc_Kac00102.displayBhn}"><h:outputText
										styleClass="outputText" id="lblColEdaNo" value="枝番"></h:outputText></TH>
									<TH width="148" style="${pc_Kac00102.displayLease}"><h:outputText
										styleClass="outputText" id="lblColLeaseNo" value="リース番号"></h:outputText></TH>
									<TH width="101" style="${pc_Kac00102.displayLease}"><h:outputText
										styleClass="outputText" id="lblColLeaseSsnNo" value="リース資産番号"></h:outputText></TH>

									<TH width="165"><h:outputText styleClass="outputText"
										id="lblColSsnName" value="資産名称"></h:outputText></TH>
									<TH width="85"><h:outputText styleClass="outputText"
										id="lblColIkanDate" value="移管日付"></h:outputText></TH>
									<TH width="130"><h:outputText styleClass="outputText"
										id="lblColIkanMaeSetiBsho" value="移管前設置場所"></h:outputText></TH>
									<TH width="131" valign="middle"><h:outputText
										styleClass="outputText" id="lblColIkanMaeKanriBmn"
										value="移管前管理部門"></h:outputText></TH>
									<TH width="38"><h:outputText styleClass="outputText" value="  "
										id="lblColSyosai"></h:outputText></TH>
									<TH width="38"><h:outputText styleClass="outputText"
										value="   " id="lblColHensyu"></h:outputText></TH>
									<TH width="19"><h:outputText styleClass="outputText"
										value="   " id="lblColScroll"></h:outputText></TH>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="900">
						<DIV class="listScroll"
							style="height:463px; OVERFLOW:scroll;overflow-x: hidden;"
							onscroll="setScrollPosition('scroll',this);" id="listScroll"><h:dataTable
							border="0" cellpadding="0" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Kac00102.propIchiranDataTable.rowClasses}"
							styleClass="meisai_scroll" id="htmlIchiranDataTable"
							value="#{pc_Kac00102.propIchiranDataTable.list}" var="varlist"
							rows="#{pc_Kac00102.propIchiranDataTable.rows}"
							first="#{pc_Kac00102.propIchiranDataTable.first}">

							<h:column id="column1">
								<f:facet name="header">
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlColNo"
									value="#{varlist.colIkanJokyo}" style="padding-right: 3px;"></h:outputText>
								<f:attribute value="35" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column2">
								<hx:jspPanel id="jspPanel1">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<!-- 固定資産 -->
											<TR height="20" style="${pc_Kac00102.displaySsn}">
												<TD width="100" style="border-style: none;"><h:outputText
													styleClass="outputText" id="htmlColSsnKbn"
													value="#{varlist.colSsnKbnName}"
													title="#{varlist.colSsnKbnName}"></h:outputText></TD>
											</TR>
											<!-- 備品 -->
											<TR height="20" style="${pc_Kac00102.displayBhn}">
												<TD width="165" style="border-style: none;">
												<DIV
													style="width:165px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText" id="htmlColBhnNo"
													value="#{varlist.colBhnNo}" title="#{varlist.colBhnNo}"></h:outputText></DIV>
												</TD>
											</TR>
											<!-- ﾘｰｽ -->
											<TR height="20" style="${pc_Kac00102.displayLease}">
												<TD width="149" style="border-style: none;">
												<DIV
													style="width:149px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText" id="htmlColLeaseNo"
													value="#{varlist.colLeaseNo}" title="#{varlist.colLeaseNo}"></h:outputText></DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
							</h:column>

							<h:column id="column3">
								<hx:jspPanel id="jspPanel2">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<!-- 固定資産 -->
											<TR height="20" style="${pc_Kac00102.displaySsn}">
												<TD width="149" style="border-style: none;">
												<DIV
													style="width:149px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText" id="htmlColSsnNo"
													value="#{varlist.colSsnNo}" title="#{varlist.colSsnNo}"></h:outputText></DIV>
												</TD>
											</TR>
											<!-- 備品 -->
											<TR height="20" style="${pc_Kac00102.displayBhn}">
												<TD width="80"
													style="border-style: none;text-align: center; padding-right: 3pt;">
												<h:outputText styleClass="outputText" id="htmlColBhnEda"
													value="#{varlist.colEdaNo}"></h:outputText></TD>
											</TR>
											<!-- ﾘｰｽ -->
											<TR height="20" style="${pc_Kac00102.displayLease}">
												<TD width="100"
													style="border-style: none;text-align: center; padding-right: 3pt;"><h:outputText
													styleClass="outputText" id="htmlColLeaseSsnNo"
													value="#{varlist.colLeaseSsnNo}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
							</h:column>

							<h:column id="column4">
								<hx:jspPanel id="jspPanel3">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<TR>
												<TD style="border-style:none;">
												<DIV
													style="width:165px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText" id="htmlSsnName"
													value="#{varlist.colSsnName.stringValue}"
													style="white-space:nowrap;"
													title="#{varlist.colSsnName.stringValue}"></h:outputText></DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="165" name="width" />
							</h:column>

							<h:column id="column5">
								<f:facet name="header">
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlIkanDate"
									value="#{varlist.colIkanDate}"></h:outputText>
								<f:attribute value="85" name="width" />
								<f:attribute value="text-align:center" name="style" />
							</h:column>
							<h:column id="column6">
								<hx:jspPanel id="jspPanel4">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<TR>
												<TD style="border-style:none;">
												<DIV
													style="width:130px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText"
													id="htmlIkanMaeSetiBasho"
													value="#{varlist.colSetchBashoName.stringValue}"
													title="#{varlist.colSetchBashoName.stringValue}"></h:outputText>
												</DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="130" name="width" />
							</h:column>

							<h:column id="column7">
								<hx:jspPanel id="jspPanel5">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<TR>
												<TD style="border-style:none;">
												<DIV
													style="width:130px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText"
													id="htmlIkanMaeKanriBmn"
													value="#{varlist.colKanriBmnName.stringValue}"
													title="#{varlist.colKanriBmnName.stringValue}"></h:outputText>
												</DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="130" name="width" />
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									value="詳細" id="detail"
									disabled="#{varlist.colDetailButton.disabled}"
									rendered="#{varlist.colDetailButton.rendered}"
									action="#{pc_Kac00102.doDetailAction}"></hx:commandExButton>
								<f:attribute value="38" name="width" />
							</h:column>
							<h:column id="column9">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									value="編集" id="edit"
									rendered="#{varlist.colEditButton.rendered}"
									disabled="#{varlist.colEditButton.disabled}"
									action="#{pc_Kac00102.doEditAction}"></hx:commandExButton>
								<f:attribute value="38" name="width" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↓ここから資産情報の一覧を配置 HEX-1684 -->
			<TABLE border="0" width="900" style="margin-top:0px; ${pc_Kac00102.displaySsnInfo}" cellpadding="0"
				cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" width="100%" class="meisai_scroll" height="20">
							<TBODY>
								<TR>
									<TH width="100" style="${pc_Kac00102.displaySsn}"><h:outputText
										styleClass="outputText" id="lblColSsnKbnSsnInfo" value="資産区分"></h:outputText></TH>
									<TH width="148" style="${pc_Kac00102.displaySsn}"><h:outputText
										styleClass="outputText" id="lblColSsnNoSsnInfo" value="資産番号"></h:outputText></TH>
									<TH width="168" style="${pc_Kac00102.displayBhn}"><h:outputText
										styleClass="outputText" id="lblColBhnNoSsnInfo" value="備品番号"></h:outputText></TH>
									<TH width="81" style="${pc_Kac00102.displayBhn}"><h:outputText
										styleClass="outputText" id="lblColEdaNoSsnInfo" value="枝番"></h:outputText></TH>
									<TH width="148" style="${pc_Kac00102.displayLease}"><h:outputText
										styleClass="outputText" id="lblColLeaseNoSsnInfo" value="リース番号"></h:outputText></TH>
									<TH width="101" style="${pc_Kac00102.displayLease}"><h:outputText
										styleClass="outputText" id="lblColLeaseSsnNoSsnInfo" value="リース資産番号"></h:outputText></TH>
									<TH width="127"><h:outputText styleClass="outputText"
										id="lblColSsnNameSsnInfo" value="資産名称"></h:outputText></TH>
									<TH width="85"><h:outputText styleClass="outputText"
										id="lblColShutokuDateSsnInfo" value="取得日付"></h:outputText></TH>
									<TH width="100"><h:outputText styleClass="outputText"
										id="lblColShutokuKagakuSsnInfo" value="取得価額"></h:outputText></TH>
									<TH width="130" valign="middle"><h:outputText
										styleClass="outputText" id="lblColSsnBunruiNameSsnInfo" value="資産分類名称"></h:outputText></TH>
									<TH width="106"><h:outputText styleClass="outputText"
										id="lblSetchiBashoNameSsnInfo" value="設置場所名称"></h:outputText></TH>
									<TH width="38"><h:outputText styleClass="outputText"
										id="lblColRegisterSsnInfo" value="   "></h:outputText></TH>
									<TH width="38"><h:outputText styleClass="outputText"
										id="lblColIknSsnInfo" value="   "></h:outputText></TH>
									<TH width="19"><h:outputText styleClass="outputText"
										id="lblColScrollSsnInfo" value="   "></h:outputText></TH>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="900">
						<DIV  class="listScroll"
							style="height:463px; OVERFLOW:scroll;overflow-x: hidden;" id="listScroll">
							<h:dataTable border="0" cellspacing="0"
							columnClasses="columnClass1" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Kac00102.propIchiranDataTableSsnInfo.rowClasses}"
							styleClass="meisai_scroll" id="htmlIchiranDataTableSsnInfo"
							cellpadding="0"
							first="#{pc_Kac00102.propIchiranDataTableSsnInfo.first}"
							rows="#{pc_Kac00102.propIchiranDataTableSsnInfo.rows}"
							value="#{pc_Kac00102.propIchiranDataTableSsnInfo.list}"
							var="varlist">
							
							<h:column id="column10">
							<hx:jspPanel id="jspPanel6">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<!-- 固定資産 -->
											<TR height="20" style="${pc_Kac00102.displaySsn}">
												<TD width="100" style="border-style: none;"><h:outputText
													styleClass="outputText" id="htmlColSsnKbnSsnInfo" title="#{varlist.colSsnKbnName}" value="#{varlist.colSsnKbnName}"></h:outputText></TD>
											</TR>
											<!-- 備品 -->
											<TR height="20" style="${pc_Kac00102.displayBhn}">
												<TD width="168" style="border-style: none;">
												<DIV
													style="width:168px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText"
													id="htmlColBhnNoSsnInfo" title="#{varlist.colBhnNo}" value="#{varlist.colBhnNo}"></h:outputText></DIV>
												</TD>
											</TR>
											<!-- ﾘｰｽ -->
											<TR height="20" style="${pc_Kac00102.displayLease}">
												<TD width="148" style="border-style: none;">
												<DIV
													style="width:148px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText"
													id="htmlColLeaseNoSsnInfo" title="#{varlist.colLeaseNo}" value="#{varlist.colLeaseNo}"></h:outputText></DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
							</h:column>
							
							<h:column id="column11">
								<hx:jspPanel id="jspPanel7">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<!-- 固定資産 -->
											<TR height="20" style="${pc_Kac00102.displaySsn}">
												<TD width="148" style="border-style: none;">
												<DIV
													style="width:148px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText" id="htmlColSsnNoSsnInfo" title="#{varlist.colSsnNo}" value="#{varlist.colSsnNo}"></h:outputText></DIV>
												</TD>
											</TR>
											<!-- 備品 -->
											<TR height="20" style="${pc_Kac00102.displayBhn}">
												<TD width="76"
													style="border-style: none;text-align: center; padding-right: 3pt;">
												<h:outputText styleClass="outputText"
													id="htmlColBhnEdaSsnInfo" value="#{varlist.colEdaNo}"></h:outputText></TD>
											</TR>
											<!-- ﾘｰｽ -->
											<TR height="20" style="${pc_Kac00102.displayLease}">
												<TD width="96"
													style="border-style: none;text-align: center; padding-right: 3pt;"><h:outputText
													styleClass="outputText" id="htmlColLeaseSsnNoSsnInfo" value="#{varlist.colLeaseSsnNo}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
							</h:column>
							
							<h:column id="column12">
								<hx:jspPanel id="jspPanel8">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<TR>
												<TD style="border-style:none;">
												<DIV 
													style="width:127px;white-space:nowrap;overflow:hidden;display:block;">
												<h:outputText styleClass="outputText" id="htmlSsnNameSsnInfo"
													title="#{varlist.colSsnName.stringValue}" value="#{varlist.colSsnName.stringValue}" style="white-space:nowrap;"></h:outputText>
												</DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="127" name="width" />
							</h:column>
							
							<h:column id="column13">
								<f:facet name="header">
								</f:facet>							
								<f:attribute value="85" name="width" />
								<f:attribute value="text-align:center" name="style" />
								<h:outputText styleClass="outputText"
									id="htmlShutokuDateSsnInfo" value="#{varlist.colShutokuDateSsnInfo}"></h:outputText>
							</h:column>
							
							<h:column id="column14">
								<f:facet name="header">
								</f:facet>							
								<f:attribute value="100" name="width" />
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="text-align: right" name="style" />
								<h:outputText styleClass="outputText" style="padding-right: 3px"
									id="htmlShutokuKagakuSsnInfo" value="#{varlist.colShutokuKagakuSsnInfo}">
									<f:convertNumber pattern="###,###,###,###;###,###,###,###" />
								</h:outputText>
							</h:column>
							
							<h:column id="column15">
								<hx:jspPanel id="jspPanel9">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<TR>
												<TD style="border-style:none;">
												<DIV style="width:130px;white-space:nowrap;overflow:hidden;display:block;">												
												<h:outputText styleClass="outputText"
													id="htmlSsnBunruiNameSsnInfo"
													title="#{varlist.colSsnBunruiNameSsnInfo.stringValue}" value="#{varlist.colSsnBunruiNameSsnInfo.stringValue}"></h:outputText></DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="130" name="width" />
							</h:column>
							
							<h:column id="column16">
								<hx:jspPanel id="jspPanel10">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										style="border-bottom-style:none;border-top-style:none;">
										<TBODY>
											<TR>
												<TD style="border-style:none;">
												<DIV style="width:106px;white-space:nowrap;overflow:hidden;display:block;">												
												<h:outputText styleClass="outputText"
													id="htmlSetchiBashoNameSsnInfo" title="#{varlist.colSetchiBashoNameSsnInfo.stringValue}" value="#{varlist.colSetchiBashoNameSsnInfo.stringValue}"></h:outputText></DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</hx:jspPanel>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="106" name="width" />
							</h:column>
							
							<h:column id="column18">
								<f:facet name="header">
								</f:facet>								
								<f:attribute value="38" name="width" />
								<f:attribute value="text-align:center" name="style" />
								<hx:commandExButton type="submit" value="登録"
									styleClass="commandExButton" id="registerSsnInfo" 
									action="#{pc_Kac00102.doRowRegisterAction}" 
									rendered="#{varlist.colRegisterButton.rendered}" 
									disabled="#{varlist.colRegisterButton.disabled}"></hx:commandExButton>
							</h:column>

							<h:column id="column17">
								<f:facet name="header">
								</f:facet>								
								<f:attribute value="38" name="width" />
								<f:attribute value="text-align:center" name="style" />
								<hx:commandExButton type="submit" value="移管"
									styleClass="commandExButton" id="editSsnInfo"
									rendered="#{varlist.colEditButton.rendered}"
									disabled="#{varlist.colEditButton.disabled}"
									action="#{pc_Kac00102.doEditAction}"></hx:commandExButton>
							</h:column>
							
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>

</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->

<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

			<h:inputHidden
				value="#{pc_Kac00102.propIchiranDataTable.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

