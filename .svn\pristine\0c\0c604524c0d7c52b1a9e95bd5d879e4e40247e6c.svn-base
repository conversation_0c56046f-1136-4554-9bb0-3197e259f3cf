<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kaz00101T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>管財デフォルト設定(その他)</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

window.attachEvent("onload", attachFormatNumber);
//window.attachEvent("onload", loadFunc);
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kaz00101T05.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kaz00101T05.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kaz00101T05.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kaz00101T05.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0" width="880"
				height="500" style="margin-top: 10px;">
				<TBODY>
					<TR>
						<TD align="left" height="27"><hx:commandExButton type="submit"
							style="width: 120px" value="自動採番" styleClass="tab_head_off"
							id="tabAuto" tabindex="1"
							action="#{pc_Kaz00101T05.doTabAutoAction}"></hx:commandExButton><hx:commandExButton type="submit" style="width: 120px"
							value="番号桁数" styleClass="tab_head_off" id="tabNoKeta"
							action="#{pc_Kaz00101T05.doTabNoKetaAction}" tabindex="2"></hx:commandExButton><hx:commandExButton type="submit" style="width: 120px"
							value="レベル桁数１" styleClass="tab_head_off" id="tabLevel1"
							action="#{pc_Kaz00101T05.doTabLevel1Action}" tabindex="3"></hx:commandExButton><hx:commandExButton type="submit" style="width: 120px"
							value="レベル桁数２" styleClass="tab_head_off" id="tabLevel2"
							action="#{pc_Kaz00101T05.doTabLevel2Action}" tabindex="4"></hx:commandExButton><hx:commandExButton type="submit" style="width: 120px" value="その他"
							styleClass="tab_head_on" id="tabOther" tabindex="5"></hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TD height="100%" align="left" valign="top">
						<TABLE border="1" cellpadding="20" cellspacing="0" height="500"
							width="880" class="tab_body">
							<TBODY>
								<TR>
									<TD align="center" valign="top">
									<TABLE width="860" border="0">
										<TBODY>
											<tr><TH><h:outputText styleClass="outputText" value="　"></h:outputText></TH></tr>										
											<TR>
												<TH align="left"><h:outputText styleClass="outputText"
													value="備品"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="860">
										<TBODY>
											<TR>
												<TH class="v_b" width="180" colspan="1">
												<h:outputText styleClass="outputText" id="lblEdaShoki"
													style="#{pc_Kaz00101T05.propEdaShoki.labelStyle}"
													value="#{pc_Kaz00101T05.propEdaShoki.labelName}"></h:outputText></TH>
												<TD colspan="1" width="680">
												<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio" id="htmlEdaShoki"
													disabled="#{pc_Kaz00101T05.propEdaShoki.disabled}"
													rendered="#{pc_Kaz00101T05.propEdaShoki.rendered}"
													style="#{pc_Kaz00101T05.propEdaShoki.style}"
													value="#{pc_Kaz00101T05.propEdaShoki.stringValue}">
													<f:selectItems value="#{pc_Kaz00101T05.propEdaShoki.list}" />
												</h:selectOneRadio></TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE width="860" border="0">
										<TBODY>
											<tr><TH><h:outputText styleClass="outputText" value="　"></h:outputText></TH></tr>										
											<TR>
												<TH align="left"><h:outputText styleClass="outputText"
													value="減価償却"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="860">
										<TBODY>
											<TR>
												<TH class="v_b" width="180" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propIkatsuYosan.labelName}"
													style="#{pc_Kaz00101T05.propIkatsuYosan.labelStyle}"
													id="lblIkatsuYosan">
												</h:outputText></TH>
												<TD colspan="1" width="680"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propIkatsuYosan.stringValue}"
													id="htmlIkatsuYosan" tabindex="6">
													<f:selectItems value="#{pc_Kaz00101T05.propIkatsuYosan.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propZanzonMarume.labelName}"
													style="#{pc_Kaz00101T05.propZanzonMarume.labelStyle}">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propZanzonMarume.stringValue}"
													id="htmlZanzonMarume" tabindex="7">
													<f:selectItems value="#{pc_Kaz00101T05.propZanzonMarume.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propGenkaMarume.labelName}"
													style="#{pc_Kaz00101T05.propGenkaMarume.labelStyle}"
													id="lblGenkaMarume">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propGenkaMarume.stringValue}"
													id="htmlGenkaMarume" tabindex="8">
													<f:selectItems value="#{pc_Kaz00101T05.propGenkaMarume.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propKyokanMarume.labelName}"
													style="#{pc_Kaz00101T05.propKyokanMarume.labelStyle}"
													id="lblKyokanMarume">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propKyokanMarume.stringValue}"
													id="htmlKyokanMarume" tabindex="9">
													<f:selectItems
														value="#{pc_Kaz00101T05.propKyokanMarume.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR style="${pc_Kaz00101T05.trRevo}">
											<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propSsnBnrSyukeiKeta.labelName}"
													style="#{pc_Kaz00101T05.propSsnBnrSyukeiKeta.labelStyle}"
													id="lblSsnBnrSyukeiKeta">
												</h:outputText></TH>
												<TD colspan="1"><h:inputText 
													styleClass="inputText" size="1"
													id="htmlSsnBnrSyukeiKeta" 
													style="padding-right:3px; text-align:right;#{pc_Kaz00101T05.propSsnBnrSyukeiKeta.style}"
													value="#{pc_Kaz00101T05.propSsnBnrSyukeiKeta.stringValue}" tabindex="10"></h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE width="860" border="0">
										<TBODY>
											<tr><TH><h:outputText styleClass="outputText" value="　"></h:outputText></TH></tr>
											<TR>
												<TH align="left"><h:outputText styleClass="outputText"
													value="調達"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="860">
										<TBODY>
											<TR>
												<TH class="v_b" width="180" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propChotSyoki.labelName}"
													style="#{pc_Kaz00101T05.propChotSyoki.labelStyle}"
													id="lblChotSyoki">
												</h:outputText></TH>
												<TD colspan="1" width="680"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propChotSyoki.stringValue}"
													id="htmlChotSyoki" tabindex="10">
													<f:selectItems value="#{pc_Kaz00101T05.propChotSyoki.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propChotShinsei.labelName}"
													style="#{pc_Kaz00101T05.propChotShinsei.labelStyle}"
													id="lblChotShinsei">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propChotShinsei.stringValue}"
													id="htmlChotSinsei" tabindex="11">
													<f:selectItems value="#{pc_Kaz00101T05.propChotShinsei.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propChotKensyu.labelName}"
													style="#{pc_Kaz00101T05.propChotKensyu.labelStyle}"
													id="lblChotKensyu">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propChotKensyu.stringValue}"
													id="htmlChotKensyu" tabindex="12">
													<f:selectItems
														value="#{pc_Kaz00101T05.propChotKensyu.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propChotTax.labelName}"
													style="#{pc_Kaz00101T05.propChotTax.labelStyle}"
													id="lblChotTax">
												</h:outputText></TH>
												<TD colspan="1"><h:inputText
													styleClass="inputText" size="3" id="htmlChotTax"
													style="padding-right:3px; text-align:right;#{pc_Kaz00101T05.propChotTax.style}"
													value="#{pc_Kaz00101T05.propChotTax.stringValue}" tabindex="13"></h:inputText><h:outputText
													styleClass="outputText" value="%"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE width="860" border="0">
										<TBODY>
											<tr><TH><h:outputText styleClass="outputText" value="　"></h:outputText></TH></tr>
											<TR>
												<TH align="left"><h:outputText styleClass="outputText"
													value="仕訳"></h:outputText></TH>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
										width="860">
										<TBODY>
											<TR>
												<TH class="v_b" width="180" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propSiwakeHoho.labelName}"
													style="#{pc_Kaz00101T05.propSiwakeHoho.labelStyle}"
													id="lblSiwakeHoho">
												</h:outputText></TH>
												<TD colspan="1" width="680"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propSiwakeHoho.stringValue}"
													id="htmlSiwakeHoho" tabindex="14">
													<f:selectItems value="#{pc_Kaz00101T05.propSiwakeHoho.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propBuanKbn.labelName}"
													style="#{pc_Kaz00101T05.propBuanKbn.labelStyle}"
													id="lblBuanKbn">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propBuanKbn.stringValue}"
													id="htmlBuanKbn" tabindex="15">
													<f:selectItems value="#{pc_Kaz00101T05.propBuanKbn.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propKashiKbn.labelName}"
													style="#{pc_Kaz00101T05.propKashiKbn.labelStyle}"
													id="lblKashiKbn">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propKashiKbn.stringValue}"
													id="htmlKashiKbn" tabindex="16">
													<f:selectItems value="#{pc_Kaz00101T05.propKashiKbn.list}" />
												</h:selectOneRadio></TD>
											</TR>
											<TR>
												<TH class="v_b" colspan="1"><h:outputText
													styleClass="outputText"
													value="#{pc_Kaz00101T05.propSiwakeKbn.labelName}"
													style="#{pc_Kaz00101T05.propSiwakeKbn.labelStyle}"
													id="lblSiwakeKbn">
												</h:outputText></TH>
												<TD colspan="1"><h:selectOneRadio
													styleClass="selectOneRadio"
													value="#{pc_Kaz00101T05.propSiwakeKbn.stringValue}"
													id="htmlSiwakeKbn" tabindex="17">
													<f:selectItems value="#{pc_Kaz00101T05.propSiwakeKbn.list}" />
												</h:selectOneRadio></TD>
											</TR>
										</TBODY>
									</TABLE>
										<TABLE width="860" border="0">
										<TBODY>
											<tr><TH><h:outputText styleClass="outputText" value="　"></h:outputText></TH></tr>
											<tr><TH height="20"><h:outputText styleClass="outputText" value="　"></h:outputText></TH></tr>
										</TBODY>
									</TABLE>									
									<TABLE width="860" border="0" style="" class="button_bar">
										<TBODY>
											<TR>
												<TD align="center"><hx:commandExButton type="submit"
													value="更新" styleClass="commandExButton_dat" id="update"
													confirm="#{msg.SY_MSG_0003W}"
													action="#{pc_Kaz00101T05.doUpdateAction}" tabindex="18">
												</hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>



			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="htmlChotTax=ZZ9;ZZ9"id="htmlFormatNumberOption"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

