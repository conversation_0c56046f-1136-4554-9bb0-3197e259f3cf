<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_GHP_K_PAYW" name="過年度納付金割当" prod_id="GH" description="過年度の学生の納付金に対しての情報を管理します。|割当てられた納付金が完納で出学した学生の情報は、過年度の情報に移されます。">
<STATMENT><![CDATA[
GHP_K_PAYW
]]></STATMENT>
<COLUMN id="SYUTGAK_NENDO" name="出学年度" type="number" length="4" lengthDP="0" byteLength="0" description="学生の出学した年度（西暦）です。志願者の場合は入試年度となります。"/><COLUMN id="SYUTGAK_GAKKI_NO" name="出学学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="学生の出学した学期ＮＯです。志願者の場合は入試学期ＮＯとなります。"/><COLUMN id="NENDO" name="学費年度" type="number" length="4" lengthDP="0" byteLength="0" description="業務実行時の年度（西暦）です。"/><COLUMN id="KANRI_NO" name="管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="学生、志願者等を一意に識別する為の番号です。"/><COLUMN id="PAY_CD" name="納付金コード" type="string" length="6" lengthDP="0" byteLength="6" description="納付金コードです。"/><COLUMN id="PATTERN_CD" name="パターンコード" type="string" length="4" lengthDP="0" byteLength="4" description="同一納付金コードで複数の納付金の種類を識別するための番号です。"/><COLUMN id="BUNNO_KBN_CD" name="分納区分コード" type="number" length="2" lengthDP="0" byteLength="0" description="分納方法を管理する為のコードです。"/><COLUMN id="END_DATE" name="完納日付" type="date" length="0" lengthDP="0" byteLength="0" description="納付金の徴収が完了した日付です。"/><COLUMN id="PAY_END_FLG" name="納付完了フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="納付金の徴収が完了したか否かをあらわします。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="PAY_STATUS_KBN" name="納付金状態区分" type="number" length="1" lengthDP="0" byteLength="0" description="学生納付金の現在の状態を表します。"><CODE><CASE value="1" display="通常"/><CASE value="2" display="削除済"/><CASE value="3" display="振替済"/><CASE value="4" display="再割当済"/></CODE></COLUMN><COLUMN id="WARIATE_HOUHOU_KBN" name="割当方法区分" type="number" length="1" lengthDP="0" byteLength="0" description="割当方法を表します。"><CODE><CASE value="1" display="通常割当"/><CASE value="2" display="振替割当"/><CASE value="3" display="残高再割当"/></CODE></COLUMN><COLUMN id="NYUKIN_PRIORITY" name="入金優先順位" type="number" length="2" lengthDP="0" byteLength="0" description="同一学生に対する|納付金間での優先順位です。"/><COLUMN id="GAKU_SHI_KBN" name="学生志願者区分" type="number" length="1" lengthDP="0" byteLength="0" description="学生か志願者かを識別する区分です。"><CODE><CASE value="1" display="学生"/><CASE value="2" display="志願者"/></CODE></COLUMN><COLUMN id="TAINOU_HANTEI_FLG" name="滞納判定フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="未納時に滞納と判断する必要がある納付金かどうかを表します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="IKO_NENDO" name="移行年度" type="number" length="4" lengthDP="0" byteLength="0" description="このデータを作成した年次更新の処理年度が設定されます。"/>
</TABLE>
