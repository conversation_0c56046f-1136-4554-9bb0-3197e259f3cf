<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/PGhz0201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<TITLE>pGhz0201.jsp</TITLE>

<SCRIPT type="text/javascript">
	//検索結果を呼び出し元画面に戻す
    function doSelect() {
		
		try{

			var SEP = "|_|";
			
			var select = 0;
			var option = null;
			var values;
			
			var noSelectMsg = document.getElementById("form1:htmlNoSelectMsgHidden").value;
			var itemCdId = document.getElementById("form1:htmlItemCdIdHidden").value;
			var itemNameId = document.getElementById("form1:htmlItemNmIdHidden").value;
			var listBox = document.getElementById("form1:htmlExcessList");
			
			var itemCd = "";
			var itemName = "";

			if (window.opener && listBox != null) {
				for (i = 0; i < listBox.options.length; i++) {
					option = listBox.options[ i ];

					if (option.selected) {
						values = option.value.split(SEP);
						
						for (j = 0; j < values.length; j++) {
							switch (j) {
								case 0:
									itemCd = values[ j ];
									break;
								case 1:
									itemName = values[ j ];
									break;
							}
						} 

						setValue(itemCdId,   itemCd);
						setValue(itemNameId, itemName);		
						select++;
						break;
					}
				}
					
				if (select > 0) {			
					self.close();
					return true;
				} else {
					alert(noSelectMsg); 
					return false;
				}
	
			} else {
				throw "";
			}
		} catch(e) {
			alert("呼び出し元画面に値を返せません。");
		}
    }
    
	function setValue(targetId, value) {
		var target = window.opener.document.getElementById(targetId);
		if (target.nodeName == "SPAN") {
			target.innerHTML = value;
		} else {
			target.value = value;
		}
	}
    
	//メッセージ出力(OKボタン押下)
	function confirmOk() {

		indirectClick('search');
		
	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY>
        <hx:scriptCollector id="scriptCollector1" preRender="#{pc_PGhz0201.onPageLoadBegin}">

        <h:form styleClass="form" id="form1">
        
			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/childHeader.jsp" />		

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp" 
					action="#{pc_PGhz0201.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PGhz0201.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PGhz0201.screenName}"></h:outputText>
			</div>
			
			<!--↓outer↓-->
			<DIV class="outer">
				<FIELDSET class="fieldset_err">
					<LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>
			
				<!--↓content↓-->
				<DIV class="head_button_area" >　
				<!-- ↓ここに戻る／閉じるボタンを配置 -->
				
				<!-- ↑ここに戻る／閉じるボタンを配置 -->
				</DIV>
				
				<DIV id="content">			
					<DIV class="column" align="center">
			        	<TABLE border="0" cellpadding="0" cellspacing="0" width="600px">
			            	<TBODY>
			                	<TR>
			                    	<TD>
										<TABLE border="0" width="100%" cellpadding="0" cellspacing="0" class="table">
											<TBODY>
												<TR>
			                                    	<TH class="v_a" width="150px">
			                                        	<h:outputText
															styleClass="outputText" 
															id="lblExcessCd"
															value="#{pc_PGhz0201.propItemCd.labelName}"
															style="#{pc_PGhz0201.propItemCd.labelStyle}">
														</h:outputText>
			                                      	</TH>
			                                        <TD nowrap width="*">
			                                        	<h:inputText
															styleClass="inputText" id="htmlItemCd"
															value="#{pc_PGhz0201.propItemCd.value}"
															style="#{pc_PGhz0201.propItemCd.style}" size="12"
															maxlength="#{pc_PGhz0201.propItemCd.maxLength}">
														</h:inputText>
			                                  		</TD>
			                                        <TD nowrap width="180px">
			                                        	<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlItemCdFindType"
															value="#{pc_PGhz0201.propItemCdFindType.value}">
															<f:selectItem itemValue="0" itemLabel="前方一致" />
															<f:selectItem itemValue="1" itemLabel="部分一致" />
														</h:selectOneRadio>
			                                		</TD>
			                             		</TR>
			                                    <TR>
			                                    	<TH class="v_b" nowrap  width="150px">
			                                        	<h:outputText styleClass="outputText" 
			                                        		id="lblExcessNm"
															value="#{pc_PGhz0201.propItemName.labelName}"
															style="#{pc_PGhz0201.propItemName.labelStyle}">
														</h:outputText>
			                                      	</TH>
			                                        <TD width="*">
			                                        	<h:inputText
										styleClass="inputText" id="htmlItemName"
										value="#{pc_PGhz0201.propItemName.value}"
										style="#{pc_PGhz0201.propItemName.style}" size="40"
										maxlength="#{pc_PGhz0201.propItemName.maxLength}">
									</h:inputText>
			                                     	</TD>
			                                        <TD nowrap width="180px">
			                                        	<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlItemNmFindType"
															value="#{pc_PGhz0201.propItemNameFindType.value}">
															<f:selectItem itemValue="0" itemLabel="前方一致" />
															<f:selectItem itemValue="1" itemLabel="部分一致" />
														</h:selectOneRadio>
			                                    	</TD>
			                          			</TR>
			                        		</TBODY>
			                    		</TABLE>
			                      	</TD>
			           			</TR>
			           			<TR>
			           				<TD>
			           				</TD>
			           			</TR>
			           			<TR>
			                    	<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton type="submit" value="検索"
															styleClass="commandExButton_dat" id="search"
															action="#{pc_PGhz0201.doSearchAction}">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton type="submit" value="クリア"
															id="clear" styleClass="commandExButton_etc"
															action="#{pc_PGhz0201.doClearAction}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
			                   	</TR>
			                    <TR>
			                    	<TD height="20px">
										<HR noshade width="100%" align="center">
									</TD>
			                	</TR>
			                	<TR>
			                		<TD align="right">
				                		<h:outputText styleClass="outputText" 
				                			id="text1" value="#{pc_PGhz0201.propDataCount.integerValue}">
				                		<f:convertNumber /></h:outputText>
				                		<h:outputText styleClass="outputText" 
				                			id="text2" value="件">
				                		</h:outputText>
			                		</TD>
			                	</TR>
			                    <TR>
			                    	<TD>
										<TABLE width="99%" border="0" cellpadding="3" cellspacing="0" class="list_table">
											<TBODY>
												<TR>
													<TH>
														<h:outputText styleClass="outputText" id="lblExcessCdList"
															value="#{pc_PGhz0201.propItemCd.name}">
														</h:outputText>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														<h:outputText styleClass="outputText" 
															id="lblExcessNameList"
															value="#{pc_PGhz0201.propItemName.name}">
														</h:outputText>
													</TH>
												</TR>
												<TR>
													<TD>
														<h:selectOneListbox styleClass="selectOneListbox" 
															id="htmlExcessList"
															value="#{pc_PGhz0201.propItemList.stringValue}" 
															size="16"
															style="width: 100%" 
															ondblclick="return doSelect();">
															<f:selectItems value="#{pc_PGhz0201.propItemList.list}" />
														</h:selectOneListbox>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
			                   	</TR>
								<TR>
									<TD>
									</TD>
								</TR>
			                    <TR>
			                    	<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
			                                    		<hx:commandExButton type="submit" 
			                                    			value="選　択" 
			                                    			onclick="return doSelect();"
															styleClass="commandExButton_dat" 
															id="select">
														</hx:commandExButton>&nbsp;
													</TD>
												</TR>
											</TBODY>
										</TABLE>			                                    	
									</TD>
			                 	</TR>
			        		</TBODY>
			     		</TABLE>
					</DIV>
				</DIV>
				<!--↑content↑--> 
			</DIV>
			<!--↑outer↑-->

			<h:inputHidden id="htmlItemCdIdHidden" value="#{pc_PGhz0201.propItemCdIdHidden.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlItemNmIdHidden" value="#{pc_PGhz0201.propItemNmIdHidden.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlNoSelectMsgHidden" value="#{pc_PGhz0201.propNoSelectMsgHidden.value}"></h:inputHidden>
			<h:inputHidden value="#{pc_PGhz0201.propExecutableSearch.integerValue}" id="htmlExecutableSearch"></h:inputHidden>
                
			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/childFooter.jsp" />

        </h:form>
    </hx:scriptCollector></BODY>
    
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
