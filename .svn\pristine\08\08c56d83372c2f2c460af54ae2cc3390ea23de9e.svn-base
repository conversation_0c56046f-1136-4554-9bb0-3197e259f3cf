var oEditorFrame = null;
var oToolbars = null;
var editorFrame_height = 0;
var toolbar_height = 0;

var EditPropElement = null;

var oTargetElement = null;	//コンテキストメニューの処理対象エレメント


var newItem = null;	//新規追加アイテムタイプ
var newElm = null;		//新規追加エレメント
var dragItems = null;	//ドラッグ対象エレメント

var sDragFromUniqueID = null;

var dropOffsetX = 0;
var dropOffsetY = 0;
var dropTarget = null;
var dragElm = null;

//データディクショナリXMLのROOT
var ddXML_ROOT = null;
var dbms = null;

var startX=0;
var startY=0;

var Elm = null;

var formatElm;

var history = new Array(10);
var countHistory = -1;
var current = -1;
var fMove = false;

var LINE_MARGIN = 0.0;
var MAX_PRINT_LENGTH = 256;


//function Init(dd,thisFrame,folderFrame,columnFrame){
function Init(thisFrame,folderFrame,columnFrame,toolbars){

	oToolbars = window.parent.document.getElementById(toolbars);
	toolbar_height = oToolbars.offsetHeight;

	oEditorFrame = window.parent.document.getElementById(thisFrame);
	editorFrame_height = oEditorFrame.style.pixelHeight;

	var dict = document.getElementById("DD");
//	alert(dict.innerHTML);
	var root = dict.selectSingleNode("/");


//	var ddXML = window.parent.document.getElementById(dd);	
//	ddXML.XMLDocument.loadXML(root.xml);
//	ddXML_ROOT = ddXML.selectSingleNode("//");
	ddXML_ROOT = dict.selectSingleNode("//");
	dbms = ddXML_ROOT.getAttribute("DBMS")

	if(!document.getElementById("QUERYFIELDS")){

		var html = "";
		html =  '<XML id="QUERYFIELDS">';
		var query = document.createElement(html);
		document.body.insertAdjacentElement("beforeEnd",query);
		var XMLDoc = query.XMLDocument;
		var newElm = XMLDoc.createElement("FIELDS");
		XMLDoc.documentElement = newElm;
		var root = XMLDoc.documentElement;
//		alert(root.xml);
//		alert(root.tagName);
		
//		html =  '<XML id="QUERYFIELDS"><FIELDS><aa>ss</aa><FIELDS/><XML/>';
//		document.body.innerHTML = html + document.body.innerHTML

//		var query = document.getElementById("QUERYFIELDS")
//		alert(query.tagName);
//		root = query.selectSingleNode("/");
//		alert(root);
//		alert(root.xml);

	}
	
	//PAGE DIRECTION
	var sSize = "A4";
	
	var sDirection = "portrait";

	var page = document.getElementById("PAGE");
	if(page == null){
		var html = "";
		html =  '<DIV id="PAGE" class="'+sSize+sDirection+'" UNSELECTABLE="on">';

		page = document.createElement(html);
		page.contentEditable = true;
		page.SVF_SIZE = sSize;
		page.SVF_DIRECTION = sDirection;
		
		document.body.insertAdjacentElement("beforeEnd",page);
		
//		html =  '<DIV id="SUBFORM_1" name="サブフォーム1" class="subform" style="top : 100px; left: 0px; width: '+page.clientWidth+'px; height :'+(page.clientHeight - 200)+'">';
//		html =  '<DIV id="SUBFORM_1" name="サブフォーム1" class="subform" style="top : 100px; left: 20px; width: '+(page.clientWidth-40)+'px; height :'+(page.clientHeight - 200)+'">';
		html =  '<DIV id="SUBFORM_1" name="サブフォーム1" class="subform" >';
		
		var subform = document.createElement(html);
		
		subform.style.top = "100px";
		subform.style.left = "0px";
//		subform.style.width = (page.clientWidth-40)+"px";
		subform.style.width = page.clientWidth+"px";
		subform.style.height = (page.clientHeight - 200)+"px";

		subform.SVF_DIRECTION = "VERTICAL";
		subform.style.borderTopWidth = "0px";
		subform.style.borderTopStyle = "solid";
		subform.style.borderLeftWidth = "0px";
		subform.style.borderLeftStyle = "solid";
		subform.style.borderRightWidth = "0px";
		subform.style.borderRightStyle = "solid";
		subform.style.borderBottomWidth = "0px";
		subform.style.borderBottomStyle = "solid";
		subform.title =subform.name;
		
		page.appendChild(subform);
		
	}

	document.execCommand("MultipleSelection");
	
	initItems();

	Resume();
	
	var folder = window.parent.document.getElementById(folderFrame);
//	alert(folder);
//	folder.contentWindow.Init(dd,columnFrame);
//	folder.contentWindow.Init(dict,columnFrame);
	
	
}

function initItems(){

	document.onselectionchange = SelectionChange;

	document.onmousedown = MouseDown;
	document.onmousemove = MouseMove;
	document.onmouseup = MouseUp;

	document.onkeydown = Keydown;
	document.onkeyup = Keyup;
	
	document.oncontrolselect = Controlselect;
	
	document.execCommand("LiveResize");
	document.execCommand("2D-Position",false,true);
	
	document.body.oncontextmenu = Contextmenu;

	document.body.onresizestart = Resizestart;
	document.body.ondragenter = Dragenter;
	document.body.ondragleave = Dragleave;
	document.body.ondragstart = DragStart;
	document.body.ondragend = DragEnd;
	document.body.ondrop = Drop;

	document.body.onmove = Move;
	
	document.body.onbeforepaste = Cancel;
	document.body.onpaste = Cancel;

	document.body.ondblclick  = Dblclick;

	document.body.onbeforeeditfocus = Beforeeditfocus;
	document.body.onbeforeactivate = Beforeactivate;

	document.body.onresizeend = Resizeend;
	
//	document.getElementById("CLIPBRD").innerHTML = "";

	var elms = document.body.getElementsByTagName("DIV");
	for (var i=0 ; i <elms.length; i++){
		switch (elms(i).className){
		case "textfield":
			elms(i).onresize = Resize;
			elms(i).onresizestart = Cancel;
			elms(i).onblur = Blur;
			elms(i).onfocus = Focus;
			break;

		case "hline":
		case "vline":
			elms(i).onresize = Resize;
			break;

		case "box":
			elms(i).onresize = Resize;
//			elms(i).contentEditable = false;

			break;

		case "datafield":
		case "calcfield":
		case "field":
			elms(i).onresize = Resize;
			elms(i).onbeforeactivate = Cancel;
			elms(i).onbeforeeditfocus = Cancel;
			
			//書式変更に伴い、旧仕様formのコンバートロジック 　-->
			if(elms(i).SVF_DATA_TYPE == "date"){
				switch (elms(i).SVF_FORMAT){
				case "YYYY/MM/DD":
					elms(i).SVF_FORMAT = "yyyy/MM/dd";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				case "元号 Y年 M月 D日":
					elms(i).SVF_FORMAT = "元号#Y年#M月#d日";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				case "元号YY年MM月DD日":
					elms(i).SVF_FORMAT = "元号YY年MM月dd日";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				case " D-MMM-YY":
					elms(i).SVF_FORMAT = "#d-MMM-yy";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				case "DD-MMM-YY":
					elms(i).SVF_FORMAT = "dd-MMM-yy";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				}
			}else if(elms(i).SVF_DATA_TYPE == "time"){
				switch (elms(i).SVF_FORMAT){
				case "HH:MM:SS":
					elms(i).SVF_FORMAT = "hh:mm:ss";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				case "HH":
					elms(i).SVF_FORMAT = "ｈ";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				case "MM":
					elms(i).SVF_FORMAT = "ｍ";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				case "SS":
					elms(i).SVF_FORMAT = "ｓ";
					var oSpans = elms(i).getElementsByTagName("SPAN");
					for(var j=0; j<oSpans.length; j++){
						if(oSpans(j).className == "FORMAT"){
							oSpans(j).innerText = elms(i).SVF_FORMAT;
						}
					}
					break;
				}
			}
			//<-- 書式変更に伴い、旧仕様formのコンバートロジック 
			
			break;

		case "HEADER":
		case "DETAIL":
		case "SUM":
		case "TOTAL":
		case "subform":
			elms(i).onresize = Resize;
			elms(i).onfocus = Cancel;
			
		default:
			break;
		}
	}

	elms = document.body.getElementsByTagName("TD");
	for (var i=0 ; i <elms.length; i++){
		switch (elms(i).className){
		case "hcell":
		case "dcell":
		case "scell":
		case "tcell":
			elms(i).onresize = Resize;
			break;
		case "row":
		case "column":
			elms(i).onresize = Resize;
			break;
		default:
			break;
		}
	}

	elms = document.body.getElementsByTagName("SPAN");
	for (var i=0 ; i <elms.length; i++){
		switch (elms(i).className){
		case "columnspacer":
		case "rowspacer":
			elms(i).onresize = Resize;
			elms(i).onresizeend = Resizeend;
			elms(i).contentEditable = false;
			break;
		case "PREFIX":
		case "FORMAT":
		case "SUFFIX":
			elms(i).onmousedown = Cancel;
			break;
		default:
			break;
		}
	}

	elms = document.body.getElementsByTagName("TABLE");
	for (var i=0 ; i <elms.length; i++){
		switch (elms(i).className){
		case "spreadsheet":
			elms(i).onresize = Resize;
			break;
		default:
			break;
		}
	}
	
	elms = document.body.getElementsByTagName("A");
	for (var i=0 ; i <elms.length; i++){
		elms(i).oncontrolselect = Cancel;
	}

	
}

function getHeader(){

	var headerList = new Array();
	var elms = document.body.getElementsByTagName("DIV");
	var cnt = 0
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "HEADER"){
			headerList[cnt] = elms(i);
			cnt++;
		}
	}
	var elms = document.body.getElementsByTagName("TR");
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "TableHead"){
			headerList[cnt] = elms(i);
			cnt++;
		}
	}	
//	return headerList.sort(compareByName);
	return headerList.sort(compareByPosition);
}

function getSum(){

	var sumList = new Array();
	var elms = document.body.getElementsByTagName("DIV");
	var cnt = 0
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "SUM"){
			sumList[cnt] = elms(i);
			cnt++;
		}
	}
	var elms = document.body.getElementsByTagName("TR");
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "TableSum"){
			sumList[cnt] = elms(i);
			cnt++;
		}
	}	
//	return sumList.sort(compareByName);
	return sumList.sort(compareByPosition);
}

function getDataField(){

	var fieldList = new Array();
	var elms = document.body.getElementsByTagName("DIV");
	var cnt = 0
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "datafield"){
			fieldList[cnt] = elms(i);
			cnt++;
		}
	}
//	return sumList.sort(compareByName);
	return fieldList.sort(compareByPosition);

}

function getField(){

	var fieldList = new Array();
	var elms = document.body.getElementsByTagName("DIV");
	var cnt = 0
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "datafield" || elms(i).className == "field" || elms(i).className == "calcfield" ){
			fieldList[cnt] = elms(i);
			cnt++;
		}
	}
//	return sumList.sort(compareByName);
	return fieldList.sort(compareByPosition);

}

function compareByName(elm1,elm2){
	if(elm1.name < elm2.name){
		return -1;
	}
	if(elm1.name == elm2.name){
		return 0;
	}
	return 1;
}

function compareByPosition(elm1,elm2){

	
	var elm1Top = getAbsolutePositionTop(elm1);
	var elm1Left = getAbsolutePositionLeft(elm1);
	var elm2Top = getAbsolutePositionTop(elm2);
	var elm2Left = getAbsolutePositionLeft(elm2);
	
	if(elm1Top < elm2Top){
		return -1;
	}
	if(elm2Top < elm1Top){
		return 1;
	}
	if(elm1Left < elm2Left){
		return -1;
	}
	if(elm2Left < elm1Left){
		return 1;
	}
	
	return 0;

}

function getAbsolutePositionTop(field){

	var top = 0;
	for(var elm = field; elm.id != "PAGE"; elm = elm.parentElement){
		if(elm.tagName != "TR" && elm.tagName != "TBODY"){
			top += elm.offsetTop;
		}
	}

	return top;
}

function getAbsolutePositionLeft(field){
	var left = 0;
	for(var elm = field; elm.id != "PAGE"; elm = elm.parentElement){
		left += elm.offsetLeft;
	}

	return left;
}
/*
function fontSize(size){

	var bChange = false;
	var Items = getSelectedItems();
	for(var i = 0; i < Items.length; i++){
		var elm = Items(i);
		if (elm.className == "textfield" || elm.className == "datafield"){
			if(elm.style.fontSize != size){
				elm.style.fontSize = size;
				bChange = true;
			}
		}
	}
	if (bChange){
		Resume();
	}
}
*/
function fontSize(size){

//	alert(size.selectedIndex);
//	alert(size.options(size.selectedIndex).text);

	var bChange = false;
	var Items = getSelectedItems();
	for(var i = 0; i < Items.length; i++){
		var elm = Items(i);
		if (elm.className == "textfield" || elm.className == "datafield" || elm.className == "calcfield" || elm.className == "field"){
			if(elm.style.fontSize != size.value){
				elm.style.fontSize = size.value;
				elm.SVF_FONT_SIZE = size.options(size.selectedIndex).text;
				bChange = true;
			}
		}
	}
	if (bChange){
		Resume();
	}
	
}

function fontName(name){

	var bChange = false;
	var Items = getSelectedItems();
	for(var i = 0; i < Items.length; i++){
		var elm = Items(i);
		if (elm.className == "textfield" || elm.className == "datafield" || elm.className == "calcfield" || elm.className == "field"){
			if(elm.style.fontFamily != name){
				elm.style.fontFamily = name;
				bChange = true;
			}
		}
	}
	if (bChange){
		Resume();
	}
}

function bold(){

	var bChange = false;
	var Items = getSelectedItems();

	for(var i = 0; i < Items.length; i++){
		var elm = Items(i);
		if (elm.className == "textfield" || elm.className == "datafield" || elm.className == "calcfield" || elm.className == "field"){
			
			if ( elm.style.fontWeight == 'bold' ) {
				elm.style.fontWeight = 'normal';
				elm.style.letterSpacing  = '0px';
				elm.style.wordSpacing = '0px';
			} else {
				elm.style.fontWeight = 'bold';
				elm.style.letterSpacing  = '-1px';
				elm.style.wordSpacing = '-1px';
			}
			bChange = true;
		}
	}
	if (bChange){
		Resume();
	}

}

function italic(){

	var bChange = false;
	var Items = getSelectedItems();
	for(var i = 0; i < Items.length; i++){
		var elm = Items(i);
		if (elm.className == "textfield" || elm.className == "datafield" || elm.className == "calcfield" || elm.className == "field"){

			if ( elm.style.fontStyle == 'italic' ) {
				elm.style.fontStyle  = 'normal';
			} else {
				elm.style.fontStyle  = 'italic';
			}
			bChange = true;
		}
	}
	if (bChange){
		Resume();
	}

}

function justifycenter(){

	var bChange = false;
	var items = getSelectedItems();
	
	for (var i = 0; i < items.length; i++){

		aligncenter(items(i));

	}

	Resume();
	
}

function concatCenter(){

	var items = this.getSelectedItems();
	var width = 0;
	var elm;
	var left = 1;

	if (0 < items.length){
		var paperWidth = items(0).parentElement.offsetWidth;
	}

	for (var i = 0; i < items.length; i++){
		if(items(i).className == "box"){
			width += items(i).offsetWidth - parseInt(items(i).style.borderBottomWidth);
		}else if (items(i).className == "spreadsheet"){
			width += getSpreadsheetWidth(items(i));
		}else{
			if ( items(i).style.fontWeight == 'bold' ) {
				width += (items(i).clientWidth - 2);
			}else{
				width += (items(i).clientWidth - 1);
			}			
		}

	}
	left = paperWidth / 2 - width / 2;

	for (var i = 0; i < items.length; i++){
		elm = items(i);
		elm.style.pixelLeft = left;
		if(elm.className == "box"){
			left += elm.offsetWidth - parseInt(elm.style.borderBottomWidth);
		}else if (elm.className == "spreadsheet"){
			left += getSpreadsheetWidth(elm);
		}else{
			if ( elm.style.fontWeight == 'bold' ) {
				left += (elm.clientWidth - 2);
			}else{
				left += (elm.clientWidth - 1);
			}
		}
	}

	Resume();

}


function justifyleft(){

	var items = this.getSelectedItems();
	var elm;
	var left = 0;
	
	for (var i = 0; i < items.length; i++){
		elm = items(i);
		if (left < 1) {
			left = elm.style.pixelLeft;
		}
		if (left > elm.style.pixelLeft) {
				left = elm.style.pixelLeft;
		}
	}

	for (var i = 0; i < items.length; i++){
		
		elm = items(i);
		elm.style.pixelLeft = left;

//		elm.style.pixelRight = left + elm.clientWidth;

	}


	Resume();
	
}

function justifyright(){

	var items = this.getSelectedItems();
	var width = 0;
	var elm;
	var right = 0;

	for (var i = 0; i < items.length; i++){
		elm = items(i);

		if (elm.className == "spreadsheet"){
			width = getSpreadsheetWidth(elm);
		}else if ( elm.style.fontWeight == 'bold' ) {
			width = (elm.clientWidth - 1);
		}else{
			width = (elm.clientWidth - 0);
		}			
		if (right < elm.style.pixelLeft + width) {
			right = elm.style.pixelLeft + width;
		}
	}

	for (var i = 0; i < items.length; i++){
		elm = items(i);
		if (elm.className == "spreadsheet"){
			elm.style.pixelLeft = right - getSpreadsheetWidth(elm);
			elm.style.pixelRight = elm.style.pixelLeft + getSpreadsheetWidth(elm);
		}else if ( elm.style.fontWeight == 'bold' ) {
			elm.style.pixelLeft = right - (elm.clientWidth - 1);
			elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;
		}else{
			elm.style.pixelLeft = right - (elm.clientWidth - 0);
			elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;
		}

	}

	Resume();
	
}

function concatLeft(){

	var items = this.getSelectedItems();
	var width = 0;
	var elm;
	var left = 0;
	
	if (items.length < 1){
		return true;
	}

	left = items(0).style.pixelLeft;

	for (var i = 0; i < items.length; i++){
		elm = items(i);
		elm.style.pixelLeft = left;

//		elm.style.pixelRight = left + elm.clientWidth;

		if(elm.className == "box"){
			left += elm.offsetWidth - parseInt(elm.style.borderBottomWidth);
		}else{
			if ( elm.style.fontWeight == 'bold' ) {
				left += (elm.clientWidth - 2);
			}else{
				left += (elm.clientWidth - 1);
			}		
		}
		

	}

	Resume();
	
}


function concatRight(){

	var items = this.getSelectedItems();
	var width = 0;
	var elm;
	var right = 0;


	if(items(0).className == "box"){
//		right = items(0).style.pixelLeft + items(0).offsetWidth - parseInt(items(0).style.borderBottomWidth);
		right = items(0).style.pixelLeft + items(0).offsetWidth;
	}else{
		if ( items(0).style.fontWeight == 'bold' ) {
			right = items(0).style.pixelLeft + (items(0).clientWidth - 1);
		}else{
			right = items(0).style.pixelLeft + (items(0).clientWidth - 0);
		}			
	}

	for (var i = 0; i < items.length; i++){
		if(items(i).className == "box"){
			width += items(i).offsetWidth - parseInt(items(i).style.borderBottomWidth);
		}else{
			if ( items(i).style.fontWeight == 'bold' ) {
				width += (items(i).clientWidth - 2);
			}else{
				width += (items(i).clientWidth - 1);
			}
		}
	}
	left = (right - width);


	for (var i = 0; i < items.length; i++){
		elm = items(i);
		if(elm.className == "box"){
			elm.style.pixelLeft = right - elm.offsetWidth;
		}else{
			if ( elm.style.fontWeight == 'bold' ) {
				elm.style.pixelLeft = right - (elm.clientWidth - 1);
			}else{
				elm.style.pixelLeft = right - (elm.clientWidth - 0);
			}			
		}

//		elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;

		if(elm.className == "box"){
			right -= elm.offsetWidth - parseInt(elm.style.borderBottomWidth);
		}else{
			if ( elm.style.fontWeight == 'bold' ) {
				right -= (elm.clientWidth - 2);
			}else{
				right -= (elm.clientWidth - 1);
			}
		}			
	}

	Resume();
	
}

function justifyBottom(){

	var bChange = false;
	var items = getSelectedItems();
	
	var bottom = items(0).style.pixelTop + items(0).clientHeight;

	for (var i = 0; i < items.length; i++){
		elm = items(i);
		
		elm.style.pixelTop = bottom - elm.clientHeight;
	}
	Resume();
	
}

function concatVertical(){

	var bChange = false;
	var items = getSelectedItems();
	
	var top = items(0).style.pixelTop;

	for (var i = 0; i < items.length; i++){
		elm = items(i);
		elm.style.pixelTop = top;
//		top += elm.clientHeight;
		top += elm.offsetHeight;
		if(elm.className == "box"){
//			alert(parseInt(elm.style.borderBottomWidth));
			top -= parseInt(elm.style.borderBottomWidth);
		}
	}
	Resume();
	
}

function align(elm){

	switch (elm.parentElement.align) {
	case "left":
		alignleft(elm);
		break;
	case "right":
		alignright(elm);
		break;
	default:
		aligncenter(elm);
		break;
	}

}

function aligncenter(elm){

	var ContainerWidth = elm.parentElement.offsetWidth;

	if (elm.className == "spreadsheet"){
		elm.style.pixelLeft = ContainerWidth / 2 - (getSpreadsheetWidth(elm) - 2) / 2;
	}else if ( elm.style.fontWeight == 'bold' ) {
		elm.style.pixelLeft = ContainerWidth / 2 - (elm.clientWidth - 2) / 2;
	}else{
		elm.style.pixelLeft = ContainerWidth / 2 - (elm.clientWidth - 1) / 2;
	}
//	elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;
	
}
function alignleft(elm){

//	elm.style.pixelLeft = elm.parentElement.offsetLeft;
	elm.style.pixelLeft = 0;

//	elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;
	
}

function alignright(elm){

	var ContainerWidth = elm.parentElement.offsetWidth;
	
	if (elm.className == "spreadsheet"){
		elm.style.pixelLeft = ContainerWidth - (getSpreadsheetWidth(elm) - 2);
	}else if ( elm.style.fontWeight == 'bold' ) {
		elm.style.pixelLeft = ContainerWidth - (elm.clientWidth - 2);
	}else{
		elm.style.pixelLeft = ContainerWidth - (elm.clientWidth - 1);
	}			

//	elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;
	
}

function valign(elm){

	switch (elm.parentElement.vAlign) {
	case "top":
		valigntop(elm);
		break;
	case "bottom":
		valignbottom(elm);
		break;
	default:
		valigncenter(elm);
		break;
	}

}


function valigncenter(elm){

	var ContainerHeight = elm.parentElement.offsetHeight;

	if (ContainerHeight < elm.clientHeight){
		elm.style.pixelTop = 0;
	}else{
		elm.style.pixelTop = ContainerHeight / 2 - elm.clientHeight / 2;
	}

}

function valigntop(elm){

	elm.style.pixelTop = 0;

}

function valignbottom(elm){

	var ContainerHeight = elm.parentElement.offsetHeight;

	elm.style.pixelTop = ContainerHeight - elm.clientHeight;

}

function textAlign(op){

	var bChange = false;

	var items = this.getSelectedItems();

	for (var i = 0; i < items.length; i++){
		elm = items(i);
		if (elm.className == "datafield" || elm.className == "textfield"){
			elm.style.textAlign = op;
			bChange = true;
		}
	}
	
	if(bChange){
		Resume();
	}

}

function fieldBeforePaste(){
	event.returnValue = false;
	return false;
}

function fieldPaste(){
	event.returnValue = false;

	var sel, rng, el, dup;

	var html = window.clipboardData.getData("Text");
	
	var re;
	re = /&/g;
	html = html.replace(re,"&amp;");
	re = /</g;
	html = html.replace(re,"&lt;");
	re = />/g;
	html = html.replace(re,"&gt;");
	re = / /g;
	html = html.replace(re,"&nbsp;");
	

	if (sel = document.selection) {

		if(sel.type.toLowerCase() == "control"){
			return false;
		}
		rng = sel.createRange();
		if (rng.parentElement()){
			el = rng.parentElement();
			if(sel.type.toLowerCase() == "none"){
				if (el.className == "field"){
					rng.pasteHTML(html);
					el.focus();
				}else if(el.tagName.toLowerCase() == "a"){
					el.insertAdjacentHTML("beforeBegin",html);
					el.parentElement.focus();
				}
			}else{
				if (rng.htmlText != rng.text){
					return false;
				}
				if (el.className == "field"){
					rng.pasteHTML(html);
					el.focus();
				}
			}
		}
	}
	
	return false;
	
}



function Resume(){

	oPage = document.getElementById("PAGE");

	if ((current > 0) && (history[current] == oPage.innerHTML)){
		return;
	}

	current++;

	if (current >= history.length){
		for(var i = 0; i < history.length - 1; i ++){
			history[i] = history[i + 1];
		}
		current = history.length - 1;
	}
	
	history[current] = oPage.innerHTML;
	countHistory = current;
	
}

function Undo(){

	if (current > 0){
		current--;

//		document.body.innerHTML = history[current];
		document.getElementById("PAGE").innerHTML = history[current];
		initItems();

		
	}
}

function Redo(){

	if (current < countHistory){
		current++;

//		document.body.innerHTML = history[current];
		document.getElementById("PAGE").innerHTML = history[current];
		initItems();

	}

}

function FieldAdjustSize(elm){


	if(elm.style.posWidth < 0.5){
		elm.style.posWidth = 0.5;
	}
	if(elm.style.posWidth > 128){
		elm.style.posWidth = 128;
	}
	
	var em = Math.round(elm.style.posWidth * 2);
	em = em / 2
	elm.style.posWidth = em;
	
	if ( elm.style.fontWeight == 'bold' ) {
		elm.style.pixelWidth = elm.style.pixelWidth + 2;
	}else{
		elm.style.pixelWidth = elm.style.pixelWidth + 1;
	}
		
	return true;
				
}

function cleanupQueryFields(){

	var queryContainer = document.getElementById("QUERYFIELDS");
	var root = queryContainer.selectSingleNode("//FIELDS");
	var querys = root.childNodes;
	
	var dirtyFieldList = new Array();
	var cnt = 0;

	for(var i=0; i <querys.length; i++){
		if(document.getElementById(querys(i).getAttribute("SVF_FIELD_ID")) == null){
			if (querys(i).childNodes.length == 0){
				dirtyFieldList[cnt] = querys(i);
				cnt++;				
			}
		}
	}
	for(var i=0; i<dirtyFieldList.length ; i++){
		root.removeChild(dirtyFieldList[i]);
	}

	return queryContainer;

}

function setInnerTHML(){

	PopupCancel();

//	var query = cleanupQueryFields();
//	var root = query.selectSingleNode("/");		
//	query.innerHTML = root.xml;
		
	var oTables = document.body.getElementsByTagName("TABLE");
	for (var i=0 ; i <oTables.length; i++){
		if(oTables(i).className == "spreadsheet"){
			var recordLeft = oTables(i).offsetLeft;
			var tableTop = oTables(i).offsetTop;
			for(var j=0; j<oTables(i).rows.length; j++){
				var oTr = oTables(i).rows(j);
				switch (oTr.className){
				case "TableHead":
				case "TableBody":
				case "TableSum":
				case "TableTotal":
					oTr.style.left = recordLeft + "px";
					oTr.style.top = (tableTop + oTr.offsetTop) + "px";
//					oTr.style.height = oTr.offsetHeight + "px";
					oTr.SVF_REC_HEIGHT = oTr.offsetHeight;
					var trWidth = 0;
					for(var k=0; k<oTr.cells.length; k++){
						var oCell = oTr.cells(k);
						if(oCell.className != "row"){
							oCell.style.top = 0;
							oCell.style.left = oCell.offsetLeft;
							oCell.SVF_WIDTH = oCell.offsetWidth;
							oCell.SVF_HEIGHT = oCell.offsetHeight;
							
							trWidth += oCell.offsetWidth;
							
							var fields = oCell.childNodes;
							for(var l=0; l<fields.length; l++){
								fields(l).style.top = fields(l).offsetTop;
								fields(l).style.left = oCell.offsetLeft + fields(l).offsetLeft;
							}
						}
					}
//					oTr.style.width = trWidth + "px";
					oTr.SVF_REC_WIDTH = trWidth;
					break;					
				default:
					break;
				}
			}
		}
	}

	var oElms = document.body.getElementsByTagName("DIV");
	for (var i=0 ; i <oElms.length; i++){
		oElm = oElms(i);
		switch (oElm.className){
		case "textfield":
		case "datafield":
		case "calcfield":
		case "field":
			oElm.SVF_WIDTH = oElm.offsetWidth * 4;
			break;
		}
	}
	

	return false;
	
}

function Cancel(){
//	alert("cancel! " + event.type + " at " +  event.srcElement.tagName + " " + event.srcElement.className);
	event.returnValue = false;
	event.cancelBubble = true;
	return false;
}

function getItemByName(name){
	var elements = document.all;
	for(i = 0; i < elements.length; i++){
		if(elements(i).name == name){
			return elements(i);
		}
	}
	return null;
}

function getCountOfSameName(id,name){

	var count = 0;
	var elements = document.all;
	for(i = 0; i < elements.length; i++){
		if(elements(i).name == name && elements(i).id != id){
			count++;
		}
	}
	return count;
}

function Focus(){

	switch (event.srcElement.className) {
	case "textfield":
		event.srcElement.style.overflow = "visible";
		event.srcElement.style.width = "0.5em";
		break;
	case "datafield":
		break;
	}
	
}

function Blur(){
	if(event.srcElement.className == "textfield"){
	
		//障害NO:8722 対策
		for(var i=0; i<document.links.length ;i++){
			document.links[i].outerHTML = document.links[i].innerText;
		}
	
	
	
		var offsetWidth = event.srcElement.offsetWidth;
		var fontSize = parseInt(event.srcElement.style.fontSize);
		event.srcElement.style.posWidth = offsetWidth / fontSize;
		
//		event.srcElement.style.overflow = "hidden";
		event.srcElement.SVF_DATA_LENGTH = event.srcElement.style.posWidth;
		
		//2009-11-11 add ->
		var originalText = "";
		if(event.srcElement.SVF_TEXT){
			originalText = event.srcElement.SVF_TEXT;
		}
		var updatedText = event.srcElement.innerText;
		//2009-11-11 add <-
		
		if(event.srcElement.innerText == ""){
			event.srcElement.parentElement.removeChild(event.srcElement);
		}else{
			event.srcElement.SVF_TEXT = event.srcElement.innerText;
		}
		
		
		//2009-11-11 update ->
		//Resume();
		if(originalText != updatedText){
			Resume();
		}
		//2009-11-11 update <-
		
	}
}

function getNewQueryField(dd_alias,dd_id){
	
	var query = document.getElementById("QUERYFIELDS")
	var XMLDoc = query.XMLDocument;
	var newElm = XMLDoc.createElement("COLUMN");
	newElm.setAttribute("DD_ARIAS",dd_alias);
	newElm.setAttribute("DD_ID",dd_id);
	
	return newElm;
//	alert("aa");
}

function getNewDataField(queryField){

	var query = document.getElementById("QUERYFIELDS")
	var root = query.selectSingleNode("//FIELDS");
	root.appendChild(queryField);


	var id = "";
	for (var i=1 ; ; i++){
		id = "DB_" + i;
		if(root.selectSingleNode('COLUMN[@SVF_FIELD_ID="'+id+'"]') == null){
			break;
		}
	}

	var html = '<DIV id="'+id+'">';
	var eml = document.createElement(html);

	eml.className = "datafield";

	queryField.setAttribute("SVF_FIELD_ID",id);
	
	var relations = ddXML_ROOT.getElementsByTagName("RELATION");
	
	var relation = null;
	var column = null;
	for(var i=0; i<relations.length; i++){
		relation = relations(i)
		if(relation.getAttribute("alias") == queryField.getAttribute("DD_ARIAS")){
			var childNodes = relation.childNodes;

			for(var j=0; j<childNodes.length; j++){
				column = childNodes.item(j);
				if(column.tagName == "COLUMN" && column.getAttribute("id") == queryField.getAttribute("DD_ID")){
					break;
				}
			}
			
			break;
		}
	}

	var name = column.getAttribute("name");
	for (var i=2 ; ; i++){
		
		if (getItemByName(name) == null){
			eml.name = name;
			break;
		}
		name = column.getAttribute("name") + i;
	}
	eml.title = eml.name;	

	
	//2009-03-31
	if(column.getAttribute("type") == "picture"){
		eml.className = "picturedatafield"
		eml.style.width = "100px";
		eml.style.height = "115px";
		eml.style.borderStyle = "solid";
		eml.style.borderWidth = "1px";
		eml.style.backgroundColor = "#ffd700";
		eml.columnId = queryField.getAttribute("DD_ID");
	}
	else{
		eml.style.fontSize = "16px";
		eml.SVF_FONT_SIZE = "11.5pt"
		eml.style.fontFamily = "ＭＳ 明朝";
		eml.style.height = "1em";
		eml.style.letterSpacing = "0px";
		eml.style.textAlign = "left";
		eml.style.borderWidth = 0;

		eml.SVF_LENGTH = column.getAttribute("length");
		eml.SVF_DISPLENGTH = column.getAttribute("length");
		eml.SVF_BYTELENGTH = column.getAttribute("byteLength");
		if (column.getAttribute("lengthDP") != null){
			eml.SVF_LENGTH_DP = column.getAttribute("lengthDP");
			eml.SVF_DISPLENGTH_DP = column.getAttribute("lengthDP");
		}else{
			eml.SVF_LENGTH_DP = 0;
			eml.SVF_DISPLENGTH_DP = 0;
		}
	}

	eml.SVF_DATA_TYPE = column.getAttribute("type");

	
	var codeEdit = "";
	var codeDef = column.selectSingleNode("CODE");
	if (codeDef !=null){
		var codes = codeDef.childNodes;
		var nullCode = ":;";
		for(var j=0; j<codes.length; j++){
			var code = codes.item(j);
			if(code.getAttribute("value") == ""){
				nullCode = code.getAttribute("value") + ":" + code.getAttribute("display") + ";";
			}else{
				codeEdit += code.getAttribute("value") + ":" + code.getAttribute("display") + ";";
			}
		}
		codeEdit += nullCode;
	}
	if(codeEdit > ""){
		eml.SVF_CODE_EDIT = codeEdit;
	}

	var format = "";
	switch (eml.SVF_DATA_TYPE) {
	case "number":
		for(var i=0; i<parseInt(eml.SVF_DISPLENGTH); i++){
			format += "9";
		}
		if(parseInt(eml.SVF_DISPLENGTH_DP) > 0){
			format += ".";
			for(var i=0; i<parseInt(eml.SVF_DISPLENGTH_DP); i++){
				format += "9";
			}
		}
		eml.SVF_EDIT_STYLE = "EDIT_STYLE_RIGHT_DECIMAL_POINT_POSITION_FIXED";
		//2008-08-06 初期値を空文字とするをDefaultとする。
		eml.ALLOW_NULL = "true";
		break;
	case "date":
//		format = "YYYY/MM/DD";
		format = "yyyy/MM/dd";
		break;
	case "time":
//		format = "HH:MM:SS";
		format = "hh:mm:ss";
		break;
//2009-03-31
	case "picture":
		format = "";
		break;
	default:
		for(var i=0; i<parseInt(eml.SVF_LENGTH); i++){
			if(i > 255){
				break;
			}
			format += "X";
		}
		break;
	}

	//2009-03-31
	if(column.getAttribute("type") != "picture"){
		eml.SVF_DATA_LENGTH = format.length/2 * 16;
		eml.style.width = format.length/2 +"em";
	}
	
	if(eml.SVF_DATA_TYPE == "string" && eml.style.posWidth > 10){
		eml.style.posWidth = 10;
	}
	eml.SVF_FORMAT = format;
	eml.onbeforeeditfocus = Cancel;
	eml.noWrap = true;

	var html = "";
	html += '<SPAN class="PREFIX"></SPAN><SPAN class="FORMAT">'+format+'</SPAN><SPAN class="SUFFIX"></SPAN>';
	eml.innerHTML = html;
	
	eml.onresize = Resize;
	
	eml.onbeforeactivate = Cancel;
	
	var oSpans = eml.getElementsByTagName("SPAN");
	for(var i=0; i<oSpans.length; i++){
		oSpans(i).onmousedown = Cancel;
	}


	return eml;

}

function getNewCalcField(){

	var id = "";
	for (var i=1 ; ; i++){
		id = "FL_" + i;
		if (document.getElementById(id) == null){
			break;
		}
	}

	var elm = document.createElement('<DIV id='+id+'>');
	for (var i=1 ; ; i++){
		var name = "集計フィールド" + i;
		if (getItemByName(name) == null){
			elm.name = name;
			break;
		}
	}
	
	elm.title = name;

	elm.className = "calcfield"
	elm.style.fontSize = "16px";
	elm.SVF_FONT_SIZE = "11.5pt"
	elm.style.fontFamily = "ＭＳ 明朝";
	elm.style.width = "0.5em";
	elm.style.height = "1em";
	elm.style.borderWidth = 0;


	elm.SVF_DATA_TYPE = "number";

	
	format = "999999999";
	elm.SVF_LENGTH = format.length;
	elm.SVF_LENGTH_DP = 0;
	elm.SVF_DISPLENGTH = format.length;
	elm.SVF_DISPLENGTH_DP = 0;
	elm.SVF_DATA_LENGTH = format.length/2 * 16;
	elm.style.width = format.length/2 +"em";
	elm.SVF_FORMAT = format;

	elm.noWrap = true;

	var html = "";
	html += '<SPAN class="PREFIX"></SPAN><SPAN class="FORMAT">'+format+'</SPAN><SPAN class="SUFFIX"></SPAN>';
	elm.innerHTML = html;

	elm.onresize = Resize;
	
	elm.onbeforeactivate = Cancel;
	elm.onbeforeeditfocus = Cancel;
	
	var oSpans = elm.getElementsByTagName("SPAN");
	for(var i=0; i<oSpans.length; i++){
		oSpans(i).onmousedown = Cancel;

	}

	
	return elm;

}

function getNewCommonField(type){

	var id = "";
	for (var i=1 ; ; i++){
		id = "FL_" + i;
		if (document.getElementById(id) == null){
			break;
		}
	}

	var elm = document.createElement('<DIV id='+id+'>');
	for (var i=1 ; ; i++){
		var name = "フィールド" + i;
		if (getItemByName(name) == null){
			elm.name = name;
			break;
		}
	}
	
	elm.title = name;

	elm.className = "field"
	elm.style.fontSize = "16px";
	elm.SVF_FONT_SIZE = "11.5pt"
	elm.style.fontFamily = "ＭＳ 明朝";
	elm.style.width = "0.5em";
	elm.style.height = "1em";
	elm.style.borderWidth = 0;

	elm.SVF_DATA_TYPE = type;
	
	var format = "";
	if(type == "string"){
		for(var i=0; i<256; i++){
			format += "X";
		}
		elm.SVF_LENGTH = format.length;
	}else if(type == "date"){
//		format = "YYYY/MM/DD";
		format = "yyyy/MM/dd";
		elm.SVF_LENGTH = 8;
		elm.SVF_REF_FIELD = "IDATE()"
	}else if(type == "time"){
//		format = "HH:MM:SS";
		format = "hh:mm:ss";
		elm.SVF_LENGTH = 6;
		elm.SVF_REF_FIELD = "ITIME()"
	}else{
		format = "99999";
		elm.SVF_LENGTH = format.length;
		elm.SVF_EDIT_STYLE = "EDIT_STYLE_RIGHT_DECIMAL_POINT_POSITION_FIXED";
		//2008-08-06 初期値を空文字とするをDefaultとする。
		elm.ALLOW_NULL = "true";
	}
	
	elm.SVF_LENGTH_DP = 0;
	elm.SVF_DISPLENGTH = format.length;
	elm.SVF_DISPLENGTH_DP = 0;
	elm.SVF_DATA_LENGTH = format.length/2 * 16;
	var printLength = format.length;
	if(type == "string"){
		printLength = 20;
	}
	elm.style.width = printLength/2 +"em";
	elm.SVF_FORMAT = format;

	elm.noWrap = true;

	var html = "";
	html += '<SPAN class="PREFIX"></SPAN><SPAN class="FORMAT">'+format+'</SPAN><SPAN class="SUFFIX"></SPAN>';
	elm.innerHTML = html;

	elm.onresize = Resize;
	
	elm.onbeforeeditfocus = Cancel;
	elm.onbeforeactivate = Cancel;
	
	var oSpans = elm.getElementsByTagName("SPAN");
	for(var i=0; i<oSpans.length; i++){
		oSpans(i).onmousedown = Cancel;
	}

	
	return elm;

}


function getNewTextField(text){

	var elm = document.createElement('DIV');

	for (var i=1 ; ; i++){
		var name = "文字列フィールド" + i;
		if (getItemByName(name) == null){
			elm.name = name;
			break;
		}
	}
	elm.className= "textfield";
	elm.style.fontSize = "16px";
	elm.SVF_FONT_SIZE = "11.5pt"
	elm.style.fontFamily = "ＭＳ 明朝";
	elm.style.width = "0.5em";
	elm.style.height = "1em";
	elm.style.borderWidth = 0;
	
	elm.contentEditable = true;
	elm.onresize = Resize;
	elm.onresizestart = Cancel;
	elm.onblur = Blur;
	elm.onfocus  = Focus;
	
//	elm.innerText = "";
	elm.innerText = text;
	return elm;

}


function getNewHLine(){
	var elm = document.createElement("DIV");

	elm.className = "hline"
	elm.style.width = "10px";
	elm.style.height = "4px";
	elm.style.borderTopStyle = "solid";
	elm.style.borderTopWidth = "1px";

//	elm.contentEditable = false;
		
	elm.onresize = Resize;
	return elm;

}

function getNewVLine(){
	var elm = document.createElement("DIV");

	elm.className = "vline"
	elm.style.width = "4px";
	elm.style.height = "10px";
	elm.style.borderLeftStyle = "solid";
	elm.style.borderLeftWidth = "1px";
//	elm.contentEditable = false;
	
	elm.onresize = Resize;
	return elm;

}

function getNewBox(){

	var elm = document.createElement("DIV");
	elm.className = "box"
	elm.style.width = "10px";
	elm.style.height = "10px";
	elm.style.borderStyle = "solid";
	elm.style.borderWidth = "1px";
//	elm.contentEditable = false;

	elm.onresize = Resize;

	return elm;

}

function getNewSubForm(){

	for (var i=1 ; ; i++){
		id = "SUBFORM_" + i;
		if (document.getElementById(id) == null){
			break;
		}
	}
	var html = '<DIV id="'+id+'">';
	var elm = document.createElement(html);

	elm.className = "subform"

	var name;
	for (var i=1 ; ; i++){
		name = "サブフォーム" + i;
		if (getItemByName(name) == null){
			elm.name = name;
			break;
		}
	}
	elm.title = elm.name;
	elm.SVF_DIRECTION = "VERTICAL";

	elm.style.borderTopWidth = "0px";
	elm.style.borderTopStyle = "solid";
	elm.style.borderLeftWidth = "0px";
	elm.style.borderLeftStyle = "solid";
	elm.style.borderRightWidth = "0px";
	elm.style.borderRightStyle = "solid";
	elm.style.borderBottomWidth = "0px";
	elm.style.borderBottomStyle = "solid";

	elm.style.height = "50px";
	elm.style.width = "500px";

	elm.onfocus = Cancel;
	elm.onresize = Resize;

//	elm.contentEditable = false;
 		
//	alert(elm);
	return elm;

}

function getNewRecord(newItem){

	var id = "";
	for (var i=1 ; ; i++){
		switch (newItem) {
		case "Header":
			id = "HEADER_" + i;
			break;
		case "Detail":		
			id = "DETAIL_" + i;
			break;
		case "Sum":		
			id = "SUM_" + i;
			break;
		case "Total":		
			id = "TOTAL_" + i;
			break;
		}
		if (document.getElementById(id) == null){
			break;
		}
	}

	var html = '<DIV id="'+id+'">';
	var elm = document.createElement(html);

	switch (newItem) {
	case "Header":
		elm.className = "HEADER"
		break;
	case "Detail":		
		elm.className = "DETAIL"
		break;
	case "Sum":		
		elm.className = "SUM"
		break;
	case "Total":		
		elm.className = "TOTAL"
		break;
	}

	
	
	var name;
	for (var i=1 ; ; i++){
		switch (newItem) {
		case "Header":
			name = "ヘッダ" + i;
			break;
		case "Detail":		
			name = "明細" + i;
			break;
		case "Sum":		
			name = "合計" + i;
			break;
		case "Total":		
			name = "総計" + i;
			break;
		}
		if (getItemByName(name) == null){
			elm.name = name;
			break;
		}
	}

	elm.title = name;
	
	elm.style.borderTopWidth = "0px";
	elm.style.borderTopStyle = "solid";
	elm.style.borderLeftWidth = "0px";
	elm.style.borderLeftStyle = "solid";
	elm.style.borderRightWidth = "0px";
	elm.style.borderRightStyle = "solid";
	elm.style.borderBottomWidth = "0px";
	elm.style.borderBottomStyle = "solid";
	elm.style.height = "50px";
	elm.style.width = "500px";

	elm.onfocus = Cancel;
	elm.onresize = Resize;
 		
	elm.SVF_AUTO_LINK_FIELD_PITCH = "1.2"
	elm.SVF_DONOT_PRINT_IF_EMPTY = "true";
	
	return elm;
	
}

function createNewTableHead(oTable,rowIndex){
			
	var oTableHead = oTable.insertRow(rowIndex);
	oTableHead.className = "TableHead";
	
	oTableHead.style.borderTopStyle = "solid";
	oTableHead.style.borderTopWidth = "1px";
	oTableHead.style.borderLeftStyle = "solid";
	oTableHead.style.borderLeftWidth = "0px";
	oTableHead.style.borderRightStyle = "solid";
	oTableHead.style.borderRightWidth = "0px";
	oTableHead.style.borderBottomStyle = "solid";
	oTableHead.style.borderBottomWidth = "1px";

	var id = "";
	for (var i=1 ; ; i++){	
		id = "HEADER_" + i;
		if (document.getElementById(id) == null){
			oTableHead.id = id;
			break;
		}
	}
	
	for (var i=1 ; ; i++){
		var name = "ヘッダ" + i;
		if (getItemByName(name) == null){
			oTableHead.name = name;
			break;
		}
	}
	oTableHead.title = oTableHead.name;
	oTableHead.SVF_AUTO_LINK_FIELD_PITCH = "1.2";
	oTableHead.SVF_PRINT_ON_TOP_OF_DETAILS = "true";
	oTableHead.SVF_PRINT_ON_TOP_OF_PAGE = "true";
	oTableHead.SVF_DONOT_PRINT_IF_EMPTY = "true";
	return oTableHead;
}
function createNewTableBody(oTable,rowIndex){
	var oTableBody = oTable.insertRow(rowIndex);
	oTableBody.className = "TableBody";

	oTableBody.style.borderTopStyle = "solid";
	oTableBody.style.borderTopWidth = "1px";
	oTableBody.style.borderLeftStyle = "solid";
	oTableBody.style.borderLeftWidth = "0px";
	oTableBody.style.borderRightStyle = "solid";
	oTableBody.style.borderRightWidth = "0px";
	oTableBody.style.borderBottomStyle = "solid";
	oTableBody.style.borderBottomWidth = "1px";

	id = "";
	for (var i=1 ; ; i++){	
		id = "DETAIL_" + i;
		if (document.getElementById(id) == null){
			oTableBody.id = id;
			break;
		}
	}
	for (var i=1 ; ; i++){
		var name = "明細" + i;
		if (getItemByName(name) == null){
			oTableBody.name = name;
			break;
		}
	}
	oTableBody.title = oTableBody.name;
	oTableBody.SVF_AUTO_LINK_FIELD_PITCH = "1.2"
	oTableBody.SVF_DONOT_PRINT_IF_EMPTY = "true";			
	return oTableBody;
}
function createNewTableSum(oTable,rowIndex){
	var oTableSum = oTable.insertRow(rowIndex);
	oTableSum.className = "TableSum";

	oTableSum.style.borderTopStyle = "solid";
	oTableSum.style.borderTopWidth = "1px";
	oTableSum.style.borderLeftStyle = "solid";
	oTableSum.style.borderLeftWidth = "0px";
	oTableSum.style.borderRightStyle = "solid";
	oTableSum.style.borderRightWidth = "0px";
	oTableSum.style.borderBottomStyle = "solid";
	oTableSum.style.borderBottomWidth = "1px";

	id = "";
	for (var i=1 ; ; i++){	
		id = "SUM_" + i;
		if (document.getElementById(id) == null){
			oTableSum.id = id;
			break;
		}
	}
	for (var i=1 ; ; i++){
		var name = "合計" + i;
		if (getItemByName(name) == null){
			oTableSum.name = name;
			break;
		}
	}
	oTableSum.title = oTableSum.name;
	oTableSum.SVF_AUTO_LINK_FIELD_PITCH = "1.2"
	oTableSum.SVF_DONOT_PRINT_IF_EMPTY = "true";

	return oTableSum;
}
function createNewTableTotal(oTable,rowIndex){
	var oTableTotal = oTable.insertRow(rowIndex);
	oTableTotal.className = "TableTotal";

	oTableTotal.style.borderTopStyle = "solid";
	oTableTotal.style.borderTopWidth = "1px";
	oTableTotal.style.borderLeftStyle = "solid";
	oTableTotal.style.borderLeftWidth = "0px";
	oTableTotal.style.borderRightStyle = "solid";
	oTableTotal.style.borderRightWidth = "0px";
	oTableTotal.style.borderBottomStyle = "solid";
	oTableTotal.style.borderBottomWidth = "1px";


	id = "";
	for (var i=1 ; ; i++){	
		id = "TOTAL_" + i;
		if (document.getElementById(id) == null){
			oTableTotal.id = id;
			break;
		}
	}
	for (var i=1 ; ; i++){
		var name = "総計" + i;
		if (getItemByName(name) == null){
			oTableTotal.name = name;
			break;
		}
	}
	oTableTotal.title = oTableTotal.name;
	oTableTotal.SVF_AUTO_LINK_FIELD_PITCH = "1.2"
	oTableTotal.SVF_DONOT_PRINT_IF_EMPTY = "true";
	return oTableTotal;
}

function getNewTable(){

	var oTable = document.createElement("TABLE");

	oTable.className="spreadsheet";
	oTable.border=0;
	oTable.cellSpacing=0;
	oTable.cellPadding =0;
	oTable.rules="none";
	
	oTable.style.borderTopStyle = "solid";
	oTable.style.borderTopWidth = "0px";
	oTable.onresize = Resize;

	var oTHead = oTable.createTHead();
	var oHeadTR = oTHead.insertRow(-1);
	oHeadTR.className = "columnCtl";
	
	createNewTableHead(oTable,-1);

	createNewTableBody(oTable,-1);

	createNewTableSum(oTable,-1);

//	createNewTableTotal(oTable,-1);

	for(var i=0 ; i < oTable.rows.length; i++){
					
		if (i > 0){
			
			addRowSpacer(oTable.rows(i))

		}else{
			var oNewCell = oTable.rows(i).insertCell(-1);
		
			oNewCell.className = "ctrl";
		}
	}

	addColumn(oTable,0);
	addColumn(oTable,1);
	addColumn(oTable,2);
	addColumn(oTable,3);
	addColumn(oTable,4);
		
	return oTable;

}

function addRowSpacer(oTr){

	var oNewCell = oTr.insertCell(-1);
			
	oNewCell.className = "row";
	oNewCell.onresize = Resize;
	html = '<SPAN>';
	var oCell = document.createElement(html);
	oNewCell.insertAdjacentElement("beforeEnd",oCell);
	oCell.className = "rowspacer";
	oCell.contentEditable = false;
	oCell.style.height = "20px";
	oCell.onresize = Resize;
	oCell.onresizeend = Resizeend;

}

function createCell(oTr,index){
	var oNewCell = oTr.insertCell(index);
	
	switch (oTr.className) {
	case "TableHead":
		oNewCell.className = "hcell";

/*
		var oText = getNewTextField("");
		oText.style.position = "static"
		oText.style.display = "inline"
		oText.innerHTML = "列" + (oNewCell.cellIndex+1);
		oText.style.overflowX = "visible";
		oText.style.pixelWidth = oText.offsetWidth;
		oText.SVF_TEXT = oText.innerHTML;
		oNewCell.insertAdjacentElement("beforeEnd",oText);
*/

		break;
	case "TableBody":
		oNewCell.className = "dcell";

		break;		
	case "TableSum":
		oNewCell.className = "scell";

		break;		
	case "TableTotal":

		oNewCell.className = "tcell";
		break;		
	default:
		break;
	}
	oNewCell.align = "center";
	oNewCell.vAlign = "middle";

//	alert(oNewCell.cellIndex + ":" + oTr.cells.length);
	
	oNewCell.style.borderLeftStyle = "solid";
	oNewCell.style.borderLeftWidth = "1px";
	oNewCell.style.borderRightStyle = "solid";
	oNewCell.style.borderRightWidth = "1px";

//	oNewCell.style.borderTopStyle = "solid";
//	oNewCell.style.borderTopWidth = "1px";
//	oNewCell.style.borderBottomStyle = "solid";
//	oNewCell.style.borderBottomWidth = "1px";
	oNewCell.style.borderTopStyle = oTr.style.borderTopStyle;
	oNewCell.style.borderTopWidth = oTr.style.borderTopWidth;
	oNewCell.style.borderBottomStyle = oTr.style.borderBottomStyle;
	oNewCell.style.borderBottomWidth = oTr.style.borderBottomWidth;

	oNewCell.onresize = Resize;
}

function addColumn(oTable,index){


	for(var i=0 ; i < oTable.rows.length; i++){
		var oTr = oTable.rows(i);
//		var oNewCell = oTr.insertCell(index);
		if (i == 0){
			var oNewCell = oTr.insertCell(index);
			
			oNewCell.className = "column";
			oNewCell.onresize = Resize;

			var html = '<SPAN>';
			var oCell = document.createElement(html);
			oNewCell.insertAdjacentElement("beforeEnd",oCell);
			oCell.className = "columnspacer";
			oCell.contentEditable = false;
			oCell.style.width = "100px";
			oCell.onresize = Resize;
			oCell.onresizeend = Resizeend;
			
		}else{
			createCell(oTr,index);
		}

	}
}

function insertNewItem(){

	if(event.srcElement.className == "column" || event.srcElement.className == "columnspacer"){
		return false;
	}
	if(event.srcElement.className == "row" || event.srcElement.className == "rowspacer"){
		return false;
	}
//	alert(event.srcElement.className);

	switch (newItem) {
//	case "DataField":
//		placeElement(newElm,event.srcElement,event.offsetX,event.offsetY);
//		break;
	case "queryField":	
//		var querys = document.getElementById("QUERY");
//		querys.insertAdjacentElement("beforeEnd",newElm);
		newElm = getNewDataField(newElm);
		placeElement(newElm,event.srcElement,event.offsetX,event.offsetY);
		break;
	case "TextField":
		var elm = getNewTextField("");
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;
		break;

	case "CalcField":
		var elm = getNewCalcField();
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;
		break;

	case "CommonField":
		var elm = getNewCommonField("string");
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;
		break;
		
	case "DateField":
		var elm = getNewCommonField("date");
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;
		break;
		
	case "TimeField":
		var elm = getNewCommonField("time");
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;
		break;
		
	case "NumberField":
		var elm = getNewCommonField("number");
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;
		break;
		
	case "HLine":

		var elm = getNewHLine();
		if(event.srcElement.id == "PAGE"){
			placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		}else{
			placeElement(elm,event.srcElement,0,event.offsetY);
			elm.style.width = event.srcElement.style.width;
		}
		
		newElm = elm;
		break;
		
	case "VLine":

		var elm = getNewVLine();
		if(event.srcElement.id == "PAGE"){
			placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		}else{
			placeElement(elm,event.srcElement,event.offsetX,0);
			elm.style.height = event.srcElement.style.height;
		}

		newElm = elm;

		break;

	case "Box":

		var elm = getNewBox();
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;

		break;

	case "Header":
	case "Detail":
	case "Sum":
	case "Total":

		var elm = getNewRecord(newItem);
		placeElement(elm,event.srcElement,0,event.offsetY);
		elm.style.width = event.srcElement.style.width;
		newElm = elm;

		break;


	case "Table":
	
		var elm = getNewTable();
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newItem = null;
		newElm = null;
		
		break;
		
	case "SubForm":
		var elm = getNewSubForm();
		placeElement(elm,event.srcElement,event.offsetX,event.offsetY);
		newElm = elm;

		break;
		
	default:
	
//		alert(newItem);
		
		break;
	}

}



function alignNewElement(){
	
	switch (newItem) {
	case "HLine":
		var left = newElm.style.pixelLeft;
		var offset = event.offsetX - left;
		if (offset > 0){
			newElm.style.pixelWidth = offset;
		}
		break;
	case "VLine":
		var top = newElm.style.pixelTop;
		var offset = event.offsetY - top;
		if (offset > 0){
			newElm.style.pixelHeight = offset;
		}
		break;
	case "Box":
		var left = newElm.style.pixelLeft;
		var offset = event.offsetX - left;
		if (offset > 0){
			newElm.style.pixelWidth = offset;
		}

		var top = newElm.style.pixelTop;
		offset = event.offsetY - top;
		
		if (offset > 0){
			newElm.style.pixelHeight = offset;
		}

		break;

	case "Header":
	case "Detail":
	case "Sum":
	case "Total":
		var top = newElm.style.pixelTop;
		var offset = event.offsetY - top;
		if (offset > 0){
			newElm.style.pixelHeight = offset;
		}
		break;

	default:
		break;
	}
	event.cancelBubble = true;
	event.returnValue = false;
	return false;

}

function Move(){

	if (event.srcElement.style.pixelLeft < 0){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}
	if (event.srcElement.style.pixelTop < 0){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}
	var parent = event.srcElement.parentElement;
	if (event.srcElement.style.pixelTop+event.srcElement.style.pixelHeight > parent.style.pixelHeight){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}
	if (event.srcElement.style.pixelLeft+event.srcElement.style.pixelWidth > parent.style.pixelWidth){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}
	
	switch (event.srcElement.className) {		
	case "spreadsheet":
//		for(var i=0; i < event.srcElement.rows.length; i++){
//			event.srcElement.rows(i).style.top = event.srcElement.rows(i).offsetTop;
//			event.srcElement.rows(i).style.left = event.srcElement.rows(i).offsetLeft;
//		}
	}
	
	
}

function Beforeeditfocus(){

	switch (event.srcElement.className) {		
	case "subform":	
	case "hline":
	case "vline":
	case "box":
		event.cancelBubble = true;
		event.returnValue = false;
		return false;

	default:
		break;
	}
}

function Controlselect(){

	//★EXCELグリッド対応で追加
	//選択しているフィールドの位置調整
	//位置調整するだけなので、第二引数のKeyコードはブランク
	moveItem(event.srcElement,"");

	var popup = document.getElementById("POPUP");
	if(popup != null){	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}

}

function Beforeactivate(){

	if(event.srcElement.id == "POPUP"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}
}

function closeContextmenu(){

	oTargetElement = null;
	var contextMenu = document.getElementById("contextmenu");
	
	if(contextMenu != null){
		document.body.removeChild(contextMenu);
	}
	
}


function getContextmenu(){

	var contextMenu = document.getElementById("contextmenu");
	if (contextMenu == null){
		html =  '<DIV id="contextmenu">';

		var contextMenu = document.createElement(html);
		contextMenu.contentEditable = false;
		document.body.insertAdjacentElement("afterBegin",contextMenu);
	}
	return contextMenu;
}

function Contextmenu(){

	EditPropElement = event.srcElement;

	sel = document.selection;
	sel.empty();
	var html = '';
	var popup = null;

	if(event.srcElement.id == "PAGE" || event.srcElement.tagName == "BODY"){
	
		popup = getContextmenu();
		oTargetElement = event.srcElement;
	
		html += '<table style="background-color: #d3d3d3;" border="0" cellpadding="1" cellspacing="1">';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="closeContextmenu();reportPopupOpen();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">プロパティ</span>';
		html += '</td></tr>';
		html += '</table>';

		popup.innerHTML = html;

		popup.style.display = "block";
		popup.style.visibility = "visible";

		popup.style.left = event.clientX + document.body.scrollLeft;
		popup.style.top = event.clientY + document.body.scrollTop;
				
		event.cancelBubble = true;
		event.returnValue = false;
		return;
	}
	
	
	switch (event.srcElement.className) {
	case "FORMAT":
		EditPropElement = event.srcElement.parentElement;
	case "box":
	case "hline":
	case "vline":
//	case "textfield":
	case "picturedatafield":
	case "calcfield":
	case "datafield":
	case "HEADER":
	case "DETAIL":
	case "SUM":
	case "TOTAL":

	case "hcell":
	case "dcell":
	case "scell":
	case "tcell":
//2009-03-31
		if(event.srcElement.className == "picturedatafield"){
			EditPropElement = event.srcElement;
		}
		
		popup = getContextmenu();
		oTargetElement = event.srcElement.parentElement;
		html += '<table style="background-color: #d3d3d3;" border="0" cellpadding="1" cellspacing="1">';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="closeContextmenu();PopupOpen();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">プロパティ</span>';
		html += '</td></tr>';
		html += '</table>';

		popup.innerHTML = html;

		popup.style.display = "block";
		popup.style.visibility = "visible";

		popup.style.left = event.clientX + document.body.scrollLeft;
		popup.style.top = event.clientY + document.body.scrollTop;
				
		break;

	case "subform":

		popup = getContextmenu();
		oTargetElement = event.srcElement.parentElement;
	
		html += '<table style="background-color: #d3d3d3;" border="0" cellpadding="1" cellspacing="1">';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="closeContextmenu();PopupOpen();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">プロパティ</span>';
		html += '</td></tr>';
		html += '</table>';

		popup.innerHTML = html;

		popup.style.display = "block";
		popup.style.visibility = "visible";

		popup.style.left = event.clientX + document.body.scrollLeft;
		popup.style.top = event.clientY + document.body.scrollTop;
				
		break;

	case "columnspacer":
	
		popup = getContextmenu();
//		oTargetElement = event.srcElement.parentElement;

		oTargetElement = event.srcElement.parentElement.parentElement;
		var cellIndex = event.srcElement.parentElement.cellIndex;
		
		html += '<table style="background-color: #d3d3d3;" border="0" cellpadding="1" cellspacing="1">';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertColumn('+cellIndex+');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">列を挿入</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="deleteColumn('+cellIndex+');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">列を削除</span>';
		html += '</td></tr>';
//		html += '<tr><td height="1" bgcolor="gray"></td></tr>';
		html += '</table>';

		popup.innerHTML = html;

		popup.style.display = "block";
		popup.style.visibility = "visible";

		popup.style.left = event.clientX + document.body.scrollLeft;
		popup.style.top = event.clientY + document.body.scrollTop;
				
		break;

	case "rowspacer":
	
		popup = getContextmenu();
		oTargetElement = event.srcElement.parentElement.parentElement;
		EditPropElement = event.srcElement.parentElement.parentElement;
			
			
		html += '<table style="background-color: #d3d3d3;" border="0" cellpadding="1" cellspacing="1">';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="closeContextmenu();PopupOpen();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">行のプロパティ</span>';
		html += '</td></tr>';
		html += '<td height="1" bgcolor="gray"></td>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableHead\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">ヘッダ行を追加</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableBody\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">明細行を追加</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableSum\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">合計行を追加</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableTotal\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">総計行を追加</span>';
		html += '</td></tr>';
		html += '<td height="1" bgcolor="gray"></td>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="removeRow();closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">行を削除</span>';
		html += '</td></tr>';
//		html += '<tr><td height="1" bgcolor="gray"></td></tr>';
		html += '</table>';

		popup.innerHTML = html;

		popup.style.display = "block";
		popup.style.visibility = "visible";

		popup.style.left = event.clientX + document.body.scrollLeft;
		popup.style.top = event.clientY + document.body.scrollTop;
				
		break;

		
	case "ctrl":

		popup = getContextmenu();
//		oTargetElement = event.srcElement;
		oTargetElement = event.srcElement.parentElement;
		var cellIndex = event.srcElement.cellIndex;

		html += '<table style="background-color: #d3d3d3;" border="0" cellpadding="0" cellspacing="0">';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertColumn('+cellIndex+');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">列を挿入</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableHead\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">ヘッダ行を追加</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableBody\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">明細行を追加</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableSum\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">合計行を追加</span>';
		html += '</td></tr>';
		html += '<tr><td>';
		html += '<span class="menuItem" onclick="insertRow(\'TableTotal\');closeContextmenu();" onmouseenter="MouseEnter();" onmouseleave="MouseLeave();">総計行を追加</span>';
		html += '</td></tr>';
		html += '</table>';

		popup.innerHTML = html;

		popup.style.display = "block";
		popup.style.visibility = "visible";

		popup.style.left = event.clientX + document.body.scrollLeft;
		popup.style.top = event.clientY + document.body.scrollTop;
		
		break;
	
	default:
		break;
	}

	event.cancelBubble = true;
	event.returnValue = false;
	return false;		

}

function insertFieldRef(){
//	alert("aa");
	EditPropElement
	return true;
}

function MouseEnter(){
	switch (event.srcElement.className) {
	case "menuItem":
		event.srcElement.style.backgroundColor = "Navy";
		event.srcElement.style.color = "#ffffff";
		break;
	default:
		break;
	}
	
}

function MouseLeave (){
	switch (event.srcElement.className) {
	case "menuItem":
		event.srcElement.style.backgroundColor = "transparent";
		event.srcElement.style.color = "black";
		break;
	default:
		break;
	}
	
}

function insertColumn(cellIndex){

//	var cellIndex = oTargetElement.cellIndex;

//	var table = oTargetElement.parentElement.parentElement.parentElement;
	var table = oTargetElement.parentElement.parentElement;
	
	addColumn(table,cellIndex);
	Resume();
}

function deleteColumn(cellIndex){

//	var cellIndex = oTargetElement.cellIndex;

//	var table = oTargetElement.parentElement.parentElement.parentElement;
	var table = oTargetElement.parentElement.parentElement;
	
	//削除する列に対して他フィールドで利用されている項目の存在確認を行う
	for(var i=0; i<table.rows.length;i++){
	//テーブルの行ごとのループ（列はcellIndexで固定）
		var cell = table.rows[i].cells[cellIndex];
		for(var j=0; j<cell.children.length;j++){
		//セル内の子要素ごとに存在確認（一つのセル内に複数のフィールドが設定されることがあるため）
			var cellId = cell.children[j].id;
			//子要素のIDでプロパティ項目の削除処理を行う
			deleteProperty(cellId);
		}
	}

		
	for(var i=0; i<table.rows.length;i++){
		table.rows(i).deleteCell(cellIndex);
	}
	Resume();

}

function insertRow(type){

	var rowIndex = oTargetElement.rowIndex+1;
	var cellcount = oTargetElement.cells.length-1;
//	alert(cellcount);


	var table = oTargetElement.parentElement.parentElement;
	var oTr;

	switch (type) {
	case "TableHead":
		oTr = createNewTableHead(table,rowIndex);
		break;
	case "TableBody":
		oTr = createNewTableBody(table,rowIndex);
		break;
	case "TableSum":
		oTr = createNewTableSum(table,rowIndex);
		break;
	case "TableTotal":
		oTr = createNewTableTotal(table,rowIndex);
		break;
	}

	
	//	alert(oTr.tagName);
	
	addRowSpacer(oTr);
	for(var index=0; index<cellcount; index++){
//		alert(index);
		createCell(oTr,index);
	}
	Resume();

}
function removeRow(){
	var rowIndex = oTargetElement.rowIndex;
	var table = oTargetElement.parentElement.parentElement;
	
	//削除する行に対して他フィールドで利用されている項目の存在確認を行う
	for(var i=0; i<table.rows[rowIndex].cells.length;i++){
	//テーブルの列ごとのループ（行はrowIndexで固定）
		var cell = table.rows[rowIndex].cells[i];
		for(var j=0; j<cell.children.length;j++){
		//セル内の子要素ごとに存在確認（一つのセル内に複数のフィールドが設定されることがあるため）
			var cellId = cell.children[j].id;
			//子要素のIDでプロパティ項目の削除処理を行う
			deleteProperty(cellId);
		}
	}
	
	table.deleteRow(rowIndex);
	Resume();

}

function Dblclick(){

	closeContextmenu();

	if(event.srcElement.id == "PAGE" || event.srcElement.tagName == "BODY"){
		EditPropElement = event.srcElement;
		reportPopupOpen();	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}
	
//	alert(event.srcElement.className);

	switch (event.srcElement.className) {
		
	case "box":
	case "hline":
	case "vline":
//	case "textfield":
	case "calcfield":
	case "field":
	case "datafield":
	case "HEADER":
	case "DETAIL":
	case "SUM":
	case "TOTAL":

	case "hcell":
	case "dcell":
	case "scell":
	case "tcell":			

	case "ref":
	case "subform":

		EditPropElement = event.srcElement;
		PopupOpen();	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;

	case "FORMAT":
		EditPropElement = event.srcElement.parentElement;
		PopupOpen();	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
		
	case "rowspacer":
		EditPropElement = event.srcElement.parentElement.parentElement;
		PopupOpen();	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
		
	//2009-03-31
	case "picturedatafield":
		EditPropElement = event.srcElement;
		PopupOpen();	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;

		
	default:
//		alert(event.srcElement.tagName + ":" +event.srcElement.className + ":" +event.srcElement.id);
		return true;
		break;
	}
	
}

function reportPopupOpen(){



	PopupCancel();
	oToolbars.style.display = "none";
	oEditorFrame.style.pixelHeight += toolbar_height;

	target = EditPropElement;
	
	var html =  '<DIV id="POPUP">';

	var popup = document.createElement(html);
	popup.contentEditable = false;
	document.body.insertAdjacentElement("afterBegin",popup);


	var html = '';
		
	document.getElementById("PAGE").style.display = "none";

	html += '<table style="width:100%; height:100%; background-color: #d3d3d3;" border="0" cellpadding="0" cellspacing="0">';
	html += '<tr><td style="width: 100%; height: 1em;" align="left">';
	html += '<SPAN style="width:100%; height:100%; background-color: Navy; 	color: #ffffff;">レポートのプロパティ</SPAN>';
	html += '</td></tr>';
	html += '<tr><td style="width: 100%; height: 99%;" align="right">';
	html += '<iframe id="properties" style="width:100%; height:100%;" scrolling="no"></iframe>';
	html += '</td></tr>';
	html += '<tr><td style="width: 100%; height: 1em;" align="right">';
	html += '<input type="button" value="OK" style="width:5em;" onclick="PopupOk()">';
	html += '<input type="button" value="キャンセル" style="width:5em;" onclick="PopupCancel()">';
	html += '</td></tr>';
	html += '</table>';
	popup.innerHTML = html;
	document.getElementById("properties").src = "report.html";

	popup.style.width = document.body.clientWidth;
	popup.style.height = document.body.clientHeight;
	
	popup.style.display = "block";
	popup.style.visibility = "visible";
	popup.style.left = 0;
	popup.style.top = 0;


	popup.onmoveend = activatePopup;
	popup.onresizestart = activatePopup;

	document.getElementById("properties").setActive();


}

function PopupOpen(){


	PopupCancel();

//	oToolbars.style.display = "none";
//	oEditorFrame.style.pixelHeight += toolbar_height;
	
	target = EditPropElement;

	var html =  '<DIV id="POPUP">';

	var popup = document.createElement(html);
	popup.contentEditable = false;
	document.body.insertAdjacentElement("afterBegin",popup);


	var html = '';
	
	switch (target.className) {		
	case "box":
		html = "矩形" +'のプロパティ<br>';
		break;
	case "hline":
		html = "水平線" +'のプロパティ<br>';
		break;
	case "vline":
		html = "垂直線" +'のプロパティ<br>';
		break;
	case "textfield":
		html = "文字列フィールド" +'のプロパティ<br>';
		break;
	case "calcfield":
		html = "集計フィールド" +'のプロパティ<br>';
		break;
	case "field":
		html = "フィールド" +'のプロパティ<br>';
		break;
	case "datafield":
		html = "データベースフィールド" +'のプロパティ<br>';
		break;
	//2009-03-31
	case "picturedatafield":
		html = "データベースフィールド" +'のプロパティ<br>';
		break;
	case "HEADER":
		html = "ヘッダ" +'のプロパティ<br>';
		break;
	case "DETAIL":
		html = "明細" +'のプロパティ<br>';
		break;
	case "SUM":
		html = "合計" +'のプロパティ<br>';
		break;
	case "TOTAL":
		html = "総計" +'のプロパティ<br>';
		break;
	case "hcell":
	case "dcell":
	case "scell":
	case "tcell":
		html = "セル" +'のプロパティ<br>';
		break;
	case "subform":
		html = "サブフォーム" +'のプロパティ<br>';
		break;
		
	case "TableHead":
		html = "ヘッダ行" +'のプロパティ<br>';
		break;		
	case "TableBody":
		html = "明細行" +'のプロパティ<br>';
		break;		
	case "TableSum":
		html = "合計行" +'のプロパティ<br>';
		break;		
	case "TableTotal":
		html = "総計行" +'のプロパティ<br>';
		break;		

		
	default:
		return;
	}
		

	html += '<table style="width:100%; height:100%; background-color: #d3d3d3;" border="0" cellpadding="0" cellspacing="0">';
	html += '<tr><td>';
	html += '<iframe id="properties" style="width:100%; height:100%;" scrolling="no"></iframe>';
	html += '</td></tr>';
	html += '<tr><td style="width: 100%; height: 1em;" align="right">';
	html += '<input type="button" value="OK" style="width:5em;" onclick="PopupOk()">';
	html += '<input type="button" value="キャンセル" style="width:5em;" onclick="PopupCancel()">';
	html += '</td></tr>';
	html += '</table>';
	popup.innerHTML = html;

	document.getElementById("properties").src = "popup.html";

	popup.style.width = "500px";
	popup.style.height = "310px";

	popup.style.display = "block";
	popup.style.visibility = "visible";
	popup.style.left = (document.body.clientWidth - popup.offsetWidth) / 2 + document.body.scrollLeft;
	popup.style.top = (document.body.clientHeight - popup.offsetHeight) / 2 + document.body.scrollTop;

	
//	document.body.contentEditable = true;
//	document.execCommand("2D-Position",false,true);
	document.execCommand("LiveResize",false,false);

	popup.onmoveend = activatePopup;
	popup.onresizestart = activatePopup;

	document.getElementById("properties").setActive();


}

function activatePopup(){
	document.getElementById("properties").contentWindow.document.body.setActive();
}


function PopupOk(){


	var popup = document.getElementById("POPUP");

	if(popup != null){
//		document.getElementById("properties").contentWindow.fixProperty();
		if(!document.getElementById("properties").contentWindow.fixProperty()){
			return false;
		}
//		alert("PopupOk");
		document.body.removeChild(popup);
//		alert("b");
	}

//		alert("c");
	document.body.contentEditable = false;
//		alert("d");
//		alert(document.getElementById("PAGE").style.display);
	document.getElementById("PAGE").style.display = "block";

//		alert("x");
	oEditorFrame.style.pixelHeight = editorFrame_height;
	oToolbars.style.display = "block";

	Resume();
	
}

function PopupCancel(){
	
	var popup = document.getElementById("POPUP");

	if(popup != null){
		document.getElementById("properties").contentWindow.closeProperty();
		document.body.removeChild(popup);
	}

	document.body.contentEditable = false;
	document.getElementById("PAGE").style.display = "block";

	oEditorFrame.style.pixelHeight = editorFrame_height;
	oToolbars.style.display = "block";

	}

function SelectionChange(){

	var Items = getSelectedItems();
	
	if(!Items.length){
		return;
	}
	
	if(Items.length == 0){
		return;
	}
	
	if (Items.length > 1){
	
		document.execCommand("2D-Position",false,true);
	}else{
		switch (Items(0).className) {
		case "spreadsheet":
		case "HEADER":
		case "DETAIL":
		case "SUM":
		case "TOTAL":
//			document.execCommand("2D-Position",false,true);
			document.execCommand("2D-Position",false,false);
			break;
		default:
			if (Items(0).id == "POPUP"){
				document.execCommand("2D-Position",false,true);
			}else{
				document.execCommand("2D-Position",false,false);
			}
			break;
		}
	}

}


function Resize(){
			
	switch (event.srcElement.className) {
	case "textfield":
		if(event.srcElement.style.posHeight != 1){
			event.srcElement.style.posHeight = 1;
		}

		event.srcElement.SVF_DATA_LENGTH = event.srcElement.style.posWidth;
//		if(event.srcElement.SVF_DATA_LENGTH != event.srcElement.style.posWidth){
//			event.srcElement.style.posWidth = event.srcElement.SVF_DATA_LENGTH;
//		}
//		event.cancelBubble = true;
//		event.returnValue = false;
//		return false;
		break;

	case "datafield":
	case "calcfield":
	case "field":
//		if (event.srcElement.className == "datafield"){
			if(event.srcElement.style.posWidth > 128){
				event.srcElement.style.posWidth = 128;
			}
//		}
//		if (event.srcElement.className == "textfield"){
//			if(event.srcElement.offsetWidth > event.srcElement.SVF_DATA_LENGTH){
//				event.srcElement.style.pixelWidth = event.srcElement.SVF_DATA_LENGTH;
//				event.cancelBubble = true;
//				event.returnValue = false;
//				return false;
//			}
//		}
		
		if(event.srcElement.style.posHeight != 1){
			event.srcElement.style.posHeight = 1;
//			event.cancelBubble = true;
//			event.returnValue = false;
//			return false;
		}
		FieldAdjustSize(event.srcElement);
		break;

	case "hline":
		event.srcElement.style.posHeight = 4;
	
		break;
	case "vline":
		event.srcElement.style.posWidth = 4;
	
		break;

	case "rowspacer":
		if (event.srcElement.style.pixelWidth != 10){
			event.srcElement.style.pixelWidth = 10;
//			event.cancelBubble = true;
//			event.returnValue = false;
//			return false;
		}
	
		break;
	case "columnspacer":
		if (event.srcElement.style.pixelHeight != 10){
			event.srcElement.style.pixelHeight = 10;
//			event.cancelBubble = true;
//			event.returnValue = false;
//			return false;
		}				
		break;

	case "row":
		
		var elm = event.srcElement.firstChild;
//		alert(event.srcElement.clientHeight + ":" + elm.style.pixelHeight);
		if(event.srcElement.clientHeight > elm.style.pixelHeight){
			elm.style.pixelHeight = event.srcElement.clientHeight;
		}
		break;
		
	case "column":
		var elm = event.srcElement.firstChild;
//		alert(event.srcElement.clientWidth + ":" + elm.style.pixelWidth);
		if(event.srcElement.clientWidth > (elm.style.pixelWidth+1)){
			elm.style.pixelWidth = event.srcElement.clientWidth - 1;
		}
				
		break;

	case "hcell":
	case "dcell":
	case "scell":
	case "tcell":
		
//		var elm = event.srcElement.firstChild;
//		if(elm != null){
//			align(elm);
//			valign(elm);
//			elm.style.pixelLeft += event.srcElement.offsetLeft;
//			elm.style.pixelTop += event.srcElement.offsetTop;			
//		}		
		break;

	case "HEADER":
	case "DETAIL":
	case "SUM":
	case "TOTAL":
	case "subform":

		var maxRight = 0;
		var maxBottom = 0;
		
		var child = null;
		for(var i=0; i<event.srcElement.childNodes.length; i++){
			child = event.srcElement.childNodes(i);
			var offsetRight;
			if (child.className == "spreadsheet"){
				offsetRight = child.offsetLeft + getSpreadsheetWidth(child);
			}else{
				offsetRight = child.offsetLeft + child.offsetWidth;
			}

			if(offsetRight > maxRight){
				maxRight = offsetRight;
			}
			var offsetBottom = child.offsetTop + child.offsetHeight;
			if(offsetBottom > maxBottom){
				maxBottom = offsetBottom;
			}
		}

		if(event.srcElement.offsetWidth < maxRight){
			event.srcElement.style.pixelWidth = maxRight;
		}
		if(event.srcElement.offsetHeight < maxBottom){
			event.srcElement.style.pixelHeight = maxBottom;
		}
	
		break;

	case "spreadsheet":
	
		break;
		
	default:
		break;
	}
	containerResize(event.srcElement);
	
}

function getSpreadsheetWidth(elm){
	var row = elm.rows(0);
	var spacerWidth = row.cells(row.cells.length - 1).offsetWidth;
	return elm.offsetWidth - spacerWidth;
}

function containerResize(elm){
	var container = elm.parentElement;
	
	switch(container.className){
	case "HEADER":
	case "DETAIL":
	case "SUM":
	case "TOTAL":

		if(elm.offsetLeft < 0){
			elm.style.pixelLeft = 0;
		}
		if(elm.offsetTop < 0){
			elm.style.pixelTop = 0;
		}
	
	
		var offsetRight;
		if (elm.className == "spreadsheet"){
			offsetRight = elm.offsetLeft + getSpreadsheetWidth(elm);
		}else{
			offsetRight = elm.offsetLeft + elm.offsetWidth;
		}
		if(container.offsetWidth < offsetRight){
			container.style.pixelWidth = offsetRight;
		}
		
		var offsetBottom = elm.offsetTop + elm.offsetHeight;

		if(container.offsetHeight < offsetBottom){
			container.style.pixelHeight = offsetBottom;
		}
	
		break;

	case "subform":
		if(elm.offsetLeft < 0){
			elm.style.pixelLeft = 0;
		}
		if(elm.offsetTop < 0){
			elm.style.pixelTop = 0;
		}

		var offsetBottom = elm.offsetTop + elm.offsetHeight;

		if(container.offsetHeight < offsetBottom){
			container.style.pixelHeight = offsetBottom;
		}
		break;
	}

}

function Resizeend(){

	switch (event.srcElement.className) {
	case "columnspacer":
	
		var elm = event.srcElement.parentElement;
		if(elm.clientWidth > (event.srcElement.style.pixelWidth+1)){
			event.srcElement.style.pixelWidth = elm.clientWidth - 1;
		}
		
		break;
		
	case "rowspacer":
	
		var elm = event.srcElement.parentElement;
		if(elm.clientHeight > (event.srcElement.style.pixelHeight)){
			event.srcElement.style.pixelHeight = elm.clientHeight;
		}
		
		break;
		
	default:
		break;
	}
	Resume();
}

function DragEnd(){

//	alert(event.srcElement.className);
}

function DragStart(){

	if(document.queryCommandState("2D-Position")){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}

/*
	if (event.srcElement.tagName == "A"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}
*/
	if (event.srcElement.className == "subform"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}

	if (event.srcElement.className == "hcell"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}
	if (event.srcElement.className == "dcell"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}
	if (event.srcElement.className == "scell"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}
	if (event.srcElement.className == "tcell"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}

	if (event.srcElement.className == "rowspacer"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}
	if (event.srcElement.className == "columnspacer"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}

	if (event.srcElement.className == "ctrl"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}

	if (event.srcElement.className == "PREFIX"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}
	if (event.srcElement.className == "SUFFIX"){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;	
	}

	dragElm = event.srcElement;

}



function Resizestart(){

	switch (event.srcElement.className) {
	case "spreadsheet":
		event.cancelBubble = true;
		event.returnValue = false;
		break;		

	default:
		break;		
	}

}

function Dragenter(){
/*
	switch (event.srcElement.className) {
	case "subform":
		switch (dragElm.className){
		case "spreadsheet":
		case "HEADER":
		case "DETAIL":
		case "SUM":
		case "TOTAL":
		
			break;		
		default:

			event.cancelBubble = true;
			event.returnValue = false;
			return false;
		}
	
		break;

	default:
		break;		
	}
*/
	if(dragElm == null){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;

	}
	
	switch (dragElm.className){
	case "spreadsheet":
	case "HEADER":
	case "DETAIL":
	case "SUM":
	case "TOTAL":

		if(event.srcElement.className != "subform"){
			event.cancelBubble = true;
			event.returnValue = false;
			return false;
		}

		break;		

	case "subform":
		if(event.srcElement.id != "PAGE"){
			event.cancelBubble = true;
			event.returnValue = false;
			return false;
		}
		break;		
	
	case "hline":
	case "vline":
	case "box":
		if(event.srcElement.id == "PAGE"){
			return true;
		}
		switch (event.srcElement.className){
		case "HEADER":
		case "DETAIL":
		case "SUM":
		case "TOTAL":
		case "subform":
		case "box":
			break;		
		default:
			event.cancelBubble = true;
			event.returnValue = false;
			return false;
		}
	
	default:

		break;		
	}

}

function Dragleave(){
//	if (event.srcElement.className == "spreadsheet") {
//		alert("Dragleave");
//	}

}


function spanFocus(){
	event.srcElement.parentElement.style.overflow = "visible";

}
function spanBlur(){
	event.srcElement.parentElement.style.overflow = "hidden";

}

function placeElement(elm,container,offsetX,offsetY){
		
	//alert(elm.className);
	
	if(elm.className == "spreadsheet"
		|| elm.className == "HEADER"
		|| elm.className == "DETAIL"
		|| elm.className == "SUM"
		|| elm.className == "TOTAL"){

		if(container.className != "subform"){
			return false;
		}
	}
	if(elm.className == "subform"){

		if(container.id != "PAGE"){
			return false;
		}
	}

	switch (elm.className){
	case "hline":
	case "vline":
	case "box":
		if(container.id != "PAGE"){
			switch (container.className){
			case "HEADER":
			case "DETAIL":
			case "SUM":
			case "TOTAL":
			case "subform":
			case "box":
				break;		
			default:
				return false;
			}
		}
	}
	
	
	//cell
	if (container.className == "hcell" || container.className == "dcell" || container.className == "scell" || container.className == "tcell"){
			
		var td = container;
		var cellIndex = td.cellIndex;
		var row = td.parentElement;
		var rowIndex = row.rowIndex;
		var table = row.parentElement.parentElement;

		elm.style.position = "static"
		elm.style.display = "inline"
		container.insertAdjacentElement("beforeEnd",elm);

		codeEdit(elm);
		
		//2009-11-11 add
		switch (elm.className) {
		case "textfield":
			break;
		default:
			Resume();
			break;
		}
		
		//明細行への項目追加
		if(container.className == "dcell"){
//			alert(elm.name);
//			alert(rowIndex);
			//何番目の明細
			var detailIndex = 0;
			for (var i=1; i <= rowIndex; i++){
//				alert(table.rows(i).className);
				if(table.rows(i).className == "TableBody"){
					detailIndex++;
				}
			}
			var headerIndex = 0;
			for (var i=1; i <= rowIndex; i++){
//				alert(table.rows(i).className);
				if(table.rows(i).className == "TableHead"){
					headerIndex++;
				}
				if(headerIndex == detailIndex){
//					alert(table.rows(i).className);
					var titleCell = table.rows(i).cells(cellIndex);
					if(titleCell.childNodes.length == 0){
						var title = getNewTextField(elm.name);
						placeElement(title,table.rows(i).cells(cellIndex),offsetX,offsetY);
						title.focus();
					}
					break;
				}
			}
			
		}
		
		return;
	}
	
	//container
	if(container.id == "PAGE" || container.className == "subform"
		|| container.className == "HEADER"
		|| container.className == "DETAIL"
		|| container.className == "SUM"
		|| container.className == "TOTAL"){
	
	}else{
		var parent = container.parentElement;
		if (parent.className == "hcell" || parent.className == "dcell" || parent.className == "scell" || parent.className == "tcell"){
			placeElement(elm,parent,event.offsetX,event.offsetY);

			return;
		}else{
			placeElement(elm,parent,container.offsetLeft+event.offsetX,container.offsetTop+event.offsetY);
			
			return;
		}
	}

/*	
	if(container.className == "textfield"){

		var parent = container.parentElement;
		if (parent.className == "hcell"){
			placeElement(elm,parent,event.offsetX,event.offsetY);
			return;
		}
		if (parent.className == "dcell"){
			placeElement(elm,parent,event.offsetX,event.offsetY);
			return;
		}
		if (parent.className == "scell"){
			placeElement(elm,parent,event.offsetX,event.offsetY);
			return;
		}
		if (parent.className == "tcell"){
			placeElement(elm,parent,event.offsetX,event.offsetY);
			return;
		}
		return;
	}
	
	if(container.className == "box" || container.className == "hline" || container.className == "vline"){
		var parent = container.parentElement;
		placeElement(elm,parent,container.offsetLeft+event.offsetX,container.offsetTop+event.offsetY);
		return;
	}
*/	
	
	elm.style.position = "absolute"
	elm.style.display = "block"
	dropOffsetX = offsetX;
	dropOffsetY = offsetY;
	
	container.insertAdjacentElement("beforeEnd",elm);

	elm.style.pixelLeft = dropOffsetX;
	elm.style.pixelTop = dropOffsetY;

	codeEdit(elm);
//	elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;
	

	//2009-11-11 add
	switch (elm.className) {
	case "textfield":
		break;
	default:
		Resume();
		break;
	}
			

}

function codeEdit(elm){
	if(elm.SVF_CODE_EDIT > ""){
		
		var format = "";
		var formatLength = 0;
		var oSpans = elm.getElementsByTagName("SPAN")
		for(var i=0; i< oSpans.length;i++){
			if(oSpans(i).className == "FORMAT"){
				var codes = elm.SVF_CODE_EDIT.split(";");
				for(var j=0; j<codes.length-1; j++){
					var entry = codes[j].split(":");
					oSpans(i).innerText = entry[1];
					if (oSpans(i).offsetWidth > formatLength){
						formatLength = oSpans(i).offsetWidth;

						format = entry[1];
					}
				}
				oSpans(i).innerText = format;
				elm.style.pixelWidth = formatLength;
			}
		}
	}
}

function Drop(){

	var popup = document.getElementById("POPUP");
	if(popup != null){	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}

	newItem = null;
	newElm = null;

//	alert(dragElm);
	
	if(dragElm == null){
		event.cancelBubble = true;
		event.returnValue = false;
		return false;

	}
//	dragElm.style.visibility = "visible";
//	event.srcElement.style.display = "block";

	var offsetX = event.offsetX;
	var offsetY = event.offsetY;
	
//	alert(dragElm.tagName);
	if(dragElm.tagName == "COLUMN"){
		dragElm = getNewDataField(dragElm);
		offsetX = event.offsetX;
	}else{
		switch (dragElm.className) {
		case "subform":
//			alert(event.srcElement.style.pixelWidth);
//			dragElm.style.width = event.srcElement.style.width;
			break;
		case "HEADER":
		case "DETAIL":
		case "SUM":
		case "TOTAL":
		case "hline":
			if(event.srcElement.id != "PAGE"){
				offsetX = 0;
				dragElm.style.width = event.srcElement.style.width;
			}
			break;
		case "vline":
			if(event.srcElement.id != "PAGE"){
				offsetY = 0;
				dragElm.style.height = event.srcElement.style.height;
			}
			break;
		case "queryField":	
			var querys = document.getElementById("QUERY");
			querys.insertAdjacentElement("beforeEnd",dragElm);
			dragElm = getNewDataField(dragElm);
		default:
//			offsetX = event.offsetX;
		}
	}

//	placeElement(dragElm,event.srcElement,offsetX,event.offsetY);
	placeElement(dragElm,event.srcElement,offsetX,offsetY);

	//2009-11-11-del
	//Resume();

	switch (dragElm.className) {
	case "textfield":
		if(dragElm.innerText == ""){
			dragElm.focus();
		}
		break;
//	case "datafield":
//		addQueryField(dragElm);
//		break;
	default:
		break;
	}
	
	dragElm = null;
	event.cancelBubble = true;
	event.returnValue = false;
	return false;

}

function addQueryField(elm){
	
	var queryContainer = document.getElementById("QUERY");
	
	var querys = queryContainer.getElementsByTagName("DIV");
	var bExist = false;
//	alert(querys.length);
	for(var i=0; i <querys.length; i++){
		if(querys(i).SVF_FIELD_ID == elm.id){
			bExist = true;
			break;
		}
	}
//	alert(bExist);
	if(!bExist){
		var queryField = document.createElement("DIV");
		queryField.className = "queryField";
		queryField.SVF_FIELD_ID = elm.id;
		queryField.SVF_DD_ARIAS = queryField.SVF_DD_ARIAS;
		queryContainer.insertAdjacentElement("beforeEnd",queryField);

	}
}

function TableMouseEnter(){

	var bAddCell = false
	
	
	for(var i=0 ; i < event.srcElement.rows.length; i++){

		var row = event.srcElement.rows(i);

		var cell = row.cells(row.cells.length - 1).firstChild;
		
		if (cell.childNodes.length > 0){
			bAddCell = true;
			break;
		}
	}
	
	if (bAddCell){
	
		addColumn(event.srcElement,-1);
	}
	
}
function TableMouseLeave(){

	var bAddCell = false
	
	for(var i=0 ; i < event.srcElement.rows.length; i++){

		var row = event.srcElement.rows(i);

		if(row.cells.length == 2){
			bAddCell = true;
			break;
		}
		var cell = row.cells(row.cells.length - 1);
		if (cell.firstChild.childNodes.length > 0){
			bAddCell = true;
			break;
		}
	}
	
	if (!bAddCell){
		for(var i=0 ; i < event.srcElement.rows.length; i++){
		
			var row = event.srcElement.rows(i);
		
			var oNewCell = row.deleteCell();
			
		}
	}
	
//	event.srcElement.border = 0;

}



function MouseDown(){
	
	var popup = document.getElementById("POPUP");
	if(popup != null){	
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}
	
	if(event.srcElement.id == "contextmenu"){
		return true;
	}
	if(event.srcElement.className == "menuItem"){
		return true;
	}

	if(event.srcElement.tagName != "BODY"){
		closeContextmenu();
	}
	if(event.srcElement.tagName == "BODY" || event.srcElement.id == "PAGE"){
		var sel = document.selection;
		if (sel.type.toLowerCase() == "control"){
			rng = sel.empty();
		}
//		event.srcElement.focus();
	}

	
	if(newItem != null){
		insertNewItem();
		//2009-11-11 del
		//Resume();
		event.cancelBubble = true;
		event.returnValue = false;
		return false;
	}

	
	switch (event.srcElement.className) {
	case "hcell":
	case "dcell":
	case "scell":
	case "tcell":
	
		var rng = document.body.createControlRange();		
		for (var i=0 ; i <rng.length; i++){
			rng.remove(i);
		}
		rng.add(event.srcElement.parentElement.parentElement.parentElement);
		rng.select();

		event.cancelBubble = true;
		event.returnValue = false;
		return false;
			
	default:
		break;
	}
	
	return true;

}

function MouseMove(){

	if(newElm != null){
		alignNewElement();
	}

}

function MouseUp(){

	if(newElm != null){
		switch (newItem) {
		case "TextField":
			newElm.focus();
			break;
		default:
			//2009-11-11 del
			//Resume();
			break;
		}
	}

	newItem = null;
	newElm = null;

}


function isFullContained(elm, startX, startY, endX, endY){

	var sX,eX,sY,eY;
	if (startX < endX){
		sX = startX;
		eX = endX;
	}else{
		sX = endX;
		eX = startX;
	}
	if (startY < endY){
		sY = startY;
		eY = endY;
	}else{
		sY = endY;
		eY = startY;
	}

	if (elm.style.pixelLeft < sX){
		return false;
	}
	if (elm.style.pixelLeft + elm.style.pixelWidth > eX){
		return false;
	}
	if (elm.style.pixelTop < sY){
		return false;
	}
	if (elm.style.pixelTop + elm.style.pixelHeight > eY){
		return false;
	}
	return true;

}

function isContained(elm, startX, startY, endX, endY){

	var sX,eX,sY,eY;
	if (startX < endX){
		sX = startX;
		eX = endX;
	}else{
		sX = endX;
		eX = startX;
	}
	if (startY < endY){
		sY = startY;
		eY = endY;
	}else{
		sY = endY;
		eY = startY;
	}

	if (elm.style.pixelLeft > eX){
		return false;
	}
	if (elm.style.pixelLeft + elm.style.pixelWidth < sX){
		return false;
	}
	if (elm.style.pixelTop > eY){
		return false;
	}
	if (elm.style.pixelTop + elm.style.pixelHeight < sY){
		return false;
	}
	return true;

}



function refSelectFocus(){

	event.srcElement.parentElement.style.overflow = "visible";

}
function refSelectBlur(){

	if(event.srcElement.value >""){
		var oField = document.getElementById(event.srcElement.value);
	
		var oRef = document.createElement("A");
		oRef.id = oField.id;
		oRef.name = oField.name;
		oRef.className = "ref";

		var format;
		switch (oField.SVF_DATA_TYPE) {
//		case "integer":
//			format = ""
//			for(var i=0; i<oField.SVF_LENGTH; i++){
//				format += "9";
//			}
//			break;
//		case "real":
//			format = ""
//			for(var i=0; i<oField.SVF_LENGTH; i++){
//				format += "9";
//			}
//			format += ".";
//			for(var i=0; i<oField.SVF_LENGTH_DP; i++){
//				format += "9";
//			}
//			break;
		case "number":
			format = ""
			for(var i=0; i<oField.SVF_DISPLENGTH; i++){
				format += "9";
			}
			if(oField.SVF_DISPLENGTH_DP > 0){
				format += ".";
				for(var i=0; i<oField.SVF_DISPLENGTH_DP; i++){
					format += "9";
				}
			}
			break;
		default:
			format = ""
			for(var i=0; i<oField.SVF_LENGTH; i++){
				format += "X";
			}
			break;
		}

		oRef.innerHTML = format;

		oRef.oncontrolselect = Cancel;
		oRef.contentEditable = false;
		event.srcElement.insertAdjacentElement("afterEnd",oRef);
	}

	var oTestField = event.srcElement.parentElement;
	oTestField.style.overflow = "hidden";
	oTestField.removeChild(event.srcElement);

}


function Keydown(){

	window.event.cancelBubble = true;
	
	var code = fromCharCode(window.event.keyCode);
	
	if (code == 'enter'){
		event.returnValue=false;
		return false;
	}
	
	var sel = document.selection;

	if (sel.type.toLowerCase() == "control"){
	
		var rng = sel.createRange();
		for(var i=0; i<rng.length ;i++){
			if(rng(i).className == "columnspacer"){
				event.returnValue=false;
				return false;
			}
			if(rng(i).className == "rowspacer"){
				event.returnValue=false;
				return false;
			}
		}
	
		switch (code) {
		case "left":
		case "up":
		case "right":
		case "down":
			for(var i=0; i<rng.length ;i++){
				if(rng(i).style.position == "absolute"){
					moveItem(rng(i),code);
				}
			}
			return false;
		case "del":
			
			//削除時にプロパティ項目から前回値を消去する処理
			//選択項目を取得（複数件選択時を考慮）
			var sel = document.selection;
			if (sel.type.toLowerCase() == "control"){
				//ControlRangeの集まりであるControlRangeCollectionを取得する
				var rng = sel.createRange();
				//選択された項目の数だけ回るループ
				for(var i=0; i<rng.length ;i++){
					//項目ID
					var delId = rng(i).id;
					//クラス名
					var className = rng(i).className;
					
					//子要素に削除できるフィールドが存在する項目（ヘッダ、明細、合計、総計、テーブル、サブフォーム）を選択していた場合、その子要素全てを選択された項目のIDとして、
					//ループを回す
					if(className == "HEADER" || className == "DETAIL" || className == "SUM" || className == "TOTAL" || className == "subform" || className == "spreadsheet"){
						var childList = rng(i).children;
						if(childList.length != 0){
							//子要素が存在する場合、子要素ごとに削除を行う
							deleteChildren(childList);
						}
						
						//対象がヘッダー・合計の場合は、フィールドのプロパティ項目に設定されていないか確認する必要がある
						if(className == "HEADER" || className == "SUM"){
							deleteProperty(delId);
						
						//対象がサブフォームの場合は、溢れ印刷先も確認する必要がある
						}else if(className == "subform"){
							//画面上のサブフォーム項目取得
							var elms = document.body.getElementsByTagName("DIV");
							for (var j=0; j<elms.length; j++){
								if(elms(j).className == "subform"){
									
									//★溢れ印刷先SVF_LINK_SUBFORM_NAME
									if(elms(j).SVF_LINK_SUBFORM_NAME != null){
										var property = elms(j).SVF_LINK_SUBFORM_NAME;
										if(matchProperty(property,delId)){
											elms(j).SVF_LINK_SUBFORM_NAME = "";
											//elms(j).removeAttribute("SVF_LINK_SUBFORM_NAME");
										}
									}
								}
							}
						}
					}else{
						//選択した項目が、フィールドのような子要素が想定されない項目の場合、選択した項目IDでプロパティ項目の削除処理を行う
						deleteProperty(delId);
					}
				}
			}
			Resume();
			return true;
		}
	}
	if(window.event.srcElement.className == "textfield"){

		return true;
	}
	
	return false;
}


function deleteProperty(delId){

	//画面上のフィールド項目取得
	var fields = getField();
	for (var i=0; i<fields.length; i++){
		//画面上に存在するフィールドの数だけ回るループ
		//以下で削除時にクリアする必要のあるフィールドのプロパティ項目★に選択された項目が含まれていないかを確認する
		
		//★条件左辺SVF_ALT_IF_LEFT
		if(fields[i].SVF_ALT_IF_LEFT != null){
		//条件左辺に値が設定されていた場合、
			var property = fields[i].SVF_ALT_IF_LEFT;
			if(matchProperty(property,delId)){
				//条件左辺削除時は「条件を指定して値を変更」のプロパティ項目がすべて削除される
				fields[i].removeAttribute("SVF_ALT_IF_LEFT");
				fields[i].removeAttribute("SVF_ALT_IF_OPERATOR");
				fields[i].removeAttribute("SVF_ALT_IF_RIGHT");
				fields[i].removeAttribute("SVF_ALT_FILED");
				fields[i].removeAttribute("SVF_ALT_OPERATOR");
				fields[i].removeAttribute("SVF_ALT_FILED_RIGHT");
			}
		}
		
		//★条件右辺SVF_ALT_IF_RIGHT
		if(fields[i].SVF_ALT_IF_RIGHT != null){
		//条件右辺に値が設定されていた場合、
			var property = fields[i].SVF_ALT_IF_RIGHT;
			if(matchProperty(property,delId)){
				//条件右辺削除時はプロパティ項目は残る
				fields[i].SVF_ALT_IF_RIGHT = "";
			}
		}
		
		//★計算式左辺SVF_ALT_FILED
		if(fields[i].SVF_ALT_FILED != null){
		//計算式左辺に値が設定されていた場合、
			var property = fields[i].SVF_ALT_FILED;
			if(matchProperty(property,delId)){
				//計算式左辺削除時はプロパティ項目は残る
				fields[i].SVF_ALT_FILED = "";
			}
		}
		
		//★計算式右辺SVF_ALT_FILED_RIGHT
		if(fields[i].SVF_ALT_FILED_RIGHT != null){
		//計算式右辺に値が設定されていた場合、
			var property = fields[i].SVF_ALT_FILED_RIGHT;
			if(matchProperty(property,delId)){
				//計算式右辺削除時はプロパティ項目は残る
				//計算式の演算式も削除する
				fields[i].SVF_ALT_FILED_RIGHT = "";
				fields[i].SVF_ALT_OPERATOR = "";
			}
		}
		
		//★ヘッダSVF_HEADER_RECORD_NAME
		if(fields[i].SVF_HEADER_RECORD_NAME != null){
		//ヘッダに値が設定されていた場合、
			var property = fields[i].SVF_HEADER_RECORD_NAME;
			if(matchProperty(property,delId)){
				//ヘッダ削除時はプロパティ項目が削除される
				fields[i].removeAttribute("SVF_HEADER_RECORD_NAME");
			}
		}
		
		//★合計SVF_SUM_RECORD_NAME
		if(fields[i].SVF_SUM_RECORD_NAME != null){
		//合計に値が設定されていた場合、
			var property = fields[i].SVF_SUM_RECORD_NAME;
			if(matchProperty(property,delId)){
				//合計削除時はプロパティ項目が削除される
				fields[i].removeAttribute("SVF_SUM_RECORD_NAME");
			}
		}
	}
}


function deleteChildren(childList){

	//子要素を取得する処理
	for(var i=0 ; i <childList.length; i++){
		//DIVタグのエレメントを抜き出す
		var elms = childList(i).getElementsByTagName("DIV");
		//クラス名がフィールド、ヘッダー、合計のエレメントを抜き出す
		for(var j=0 ; j <elms.length; j++){
			if (elms(j).className == "datafield" || elms(j).className == "field" || elms(j).className == "calcfield" || elms(j).className == "HEADER" || elms(j).className == "SUM"){
				//項目のIDをプロパティ項目の削除処理へ渡す
				deleteProperty(elms(j).id);
			}
		}
		
		//TRタグのエレメントを抜き出す（テーブルのヘッダ・合計を取得するため）
		elms = childList(i).getElementsByTagName("TR");
		for(var k=0 ; k <elms.length; k++){
			if (elms(k).className == "TableHead" || elms(k).className == "TableSum"){
				//項目のIDをプロパティ項目の削除処理へ渡す
				deleteProperty(elms(k).id);
			}
		}
		
		//childList(i)自体は上記で取得できないため、ここで確認する
		if (childList(i).className == "datafield" || childList(i).className == "field" || childList(i).className == "calcfield" ||childList(i).className == "HEADER" || childList(i).className == "SUM"){
			deleteProperty(childList(i).id);
		}
	}
}


function matchProperty(property,delId){

	var result =property.indexOf("VAL(");
	//プロパティ項目に関数式がついているかを確認
	if(result != -1){
		//関数式がついている場合
		delId = "VAL(" + delId + ")";
		//削除対象のIDにも関数式を設定する
	}
	if(property == delId){
		return true;
	}else{
		return false;
	}
}	


function Keyup(){
	
	if(window.event.srcElement.className == "textfield"){

		//障害NO:8722 対策
		for(var i=0; i<document.links.length ;i++){
			document.links[i].outerHTML = document.links[i].innerText;
		}

	}

	
	var sel = document.selection;

	if (sel.type.toLowerCase() == "control"){
		var code = fromCharCode(window.event.keyCode);
	
		switch (code) {
		case "left":
		case "up":
		case "right":
		case "del":
			Resume();
		}
	}

}

//***設計項目をテンキーで移動した場合に発生するイベント ***//
//★EXCELグリッド対応あり
function moveItem(elm, direction){

	//グリッドコンボボックスのidは、"edit1_grid_size"で設定されている。
	//"edit1"はjsp側から渡ってきている名前でほぼ固定だが、一応動的に対応できるようにしておく。
	var wStart = oToolbars.id.indexOf('_');
	var oid = oToolbars.id.substring(0,wStart);
	
	//グリッドコンボボックスのオブジェクトを取得
	var option = oToolbars.document.getElementById(oid + "_grid_size");
	var gridSelect;
	for(var i=0;i<option.length;i++){
		if( option[ i ].selected ){
			//選択されているグリッド値を取得
			gridSelect = option[ i ].value;
			break;
		}
	}

	//グリッドのサイズを取得
	//※選択できるグリッドの種類が増えると、修正する必要がある。
	var gridSize = 0;
	if(gridSelect == 'url(images/grid_blue_20.png)'){
		gridSize = 20;
	}else{
		gridSize = 1;
	}
	
	
	
	//対象の項目がテーブルの場合、上枠の10ピクセル分を考慮する必要がある
	var tableHeight = 0;
	if(elm.className == "spreadsheet"){
		tableHeight = 10;
	}


	switch (direction) {
	case "left":
		if(elm.style.pixelLeft > 0){
			elm.style.pixelLeft = elm.style.pixelLeft - gridSize;
		}
		break;
	case "up":
		if(elm.style.pixelTop > 0){
			elm.style.pixelTop = elm.style.pixelTop - gridSize;
		}
		break;
	case "right":
		if(elm.offsetLeft + elm.offsetWidth < elm.parentElement.offsetWidth){
			elm.style.pixelLeft = elm.style.pixelLeft + gridSize;
		}
		break;
	case "down":
		if(elm.offsetTop + elm.offsetHeight < elm.parentElement.offsetHeight){
			elm.style.pixelTop = elm.style.pixelTop + gridSize;
		}
		break;
		
	case "":
		//フィールドが選択された場合の位置調整

		if(gridSize == 1){
			//デフォルトのグリッドを使用している場合、位置調整は行わない
		}else{
			//左上から起点からの座標を取得（テーブルの場合は上端の幅を考慮）
			var posTop  = elm.style.pixelTop + tableHeight;
			//左端から起点からの座標を取得
			var posLeft = elm.style.pixelLeft;
			
			//現在座標からグリッドのサイズで割りきれる位置に調整
			var criterionTop = posTop - (posTop % gridSize);
			elm.style.pixelTop = criterionTop - tableHeight ;
			
			var criterionLeft = posLeft - (posLeft % gridSize);
			elm.style.pixelLeft = criterionLeft ;
			
			
		}

		break;
	}
	
}

function getSelectedItems()
{
	var sel;
	var rng = null;

	var rng = document.body.createControlRange();
//	alert(rng.length);
	for (var i=0 ; i <rng.length; i++){
		rng.remove(i);
	}

	sel = document.selection;

	if (sel.type.toLowerCase() == "control"){
//		alert("control");
		rng = sel.createRange();
//		alert(rng.length);
	}
//	rng.select();
	return rng;
}

/*
function moveLeft()
{
	ItemsMove(-1,0);
}

function moveRight()
{
	ItemsMove(1,0);
}

function moveUp()
{
	ItemsMove(0,-1);
}

function moveDown()
{
	ItemsMove(0,1);
}
*/

// キーコード文字列変換関数
function fromCharCode(c){

	//alert(c + ":" + ch);

    var ch=String.fromCharCode(c);
    // KeyCode 変換( Windows )
    switch(c){
    case   8: ch='back';  break;
    case   9: ch='tab';  break;
    case  13: ch='enter'; break;
    case  27: ch='esc';   break;
    case  37: ch='left';  break;
    case  38: ch='up';    break;
    case  39: ch='right'; break;
    case  40: ch='down';  break;
    case  46: ch='del';   break;
    case 106: ch='*';     break;
    case 107: ch='+';     break;
    case 109: ch='-';     break;
    case 110: ch='.';     break;
    case 111: ch='/';     break;
    case 186: ch=(event.shiftKey?'*':':');  break;
    case 187: ch=(event.shiftKey?'+':';');  break;
    case 188: ch=(event.shiftKey?'<':',');  break;
    case 189: ch=(event.shiftKey?'=':'-');  break;
    case 190: ch=(event.shiftKey?'>':'.');  break;
    case 191: ch=(event.shiftKey?'?':'/');  break;
    case 192: ch=(event.shiftKey?'`':'@');  break;
    case 219: ch=(event.shiftKey?'{':'[');  break;
    case 220: ch=(event.shiftKey?'|':'\\'); break;
    case 221: ch=(event.shiftKey?'}':']');  break;
    case 222: ch=(event.shiftKey?'~':'^');  break;
    case 226: ch=(event.shiftKey?'_':'\\'); break;
    default:
      if(c>=96 && c<=105) ch='0123456789'.charAt(c-96);
      else if(event.shiftKey){
        if(ch>=0 && ch<=9) ch=' !"#$%&\'()'.charAt(ch);
        else               ch=ch.toUpperCase();
      } else ch=ch.toLowerCase();
      break;
    }
    return ch;
}


