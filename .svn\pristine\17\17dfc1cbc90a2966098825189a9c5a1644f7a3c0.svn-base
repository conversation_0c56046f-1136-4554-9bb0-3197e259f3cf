<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ka/Kab01103.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>施設詳細</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY  >
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Kab01103.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kab01103.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kab01103.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kab01103.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Kab01103.doReturnDispAction}"
				rendered="#{pc_Kab01103.propReturnDisp.rendered}">
			</hx:commandExButton> <!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="880">
				<TBODY>
					<TR>
						<TH class="v_a" width="200"><h:outputText styleClass="outputText"
							id="lblSistNo" value="#{pc_Kab01103.propSistNo.name}"></h:outputText></TH>
						<TD width="680"><h:outputText
							styleClass="outputText" id="htmlSistNo" 
							value="#{pc_Kab01103.propSistNo.stringValue}"
							title="#{pc_Kab01103.propSistNo.stringValue}">
						</h:outputText></TD>
					</TR>
				</TBODY>				
				<TBODY>
					<TR class="clear_border">
						<TD width="200" style="background-color:transparent;"></TD>
						<TD width="680" style="background-color:transparent;"></TD>					
					</TR>
					<TR>
						<TH class="v_b"><h:outputText styleClass="outputText"
							id="lblSistName" value="#{pc_Kab01103.propSistName.name}"></h:outputText></TH>
						<TD >	
							<h:outputText id="htmlSistName" styleClass="outputText"
								value="#{pc_Kab01103.propSistName.stringValue}" 
								title="#{pc_Kab01103.propSistName.stringValue}"></h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblSistNameRyak" value="#{pc_Kab01103.propSistNameRyak.name}"></h:outputText></TH>
						<TD >	
							<h:outputText id="htmlSistNameRyak" styleClass="outputText"
							value="#{pc_Kab01103.propSistNameRyak.stringValue}"
							title="#{pc_Kab01103.propSistNameRyak.stringValue}"></h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblSetchiBashoCd"
							value="#{pc_Kab01103.propSetchiBashoCd.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;">
							<h:outputText styleClass="outputText" id="htmlSetchiBashoCd"
							value="#{pc_Kab01103.propSetchiBashoCd.stringValue}"
							title="#{pc_Kab01103.propSetchiBashoCd.stringValue}"></h:outputText>
							</DIV>
						</TD>						
					</TR>
					<TR>
						<TH class="v_e"><h:outputText styleClass="outputText"
							id="lblYotoCd" value="#{pc_Kab01103.propYotoCd.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;">
							<h:outputText styleClass="outputText" id="htmlYotoCd" 
							value="#{pc_Kab01103.propYotoCd.stringValue}"
							title="#{pc_Kab01103.propYotoCd.stringValue}"></h:outputText>
							</DIV>
						</TD>						
					</TR>
					<TR>
						<TH class="v_f"><h:outputText styleClass="outputText"
							id="lblJissokuMenseki" value="#{pc_Kab01103.propJissokuMenseki.name}"></h:outputText></TH>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">						
							<TR>
							<TD width="100" style="text-align: right">
							<h:outputText id="htmlJissokuMenseki" styleClass="outputText"
							style="text-align: right;"
							value="#{pc_Kab01103.propJissokuMenseki.stringValue}">
							</h:outputText>
							</TD>
							</TR>
						</TABLE>	
						</TD>
					</TR>
					<TR>
						<TH class="v_g"><h:outputText styleClass="outputText"
							id="lblTokiMenseki" value="#{pc_Kab01103.propTokiMenseki.name}"></h:outputText></TH>
						<TD >
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">						
							<TR>
							<TD width="100" style="text-align: right">
							<h:outputText id="htmlTokiMenseki" styleClass="outputText"
							value="#{pc_Kab01103.propTokiMenseki.stringValue}" >
							</h:outputText>
							</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblKoseihodoMenseki" value="#{pc_Kab01103.propKoseihodoMenseki.name}"></h:outputText></TH>
						<TD >
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="clear_border" width="100%">						
							<TR>
							<TD width="100" style="text-align: right">
							<h:outputText id="htmlKoseihodoMenseki" styleClass="outputText"
							style="text-align: right;"
							value="#{pc_Kab01103.propKoseihodoMenseki.stringValue}">
							</h:outputText>
							</TD>
							</TR>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText styleClass="outputText"
							id="lblBmnCd" value="#{pc_Kab01103.propBmnCd.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;">
							<h:outputText styleClass="outputText" id="htmlBmnCd" 
							value="#{pc_Kab01103.propBmnCd.stringValue}"
							title="#{pc_Kab01103.propBmnCd.stringValue}"></h:outputText>
							</DIV>
						</TD>						
					</TR>
					<TR>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblBmnAnbPtn" value="#{pc_Kab01103.propBmnAnbPtn.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;">
							<h:outputText styleClass="outputText" id="htmlBmnAnbPtn" 
							value="#{pc_Kab01103.propBmnAnbPtn.stringValue}"
							title="#{pc_Kab01103.propBmnAnbPtn.stringValue}"></h:outputText>
							</DIV>
						</TD>						
					</TR>
					<TR>
						<TH class="v_d"><h:outputText styleClass="outputText"
							id="lblShoyukenKbn"
							value="#{pc_Kab01103.propShoyukenKbn.name}"></h:outputText></TH>
						<TD >
							<h:outputText id="htmlShoyukenKbn" styleClass="outputText"
							value="#{pc_Kab01103.propShoyukenKbn.stringValue}"
							title="#{pc_Kab01103.propShoyukenKbn.stringValue}" ></h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_e"><h:outputText styleClass="outputText"
							id="lblHoteiTaiyoNensuCd"
							value="#{pc_Kab01103.propHoteiTaiyoNensuCd.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;">
							<h:outputText styleClass="outputText" id="htmlHoteiTaiyoNensuCd" 
							value="#{pc_Kab01103.propHoteiTaiyoNensuCd.stringValue}"
							title="#{pc_Kab01103.propHoteiTaiyoNensuCd.stringValue}"></h:outputText>
							</DIV>
						</TD>	
					</TR>
					<TR>
						<TH class="v_f"><h:outputText styleClass="outputText"
							id="lblBiko" value="#{pc_Kab01103.propBiko.name}"></h:outputText></TH>
						<TD nowrap>
							<DIV style="width:680px;white-space:nowrap;overflow:hidden;display:block;"> 	
							<h:outputText id="htmlBiko" styleClass="outputText"
							value="#{pc_Kab01103.propBiko.stringValue}"
							title="#{pc_Kab01103.propBiko.stringValue}" ></h:outputText>
							</DIV>
						</TD>
					</TR>
					<TR>
						<TH class="v_g"><h:outputText styleClass="outputText"
							id="lblKyoshituRiyoFlg"
							value="#{pc_Kab01103.propKyoshituRiyoFlg.name}"></h:outputText></TH>
						<TD >
							<h:outputText id="htmlKyoshituRiyoFlg" styleClass="outputText"
							value="#{pc_Kab01103.propKyoshituRiyoFlg.stringValue}"
							title="#{pc_Kab01103.propKyoshituRiyoFlg.stringValue}" ></h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_g"><h:outputText styleClass="outputText"
							id="lblYukoMukoFlg"
							value="#{pc_Kab01103.propYukoMukoFlg.name}"></h:outputText></TH>
						<TD >
							<h:outputText id="htmlYukoMukoFlg" styleClass="outputText"
							value="#{pc_Kab01103.propYukoMukoFlg.stringValue}"
							title="#{pc_Kab01103.propYukoMukoFlg.stringValue}" ></h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>			

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden 
				id="htmlGyomuNendoHidden"
				value="#{pc_Kab01103.propGyomuNendoHidden.integerValue}">
			</h:inputHidden>			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

