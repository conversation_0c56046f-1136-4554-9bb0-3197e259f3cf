<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghi00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>経理インターフェース仕訳作成</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">	

<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	// 起票予算単位ロストフォーカス
	funcYosanTaniAJAX();
}
function func_2(thisObj, thisEvent) {
	// 会計年度ロストフォーカス
	funcYosanTaniAJAX();
}

function funcYosanTaniAJAX() {
	// 起票予算単位名称の取得
	var kaikeiNendo = document.getElementById("form1:htmlKaikeiNendo").value;
	var ysnTCd = document.getElementById("form1:htmlKihyoYsnTCd").value;;
	var ysnTName = "form1:htmlKihyoYsnTName";
	funcAjaxSetYsnTCd(kaikeiNendo, ysnTCd, ysnTName);
}


function onloadFunc() {
	funcYosanTaniAJAX();
}

window.attachEvent("onload", onloadFunc);

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ghi00601.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ghi00601.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ghi00601.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ghi00601.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- レイアウト崩れ対応の全角スペース -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->

<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
	<TBODY>					
		<TR>
			<TD>
				<TABLE width="700px" border="0" cellpadding="0" cellspacing="0" class="table">
					<TBODY>
						<TR>
							<TH class="v_a" width="200px">
								<h:outputText styleClass="outputText" id="lblKaikeiNendo"
										style="#{pc_Ghi00601.propKaikeiNendo.labelStyle}"
										value="#{pc_Ghi00601.propKaikeiNendo.labelName}"></h:outputText>
							</TH>
							<TD width="500px"><h:inputText styleClass="inputText"
										id="htmlKaikeiNendo"
										disabled="#{pc_Ghi00601.propKaikeiNendo.disabled}"
										readonly="#{pc_Ghi00601.propKaikeiNendo.readonly}"
										rendered="#{pc_Ghi00601.propKaikeiNendo.rendered}"
										style="#{pc_Ghi00601.propKaikeiNendo.style}"
										value="#{pc_Ghi00601.propKaikeiNendo.dateValue}" size="4" onblur="return func_2(this, event);">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
						</TR>
						<TR>
							<TH class="v_c">
								<h:outputText styleClass="outputText" id="lblIkkatsuKbn"
										style="#{pc_Ghi00601.propIkkatsuKbn.labelStyle}"
										value="#{pc_Ghi00601.propIkkatsuKbn.labelName}"></h:outputText>
							</TH>
							<TD>
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlIkkatsuKbn"
										disabled="#{pc_Ghi00601.propIkkatsuKbn.disabled}"
										readonly="#{pc_Ghi00601.propIkkatsuKbn.readonly}"
										rendered="#{pc_Ghi00601.propIkkatsuKbn.rendered}"
										style="#{pc_Ghi00601.propIkkatsuKbn.style}"
										value="#{pc_Ghi00601.propIkkatsuKbn.stringValue}">
										<f:selectItems value="#{pc_Ghi00601.propIkkatsuKbn.list}" />
									</h:selectOneMenu></TD>
						</TR>
						<TR>
							<TH class="v_c">
								<h:outputText styleClass="outputText" id="lblKihyoDate"
										style="#{pc_Ghi00601.propKihyoDate.labelStyle}"
										value="#{pc_Ghi00601.propKihyoDate.labelName}"></h:outputText>
							</TH>
							<TD>
							<h:inputText styleClass="inputText" id="htmlKihyoDate"
										disabled="#{pc_Ghi00601.propKihyoDate.disabled}"
										readonly="#{pc_Ghi00601.propKihyoDate.readonly}"
										rendered="#{pc_Ghi00601.propKihyoDate.rendered}"
										style="#{pc_Ghi00601.propKihyoDate.style}"
										value="#{pc_Ghi00601.propKihyoDate.dateValue}" size="10">
										<f:convertDateTime pattern="yyyy/MM/dd" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText></TD>
						</TR>
						<TR>
							<TH class="v_c">
								<h:outputText styleClass="outputText" id="lblKihyoYsnTCd"
										style="#{pc_Ghi00601.propKihyoYsnTCd.labelStyle}"
										value="#{pc_Ghi00601.propKihyoYsnTCd.labelName}"></h:outputText>
							</TH>
							<TD>
								<DIV style="width:500px;white-space:nowrap;overflow:hidden;display:block;">
									<h:inputText styleClass="inputText"
											id="htmlKihyoYsnTCd"
											disabled="#{pc_Ghi00601.propKihyoYsnTCd.disabled}"
											maxlength="#{pc_Ghi00601.propKihyoYsnTCd.maxLength}"
											readonly="#{pc_Ghi00601.propKihyoYsnTCd.readonly}"
											rendered="#{pc_Ghi00601.propKihyoYsnTCd.rendered}"
											style="#{pc_Ghi00601.propKihyoYsnTCd.style}"
											value="#{pc_Ghi00601.propKihyoYsnTCd.stringValue}" onblur="return func_1(this, event);"></h:inputText><hx:commandExButton
											type="submit" styleClass="commandExButton_search"
											id="button1" action="#{pc_Ghi00601.doButton1Action}"></hx:commandExButton><h:outputText
											styleClass="outputText" id="htmlKihyoYsnTName"></h:outputText>
								</DIV>
							</TD>
						</TR>
						<TR>
							<TH class="v_c">
								<h:outputText styleClass="outputText" id="lblShimeKbn"
											style="#{pc_Ghi00601.propShimeKbn.labelStyle}"
											value="#{pc_Ghi00601.propShimeKbn.labelName}"></h:outputText></TH>
							<TD>
								<h:selectOneMenu styleClass="selectOneMenu" id="htmlShimeKbn"
											disabled="#{pc_Ghi00601.propShimeKbn.disabled}"
											readonly="#{pc_Ghi00601.propShimeKbn.readonly}"
											rendered="#{pc_Ghi00601.propShimeKbn.rendered}"
											style="#{pc_Ghi00601.propShimeKbn.style}"
											value="#{pc_Ghi00601.propShimeKbn.stringValue}">
											<f:selectItems value="#{pc_Ghi00601.propShimeKbn.list}" />
								</h:selectOneMenu>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE width="700px" border="0" cellpadding="0" cellspacing="0" class="table" style="margin-top:20px">
					<TBODY>
						<TR>
							<TH class="v_d" width="200px">
								<h:outputText styleClass="outputText" id="lblNyushukkinDate"
										style="#{pc_Ghi00601.propNyushukkinDateFrom.labelStyle}"
										value="入出金日付"></h:outputText></TH>
							<TD width="500px">
								<h:inputText styleClass="inputText" id="htmlNyushukkinDateFrom"
											disabled="#{pc_Ghi00601.propNyushukkinDateFrom.disabled}"
											readonly="#{pc_Ghi00601.propNyushukkinDateFrom.readonly}"
											rendered="#{pc_Ghi00601.propNyushukkinDateFrom.rendered}"
											style="#{pc_Ghi00601.propNyushukkinDateFrom.style}"
											value="#{pc_Ghi00601.propNyushukkinDateFrom.dateValue}" size="10">
											<f:convertDateTime pattern="yyyy/MM/dd" />
											<hx:inputHelperDatePicker />
											<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
								</h:inputText><h:outputText
											styleClass="outputText" id="text2" value="～"></h:outputText><h:inputText
											styleClass="inputText" id="htmlNyushukkinDateTo"
											disabled="#{pc_Ghi00601.propNyushukkinDateTo.disabled}"
											readonly="#{pc_Ghi00601.propNyushukkinDateTo.readonly}"
											rendered="#{pc_Ghi00601.propNyushukkinDateTo.rendered}"
											style="#{pc_Ghi00601.propNyushukkinDateTo.style}"
											value="#{pc_Ghi00601.propNyushukkinDateTo.dateValue}" size="10">
											<f:convertDateTime pattern="yyyy/MM/dd" />
											<hx:inputHelperDatePicker />
											<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
								</h:inputText>
							</TD>
						</TR>
						<TR>
							<TH class="v_d">
								<h:outputText styleClass="outputText" id="lblSakuseiDenpyoShurui"
											style="#{pc_Ghi00601.propSakuseiDenpyoShurui.labelStyle}"
											value="#{pc_Ghi00601.propSakuseiDenpyoShurui.labelName}"></h:outputText></TH>
							<TD>
								<TABLE border="0" class="clear_border" width="100%">
									<TBODY>
										<TR>
											<TD rowspan="3" width="50px">
												<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
														styleClass="selectOneRadio" id="htmlSakuseiDenpyoShurui"
														layout="pageDirection"
														disabled="#{pc_Ghi00601.propSakuseiDenpyoShurui.disabled}"
														readonly="#{pc_Ghi00601.propSakuseiDenpyoShurui.readonly}"
														rendered="#{pc_Ghi00601.propSakuseiDenpyoShurui.rendered}"
														style="#{pc_Ghi00601.propSakuseiDenpyoShurui.style}"
														value="#{pc_Ghi00601.propSakuseiDenpyoShurui.stringValue}">
														<f:selectItems
															value="#{pc_Ghi00601.propSakuseiDenpyoShurui.list}" />
												</h:selectOneRadio>
											</TD>
											<TD>
												<TABLE border="0" class="clear_border">
													<TBODY>
														<TR>
															<TD>
																<h:outputText styleClass="outputText" id="text1" value="（"></h:outputText></TD>
															<TD><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlSakuseiDateShurui"
																disabled="#{pc_Ghi00601.propSakuseiDateShurui.disabled}"
																readonly="#{pc_Ghi00601.propSakuseiDateShurui.readonly}"
																rendered="#{pc_Ghi00601.propSakuseiDateShurui.rendered}"
																style="#{pc_Ghi00601.propSakuseiDateShurui.style}"
																value="#{pc_Ghi00601.propSakuseiDateShurui.stringValue}">
																<f:selectItems
																	value="#{pc_Ghi00601.propSakuseiDateShurui.list}" />
															</h:selectOneRadio>
															</TD>
															<TD>&nbsp;
																<h:inputText styleClass="inputText" id="htmlSuitoDate"
																disabled="#{pc_Ghi00601.propSuitoDate.disabled}"
																readonly="#{pc_Ghi00601.propSuitoDate.readonly}"
																rendered="#{pc_Ghi00601.propSuitoDate.rendered}"
																style="#{pc_Ghi00601.propSuitoDate.style}"
																value="#{pc_Ghi00601.propSuitoDate.dateValue}" size="10">
																<f:convertDateTime pattern="yyyy/MM/dd" />
																<hx:inputHelperDatePicker />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
															</h:inputText>
															</TD>
															<TD>
																<h:outputText styleClass="outputText" id="text4" value="）"></h:outputText>
															</TD>
														</TR>
													</TBODY>
												</TABLE>	
											</TD>
										</TR>
										<TR>
											<TD>
											</TD>
										</TR>
										<TR>
											<TD>
												<TABLE border="0" class="clear_border">
													<TBODY>
														<TR>
															<TD>
																<h:outputText styleClass="outputText" id="text5" value="（"></h:outputText></TD>
															<TD>
																<h:selectManyCheckbox
																disabledClass="selectManyCheckbox_Disabled"
																styleClass="selectManyCheckbox" id="htmlFurikaeShurui"
																disabled="#{pc_Ghi00601.propFurikaeShurui.disabled}"
																readonly="#{pc_Ghi00601.propFurikaeShurui.readonly}"
																rendered="#{pc_Ghi00601.propFurikaeShurui.rendered}"
																style="#{pc_Ghi00601.propFurikaeShurui.style}"
																value="#{pc_Ghi00601.propFurikaeShurui.stringValue}">
																<f:selectItems
																	value="#{pc_Ghi00601.propFurikaeShurui.list}" />
															</h:selectManyCheckbox></TD>
															<TD>
																<h:outputText styleClass="outputText" id="text6" value="）"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
													
											</TD>
										</TR>
									</TBODY>
								</TABLE>	
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE width="700px" border="0" cellpadding="0" cellspacing="0" class="table" style="margin-top:20px">
					<TBODY>
						<TR>
							<TH class="v_d" width="200px"><h:outputText
										styleClass="outputText" id="lblShoriKbn"
										value="#{pc_Ghi00601.propShoriKbn.labelName}"
										style="#{pc_Ghi00601.propShoriKbn.labelStyle}"></h:outputText></TH>
							<TD>
								<TABLE border="0" class="clear_border">
									<TBODY>
										<TR>
											<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlShoriKbn"
													value="#{pc_Ghi00601.propShoriKbn.checked}"
													disabled="#{pc_Ghi00601.propShoriKbn.disabled}"
													readonly="#{pc_Ghi00601.propShoriKbn.readonly}"
													rendered="#{pc_Ghi00601.propShoriKbn.rendered}"
													style="#{pc_Ghi00601.propShoriKbn.style}"></h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText" id="lblShoriKbnL"
													value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</TD>
		</TR>
		<TR>
			<TD>
				<TABLE width="700" border="0" class="button_bar" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
							<TD><hx:commandExButton type="submit" value="実行"
								styleClass="commandExButton_dat" id="exec"
								action="#{pc_Ghi00601.doExecAction}"
								disabled="#{pc_Ghi00601.propExec.disabled}"
								rendered="#{pc_Ghi00601.propExec.rendered}"
								confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
						</TR>
					</TBODY>
				</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>
</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

