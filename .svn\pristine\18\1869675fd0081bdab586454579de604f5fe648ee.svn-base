<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb40401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>消費税区分別集計表</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.css">

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/ke/inc/gakuenKE.js"></SCRIPT>
<SCRIPT type="text/javascript">

function func_KeiriKamokFromAJAX(thisObj, thisEvent) {
	// 科目略称（Ajax）を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkNameFrom";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	code['code2'] = thisObj.value;
	code['code3'] = '100';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}

function func_KeiriKamokToAJAX(thisObj, thisEvent) {
	// 科目略称（Ajax）を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlKmkNameTo";
	var code = new Array();
	code['code1'] = document.getElementById('form1:htmlKaikeiNendo').value;
	code['code2'] = thisObj.value;
	code['code3'] = '100';
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, code);
}
function copy_kamokuTo(thisObj, thisEvent) {
	// 科目コード(from)を科目コード(to)にコピーする
	if(document.getElementById("form1:htmlKmkCdTo").value == ""){
			document.getElementById("form1:htmlKmkCdTo").value = document.getElementById("form1:htmlKmkCdFrom").value;
			func_KeiriKamokToAJAX(document.getElementById("form1:htmlKmkCdTo"), "");
	}
}
function titleChange(thisObj, thisEvent) {
	if(thisObj.value == '0') {
		document.getElementById('form1:htmlTitle').value 
			= document.getElementById('form1:htmlPdfTitle1').value;
	} else if(thisObj.value == '1') {
		document.getElementById('form1:htmlTitle').value 
			= document.getElementById('form1:htmlPdfTitle2').value;
	}
}

function loadFunc() {
	func_KeiriKamokFromAJAX(document.getElementById('form1:htmlKmkCdFrom'), '');
	func_KeiriKamokToAJAX(document.getElementById('form1:htmlKmkCdTo'), '');
}


function titleChange(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
	if(thisObj.value == '0') {
		document.getElementById('form1:htmlTitle').value 
			= document.getElementById('form1:htmlPdfTitle1').value;
	} else if(thisObj.value == '1') {
		document.getElementById('form1:htmlTitle').value 
			= document.getElementById('form1:htmlPdfTitle2').value;
	}
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="return loadFunc();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb40401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Keb40401.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Keb40401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb40401.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<CENTER>
		<TABLE border="0" 
			   class="table" 
			   width="800" 
			   cellspacing="0" 
			   cellpadding="0">
			<TBODY>
				<TR>
					<TH class="v_a" width="120" colspan="2">
						<h:outputText styleClass="outputText" 
									  id="lblKaikeiNendo" 
									  style="#{pc_Keb40401.propKaikeiNendo.labelStyle}" 
									  value="#{pc_Keb40401.propKaikeiNendo.labelName}" 
									  rendered="#{pc_Keb40401.propKaikeiNendo.rendered}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText styleClass="inputText" 
									 id="htmlKaikeiNendo" 
									 size="4" 
									 value="#{pc_Keb40401.propKaikeiNendo.dateValue}" 
									 style="#{pc_Keb40401.propKaikeiNendo.style}" 
									 disabled="#{pc_Keb40401.propKaikeiNendo.disabled}" 
									 readonly="#{pc_Keb40401.propKaikeiNendo.readonly}" 
									 rendered="#{pc_Keb40401.propKaikeiNendo.rendered}">
							<f:convertDateTime pattern="yyyy" />
							<hx:inputHelperAssist errorClass="inputText_Error"
												  promptCharacter="_" />
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="" colspan="2">
						<h:outputText styleClass="outputText" 
									  id="lblKaikeiTsuki" 
									  style="#{pc_Keb40401.propKaikeiTsuki.labelStyle}" 
									  value="#{pc_Keb40401.propKaikeiTsuki.labelName}" 
									  rendered="#{pc_Keb40401.propKaikeiTsuki.rendered}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText styleClass="inputText" 
									 id="htmlKaikeiTsukiFrom" 
									 size="2" 
									 value="#{pc_Keb40401.propKaikeiTsukiFrom.dateValue}" 
									 style="#{pc_Keb40401.propKaikeiTsukiFrom.style}" 
									 disabled="#{pc_Keb40401.propKaikeiTsukiFrom.disabled}" 
									 readonly="#{pc_Keb40401.propKaikeiTsukiFrom.readonly}" 
									 rendered="#{pc_Keb40401.propKaikeiTsukiFrom.rendered}">
							<f:convertDateTime pattern="MM" />
							<hx:inputHelperAssist errorClass="inputText_Error"
												  promptCharacter="_" />
						</h:inputText>　～
						<h:inputText styleClass="inputText" 
									 id="htmlKaikeiTsukiTo" 
									 size="2" 
									 value="#{pc_Keb40401.propKaikeiTsukiTo.dateValue}" 
									 style="#{pc_Keb40401.propKaikeiTsukiTo.style}" 
									 disabled="#{pc_Keb40401.propKaikeiTsukiTo.disabled}" 
									 readonly="#{pc_Keb40401.propKaikeiTsukiTo.readonly}" 
									 rendered="#{pc_Keb40401.propKaikeiTsukiTo.rendered}">
							<f:convertDateTime pattern="MM" />
							<hx:inputHelperAssist errorClass="inputText_Error"
												  promptCharacter="_" />
						</h:inputText>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="" colspan="2">
						<h:outputText styleClass="outputText" 
									  id="lblOutputKind" 
									  style="#{pc_Keb40401.propOutputKind.labelStyle}" 
									  value="#{pc_Keb40401.propOutputKind.labelName}" 
									  rendered="#{pc_Keb40401.propOutputKind.rendered}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneRadio styleClass="selectOneRadio" 
										  disabledClass="selectOneRadio_Disabled" 
										  id="htmlOutputKind" 
										  disabled="#{pc_Keb40401.propOutputKind.disabled}" 
										  value="#{pc_Keb40401.propOutputKind.value}" 
										  readonly="#{pc_Keb40401.propOutputKind.readonly}" 
										  onclick="return titleChange(this, event);">
							<f:selectItem itemValue="0" itemLabel="集計　" />
							<f:selectItem itemValue="1" itemLabel="科目別" />
						</h:selectOneRadio>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="" colspan="2">
						<h:outputText id="lblKaikeiTani" 
									  styleClass="outputText" 
									  style="#{pc_Keb40401.propKaikeiTani.labelStyle}" 
									  value="#{pc_Keb40401.propKaikeiTani.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu styleClass="selectOneMenu" 
										 id="htmlKaikeiTani" 
										 value="#{pc_Keb40401.propKaikeiTani.value}" 
										 style="#{pc_Keb40401.propKaikeiTani.style}" 
										 disabled="#{pc_Keb40401.propKaikeiTani.disabled}" 
										 readonly="#{pc_Keb40401.propKaikeiTani.readonly}" 
										 rendered="#{pc_Keb40401.propKaikeiTani.rendered}">
							<f:selectItems value="#{pc_Keb40401.propKaikeiTani.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="" colspan="2">
						<h:outputText id="lblShimeKbn" 
									  styleClass="outputText" 
									  style="#{pc_Keb40401.propShimeKbn.labelStyle}" 
									  value="#{pc_Keb40401.propShimeKbn.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneMenu styleClass="selectOneMenu" 
										 id="htmlShimeKbn" 
										 value="#{pc_Keb40401.propShimeKbn.value}" 
										 style="#{pc_Keb40401.propShimeKbn.style}" 
										 disabled="#{pc_Keb40401.propShimeKbn.disabled}" 
										 readonly="#{pc_Keb40401.propShimeKbn.readonly}" 
										 rendered="#{pc_Keb40401.propShimeKbn.rendered}">
							<f:selectItems value="#{pc_Keb40401.propShimeKbn.list}" />
						</h:selectOneMenu>
					</TD>
				</TR>
				<TR>
					<TH class="v_a" width="" colspan="2">
						<h:outputText styleClass="outputText" 
									  id="lblShohizeiHyoji" 
									  style="#{pc_Keb40401.propShohizeiHyoji.labelStyle}" 
									  value="#{pc_Keb40401.propShohizeiHyoji.labelName}" 
									  rendered="#{pc_Keb40401.propShohizeiHyoji.rendered}">
						</h:outputText>
					</TH>
					<TD>
						<h:selectOneRadio styleClass="selectOneRadio" 
										  disabledClass="selectOneRadio_Disabled" 
										  id="htmlShohizeiHyoji" 
										  disabled="#{pc_Keb40401.propShohizeiHyoji.disabled}" 
										  value="#{pc_Keb40401.propShohizeiHyoji.value}" 
										  readonly="#{pc_Keb40401.propShohizeiHyoji.readonly}">
							<f:selectItem itemValue="0" itemLabel="無　" />
							<f:selectItem itemValue="1" itemLabel="有" />
						</h:selectOneRadio>
					</TD>
				</TR>				
				<TR>
					<TH class="group_label_top" width="" colspan="2">
						<h:outputText styleClass="outputText" 
									  id="lblKmkCd" 
									  style="#{pc_Keb40401.propKmkCd.labelStyle}" 
									  value="#{pc_Keb40401.propKmkCd.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:outputText styleClass="outputText" 
									  id="htmlKmkCd" 
									  value="（科目別の場合）">
						</h:outputText>
					</TD>
				</TR>
				<TR>
					<TD class="group_label" width="20">
					</TD>
					<TH class="v_b" width="100">
						<h:outputText styleClass="outputText" 
									  id="lblKmkCdFrom" 
									  style="#{pc_Keb40401.propKmkCdFrom.labelStyle}" 
									  value="開始">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText styleClass="inputText" 
									 id="htmlKmkCdFrom" 
									 size="12" 
									 style="#{pc_Keb40401.propKmkCdFrom.style}" 
									 maxlength="#{pc_Keb40401.propKmkCdFrom.maxLength}" 
									 disabled="#{pc_Keb40401.propKmkCdFrom.disabled}" 
									 readonly="#{pc_Keb40401.propKmkCdFrom.readonly}" 
									 rendered="#{pc_Keb40401.propKmkCdFrom.rendered}" 
									 value="#{pc_Keb40401.propKmkCdFrom.stringValue}" 
									 onblur="func_KeiriKamokFromAJAX(this, event); copy_kamokuTo(this, event);">
						</h:inputText>
						<hx:commandExButton type="submit" 
											styleClass="commandExButton_search" 
											id="kmkFromSearch" 
											action="#{pc_Keb40401.doKmkFromSearchAction}">
						</hx:commandExButton>
						<h:outputText styleClass="outputText" 
									  id="htmlKmkNameFrom">
						</h:outputText>
					</TD>
				</TR>
				<TR>
					<TD class="group_label_bottom" width="20">
					</TD>
					<TH class="v_b" width="100">
						<h:outputText styleClass="outputText" 
									  id="lblKmkCdTo" 
									  style="#{pc_Keb40401.propKmkCdTo.labelStyle}" 
									  value="終了">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText styleClass="inputText" 
									 id="htmlKmkCdTo" 
									 size="12" 
									 style="#{pc_Keb40401.propKmkCdTo.style}" 
									 maxlength="#{pc_Keb40401.propKmkCdTo.maxLength}" 
									 disabled="#{pc_Keb40401.propKmkCdTo.disabled}" 
									 readonly="#{pc_Keb40401.propKmkCdTo.readonly}" 
									 rendered="#{pc_Keb40401.propKmkCdTo.rendered}" 
									 value="#{pc_Keb40401.propKmkCdTo.stringValue}" 
									 onblur="return func_KeiriKamokToAJAX(this, event);">
						</h:inputText>
						<hx:commandExButton type="submit" 
											styleClass="commandExButton_search" 
											id="kmkToSearch" 
											action="#{pc_Keb40401.doKmkToSearchAction}">
						</hx:commandExButton>
						<h:outputText styleClass="outputText" 
									  id="htmlKmkNameTo">
						</h:outputText>
					</TD>
				</TR>				
			</TBODY>
		</TABLE>
		<BR>
		<TABLE border="0" 
			   cellspacing="0" 
			   cellpadding="0" 
			   class="table" 
			   width="800" 
			   style="margin-top: 0px;">
			<TBODY>
				<TR>
					<TH class="v_d" width="120">
						<h:outputText styleClass="outputText" 
									  id="lblTitle" 
									  style="#{pc_Keb40401.propTitle.labelStyle}" 
									  value="#{pc_Keb40401.propTitle.labelName}">
						</h:outputText>
					</TH>
					<TD>
						<h:inputText id="htmlTitle" 
									 styleClass="inputText" 
									 size="70" 
									 maxlength="#{pc_Keb40401.propTitle.maxLength}" 
									 value="#{pc_Keb40401.propTitle.stringValue}" 
									 style="#{pc_Keb40401.propTitle.style}" 
									 readonly="#{pc_Keb40401.propTitle.readonly}" 
									 disabled="#{pc_Keb40401.propTitle.disabled}" 
									 rendered="#{pc_Keb40401.propTitle.rendered}">
						</h:inputText>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</CENTER>
	<BR>
	<CENTER>
		<TABLE border="0" 
			   cellpadding="0" 
			   cellspacing="0" 
			   style="margin-top: 10px;" 
			   class="button_bar" 
			   width="800">
			<TBODY>
				<TR>
					<TD align="center" style="margin-top:10px;">
						<hx:commandExButton type="submit" 
											value="PDF作成" 
											styleClass="commandExButton_out" 
											id="pdfout" 
											rendered="#{pc_Keb40401.propPdfout.rendered}" 
											disabled="#{pc_Keb40401.propPdfout.disabled}" 
											style="#{pc_Keb40401.propPdfout.style}" 
											action="#{pc_Keb40401.doPdfoutAction}" 
											confirm="#{msg.SY_MSG_0019W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="EXCEL作成" 
											styleClass="commandExButton_out" 
											id="excelout"
											rendered="#{pc_Keb40401.propExcelout.rendered}" 
											disabled="#{pc_Keb40401.propExcelout.disabled}"
											style="#{pc_Keb40401.propExcelout.style}" 
											action="#{pc_Keb40401.doExcelOutAction}" 
											confirm="#{msg.SY_MSG_0027W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="CSV作成" 
											styleClass="commandExButton_out" 
											id="csvout" 
											rendered="#{pc_Keb40401.propCsvout.rendered}" 
											disabled="#{pc_Keb40401.propCsvout.disabled}" 
											style="#{pc_Keb40401.propCsvout.style}" 
											action="#{pc_Keb40401.doCsvoutAction}" 
											confirm="#{msg.SY_MSG_0020W}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="出力項目指定" 
											styleClass="commandExButton_out" 
											id="setoutput" 
											action="#{pc_Keb40401.doSetoutputAction}" 
											disabled="#{pc_Keb40401.propSetoutput.disabled}" 
											rendered="#{pc_Keb40401.propSetoutput.rendered}" 
											style="#{pc_Keb40401.propSetoutput.style}">
						</hx:commandExButton>
						<hx:commandExButton type="submit" 
											value="印刷" 
											styleClass="commandExButton_out" 
											id="print" 
											rendered="#{pc_Keb40401.propPrint.rendered}" 
											disabled="#{pc_Keb40401.propPrint.disabled}" 
											style="#{pc_Keb40401.propPrint.style}" 
											action="#{pc_Keb40401.doPrintAction}" 
											confirm="#{msg.SY_MSG_0022W}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</CENTER>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden value="#{pc_Keb40401.propPdfTitle1.stringValue}"
			   id="htmlPdfTitle1">
</h:inputHidden>
<h:inputHidden value="#{pc_Keb40401.propPdfTitle2.stringValue}"
			   id="htmlPdfTitle2">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
