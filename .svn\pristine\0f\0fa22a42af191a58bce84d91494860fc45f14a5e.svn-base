<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coi00504.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>役職設定登録</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Coi00504.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/childHeader.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" 
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp" 
					action="#{pc_Coi00504.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Coi00504.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Coi00504.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" 
			  value="#{requestScope.DISPLAY_INFO.displayMessage}" 
			  styleClass="outputText" 
			  escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   style=""
		   class="table" 
		   width="900">
		<TBODY>
			<TR>
				<TH style="" class="v_a" width="150">
					<h:outputText styleClass="outputText" 
								  id="lblStartDate" 
								  style="#{pc_Coi00504.propStartDate.labelStyle}" 
								  value="#{pc_Coi00504.propStartDate.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText styleClass="inputText" 
								 id="htmlStartDate" 
								 size="10" 
								 value="#{pc_Coi00504.propStartDate.dateValue}" 
								 style="#{pc_Coi00504.propStartDate.style}" 
								 readonly="#{pc_Coi00504.propStartDate.readonly}" 
								 disabled="#{pc_Coi00504.propStartDate.disabled}" 
								 rendered="#{pc_Coi00504.propStartDate.rendered}">
						<f:convertDateTime />
						<hx:inputHelperAssist errorClass="inputText_Error" 
											  promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_b" width="">
					<h:outputText styleClass="outputText" 
								  id="lblEndDate" 
								  style="#{pc_Coi00504.propEndDate.labelStyle}" 
								  value="#{pc_Coi00504.propEndDate.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText styleClass="inputText" 
								 id="htmlEndDate" 
								 size="10" 
								 value="#{pc_Coi00504.propEndDate.dateValue}" 
								 style="#{pc_Coi00504.propEndDate.style}" 
								 readonly="#{pc_Coi00504.propEndDate.readonly}" 
								 disabled="#{pc_Coi00504.propEndDate.disabled}" 
								 rendered="#{pc_Coi00504.propEndDate.rendered}">
						<f:convertDateTime />
						<hx:inputHelperAssist errorClass="inputText_Error" 
											  promptCharacter="_" />
						<hx:inputHelperDatePicker />
					</h:inputText>
				</TD>
			</TR>
		</TBOD>
	</TABLE><BR>
	<TABLE border="0" 
		   cellpadding="0" 
		   cellspacing="0" 
		   class="table" 
		   width="900">
		<TBODY>
			<TR>
				<TH style="" class="v_c" width="150">
					<h:outputText styleClass="outputText" 
								  id="lblBmnCd" 
								  style="#{pc_Coi00504.propBmnCd.labelStyle}" 
								  value="#{pc_Coi00504.propBmnCd.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlBmnCd" 
								 styleClass="inputText" 
								 size="10" 
								 maxlength="#{pc_Coi00504.propBmnCd.maxLength}" 
								 value="#{pc_Coi00504.propBmnCd.stringValue}" 
								 style="#{pc_Coi00504.propBmnCd.style}" 
								 readonly="#{pc_Coi00504.propBmnCd.readonly}" 
								 disabled="#{pc_Coi00504.propBmnCd.disabled}" 
								 rendered="#{pc_Coi00504.propBmnCd.rendered}">
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_c" width="">
					<h:outputText styleClass="outputText" 
								  id="lblJoiBmnCd" 
								  style="#{pc_Coi00504.propJoiBmnCd.labelStyle}" 
								  value="#{pc_Coi00504.propJoiBmnCd.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlJoiBmnCd" 
								 styleClass="inputText" 
								 size="10" 
								 maxlength="#{pc_Coi00504.propJoiBmnCd.maxLength}" 
								 value="#{pc_Coi00504.propJoiBmnCd.stringValue}" 
								 style="#{pc_Coi00504.propJoiBmnCd.style}" 
								 readonly="#{pc_Coi00504.propJoiBmnCd.readonly}" 
								 disabled="#{pc_Coi00504.propJoiBmnCd.disabled}" 
								 rendered="#{pc_Coi00504.propJoiBmnCd.rendered}">
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_e" width="">
					<h:outputText styleClass="outputText" 
								  id="lblLvl" 
								  style="#{pc_Coi00504.propLvl.style}" 
								  value="#{pc_Coi00504.propLvl.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText styleClass="inputText" 
								 id="htmlLvl" 
								 size="1" 
								 style="padding-right: 3px; text-align: right" 
								 disabled="#{pc_Coi00504.propLvl.disabled}" 
								 readonly="#{pc_Coi00504.propLvl.readonly}" 
								 rendered="#{pc_Coi00504.propLvl.rendered}" 
								 value="#{pc_Coi00504.propLvl.integerValue}">
						<f:convertNumber pattern="0;0" />
						<hx:inputHelperAssist errorClass="inputText_Error"
											  promptCharacter="_" />
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_d" width="">
					<h:outputText styleClass="outputText" 
								  id="lblBmnName" 
								  style="#{pc_Coi00504.propBmnName.labelStyle}" 
								  value="#{pc_Coi00504.propBmnName.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlBmnName" 
								 styleClass="inputText" 
								 size="55" 
								 maxlength="#{pc_Coi00504.propBmnName.maxLength}" 
								 value="#{pc_Coi00504.propBmnName.stringValue}" 
								 style="#{pc_Coi00504.propBmnName.style}" 
								 readonly="#{pc_Coi00504.propBmnName.readonly}" 
								 disabled="#{pc_Coi00504.propBmnName.disabled}" 
								 rendered="#{pc_Coi00504.propBmnName.rendered}">
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_e" width="">
					<h:outputText styleClass="outputText" 
								  id="lblBmnNameRyak" 
								  style="#{pc_Coi00504.propBmnNameRyak.labelStyle}" 
								  value="#{pc_Coi00504.propBmnNameRyak.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlBmnNameRyak" 
								 styleClass="inputText" 
								 size="20" 
								 maxlength="#{pc_Coi00504.propBmnNameRyak.maxLength}" 
								 value="#{pc_Coi00504.propBmnNameRyak.stringValue}" 
								 style="#{pc_Coi00504.propBmnNameRyak.style}" 
								 readonly="#{pc_Coi00504.propBmnNameRyak.readonly}" 
								 disabled="#{pc_Coi00504.propBmnNameRyak.disabled}" 
								 rendered="#{pc_Coi00504.propBmnNameRyak.rendered}">
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_f" width="">
					<h:outputText styleClass="outputText" 
								  id="lblBmnEigo" 
								  style="#{pc_Coi00504.propBmnEigo.labelStyle}" 
								  value="#{pc_Coi00504.propBmnEigo.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlBmnEigo" 
								 styleClass="inputText" 
								 size="55" 
								 maxlength="#{pc_Coi00504.propBmnEigo.maxLength}" 
								 value="#{pc_Coi00504.propBmnEigo.stringValue}" 
								 style="#{pc_Coi00504.propBmnEigo.style}" 
								 readonly="#{pc_Coi00504.propBmnEigo.readonly}" 
								 disabled="#{pc_Coi00504.propBmnEigo.disabled}" 
								 rendered="#{pc_Coi00504.propBmnEigo.rendered}">
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_g" width="">
					<h:outputText styleClass="outputText" 
								  id="lblBmnKana" 
								  style="#{pc_Coi00504.propBmnKana.labelStyle}" 
								  value="#{pc_Coi00504.propBmnKana.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlBmnKana" 
								 styleClass="inputText" 
								 size="115" 
								 maxlength="#{pc_Coi00504.propBmnKana.maxLength}" 
								 value="#{pc_Coi00504.propBmnKana.stringValue}" 
								 style="#{pc_Coi00504.propBmnKana.style}" 
								 readonly="#{pc_Coi00504.propBmnKana.readonly}" 
								 disabled="#{pc_Coi00504.propBmnKana.disabled}" 
								 rendered="#{pc_Coi00504.propBmnKana.rendered}">
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_g" width="">
					<h:outputText styleClass="outputText" 
								  id="lblHatsuName" 
								  style="#{pc_Coi00504.propHatsuName.labelStyle}" 
								  value="#{pc_Coi00504.propHatsuName.labelName}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:inputText id="htmlHatsuName" 
								 styleClass="inputText" 
								 size="55" 
								 maxlength="#{pc_Coi00504.propHatsuName.maxLength}" 
								 value="#{pc_Coi00504.propHatsuName.stringValue}" 
								 style="#{pc_Coi00504.propHatsuName.style}" 
								 readonly="#{pc_Coi00504.propHatsuName.readonly}" 
								 disabled="#{pc_Coi00504.propHatsuName.disabled}" 
								 rendered="#{pc_Coi00504.propHatsuName.rendered}">
					</h:inputText>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_f" width="">
					<h:outputText styleClass="outputText" 
								  id="lblCampus" 
								  style="#{pc_Coi00504.propCampus.labelStyle}" 
								  value="#{pc_Coi00504.propCampus.labelName}" 
								  rendered="#{pc_Coi00504.propCampus.rendered}">
					</h:outputText>
				</TH>
				<TD width="">
					<h:selectOneMenu styleClass="selectOneMenu" 
									 id="htmlCampus" 
									 value="#{pc_Coi00504.propCampus.value}" 
									 style="#{pc_Coi00504.propCampus.style}" 
									 disabled="#{pc_Coi00504.propCampus.disabled}" 
									 rendered="#{pc_Coi00504.propCampus.rendered}">
						<f:selectItems value="#{pc_Coi00504.propCampus.list}" />
					</h:selectOneMenu>
				</TD>
			</TR>
			<TR>
				<TH style="" class="v_a" width="">
					<h:outputText styleClass="outputText" 
								  id="lblBiko" 
								  style="#{pc_Coi00504.propBiko.labelStyle}" 
								  value="#{pc_Coi00504.propBiko.labelName}">
					</h:outputText>
					<img src="${pageContext.request.contextPath}/rev/image/ja/hankakukana_Permission.gif">
				</TH>
				<TD width="">
					<h:inputText id="htmlBiko" 
								 styleClass="inputText" 
								 size="115" 
								 maxlength="#{pc_Coi00504.propBiko.maxLength}" 
								 value="#{pc_Coi00504.propBiko.stringValue}" 
								 style="#{pc_Coi00504.propBiko.style}" 
								 readonly="#{pc_Coi00504.propBiko.readonly}" 
								 disabled="#{pc_Coi00504.propBiko.disabled}" 
								 rendered="#{pc_Coi00504.propBiko.rendered}">
					</h:inputText>
				</TD>
			</TR>
		</TBOD>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/childFooter.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
