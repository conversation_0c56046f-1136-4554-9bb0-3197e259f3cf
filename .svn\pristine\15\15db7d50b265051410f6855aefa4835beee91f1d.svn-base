<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Keb02301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Keb02301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<style type="text/css">
<!--
.setWidth TD {width: 60px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript">
//Ajax
function func_1(thisObj, thisEvent) {
	//(振替元)予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlMotoYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMotoYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_2(thisObj, thisEvent) {
	//(振替先)予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlSakiYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlSakiYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_3(thisObj, thisEvent) {
	//(振替元)目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlMotoMokuName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMotoMokuCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_4(thisObj, thisEvent) {
	//(振替先)目的名称を取得する
	var servlet = "rev/co/CogYosanMokuAJAX";
	var target = "form1:htmlSakiMokuName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlSakiMokuCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_5(thisObj, thisEvent) {
	//(振替元)科目名称を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlMotoKmkName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMotoKmkCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}
function func_6(thisObj, thisEvent) {
	//(振替先)科目名称を取得する
	var servlet = "rev/co/CogKeiriKamokAJAX";
	var target = "form1:htmlSakiKmkName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlSakiKmkCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//onload
function func_onload(thisObj, thisEvent){
	//(振替元)予算単位名称を取得する
	var servlet = "rev/co/CogYosanTaniAJAX";
	var target = "form1:htmlMotoYsnTName";
	var args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMotoYsnTCd").value;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

	//(振替先)予算単位名称を取得する
	servlet = "rev/co/CogYosanTaniAJAX";
	target = "form1:htmlSakiYsnTName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlSakiYsnTCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

	//(振替元)目的名称を取得する
	servlet = "rev/co/CogYosanMokuAJAX";
	target = "form1:htmlMotoMokuName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMotoMokuCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
	
	//(振替先)目的名称を取得する
	servlet = "rev/co/CogYosanMokuAJAX";
	target = "form1:htmlSakiMokuName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlSakiMokuCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

	//(振替元)科目名称を取得する
	servlet = "rev/co/CogKeiriKamokAJAX";
	target = "form1:htmlMotoKmkName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlMotoKmkCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
	
	//(振替先)科目名称を取得する
	servlet = "rev/co/CogKeiriKamokAJAX";
	target = "form1:htmlSakiKmkName";
	args = new Array();
	args['code1'] = document.getElementById("form1:htmlKaikeiNendo").value;
	args['code2'] = document.getElementById("form1:htmlSakiKmkCd").value;
	ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_onload(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Keb02301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Keb02301.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="lblOutKbn"
				value="#{pc_Keb02301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Keb02301.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、全角スペースを配置-->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" colspan="2">
									
									<h:outputText styleClass="outputText" id="lblKaikeiNendo"
										style="#{pc_Keb02301.propKaikeiNendo.labelStyle}"
										value="#{pc_Keb02301.propKaikeiNendo.labelName}"></h:outputText></TH>
									<TD width="645">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD class="clear_border" width="50">
												<h:inputText styleClass="inputText" id="htmlKaikeiNendo"
													size="5" tabindex="1"
													value="#{pc_Keb02301.propKaikeiNendo.dateValue}"
													style="#{pc_Keb02301.propKaikeiNendo.style}"
													disabled="#{pc_Keb02301.propKaikeiNendo.disabled}"
													readonly="#{pc_Keb02301.propKaikeiNendo.readonly}"
													maxlength="#{pc_Keb02301.propKaikeiNendo.maxLength}">
													<f:convertDateTime pattern="yyyy" type="date" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TD class="clear_border"></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2">
									<h:outputText styleClass="outputText" id="lblFurikaeMoto"
										value="振替元"></h:outputText></TH>
									<TD class="group_item" width="645"></TD>
								</TR>
								<TR>
									<TH class="group_label" width="23"></TH>
									<TH class="v_b" width="149">
									<h:outputText styleClass="outputText" id="lblMotoYsnTCd"
										value="#{pc_Keb02301.propMotoYsnTCd.labelName}"
										style="#{pc_Keb02301.propMotoYsnTName.style}"></h:outputText></TH>
									<TD width="645" nowrap>
										<DIV style="width:645px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlMotoYsnTCd"
												size="14" value="#{pc_Keb02301.propMotoYsnTCd.stringValue}"
												style="#{pc_Keb02301.propMotoYsnTCd.style}" tabindex="2"
												maxlength="#{pc_Keb02301.propMotoYsnTCd.maxLength}"
												disabled="#{pc_Keb02301.propMotoYsnTCd.disabled}"
												readonly="#{pc_Keb02301.propMotoYsnTCd.readonly}"
												onblur="return func_1(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchMotoYsnTCd"
												tabindex="3" action="#{pc_Keb02301.doSearchMotoYsnTCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMotoYsnTName"
												value="#{pc_Keb02301.propMotoYsnTName.stringValue}"
												style="#{pc_Keb02301.propMotoYsnTName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label" width="23"></TH>
									<TH class="v_c" width="149"><h:outputText
										styleClass="outputText" id="lblMotoMokuCd"
										value="#{pc_Keb02301.propMotoMokuCd.labelName}"
										style="#{pc_Keb02301.propMotoMokuCd.labelStyle}"></h:outputText></TH>
									<TD width="645" nowrap>
										<DIV style="width:645px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlMotoMokuCd"
												size="14" tabindex="4"
												value="#{pc_Keb02301.propMotoMokuCd.stringValue}"
												style="#{pc_Keb02301.propMotoMokuCd.style}"
												maxlength="#{pc_Keb02301.propMotoMokuCd.maxLength}"
												disabled="#{pc_Keb02301.propMotoMokuCd.disabled}"
												readonly="#{pc_Keb02301.propMotoMokuCd.readonly}"
												onblur="return func_3(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchMotoMokuCd"
												tabindex="5"
												action="#{pc_Keb02301.doSearchMotoMokuCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMotoMokuName"
												value="#{pc_Keb02301.propMotoMokuName.stringValue}"
												style="#{pc_Keb02301.propMotoMokuName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_bottom" width="23"></TH>
									<TH class="v_d" width="149">
									<h:outputText styleClass="outputText" id="lblMotoKmkCd"
										value="#{pc_Keb02301.propMotoKmkCd.labelName}"
										style="#{pc_Keb02301.propMotoKmkCd.style}"></h:outputText></TH>
									<TD width="645" nowrap>
										<DIV style="width:645px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlMotoKmkCd"
												size="14" tabindex="6"
												value="#{pc_Keb02301.propMotoKmkCd.stringValue}"
												style="#{pc_Keb02301.propMotoKmkCd.style}"
												maxlength="#{pc_Keb02301.propMotoKmkCd.maxLength}"
												disabled="#{pc_Keb02301.propMotoKmkCd.disabled}"
												readonly="#{pc_Keb02301.propMotoKmkCd.readonly}"
												onblur="return func_5(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchMotoKmkCd"
												tabindex="7"
												action="#{pc_Keb02301.doSearchMotoKmkCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlMotoKmkName"
												value="#{pc_Keb02301.propMotoKmkName.stringValue}"
												style="#{pc_Keb02301.propMotoKmkName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_top" colspan="2">
									<h:outputText styleClass="outputText" id="lblFurikaeSaki"
										value="振替先"></h:outputText></TH>
									<TD class="group_item" width="645"></TD>
								</TR>
								<TR>
									<TH class="group_label" width="23"></TH>
									<TH class="v_e" width="149">
									<h:outputText styleClass="outputText" id="lblSakiYsnTCd"
										value="#{pc_Keb02301.propSakiYsnTCd.labelName}"
										style="#{pc_Keb02301.propSakiYsnTCd.labelStyle}"></h:outputText></TH>
									<TD width="645" nowrap>
										<DIV style="width:645px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlSakiYsnTCd"
												size="14" value="#{pc_Keb02301.propSakiYsnTCd.stringValue}"
												style="#{pc_Keb02301.propSakiYsnTCd.style}" tabindex="8"
												maxlength="#{pc_Keb02301.propSakiYsnTCd.maxLength}"
												disabled="#{pc_Keb02301.propSakiYsnTCd.disabled}"
												readonly="#{pc_Keb02301.propSakiYsnTCd.readonly}"
												onblur="return func_2(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchSakiYsnTCd"
												tabindex="9"
												action="#{pc_Keb02301.doSearchSakiYsnTCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlSakiYsnTName"
												value="#{pc_Keb02301.propSakiYsnTName.stringValue}"
												style="#{pc_Keb02301.propSakiYsnTName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label" width="23"></TH>
									<TH class="v_f" width="149">
									<h:outputText styleClass="outputText" id="lblSakiMokuCd"
										value="#{pc_Keb02301.propSakiMokuCd.labelName}"
										style="#{pc_Keb02301.propSakiMokuCd.labelStyle}"></h:outputText></TH>
									<TD width="645" nowrap>
										<DIV style="width:645px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlSakiMokuCd"
												size="14" value="#{pc_Keb02301.propSakiMokuCd.stringValue}"
												style="#{pc_Keb02301.propSakiMokuCd.style}" tabindex="10"
												maxlength="#{pc_Keb02301.propSakiMokuCd.maxLength}"
												disabled="#{pc_Keb02301.propSakiMokuCd.disabled}"
												readonly="#{pc_Keb02301.propSakiMokuCd.readonly}"
												onblur="return func_4(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchSakiMokuCd"
												tabindex="11"
												action="#{pc_Keb02301.doSearchSakiMokuCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlSakiMokuName"
												value="#{pc_Keb02301.propSakiMokuName.stringValue}"
												style="#{pc_Keb02301.propSakiMokuName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="group_label_bottom" width="23"></TH>
									<TH class="v_g" width="149">
									<h:outputText styleClass="outputText" id="lblSakiKmkCd"
										value="#{pc_Keb02301.propSakiKmkCd.labelName}"
										style="#{pc_Keb02301.propSakiKmkCd.labelStyle}"></h:outputText></TH>
									<TD width="645" nowrap>
										<DIV style="width:645px;white-space:nowrap;overflow:hidden;display:block;">
											<h:inputText styleClass="inputText" id="htmlSakiKmkCd"
												size="14" tabindex="12"
												value="#{pc_Keb02301.propSakiKmkCd.stringValue}"
												style="#{pc_Keb02301.propSakiKmkCd.style}"
												maxlength="#{pc_Keb02301.propSakiKmkCd.maxLength}"
												disabled="#{pc_Keb02301.propSakiKmkCd.disabled}"
												readonly="#{pc_Keb02301.propSakiKmkCd.readonly}"
												onblur="return func_6(this, event);"></h:inputText><hx:commandExButton type="submit"
												styleClass="commandExButton_search" id="searchSakiKmkCd"
												tabindex="13"
												action="#{pc_Keb02301.doSearchSakiKmkCdAction}"></hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlSakiKmkName"
												value="#{pc_Keb02301.propSakiKmkName.stringValue}"
												style="#{pc_Keb02301.propSakiKmkName.style}"></h:outputText>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TH class="v_a" colspan="2">
										<h:outputText styleClass="outputText" id="lblShinseiNo"
										value="#{pc_Keb02301.propShinseiNoFrom.labelName}"
										style="#{pc_Keb02301.propShinseiNoFrom.labelStyle}"></h:outputText></TH>
									<TD colspan="" width="645">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD width="130" class="clear_border">
												<h:inputText styleClass="inputText" id="htmlShinseiNoFrom"
													size="14"
													disabled="#{pc_Keb02301.propShinseiNoFrom.disabled}"
													readonly="#{pc_Keb02301.propShinseiNoFrom.readonly}"
													style="#{pc_Keb02301.propShinseiNoFrom.style}"
													value="#{pc_Keb02301.propShinseiNoFrom.integerValue}"
													tabindex="14">

													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													<f:convertNumber pattern="####0;####0" />
												</h:inputText></TD>
												<TD width="25" class="clear_border">
												<h:outputText styleClass="outputText" id="lblFromTo1" 
													value="～"></h:outputText></TD>
												<TD width="150" class="clear_border">
												<h:inputText styleClass="inputText" id="htmlShinseiNoTo"
													size="14"
													disabled="#{pc_Keb02301.propShinseiNoTo.disabled}"
													readonly="#{pc_Keb02301.propShinseiNoTo.readonly}"
													style="#{pc_Keb02301.propShinseiNoTo.style}"
													value="#{pc_Keb02301.propShinseiNoTo.integerValue}"
													tabindex="15"
													maxlength="#{pc_Keb02301.propShinseiNoTo.maxLength}">
													<f:convertNumber pattern="####0;####0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_b" colspan="2"><h:outputText
										styleClass="outputText" id="lblShinseiDate"
										value="#{pc_Keb02301.propShinseiDateFrom.name}"
										style="#{pc_Keb02301.propShinseiDateFrom.labelStyle}"></h:outputText></TH>
									<TD colspan="" width="645">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD width="130" class="clear_border">
												<h:inputText styleClass="inputText" id="htmlShinseiDateFrom"
													size="14"
													readonly="#{pc_Keb02301.propShinseiDateTo.readonly}"
													style="#{pc_Keb02301.propShinseiDateTo.style}"
													value="#{pc_Keb02301.propShinseiDateFrom.dateValue}"
													tabindex="16"
													disabled="#{pc_Keb02301.propShinseiDateTo.disabled}">
													<f:convertDateTime pattern="yyyy/MM/dd" type="date" />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TD width="25" class="clear_border">
												<h:outputText styleClass="outputText" id="lblFromTo2" 
													value="～"></h:outputText></TD>
												<TD width="180" class="clear_border">
												<h:inputText styleClass="inputText" id="htmlShinseiDateTo"
													size="14"
													disabled="#{pc_Kea02301.propShinseiDateTo.disabled}"
													readonly="#{pc_Kea02301.propShinseiDateTo.readonly}"
													style="#{pc_Kea02301.propShinseiDateTo.style}"
													value="#{pc_Keb02301.propShinseiDateTo.dateValue}"
													tabindex="17">
													<f:convertDateTime pattern="yyyy/MM/dd" />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_c" colspan="2"><h:outputText
										styleClass="outputText" id="lblShinseiUser"
										value="#{pc_Keb02301.propShinseiUser1.labelName}"
										style="#{pc_Keb02301.propShinseiUser1.labelStyle}"></h:outputText></TH>
									<TD colspan="" width="645">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser1" size="14"
													disabled="#{pc_Keb02301.propShinseiUser1.disabled}"
													readonly="#{pc_Keb02301.propShinseiUser1.readonly}"
													style="#{pc_Keb02301.propShinseiUser1.style}"
													value="#{pc_Keb02301.propShinseiUser1.stringValue}"
													tabindex="18"
													maxlength="#{pc_Keb02301.propShinseiUser1.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser2" size="14"
													disabled="#{pc_Keb02301.propShinseiUser2.disabled}"
													readonly="#{pc_Keb02301.propShinseiUser2.readonly}"
													style="#{pc_Keb02301.propShinseiUser2.style}"
													value="#{pc_Keb02301.propShinseiUser2.stringValue}"
													tabindex="19"
													maxlength="#{pc_Keb02301.propShinseiUser2.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser3" size="14"
													disabled="#{pc_Keb02301.propShinseiUser3.disabled}"
													readonly="#{pc_Keb02301.propShinseiUser3.readonly}"
													style="#{pc_Keb02301.propShinseiUser3.style}"
													value="#{pc_Keb02301.propShinseiUser3.stringValue}"
													tabindex="20"
													maxlength="#{pc_Keb02301.propShinseiUser3.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser4" size="14"
													disabled="#{pc_Keb02301.propShinseiUser4.disabled}"
													readonly="#{pc_Keb02301.propShinseiUser4.readonly}"
													style="#{pc_Keb02301.propShinseiUser4.style}"
													value="#{pc_Keb02301.propShinseiUser4.stringValue}"
													tabindex="21"
													maxlength="#{pc_Keb02301.propShinseiUser4.maxLength}">
												</h:inputText></TD>
												<TD width="20%" class="clear_border"><h:inputText
													styleClass="inputText" id="htmlShinseiUser5" size="14"
													disabled="#{pc_Keb02301.propShinseiUser5.disabled}"
													readonly="#{pc_Keb02301.propShinseiUser5.readonly}"
													style="#{pc_Keb02301.propShinseiUser5.style}"
													value="#{pc_Keb02301.propShinseiUser5.stringValue}"
													tabindex="22"
													maxlength="#{pc_Keb02301.propShinseiUser5.maxLength}">
												</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_d" colspan="2">
									<h:outputText styleClass="outputText" id="lblShutsuryokuKbn"
										value="#{pc_Keb02301.propOutKbn.name}"
										style="#{pc_Keb02301.propOutKbn.labelStyle}"></h:outputText></TH>
									<TD width="645"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox setWidth" id="htmlOutKbn"
										disabled="#{pc_Keb02301.propOutKbn.disabled}"
										readonly="#{pc_Keb02301.propOutKbn.readonly}"
										value="#{pc_Keb02301.propOutKbn.value}" tabindex="23">
										<f:selectItems value="#{pc_Keb02301.propOutKbn.list}" />
									</h:selectManyCheckbox></TD>
								</TR>
								<TR>
									<TH class="v_e" colspan="2"><h:outputText
										styleClass="outputText" id="lblTitle"
										value="#{pc_Keb02301.propTitle.labelName}"
										style="#{pc_Keb02301.propTitle.labelStyle}"></h:outputText></TH>
									<TD width="645"><h:inputText styleClass="inputText"
										id="htmlTitle" size="85" tabindex="24"
										disabled="#{pc_Keb02301.propTitle.disabled}"
										maxlength="#{pc_Keb02301.propTitle.maxLength}"
										readonly="#{pc_Keb02301.propTitle.readonly}"
										style="#{pc_Keb02301.propTitle.style}"
										value="#{pc_Keb02301.propTitle.stringValue}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="800" class="button_bar" cellpadding="1"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout" tabindex="25"
										action="#{pc_Keb02301.doPdfoutAction}"
										confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="EXCEL作成" 
										styleClass="commandExButton_out" id="excelout" 
										confirm="#{msg.SY_MSG_0027W}" tabindex="26"
										action="#{pc_Keb02301.doExcelOutAction}"
										disabled="#{pc_Keb02301.propExcelout.disabled}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="CSV作成"
										styleClass="commandExButton_out" id="csvout" tabindex="27"
										confirm="#{msg.SY_MSG_0020W}" action="#{pc_Keb02301.doCsvoutAction}"></hx:commandExButton>
									<hx:commandExButton type="submit" value=" 出力項目指定 "
										styleClass="commandExButton_out" id="setoutput" tabindex="28"
										action="#{pc_Keb02301.doSetoutputAction}"></hx:commandExButton>
									<hx:commandExButton type="submit" value="印刷"
										styleClass="commandExButton_out" id="print" tabindex="29"
										action="#{pc_Keb02301.doPrintAction}"
										confirm="#{msg.SY_MSG_0022W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

