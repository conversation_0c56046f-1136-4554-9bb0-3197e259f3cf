<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cog03401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cog03401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">	

<style type="text/css">
<!--
.setWidth TD {width: 80px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
　　changeScrollPosition('scroll', 'listScroll');

}
//----------↑経理検索部品共通js↓----------
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}			
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
	document.getElementById('form1:scroll').value = 0;
}
//----------↑経理検索部品共通js↑----------
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="return func_1(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Cog03401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Cog03401.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Cog03401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Cog03401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="新規登録"
				styleClass="commandExButton" id="creatnew" action="#{pc_Cog03401.doCreatnewAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblKaikeiNendo"
										value="#{pc_Cog03401.propKaikeiNendo.labelName}"
										style="#{pc_Cog03401.propKaikeiNendo.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlKaikeiNendo" size="5"
										disabled="#{pc_Cog03401.propKaikeiNendo.disabled}"
										readonly="#{pc_Cog03401.propKaikeiNendo.readonly}"
										style="#{pc_Cog03401.propKaikeiNendo.style}"
										value="#{pc_Cog03401.propKaikeiNendo.dateValue}"
										tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b"><h:outputText styleClass="outputText"
										id="lblMokuCd" value="#{pc_Cog03401.propMokuCd.labelName}"
										style="#{pc_Cog03401.propMokuCd.labelStyle}"></h:outputText></TH>
									<TD colspan="3">
									<TABLE width="450" border="0" cellpadding="0" cellspacing="0"
										class="clear_border">
										<TBODY>
											<TR>
												<TD width="224"><h:inputText styleClass="inputText"
													id="htmlMokuCd" size="12"
													value="#{pc_Cog03401.propMokuCd.stringValue}"
													style="#{pc_Cog03401.propMokuCd.style}"
													disabled="#{pc_Cog03401.propMokuCd.disabled}"
													maxlength="#{pc_Cog03401.propMokuCd.maxLength}"
													readonly="#{pc_Cog03401.propMokuCd.readonly}" tabindex="2"></h:inputText></TD>
												<TD width="226"><h:outputText styleClass="outputText"
													id="lblMokuCdFindType" value="（前方一致）"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_c"><h:outputText styleClass="outputText"
										id="lblMokuName" value="#{pc_Cog03401.propMokuName.labelName}"
										style="#{pc_Cog03401.propMokuName.style}"></h:outputText></TH>
									<TD colspan="3">
									<TABLE width="450" border="0" cellpadding="0" cellspacing="0"
										class="clear_border">
										<TBODY>
											<TR>
												<TD width="210"><h:inputText styleClass="inputText"
													id="htmlMokuName" size="28"
													disabled="#{pc_Cog03401.propMokuName.disabled}"
													maxlength="#{pc_Cog03401.propMokuName.maxLength}"
													readonly="#{pc_Cog03401.propMokuName.readonly}"
													style="#{pc_Cog03401.propMokuName.style}" tabindex="3"
													value="#{pc_Cog03401.propMokuName.stringValue}"></h:inputText></TD>
												<TD width="250"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio setWidth"
													id="htmlMokuNameFindType"
													disabled="#{pc_Cog03401.propMokuNameFindType.disabled}"
													readonly="#{pc_Cog03401.propMokuNameFindType.readonly}"
													value="#{pc_Cog03401.propMokuNameFindType.stringValue}"
													tabindex="4">
													<f:selectItems
														value="#{pc_Cog03401.propMokuNameFindType.list}" />
												</h:selectOneRadio></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_d"><h:outputText styleClass="outputText"
										id="lblMokuNameKana"
										value="#{pc_Cog03401.propMokuNameKana.labelName}"
										style="#{pc_Cog03401.propMokuNameKana.style}"></h:outputText></TH>
									<TD colspan="3">
									<TABLE width="450" border="0" cellpadding="0" cellspacing="0"
										class="clear_border">
										<TBODY>
											<TR>
												<TD width="210"><h:inputText styleClass="inputText"
													id="htmlMokuNameKana" size="28"
													disabled="#{pc_Cog03401.propMokuNameKana.disabled}"
													maxlength="#{pc_Cog03401.propMokuNameKana.maxLength}"
													readonly="#{pc_Cog03401.propMokuNameKana.readonly}"
													style="#{pc_Cog03401.propMokuNameKana.style}" tabindex="5"
													value="#{pc_Cog03401.propMokuNameKana.stringValue}"></h:inputText></TD>
												<TD width="250"><h:selectOneRadio
													disabledClass="selectOneRadio_Disabled"
													styleClass="selectOneRadio setWidth"
													id="htmlMokuNameKanaFindType"
													value="#{pc_Cog03401.propMokuNameKanaFindType.stringValue}"
													disabled="#{pc_Cog03401.propMokuNameKanaFindType.disabled}"
													readonly="#{pc_Cog03401.propMokuNameKanaFindType.readonly}"
													tabindex="6">
													<f:selectItems
														value="#{pc_Cog03401.propMokuNameKanaFindType.list}" />
												</h:selectOneRadio></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_e"><h:outputText styleClass="outputText"
										id="lblSknShohiKbn"
										value="#{pc_Cog03401.propSknShohiKbn.labelName}"
										style="#{pc_Cog03401.propSknShohiKbn.style}"></h:outputText></TH>
									<TD width="220"><h:selectManyCheckbox
										styleClass="selectManyCheckbox setWidth" id="htmlSknShohiKbn"
										value="#{pc_Cog03401.propSknShohiKbn.value}"
										disabledClass="selectManyCheckbox_Disabled" tabindex="7"
										readonly="#{pc_Cog03401.propSknShohiKbn.readonly}"
										disabled="#{pc_Cog03401.propSknShohiKbn.disabled}">
										<f:selectItems value="#{pc_Cog03401.propSknShohiKbn.list}" />
									</h:selectManyCheckbox></TD>
									<TH width="150" class="v_f"><h:outputText
										styleClass="outputText" id="lblMokuKbn"
										value="#{pc_Cog03401.propMokuKbn.labelName}"
										style="#{pc_Cog03401.propMokuKbn.style}"></h:outputText></TH>
									<TD width="330">
									
									<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlMokuKbn" style="width:320px;"
										value="#{pc_Cog03401.propMokuKbn.value}"
										disabled="#{pc_Cog03401.propMokuKbn.disabled}"
										readonly="#{pc_Cog03401.propMokuKbn.readonly}" tabindex="8">
										<f:selectItems value="#{pc_Cog03401.propMokuKbn.list}" />
									</h:selectOneMenu>
									
									
									</TD>
								</TR>
								<TR>
									<TH class="v_g"><h:outputText styleClass="outputText"
										id="lblShushiKbn" value="#{pc_Cog03401.propShushiKbn.labelName}"
										style="#{pc_Cog03401.propShushiKbn.style}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlShushiKbn" style="width:200px;"
										value="#{pc_Cog03401.propShushiKbn.value}"
										disabled="#{pc_Cog03401.propShushiKbn.disabled}"
										readonly="#{pc_Cog03401.propShushiKbn.readonly}" tabindex="9">
										<f:selectItems value="#{pc_Cog03401.propShushiKbn.list}" />
									</h:selectOneMenu></TD>
									<TH class="v_a"><h:outputText styleClass="outputText"
										id="lblLvl" value="#{pc_Cog03401.propLvl.labelName}"
										style="#{pc_Cog03401.propLvl.style}"></h:outputText></TH>
									<TD>
									<h:selectOneMenu styleClass="selectOneMenu" id="htmlLvl"
										style="width:150px;" value="#{pc_Cog03401.propLvl.value}"
										disabled="#{pc_Cog03401.propLvl.disabled}"
										readonly="#{pc_Cog03401.propLvl.readonly}" tabindex="10">
										<f:selectItems value="#{pc_Cog03401.propLvl.list}" />
									</h:selectOneMenu>
									
									</TD>
								</TR>
							</TBODY>
						</TABLE><BR>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="800">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="検索" styleClass="commandExButton_dat" id="search" action="#{pc_Cog03401.doSearchAction}" tabindex="11"></hx:commandExButton><hx:commandExButton type="submit" value="クリア" styleClass="commandExButton_etc" id="clear" action="#{pc_Cog03401.doClearAction}" tabindex="12"></hx:commandExButton><hx:commandExButton
										type="submit" value="CSV作成" styleClass="commandExButton_out"
										id="csvout" action="#{pc_Cog03401.doCsvoutAction}"
										tabindex="13" confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton type="submit" value="出力項目指定" styleClass="commandExButton_out" id="setoutput" action="#{pc_Cog03401.doSetoutputAction}" tabindex="14"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="30" height="20">　</TD>
									<TD width="36" height="20" align="right">
									<h:outputText styleClass="outputText" id="htmlKaikeiNendoDsp"
										value="#{pc_Cog03401.propKaikeiNendoDsp.dateValue}"
										style="#{pc_Cog03401.propKaikeiNendoDsp.labelStyle}">
										<f:convertDateTime pattern="yyyy" />
									</h:outputText>
									<TD width="64"  height="20" align="left">
									<h:outputText styleClass="outputText" id="lblNendo" value="年度"></h:outputText></TD>
									<TD width="700" height="20" align="right"><h:outputText
										styleClass="outputText" id="htmlListCount" value="#{pc_Cog03401.propTableList1.listCount}">

									</h:outputText><h:outputText styleClass="outputText" id="lblKen" value="件"></h:outputText></TD>
									<TD width="30" height="20"></TD>
								</TR>
								<TR>
									<TD width="30" height="20">　</TD>
									<TD colspan="4" align="left" nowrap>
									<TABLE border="1" class="meisai_page" height="20">
										<TR>
											<TH align="center" nowrap width="96"><h:outputText styleClass="outputText"
												id="lblColMokuCd"
												value="目的コード"></h:outputText></TH>
											<TH align="center" nowrap width="76"><h:outputText styleClass="outputText"
												id="lblColLvl"
												value="レベル"></h:outputText></TH>
											<TH align="center" nowrap width="549"><h:outputText styleClass="outputText"
												id="lblColMokuName"
												value="目的名称"></h:outputText></TH>
											<TH align="center" nowrap width="91"><h:outputText styleClass="outputText"
												id="lblColDummy"
												value="　"></h:outputText></TH>
										</TR>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD width="30">　</TD>
									<TD colspan="4" align="left" nowrap>
									<DIV class="listScroll" style="height: 295px; width: 815px"
										id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
										border="0" cellpadding="2" cellspacing="0"
										headerClass="headerClass" footerClass="footerClass"
										rowClasses="#{pc_Cog03401.propTableList1.rowClasses}"
										styleClass="meisai_scroll" id="htmlTableList1" var="varlist"
										width="798" value="#{pc_Cog03401.propTableList1.list}"
										style="table-layout: fixed;">
										<h:column id="column1">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColMokuCd"
												value="#{varlist.colMokuCd}"></h:outputText>
											<f:attribute value="95" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColLvl"
												value="#{varlist.colLvl}">
												
											</h:outputText>
											<f:attribute value="75" name="width" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlColMokuName"
												value="#{varlist.colMokuName.displayValue}"
												title="#{varlist.colMokuName.value}"
												style="white-space:nowrap;"></h:outputText>
											<f:attribute value="549" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="編集"
												styleClass="commandExButton" id="editMoku"
												action="#{pc_Cog03401.doEditMokuAction}" style="width: 36px" tabindex="15"></hx:commandExButton>
											<f:attribute value="36" name="width" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="ｺﾋﾟｰ"
												styleClass="commandExButton" id="copyMoku"
												action="#{pc_Cog03401.doCopyMokuAction}" style="width: 36px" tabindex="15"></hx:commandExButton>
											<f:attribute value="36" name="width" />
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Cog03401.propTableList1.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Cog03401.propExecutableSearchHidden.integerValue}"
				id="htmlExecutableSearchHidden">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Cog03401.propCsvOutHidden.stringValue}"
				id="htmlCsvOutHidden"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

