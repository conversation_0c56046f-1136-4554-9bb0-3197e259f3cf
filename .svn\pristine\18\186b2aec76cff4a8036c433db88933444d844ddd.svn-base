<%-- 
	事務局割当（割当対象選択）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/kk/Kkd00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kkz01001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageCO" var="msgCO"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kkd00301.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Kkd00301">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
  styleClass="commandExButton" id="closeDisp"
  action="#{pc_Kkd00301.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kkd00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kkd00301.screenName}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE class="table" width="700px" style="margin-top:80px">
							<TBODY>
								<TR>
									<TH class="v_a" width="150px" nowrap>
									<!-- 校友会 -->
										<h:outputText id="lblKoyuCombo" styleClass="outputText"
											style="#{pc_Kkd00301.propKoyuCombo.labelStyle}"
											value="#{pc_Kkd00301.propKoyuCombo.labelName}" /></TH>
									<TD witdh="550px">
										<h:selectOneMenu id="htmlKoyuCombo" styleClass="selectOneMenu"
											style="width:430px;"
											disabled="#{pc_Kkd00301.propKoyuCombo.disabled}"
											value="#{pc_Kkd00301.propKoyuCombo.value}">
											<f:selectItems value="#{pc_Kkd00301.propKoyuCombo.list}" />
										</h:selectOneMenu>
										<hx:commandExButton type="submit" value="選択"
											styleClass="commandExButton" id="select"
											disabled="#{pc_Kkd00301.propSelect.disabled}"
											action="#{pc_Kkd00301.doSelectAction}" />
										<hx:commandExButton type="submit" value="解除"
											styleClass="commandExButton" id="unselect"
											disabled="#{pc_Kkd00301.propUnselect.disabled}"
											action="#{pc_Kkd00301.doUnselectAction}" /></TD>
								</TR>
								<TR>
									<TH class="v_b" nowrap>
									<!-- 事務局 -->
										<h:outputText id="lblJimukyokuCombo" styleClass="outputText"
											style="#{pc_Kkd00301.propJimukyokuCombo.labelStyle}"
											value="#{pc_Kkd00301.propJimukyokuCombo.labelName}" /></TH>
									<TD>
										<h:selectOneMenu id="htmlJimukyokuCombo" styleClass="selectOneMenu"
											style="width:430px;"
											disabled="#{pc_Kkd00301.propJimukyokuCombo.disabled}"
											value="#{pc_Kkd00301.propJimukyokuCombo.value}">
											<f:selectItems value="#{pc_Kkd00301.propJimukyokuCombo.list}" />
										</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_c" nowrap>
									<!-- 対象検索指定 -->
										<h:outputText id="lblMode" styleClass="outputText"
											style="#{pc_Kkd00301.propMode.labelStyle}"
											value="#{pc_Kkd00301.propMode.labelName}" /></TH>
									</TH>
									<TD>
										<h:selectOneRadio id="htmlMode" styleClass="selectOneRadio setWidth"
											disabledClass="selectOneRadio_Disabled"
											value="#{pc_Kkd00301.propMode.stringValue}"
											layout="pageDirection">
											<f:selectItem itemValue="0" itemLabel="管理出身地" />
											<f:selectItem itemValue="1" itemLabel="管理郵便番号" />
										</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE class="button_bar" width="700px" style="margin-top:10px">
							<TBODY>
								<TR>
									<TD align="center">
										<hx:commandExButton type="submit" id="forword" value="次へ"
											styleClass="commandExButton_out"
											action="#{pc_Kkd00301.doForwordAction}" /></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑-->
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />

</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
