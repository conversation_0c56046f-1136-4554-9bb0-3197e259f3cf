<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coi01501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Coi01501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:htmlExecutableShomBunrui').value = "1";
	indirectClick('delete');
}
function confirmCancel() {
	document.getElementById('form1:propExecutableShomBunrui').value = "0";
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Coi01501.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Coi01501.doCloseDispAction}"
></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Coi01501.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Coi01501.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
	<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0" width="700">
				<TBODY>
					<TR>

						<TD align="right"><h:outputText styleClass="outputText" id="textCnt1" value="#{pc_Coi01501.propCount.stringValue}" style="font-size: 8pt"></h:outputText>
							<h:outputText styleClass="outputText" id="textCnt2" value="件" style="font-size: 8pt"></h:outputText>
						</TD>

					</TR>
				</TBODY>
			</TABLE>
			
			<TABLE border="0" cellpadding="0" cellspacing="0" width="700">
				<TBODY>
					<TR>
						<TD>
							<div class="listScroll" style="height:337px;" id="listScroll" onscroll="setScrollPosition('scroll',this);">
							<h:dataTable border="0" cellpadding="2" cellspacing="0"
								headerClass="headerClass" footerClass="footerClass"
								rowClasses="#{pc_Coi01501.propShomBunruiList.rowClasses}"
								styleClass="meisai_scroll" id="table1"
								value="#{pc_Coi01501.propShomBunruiList.list}" var="varlist">
								<!-- 並び順 -->
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText id="text1" styleClass="outputText" value="並び順"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text2"
										value="#{varlist.orderNo}"
										style="text-align: left; vertical-align: baseline"></h:outputText>
									<f:attribute value="60" name="width" />
									<f:attribute value="text-align: right" name="style" />
								</h:column>
								<!-- 職名分類コード -->
								<h:column id="column2">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="コード" id="text3"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text4"
										value="#{varlist.shokumeiBunruiCd}"></h:outputText>
									<f:attribute value="80" name="width" />
								</h:column>
								<!-- 職名分類名称 -->
								<h:column id="column3">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="職名分類名称" id="text5"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text6"
										value="#{varlist.shokumeiBunruiName}"></h:outputText>
									<f:attribute value="350" name="width" />
								</h:column>
								<!-- 職名分類区分 -->
								<h:column id="column4">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="職名分類区分" id="text7"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text8"
										value="#{varlist.shokumeiBunruiKbn}"></h:outputText>
									<f:attribute value="120" name="width" />
								</h:column>
								<!-- 出力対象 -->
								<h:column id="column5">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="出力対象" id="text9"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text10"
										value="#{varlist.outPutName}"></h:outputText>
									<f:attribute value="60" name="width" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
								<!-- 選択ボタン -->
								<h:column id="column6">
									<f:facet name="header">
									</f:facet>
									<hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select"
										action="#{pc_Coi01501.doSelectAction}"></hx:commandExButton>
									<f:attribute value="30" name="width" />
								</h:column>
							</h:dataTable>
							</div>
						
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="700">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
							<TBODY>
								<!-- 職名分類コード -->
								<TR>
									<TH width="30%" class="v_a"><h:outputText
										styleClass="outputText" id="lblShokumeiBunruiCd"
										value="#{pc_Coi01501.propShokumeiBunruiCd.labelName}"
										style="#{pc_Coi01501.propShokumeiBunruiCd.labelStyle}"></h:outputText></TH>
									<TD width="70%"><h:inputText styleClass="inputText"
										id="htmlShokumeiBunruiCd"
										value="#{pc_Coi01501.propShokumeiBunruiCd.stringValue}"
										maxlength="#{pc_Coi01501.propShokumeiBunruiCd.maxLength}"
										style="#{pc_Coi01501.propShokumeiBunruiCd.style}" size="2">
										</h:inputText>
									</TD>
								</TR>
								<!-- 職名分類名称 -->
								<TR>
									<TH width="30%" class="v_b"><h:outputText
										styleClass="outputText" id="lblShokumeiBunruiName"
										value="#{pc_Coi01501.propShokumeiBunruiName.labelName}"
										style="#{pc_Coi01501.propShokumeiBunruiName.labelStyle}"></h:outputText></TH>
									<TD width="70%"><h:inputText styleClass="inputText"
										id="htmlShokumeiBunruiName"
										value="#{pc_Coi01501.propShokumeiBunruiName.stringValue}"
										style="#{pc_Coi01501.propShokumeiBunruiName.style}"
										maxlength="#{pc_Coi01501.propShokumeiBunruiName.maxLength}" size="40">
										</h:inputText>
									</TD>
								</TR>
								<!-- 職名分類区分 -->
								<TR>
									<TH width="30%" class="v_c"><h:outputText
										styleClass="outputText" id="lblShokumeiBunruiKbn"
										value="#{pc_Coi01501.propShokumeiBunruiKbn.labelName}"
										style="#{pc_Coi01501.propShokumeiBunruiKbn.labelStyle}"></h:outputText></TH>
									<TD width="70%"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlShokumeiBunruiKbn"
										value="#{pc_Coi01501.propShokumeiBunruiKbn.value}"
										style="#{pc_Coi01501.propShokumeiBunruiKbn.style}">
										<f:selectItems value="#{pc_Coi01501.propShokumeiBunruiKbn.list}"/>
										</h:selectOneMenu>
									</TD>
								</TR>
								<!-- 並び順 -->
								<TR>
									<TH width="30%" class="v_d"><h:outputText
										styleClass="outputText" id="lblOrderNo"
										value="#{pc_Coi01501.propOrderNo.labelName}"
										style="#{pc_Coi01501.propOrderNo.labelStyle}"></h:outputText></TH>
									<TD width="70%"><h:inputText
										styleClass="inputText" id="htmlOrderNo"
										value="#{pc_Coi01501.propOrderNo.integerValue}"
										maxlength="#{pc_Coi01501.propOrderNo.maxLength}"
										style="#{pc_Coi01501.propOrderNo.style}" size="3">
										<f:convertNumber type="number" pattern="##0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR>
								<!-- 出力対象 -->
								<TR>
									<TH width="30%" class="v_e"><h:outputText
										styleClass="outputText" id="lblOutPutFlg"
										value="#{pc_Coi01501.propOutPutFlg.labelName}"></h:outputText></TH>
									<TD width="70%"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlOutPutFlg"
										value="#{pc_Coi01501.propOutPutFlg.value}">
										<f:selectItem itemValue="1" itemLabel="出力対象にする" />
										<f:selectItem itemValue="0" itemLabel="出力対象にしない" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center" height="16" width="100%"><hx:commandExButton
							type="submit" value="確定" styleClass="commandExButton_dat"
							id="register" action="#{pc_Coi01501.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Coi01501.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Coi01501.doClearAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
	</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Coi01501.propShomBunruiList.scrollPosition}" id="scroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Coi01501.propExecutableShomBunrui.integerValue}"
				id="htmlExecutableShomBunrui">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

