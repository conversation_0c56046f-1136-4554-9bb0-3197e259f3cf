<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coc00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Coc00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
	// 全選択ボタン押下時の処理
	function func_1(thisObj, thisEvent) {
		check('htmlReportList','htmlColCheck');
	}
	
	// 全解除ボタン押下時の処理
	function func_2(thisObj, thisEvent) {
		uncheck('htmlReportList','htmlColCheck');
	}

	// 確認ダイアログで「ＯＫ」の場合
	function confirmOk() {
		document.getElementById("form1:htmlHidOutPutButton").value = "1";
		var action = document.getElementById("form1:htmlHidAction").value;
		indirectClick(action);
	}

	// 確認ダイアログで「キャンセル」の場合
	function confirmCancel() {
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Coc00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ページコード定義 -->
			<jsp:useBean id="pc_Coc00201" scope="session"
				class="com.jast.gakuen.rev.co.Coc00201" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Coc00201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Coc00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Coc00201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR align="center">
						<TD width="974">
							<TABLE width="700" border="0" cellpadding="3" cellspacing="0">
								<TBODY>
									<TR>
										<TD>
											<h:dataTable border="0" cellpadding="3" cellspacing="0"
												columnClasses="columnClass1"
												headerClass="headerClass" footerClass="footerClass"
												rowClasses="#{pc_Coc00201.propReportList.rowClasses}"
												styleClass="meisai_scroll" width="700" var="varlist"
												id="htmlReportList" value="#{pc_Coc00201.propReportList.list}"
												first="#{pc_Coc00201.propReportList.first}"
												rows="#{pc_Coc00201.propReportList.rows}">
												<!--出力-->
												<h:column id="column1">
													<f:facet name="header">
														<h:outputText id="lblSelected" styleClass="outputText" 
															value="出力"></h:outputText>
													</f:facet>
													<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
														id="htmlColCheck" value="#{varlist.selected}"
														disabled="#{varlist.reportDisabled}"></h:selectBooleanCheckbox>
													<f:attribute value="40" name="width" />
													<f:attribute value="text-align:center;" name="style" />
												</h:column>
												<!--帳票-->
												<h:column id="column2">
													<f:facet name="header">
														<h:outputText id="lblReportName" styleClass="outputText" 
															value="帳票"></h:outputText>
													</f:facet>
													<h:outputText styleClass="outputText" id="htmlReportName"
														value="#{varlist.reportName}" rendered="#{varlist.rendered}"></h:outputText>
													<f:attribute value="200" name="width" />
												</h:column>
												<!--帳票タイトル-->
												<h:column id="column3">
													<f:facet name="header">
														<h:outputText id="lblReportTitle" styleClass="outputText" 
															value="帳票タイトル(全30)"></h:outputText>
													</f:facet>
													<h:inputText styleClass="inputText" id="htmlReportTitle" size="68"
															value="#{varlist.propReportTitle.stringValue}"
															disabled="#{varlist.reportDisabled}"
															style="#{varlist.propReportTitle.style}"
															maxlength="#{varlist.propReportTitle.maxLength}"></h:inputText>
													<f:attribute value="450" name="width" />
												</h:column>
											</h:dataTable>
										</TD>
									</TR>
									<TR>
										<TD align="left">
											<hx:commandExButton type="button" styleClass="check"
												id="check" onclick="return func_1(this, event);"></hx:commandExButton>
											<hx:commandExButton type="button" styleClass="uncheck"
												id="uncheck" onclick="return func_2(this, event);"></hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE width="700" border="0" cellpadding="0" cellspacing="0"
								class="table" style="margin-top:10px;">
								<TBODY>
									<TR>
									<!--卒業年度-->
										<TH width="25%" class="v_e"><h:outputText styleClass="outputText" id="lblSotNendo"
											value="#{pc_Coc00201.propSotNendo.labelName}"
											style="#{pc_Coc00201.propSotNendo.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlSotNendo"
											value="#{pc_Coc00201.propSotNendo.value}"
											style="#{pc_Coc00201.propSotNendo.style}"
											disabled="#{pc_Coc00201.propSotNendo.disabled}" size="4">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" /></h:inputText>
										</TD>
									</TR>
									<TR>
									<!--データ取得日付-->
										<TH width="25%" class="v_e"><h:outputText styleClass="outputText" id="lblDataDate"
											value="#{pc_Coc00201.propDataDate.labelName}"
											style="#{pc_Coc00201.propDataDate.labelStyle}"></h:outputText></TH>
										<TD><h:inputText styleClass="inputText" id="htmlDataDate"
											value="#{pc_Coc00201.propDataDate.dateValue}" size="10">
											<f:convertDateTime />
											<hx:inputHelperDatePicker />
											<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" /></h:inputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE width="700" border="0" cellpadding="0" cellspacing="0"
								class="table" style="margin-top:10px;">
								<TBODY>
									<TR>
									<!--学校-->
										<TH width="25%" class="v_e"><h:outputText styleClass="outputText" id="lblGakko"
											value="#{pc_Coc00201.propGakko.labelName}"
											style="#{pc_Coc00201.propGakko.labelStyle}"></h:outputText></TH>
										<TD><h:selectOneMenu
											styleClass="selectOneMenu" id="htmlGakko"
											value="#{pc_Coc00201.propGakko.value}"
											disabled="#{pc_Coc00201.propGakko.disabled}">
											<f:selectItems value="#{pc_Coc00201.propGakko.list}" />
										</h:selectOneMenu>
										</TD>
										<TD rowspan="2" width="80"  style="background-color: transparent; text-align: left; 
											vertical-align: bottom; padding-left: 10px;" class="clear_border">
											<hx:commandExButton type="submit"
												value="選択" styleClass="commandExButton" id="selectMeisai"
												action="#{pc_Coc00201.doSelectMeisaiAction}"
												disabled="#{pc_Coc00201.propButtonSelectMeisai.disabled}"></hx:commandExButton>
											<hx:commandExButton type="submit"
												value="解除" styleClass="commandExButton" id="unselectMeisai"
												action="#{pc_Coc00201.doUnselectMeisaiAction}"
												disabled="#{pc_Coc00201.propButtonUnselectMeisai.disabled}"></hx:commandExButton>
										</TD>
									</TR>
									<TR>
									<!--明細出力レベル指定-->
										<TH width="25%" class="v_e"><h:outputText styleClass="outputText" id="lblMeisaiOutputLvl"
											value="#{pc_Coc00201.propMeisaiOutputLvl.labelName}"
											style="#{pc_Coc00201.propMeisaiOutputLvl.labelStyle}"></h:outputText></TH>
										<TD><h:selectOneMenu
											styleClass="selectOneMenu" id="htmlMeisaiOutputLvl"
											value="#{pc_Coc00201.propMeisaiOutputLvl.value}"
											disabled="#{pc_Coc00201.propMeisaiOutputLvl.disabled}">
											<f:selectItems value="#{pc_Coc00201.propMeisaiOutputLvl.list}" />
										</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
									<!--学科組織-->
										<TH width="25%" class="v_f"><h:outputText styleClass="outputText" id="lblSzkGakka"
											value="#{pc_Coc00201.propSzkGakkaList.labelName}"
											style="#{pc_Coc00201.propSzkGakkaList.labelStyle}"></h:outputText><BR>
										<h:outputText styleClass="outputText" id="text1" value="（複数選択可）"></h:outputText>
										</TH>
										<TD colspan="2"><h:selectManyListbox
											styleClass="selectManyListbox" id="htmlGakkaList"
											value="#{pc_Coc00201.propSzkGakkaList.value}"
											disabled="#{pc_Coc00201.propSzkGakkaList.disabled}" size="5"
											style="width:95%">
											<f:selectItems value="#{pc_Coc00201.propSzkGakkaList.list}" />
										</h:selectManyListbox></TD>
									</TR>
								</TBODY>
							</TABLE>
							<TABLE width="700" border="0" cellpadding="0" cellspacing="0"
								class="table" style="margin-top:10px;">
								<TBODY>
									<TR>
									<!--改頁出力レベル指定-->
										<TH width="25%" class="v_e"><h:outputText styleClass="outputText" id="lblKaiPageLvl"
											value="#{pc_Coc00201.propKaiPageLvl.labelName}"
											style="#{pc_Coc00201.propKaiPageLvl.labelStyle}"></h:outputText></TH>
										<TD><h:selectOneMenu
											styleClass="selectOneMenu" id="htmlKaiPageLvl"
											value="#{pc_Coc00201.propKaiPageLvl.value}"
											disabled="#{pc_Coc00201.propKaiPageLvl.disabled}">
											<f:selectItems value="#{pc_Coc00201.propKaiPageLvl.list}" />
										</h:selectOneMenu> 
										</TD>
								</TBODY>
							</TABLE>
							<TABLE width="700" border="0" cellpadding="0" cellspacing="0"
								class="table" style="margin-top:10px;">
								<TBODY>
									<TR>
									<!--学年-->
										<TH width="25%" class="v_f"><h:outputText styleClass="outputText" id="lblGakunen"
											value="#{pc_Coc00201.propGakunenList.labelName}"
											style="#{pc_Coc00201.propGakunenList.labelStyle}"></h:outputText><BR>
										<h:outputText styleClass="outputText" id="text2" value="（複数選択可）"></h:outputText>
										</TH>
										<TD colspan="2"><h:selectManyListbox
											styleClass="selectManyListbox" id="htmlGakunenList"
											value="#{pc_Coc00201.propGakunenList.value}"
											disabled="#{pc_Coc00201.propGakunenList.disabled}" size="5"
											style="width:40%">
											<f:selectItems value="#{pc_Coc00201.propGakunenList.list}" />
										</h:selectManyListbox></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar">
				<TBODY>
					<TR align="right">
						<TD width="974"><hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfout"
							action="#{pc_Coc00201.doPdfOutAction}"
							confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
							<hx:commandExButton type="submit" value="EXCEL作成"
							styleClass="commandExButton_out" id="excelout"
							action="#{pc_Coc00201.doExcelOutAction}"
							confirm="#{msg.SY_MSG_0027W}"></hx:commandExButton>
							<hx:commandExButton type="submit" value="CSV作成" 
							styleClass="commandExButton_out" id="csvout" 
							action="#{pc_Coc00201.doCsvOutAction}"
							confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<h:inputHidden id="htmlHidAction" value="#{pc_Coc00201.propHidAction.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlHidOutPutButton" value="#{pc_Coc00201.propHidOutPutButton.integerValue}"></h:inputHidden>
			
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

