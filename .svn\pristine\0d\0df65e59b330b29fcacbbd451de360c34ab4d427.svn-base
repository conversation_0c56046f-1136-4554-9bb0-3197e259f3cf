<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_COA_NYS_SBT" name="入試種別" prod_id="CO" description="入試年度毎の入試種別の情報を持ちます。
_『入試種別設定』にて設定されます。">
<STATMENT><![CDATA[
COA_NYS_SBT
]]></STATMENT>
<COLUMN id="NYUSHI_NENDO" name="入試年度" type="number" length="4" lengthDP="0" byteLength="0" description="入試年度が設定されます。"/><COLUMN id="NYUSHI_GAKKI_NO" name="入試学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="学期を表す一意の番号が設定されます。"/><COLUMN id="NYS_SBT_CD" name="入試種別コード" type="string" length="6" lengthDP="0" byteLength="6" description="入試種別を表す一意のコードが設定されます。"/><COLUMN id="NYS_SBT_NAME" name="入試種別名称" type="string" length="60" lengthDP="0" byteLength="180" description="入試種別の名称が設定されます。"/><COLUMN id="TAIGAI_NAME" name="入試種別対外名称" type="string" length="60" lengthDP="0" byteLength="180" description="入試種別の対外的に利用する名称が設定されます。"/><COLUMN id="NYUSHI_SBT_BUNRUI_KBN" name="入試種別分類区分" type="string" length="1" lengthDP="0" byteLength="1" description="入試種別のレベルを識別する区分がシステムで自動的に設定されます。"><CODE><CASE value="0" display="明細レコード"/><CASE value="1" display="小分類レコード"/><CASE value="2" display="中分類レコード"/><CASE value="3" display="大分類レコード"/></CODE></COLUMN><COLUMN id="NYUGAK_GAKUNEN" name="入学時学年" type="number" length="1" lengthDP="0" byteLength="0" description="当入試種別の受験者が入学するときに設定される学年が設定されます。"/><COLUMN id="NYUGAK_SEMESTER" name="入学時セメスタ" type="number" length="2" lengthDP="0" byteLength="0" description="当入試種別の受験者が入学するときに設定されるセメスタが設定されます。"/><COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="学生の実際に入学する入学年度が設定されます。"/><COLUMN id="NYUGAK_GAKKI_NO" name="入学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="学生の実際に入学する学期が設定されます。"/><COLUMN id="NYUGAK_NENDO_CUR" name="みなし入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="学生のカリキュラムの基準となる年度が設定されます。通常の学生は入学年度と同じになりますが、編入生等で実際の入学年度とカリキュラムの年度が異なる場合に、みなし入学年度にカリキュラムの基準となる年度が設定されます。"/><COLUMN id="NYUGAK_GAKKI_NO_CUR" name="みなし入学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="学生のカリキュラムの基準となる学期が設定されます。通常の学生は入学学期と同じになりますが、編入生等で実際の入学学期とカリキュラムの学期が異なる場合に、みなし入学期にカリキュラムの基準となる学期が設定されます。"/><COLUMN id="NYUGAK_SBT_CD" name="入学種別コード" type="string" length="2" lengthDP="0" byteLength="2" description="入学種別を表す任意の区分です。"/><COLUMN id="SYUGAK_SBT_CD" name="就学種別コード" type="string" length="2" lengthDP="0" byteLength="2" description="就学種別を表す任意の区分です。"/>
</TABLE>
