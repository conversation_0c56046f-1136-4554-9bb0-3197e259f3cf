<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cog06201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>研究資金区分設定</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function endload(){
	changeScrollPosition('scroll', 'listScroll');
}

window.attachEvent('onload', endload);
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cog06201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cog06201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cog06201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cog06201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" width="100%" style="margin-top:10px">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="565" align="right"><h:outputText
							styleClass="outputText" id="htmlListCount"
							value="#{pc_Cog06201.propKnsknKubunList.listCount}"
							style="font-size: 8pt"></h:outputText><FONT><SPAN
							style="font-size: 8pt">件</SPAN></FONT></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<CENTER>
			<TABLE border="0" width="550">
				<TBODY>
					<TR>
						<TD>
						<DIV id="listScroll" onscroll="setScrollPosition('scroll',this);"
							class="listScroll" style="height:354px;width:570px;OVERFLOW:scroll;overflow-x: hidden"><h:dataTable border="0"
							cellpadding="2" cellspacing="0" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Cog06201.propKnsknKubunList.rowClasses}"
							id="htmlKnsknKubunList" width="550"
							value="#{pc_Cog06201.propKnsknKubunList.list}" var="varlist"
							styleClass="meisai_scroll"
							first="#{pc_Cog06201.propKnsknKubunList.first}"
							rows="#{pc_Cog06201.propKnsknKubunList.rows}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListKenkyuSknKbn"
										value="#{pc_Cog06201.propKenkyuSknKbn.name}"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListKenkyuSknKbn"
									value="#{varlist.kenkyuSknKbn}"></h:outputText>
								<f:attribute value="100" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListKenkyuSknName"
										value="#{pc_Cog06201.propKenkyuSknName.name}"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListKenkyuSknName"
									value="#{varlist.kenkyuSknName}"></h:outputText>
								<f:attribute value="300" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblListYukoMuko"
										value="状況"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListYukoMuko"
									value="#{varlist.yukoMuko}"></h:outputText>
								<f:attribute value="75" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit"
									styleClass="commandExButton" id="select" value="選択"
									action="#{pc_Cog06201.doSelectAction}"></hx:commandExButton>
                                <f:attribute value="28px" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE><BR>
            </CENTER>
			<CENTER>
			<TABLE width="570" border="0" cellpadding="0" cellspacing="0"
				class="table" style="margin-top:10px">
				<TBODY>
					<TR>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblKenkyuSknKbn"
							style="#{pc_Cog06201.propKenkyuSknKbn.labelStyle}"
							value="#{pc_Cog06201.propKenkyuSknKbn.labelName}"></h:outputText></TH>

						<TD><h:inputText styleClass="inputText" id="htmlKenkyuSknKbn"
							maxlength="#{pc_Cog06201.propKenkyuSknKbn.maxLength}"
							value="#{pc_Cog06201.propKenkyuSknKbn.stringValue}"
							style="#{pc_Cog06201.propKenkyuSknKbn.style}" size="3">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText styleClass="outputText"
							id="lblKenkyuSknName"
							style="#{pc_Cog06201.propKenkyuSknName.labelStyle}"
							value="#{pc_Cog06201.propKenkyuSknName.labelName}"></h:outputText></TH>

						<TD><h:inputText styleClass="inputText" id="htmlKenkyuSknName"
							maxlength="#{pc_Cog06201.propKenkyuSknName.maxLength}"
							value="#{pc_Cog06201.propKenkyuSknName.stringValue}"
							style="#{pc_Cog06201.propKenkyuSknName.style}" size="40">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_c"><h:outputText styleClass="outputText"
							id="lblYukoMuko" style="#{pc_Cog06201.propYukoMuko.labelStyle}"
							value="有効無効"></h:outputText></TH>

						<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlYukoMuko"
							value="#{pc_Cog06201.propYukoMuko.value}">
							<f:selectItem itemValue="1" itemLabel="有効" />
							<f:selectItem itemValue="0" itemLabel="無効" />
						</h:selectOneRadio></TD>
					</TR>
				</TBODY>
			</TABLE><BR>
			</CENTER>

			<CENTER>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="570"
				border="0" cellpadding="0" cellspacing="0" height="25"
				class="button_bar" style="margin-top:10px">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="登録"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Cog06201.doRegisterAction}"
							confirm="#{msg.SY_MSG_0002W}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit" value="更新"
							styleClass="commandExButton_dat" id="update"
							action="#{pc_Cog06201.doUpdateAction}"
							confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit" value="削除"
							styleClass="commandExButton_dat" id="delete"
							action="#{pc_Cog06201.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clear"
							action="#{pc_Cog06201.doClearAction}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit" value="CSV作成"
							styleClass="commandExButton_out" id="csvout"
							action="#{pc_Cog06201.doCsvoutAction}"
							confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton></TD>
						<TD align="center"><hx:commandExButton type="submit"
							value="出力項目指定" styleClass="commandExButton_out" id="setoutput"
							action="#{pc_Cog06201.doSetoutputAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Cog06201.propKnsknKubunList.scrollPosition}" id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
</SCRIPT>
</HTML>

