<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_NSG_CTR_HG" name="センター試験併願台帳" prod_id="NS" description="大学入試センターから提供された、他大学・学部への出願・合否情報を持ちます。|『センター試験出願状況登録』及び『センター試験合格状況登録』にて作成され、『センター試験併願状況一覧』を出力します。|『期末処理』で削除されます。">
<STATMENT><![CDATA[
NSG_CTR_HG
]]></STATMENT>
<COLUMN id="JUKEN_CD" name="受験番号" type="string" length="10" lengthDP="0" byteLength="10" description="受験番号です。"/><COLUMN id="HGAN_DAI_CD" name="併願大学コード" type="string" length="4" lengthDP="0" byteLength="4" description="志願者の併願した大学のコードです（大学入試センター指定）。"/><COLUMN id="HGAN_GAK_CD" name="併願学部コード" type="string" length="4" lengthDP="0" byteLength="4" description="志願者の併願した大学の学部コードです（大学入試センター指定）。"/><COLUMN id="GOHI_SBT_CD" name="合否種別コード" type="string" length="1" lengthDP="0" byteLength="1" description="合否種別を識別する任意のコードです。|段階選抜実施の場合、最終結果が入ります。"/>
</TABLE>
