<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssg01603.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssg016.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	var url="${pageContext.request.contextPath}/faces/rev/ss/pSsc0101.jsp?retFieldName=form1:htmlKigyoCd";
	//openModalWindow(url, "pSsc0101", "status=yes,toolbar=no,menubar=no,location=no");
	var pageCodeClass = "com.jast.gakuen.rev.ss.PSsc0101";
	removeSessionAndOpenWindow(pageCodeClass 
	,url, "pSsc0101", "<%=com.jast.gakuen.rev.ss.PSsc0101.getWindowOpenOption() %>","Ssg016");
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssg01603.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssg01603.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssg01603.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssg01603.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton" id="btnBack"
				action="#{pc_Ssg01603.doBtnBackAction}"></hx:commandExButton> <!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR align="center">
						<TD width="871">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="871">
							<TBODY>
								<TR>
									<TH class="v_a" width="161"><h:outputText
										styleClass="outputText" id="htmlLblKigyoCd"
										value="#{pc_Ssg01603.propKigyoCd.labelName}"
										style="#{pc_Ssg01603.propKigyoCd.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlKigyoCd"
										value="#{pc_Ssg01603.propKigyoCd.stringValue}"
										style="#{pc_Ssg01603.propKigyoCd.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="161"><h:outputText
										styleClass="outputText" id="htmlLblKigyoName"
										value="#{pc_Ssg01603.propKigyoName.labelName}"
										style="#{pc_Ssg01603.propKigyoName.labelStyle}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText" id="htmlKigyoName"
										value="#{pc_Ssg01603.propKigyoName.stringValue}"
										style="#{pc_Ssg01603.propKigyoName.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<div style="height:300px; width=" 871px" id="listScroll"
							onscroll="setScroll('scroll', this);" class="listScroll"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssg01603.propKanendoKyujin.rowClasses}"
							styleClass="meisai_scroll" id="htmlKanendoKyujin"
							value="#{pc_Ssg01603.propKanendoKyujin.list}" var="varlist"
							columnClasses="columnClass1" width="851">
							<h:column id="colNendo">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="年度"
										id="listLblNendo"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listNendo"
									value="#{varlist.nendo}">
									<f:convertNumber />
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
								<f:attribute value="40" name="width" />
							</h:column>
							<h:column id="colGakko">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学校"
										id="listLblGakko"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listGakko"
									value="#{varlist.gakko}"></h:outputText>
								<f:attribute value="161" name="width" />
							</h:column>
							<h:column id="colKyujinUmu">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="求人有無"
										id="listLblKyujinUmu"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listKyujinUmu"
									value="#{varlist.kyujinUmu}"></h:outputText>
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="colSaiyoYoteiSu">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="採用予定数"
										id="listLblSaiyoYoteiSu"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listSaiyoYoteiSu"
									value="#{varlist.saiyoYoteiSu}"></h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="90" name="width" />
							</h:column>
							<h:column id="colKyujinSu">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="求人数"
										id="listLblKyujinSu"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listKyujinSu"
									value="#{varlist.kyujinSu}"></h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="60" name="width" />
							</h:column>
							<h:column id="colNaiteisyaSu">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="内定者数"
										id="listLblNaiteisyaSu"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listNaiteisyaSu"
									value="#{varlist.naiteisyaSu}">
									<f:convertNumber />
								</h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="colNaiteiMale">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="内定（男）"
										id="listLblNaiteiMale"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listNaiteiMale"
									value="#{varlist.naiteiMale}">
									<f:convertNumber />
								</h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="colNaiteiFemale">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="内定（女）"
										id="listLblNaiteiFemale"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listNaiteiFemale"
									value="#{varlist.naiteiFemale}">
									<f:convertNumber />
								</h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="colSaisyosyaSu">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="採用者数"
										id="listLblSaiyosyaSu"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listSaiyosyaSu"
									value="#{varlist.saiyosyaSu}">
									<f:convertNumber />
								</h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="colSaiyoMale">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="採用（男）"
										id="listLblSaiyoMale"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listSaiyoMale"
									value="#{varlist.saiyoMale}">
									<f:convertNumber />
								</h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="colSaiyoFemale">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="採用（女）"
										id="listLblSaiyoFemale"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="listSaiyoFemale"
									value="#{varlist.saiyoFemale}">
									<f:convertNumber />
								</h:outputText>
								<f:attribute value="text-align: right; padding-right:5px;"
									name="style" />
								<f:attribute value="70" name="width" />
							</h:column>
						</h:dataTable></div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden id="scroll"
				value="#{pc_Ssg01603.propKanendoKyujin.scrollPosition}">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Ssg01603.propBackFormId.stringValue}"
				id="backFormId"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition('scroll', 'listScroll');
</SCRIPT>
</HTML>

