<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Cob01801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Cob01801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Cob01801.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Cob01801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Cob01801.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> 
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="70%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="200" class="v_a"><h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Cob01801.propNendo.labelName}"
										style="#{pc_Cob01801.propNendo.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:inputText styleClass="inputText"
										id="htmlNendo" value="#{pc_Cob01801.propNendo.dateValue}"
										size="4" maxlength="#{pc_Cob01801.propNendo.maxLength}"
										style="#{pc_Cob01801.propNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText></TD>
								</TR>
								<TR>
									<TH width="200" class="v_b"><h:outputText
										styleClass="outputText" id="lblGakkiNo" value="#{pc_Cob01801.propGakkiNo.labelName}"
										style="#{pc_Cob01801.propGakkiNo.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:inputText styleClass="inputText"
										id="htmlGakkiNO" size="2"
										value="#{pc_Cob01801.propGakkiNo.integerValue}"
										maxlength="#{pc_Cob01801.propGakkiNo.maxLength}"
										style="#{pc_Cob01801.propGakkiNo.style}">
										<f:convertNumber type="number" pattern="#0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="15%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="70%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="200" class="v_c"><h:outputText
										styleClass="outputText" id="lblTitle" value="#{pc_Cob01801.propTitle.labelName}"
										style="#{pc_Cob01801.propTitle.labelStyle}"></h:outputText></TH>
									<TD width="400"><h:inputText styleClass="inputText"
										id="htmlTitle" maxlength="#{pc_Cob01801.propTitle.maxLength}"
										size="60" style="#{pc_Cob01801.propTitle.style}"
										value="#{pc_Cob01801.propTitle.stringValue}">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="15%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<BR>
			<HR noshade class="hr">
			<BR>
				<TABLE border="0" width="100%" class="button_bar" height="100%">
					<TBODY>
						<TR>
							<TD align="center" valign="middle" height="100%"><hx:commandExButton
								type="submit" value="PDF作成" styleClass="commandExButton_out"
								id="pdfOut" confirm="#{msg.SY_MSG_0019W}"
								action="#{pc_Cob01801.doPdfOutAction}"></hx:commandExButton> <hx:commandExButton
								type="submit" value="CSV作成" styleClass="commandExButton_out"
								id="csvOut" confirm="#{msg.SY_MSG_0020W}" action="#{pc_Cob01801.doCsvOutAction}"></hx:commandExButton> <hx:commandExButton
								type="submit" value="出力項目指定" styleClass="commandExButton_out"
								id="setoutput" action="#{pc_Cob01801.doSetoutputAction}"></hx:commandExButton></TD>
						</TR>
					</TBODY>
				</TABLE>
			<!-- ↑ここにコンポーネントを配置 -->				
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			</DIV>			
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

