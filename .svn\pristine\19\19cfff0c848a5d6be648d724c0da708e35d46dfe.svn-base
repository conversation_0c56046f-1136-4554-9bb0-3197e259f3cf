<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_SSC_KGY" name="企業台帳" prod_id="SS" description="企業（事務所）固有の情報を求人票を発送する単位（採用を行っている単位）で持ちます。この為、１企業複数レコードの場合もあります。|『企業情報一括登録』または『企業求人情報登録』にて作成されます。系列校で共有することも可能です。">
<STATMENT><![CDATA[
SSC_KGY
]]></STATMENT>
<COLUMN id="KGY_CD" name="企業コード" type="string" length="10" lengthDP="0" byteLength="10" description="企業を一意に識別するためのコードです。|企業コードは必ずユニークであることを原則とします。"/><COLUMN id="KGY_NAME" name="企業名称" type="string" length="80" lengthDP="0" byteLength="240" description="企業（事業所）の名称です。|基本的には企業（事業所）の登記名を設定します。"/><COLUMN id="KGY_NAME_KANA" name="企業名称＿カナ" type="string" length="160" lengthDP="0" byteLength="480" description="企業（事業所）のカナ名称です。|基本的には企業（事業所）の登記名をカナで設定します。"/><COLUMN id="KGY_NAME_RYAK" name="企業名称＿略称" type="string" length="40" lengthDP="0" byteLength="120" description="企業（事業所）の略称です。|基本的には企業（事業所）の略称または呼称を設定します。"/><COLUMN id="JGY_NAIYO" name="企業事業内容" type="string" length="100" lengthDP="0" byteLength="300" description="企業（事業所）の事業内容です。"/><COLUMN id="JGY_PLACE" name="企業事業所" type="string" length="200" lengthDP="0" byteLength="600" description="企業（事業所）の事業所（支店、工場、営業所等）です。|複数事業所がある場合は、事業所を羅列して設定します。"/><COLUMN id="KGY_ADDR_NO" name="企業郵便番号" type="string" length="7" lengthDP="0" byteLength="7" description="企業（事業所）所在地の郵便番号です。|（前詰めで設定されます。また'竏驤'は含みません。）"/><COLUMN id="KGY_ADDR1" name="企業所在地＿１" type="string" length="50" lengthDP="0" byteLength="150" description="企業（事業所）の所在地です。|"/><COLUMN id="KGY_ADDR2" name="企業所在地＿２" type="string" length="50" lengthDP="0" byteLength="150" description="企業（事業所）の所在地です。|"/><COLUMN id="KGY_ADDR3" name="企業所在地＿３" type="string" length="50" lengthDP="0" byteLength="150" description="企業（事業所）の所在地です。|"/><COLUMN id="SYOZAICHI_CD" name="企業所在地コード" type="string" length="2" lengthDP="0" byteLength="2" description="企業の所在地コードです。|出身地テーブルに存在しないコードは設定できません。"/><COLUMN id="KGY_TEL" name="企業電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="企業（事業所）の電話番号です。"/><COLUMN id="KGY_FAX" name="企業ＦＡＸ番号" type="string" length="25" lengthDP="0" byteLength="25" description="企業（事業所）のＦＡＸ番号です。"/><COLUMN id="KGY_URL" name="企業ＵＲＬ" type="string" length="100" lengthDP="0" byteLength="100" description="企業（事業所）のホームページのアドレスです。"/><COLUMN id="KGY_E_MAIL" name="企業Ｅ＿ＭＡＩＬアドレス" type="string" length="60" lengthDP="0" byteLength="60" description="企業（事業所）のＥ竏窒lＡＩＬのアドレスです。"/><COLUMN id="DHY_YAK_NAME" name="代表者役職名" type="string" length="20" lengthDP="0" byteLength="60" description="企業（事業所）の代表となる者の役職（肩書）の名称です。"/><COLUMN id="DHY_NAME" name="代表者氏名" type="string" length="40" lengthDP="0" byteLength="120" description="企業（事業所）の代表となる者の氏名です。"/><COLUMN id="DHY_NAME_KANA" name="代表者氏名＿カナ" type="string" length="80" lengthDP="0" byteLength="240" description="企業（事業所）の代表となる者のカナ氏名です。"/><COLUMN id="SRI_KGY_NAME" name="書類提出先企業名称" type="string" length="80" lengthDP="0" byteLength="240" description="書類提出先企業（事業所）の名称です。|企業（事業所）の登記名と異なる場合があります。"/><COLUMN id="SRI_ADDR_NO" name="書類提出先郵便番号" type="string" length="7" lengthDP="0" byteLength="7" description="企業（事業所）の採用担当部署がある所在地の郵便番号です。（前詰めで設定されます。また'竏驤'は含みません。）"/><COLUMN id="SRI_ADDR1" name="書類提出先住所＿１" type="string" length="50" lengthDP="0" byteLength="150" description="企業（事業所）の採用担当部署が存在する住所です。|標準的には、書類提出先住所１に都道府県市区町村を設定し、書類提出先住所２に番地、建物の名称等設定します。"/><COLUMN id="SRI_ADDR2" name="書類提出先住所＿２" type="string" length="50" lengthDP="0" byteLength="150" description="企業（事業所）の採用担当部署が存在する住所です。|標準的には、書類提出先住所１に都道府県市区町村を設定し、書類提出先住所２に番地、建物の名称等設定します。"/><COLUMN id="SRI_ADDR3" name="書類提出先住所＿３" type="string" length="50" lengthDP="0" byteLength="150" description="企業（事業所）の採用担当部署が存在する住所です。|標準的には、書類提出先住所１に都道府県市区町村を設定し、書類提出先住所２に番地、建物の名称等設定します。"/><COLUMN id="SRI_TEL" name="書類提出先電話番号" type="string" length="25" lengthDP="0" byteLength="25" description="企業（事業所）の採用担当部署の電話番号です。"/><COLUMN id="SRI_FAX" name="書類提出先ＦＡＸ番号" type="string" length="25" lengthDP="0" byteLength="25" description="企業（事業所）の採用担当部署のＦＡＸ番号です。"/><COLUMN id="SIYO_BUSYO_NAME" name="採用担当者所属部署名称" type="string" length="40" lengthDP="0" byteLength="120" description="採用担当者の所属部署の名称です。"/><COLUMN id="SIYO_YAK" name="採用担当者役職名" type="string" length="20" lengthDP="0" byteLength="60" description="企業（事業所）の採用担当となる者の役職名称です。||基本的には宛名ラベルの宛先となるので、全角文字で設定します。"/><COLUMN id="SIYO_NAME" name="採用担当者氏名" type="string" length="40" lengthDP="0" byteLength="120" description="企業（事業所）の採用担当となる者の氏名です。|基本的には宛名ラベルの宛先となるので、全角文字で設定します。"/><COLUMN id="CHIKI_CD" name="地域コード" type="string" length="6" lengthDP="0" byteLength="6" description="地域を表わすコードです。"/><COLUMN id="SIHONKIN" name="資本金＿百万円" type="number" length="11" lengthDP="2" byteLength="0" description="企業（事業所）の払い込み資本金額です。|データは百万円単位で設定を行います。"/><COLUMN id="NENSYO" name="年商＿百万円" type="number" length="11" lengthDP="2" byteLength="0" description="企業（事業所）の年間の売上金額です。|データは百万円単位で設定を行います。"/><COLUMN id="KABU_JOUJOU_KBN" name="株式上場区分" type="string" length="2" lengthDP="0" byteLength="2" description="株式の上場方法を一意に識別するための区分です。"/><COLUMN id="RODO_UMU_KBN" name="労働組合有無区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）に労働組合があるか否かを識別する区分です。"><CODE><CASE value="0" display="労働組合はない"/><CASE value="1" display="労働組合がある"/><CASE value="9" display="不明"/></CODE></COLUMN><COLUMN id="KIGYO_KEIRETU_KBN" name="企業系列区分" type="string" length="5" lengthDP="0" byteLength="5" description="企業系列を一意に識別するための区分です。"/><COLUMN id="KGY_SET_KBN" name="企業設立区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業の設立形態を表す区分です。"><CODE><CASE value="1" display="株式会社"/><CASE value="2" display="有限会社"/><CASE value="3" display="社団・財団法人"/><CASE value="4" display="学校法人"/><CASE value="5" display="医療法人"/><CASE value="6" display="社会福祉法人"/><CASE value="7" display="共同組合"/><CASE value="8" display="公的法人"/><CASE value="9" display="その他"/></CODE></COLUMN><COLUMN id="KGY_SET_DATE" name="企業設立日付" type="string" length="8" lengthDP="0" byteLength="8" description="企業（事業所）が設立された日付（西暦）です。|年月までの入力も可能です。"/><COLUMN id="JGYIN" name="従業員数" type="number" length="6" lengthDP="0" byteLength="0" description="企業（事業所）の総従業員数です。"/><COLUMN id="JGYIN_W" name="従業員数＿うち女性" type="number" length="6" lengthDP="0" byteLength="0" description="企業（事業所）の総従業員数のうち、女性だけの従業員数です。|従業員数（うち女性）は、従業員数より大きい値は設定できません。"/><COLUMN id="JGYIN_DAI" name="従業員数＿うち大卒" type="number" length="6" lengthDP="0" byteLength="0" description="企業（事業所）の総従業員数のうち、大学卒の従業員数です。|従業員数より大きい値は設定できません。"/><COLUMN id="JGYIN_DAI_W" name="従業員数＿大卒女子" type="number" length="6" lengthDP="0" byteLength="0" description="企業（事業所）の総従業員数のうち、大学卒女性の従業員数です。|従業員数及び従業員数（うち大卒）より大きい値は設定できません。"/><COLUMN id="JGYIN_HON" name="従業員数＿うち本校" type="number" length="6" lengthDP="0" byteLength="0" description="企業（事業所）の総従業員数のうち、本校卒の従業員数です。|従業員数より大きい値は設定できません。"/><COLUMN id="JGYIN_HON_W" name="従業員数＿本校女子" type="number" length="6" lengthDP="0" byteLength="0" description="企業（事業所）の総従業員数のうち、本校卒女性の従業員数です。|従業員数より大きい値にはなりません。|従業員数（うち本校）より大きい値は設定できません。"/><COLUMN id="SYOKYU_KAI" name="昇給回数" type="number" length="2" lengthDP="0" byteLength="0" description="年間の企業（事業所）の昇給回数です。"/><COLUMN id="SYOKYU_RIT" name="昇給率＿パーセント" type="number" length="5" lengthDP="2" byteLength="0" description="年間の企業（事業所）の給与昇給率です。"/><COLUMN id="SYOYO_KAI" name="賞与支給回数" type="number" length="2" lengthDP="0" byteLength="0" description="企業（事業所）が年間に従業員へ支給する賞与の回数です。"/><COLUMN id="SYOYO_TUKI" name="賞与支給月数" type="number" length="5" lengthDP="2" byteLength="0" description="企業（事業所）が年間に従業員へ支給する賞与の給与相当分月数です。|基本的には、入社２年目以降の支給月数が設定されます。"/><COLUMN id="TUKIN_KBN" name="通勤費支給区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）が支給する通勤費が、全額支給であるか限度額があるかを識別する区分です。"><CODE><CASE value="0" display="全額支給"/><CASE value="1" display="支給限度額あり"/><CASE value="9" display="不明"/></CODE></COLUMN><COLUMN id="TUKIN_MAX" name="通勤費支給限度額" type="number" length="7" lengthDP="0" byteLength="0" description="企業（事業所）が従業員に支給する通勤費の支給限度額です。|通勤費支給区分が'２'（通勤費支給限度額あり）の場合にしかデータの設定は行えません。"/><COLUMN id="RYO_UMU_KBN" name="寮有無区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）に社員が入寮できる寮があるか否かを識別する区分です。"><CODE><CASE value="0" display="寮はない"/><CASE value="1" display="寮がある"/><CASE value="9" display="不明"/></CODE></COLUMN><COLUMN id="NENSY_2NEN" name="入社２年目年収＿千円" type="number" length="5" lengthDP="0" byteLength="0" description="入社２年目における企業の年収です。|千円単位で設定します。"/><COLUMN id="MODEL_NENSY" name="モデル年収＿千円" type="number" length="5" lengthDP="0" byteLength="0" description="３０歳時における企業のモデル年収です。|千円単位で設定します。"/><COLUMN id="SYAHO_KENPO_KBN" name="社会保険＿健康保険区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）に健康保険があるか否かを識別する区分です。"/><COLUMN id="SYAHO_NENKIN_KBN" name="社会保険＿厚生年金区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）に厚生年金があるか否かを識別する区分です。"/><COLUMN id="SYAHO_KOYO_KBN" name="社会保険＿雇用保険区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）に雇用保険があるか否かを識別する区分です。"/><COLUMN id="SYAHO_ROSAI_KBN" name="社会保険＿労災区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）に労災があるか否かを識別する区分です。"/><COLUMN id="SYAHO_ETC_KBN" name="社会保険＿その他区分" type="string" length="1" lengthDP="0" byteLength="1" description="企業（事業所）にその他の社会保険があるか否かを識別する区分です。"/><COLUMN id="SYAHO_ETC_MEMO" name="社会保険＿その他メモ" type="string" length="40" lengthDP="0" byteLength="120" description="企業（事業所）のその他社会保険についてのメモです。"/><COLUMN id="HAIGYO_KBN" name="廃業区分" type="string" length="1" lengthDP="0" byteLength="1" description="廃業しているか否かを識別する区分です。"><CODE><CASE value="0" display="廃業していない"/><CASE value="1" display="廃業している"/></CODE></COLUMN><COLUMN id="HAIGYO_NAIYO" name="廃業内容" type="string" length="200" lengthDP="0" byteLength="600" description="廃業した理由等を設定します。"/><COLUMN id="HAIGYO_DATE" name="廃業日付" type="date" length="0" lengthDP="0" byteLength="0" description="廃業した日付です。"/><COLUMN id="GPEI_KGY_CD" name="合併先企業コード" type="string" length="10" lengthDP="0" byteLength="10" description="合併先企業をあらわすコードです。"/><COLUMN id="KGY_MEMO" name="企業メモ" type="string" length="1024" lengthDP="0" byteLength="3072" description="企業に関するメモを設定します。"/><COLUMN id="SYAFU" name="企業社風" type="string" length="200" lengthDP="0" byteLength="600" description="企業の社風を設定します。"/><COLUMN id="HON_KBN" name="本社区分" type="string" length="1" lengthDP="0" byteLength="1" description="本社の場合には識別区分を本社にします。"><CODE><CASE value="0" display="本社以外"/><CASE value="1" display="本社"/></CODE></COLUMN><COLUMN id="HON_KGY_CD" name="本社企業コード" type="string" length="10" lengthDP="0" byteLength="10" description="支社の場合、ここに本社の企業コードが入ります。"/><COLUMN id="NEW_KJN_NENDO" name="最新求人年度" type="number" length="4" lengthDP="0" byteLength="0" description="企業（事業所）からの求人が行われた、最新の西暦年度です。"/><COLUMN id="ONR_SOT_KBN" name="オーナー卒業生区分" type="string" length="1" lengthDP="0" byteLength="1" description="卒業生が企業のオーナーか否かを識別する区分です。"><CODE><CASE value="0" display="卒業生以外"/><CASE value="1" display="卒業生"/></CODE></COLUMN><COLUMN id="ONR_KANRI_NO" name="オーナー管理番号" type="number" length="15" lengthDP="0" byteLength="0" description="卒業生が企業のオーナーの場合、管理番号が入ります。"/><COLUMN id="KGY_APPEND_DATE" name="企業登録日" type="date" length="0" lengthDP="0" byteLength="0" description="企業情報が登録された日付です。"/><COLUMN id="KGY_UPDATE_DATE" name="企業更新日" type="date" length="0" lengthDP="0" byteLength="0" description="企業情報が更新された日付です。"/>
</TABLE>
