<%-- 
	摘要、金額修正
	
	<AUTHOR>
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ke/Kec05102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kec00901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kec05102.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kec05102.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kec05102.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kec05102.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻るボタンを配置 -->
<!-- 2009-10-22 戻るボタンを追加する -->
<hx:commandExButton type="submit" value="戻る" styleClass="commandExButton" 
		id="returnDisp" action="#{pc_Kec05102.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻るボタンを配置 -->
</DIV>



<DIV id="content">			
<DIV class="column" align="center">

<!-- ↓ここにコンポーネントを配置 -->


			
			
			<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="150" align="right">
						
						<h:outputText styleClass="outputText" id="lblKaikeiNendo"
							style="#{pc_Kec05102.propKaikeiNendo.labelStyle}"
							value="#{pc_Kec05102.propKaikeiNendo.labelName}"></h:outputText></TD>
						<TD width="750" align="left"><h:outputText styleClass="outputText"
							id="htmlKaikeiNendo" value="#{pc_Kec05102.propKaikeiNendo.stringValue}" style="#{pc_Kec05102.propKaikeiNendo.style}"></h:outputText></TD>
					</TR>
					<TR>
						<TD align="right" width="150"><h:outputText styleClass="outputText"
							id="lblKmkCd" style="#{pc_Kec05102.propKmkCd.labelStyle}"
							value="#{pc_Kec05102.propKmkCd.labelName}"></h:outputText></TD>
						<TD align="left" width="750"><h:outputText styleClass="outputText"
							id="htmlKmkCd" value="#{pc_Kec05102.propKmkCd.stringValue}" style="#{pc_Kec05102.propKmkCd.style}"></h:outputText>　<h:outputText
							styleClass="outputText" id="htmlKmkName"
							value="#{pc_Kec05102.propKmkName.stringValue}"
							style="#{pc_Kec05102.propKmkName.style}"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="150" valign="top" align="right"><h:outputText styleClass="outputText"
							id="lblTekiyo" style="#{pc_Kec05102.propTekiyo.labelStyle}"
							value="#{pc_Kec05102.propTekiyo.labelName}"></h:outputText></TD>
						<TD width="750"><h:inputTextarea styleClass="inputTextarea"
							id="htmlTekiyo" value="#{pc_Kec05102.propTekiyo.stringValue}"
							style="#{pc_Kec05102.propTekiyo.style}" cols="100" rows="16">
						</h:inputTextarea></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			
			<BR>
			<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="450" align="right"><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Kec05102.doRegisterAction}"></hx:commandExButton></TD>
						<TD width="30"></TD>
						<TD width="420" align="left"><hx:commandExButton type="submit"
							value="クリア" styleClass="commandExButton_dat" id="clear"
							action="#{pc_Kec05102.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			

			
			
			

			<!-- ↑ここにコンポーネントを配置 -->
<h:inputHidden
				value="#{pc_Kec05102.propLineNumber.integerValue}"
				id="htmlLineNumber">
			<f:convertNumber /></h:inputHidden></DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			
			

		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

