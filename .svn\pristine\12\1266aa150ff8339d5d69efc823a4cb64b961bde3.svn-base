<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssc00503T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssc00503.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"> 
<LINK rel="stylesheet" type="text/css" href="inc/gakuenSS.css">

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onclick="return func_1(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssc00503T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssc00503T02.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssc00503T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssc00503T02.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton type="submit" value="戻る" disabled="#{pc_Ssc00503T01.ssc00503.propReturnBtn.disabled}"
				styleClass="commandExButton" id="returnDisp" action="#{pc_Ssc00503T02.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
	<!-- ↓ここにコンポーネントを配置 -->
	<TABLE width="926" border="0" cellpadding="3" cellspacing="0">
			<TR>
				<TD width="926">
					<TABLE width="926" border="0" cellpadding="3" cellspacing="0" class="table">
						<TBODY>
							<TR>
								<TH class="v_b" width="17%"><h:outputText styleClass="outputText"
									id="lblKgyCdSub" value="#{pc_Ssc00503T01.ssc00503.propKgyCdSub.labelName}"
									style="#{pc_Ssc00503T01.ssc00503.propKgyCdSub.labelStyle}"></h:outputText></TH>
								<TD width="33%"><h:outputText styleClass="outputText" id="htmlKgyCdSub"
									value="#{pc_Ssc00503T01.ssc00503.propKgyCdSub.stringValue}"
									style="#{pc_Ssc00503T01.ssc00503.propKgyCdSub.style}"></h:outputText></TD>
								<TH class="v_c" width="17%"><h:outputText styleClass="outputText"
									id="lblKgyNameSub"
									value="#{pc_Ssc00503T01.ssc00503.propKgyNameSub.labelName}"
									style="#{pc_Ssc00503T01.ssc00503.propKgyNameSub.labelStyle}"></h:outputText></TH>
								<TD width="33%"><h:outputText styleClass="outputText" id="htmlKgyNameSub"
									value="#{pc_Ssc00503T01.ssc00503.propKgyNameSub.displayValue}"
									style="#{pc_Ssc00503T01.ssc00503.propKgyNameSub.style}" title="#{pc_Ssc00503T01.ssc00503.propKgyNameSub.stringValue}"></h:outputText></TD>
							</TR>
							<TR>
								<TH class="v_e"><h:outputText styleClass="outputText"
									id="lblGyosyuNameSub"
									value="#{pc_Ssc00503T01.ssc00503.propGyosyuNameSub.labelName}"
									style="#{pc_Ssc00503T01.ssc00503.propGyosyuNameSub.labelStyle}"></h:outputText></TH>
								<TD><h:outputText styleClass="outputText" id="htmlGyosyuNameSub"
									value="#{pc_Ssc00503T01.ssc00503.propGyosyuNameSub.displayValue}"
									style="#{pc_Ssc00503T01.ssc00503.propGyosyuNameSub.style}"
									title="#{pc_Ssc00503T01.ssc00503.propGyosyuNameSub.stringValue}"></h:outputText></TD>
								<TH class="v_f"><h:outputText styleClass="outputText"
									id="lblChikiNameSub"
									value="#{pc_Ssc00503T01.ssc00503.propKariKigyoSub.labelName}"
									style="#{pc_Ssc00503T01.ssc00503.propKariKigyoSub.labelStyle}"></h:outputText></TH>
								<TD><h:outputText styleClass="outputText" id="htmlChikiNameSub"
									value="#{pc_Ssc00503T01.ssc00503.propKariKigyoSub.displayValue}"
									style="#{pc_Ssc00503T01.ssc00503.propKariKigyoSub.style}"
									title="#{pc_Ssc00503T01.ssc00503.propKariKigyoSub.stringValue}"></h:outputText></TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
				<TD width="100" valign="bottom" align="right">
								<hx:commandExButton
									type="submit" value="合併元企業" styleClass="commandExButton"
									id="goGpeiKgy" action="#{pc_Ssc00503T01.doGoGpeiKgyAction}"
									rendered="#{pc_Ssc00503T01.ssc00503.propGpeiKgy.rendered}">
								</hx:commandExButton>
				</TD>
			</TR>
		</TABLE>
	<TABLE width="926" border="0" cellpadding="0" cellspacing="0"
		style="margin-top:20px;">
		<TBODY>
			<TR>
				<TD style="text-align:left;">
				<hx:commandExButton type="submit" 
				value="基本" styleClass="tab_head_off" id="btnSsc00503T01" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T01Action}" style="width:7.2%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="所在地" styleClass="tab_head_on" id="btnSsc00503T02" style="width:7.2%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="代表者" styleClass="tab_head_off" id="btnSsc00503T03" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T03Action}" style="width:7.2%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="採用担当" styleClass="tab_head_off" id="btnSsc00503T04" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T04Action}" style="width:7.8%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="説明会" styleClass="tab_head_off" id="btnSsc00503T05" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T05Action}" style="width:7.2%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="訪問記録" styleClass="tab_head_off" id="btnSsc00503T06" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T06Action}" style="width:7.8%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="売上構成" styleClass="tab_head_off" id="btnSsc00503T07" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T07Action}" style="width:7.8%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="求人票礼状" styleClass="tab_head_off" id="btnSsc00503T08" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T08Action}" style="width:9%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="廃業" styleClass="tab_head_off" id="btnSsc00503T09" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T09Action}" style="width:7.2%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="求人" styleClass="tab_head_off" id="btnSsc00503T10" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T10Action}" style="width:7.2%"></hx:commandExButton><hx:commandExButton
				value="ｲﾝﾀｰﾝｼｯﾌﾟ" styleClass="tab_head_off" id="btnSsc00503T13" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T13Action}" style="width:9.3%"></hx:commandExButton><hx:commandExButton
							type="submit" value="メモ" styleClass="tab_head_off"
							id="btnSsc00503T11"
							action="#{pc_Ssc00503T02.doBtnSsc00503T11Action}" style="width:7.2%"></hx:commandExButton><hx:commandExButton type="submit" 
				value="自由設定" styleClass="tab_head_off" id="btnSsc00503T12" 
				action="#{pc_Ssc00503T02.doBtnSsc00503T12Action}" style="width:7.8%"></hx:commandExButton>
				</TD>
			</TR>
			<TR>
				<TD>
					<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="tab_body" style="margin-bottom:10px;">
						<TBODY>
							<TR>
								<TD valign="top" style="height:400px;text-align:center;">
									<TABLE border="0" cellpadding="0" cellspacing="0" style="">
										<TBODY>
											<TR>
												<TD align="left">
												<TABLE width="800" border="0" cellpadding="3"
													cellspacing="0" class="table" style="margin-top:20px;">
													<TBODY>
														<TR>
															<TH class="v_a" width="150"><h:outputText
																styleClass="outputText" id="lblKgyAddrNo"
																value="#{pc_Ssc00503T02.propKgyAddrNo.labelName}"
																style="#{pc_Ssc00503T02.propKgyAddrNo.labelStyle}"></h:outputText></TH>
															<TD colspan="3"><h:outputText styleClass="outputText"
																id="htmlKgyAddrNo"
																style="#{pc_Ssc00503T02.propKgyAddrNo.style}"
																value="#{pc_Ssc00503T02.propKgyAddrNo.stringValue}">
																<hx:convertMask mask="###-####" />
															</h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_b" width="150" rowspan="3"><h:outputText
																styleClass="outputText" id="lblKgyAddr1"
																value="#{pc_Ssc00503T02.propKgyAddr1.labelName}"
																style="#{pc_Ssc00503T02.propKgyAddr1.labelStyle}"></h:outputText></TH>
															<TD colspan="3"><h:outputText styleClass="outputText"
																id="htmlKgyAddr1"
																style="#{pc_Ssc00503T02.propKgyAddr1.style}"
																value="#{pc_Ssc00503T02.propKgyAddr1.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="3"><h:outputText styleClass="outputText"
																id="htmlKgyAddr2"
																style="#{pc_Ssc00503T02.propKgyAddr2.style}"
																value="#{pc_Ssc00503T02.propKgyAddr2.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TD colspan="3"><h:outputText styleClass="outputText"
																id="htmlKgyAddr3"
																style="#{pc_Ssc00503T02.propKgyAddr3.style}"
																value="#{pc_Ssc00503T02.propKgyAddr3.stringValue}"></h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_e" width="150"><h:outputText
																styleClass="outputText" id="lblKgyTel"
																value="#{pc_Ssc00503T02.propKgyTel.labelName}"
																style="#{pc_Ssc00503T02.propKgyTel.labelStyle}"></h:outputText></TH>
															<TD width="250"><h:outputText styleClass="outputText"
																id="htmlKgyTel" style="#{pc_Ssc00503T02.propKgyTel.style}"
																value="#{pc_Ssc00503T02.propKgyTel.stringValue}"></h:outputText></TD>
															<TH class="v_f" width="150"><h:outputText
																styleClass="outputText" id="lblKgyFax"
																value="#{pc_Ssc00503T02.propKgyFax.labelName}"
																style="#{pc_Ssc00503T02.propKgyFax.labelStyle}"></h:outputText></TH>
															<TD width="250"><h:outputText styleClass="outputText"
																id="htmlKgyFax" style="#{pc_Ssc00503T02.propKgyFax.style}"
																value="#{pc_Ssc00503T02.propKgyFax.stringValue}"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TD align="left">
													<TABLE width="800" border="0" cellpadding="3" cellspacing="0"
														class="table" style="margin-top: 20px;">
														<TBODY>
															<TR>
																<TH class="v_g" width="150"><h:outputText
																styleClass="outputText" id="lblSyozaichiName"
																value="#{pc_Ssc00503T02.propSyozaichiName.labelName}"
																style="#{pc_Ssc00503T02.propSyozaichiName.labelStyle}"></h:outputText></TH>
															<TD width="250"><h:outputText styleClass="outputText"
																id="htmlSyozaichiName"
																value="#{pc_Ssc00503T02.propSyozaichiName.stringValue}"
																style="#{pc_Ssc00503T02.propSyozaichiName.style}"></h:outputText></TD>
															<TH width="150" class="v_a"><h:outputText
																styleClass="outputText" id="lblChikiName"
																value="#{pc_Ssc00503T02.propChikiName.labelName}"
																style="#{pc_Ssc00503T02.propChikiName.labelStyle}"></h:outputText></TH>
															<TD width="250"><h:outputText
																styleClass="outputText" id="htmlChikiName"
																value="#{pc_Ssc00503T02.propChikiName.stringValue}"
																style="#{pc_Ssc00503T02.propChikiName.style}"></h:outputText></TD>
														</TR>
														</TBODY>
													</TABLE>
												</TD>
											</TR>
											<TR>
												<TD align="left">
													</TD>
											</TR>
											<TR>
												<TD align="left">
												<TABLE width="800" border="0" cellpadding="3"
													cellspacing="0" class="table" style="margin-top:20px;">
													<TBODY>
														<TR>
															<TH class="v_a" width="150"><h:outputText
																styleClass="outputText" id="lblRodoUmuKbnText"
																value="#{pc_Ssc00503T02.propRodoUmuKbnText.labelName}"
																style="#{pc_Ssc00503T02.propRodoUmuKbnText.labelStyle}"></h:outputText></TH>
															<TD width="250"><h:outputText styleClass="outputText"
																id="htmlRodoUmuKbnText"
																value="#{pc_Ssc00503T02.propRodoUmuKbnText.stringValue}"
																style="#{pc_Ssc00503T02.propRodoUmuKbnText.style}"></h:outputText></TD>
															<TH class="v_b" width="150"><h:outputText
																styleClass="outputText" id="lblRyoUmuKbnText"
																value="#{pc_Ssc00503T02.propRyoUmuKbnText.labelName}"
																style="#{pc_Ssc00503T02.propRyoUmuKbnText.labelStyle}"></h:outputText></TH>
															<TD width="250"><h:outputText styleClass="outputText"
																id="htmlRyoUmuKbnText"
																value="#{pc_Ssc00503T02.propRyoUmuKbnText.stringValue}"
																style="#{pc_Ssc00503T02.propRyoUmuKbnText.style}"></h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<!-- ↑ここにコンポーネントを配置 --></DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
</h:form>
</hx:scriptCollector>
	<jsp:include page="../inc/footer.jsp" />
	</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

