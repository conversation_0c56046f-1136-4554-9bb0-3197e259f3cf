<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../table.xsl"?>
<TABLE id="_KMJ_SOT_KKBR" name="卒業生＿資格用科目分類" prod_id="KM" description="卒業生の卒業年度、学期、カリキュラム学科組織毎の資格用科目分類の情報を持ちます。">
<STATMENT><![CDATA[
KMJ_SOT_KKBR
]]></STATMENT>
<COLUMN id="NYUGAK_NENDO" name="入学年度" type="number" length="4" lengthDP="0" byteLength="0" description="卒業生が入学した年度が設定されます。"/><COLUMN id="GAKKI_NO" name="学期ＮＯ" type="number" length="2" lengthDP="0" byteLength="0" description="卒業生が入学した学期ＮＯが設定されます。"/><COLUMN id="CUR_GAKKA_CD" name="カリキュラム学科組織コード" type="string" length="6" lengthDP="0" byteLength="6" description="卒業生の在学時のカリキュラム学科組織コードが設定されます。"/><COLUMN id="SIKAK_CD" name="資格コード" type="string" length="6" lengthDP="0" byteLength="6" description="資格を任意に識別する為のコードが設定されます。"/><COLUMN id="SIKAK_KBR_CD" name="資格用科目分類コード" type="string" length="6" lengthDP="0" byteLength="6" description="資格を取得する為の科目を分類する為の任意のコードが設定されます。"/><COLUMN id="GAKKI_NAME" name="学期名称" type="string" length="10" lengthDP="0" byteLength="30" description="入学学期名称が設定されます。"/><COLUMN id="SIKAK_KBR_NAME" name="資格用科目分類名称" type="string" length="60" lengthDP="0" byteLength="180" description="資格用の科目分類の名称が設定されます。"/><COLUMN id="SIKAK_KBR_NAME_RYAK" name="資格用科目分類略称" type="string" length="8" lengthDP="0" byteLength="24" description="資格用科目分類の略称が設定されます。"/><COLUMN id="SIKAK_LVL" name="レベル" type="number" length="1" lengthDP="0" byteLength="0" description="資格用科目分類のレベル（階層）が設定されます。１～４が設定されます。"/><COLUMN id="HIGH_KAMOK_BUNRUI_CD" name="上位科目分類コード" type="string" length="6" lengthDP="0" byteLength="6" description="資格用科目分類の上位の科目分類コードが設定されます。"/><COLUMN id="ROW_NO" name="並び順ＮＯ" type="number" length="3" lengthDP="0" byteLength="0" description="資格用科目分類の並び順が設定されます。1からの連番となります。"/><COLUMN id="HYOJI_OFF_SET" name="表示オフセット" type="number" length="1" lengthDP="0" byteLength="0" description="帳票へ資格用科目分類名称を表示する場合、名称の前に付加するスペースの文字数を表します。スペースは半角です。"/><COLUMN id="TANI_HYOJI_FLG" name="単位表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="帳票へこの資格用科目分類の修得単位集計を表示するかどうかを表します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="JYOI_KASAN_FLG" name="上位加算フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="帳票で上位科目分類への単位加算をするかどうかを表します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="PRT_HYOJI_FLG" name="帳票表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="帳票でこの資格用科目分類名称を表示するかどうかを表します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SHOMEI_HIHYOJI_FLG" name="証明書非表示フラグ" type="boolean" length="1" lengthDP="0" byteLength="0" description="証明書でこの資格科目分類名称を表示するかどうかを表します。"><CODE><CASE value="0" display="偽"/><CASE value="1" display="真"/></CODE></COLUMN><COLUMN id="SIKAK_YOKEN_TANI" name="資格取得要件単位" type="number" length="4" lengthDP="1" byteLength="0" description="資格用科目分類の資格取得要件単位が設定されます。"/>
</TABLE>
