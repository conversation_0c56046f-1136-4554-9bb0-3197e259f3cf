<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssc00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<META name="GENERATOR" content="IBM Software Development Platform" />
<META http-equiv="Content-Style-Type" content="text/css" />
<TITLE>Ssc00601.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css" />
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style" />
<LINK rel="stylesheet" type="text/css" href="../../theme/Master.css"/>
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/rev/ss/inc/gakuenSS.css" />
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg" />
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssc00601.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<DIV style="display:none;">
<hx:commandExButton type="submit" value="閉じる" 
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssc00601.doCloseDispAction}">
</hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssc00601.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssc00601.screenName}"></h:outputText>
</DIV>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<DIV class="head_button_area">　
</DIV>

<!--↓content↓-->
<DIV id="content">
	<DIV class="column">
		<TABLE>
			<TBODY>
				<TR>
					<TD valign="top">
						<DIV style="height:500px;text-align:center;">
							<TABLE width="900">
								<TBODY>
									<TR>
										<TD style="text-align:left;">
											<TABLE border="0" class="table" cellspacing="0" cellpadding="0">
												<TBODY>
													<TR>
														<TH nowrap class="v_a" width="130">
															<h:outputText
																styleClass="outputText" id="lblKgyCd"
																value="#{pc_Ssc00601.propKgyCdFrom.labelName}"
																style="#{pc_Ssc00601.propKgyCdFrom.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="750" colspan="3">
															<h:inputText id="htmlKgyCdFrom"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propKgyCdFrom.readonly}"
																style="#{pc_Ssc00601.propKgyCdFrom.style}"
																value="#{pc_Ssc00601.propKgyCdFrom.stringValue}"
																disabled="#{pc_Ssc00601.propKgyCdFrom.disabled}"
																maxlength="#{pc_Ssc00601.propKgyCdFrom.maxLength}">
															</h:inputText> ～ 
															<h:inputText id="htmlKgyCdTo"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propKgyCdTo.readonly}"
																style="#{pc_Ssc00601.propKgyCdTo.style}"
																value="#{pc_Ssc00601.propKgyCdTo.stringValue}"
																disabled="#{pc_Ssc00601.propKgyCdTo.disabled}"
																maxlength="#{pc_Ssc00601.propKgyCdTo.maxLength}">
															</h:inputText>
														</TD>
													</TR>
													<TR>
														<TH nowrap class="v_b" width="130">
																<h:outputText
																styleClass="outputText" id="lblKgyName"
																value="#{pc_Ssc00601.propKgyName.labelName}"
																style="#{pc_Ssc00601.propKgyName.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="320">
															<h:inputText id="htmlKgyName"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propKgyName.readonly}"
																style="#{pc_Ssc00601.propKgyName.style}"
																value="#{pc_Ssc00601.propKgyName.stringValue}"
																disabled="#{pc_Ssc00601.propKgyName.disabled}"
																maxlength="#{pc_Ssc00601.propKgyName.maxLength}" size="42">
															</h:inputText>
														</TD>
														<TH nowrap class="v_d" width="130">
															<h:outputText
																styleClass="outputText" id="lblKgyNameKana"
																value="#{pc_Ssc00601.propKgyNameKana.labelName}"
																style="#{pc_Ssc00601.propKgyNameKana.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="320">
															<h:inputText id="htmlKgyNameKana"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propKgyNameKana.readonly}"
																style="#{pc_Ssc00601.propKgyNameKana.style}"
																value="#{pc_Ssc00601.propKgyNameKana.stringValue}"
																disabled="#{pc_Ssc00601.propKgyNameKana.disabled}"
																maxlength="#{pc_Ssc00601.propKgyNameKana.maxLength}" size="42">
															</h:inputText>
														</TD>
													</TR>
													<TR>
														<TH nowrap class="v_c">
															<h:outputText
																styleClass="outputText" id="lblKgyNameRyak"
																value="#{pc_Ssc00601.propKgyNameRyak.labelName}"
																style="#{pc_Ssc00601.propKgyNameRyak.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3">
															<h:inputText id="htmlKgyNameRyak"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propKgyNameRyak.readonly}"
																style="#{pc_Ssc00601.propKgyNameRyak.style}"
																value="#{pc_Ssc00601.propKgyNameRyak.stringValue}"
																disabled="#{pc_Ssc00601.propKgyNameRyak.disabled}"
																maxlength="#{pc_Ssc00601.propKgyNameRyak.maxLength}" size="42">
															</h:inputText>
														</TD>
													</TR>
													<TR>
														<TH class="v_a">
															<h:outputText
																styleClass="outputText" id="lblSihonkin"
																value="#{pc_Ssc00601.propSihonkinFrom.labelName}"
																style="#{pc_Ssc00601.propSihonkinFrom.labelStyle}">
															</h:outputText>
														</TH>
														<TD>
															<h:inputText id="htmlSihonkinFrom"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propSihonkinFrom.readonly}"
																style="#{pc_Ssc00601.propSihonkinFrom.style}"
																value="#{pc_Ssc00601.propSihonkinFrom.doubleValue}"
																disabled="#{pc_Ssc00601.propSihonkinFrom.disabled}"
																maxlength="#{pc_Ssc00601.propSihonkinFrom.maxLength}"
																readonly="false" size="10">
																<f:convertNumber type="number" pattern="###,###,##0.00"/>
																<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
																</h:inputText><h:outputText styleClass="outputText"
																id="lblUnitSihonkinFrom" value="百万円"></h:outputText><h:outputText
																styleClass="outputText" id="lblFromTo2" value="～">
															</h:outputText>
															<h:inputText id="htmlSihonkinTo"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propSihonkinTo.readonly}"
																style="#{pc_Ssc00601.propSihonkinTo.style}"
																value="#{pc_Ssc00601.propSihonkinTo.doubleValue}"
																disabled="#{pc_Ssc00601.propSihonkinTo.disabled}"
																maxlength="#{pc_Ssc00601.propSihonkinTo.maxLength}"
																readonly="false" size="10">
																<f:convertNumber type="number" pattern="###,###,##0.00"/>
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
																</h:inputText><h:outputText styleClass="outputText"
																id="lblUnitSihonkinTo" value="百万円">
															</h:outputText>
														</TD>
														<TH class="v_b">
															<h:outputText
																styleClass="outputText" id="lblNensyo"
																value="#{pc_Ssc00601.propNensyoFrom.labelName}"
																style="#{pc_Ssc00601.propNensyoFrom.labelStyle}">
															</h:outputText>
														</TH>
														<TD>
															<h:inputText id="htmlNensyoFrom"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propNensyoFrom.readonly}"
																style="#{pc_Ssc00601.propNensyoFrom.style}"
																value="#{pc_Ssc00601.propNensyoFrom.doubleValue}"
																disabled="#{pc_Ssc00601.propNensyoFrom.disabled}"
																readonly="false"
																maxlength="#{pc_Ssc00601.propNensyoTo.maxLength}" size="10">
																<f:convertNumber type="number" pattern="###,###,##0.00"/>
																<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																</h:inputText>
																<h:outputText
																	styleClass="outputText" id="lblUnitNensyoFrom" value="百万円">
																</h:outputText>
																<h:outputText styleClass="outputText"
																	id="lblFromTo3" value="～">
																</h:outputText><h:inputText id="htmlNensyoTo"
																	styleClass="inputText"
																	readonly="#{pc_Ssc00601.propNensyoTo.readonly}"
																	style="#{pc_Ssc00601.propNensyoTo.style}"
																	value="#{pc_Ssc00601.propNensyoTo.doubleValue}"
																	disabled="#{pc_Ssc00601.propNensyoTo.disabled}"
																	readonly="false" size="10">
																	<f:convertNumber type="number" pattern="###,###,##0.00"/>
																	<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
															</h:inputText>
															<h:outputText styleClass="outputText"
																id="lblUnitNensyoTo" value="百万円">
															</h:outputText>
														</TD>
													</TR>
													<TR>
														<TH class="v_c">
															<h:outputText
																styleClass="outputText" id="lblJgyin"
																value="#{pc_Ssc00601.propJgyinFrom.labelName}"
																style="#{pc_Ssc00601.propJgyinFrom.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3">
															<h:inputText id="htmlJgyinFrom"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propJgyinFrom.readonly}"
																style="#{pc_Ssc00601.propJgyinFrom.style}"
																value="#{pc_Ssc00601.propJgyinFrom.integerValue}"
																disabled="#{pc_Ssc00601.propJgyinFrom.disabled}"
																maxlength="#{pc_Ssc00601.propJgyinFrom.maxLength}" size="5">
																<f:convertNumber type="number" pattern="###,##0"/>
																<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_"/>
															</h:inputText>
															<h:outputText styleClass="outputText"
																id="lblUnitJgyinFrom" value="人　">
															</h:outputText>
															<h:outputText 
																styleClass="outputText" 
																id="lblFromTo4" value="～">
															</h:outputText>
															<h:inputText
																id="htmlJgyinTo" styleClass="inputText"
																readonly="#{pc_Ssc00601.propJgyinTo.readonly}"
																style="#{pc_Ssc00601.propJgyinTo.style}"
																value="#{pc_Ssc00601.propJgyinTo.integerValue}"
																disabled="#{pc_Ssc00601.propJgyinTo.disabled}"
																maxlength="#{pc_Ssc00601.propJgyinTo.maxLength}" size="5">
																<f:convertNumber type="number" pattern="###,##0"/>
																<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
															</h:inputText>
															<h:outputText styleClass="outputText"
																id="lblUnitJgyinTo" value="人">
															</h:outputText>
														</TD>
													</TR>
													<TR>
														<TH class="v_d">
															<h:outputText
																styleClass="outputText" id="lblNensy2nen"
																value="#{pc_Ssc00601.propNensy2nenFrom.labelName}"
																style="#{pc_Ssc00601.propNensy2nenFrom.labelStyle}">
															</h:outputText>
														</TH>
														<TD>
															<h:inputText 
																id="htmlNensy2nenFrom"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propNensy2nenFrom.readonly}"
																style="#{pc_Ssc00601.propNensy2nenFrom.style}"
																value="#{pc_Ssc00601.propNensy2nenFrom.integerValue}"
																disabled="#{pc_Ssc00601.propNensy2nenFrom.disabled}"
																maxlength="#{pc_Ssc00601.propNensy2nenFrom.maxLength}" size="5">
																<f:convertNumber type="number" pattern="##,##0"/>
																<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
															</h:inputText>
															<h:outputText 
																styleClass="outputText"
																id="lblUnitNensy2NenFrom" value="千円">
															</h:outputText>
															<h:outputText styleClass="outputText"
																id="lblFromTo5" value="～">
															</h:outputText>
															<h:inputText
																id="htmlNensy2nenTo" styleClass="inputText"
																readonly="#{pc_Ssc00601.propNensy2nenTo.readonly}"
																style="#{pc_Ssc00601.propNensy2nenTo.style}"
																value="#{pc_Ssc00601.propNensy2nenTo.integerValue}"
																disabled="#{pc_Ssc00601.propNensy2nenTo.disabled}"
																maxlength="#{pc_Ssc00601.propNensy2nenTo.maxLength}" size="5">
																<f:convertNumber type="number" pattern="##,##0"/>
																<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
															</h:inputText>
															<h:outputText styleClass="outputText"
																id="lblUnitNensy2NenTo" value="千円">
															</h:outputText>
														</TD>
														<TH class="v_e">
															<h:outputText
																styleClass="outputText" id="lblModelNensyFrom"
																value="#{pc_Ssc00601.propModelNensyFrom.labelName}"
																style="#{pc_Ssc00601.propModelNensyFrom.labelStyle}">
															</h:outputText></TH>
														<TD>
															<h:inputText 
																id="htmlModelNensyFrom"
																styleClass="inputText"
																readonly="#{pc_Ssc00601.propModelNensyFrom.readonly}"
																style="#{pc_Ssc00601.propModelNensyFrom.style}"
																value="#{pc_Ssc00601.propModelNensyFrom.integerValue}"
																disabled="#{pc_Ssc00601.propModelNensyFrom.disabled}"
																maxlength="#{pc_Ssc00601.propModelNensyFrom.maxLength}" size="5">
																<f:convertNumber type="number" pattern="##,##0"/>
																<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
															</h:inputText>
															<h:outputText 
																styleClass="outputText"
																id="lblUnitModelNensyFrom" value="千円">
															</h:outputText><h:outputText styleClass="outputText"
																id="lblFromTo6" value="～">
															</h:outputText>
															<h:inputText
																id="htmlModelNensyTo" styleClass="inputText"
																readonly="#{pc_Ssc00601.propModelNensyTo.readonly}"
																style="#{pc_Ssc00601.propModelNensyTo.style}"
																value="#{pc_Ssc00601.propModelNensyTo.integerValue}"
																disabled="#{pc_Ssc00601.propModelNensyTo.disabled}"
																maxlength="#{pc_Ssc00601.propModelNensyTo.maxLength}" size="5">
																<f:convertNumber type="number" pattern="##,##0"/>
																<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
															</h:inputText>
															<h:outputText styleClass="outputText"
																id="lblUnitModelNensyTo" value="千円">
															</h:outputText>
														</TD>
													</TR>
													<TR>
														<TH class="v_a" rowspan="3">
															<h:outputText
																styleClass="outputText" id="lblJgyNaiyo1"
																value="#{pc_Ssc00601.propJgyNaiyo1.labelName}"
																style="#{pc_Ssc00601.propJgyNaiyo1.labelStyle}">
															</h:outputText>
														</TH>
														<TD>
															<h:inputTextarea id="htmlJgyNaiyo1" rows="2"
																styleClass="inputTextarea"
																readonly="#{pc_Ssc00601.propJgyNaiyo1.readonly}"
																style="#{pc_Ssc00601.propJgyNaiyo1.style}"
																value="#{pc_Ssc00601.propJgyNaiyo1.stringValue}"
																disabled="#{pc_Ssc00601.propJgyNaiyo1.disabled}" cols="34">
															</h:inputTextarea>
														</TD>
														<TH class="v_d" rowspan="3">
															<h:outputText
																styleClass="outputText" id="lblJgyPlace1"
																value="#{pc_Ssc00601.propJgyPlace1.labelName}"
																style="#{pc_Ssc00601.propJgyPlace1.labelStyle}">
															</h:outputText>
														</TH>
														<TD>
															<h:inputTextarea id="htmlJgyPlace1"
																styleClass="inputTextarea"
																readonly="#{pc_Ssc00601.propJgyPlace1.readonly}"
																style="#{pc_Ssc00601.propJgyPlace1.style}"
																value="#{pc_Ssc00601.propJgyPlace1.stringValue}"
																disabled="#{pc_Ssc00601.propJgyPlace1.disabled}" cols="34">
															</h:inputTextarea>
														</TD>
													</TR>
													<TR>
														<TD>
															<h:inputTextarea id="htmlJgyNaiyo2" rows="2"
																styleClass="inputTextarea"
																style="#{pc_Ssc00601.propJgyNaiyo2.style}"
																value="#{pc_Ssc00601.propJgyNaiyo2.stringValue}"
																disabled="#{pc_Ssc00601.propJgyNaiyo2.disabled}"
																readonly="#{pc_Ssc00601.propJgyNaiyo2.readonly}" cols="34">
															</h:inputTextarea>
														</TD>
														<TD>
															<h:inputTextarea id="htmlJgyPlace2"
																styleClass="inputTextarea"
																readonly="#{pc_Ssc00601.propJgyPlace2.readonly}"
																style="#{pc_Ssc00601.propJgyPlace2.style}"
																value="#{pc_Ssc00601.propJgyPlace2.stringValue}"
																disabled="#{pc_Ssc00601.propJgyPlace2.disabled}" cols="34">
															</h:inputTextarea>
														</TD>
													</TR>
													<TR>
														<TD>
															<h:inputTextarea id="htmlJgyNaiyo3"
																styleClass="inputTextarea"
																readonly="#{pc_Ssc00601.propJgyNaiyo3.readonly}"
																style="#{pc_Ssc00601.propJgyNaiyo3.style}"
																value="#{pc_Ssc00601.propJgyNaiyo3.stringValue}"
																disabled="#{pc_Ssc00601.propJgyNaiyo3.disabled}" cols="34">
															</h:inputTextarea>
														</TD>
														<TD>
															<h:inputTextarea id="htmlJgyPlace3"
																styleClass="inputTextarea"
																readonly="#{pc_Ssc00601.propJgyPlace3.readonly}"
																style="#{pc_Ssc00601.propJgyPlace3.style}"
																value="#{pc_Ssc00601.propJgyPlace3.stringValue}"
																disabled="#{pc_Ssc00601.propJgyPlace3.disabled}" cols="34">
															</h:inputTextarea>
														</TD>
													</TR>
													<TR>
														<TH class="v_a">
															<h:outputText
																styleClass="outputText" id="lblChiki"
																value="#{pc_Ssc00601.propChikiCd1.labelName}"
																style="#{pc_Ssc00601.propChikiCd1.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3">
															<h:selectOneMenu
																styleClass="selectOneMenu" id="htmlChikiCd1"
																value="#{pc_Ssc00601.propChikiCd1.stringValue}"
																style="#{pc_Ssc00601.propChikiCd1.style}"
																disabled="#{pc_Ssc00601.propChikiCd1.disabled}"
																readonly="#{pc_Ssc00601.propChikiCd1.readonly}">
																<f:selectItems value="#{pc_Ssc00601.propChikiCd1.list}" />
															</h:selectOneMenu>
															<h:selectOneMenu
																styleClass="selectOneMenu" id="htmlChikiCd2"
																value="#{pc_Ssc00601.propChikiCd2.stringValue}"
																style="#{pc_Ssc00601.propChikiCd2.style}"
																disabled="#{pc_Ssc00601.propChikiCd2.disabled}"
																readonly="#{pc_Ssc00601.propChikiCd2.readonly}">
																<f:selectItems value="#{pc_Ssc00601.propChikiCd2.list}" />
															</h:selectOneMenu>
															<h:selectOneMenu
																styleClass="selectOneMenu" id="htmlChikiCd3"
																value="#{pc_Ssc00601.propChikiCd3.stringValue}"
																style="#{pc_Ssc00601.propChikiCd3.style}"
																disabled="#{pc_Ssc00601.propChikiCd3.disabled}"
																readonly="#{pc_Ssc00601.propChikiCd3.readonly}">
																<f:selectItems value="#{pc_Ssc00601.propChikiCd3.list}" />
															</h:selectOneMenu>
														</TD>
													</TR>
													<TR>
														<TH class="v_b"><h:outputText
															styleClass="outputText" id="lblGyosyu"
															value="#{pc_Ssc00601.propGyosyuCd1.labelName}"
															style="#{pc_Ssc00601.propGyosyuCd1.labelStyle}"></h:outputText></TH>
														<TD colspan="3"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlGyosyuCd1"
															value="#{pc_Ssc00601.propGyosyuCd1.stringValue}"
															style="#{pc_Ssc00601.propGyosyuCd1.style}"
															disabled="#{pc_Ssc00601.propGyosyuCd1.disabled}"
															readonly="#{pc_Ssc00601.propGyosyuCd1.readonly}">
															<f:selectItems value="#{pc_Ssc00601.propGyosyuCd1.list}" />
														</h:selectOneMenu><BR>
														<h:selectOneMenu
															styleClass="selectOneMenu" id="htmlGyosyuCd2"
															value="#{pc_Ssc00601.propGyosyuCd2.stringValue}"
															style="#{pc_Ssc00601.propGyosyuCd2.style}"
															disabled="#{pc_Ssc00601.propGyosyuCd2.disabled}"
															readonly="#{pc_Ssc00601.propGyosyuCd2.readonly}">
															<f:selectItems value="#{pc_Ssc00601.propGyosyuCd2.list}" />
														</h:selectOneMenu><BR>
														<h:selectOneMenu
															styleClass="selectOneMenu" id="htmlGyosyuCd3"
															value="#{pc_Ssc00601.propGyosyuCd3.stringValue}"
															style="#{pc_Ssc00601.propGyosyuCd3.style}"
															disabled="#{pc_Ssc00601.propGyosyuCd3.disabled}"
															readonly="#{pc_Ssc00601.propGyosyuCd3.readonly}">
															<f:selectItems value="#{pc_Ssc00601.propGyosyuCd3.list}" />
														</h:selectOneMenu></TD>
													</TR>
											<TR>
												<TH class="v_c"><h:outputText
													styleClass="outputText" id="lblSyozaichi"
													value="#{pc_Ssc00601.propSyozaichiCd1.labelName}"
													style="#{pc_Ssc00601.propSyozaichiCd1.labelStyle}">
													</h:outputText>
												</TH>
												<TD colspan="3">
													<h:selectOneMenu
														styleClass="selectOneMenu" id="htmlSyozaichiCd1"
														value="#{pc_Ssc00601.propSyozaichiCd1.stringValue}"
														style="#{pc_Ssc00601.propSyozaichiCd1.style}"
														disabled="#{pc_Ssc00601.propSyozaichiCd1.disabled}"
														readonly="#{pc_Ssc00601.propSyozaichiCd1.readonly}">
														<f:selectItems value="#{pc_Ssc00601.propSyozaichiCd1.list}" />
													</h:selectOneMenu>
													<h:selectOneMenu styleClass="selectOneMenu"
														id="htmlSyozaichiCd2"
														value="#{pc_Ssc00601.propSyozaichiCd2.stringValue}"
														style="#{pc_Ssc00601.propSyozaichiCd2.style}"
														disabled="#{pc_Ssc00601.propSyozaichiCd2.disabled}"
														readonly="#{pc_Ssc00601.propSyozaichiCd2.readonly}">
														<f:selectItems value="#{pc_Ssc00601.propSyozaichiCd2.list}" />
													</h:selectOneMenu>
													<h:selectOneMenu styleClass="selectOneMenu"
														id="htmlSyozaichiCd3"
														value="#{pc_Ssc00601.propSyozaichiCd3.stringValue}"
														style="#{pc_Ssc00601.propSyozaichiCd3.style}"
														disabled="#{pc_Ssc00601.propSyozaichiCd3.disabled}"
														readonly="#{pc_Ssc00601.propSyozaichiCd3.readonly}">
														<f:selectItems value="#{pc_Ssc00601.propSyozaichiCd3.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
													<TR>
														<TH class="v_d"><h:outputText
															styleClass="outputText" id="lblMainGsyFlg"
															value="出力条件"
															style="#{pc_Ssc00601.propMainGsyFlg.labelStyle}"></h:outputText></TH>
														<TD align="center" colspan="3">
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlMainGsyFlg"
																value="#{pc_Ssc00601.propMainGsyFlg.checked}"
																style="#{pc_Ssc00601.propMainGsyFlg.style}"
																disabled="#{pc_Ssc00601.propMainGsyFlg.disabled}"
																readonly="#{pc_Ssc00601.propMainGsyFlg.readonly}">
															</h:selectBooleanCheckbox>
															<h:outputText styleClass="outputText"
																id="text1" value="主業種以外も含む">
															</h:outputText>
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlOnrSotKbn"
																value="#{pc_Ssc00601.propOnrSotKbn.checked}"
																style="#{pc_Ssc00601.propOnrSotKbn.style}">
															</h:selectBooleanCheckbox>
															<h:outputText styleClass="outputText"
																id="text2" value="オーナー企業のみ対象">
															</h:outputText>
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlHonKbn"
																value="#{pc_Ssc00601.propHonKbn.checked}"
																style="#{pc_Ssc00601.propHonKbn.style}">
															</h:selectBooleanCheckbox>
															<h:outputText styleClass="outputText"
																id="text3" value="本社のみ対象">
															</h:outputText>
														</TD>
													</TR>
													<TR>
														<TH class="v_g">
															<h:outputText
																styleClass="outputText" id="lblHaigyo"
																value="#{pc_Ssc00601.propHaigyo.labelName}"
																style="#{pc_Ssc00601.propHaigyo.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3">
															<h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlHaigyo"
																value="#{pc_Ssc00601.propHaigyo.stringValue}"
																style="#{pc_Ssc00601.propHaigyo.style}">
																<f:selectItem itemValue="1" itemLabel="廃業は含まない" />
																<f:selectItem itemValue="0" itemLabel="廃業を含む" />
															</h:selectOneRadio>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											<DIV style="text-align:left;margin:10px 0;">
												<h:outputText styleClass="note" id="cmt1" value="※全角において部分一致とする"></h:outputText>
											</DIV>
											<TABLE class="table" style="margin-bottom:20px;">
												<TBODY>
													<TR>
														<TH class="v_a" width="130">
															<h:outputText
																styleClass="outputText" id="lblChohyo"
																value="#{pc_Ssc00601.propChohyo.labelName}"
																style="#{pc_Ssc00601.propChohyo.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="730">
															<h:inputText id="htmlChohyo"
															styleClass="inputText"
															readonly="#{pc_Ssc00601.propChohyo.readonly}"
															style="#{pc_Ssc00601.propChohyo.style}"
															value="#{pc_Ssc00601.propChohyo.stringValue}"
															disabled="#{pc_Ssc00601.propChohyo.disabled}"
															maxlength="#{pc_Ssc00601.propChohyo.maxLength}" size="62">
															</h:inputText>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</DIV>
						<TABLE width="100%" class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="PDF作成"
											styleClass="commandExButton_out" id="pdfout"
											action="#{pc_Ssc00601.doBtnPdf_t1Action}"
											confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton><hx:commandExButton
											type="submit" value="CSV作成" styleClass="commandExButton_out"
											id="csvout" action="#{pc_Ssc00601.doBtnCsv_t1Action}"
											confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton
										    type="submit" value="出力項目指定" 
										    styleClass="commandExButton_out" id="setoutput" 
										    action="#{pc_Ssc00601.doBtnExport_t1Action}"></hx:commandExButton><hx:commandExButton type="submit" value="クリア"
											styleClass="commandExButton_etc" id="clear"
											action="#{pc_Ssc00601.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
	</DIV>
</DIV>
<!--↑content↑-->
</DIV>	
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
