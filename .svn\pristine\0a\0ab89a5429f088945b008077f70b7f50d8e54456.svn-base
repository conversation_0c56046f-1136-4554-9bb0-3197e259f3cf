<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/kk/Kkc00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Kkc00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>

<STYLE type="text/css">
/* 住所検索用 */
#livesearch2 {
margin: 0px 25px 15px 25px;
padding:0px;
width: 140px;
display: block;
border:1px solid #99b58d;
}

#LSResult2 {    
position: absolute;
background-color: #aaa; 
min-width: 180px; 
margin: 1px 0px 2px 0px;
padding: 0px;
}

#LSResult2 li {
font-size: 12px;
padding-bottom: 2px;
padding-top: 2px;
line-height:15px;
margin-bottom: 0px;
margin-left:4px;
}
      
ul#LSShadow2 {
position: relative;
right: 1px;
margin: 0px;
padding: 0px;
background-color: #666; /*shadow color*/
color: inherit;
}

#LSResult2 ul {
margin-bottom: -5px;
margin-top: 0px;
padding-top: 0px;  
margin: 0px;
padding: 0px;
}

#LSResult2 ul a {
color: #222;
background-color:transparent;
}

#LSResult2 ul a:hover {
color: #564b47;  
background-color: #ccc;
}

#LSResult2 ul a:active { 
color: #564b47;  
background-color: #ccc;
}

#LSResult2 ul li {
text-indent: -20px;
padding: 0px 15px 3px 20px;
}
 
.LSRes2 {
position: relative;
bottom: 1px;
right: 1px;
background-color: white;
border:  1px solid #AAA;
}
  
#LSHighlight2 {
color: #564b47;  
background-color: #ccc;
}
</STYLE>
<SCRIPT type="text/javascript">
////////////////////////////////////////////////////
// 住所検索用Javascript
<%-- 住所検索用のJavascriptが同一画面で二つ利用できないという
制限がある為リソースをコピー、gakuenCOに修正を行った場合は、
修正が必要 --%>
////////////////////////////////////////////////////
var Z2 = {
	menu: null,
	ajax: null,
	result: new Object(),
	lastcode: "",
	focus: false,
	form_zip: null,
	form_addr: null,
	form_addrKana: null,
	form_chihoCd: null
};

function func_JyusyoListAjax2(thisObj, thisEvent) {
	var servlet = "rev/co/CozJyusyoListAJAX";
	var target = "";
	var args = new Array();
	args['code'] = thisObj.value;
	var ajaxUtil = new AjaxUtil();
	
	//ajaxUtil.getPluralValue(servlet, target, args);
	ajaxUtil.getPluralValueSetMethod(servlet, target, args, "makeJyusyoMenu2");
}

function makeJyusyoMenu2(value) {
	var html = "";
	var len = parseInt(value['len']);
	
	html = '<div id="LSResult2"><ul id="LSShadow2"><ul class="LSRes">';
	if (len == 0) {
		html += '<li class="LSRow"><a href="" onClick="return not_select_zipmenu2()">該当する住所が見つかりません</a></li>';
	} else {
		for (var i = 0; i < len; i++) {
			html += '<li class="LSRow"><a id=\'' + i + '\' href="" onClick="return select_zipmenu2(\''+value['zip'+i]+'\',\''+value['addr'+i]+'\',\''+value['addrKana'+i]+'\',\''+value['chihoCd'+i]+'\')">'+value['add'+i]+'</a></li>';
		}
	}
	html += '</ul></ul></div>'
	Z2.menu.innerHTML = html;
	Z2.menu.style.visibility = "visible";

	// IE6のSELECTタグのz-indexバグ対応
	var menulist = document.getElementById("LSResult2");
    
    var shim = document.createElement("<iframe scrolling='no' frameborder='0'"+
                                      "style='position:absolute; top:0px;"+
                                      "left:0px; display:none'></iframe>");
    if (menulist.offsetParent==null || menulist.offsetParent.id=="") 
    {
        window.document.body.appendChild(shim);
    }
    else 
    {
        menulist.offsetParent.appendChild(shim); 
    }
	move_zipmenu2();
    menulist.style.zIndex = 100;
    var width = menulist.offsetWidth;
    var height = menulist.offsetHeight;
    shim.style.width = width;
    shim.style.height = height;
    shim.style.top = menulist.offsetTop;
    shim.style.left = menulist.offsetLeft;
    shim.style.zIndex = menulist.style.zIndex - 1;
    shim.style.position = "absolute";
    shim.style.display = "block";
	
}


function not_select_zipmenu2() {
	Z2.menu.style.visibility = "hidden";
	return false;
}

function select_zipmenu2(zip, add, addKana, chihoCd) {
	
	if (Z2.form_zip){
		if (zip.length == 7 && zip.charAt(3) != '-'){
			Z2.form_zip.value  = zip.substring(0,3) + "-" + zip.substring(3,7);
		}else{
			Z2.form_zip.value  = zip;
		}
		
	}
	if (Z2.form_addr){
		Z2.form_addr.value = add;
	}
	if (Z2.form_addrKana){
		Z2.form_addrKana.value = addKana;
	}
	if (Z2.form_chihoCd){
		Z2.form_chihoCd.value = chihoCd;
	}

	Z2.menu.style.visibility = "hidden";
	return false;
}
function move_zipmenu2() {
	if (Z2.menu.style.visibility != "visible") {
		return;
	}
	
	Z2.menu.style.left = calc_pos2(Z2.form_zip, "offsetLeft") + "px";
	
	Z2.menu.style.top = calc_pos2(Z2.form_zip, "offsetTop") + Z2.form_zip.offsetHeight - 1 + "px";
	
}

function calc_pos2(obj, key) {
	var pos = 0;
	while (obj) {
		pos += obj[key];
		obj = obj.offsetParent;
	}
	return pos;
}

// zip 郵便番号入力ボックス
// addr 住所1入力ボックス
// addrKana 住所のカナ入力ボックス
// chichoCd 地方コード入力ボックス
function setZipMenu2() {

    var zip = "";    // 第1引数のデフォルト値
    var addr = "";    // 第2引数のデフォルト値
    var addrKana = "";    // 第3引数のデフォルト値
    var chihoCd = "";    // 第4引数のデフォルト値

    switch (arguments.length) {
    default:
    case 4: chihoCd = arguments[3];
    case 3: addrKana = arguments[2];
    case 2: addr = arguments[1];
    case 1: zip = arguments[0];
    case 0:
    }

	var menu = document.createElement("DIV");
	menu.id = "zipmenu";
	menu.style.position = "absolute";
	menu.style.visibility = "hidden";
	document.body.appendChild(menu);
	menu.onmouseover = function() {Z2.focus = true;};
	menu.onmouseout = function() {Z2.focus = false;};
	Z2.menu = menu;
	
	if (document.getElementById(zip)){
		document.getElementById(zip).setAttribute('autocomplete','off');
		Z2.form_zip = document.getElementById(zip);
		Z2.form_zip.onkeydown = liveSearchKeyPress2;
		Z2.form_zip.onkeyup = zip_onkeyup2;
		Z2.form_zip.onblur = zip_onblur2;	
	}

	if (document.getElementById(addr)){
		Z2.form_addr = document.getElementById(addr);
	}

	if (document.getElementById(addrKana)){
		Z2.form_addrKana = document.getElementById(addrKana);
	}

	if (document.getElementById(chihoCd)){
		Z2.form_chihoCd = document.getElementById(chihoCd);
	}
		
	window.onresize = move_zipmenu2;
}

// onload

function zip_onkeyup2() {
	var code = Z2.form_zip.value.replace(/\D/g, "");
	if (code == Z2.lastcode) {
		return;
	}
	Z2.lastcode = code;
	if (code.length < 3) {
		Z2.menu.style.visibility = "hidden";
		return;
	}
	if (Z2.result[code] != undefined) {
		show_zipmenu2(Z2.form_zip);
	} else {
		if (event.keyCode != 13){
			func_JyusyoListAjax2(Z2.form_zip, null);
		}
	}
}

function show_zipmenu2(code) {
	var html = Z2.result[code];
	alert(html);
	Z2.menu.innerHTML = html;
	Z2.menu.style.visibility = "visible";
	move_zipmenu2();
}

function zip_onblur2() {
	if (!Z2.focus) {
		Z2.menu.style.visibility = "hidden";
	}
}


function liveSearchKeyPress2() {
	if (event.keyCode == 40 )
	//KEY DOWN
	{
		highlight = document.getElementById("LSHighlight2");
		if (!highlight) {
			if (document.getElementById("LSShadow2")){
				highlight = document.getElementById("LSShadow2").firstChild.firstChild;
				try{
					highlight.focus();				
				} catch (e){}
			}
		} else {
			highlight.removeAttribute("id");
			highlight = highlight.nextSibling;			
		}
		if (highlight) {
			highlight.setAttribute("id","LSHighlight2");
		} 
	} 
	//KEY UP
	else if (event.keyCode == 38 ) {
		highlight = document.getElementById("LSHighlight2");
		if (!highlight) {
			if (document.getElementById("LSResult2")){
				highlight = document.getElementById("LSResult2").firstChild.firstChild.lastChild;
			}
		} 
		else {
			highlight.removeAttribute("id");
			highlight = highlight.previousSibling;
		}
		if (highlight) {
				highlight.setAttribute("id","LSHighlight2");
		}	} 
	//ESC
	else if (event.keyCode == 27) {
		highlight = document.getElementById("LSHighlight2");
		if (highlight) {
			highlight.removeAttribute("id");
		}
		if (document.getElementById("LSResult2")){
			document.getElementById("LSResult2").style.display = "none";
		}
	} 
	//ENTER
	else if (event.keyCode == 13) {
		liveSearchSubmit2();
	}

}

// リストでエンターボタンを押下された場合
function liveSearchSubmit2() {
	var highlight = document.getElementById("LSHighlight2");
	if (highlight && highlight.firstChild) {
		highlight.firstChild.onclick();
		return false;
	} 
	else {
		return true;
	}
}

</SCRIPT>

<SCRIPT type="text/javascript">

// 画面ロード時の名称再取得
function loadAction(event){
  doGakusekiAjax(document.getElementById('form1:htmlGakusekiCdFrom'), event, 'form1:htmlGakusekiNmFrom');
  doGakusekiAjax(document.getElementById('form1:htmlGakusekiCdTo'), event, 'form1:htmlGakusekiNmTo');
  doKaiinAjax(document.getElementById('form1:htmlKaiinNoFrom'), event, 'form1:htmlKaiinNmFrom');
  doKaiinAjax(document.getElementById('form1:htmlKaiinNoTo'), event, 'form1:htmlKaiinNmTo');
  doKyoinAjax(document.getElementById('form1:htmlSeminerKyoinCd'), event, 'form1:htmlSeminerKyoinName');
  setZipMenu('form1:htmlAddrCdFrom','','');
  setZipMenu2('form1:htmlAddrCdTo','','');
}

// 学籍番号より氏名を取得する
function doGakusekiAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/co/CobSotAJAX";
  var args = new Array();
  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// 会員氏名を取得する
function doKaiinAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/kk/VKkaKaiDaiAJAX";
  var args = new Array();
  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

function openSotSubWindow(field1, field2, field3) {
	// 卒業生検索画面
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0201.jsp"
		+ "?retFieldName=" + field1 
		+ "&retFieldName2=" + field2
		+ "&retFieldName3=" + field3;
	openModalWindow(url, "pCob0201", "<%=com.jast.gakuen.rev.co.PCob0201.getWindowOpenOption() %>");
	return false;
}

// 住所検索画面（引数：①②郵便番号、③④住所１、⑤住所カナ１）
function openJyusyoSubWindow(field1, field2, field3) {
  document.getElementById(field2).value = "";
  document.getElementById(field3).value = "";
  var zipNo = document.getElementById(field1).value;
  zipNo = encodeURIComponent(zipNo);
  var add = document.getElementById(field2).value;
  add = encodeURIComponent(add);
  var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp"
    +"?"
    +"zipNo=" + field1
    +"&zipNoValue=" + zipNo
    +"&jyusyoKanji=" + field2
    +"&jyusyoValue=" + add
    +"&jyusyoKana=" + field3
    +"&chihoCd=";
  openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");
  return true;
}

// 教員検索画面（引数：①教員コード）
function openKyoinSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp"
		+ "?retFieldName=" + field1 
		+ "&kensakutaishoKbn="
		
	openModalWindow(url, "PCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return false;
}

// 教員名称を取得する
function doKyoinAjax(thisObj, thisEvent, targetLabel) {
	var servlet = "rev/co/CobJinjAJAX";
	var args = new Array();
	args['code'] = thisObj.value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "1";
	indirectClick('search');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearchHidden').value = "0";
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kkc00301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Kkc00301.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Kkc00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Kkc00301.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD width="900">
				<TABLE border="0" class="table" width="100%">
					<TBODY>
						<TR>
							<TH class="v_a" width="150"><!-- グループ種別 --><h:outputText
								styleClass="outputText" id="lblGroupSbt"
								style="#{pc_Kkc00301.propGroupSbtCombo.labelStyle}"
								value="#{pc_Kkc00301.propGroupSbtCombo.labelName}"></h:outputText></TH>
							<TD><h:selectOneMenu
								styleClass="selectOneMenu" id="htmlGroupSbtCombo"
								disabled="#{pc_Kkc00301.propGroupSbtCombo.disabled}"
								style="#{pc_Kkc00301.propGroupSbtCombo.style};width:420px"
								value="#{pc_Kkc00301.propGroupSbtCombo.value}">
								<f:selectItems value="#{pc_Kkc00301.propGroupSbtCombo.list}" />
							</h:selectOneMenu> <hx:commandExButton type="submit"
								value="選択" styleClass="commandExButton" id="groupSbtSelect"
								disabled="#{pc_Kkc00301.propGroupSbtSelectBtn.disabled}"
								action="#{pc_Kkc00301.doGroupSbtSelectAction}">
							</hx:commandExButton><hx:commandExButton type="submit"
								value="解除" styleClass="commandExButton" id="groupSbtUnSelect"
								disabled="#{pc_Kkc00301.propGroupSbtUnSelectBtn.disabled}"
								action="#{pc_Kkc00301.doGroupSbtUnSelectAction}">
							</hx:commandExButton></TD>
						</TR>
						<TR>
							<TH class="v_a"><!-- グループ --><h:outputText
								styleClass="outputText" id="lblGroup"
								style="#{pc_Kkc00301.propGroupCombo.labelStyle}"
								value="#{pc_Kkc00301.propGroupCombo.labelName}"></h:outputText></TH>
							<TD><h:selectOneMenu
								styleClass="selectOneMenu" id="htmlGroupCombo"
								disabled="#{pc_Kkc00301.propGroupCombo.disabled}"
								style="#{pc_Kkc00301.propGroupCombo.style};width:504px"
								value="#{pc_Kkc00301.propGroupCombo.value}">
								<f:selectItems value="#{pc_Kkc00301.propGroupCombo.list}" />
							</h:selectOneMenu>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
					<TBODY>
						<TR>
							<TD align="left">
								<h:outputText id="text1" styleClass="outputText"
									value="※ここで選択したグループに所属していない会員を検索します。" /></TD>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE border="0" class="table" cellpadding="0" cellspacing="0" width="100%" style="margin-top:10px">
					<TBODY>
						<TR>
							<TH width="900" class="group_label_top" class="v_b" colspan="9"
								style="border-left-style:solid;" style="border-left-width:1px"><h:outputText 
								id="lbltxt4" value="卒業年度・卒業学期ＮＯ"></h:outputText></TH>
						</TR>
						<TR>
							<TD width="20" class="group_label" style="border-bottom-style:solid;"
								style="border-left-style:solid;"></TD>
							<TH width="130" class="v_b"><!-- 開始年度 --><h:outputText styleClass="outputText"
								id="lblSotNenFrom" value="#{pc_Kkc00301.propSotNenFrom.labelName}"
								style="#{pc_Kkc00301.propSotNenFrom.labelStyle}"></h:outputText></TH>
							<TD width="100" class="v_b"><h:inputText styleClass="inputText"
								id="htmlSotNenFrom" size="4" 
								disabled="#{pc_Kkc00301.propSotNenFrom.disabled}"
								value="#{pc_Kkc00301.propSotNenFrom.dateValue}"
								style="#{pc_Kkc00301.propSotNenFrom.style}">
								<hx:inputHelperAssist errorClass="inputText_Error"
									imeMode="inactive" promptCharacter="_" />
								<f:convertDateTime pattern="yyyy" />
							</h:inputText></TD>
							<TH width="100" class="v_b"><!-- 開始学期ＮＯ --><h:outputText styleClass="outputText"
								id="lblSotGakkiFrom" value="#{pc_Kkc00301.propSotGakkiFrom.labelName}"
								style="#{pc_Kkc00301.propSotGakkiFrom.labelStyle}"></h:outputText></TH>
							<TD width="100" class="v_b"><h:inputText styleClass="inputText"
								id="htmlSotGakkiFrom" size="4" 
								disabled="#{pc_Kkc00301.propSotGakkiFrom.disabled}"
								value="#{pc_Kkc00301.propSotGakkiFrom.integerValue}"
								style="#{pc_Kkc00301.propSotGakkiFrom.style}">
								<f:convertNumber type="number" pattern="#0" />
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>　 　～</TD>
							<TH width="100" class="v_b"><!-- 終了年度 --><h:outputText styleClass="outputText"
								id="lblSotNenTo" value="#{pc_Kkc00301.propSotNenTo.labelName}"
								style="#{pc_Kkc00301.propSotNenTo.labelStyle}"></h:outputText></TH>
							<TD width="100" class="v_b"><h:inputText styleClass="inputText"
								id="htmlSotNenTo" size="4" 
								disabled="#{pc_Kkc00301.propSotNenTo.disabled}"
								value="#{pc_Kkc00301.propSotNenTo.dateValue}"
								style="#{pc_Kkc00301.propSotNenTo.style}">
								<hx:inputHelperAssist errorClass="inputText_Error"
									imeMode="inactive" promptCharacter="_" />
								<f:convertDateTime pattern="yyyy" />
							</h:inputText></TD>
							<TH width="100" class="v_b"><!-- 終了学期ＮＯ --><h:outputText styleClass="outputText"
								id="lblSotGakkiTo" value="#{pc_Kkc00301.propSotGakkiTo.labelName}"
								style="#{pc_Kkc00301.propSotGakkiTo.labelStyle}"></h:outputText></TH>
							<TD width="100" class="v_b"><h:inputText styleClass="inputText"
								id="htmlSotGakkiTo" size="4" 
								disabled="#{pc_Kkc00301.propSotGakkiTo.disabled}"
								value="#{pc_Kkc00301.propSotGakkiTo.integerValue}"
								style="#{pc_Kkc00301.propSotGakkiTo.style}">
								<f:convertNumber type="number" pattern="#0" />
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText></TD>
						</TR>
						<TR>
							<TH nowrap class="v_a" width="100" colspan="2"><!--学籍番号 --><h:outputText styleClass="outputText"
								id="lblGakusekiCd"
								value="#{pc_Kkc00301.propGakusekiCdFrom.labelName}"
								style="#{pc_Kkc00301.propGakusekiCdFrom.labelStyle}"></h:outputText></TH>
							<TD width="700" colspan="7"><h:inputText styleClass="inputText"
								id="htmlGakusekiCdFrom" size="18"
								maxlength="#{pc_Kkc00301.propGakusekiCdFrom.maxLength}"
								disabled="#{pc_Kkc00301.propGakusekiCdFrom.disabled}"
								value="#{pc_Kkc00301.propGakusekiCdFrom.stringValue}"
								readonly="#{pc_Kkc00301.propGakusekiCdFrom.readonly}"
								style="#{pc_Kkc00301.propGakusekiCdFrom.style}"
								onblur="return doGakusekiAjax(this, event, 'form1:htmlGakusekiNmFrom');"></h:inputText>
								<hx:commandExButton type="submit" value="検"
								styleClass="commandExButton_search" id="btnGakusekiCdFrom"
								onclick="return openSotSubWindow('form1:htmlGakusekiCdFrom','form1:htmlNendoHidden','form1:htmlGakkiNoHidden');">
							</hx:commandExButton><h:inputText styleClass="likeOutput"
								id="htmlGakusekiNmFrom" size="22"
								readonly="true"
								value="#{pc_Kkc00301.propGakusekiNameFrom.stringValue}"></h:inputText>　～　
							<h:inputText styleClass="inputText"
								id="htmlGakusekiCdTo" size="18"
								maxlength="#{pc_Kkc00301.propGakusekiCdTo.maxLength}"
								disabled="#{pc_Kkc00301.propGakusekiCdTo.disabled}"
								value="#{pc_Kkc00301.propGakusekiCdTo.stringValue}"
								readonly="#{pc_Kkc00301.propGakusekiCdTo.readonly}"
								style="#{pc_Kkc00301.propGakusekiCdTo.style}"
								onblur="return doGakusekiAjax(this, event, 'form1:htmlGakusekiNmTo');"></h:inputText>
								<hx:commandExButton type="submit" value="検"
								styleClass="commandExButton_search" id="btnGakusekiCdTo"
								onclick="return openSotSubWindow('form1:htmlGakusekiCdTo','form1:htmlNendoHidden','form1:htmlGakkiNoHidden');">
								</hx:commandExButton>
							<h:inputText styleClass="likeOutput"
								id="htmlGakusekiNmTo" size="24"
								readonly="true"
								value="#{pc_Kkc00301.propGakusekiNameTo.stringValue}">
							</h:inputText></TD>
						</TR>
						<TR>
							<TH class="v_a" colspan="2"><!-- 校友会 --><h:outputText
								styleClass="outputText" id="lblKoyu"
								value="#{pc_Kkc00301.propKoyuList.labelName}"></h:outputText></TH>
							<TD colspan="3"><h:selectOneMenu
								styleClass="selectOneMenu" id="htmlKoyuList"
								disabled="#{pc_Kkc00301.propKoyuList.disabled}"
								style="#{pc_Kkc00301.propKoyuList.style};width:220px"
								value="#{pc_Kkc00301.propKoyuList.value}">
								<f:selectItems value="#{pc_Kkc00301.propKoyuList.list}" />
							</h:selectOneMenu> <hx:commandExButton type="submit"
								value="選択" styleClass="commandExButton" id="koyuSelect"
								disabled="#{pc_Kkc00301.propKoyuSelectBtn.disabled}"
								action="#{pc_Kkc00301.doKoyuSelectAction}">
							</hx:commandExButton><hx:commandExButton type="submit"
								value="解除" styleClass="commandExButton" id="koyuUnSelect"
								disabled="#{pc_Kkc00301.propKoyuUnSelectBtn.disabled}"
								action="#{pc_Kkc00301.doKoyuUnSelectAction}">
							</hx:commandExButton></TD>
							<TH class="v_a"><!-- 学校 --><h:outputText
								styleClass="outputText" id="lblGako"
								value="#{pc_Kkc00301.propGakoList.labelName}"></h:outputText></TH>
							<TD colspan="3"><h:selectOneMenu
								styleClass="selectOneMenu" id="htmlGakoList"
								disabled="#{pc_Kkc00301.propGakoList.disabled}"
								style="#{pc_Kkc00301.propGakoList.style};width:304px"
								value="#{pc_Kkc00301.propGakoList.value}">
								<f:selectItems value="#{pc_Kkc00301.propGakoList.list}" />
							</h:selectOneMenu></TD>
						</TR>
						<TR>
							<TH class="v_a" colspan="2"><!-- 事務局 --><h:outputText
								styleClass="outputText" id="lblJmk"
								value="#{pc_Kkc00301.propJmkList.labelName}"></h:outputText></TH>
							<TD colspan="3"><h:selectOneMenu
								styleClass="selectOneMenu" id="htmlJmkList"
								disabled="#{pc_Kkc00301.propJmkList.disabled}"
								style="#{pc_Kkc00301.propJmkList.style};width:304px"
								value="#{pc_Kkc00301.propJmkList.value}">
								<f:selectItems value="#{pc_Kkc00301.propJmkList.list}" />
							</h:selectOneMenu></TD>
							<TH class="v_a"><!-- 卒業生学科組織 --><h:outputText
								styleClass="outputText" id="lblSotSzksGakka"
								value="#{pc_Kkc00301.propSotSzksGakkaList.labelName}"
								style="#{pc_Kkc00301.propSotSzksGakkaList.labelStyle}"></h:outputText></TH>
							<TD colspan="3"><h:selectOneMenu
								styleClass="selectOneMenu" id="htmlSotSzksGakkaList"
								disabled="#{pc_Kkc00301.propSotSzksGakkaList.disabled}"
								value="#{pc_Kkc00301.propSotSzksGakkaList.value}"
								style="width:304px;">
								<f:selectItems
									value="#{pc_Kkc00301.propSotSzksGakkaList.list}" />
							</h:selectOneMenu>
						</TR>
						<TR>
							<TH nowrap class="v_f" colspan="2"><!-- 会員区分 --><h:outputText
								styleClass="outputText" id="lblKaiKbnList"
								value="#{pc_Kkc00301.propKaiKbnList.labelName}"></h:outputText><BR>
								<h:outputText styleClass="outputText" id="lblComment1"
								value="（複数選択可）"></h:outputText>
							</TH>
							<TD colspan="2" style="border-right-style:none;"><h:selectManyListbox
								styleClass="selectManyListbox" id="htmlKaiKbnList"
								value="#{pc_Kkc00301.propKaiKbnList.value}"
								disabled="#{pc_Kkc00301.propKaiKbnList.disabled}"
								style="width:200px; height:60px;">
								<f:selectItems value="#{pc_Kkc00301.propKaiKbnList.list}" /></h:selectManyListbox>
							</TD>
							<TD style="border-left-style:none;" width="100">
								<TABLE class="clear_border" widht="100">
									<TR>
										<TD valign="middle">
											<hx:commandExButton type="submit"
											value="選択" styleClass="commandExButton" id="kaiKbnSelect"
											disabled="#{pc_Kkc00301.propKaiKbnSelectBtn.disabled}"
											action="#{pc_Kkc00301.doKaiKbnSelectAction}">
										</hx:commandExButton><hx:commandExButton type="submit"
											value="解除" styleClass="commandExButton" id="kaiKbnUnSelect"
											disabled="#{pc_Kkc00301.propKaiKbnUnSelectBtn.disabled}"
											action="#{pc_Kkc00301.doKaiKbnUnSelectAction}">
										</hx:commandExButton>
										</TD>
									</TR>
								</TABLE>
							</TD>
							<TH nowrap class="v_f"><!-- 会員会員種別 --><h:outputText
								styleClass="outputText" id="lblKaiSbtList"
								value="#{pc_Kkc00301.propKaiSbtList.labelName}"></h:outputText><BR>
								<h:outputText styleClass="outputText" id="lblComment2"
								value="（複数選択可）"></h:outputText>
							</TH>
							<TD colspan="3"><h:selectManyListbox
								styleClass="selectManyListbox" id="htmlKaiSbtList"
								value="#{pc_Kkc00301.propKaiSbtList.value}"
								disabled="#{pc_Kkc00301.propKaiSbtList.disabled}"
								style="width:200px; height:60px;">
								<f:selectItems value="#{pc_Kkc00301.propKaiSbtList.list}" />
							</h:selectManyListbox></TD>
						</TR>
						<TR>
							<TH nowrap class="v_a" width="100" colspan="2"><!--会員番号 --><h:outputText styleClass="outputText"
								id="lblKaiinNo"
								value="#{pc_Kkc00301.propKaiinNoFrom.labelName}"
								style="#{pc_Kkc00301.propKaiinNoFrom.labelStyle}"></h:outputText></TH>
							<TD width="700" colspan="7"><h:inputText styleClass="inputText"
								id="htmlKaiinNoFrom" size="18"
								maxlength="#{pc_Kkc00301.propKaiinNoFrom.maxLength}"
								disabled="#{pc_Kkc00301.propKaiinNoFrom.disabled}"
								value="#{pc_Kkc00301.propKaiinNoFrom.stringValue}"
								readonly="#{pc_Kkc00301.propKaiinNoFrom.readonly}"
								style="#{pc_Kkc00301.propKaiinNoFrom.style}"
								onblur="return doKaiinAjax(this, event, 'form1:htmlKaiinNmFrom');"></h:inputText>
							<hx:commandExButton type="submit" value="検"
								styleClass="commandExButton_search" id="btnKaiinNoFrom"
								action="#{pc_Kkc00301.doKaiinFromAction}">
							</hx:commandExButton><h:inputText styleClass="likeOutput"
								id="htmlKaiinNmFrom" size="22"
								readonly="true"
								value="#{pc_Kkc00301.propKaiinNameFrom.stringValue}"></h:inputText>　～　
							<h:inputText styleClass="inputText"
								id="htmlKaiinNoTo" size="18"
								maxlength="#{pc_Kkc00301.propKaiinNoTo.maxLength}"
								disabled="#{pc_Kkc00301.propKaiinNoTo.disabled}"
								value="#{pc_Kkc00301.propKaiinNoTo.stringValue}"
								readonly="#{pc_Kkc00301.propKaiinNoTo.readonly}"
								style="#{pc_Kkc00301.propKaiinNoTo.style}"
								onblur="return doKaiinAjax(this, event, 'form1:htmlKaiinNmTo');"></h:inputText>
							<hx:commandExButton type="submit" value="検"
								styleClass="commandExButton_search" id="btnKaiinNoTo"
								action="#{pc_Kkc00301.doKaiinToAction}">
								</hx:commandExButton>
							<h:inputText styleClass="likeOutput"
								id="htmlKaiinNmTo" size="24"
								readonly="true"
								value="#{pc_Kkc00301.propKaiinNameTo.stringValue}"></h:inputText></TD>
						</TR>
						<TR>
							<TH nowrap class="v_f" colspan="2"><!-- 年齢 --><h:outputText
								styleClass="outputText" id="lblAge"
								value="#{pc_Kkc00301.propAgeFrom.labelName}"></h:outputText>
							</TH>
							<TD colspan="3"><h:inputText styleClass="inputText"
								id="htmlAgeFrom" size="4" 
								disabled="#{pc_Kkc00301.propAgeFrom.disabled}"
								value="#{pc_Kkc00301.propAgeFrom.integerValue}"
								style="#{pc_Kkc00301.propAgeFrom.style}">
								<f:convertNumber type="number" pattern="###" />
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>　歳　～　
							<h:inputText styleClass="inputText"
								id="htmlAgeTo" size="4" 
								disabled="#{pc_Kkc00301.propAgeTo.disabled}"
								value="#{pc_Kkc00301.propAgeTo.integerValue}"
								style="#{pc_Kkc00301.propAgeTo.style}">
								<f:convertNumber type="number" pattern="###" />
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>　歳
							</TD>
							<TH nowrap class="v_a"><!-- 故人	 --><h:outputText
								styleClass="outputText" id="lblKojin"
								value="#{pc_Kkc00301.propKojinChk.labelName}"
								style="#{pc_Kkc00301.propKojinChk.labelStyle}"></h:outputText></TH>
							<TD colspan="3"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlKojinChk"
								value="#{pc_Kkc00301.propKojinChk.checked}"
								style="#{pc_Kkc00301.propKojinChk.style}"
								disabled="#{pc_Kkc00301.propKojinChk.disabled}"
								readonly="#{pc_Kkc00301.propKojinChk.readonly}"></h:selectBooleanCheckbox><h:outputText
								styleClass="outputText" id="lbltxt8" value="出力しない">
							</h:outputText></TD>
						</TR>
						<TR>
							<TH nowrap class="v_f" colspan="2" rowspan="3"><!-- 郵便番号 --><h:outputText
								styleClass="outputText" id="lblAddrCd"
								value="#{pc_Kkc00301.propAddrCdFrom.labelName}"></h:outputText>
							</TH>
							<TD style="border-bottom-style:none;" colspan="3"><h:inputText id="htmlAddrCdFrom" styleClass="inputText"
								disabled="#{pc_Kkc00301.propAddrCdFrom.disabled}"
								value="#{pc_Kkc00301.propAddrCdFrom.stringValue}"
								size="8"
								style="#{pc_Kkc00301.propAddrCdFrom.style}"
								maxlength="#{pc_Kkc00301.propAddrCdFrom.maxLength}" />
							<hx:commandExButton type="button" id="btnAddrCdFrom"
								styleClass="commandExButton_search" value="〒"
								onclick="openJyusyoSubWindow('form1:htmlAddrCdFrom', 'form1:htmlJmkAddr1Hidden', 'form1:htmlJmkAddrKana1Hidden');" />
							<h:outputText id="lblBtwn" styleClass="outputText" value="～" />
							<h:inputText id="htmlAddrCdTo" styleClass="inputText"
								disabled="#{pc_Kkc00301.propAddrCdTo.disabled}"
								value="#{pc_Kkc00301.propAddrCdTo.stringValue}"
								size="8"
								style="#{pc_Kkc00301.propAddrCdTo.style}"
								maxlength="#{pc_Kkc00301.propAddrCdTo.maxLength}" />
							<hx:commandExButton type="button" id="btnAddrCdTo"
								styleClass="commandExButton_search" value="〒"
								onclick="openJyusyoSubWindow('form1:htmlAddrCdTo', 'form1:htmlJmkAddr1Hidden', 'form1:htmlJmkAddrKana1Hidden');" /></TD>
							<TH nowrap class="v_f" rowspan="3"><!-- 出身地 --><h:outputText
								styleClass="outputText" id="lblSyshinList"
								value="#{pc_Kkc00301.propSyussinList.labelName}"></h:outputText><BR>
							<h:outputText styleClass="outputText" id="lblComment3"
								value="（複数選択可）">
							</h:outputText></TH>
							<TD rowspan="3" colspan="3"><h:selectManyListbox
								styleClass="selectManyListbox" id="htmlSyshinList"
								value="#{pc_Kkc00301.propSyussinList.value}"
								style="width:200px; height:60px;">
								<f:selectItems value="#{pc_Kkc00301.propSyussinList.list}" />
							</h:selectManyListbox></TD>
						</TR>
						<TR>
							<TD colspan="3" style="border-top-style:none;border-bottom-style:none;"><h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlAddrCdFindType"
								value="#{pc_Kkc00301.propAddrCdFindType.checked}">
							</h:selectBooleanCheckbox>前方一致</TD>
						</TR>
						<TR>
							<TD colspan="3" style="border-top-style:none;"></TD>
						</TR>
						<TR>
							<TH nowrap class="v_a" colspan="2"><!-- 学位記番号 --><h:outputText styleClass="outputText"
								id="lblGakuikiNo"
								value="#{pc_Kkc00301.propGakuikiNoFrom.labelName}"
								style="#{pc_Kkc00301.propGakuikiNoFrom.labelStyle}"></h:outputText></TH>
							<TD colspan="7"><h:inputText styleClass="inputText"
								id="htmlGakuikiNoFrom" size="30"
								maxlength="#{pc_Kkc00301.propGakuikiNoFrom.maxLength}"
								disabled="#{pc_Kkc00301.propGakuikiNoFrom.disabled}"
								value="#{pc_Kkc00301.propGakuikiNoFrom.stringValue}"
								readonly="#{pc_Kkc00301.propGakuikiNoFrom.readonly}"
								style="#{pc_Kkc00301.propGakuikiNoFrom.style}"></h:inputText>
							<h:outputText id="lblBtwn2" styleClass="outputText" value="　　～　　" />
							<h:inputText styleClass="inputText"
								id="htmlGakuikiNoTo" size="30"
								maxlength="#{pc_Kkc00301.propGakuikiNoTo.maxLength}"
								disabled="#{pc_Kkc00301.propGakuikiNoTo.disabled}"
								value="#{pc_Kkc00301.propGakuikiNoTo.stringValue}"
								readonly="#{pc_Kkc00301.propGakuikiNoTo.readonly}"
								style="#{pc_Kkc00301.propGakuikiNoTo.style}"></h:inputText>
							</TD>
						</TR>
						<TR><TH nowrap class="v_a" colspan="2"><!-- 在学時ゼミ --><h:outputText styleClass="outputText"
								id="lblSeminer"
								value="#{pc_Kkc00301.propSeminer.labelName}"
								style="#{pc_Kkc00301.propSeminer.labelStyle}"></h:outputText></TH>
							<TD colspan="4"><h:inputText styleClass="inputText"
								id="htmlSeminer" size="64"
								maxlength="#{pc_Kkc00301.propSeminer.maxLength}"
								disabled="#{pc_Kkc00301.propSeminer.disabled}"
								value="#{pc_Kkc00301.propSeminer.stringValue}"
								readonly="#{pc_Kkc00301.propSeminer.readonly}"
								style="#{pc_Kkc00301.propSeminer.style}"></h:inputText>
							</TD>
							<TD colspan="3"><h:selectOneRadio
									styleClass="selectOneRadio" id="htmlSeminerFindType"
									value="#{pc_Kkc00301.propSeminerFindType.value}">
									<f:selectItems value="#{pc_Kkc00301.propSeminerFindType.list}" />
								</h:selectOneRadio></TD>
						</TR>
						<TR><TH nowrap class="v_a" colspan="2"><!-- ゼミ担当教員コード --><h:outputText styleClass="outputText"
								id="lblSeminerKyoinCd"
								value="#{pc_Kkc00301.propSeminerKyoinCd.labelName}"
								style="#{pc_Kkc00301.propSeminerKyoinCd.labelStyle}"></h:outputText></TH>
							<TD colspan="7"><h:inputText styleClass="inputText"
								id="htmlSeminerKyoinCd" size="20"
								maxlength="#{pc_Kkc00301.propSeminerKyoinCd.maxLength}"
								disabled="#{pc_Kkc00301.propSeminerKyoinCd.disabled}"
								value="#{pc_Kkc00301.propSeminerKyoinCd.stringValue}"
								readonly="#{pc_Kkc00301.propSeminerKyoinCd.readonly}"
								style="#{pc_Kkc00301.propSeminerKyoinCd.style}"
								onblur="return doKyoinAjax(this, event, 'form1:htmlSeminerKyoinName');"></h:inputText>
								<hx:commandExButton type="button" value="検"
								styleClass="commandExButton_search" id="btnJinjSearch1"
								onclick="return openKyoinSubWindow('form1:htmlSeminerKyoinCd');"></hx:commandExButton>
								<h:inputText styleClass="likeOutput" id="htmlSeminerKyoinName"
								size="60" readonly="true" tabindex="-1"
								value="#{pc_Kkc00301.propSeminerKyoinName.stringValue}"></h:inputText>
							</TD>
						</TR>
						<TR><TH nowrap class="v_a" colspan="2"><!-- クラブサークル --><h:outputText
							styleClass="outputText" id="lblClubCircle"
							value="#{pc_Kkc00301.propClubCircle.labelName}"
							style="#{pc_Kkc00301.propClubCircle.labelStyle}"></h:outputText></TH>
							<TD colspan="7"><h:selectOneMenu styleClass="selectOneMenu"
								id="htmlClubCircle"
								value="#{pc_Kkc00301.propClubCircle.value}" style="width:500px"
								disabled="#{pc_Kkc00301.propClubCircle.disabled}">
								<f:selectItems value="#{pc_Kkc00301.propClubCircle.list}">
								</f:selectItems>
							</h:selectOneMenu>
							</TD>
						</TR>
						<TR><TH nowrap class="v_a" colspan="2"><!-- 帳票タイトル --><h:outputText
							styleClass="outputText" id="lblPdfTitle"
							value="#{pc_Kkc00301.propPdfTitle.labelName}"
							style="#{pc_Kkc00301.propPdfTitle.labelStyle}"></h:outputText></TH>
							<TD colspan="7"><h:inputText styleClass="inputText"
								id="htmlPdfTitle" size="64"
								maxlength="#{pc_Kkc00301.propPdfTitle.maxLength}"
								disabled="#{pc_Kkc00301.propPdfTitle.disabled}"
								value="#{pc_Kkc00301.propPdfTitle.stringValue}"
								readonly="#{pc_Kkc00301.propPdfTitle.readonly}"
								style="#{pc_Kkc00301.propPdfTitle.style}"></h:inputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE cellspacing="0" cellpadding="0" class="button_bar" width="100%" style="margin-top:20px">
					<TR>
						<!-- PDF作成ボタン--><!-- CSV作成ボタン --><!-- 出力項目指定ボタン --><!-- 検索ボタン --> 
						<TD align="center">
							<hx:commandExButton type="submit"
								value="PDF作成" styleClass="commandExButton_out" id="pdfout"
								action="#{pc_Kkc00301.doPdfOutAction}"
								confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" styleClass="commandExButton_out"
								id="csvout" value="CSV作成"
								action="#{pc_Kkc00301.doCsvOutAction}"
								confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" styleClass="commandExButton_out"
								id="setoutput" value="出力項目指定"
								action="#{pc_Kkc00301.doSetoutputAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" styleClass="commandExButton_out"
								id="search" value="検索"
								action="#{pc_Kkc00301.doSearchAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
	<h:inputHidden id="htmlNendoHidden" />
	<h:inputHidden id="htmlGakkiNoHidden" />
	<h:inputHidden id="htmlJmkAddr1Hidden" />
	<h:inputHidden id="htmlJmkAddrKana1Hidden" />
	<h:inputHidden id="htmlExecutableSearchHidden" value="#{pc_Kkc00301.propExecutableSearchHidden.integerValue}"><f:convertNumber /></h:inputHidden>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
