<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/co/Coa01501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Coa01501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function confirmOk(){
	document.getElementById('form1:propWarningFlg').value="1";
	indirectClick('register');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Coa01501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Coa01501.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Coa01501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Coa01501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="600" align="right">
						
						<hx:commandExButton type="submit" value="コピー"
							styleClass="commandExButton" id="copy"
							action="#{pc_Coa01501.doCopyAction}"></hx:commandExButton></TD>
						<TD width="15%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="400">
						<TABLE width="400" class="table">
							<TBODY>
								<TR>
									<TH width="100" class="v_a"><h:outputText
										styleClass="outputText" id="lblNyushiNendo"
										value="#{pc_Coa01501.propNyushiNendo.labelName}"
										style="#{pc_Coa01501.propNyushiNendo.labelStyle}"></h:outputText></TH>
									<TD width="60"><h:inputText styleClass="inputText"
										id="htmlNyushiNendo"
										value="#{pc_Coa01501.propNyushiNendo.dateValue}" size="4"
										style="{pc_Coa01501.propNyushiNendo.style}"
										disabled="#{pc_Coa01501.propNyushiNendo.disabled}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH width="120" class="v_b"><h:outputText
										styleClass="outputText" id="lblNyushiGakkiNo"
										value="#{pc_Coa01501.propNyushiGakkiNo.labelName}"
										style="#{pc_Coa01501.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
									<TD width="144"><h:inputText styleClass="inputText"
										id="htmlNyushiGakkiNo"
										value="#{pc_Coa01501.propNyushiGakkiNo.integerValue}"
										maxlength="#{pc_Coa01501.propNyushiGakkiNo.maxLength}"
										size="2" style="{pc_Coa01501.propNyushiGakkiNo.style}"
										disabled="#{pc_Coa01501.propNyushiGakkiNo.disabled}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="" />
										<f:convertNumber pattern="#0" />
									</h:inputText>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="100">
						<TABLE width="100">
							<TBODY>
								<TR>
									<TD width="50">
										<hx:commandExButton
										type="submit" value="選択" styleClass="commandExButton"
										id="search" action="#{pc_Coa01501.doSearchAction}"
										disabled="#{pc_Coa01501.propSearch.disabled}"></hx:commandExButton>
									</TD>
									<TD width="50">
										<hx:commandExButton
										type="submit" value="解除" styleClass="commandExButton"
										id="unselect" action="#{pc_Coa01501.doUnselectAction}"
										disabled="#{pc_Coa01501.propUnselect.disabled}"></hx:commandExButton>
									</TD>
									
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="100" align="right">
						
						</TD>
						<TD width="15%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="600" align="right"><h:outputText
							styleClass="outputText" id="lblCount"
							value="#{pc_Coa01501.propNysSbt.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text4" value="件"></h:outputText></TD>
							<TD width="15%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="600">
						<div class="listScroll" style="height:122px;width: 100%;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass" width="583"
							rowClasses="#{pc_Coa01501.propNysSbt.rowClasses}"
							styleClass="meisai_scroll" id="htmlNysSbt"
							value="#{pc_Coa01501.propNysSbt.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text3" styleClass="outputText" value="コード"></h:outputText>
								</f:facet>
								<f:attribute value="90" name="width" />
								<h:outputText styleClass="outputText" id="text6"
									value="#{varlist.nysSbtCd}"></h:outputText>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text5"></h:outputText>
								</f:facet>
								<f:attribute value="420" name="width" />
								<h:outputText styleClass="outputText" id="text7"
									value="#{varlist.nysSbtName.displayValue}"
									title="#{varlist.nysSbtName.value}"></h:outputText>
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="分類" id="text1"></h:outputText>
								</f:facet>
								<f:attribute value="60" name="width" />
								<h:outputText styleClass="outputText" id="text2"
									value="#{varlist.nyushiSbtBunruiKbn}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="30" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Coa01501.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable>
						<div>
						</TD>
						<TD width="15%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="600" border="0">
				<TR>
					<TD align="left">
						<h:outputText styleClass="outputText" id="lbl100" value="※共通テストを利用する場合は、大学、試験区分、成績請求票種別を選択してください。"></h:outputText>
					</TD>
				</TR>
			</TABLE>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="600">
						<TABLE class="table" width="600">
							<TBODY>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblNysSbtCd"
										style="#{pc_Coa01501.propNysSbtCd.labelStyle}"
										value="#{pc_Coa01501.propNysSbtCd.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNysSbtCd"
										value="#{pc_Coa01501.propNysSbtCd.stringValue}"
										style="#{pc_Coa01501.propNysSbtCd.style}"
										maxlength="#{pc_Coa01501.propNysSbtCd.maxLength}" size="6"
										disabled="#{pc_Coa01501.propNysSbtCd.disabled}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_d"><h:outputText
										styleClass="outputText" id="lblNysSbtName"
										value="#{pc_Coa01501.propNysSbtName.labelName}"
										style="#{pc_Coa01501.propNysSbtName.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNysSbtName"
										value="#{pc_Coa01501.propNysSbtName.stringValue}"
										style="#{pc_Coa01501.propNysSbtName.style}"
										maxlength="#{pc_Coa01501.propNysSbtName.maxLength}" size="60"
										disabled="#{pc_Coa01501.propNysSbtName.disabled}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_e"><h:outputText
										styleClass="outputText" id="lblTaigaiName"
										value="#{pc_Coa01501.propTaigaiName.labelName}"
										style="#{pc_Coa01501.propTaigaiName.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlTaigaiName"
										value="#{pc_Coa01501.propTaigaiName.stringValue}"
										style="#{pc_Coa01501.propTaigaiName.style}"
										maxlength="#{pc_Coa01501.propTaigaiName.maxLength}" size="60"
										disabled="#{pc_Coa01501.propTaigaiName.disabled}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_f"><h:outputText
										styleClass="outputText" id="lblDaigakCd"
										value="#{pc_Coa01501.propDaigakCd.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlDaigakCd" value="#{pc_Coa01501.propDaigakCd.value}"
										disabled="#{pc_Coa01501.propDaigakCd.disabled}">
										<f:selectItems value="#{pc_Coa01501.propDaigakCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_g"><h:outputText
										styleClass="outputText" id="lblNyugakGakunen"
										value="#{pc_Coa01501.propNyugakGakunen.labelName}"
										style="#{pc_Coa01501.propNyugakGakunen.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNyugakGakunen"
										value="#{pc_Coa01501.propNyugakGakunen.value}"
										disabled="#{pc_Coa01501.propNyugakGakunen.disabled}">
										<f:selectItems value="#{pc_Coa01501.propNyugakGakunen.list}" />
									</h:selectOneMenu></TD>
									<TH width="150" class="v_a"><h:outputText styleClass="outputText" id="lblNyugakSemester" value="#{pc_Coa01501.propNyugakSemester.labelName}" style="#{pc_Coa01501.propNyugakSemester.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNyugakSemester"
										value="#{pc_Coa01501.propNyugakSemester.value}"
										disabled="#{pc_Coa01501.propNyugakSemester.disabled}">
										<f:selectItems value="#{pc_Coa01501.propNyugakSemester.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblNyugakNendo"
										value="#{pc_Coa01501.propNyugakNendo.labelName}"
										style="#{pc_Coa01501.propNyugakNendo.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:inputText styleClass="inputText"
										id="htmlNyugakNendo"
										value="#{pc_Coa01501.propNyugakNendo.dateValue}"
										style="#{pc_Coa01501.propNyugakNendo.style}" size="4"
										disabled="#{pc_Coa01501.propNyugakNendo.disabled}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH width="150" class="v_c"><h:outputText styleClass="outputText"
										id="lblNyugakGakkiNo"
										value="#{pc_Coa01501.propNyugakGakkiNo.labelName}"
										style="#{pc_Coa01501.propNyugakGakkiNo.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:inputText styleClass="inputText"
										id="htmlNyugakGakkiNo" size="2"
										value="#{pc_Coa01501.propNyugakGakkiNo.integerValue}"
										style="#{pc_Coa01501.propNyugakGakkiNo.style}"
										maxlength="#{pc_Coa01501.propNyugakGakkiNo.maxLength}"
										disabled="#{pc_Coa01501.propNyugakGakkiNo.disabled}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<f:convertNumber pattern="#0" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_d"><h:outputText
										styleClass="outputText" id="lblNyugakNendoCur"
										value="#{pc_Coa01501.propNyugakNendoCur.labelName}"
										style="#{pc_Coa01501.propNyugakNendoCur.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:inputText styleClass="inputText"
										id="htmlNyugakNendoCur"
										value="#{pc_Coa01501.propNyugakNendoCur.dateValue}"
										style="#{pc_Coa01501.propNyugakNendoCur.style}"
										disabled="#{pc_Coa01501.propNyugakNendoCur.disabled}" size="4">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH width="150" class="v_e"><h:outputText styleClass="outputText" id="lblNyugakGakkiNoCur" value="#{pc_Coa01501.propNyugakGakkiNoCur.labelName}" style="#{pc_Coa01501.propNyugakGakkiNoCur.labelStyle}"></h:outputText></TH>
									<TD width="150"><h:inputText styleClass="inputText"
										id="htmlNyugakGakkiNoCur" size="2"
										value="#{pc_Coa01501.propNyugakGakkiNoCur.integerValue}"
										style="#{pc_Coa01501.propNyugakGakkiNoCur.style}"
										maxlength="#{pc_Coa01501.propNyugakGakkiNoCur.maxLength}"
										disabled="#{pc_Coa01501.propNyugakGakkiNoCur.disabled}">
										<f:convertNumber pattern="#0"/>
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_f"><h:outputText
										styleClass="outputText" id="lblNyugakSbtCd"
										value="#{pc_Coa01501.propNyugakSbtCd.labelName}"
										style="#{pc_Coa01501.propNyugakSbtCd.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNyugakSbtCd"
										disabled="#{pc_Coa01501.propNyugakSbtCd.disabled}"
										value="#{pc_Coa01501.propNyugakSbtCd.value}">
										<f:selectItems value="#{pc_Coa01501.propNyugakSbtCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_g"><h:outputText
										styleClass="outputText" id="lblSyugakSbtCd"
										value="#{pc_Coa01501.propSyugakSbtCd.labelName}"
										style="#{pc_Coa01501.propSyugakSbtCd.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSyugakSbtCd"
										disabled="#{pc_Coa01501.propSyugakSbtCd.disabled}"
										value="#{pc_Coa01501.propSyugakSbtCd.value}">
										<f:selectItems value="#{pc_Coa01501.propSyugakSbtCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_a"><h:outputText
										styleClass="outputText" id="lblSikenKbn"
										value="#{pc_Coa01501.propSikenKbn.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										disabled="#{pc_Coa01501.propSikenKbn.disabled}"
										id="htmlSikenKbn" value="#{pc_Coa01501.propSikenKbn.value}">
										<f:selectItems value="#{pc_Coa01501.propSikenKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblSeisekiSeikyuhyoSbt"
										value="#{pc_Coa01501.propSeisekiSeikyuhyoSbt.labelName}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSeisekiSeikyuhyoSbt"
										value="#{pc_Coa01501.propSeisekiSeikyuhyoSbt.value}"
										disabled="#{pc_Coa01501.propSeisekiSeikyuhyoSbt.disabled}">
										<f:selectItems
											value="#{pc_Coa01501.propSeisekiSeikyuhyoSbt.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="15%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0003W}"
							action="#{pc_Coa01501.doRegisterAction}"
							disabled="#{pc_Coa01501.propRegister.disabled}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Coa01501.doDeleteAction1}"
							disabled="#{pc_Coa01501.propDelete.disabled}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Coa01501.doClearAction}"
							disabled="#{pc_Coa01501.propClear.disabled}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Coa01501.propNysSbt.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Coa01501.propWarningFlg.integerValue}"
				id="propWarningFlg">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">
    changeScrollPosition('scroll','listscroll');
</SCRIPT>
</HTML>

