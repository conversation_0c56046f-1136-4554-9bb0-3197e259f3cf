<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/km/Kmb02201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Kmb02201.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--	title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:propExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:propExecutableSearch').value = "0";
}

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Kmb02201.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />
        <!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Kmb02201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Kmb02201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Kmb02201.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">
	
	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
	<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText>
	</FIELDSET>
	<br>

	<!--↓content↓-->
	<DIV id="content">
		<DIV class="column" align="center">
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="816">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblNyugakuNendo"
							value="#{pc_Kmb02201.propNyugakuNendo.labelName}"
							style="#{pc_Kmb02201.propNyugakuNendo.labelStyle}">
						</h:outputText></TH>
						<TD width="175"><h:inputText id="htmlNyugakuNendo"
							styleClass="inputText"
							readonly="#{pc_Kmb02201.propNyugakuNendo.readonly}"
							style="#{pc_Kmb02201.propNyugakuNendo.style}"
							value="#{pc_Kmb02201.propNyugakuNendo.dateValue}"
							disabled="#{pc_Kmb02201.propNyugakuNendo.disabled}"
							maxlength="#{pc_Kmb02201.propNyugakuNendo.maxLength}" size="4"
							tabindex="1">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblNyugakuGakkiNo"
							value="#{pc_Kmb02201.propNyugakuGakkiNo.labelName}"
							style="#{pc_Kmb02201.propNyugakuGakkiNo.labelStyle}"></h:outputText></TH>
						<TD width="225"><h:inputText id="htmlNyugakuGakkiNo"
							styleClass="inputText"
							readonly="#{pc_Kmb02201.propNyugakuGakkiNo.readonly}"
							style="#{pc_Kmb02201.propNyugakuGakkiNo.style}"
							value="#{pc_Kmb02201.propNyugakuGakkiNo.integerValue}"
							maxlength="#{pc_Kmb02201.propNyugakuGakkiNo.maxLength}"
							disabled="#{pc_Kmb02201.propNyugakuGakkiNo.disabled}" size="2"
							tabindex="2">
							<f:convertNumber type="number" pattern="#0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
						<TD width="116"
							style="background-color: transparent; text-align: right" class="clear_border"><hx:commandExButton
							type="submit" value="選択" styleClass="commandExButton"
							id="selectNendoGakki" tabindex="3"
							disabled="#{pc_Kmb02201.propSelectNendoGakki.disabled}"
							action="#{pc_Kmb02201.doSelectNendoGakkiAction}">
						</hx:commandExButton><hx:commandExButton type="submit" value="解除"
							styleClass="commandExButton" id="cancelNendoGakki" tabindex="4"
							disabled="#{pc_Kmb02201.propCancelNendoGakki.disabled}"
							action="#{pc_Kmb02201.doCancelNendoGakkiAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR class="hr" noshade>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="816">
				<TBODY>
					<TR>
						<TH class="v_b" width="150"><h:outputText styleClass="outputText"
							id="lblCurGakka" value="#{pc_Kmb02201.propCurGakka.labelName}"
							style="#{pc_Kmb02201.propCurGakka.labelStyle}"></h:outputText></TH>
						<TD colspan=3 width="550"><h:selectOneMenu
							styleClass="selectOneMenu" id="htmlCurGakka"
							value="#{pc_Kmb02201.propCurGakka.value}"
							disabled="#{pc_Kmb02201.propCurGakka.disabled}"
							style="#{pc_Kmb02201.propCurGakka.style};width:540px"
							tabindex="5">
							<f:selectItems value="#{pc_Kmb02201.propCurGakka.list}" />
						</h:selectOneMenu>
						</TD>
						<TD rowspan=2 width="116"
							style="background-color: transparent; text-align: right"
							class="clear_border"><hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton" id="search" tabindex="8"
							disabled="#{pc_Kmb02201.propSearch.disabled}"
							action="#{pc_Kmb02201.doSearchAction}">
						</hx:commandExButton> <hx:commandExButton type="submit" value="解除"
							styleClass="commandExButton" id="cancel" tabindex="9"
							disabled="#{pc_Kmb02201.propCancel.disabled}"
							action="#{pc_Kmb02201.doCancelAction}">
						</hx:commandExButton></TD>
					</TR>
					<TR>
						<TH class="v_c" width="150"><h:outputText styleClass="outputText"
							id="lblTaisyoGakunen"
							value="#{pc_Kmb02201.propTaisyoGakunen.labelName}"
							style="#{pc_Kmb02201.propTaisyoGakunen.labelStyle}"></h:outputText></TH>
						<TD width="175"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlTaisyoGakunen"
							value="#{pc_Kmb02201.propTaisyoGakunen.value}"
							disabled="#{pc_Kmb02201.propTaisyoGakunen.disabled}"
							style="#{pc_Kmb02201.propTaisyoGakunen.style};width:165px"
							tabindex="6">
							<f:selectItems value="#{pc_Kmb02201.propTaisyoGakunen.list}" />
						</h:selectOneMenu></TD>
						<%-- EUC DEL 対象セメスタ 2014/06/05 k-sya start
						<TH class="v_c" width="150"><h:outputText styleClass="outputText"
							id="lblTaisyoSemester"
							value="#{pc_Kmb02201.propTaisyoSemester.labelName}"
							style="#{pc_Kmb02201.propTaisyoSemester.labelStyle}"></h:outputText></TH>
						<TD width="225"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlTaisyoSemester"
							value="#{pc_Kmb02201.propTaisyoSemester.value}"
							disabled="#{pc_Kmb02201.propTaisyoSemester.disabled}"
							style="#{pc_Kmb02201.propTaisyoSemester.style};width:165px"
							tabindex="7">
							<f:selectItems value="#{pc_Kmb02201.propTaisyoSemester.list}" />
						</h:selectOneMenu></TD>  
						EUC DEL 対象セメスタ 014/06/05 k-sya end--%>
					</TR>
				</TBODY>
			</TABLE>
			<HR class="hr" noshade>
			<TABLE border="0" class="button_bar">			
				<TBODY>
					<TR>
						<TD width="800"><hx:commandExButton type="submit" value="新規登録"
							styleClass="commandExButton_dat" id="dspEntrypage" tabindex="11"
							action="#{pc_Kmb02201.doDspEntrypageAction}"
							disabled="#{pc_Kmb02201.propDspEntrypage.disabled}"></hx:commandExButton>
							<hx:commandExButton type="submit" value="卒業条件コピーへ"
							styleClass="commandExButton_large1" id="dspSotugyoJokenCopypage"
							tabindex="12" style="width: 100px"
							action="#{pc_Kmb02201.doDspSotugyoJokenCopypageAction}"
							disabled="#{pc_Kmb02201.propDspSotugyoJokenCopypage.disabled}"></hx:commandExButton>
							<hx:commandExButton type="submit" value="ＯＲ条件設定へ"
							styleClass="commandExButton_large1" id="dspOrJokenpage"
							tabindex="13" style="width: 100px"
							action="#{pc_Kmb02201.doDspOrJokenpageAction}"
							disabled="#{pc_Kmb02201.propDspOrJokenpage.disabled}">
						</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
				<TBODY>
					<TR>
						<TD align="right" width="800"><h:outputText styleClass="outputText"
							id="htmlJokenListTotal"
							value="#{pc_Kmb02201.propJokenListTotal.stringValue}">
						</h:outputText>
						</TD>
					</TR>
					<TR>
						<TD width="900">
						<DIV style="height:400px" class="listScroll"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							columnClasses="columnClass1" headerClass="headerClass"
							footerClass="footerClass" rowClasses="#{pc_Kmb02201.propJokenList.rowClasses}"
							styleClass="meisai_scroll" id="htmlJokenList"
							value="#{pc_Kmb02201.propJokenList.list}" var="varlist"
							width="800">
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="ＯＲ条件"
										id="lblOrJokenColumn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlOrJokenColumn"
									value="#{varlist.orJoken}"></h:outputText>
								<f:attribute value="60" name="width" />
							</h:column>							
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblTaisyoGakunenColumn"
										styleClass="outputText"
										value="#{pc_Kmb02201.propTaisyoGakunen.labelName}"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlTaisyoGakunenColumn" value="#{varlist.taisyoGakunen}"></h:outputText>
								<f:attribute value="60" name="width" />
							</h:column>
							<%--EUC DEL 対象セメスタ 2014/06/05 k-sya start
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText"
										value="#{pc_Kmb02201.propTaisyoSemester.labelName}"
										id="lblTaisyoSemesterColumn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlTaisyoSemesterColumn" value="#{varlist.taisyoSemester}"></h:outputText>
								<f:attribute value="80" name="width" />
							</h:column>
							EUC DEL 対象セメスタ 2014/06/05 k-sya end--%>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="対象学期"
										id="lblTaisyoGakkiNoColumn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlTaisyoGakkiNo"
									value="#{varlist.hanteiTaisyoGakkiNo}"></h:outputText>
								<f:attribute value="60" name="width" />
							</h:column>

							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="条件コード"
										id="lblJokenCodeColumn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlJokenCodeColumn"
									value="#{varlist.jokenCode}"></h:outputText>
								<f:attribute value="70" name="width" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="タイトル"
										id="lblTitleColumn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlTitleColumn"
									value="#{varlist.title}"></h:outputText>
								<f:attribute value="362" name="width" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="編集"
									styleClass="commandExButton" id="editJoken"
									action="#{pc_Kmb02201.doEditJokenAction}" tabindex="14"></hx:commandExButton>
								<hx:commandExButton type="submit" value="コピー"
									styleClass="commandExButton" id="copyJoken"
									action="#{pc_Kmb02201.doCopyJokenAction}" tabindex="14"></hx:commandExButton>
								<f:attribute value="88" name="width" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
		</DIV>
	</DIV>
	<!--↑content↑-->
	</DIV>
	<!--↑outer↑-->
			<h:inputHidden
				value="#{pc_Kmb02201.propExecutableSearch.integerValue}"
				id="propExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
	</h:form>
	<!-- フッターインクルード -->
	<jsp:include page ="../inc/footer.jsp" />
	</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
