<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Nsa01001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Nsa01001.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Nsa01001.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Nsa01001.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Nsa01001.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="right"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style=""
							class="" width="80%">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" style=""
										class="table" width="100%">
										<TBODY>
											<TR>
												<TH style="" class="v_a" width="145"><h:outputText
													styleClass="outputText" id="lblNyushiNendo"
													value="#{pc_Nsa01001.propNyushiNendo.labelName}"
													style="#{pc_Nsa01001.propNyushiNendo.labelStyle}"></h:outputText></TH>
												<TD class="" colspan="" width="120"><h:inputText
													styleClass="inputText" id="htmlNyushiNendo" size="5"
													tabindex="1"
													disabled="#{pc_Nsa01001.propNyushiNendo.disabled}"
													readonly="#{pc_Nsa01001.propNyushiNendo.readonly}"
													style="#{pc_Nsa01001.propNyushiNendo.style}"
													value="#{pc_Nsa01001.propNyushiNendo.dateValue}">
													<f:convertDateTime pattern="yyyy" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
												<TH width="150" class="v_b"><h:outputText
													styleClass="outputText" id="lblNyushiGakkiNo"
													value="#{pc_Nsa01001.propNyushiGakkiNo.labelName}"
													style="#{pc_Nsa01001.propNyushiGakkiNo.labelStyle}"></h:outputText></TH>
												<TD class=""><h:inputText styleClass="inputText"
													id="htmlNyushiGakkiNo" size="2" tabindex="2"
													disabled="#{pc_Nsa01001.propNyushiGakkiNo.disabled}"
													readonly="#{pc_Nsa01001.propNyushiGakkiNo.readonly}"
													style="#{pc_Nsa01001.propNyushiGakkiNo.style}"
													value="#{pc_Nsa01001.propNyushiGakkiNo.integerValue}">
													<f:convertNumber pattern="#0" />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>

											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" style=""
										class="" width="80%">
										<TBODY>
											<TR>
												<TD class="" width="20"
													style="border-top-style:none;border-right-style: none;"></TD>
												<TD class=""
													style="border-top-style:none;border-right-style: none;border-left-style: none;"
													width="60"></TD>
												<TD class=""
													style="border-top-style:none;border-right-style: none;border-left-style: none;"
													width="60"></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0"
										class="table" width="100%">
										<TBODY>
											<TR>
												<TH class="v_a" width="145" style="border-top:none;"><h:outputText
													styleClass="outputText" id="lblNysSbtBunrui"
													value="#{pc_Nsa01001.propNysSbtBunrui.labelName}"
													style="#{pc_Nsa01001.propNysSbtBunrui.labelStyle}"></h:outputText></TH>
												<TD style="border-top:none;"><h:selectOneMenu styleClass="selectOneMenu"
													id="htmlNysSbtBunrui"
													disabled="#{pc_Nsa01001.propNysSbtBunrui.disabled}"
													readonly="#{pc_Nsa01001.propNysSbtBunrui.readonly}"
													value="#{pc_Nsa01001.propNysSbtBunrui.stringValue}" tabindex="3">
													<f:selectItems value="#{pc_Nsa01001.propNysSbtBunrui.list}" />
												</h:selectOneMenu></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" style=""
										class="" width="80%">
										<TBODY>
											<TR>
												<TD class="" width="20"
													style="border-top-style:none;border-right-style: none;"></TD>
												<TD class=""
													style="border-top-style:none;border-right-style: none;border-left-style: none;"
													width="60"><hx:commandExButton type="submit" value="選択"
													styleClass="commandExButton" id="select"
													disabled="#{pc_Nsa01001.propSelect.disabled}"
													action="#{pc_Nsa01001.doSelectAction}" tabindex="4"></hx:commandExButton></TD>
												<TD class=""
													style="border-top-style:none;border-right-style: none;border-left-style: none;"
													width="60"><hx:commandExButton type="submit" value="解除"
													styleClass="commandExButton" id="reset"
													disabled="#{pc_Nsa01001.propReset.disabled}"
													action="#{pc_Nsa01001.doResetAction}" tabindex="5"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" style=""
							class="table" width="80%">
							<TBODY>
								<TR>
								<TR>
									<TH style="" class="v_c" width="145"><h:outputText
										styleClass="outputText" id="lblNysSbt"
										value="#{pc_Nsa01001.propNysSbt.labelName}"></h:outputText><BR>
									<h:outputText styleClass="outputText" id="lblFukususentakuN"
										value="(複数選択可)"></h:outputText></TH>
									<TD class="" colspan="6"><h:selectManyListbox
										styleClass="selectManyListbox" id="htmlNysSbt"
										style="width: 95%" size="7"
										disabled="#{pc_Nsa01001.propNysSbt.disabled}"
										readonly="#{pc_Nsa01001.propNysSbt.readonly}"
										value="#{pc_Nsa01001.propNysSbt.value}" tabindex="6">
										<f:selectItems value="#{pc_Nsa01001.propNysSbt.list}" />
									</h:selectManyListbox></TD>
								</TR>
								<TR>
									<TH style="" class="v_d" width="145"><h:outputText
										styleClass="outputText" id="lblJoken"
										value="#{pc_Nsa01001.propJoken1.labelName}"
										style="#{pc_Nsa01001.propJoken1.labelStyle}"></h:outputText></TH>
									<TD class="" colspan="6">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
										<TBODY>
											<TR>
												<TD width="250" class="clear_border"><h:selectManyCheckbox
													disabledClass="selectManyCheckbox_Disabled"
													styleClass="selectManyCheckbox" id="htmlJoken1"
													layout="pageDirection"
													readonly="#{pc_Nsa01001.propJoken1.readonly}"
													value="#{pc_Nsa01001.propJoken1.value}" tabindex="7">
													<f:selectItem itemValue="a" itemLabel="氏名＿カナ" />
													<f:selectItem itemValue="c" itemLabel="性別" />
													<f:selectItem itemValue="e" itemLabel="電話番号" />
													<f:selectItem itemValue="g" itemLabel="出身校コード" />
													<f:selectItem itemValue="i" itemLabel="国籍コード" />
													<f:selectItem itemValue="k" itemLabel="出身地コード" />
												</h:selectManyCheckbox></TD>
												<TD class="clear_border"><h:selectManyCheckbox
													disabledClass="selectManyCheckbox_Disabled"
													styleClass="selectManyCheckbox" id="htmlJoken2"
													layout="pageDirection"
													readonly="#{pc_Nsa01001.propJoken2.readonly}"
													value="#{pc_Nsa01001.propJoken2.value}" tabindex="8">
													<f:selectItem itemValue="b" itemLabel="氏名" />
													<f:selectItem itemValue="d" itemLabel="生年月日" />
													<f:selectItem itemValue="f" itemLabel="住所カナ" />
													<f:selectItem itemValue="h" itemLabel="その他出身校等コード" />
													<f:selectItem itemValue="j" itemLabel="本籍地コード" />
													<f:selectItem itemValue="l" itemLabel="地区コード" />
												</h:selectManyCheckbox></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TH class="v_e" width="145"><h:outputText
										styleClass="outputText" id="lblTitle"
										value="#{pc_Nsa01001.propTitle.labelName}"></h:outputText></TH>
									<TD class="" colspan="6"><h:inputText styleClass="inputText"
										id="htmlTitle" size="60"
										disabled="#{pc_Nsa01001.propTitle.disabled}"
										maxlength="#{pc_Nsa01001.propTitle.maxLength}"
										readonly="#{pc_Nsa01001.propTitle.readonly}"
										value="#{pc_Nsa01001.propTitle.stringValue}" tabindex="9">
										<hx:inputHelperAssist imeMode="active"
											errorClass="inputText_Error" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="80%" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="PDF作成"
										styleClass="commandExButton_out" id="pdfout"
										confirm="#{msg.SY_MSG_0019W}"
										disabled="#{pc_Nsa01001.propPdfout.disabled}"
										action="#{pc_Nsa01001.doPdfoutAction}" tabindex="10"></hx:commandExButton><hx:commandExButton
										type="submit" value="CSV作成" styleClass="commandExButton_out"
										confirm="#{msg.SY_MSG_0020W}" id="csvout"
										disabled="#{pc_Nsa01001.propCsvout.disabled}"
										action="#{pc_Nsa01001.doCsvoutAction}" tabindex="11"></hx:commandExButton><hx:commandExButton
										type="submit" value="出力項目指定" styleClass="commandExButton_out"
										id="setoutput"
										disabled="#{pc_Nsa01001.propSetoutput.disabled}"
										action="#{pc_Nsa01001.doSetoutputAction}" tabindex="12"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

